<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>cn.hexcloud</groupId>
  <artifactId>pbis-common-service-facade</artifactId>
  <version>1.2.4-SNAPSHOT</version>

  <properties>
    <lombok.version>1.18.14</lombok.version>
    <fastjson.version>1.2.61</fastjson.version>
    <jackson.version>2.11.4</jackson.version>
    <protobuf.version>3.17.3</protobuf.version>
    <grpc.version>1.40.1</grpc.version>
    <grpc.springboot.version>2.12.0.RELEASE</grpc.springboot.version>
<!--    <maven.compiler.source>11</maven.compiler.source>-->
<!--    <maven.compiler.target>11</maven.compiler.target>-->
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>${fastjson.version}</version>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>${jackson.version}</version>
    </dependency>
    <!--grpc & protobuf start-->
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>${protobuf.version}</version>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-all</artifactId>
      <version>${grpc.version}</version>
    </dependency>
    <dependency>
      <groupId>net.devh</groupId>
      <artifactId>grpc-server-spring-boot-starter</artifactId>
      <version>${grpc.springboot.version}</version>
    </dependency>
    <!--grpc & protobuf end-->
  </dependencies>

  <build>
    <extensions>
      <extension>
        <groupId>kr.motd.maven</groupId>
        <artifactId>os-maven-plugin</artifactId>
        <version>1.7.0</version>
      </extension>
    </extensions>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <excludes>
            <include>**/service/facade/impl</include>
          </excludes>
        </configuration>
      </plugin>
      <!--protobuf编译插件（将.proto文件编译并生成java实体类、客户端和服务端代码）-->
      <plugin>
        <groupId>org.xolstice.maven.plugins</groupId>
        <artifactId>protobuf-maven-plugin</artifactId>
        <version>0.5.0</version>
        <configuration>
          <!--suppress UnresolvedMavenProperty -->
          <protocArtifact>com.google.protobuf:protoc:${protobuf.version}:exe:osx-x86_64</protocArtifact>
          <pluginId>grpc-java</pluginId>
          <!--suppress UnresolvedMavenProperty -->
          <pluginArtifact>io.grpc:protoc-gen-grpc-java:${grpc.version}:exe:osx-x86_64</pluginArtifact>
          <!--.proto文件所在目录的默认值-->
          <protoSourceRoot>${project.basedir}/src/main/resources/proto</protoSourceRoot>
          <!--生成的.java文件所在目录的默认值-->
          <outputDirectory>${project.basedir}/src/main/java</outputDirectory>
          <clearOutputDirectory>false</clearOutputDirectory>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>compile</goal>
              <goal>compile-custom</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <!-- 使用分发管理将本项目打成jar包，直接上传到指定服务器 -->
  <distributionManagement>
    <!-- hexcloud nexus -->
    <repository>
      <!-- nexus服务器中用户名：在settings.xml中<server>的id-->
      <id>rdc-releases</id>
      <name>repository-releases</name>
      <url>https://packages.aliyun.com/maven/repository/2159385-release-NLNgWB</url>
    </repository>
    <snapshotRepository>
      <id>rdc-snapshots</id>
      <name>repository-snapshot</name>
      <url>https://packages.aliyun.com/maven/repository/2159385-snapshot-ZtdOYW</url>
    </snapshotRepository>
  </distributionManagement>


  <repositories>
    <!-- 阿里云maven仓库 -->
    <repository>
      <id>central</id>
      <name>central repository</name>
      <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
      <layout>default</layout>
      <releases>
        <updatePolicy>never</updatePolicy>
      </releases>
      <snapshots>
        <updatePolicy>never</updatePolicy>
      </snapshots>
    </repository>
    <repository>
      <id>rdc-releases</id>
      <url>https://packages.aliyun.com/maven/repository/2159385-release-NLNgWB</url>
      <releases>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
    </repository>
    <repository>
      <id>rdc-snapshots</id>
      <url>https://packages.aliyun.com/maven/repository/2159385-snapshot-ZtdOYW</url>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots>
      </snapshots>
    </repository>
  </repositories>
</project>
