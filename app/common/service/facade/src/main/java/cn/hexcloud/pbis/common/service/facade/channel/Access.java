// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 第三方访问授权配置
 * </pre>
 *
 * Protobuf type {@code channel.Access}
 */
public final class Access extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.Access)
    AccessOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Access.newBuilder() to construct.
  private Access(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Access() {
    channelCode_ = "";
    applyTargets_ = java.util.Collections.emptyList();
    businessCode_ = "";
    merchantId_ = "";
    appId_ = "";
    appKey_ = "";
    accessKey_ = "";
    cert_ = "";
    privateKey_ = "";
    publicKey_ = "";
    gatewayUrl_ = "";
    channelNameItems_ = "";
    partnerId_ = "";
    companyId_ = "";
    storeId_ = "";
    terminalId_ = "";
    apiVersion_ = "";
    subMerchantId_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Access();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Access(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            channelCode_ = s;
            break;
          }
          case 16: {

            applyTo_ = input.readInt32();
            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              applyTargets_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget>();
              mutable_bitField0_ |= 0x00000001;
            }
            applyTargets_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.parser(), extensionRegistry));
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            businessCode_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            merchantId_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            appId_ = s;
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            appKey_ = s;
            break;
          }
          case 66: {
            java.lang.String s = input.readStringRequireUtf8();

            accessKey_ = s;
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            cert_ = s;
            break;
          }
          case 82: {
            java.lang.String s = input.readStringRequireUtf8();

            privateKey_ = s;
            break;
          }
          case 90: {
            java.lang.String s = input.readStringRequireUtf8();

            publicKey_ = s;
            break;
          }
          case 98: {
            java.lang.String s = input.readStringRequireUtf8();

            gatewayUrl_ = s;
            break;
          }
          case 106: {
            java.lang.String s = input.readStringRequireUtf8();

            channelNameItems_ = s;
            break;
          }
          case 112: {

            accessId_ = input.readInt64();
            break;
          }
          case 122: {
            java.lang.String s = input.readStringRequireUtf8();

            partnerId_ = s;
            break;
          }
          case 130: {
            java.lang.String s = input.readStringRequireUtf8();

            companyId_ = s;
            break;
          }
          case 138: {
            java.lang.String s = input.readStringRequireUtf8();

            storeId_ = s;
            break;
          }
          case 146: {
            java.lang.String s = input.readStringRequireUtf8();

            terminalId_ = s;
            break;
          }
          case 154: {
            java.lang.String s = input.readStringRequireUtf8();

            apiVersion_ = s;
            break;
          }
          case 162: {
            java.lang.String s = input.readStringRequireUtf8();

            subMerchantId_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        applyTargets_ = java.util.Collections.unmodifiableList(applyTargets_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_Access_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_Access_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.Access.class, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder.class);
  }

  public static final int CHANNEL_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object channelCode_;
  /**
   * <pre>
   * 渠道代码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  @java.lang.Override
  public java.lang.String getChannelCode() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道代码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelCodeBytes() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int APPLY_TO_FIELD_NUMBER = 2;
  private int applyTo_;
  /**
   * <pre>
   * 配置层级，0 租户；1 公司；2 门店
   * </pre>
   *
   * <code>int32 apply_to = 2;</code>
   * @return The applyTo.
   */
  @java.lang.Override
  public int getApplyTo() {
    return applyTo_;
  }

  public static final int APPLY_TARGETS_FIELD_NUMBER = 3;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget> applyTargets_;
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget> getApplyTargetsList() {
    return applyTargets_;
  }
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder> 
      getApplyTargetsOrBuilderList() {
    return applyTargets_;
  }
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
   */
  @java.lang.Override
  public int getApplyTargetsCount() {
    return applyTargets_.size();
  }
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget getApplyTargets(int index) {
    return applyTargets_.get(index);
  }
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder getApplyTargetsOrBuilder(
      int index) {
    return applyTargets_.get(index);
  }

  public static final int BUSINESS_CODE_FIELD_NUMBER = 4;
  private volatile java.lang.Object businessCode_;
  /**
   * <pre>
   * 渠道业务代码
   * </pre>
   *
   * <code>string business_code = 4;</code>
   * @return The businessCode.
   */
  @java.lang.Override
  public java.lang.String getBusinessCode() {
    java.lang.Object ref = businessCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      businessCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道业务代码
   * </pre>
   *
   * <code>string business_code = 4;</code>
   * @return The bytes for businessCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBusinessCodeBytes() {
    java.lang.Object ref = businessCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      businessCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MERCHANT_ID_FIELD_NUMBER = 5;
  private volatile java.lang.Object merchantId_;
  /**
   * <pre>
   * 第三方商户PID
   * </pre>
   *
   * <code>string merchant_id = 5;</code>
   * @return The merchantId.
   */
  @java.lang.Override
  public java.lang.String getMerchantId() {
    java.lang.Object ref = merchantId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      merchantId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 第三方商户PID
   * </pre>
   *
   * <code>string merchant_id = 5;</code>
   * @return The bytes for merchantId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMerchantIdBytes() {
    java.lang.Object ref = merchantId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      merchantId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int APP_ID_FIELD_NUMBER = 6;
  private volatile java.lang.Object appId_;
  /**
   * <pre>
   * 第三方app id
   * </pre>
   *
   * <code>string app_id = 6;</code>
   * @return The appId.
   */
  @java.lang.Override
  public java.lang.String getAppId() {
    java.lang.Object ref = appId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      appId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 第三方app id
   * </pre>
   *
   * <code>string app_id = 6;</code>
   * @return The bytes for appId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAppIdBytes() {
    java.lang.Object ref = appId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      appId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int APP_KEY_FIELD_NUMBER = 7;
  private volatile java.lang.Object appKey_;
  /**
   * <pre>
   * 第三方app key
   * </pre>
   *
   * <code>string app_key = 7;</code>
   * @return The appKey.
   */
  @java.lang.Override
  public java.lang.String getAppKey() {
    java.lang.Object ref = appKey_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      appKey_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 第三方app key
   * </pre>
   *
   * <code>string app_key = 7;</code>
   * @return The bytes for appKey.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAppKeyBytes() {
    java.lang.Object ref = appKey_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      appKey_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACCESS_KEY_FIELD_NUMBER = 8;
  private volatile java.lang.Object accessKey_;
  /**
   * <pre>
   * 第三方access key
   * </pre>
   *
   * <code>string access_key = 8;</code>
   * @return The accessKey.
   */
  @java.lang.Override
  public java.lang.String getAccessKey() {
    java.lang.Object ref = accessKey_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      accessKey_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 第三方access key
   * </pre>
   *
   * <code>string access_key = 8;</code>
   * @return The bytes for accessKey.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAccessKeyBytes() {
    java.lang.Object ref = accessKey_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      accessKey_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CERT_FIELD_NUMBER = 9;
  private volatile java.lang.Object cert_;
  /**
   * <pre>
   * 第三方cert
   * </pre>
   *
   * <code>string cert = 9;</code>
   * @return The cert.
   */
  @java.lang.Override
  public java.lang.String getCert() {
    java.lang.Object ref = cert_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      cert_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 第三方cert
   * </pre>
   *
   * <code>string cert = 9;</code>
   * @return The bytes for cert.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCertBytes() {
    java.lang.Object ref = cert_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      cert_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PRIVATE_KEY_FIELD_NUMBER = 10;
  private volatile java.lang.Object privateKey_;
  /**
   * <pre>
   * 第三方private key
   * </pre>
   *
   * <code>string private_key = 10;</code>
   * @return The privateKey.
   */
  @java.lang.Override
  public java.lang.String getPrivateKey() {
    java.lang.Object ref = privateKey_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      privateKey_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 第三方private key
   * </pre>
   *
   * <code>string private_key = 10;</code>
   * @return The bytes for privateKey.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPrivateKeyBytes() {
    java.lang.Object ref = privateKey_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      privateKey_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PUBLIC_KEY_FIELD_NUMBER = 11;
  private volatile java.lang.Object publicKey_;
  /**
   * <pre>
   * 第三方public key
   * </pre>
   *
   * <code>string public_key = 11;</code>
   * @return The publicKey.
   */
  @java.lang.Override
  public java.lang.String getPublicKey() {
    java.lang.Object ref = publicKey_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      publicKey_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 第三方public key
   * </pre>
   *
   * <code>string public_key = 11;</code>
   * @return The bytes for publicKey.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPublicKeyBytes() {
    java.lang.Object ref = publicKey_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      publicKey_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int GATEWAY_URL_FIELD_NUMBER = 12;
  private volatile java.lang.Object gatewayUrl_;
  /**
   * <pre>
   * 第三方网关URL
   * </pre>
   *
   * <code>string gateway_url = 12;</code>
   * @return The gatewayUrl.
   */
  @java.lang.Override
  public java.lang.String getGatewayUrl() {
    java.lang.Object ref = gatewayUrl_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      gatewayUrl_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 第三方网关URL
   * </pre>
   *
   * <code>string gateway_url = 12;</code>
   * @return The bytes for gatewayUrl.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getGatewayUrlBytes() {
    java.lang.Object ref = gatewayUrl_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      gatewayUrl_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHANNELNAMEITEMS_FIELD_NUMBER = 13;
  private volatile java.lang.Object channelNameItems_;
  /**
   * <pre>
   * 支付名称集合
   * </pre>
   *
   * <code>string channelNameItems = 13;</code>
   * @return The channelNameItems.
   */
  @java.lang.Override
  public java.lang.String getChannelNameItems() {
    java.lang.Object ref = channelNameItems_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelNameItems_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 支付名称集合
   * </pre>
   *
   * <code>string channelNameItems = 13;</code>
   * @return The bytes for channelNameItems.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelNameItemsBytes() {
    java.lang.Object ref = channelNameItems_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelNameItems_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACCESS_ID_FIELD_NUMBER = 14;
  private long accessId_;
  /**
   * <pre>
   * 渠道访问授权配置id
   * </pre>
   *
   * <code>int64 access_id = 14;</code>
   * @return The accessId.
   */
  @java.lang.Override
  public long getAccessId() {
    return accessId_;
  }

  public static final int PARTNER_ID_FIELD_NUMBER = 15;
  private volatile java.lang.Object partnerId_;
  /**
   * <pre>
   * 租户id
   * </pre>
   *
   * <code>string partner_id = 15;</code>
   * @return The partnerId.
   */
  @java.lang.Override
  public java.lang.String getPartnerId() {
    java.lang.Object ref = partnerId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      partnerId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 租户id
   * </pre>
   *
   * <code>string partner_id = 15;</code>
   * @return The bytes for partnerId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPartnerIdBytes() {
    java.lang.Object ref = partnerId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      partnerId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int COMPANY_ID_FIELD_NUMBER = 16;
  private volatile java.lang.Object companyId_;
  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 16;</code>
   * @return The companyId.
   */
  @java.lang.Override
  public java.lang.String getCompanyId() {
    java.lang.Object ref = companyId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      companyId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 16;</code>
   * @return The bytes for companyId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCompanyIdBytes() {
    java.lang.Object ref = companyId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      companyId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STORE_ID_FIELD_NUMBER = 17;
  private volatile java.lang.Object storeId_;
  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 17;</code>
   * @return The storeId.
   */
  @java.lang.Override
  public java.lang.String getStoreId() {
    java.lang.Object ref = storeId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      storeId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 17;</code>
   * @return The bytes for storeId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStoreIdBytes() {
    java.lang.Object ref = storeId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      storeId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TERMINAL_ID_FIELD_NUMBER = 18;
  private volatile java.lang.Object terminalId_;
  /**
   * <pre>
   * 终端id
   * </pre>
   *
   * <code>string terminal_id = 18;</code>
   * @return The terminalId.
   */
  @java.lang.Override
  public java.lang.String getTerminalId() {
    java.lang.Object ref = terminalId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      terminalId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 终端id
   * </pre>
   *
   * <code>string terminal_id = 18;</code>
   * @return The bytes for terminalId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTerminalIdBytes() {
    java.lang.Object ref = terminalId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      terminalId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int API_VERSION_FIELD_NUMBER = 19;
  private volatile java.lang.Object apiVersion_;
  /**
   * <pre>
   * 第三方api版本
   * </pre>
   *
   * <code>string api_version = 19;</code>
   * @return The apiVersion.
   */
  @java.lang.Override
  public java.lang.String getApiVersion() {
    java.lang.Object ref = apiVersion_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      apiVersion_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 第三方api版本
   * </pre>
   *
   * <code>string api_version = 19;</code>
   * @return The bytes for apiVersion.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getApiVersionBytes() {
    java.lang.Object ref = apiVersion_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      apiVersion_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SUB_MERCHANT_ID_FIELD_NUMBER = 20;
  private volatile java.lang.Object subMerchantId_;
  /**
   * <pre>
   * 子商户号
   * </pre>
   *
   * <code>string sub_merchant_id = 20;</code>
   * @return The subMerchantId.
   */
  @java.lang.Override
  public java.lang.String getSubMerchantId() {
    java.lang.Object ref = subMerchantId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      subMerchantId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 子商户号
   * </pre>
   *
   * <code>string sub_merchant_id = 20;</code>
   * @return The bytes for subMerchantId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSubMerchantIdBytes() {
    java.lang.Object ref = subMerchantId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      subMerchantId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getChannelCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelCode_);
    }
    if (applyTo_ != 0) {
      output.writeInt32(2, applyTo_);
    }
    for (int i = 0; i < applyTargets_.size(); i++) {
      output.writeMessage(3, applyTargets_.get(i));
    }
    if (!getBusinessCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, businessCode_);
    }
    if (!getMerchantIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, merchantId_);
    }
    if (!getAppIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, appId_);
    }
    if (!getAppKeyBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, appKey_);
    }
    if (!getAccessKeyBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, accessKey_);
    }
    if (!getCertBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, cert_);
    }
    if (!getPrivateKeyBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, privateKey_);
    }
    if (!getPublicKeyBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, publicKey_);
    }
    if (!getGatewayUrlBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 12, gatewayUrl_);
    }
    if (!getChannelNameItemsBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 13, channelNameItems_);
    }
    if (accessId_ != 0L) {
      output.writeInt64(14, accessId_);
    }
    if (!getPartnerIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 15, partnerId_);
    }
    if (!getCompanyIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 16, companyId_);
    }
    if (!getStoreIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 17, storeId_);
    }
    if (!getTerminalIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 18, terminalId_);
    }
    if (!getApiVersionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 19, apiVersion_);
    }
    if (!getSubMerchantIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 20, subMerchantId_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getChannelCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelCode_);
    }
    if (applyTo_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, applyTo_);
    }
    for (int i = 0; i < applyTargets_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, applyTargets_.get(i));
    }
    if (!getBusinessCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, businessCode_);
    }
    if (!getMerchantIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, merchantId_);
    }
    if (!getAppIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, appId_);
    }
    if (!getAppKeyBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, appKey_);
    }
    if (!getAccessKeyBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, accessKey_);
    }
    if (!getCertBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, cert_);
    }
    if (!getPrivateKeyBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, privateKey_);
    }
    if (!getPublicKeyBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, publicKey_);
    }
    if (!getGatewayUrlBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, gatewayUrl_);
    }
    if (!getChannelNameItemsBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, channelNameItems_);
    }
    if (accessId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(14, accessId_);
    }
    if (!getPartnerIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, partnerId_);
    }
    if (!getCompanyIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, companyId_);
    }
    if (!getStoreIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, storeId_);
    }
    if (!getTerminalIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(18, terminalId_);
    }
    if (!getApiVersionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(19, apiVersion_);
    }
    if (!getSubMerchantIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(20, subMerchantId_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.Access)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.Access other = (cn.hexcloud.pbis.common.service.facade.channel.Access) obj;

    if (!getChannelCode()
        .equals(other.getChannelCode())) return false;
    if (getApplyTo()
        != other.getApplyTo()) return false;
    if (!getApplyTargetsList()
        .equals(other.getApplyTargetsList())) return false;
    if (!getBusinessCode()
        .equals(other.getBusinessCode())) return false;
    if (!getMerchantId()
        .equals(other.getMerchantId())) return false;
    if (!getAppId()
        .equals(other.getAppId())) return false;
    if (!getAppKey()
        .equals(other.getAppKey())) return false;
    if (!getAccessKey()
        .equals(other.getAccessKey())) return false;
    if (!getCert()
        .equals(other.getCert())) return false;
    if (!getPrivateKey()
        .equals(other.getPrivateKey())) return false;
    if (!getPublicKey()
        .equals(other.getPublicKey())) return false;
    if (!getGatewayUrl()
        .equals(other.getGatewayUrl())) return false;
    if (!getChannelNameItems()
        .equals(other.getChannelNameItems())) return false;
    if (getAccessId()
        != other.getAccessId()) return false;
    if (!getPartnerId()
        .equals(other.getPartnerId())) return false;
    if (!getCompanyId()
        .equals(other.getCompanyId())) return false;
    if (!getStoreId()
        .equals(other.getStoreId())) return false;
    if (!getTerminalId()
        .equals(other.getTerminalId())) return false;
    if (!getApiVersion()
        .equals(other.getApiVersion())) return false;
    if (!getSubMerchantId()
        .equals(other.getSubMerchantId())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHANNEL_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getChannelCode().hashCode();
    hash = (37 * hash) + APPLY_TO_FIELD_NUMBER;
    hash = (53 * hash) + getApplyTo();
    if (getApplyTargetsCount() > 0) {
      hash = (37 * hash) + APPLY_TARGETS_FIELD_NUMBER;
      hash = (53 * hash) + getApplyTargetsList().hashCode();
    }
    hash = (37 * hash) + BUSINESS_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getBusinessCode().hashCode();
    hash = (37 * hash) + MERCHANT_ID_FIELD_NUMBER;
    hash = (53 * hash) + getMerchantId().hashCode();
    hash = (37 * hash) + APP_ID_FIELD_NUMBER;
    hash = (53 * hash) + getAppId().hashCode();
    hash = (37 * hash) + APP_KEY_FIELD_NUMBER;
    hash = (53 * hash) + getAppKey().hashCode();
    hash = (37 * hash) + ACCESS_KEY_FIELD_NUMBER;
    hash = (53 * hash) + getAccessKey().hashCode();
    hash = (37 * hash) + CERT_FIELD_NUMBER;
    hash = (53 * hash) + getCert().hashCode();
    hash = (37 * hash) + PRIVATE_KEY_FIELD_NUMBER;
    hash = (53 * hash) + getPrivateKey().hashCode();
    hash = (37 * hash) + PUBLIC_KEY_FIELD_NUMBER;
    hash = (53 * hash) + getPublicKey().hashCode();
    hash = (37 * hash) + GATEWAY_URL_FIELD_NUMBER;
    hash = (53 * hash) + getGatewayUrl().hashCode();
    hash = (37 * hash) + CHANNELNAMEITEMS_FIELD_NUMBER;
    hash = (53 * hash) + getChannelNameItems().hashCode();
    hash = (37 * hash) + ACCESS_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getAccessId());
    hash = (37 * hash) + PARTNER_ID_FIELD_NUMBER;
    hash = (53 * hash) + getPartnerId().hashCode();
    hash = (37 * hash) + COMPANY_ID_FIELD_NUMBER;
    hash = (53 * hash) + getCompanyId().hashCode();
    hash = (37 * hash) + STORE_ID_FIELD_NUMBER;
    hash = (53 * hash) + getStoreId().hashCode();
    hash = (37 * hash) + TERMINAL_ID_FIELD_NUMBER;
    hash = (53 * hash) + getTerminalId().hashCode();
    hash = (37 * hash) + API_VERSION_FIELD_NUMBER;
    hash = (53 * hash) + getApiVersion().hashCode();
    hash = (37 * hash) + SUB_MERCHANT_ID_FIELD_NUMBER;
    hash = (53 * hash) + getSubMerchantId().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.Access parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Access parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Access parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Access parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Access parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Access parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Access parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Access parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Access parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Access parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Access parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Access parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.Access prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 第三方访问授权配置
   * </pre>
   *
   * Protobuf type {@code channel.Access}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.Access)
      cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_Access_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_Access_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.Access.class, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.Access.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getApplyTargetsFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channelCode_ = "";

      applyTo_ = 0;

      if (applyTargetsBuilder_ == null) {
        applyTargets_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        applyTargetsBuilder_.clear();
      }
      businessCode_ = "";

      merchantId_ = "";

      appId_ = "";

      appKey_ = "";

      accessKey_ = "";

      cert_ = "";

      privateKey_ = "";

      publicKey_ = "";

      gatewayUrl_ = "";

      channelNameItems_ = "";

      accessId_ = 0L;

      partnerId_ = "";

      companyId_ = "";

      storeId_ = "";

      terminalId_ = "";

      apiVersion_ = "";

      subMerchantId_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_Access_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.Access getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.Access.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.Access build() {
      cn.hexcloud.pbis.common.service.facade.channel.Access result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.Access buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.Access result = new cn.hexcloud.pbis.common.service.facade.channel.Access(this);
      int from_bitField0_ = bitField0_;
      result.channelCode_ = channelCode_;
      result.applyTo_ = applyTo_;
      if (applyTargetsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          applyTargets_ = java.util.Collections.unmodifiableList(applyTargets_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.applyTargets_ = applyTargets_;
      } else {
        result.applyTargets_ = applyTargetsBuilder_.build();
      }
      result.businessCode_ = businessCode_;
      result.merchantId_ = merchantId_;
      result.appId_ = appId_;
      result.appKey_ = appKey_;
      result.accessKey_ = accessKey_;
      result.cert_ = cert_;
      result.privateKey_ = privateKey_;
      result.publicKey_ = publicKey_;
      result.gatewayUrl_ = gatewayUrl_;
      result.channelNameItems_ = channelNameItems_;
      result.accessId_ = accessId_;
      result.partnerId_ = partnerId_;
      result.companyId_ = companyId_;
      result.storeId_ = storeId_;
      result.terminalId_ = terminalId_;
      result.apiVersion_ = apiVersion_;
      result.subMerchantId_ = subMerchantId_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.Access) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.Access)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.Access other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.Access.getDefaultInstance()) return this;
      if (!other.getChannelCode().isEmpty()) {
        channelCode_ = other.channelCode_;
        onChanged();
      }
      if (other.getApplyTo() != 0) {
        setApplyTo(other.getApplyTo());
      }
      if (applyTargetsBuilder_ == null) {
        if (!other.applyTargets_.isEmpty()) {
          if (applyTargets_.isEmpty()) {
            applyTargets_ = other.applyTargets_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureApplyTargetsIsMutable();
            applyTargets_.addAll(other.applyTargets_);
          }
          onChanged();
        }
      } else {
        if (!other.applyTargets_.isEmpty()) {
          if (applyTargetsBuilder_.isEmpty()) {
            applyTargetsBuilder_.dispose();
            applyTargetsBuilder_ = null;
            applyTargets_ = other.applyTargets_;
            bitField0_ = (bitField0_ & ~0x00000001);
            applyTargetsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getApplyTargetsFieldBuilder() : null;
          } else {
            applyTargetsBuilder_.addAllMessages(other.applyTargets_);
          }
        }
      }
      if (!other.getBusinessCode().isEmpty()) {
        businessCode_ = other.businessCode_;
        onChanged();
      }
      if (!other.getMerchantId().isEmpty()) {
        merchantId_ = other.merchantId_;
        onChanged();
      }
      if (!other.getAppId().isEmpty()) {
        appId_ = other.appId_;
        onChanged();
      }
      if (!other.getAppKey().isEmpty()) {
        appKey_ = other.appKey_;
        onChanged();
      }
      if (!other.getAccessKey().isEmpty()) {
        accessKey_ = other.accessKey_;
        onChanged();
      }
      if (!other.getCert().isEmpty()) {
        cert_ = other.cert_;
        onChanged();
      }
      if (!other.getPrivateKey().isEmpty()) {
        privateKey_ = other.privateKey_;
        onChanged();
      }
      if (!other.getPublicKey().isEmpty()) {
        publicKey_ = other.publicKey_;
        onChanged();
      }
      if (!other.getGatewayUrl().isEmpty()) {
        gatewayUrl_ = other.gatewayUrl_;
        onChanged();
      }
      if (!other.getChannelNameItems().isEmpty()) {
        channelNameItems_ = other.channelNameItems_;
        onChanged();
      }
      if (other.getAccessId() != 0L) {
        setAccessId(other.getAccessId());
      }
      if (!other.getPartnerId().isEmpty()) {
        partnerId_ = other.partnerId_;
        onChanged();
      }
      if (!other.getCompanyId().isEmpty()) {
        companyId_ = other.companyId_;
        onChanged();
      }
      if (!other.getStoreId().isEmpty()) {
        storeId_ = other.storeId_;
        onChanged();
      }
      if (!other.getTerminalId().isEmpty()) {
        terminalId_ = other.terminalId_;
        onChanged();
      }
      if (!other.getApiVersion().isEmpty()) {
        apiVersion_ = other.apiVersion_;
        onChanged();
      }
      if (!other.getSubMerchantId().isEmpty()) {
        subMerchantId_ = other.subMerchantId_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.Access parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.Access) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object channelCode_ = "";
    /**
     * <pre>
     * 渠道代码
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return The channelCode.
     */
    public java.lang.String getChannelCode() {
      java.lang.Object ref = channelCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道代码
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return The bytes for channelCode.
     */
    public com.google.protobuf.ByteString
        getChannelCodeBytes() {
      java.lang.Object ref = channelCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道代码
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @param value The channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道代码
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelCode() {
      
      channelCode_ = getDefaultInstance().getChannelCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道代码
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @param value The bytes for channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelCode_ = value;
      onChanged();
      return this;
    }

    private int applyTo_ ;
    /**
     * <pre>
     * 配置层级，0 租户；1 公司；2 门店
     * </pre>
     *
     * <code>int32 apply_to = 2;</code>
     * @return The applyTo.
     */
    @java.lang.Override
    public int getApplyTo() {
      return applyTo_;
    }
    /**
     * <pre>
     * 配置层级，0 租户；1 公司；2 门店
     * </pre>
     *
     * <code>int32 apply_to = 2;</code>
     * @param value The applyTo to set.
     * @return This builder for chaining.
     */
    public Builder setApplyTo(int value) {
      
      applyTo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 配置层级，0 租户；1 公司；2 门店
     * </pre>
     *
     * <code>int32 apply_to = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearApplyTo() {
      
      applyTo_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget> applyTargets_ =
      java.util.Collections.emptyList();
    private void ensureApplyTargetsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        applyTargets_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget>(applyTargets_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder, cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder> applyTargetsBuilder_;

    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget> getApplyTargetsList() {
      if (applyTargetsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(applyTargets_);
      } else {
        return applyTargetsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public int getApplyTargetsCount() {
      if (applyTargetsBuilder_ == null) {
        return applyTargets_.size();
      } else {
        return applyTargetsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget getApplyTargets(int index) {
      if (applyTargetsBuilder_ == null) {
        return applyTargets_.get(index);
      } else {
        return applyTargetsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public Builder setApplyTargets(
        int index, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget value) {
      if (applyTargetsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureApplyTargetsIsMutable();
        applyTargets_.set(index, value);
        onChanged();
      } else {
        applyTargetsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public Builder setApplyTargets(
        int index, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder builderForValue) {
      if (applyTargetsBuilder_ == null) {
        ensureApplyTargetsIsMutable();
        applyTargets_.set(index, builderForValue.build());
        onChanged();
      } else {
        applyTargetsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public Builder addApplyTargets(cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget value) {
      if (applyTargetsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureApplyTargetsIsMutable();
        applyTargets_.add(value);
        onChanged();
      } else {
        applyTargetsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public Builder addApplyTargets(
        int index, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget value) {
      if (applyTargetsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureApplyTargetsIsMutable();
        applyTargets_.add(index, value);
        onChanged();
      } else {
        applyTargetsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public Builder addApplyTargets(
        cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder builderForValue) {
      if (applyTargetsBuilder_ == null) {
        ensureApplyTargetsIsMutable();
        applyTargets_.add(builderForValue.build());
        onChanged();
      } else {
        applyTargetsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public Builder addApplyTargets(
        int index, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder builderForValue) {
      if (applyTargetsBuilder_ == null) {
        ensureApplyTargetsIsMutable();
        applyTargets_.add(index, builderForValue.build());
        onChanged();
      } else {
        applyTargetsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public Builder addAllApplyTargets(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget> values) {
      if (applyTargetsBuilder_ == null) {
        ensureApplyTargetsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, applyTargets_);
        onChanged();
      } else {
        applyTargetsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public Builder clearApplyTargets() {
      if (applyTargetsBuilder_ == null) {
        applyTargets_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        applyTargetsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public Builder removeApplyTargets(int index) {
      if (applyTargetsBuilder_ == null) {
        ensureApplyTargetsIsMutable();
        applyTargets_.remove(index);
        onChanged();
      } else {
        applyTargetsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder getApplyTargetsBuilder(
        int index) {
      return getApplyTargetsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder getApplyTargetsOrBuilder(
        int index) {
      if (applyTargetsBuilder_ == null) {
        return applyTargets_.get(index);  } else {
        return applyTargetsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder> 
         getApplyTargetsOrBuilderList() {
      if (applyTargetsBuilder_ != null) {
        return applyTargetsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(applyTargets_);
      }
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder addApplyTargetsBuilder() {
      return getApplyTargetsFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.getDefaultInstance());
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder addApplyTargetsBuilder(
        int index) {
      return getApplyTargetsFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.getDefaultInstance());
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder> 
         getApplyTargetsBuilderList() {
      return getApplyTargetsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder, cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder> 
        getApplyTargetsFieldBuilder() {
      if (applyTargetsBuilder_ == null) {
        applyTargetsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder, cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder>(
                applyTargets_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        applyTargets_ = null;
      }
      return applyTargetsBuilder_;
    }

    private java.lang.Object businessCode_ = "";
    /**
     * <pre>
     * 渠道业务代码
     * </pre>
     *
     * <code>string business_code = 4;</code>
     * @return The businessCode.
     */
    public java.lang.String getBusinessCode() {
      java.lang.Object ref = businessCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        businessCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道业务代码
     * </pre>
     *
     * <code>string business_code = 4;</code>
     * @return The bytes for businessCode.
     */
    public com.google.protobuf.ByteString
        getBusinessCodeBytes() {
      java.lang.Object ref = businessCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        businessCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道业务代码
     * </pre>
     *
     * <code>string business_code = 4;</code>
     * @param value The businessCode to set.
     * @return This builder for chaining.
     */
    public Builder setBusinessCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      businessCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道业务代码
     * </pre>
     *
     * <code>string business_code = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearBusinessCode() {
      
      businessCode_ = getDefaultInstance().getBusinessCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道业务代码
     * </pre>
     *
     * <code>string business_code = 4;</code>
     * @param value The bytes for businessCode to set.
     * @return This builder for chaining.
     */
    public Builder setBusinessCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      businessCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object merchantId_ = "";
    /**
     * <pre>
     * 第三方商户PID
     * </pre>
     *
     * <code>string merchant_id = 5;</code>
     * @return The merchantId.
     */
    public java.lang.String getMerchantId() {
      java.lang.Object ref = merchantId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        merchantId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 第三方商户PID
     * </pre>
     *
     * <code>string merchant_id = 5;</code>
     * @return The bytes for merchantId.
     */
    public com.google.protobuf.ByteString
        getMerchantIdBytes() {
      java.lang.Object ref = merchantId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        merchantId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 第三方商户PID
     * </pre>
     *
     * <code>string merchant_id = 5;</code>
     * @param value The merchantId to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      merchantId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方商户PID
     * </pre>
     *
     * <code>string merchant_id = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearMerchantId() {
      
      merchantId_ = getDefaultInstance().getMerchantId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方商户PID
     * </pre>
     *
     * <code>string merchant_id = 5;</code>
     * @param value The bytes for merchantId to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      merchantId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object appId_ = "";
    /**
     * <pre>
     * 第三方app id
     * </pre>
     *
     * <code>string app_id = 6;</code>
     * @return The appId.
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 第三方app id
     * </pre>
     *
     * <code>string app_id = 6;</code>
     * @return The bytes for appId.
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 第三方app id
     * </pre>
     *
     * <code>string app_id = 6;</code>
     * @param value The appId to set.
     * @return This builder for chaining.
     */
    public Builder setAppId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      appId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方app id
     * </pre>
     *
     * <code>string app_id = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppId() {
      
      appId_ = getDefaultInstance().getAppId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方app id
     * </pre>
     *
     * <code>string app_id = 6;</code>
     * @param value The bytes for appId to set.
     * @return This builder for chaining.
     */
    public Builder setAppIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      appId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object appKey_ = "";
    /**
     * <pre>
     * 第三方app key
     * </pre>
     *
     * <code>string app_key = 7;</code>
     * @return The appKey.
     */
    public java.lang.String getAppKey() {
      java.lang.Object ref = appKey_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appKey_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 第三方app key
     * </pre>
     *
     * <code>string app_key = 7;</code>
     * @return The bytes for appKey.
     */
    public com.google.protobuf.ByteString
        getAppKeyBytes() {
      java.lang.Object ref = appKey_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 第三方app key
     * </pre>
     *
     * <code>string app_key = 7;</code>
     * @param value The appKey to set.
     * @return This builder for chaining.
     */
    public Builder setAppKey(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      appKey_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方app key
     * </pre>
     *
     * <code>string app_key = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppKey() {
      
      appKey_ = getDefaultInstance().getAppKey();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方app key
     * </pre>
     *
     * <code>string app_key = 7;</code>
     * @param value The bytes for appKey to set.
     * @return This builder for chaining.
     */
    public Builder setAppKeyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      appKey_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object accessKey_ = "";
    /**
     * <pre>
     * 第三方access key
     * </pre>
     *
     * <code>string access_key = 8;</code>
     * @return The accessKey.
     */
    public java.lang.String getAccessKey() {
      java.lang.Object ref = accessKey_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        accessKey_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 第三方access key
     * </pre>
     *
     * <code>string access_key = 8;</code>
     * @return The bytes for accessKey.
     */
    public com.google.protobuf.ByteString
        getAccessKeyBytes() {
      java.lang.Object ref = accessKey_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        accessKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 第三方access key
     * </pre>
     *
     * <code>string access_key = 8;</code>
     * @param value The accessKey to set.
     * @return This builder for chaining.
     */
    public Builder setAccessKey(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      accessKey_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方access key
     * </pre>
     *
     * <code>string access_key = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearAccessKey() {
      
      accessKey_ = getDefaultInstance().getAccessKey();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方access key
     * </pre>
     *
     * <code>string access_key = 8;</code>
     * @param value The bytes for accessKey to set.
     * @return This builder for chaining.
     */
    public Builder setAccessKeyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      accessKey_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object cert_ = "";
    /**
     * <pre>
     * 第三方cert
     * </pre>
     *
     * <code>string cert = 9;</code>
     * @return The cert.
     */
    public java.lang.String getCert() {
      java.lang.Object ref = cert_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        cert_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 第三方cert
     * </pre>
     *
     * <code>string cert = 9;</code>
     * @return The bytes for cert.
     */
    public com.google.protobuf.ByteString
        getCertBytes() {
      java.lang.Object ref = cert_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cert_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 第三方cert
     * </pre>
     *
     * <code>string cert = 9;</code>
     * @param value The cert to set.
     * @return This builder for chaining.
     */
    public Builder setCert(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      cert_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方cert
     * </pre>
     *
     * <code>string cert = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearCert() {
      
      cert_ = getDefaultInstance().getCert();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方cert
     * </pre>
     *
     * <code>string cert = 9;</code>
     * @param value The bytes for cert to set.
     * @return This builder for chaining.
     */
    public Builder setCertBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      cert_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object privateKey_ = "";
    /**
     * <pre>
     * 第三方private key
     * </pre>
     *
     * <code>string private_key = 10;</code>
     * @return The privateKey.
     */
    public java.lang.String getPrivateKey() {
      java.lang.Object ref = privateKey_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        privateKey_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 第三方private key
     * </pre>
     *
     * <code>string private_key = 10;</code>
     * @return The bytes for privateKey.
     */
    public com.google.protobuf.ByteString
        getPrivateKeyBytes() {
      java.lang.Object ref = privateKey_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        privateKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 第三方private key
     * </pre>
     *
     * <code>string private_key = 10;</code>
     * @param value The privateKey to set.
     * @return This builder for chaining.
     */
    public Builder setPrivateKey(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      privateKey_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方private key
     * </pre>
     *
     * <code>string private_key = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrivateKey() {
      
      privateKey_ = getDefaultInstance().getPrivateKey();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方private key
     * </pre>
     *
     * <code>string private_key = 10;</code>
     * @param value The bytes for privateKey to set.
     * @return This builder for chaining.
     */
    public Builder setPrivateKeyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      privateKey_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object publicKey_ = "";
    /**
     * <pre>
     * 第三方public key
     * </pre>
     *
     * <code>string public_key = 11;</code>
     * @return The publicKey.
     */
    public java.lang.String getPublicKey() {
      java.lang.Object ref = publicKey_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        publicKey_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 第三方public key
     * </pre>
     *
     * <code>string public_key = 11;</code>
     * @return The bytes for publicKey.
     */
    public com.google.protobuf.ByteString
        getPublicKeyBytes() {
      java.lang.Object ref = publicKey_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        publicKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 第三方public key
     * </pre>
     *
     * <code>string public_key = 11;</code>
     * @param value The publicKey to set.
     * @return This builder for chaining.
     */
    public Builder setPublicKey(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      publicKey_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方public key
     * </pre>
     *
     * <code>string public_key = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearPublicKey() {
      
      publicKey_ = getDefaultInstance().getPublicKey();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方public key
     * </pre>
     *
     * <code>string public_key = 11;</code>
     * @param value The bytes for publicKey to set.
     * @return This builder for chaining.
     */
    public Builder setPublicKeyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      publicKey_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object gatewayUrl_ = "";
    /**
     * <pre>
     * 第三方网关URL
     * </pre>
     *
     * <code>string gateway_url = 12;</code>
     * @return The gatewayUrl.
     */
    public java.lang.String getGatewayUrl() {
      java.lang.Object ref = gatewayUrl_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        gatewayUrl_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 第三方网关URL
     * </pre>
     *
     * <code>string gateway_url = 12;</code>
     * @return The bytes for gatewayUrl.
     */
    public com.google.protobuf.ByteString
        getGatewayUrlBytes() {
      java.lang.Object ref = gatewayUrl_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gatewayUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 第三方网关URL
     * </pre>
     *
     * <code>string gateway_url = 12;</code>
     * @param value The gatewayUrl to set.
     * @return This builder for chaining.
     */
    public Builder setGatewayUrl(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      gatewayUrl_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方网关URL
     * </pre>
     *
     * <code>string gateway_url = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearGatewayUrl() {
      
      gatewayUrl_ = getDefaultInstance().getGatewayUrl();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方网关URL
     * </pre>
     *
     * <code>string gateway_url = 12;</code>
     * @param value The bytes for gatewayUrl to set.
     * @return This builder for chaining.
     */
    public Builder setGatewayUrlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      gatewayUrl_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object channelNameItems_ = "";
    /**
     * <pre>
     * 支付名称集合
     * </pre>
     *
     * <code>string channelNameItems = 13;</code>
     * @return The channelNameItems.
     */
    public java.lang.String getChannelNameItems() {
      java.lang.Object ref = channelNameItems_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelNameItems_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 支付名称集合
     * </pre>
     *
     * <code>string channelNameItems = 13;</code>
     * @return The bytes for channelNameItems.
     */
    public com.google.protobuf.ByteString
        getChannelNameItemsBytes() {
      java.lang.Object ref = channelNameItems_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelNameItems_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 支付名称集合
     * </pre>
     *
     * <code>string channelNameItems = 13;</code>
     * @param value The channelNameItems to set.
     * @return This builder for chaining.
     */
    public Builder setChannelNameItems(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelNameItems_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 支付名称集合
     * </pre>
     *
     * <code>string channelNameItems = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelNameItems() {
      
      channelNameItems_ = getDefaultInstance().getChannelNameItems();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 支付名称集合
     * </pre>
     *
     * <code>string channelNameItems = 13;</code>
     * @param value The bytes for channelNameItems to set.
     * @return This builder for chaining.
     */
    public Builder setChannelNameItemsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelNameItems_ = value;
      onChanged();
      return this;
    }

    private long accessId_ ;
    /**
     * <pre>
     * 渠道访问授权配置id
     * </pre>
     *
     * <code>int64 access_id = 14;</code>
     * @return The accessId.
     */
    @java.lang.Override
    public long getAccessId() {
      return accessId_;
    }
    /**
     * <pre>
     * 渠道访问授权配置id
     * </pre>
     *
     * <code>int64 access_id = 14;</code>
     * @param value The accessId to set.
     * @return This builder for chaining.
     */
    public Builder setAccessId(long value) {
      
      accessId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道访问授权配置id
     * </pre>
     *
     * <code>int64 access_id = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearAccessId() {
      
      accessId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object partnerId_ = "";
    /**
     * <pre>
     * 租户id
     * </pre>
     *
     * <code>string partner_id = 15;</code>
     * @return The partnerId.
     */
    public java.lang.String getPartnerId() {
      java.lang.Object ref = partnerId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        partnerId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 租户id
     * </pre>
     *
     * <code>string partner_id = 15;</code>
     * @return The bytes for partnerId.
     */
    public com.google.protobuf.ByteString
        getPartnerIdBytes() {
      java.lang.Object ref = partnerId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        partnerId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 租户id
     * </pre>
     *
     * <code>string partner_id = 15;</code>
     * @param value The partnerId to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      partnerId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 租户id
     * </pre>
     *
     * <code>string partner_id = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearPartnerId() {
      
      partnerId_ = getDefaultInstance().getPartnerId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 租户id
     * </pre>
     *
     * <code>string partner_id = 15;</code>
     * @param value The bytes for partnerId to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      partnerId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object companyId_ = "";
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 16;</code>
     * @return The companyId.
     */
    public java.lang.String getCompanyId() {
      java.lang.Object ref = companyId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        companyId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 16;</code>
     * @return The bytes for companyId.
     */
    public com.google.protobuf.ByteString
        getCompanyIdBytes() {
      java.lang.Object ref = companyId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        companyId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 16;</code>
     * @param value The companyId to set.
     * @return This builder for chaining.
     */
    public Builder setCompanyId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      companyId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearCompanyId() {
      
      companyId_ = getDefaultInstance().getCompanyId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 16;</code>
     * @param value The bytes for companyId to set.
     * @return This builder for chaining.
     */
    public Builder setCompanyIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      companyId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object storeId_ = "";
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 17;</code>
     * @return The storeId.
     */
    public java.lang.String getStoreId() {
      java.lang.Object ref = storeId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        storeId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 17;</code>
     * @return The bytes for storeId.
     */
    public com.google.protobuf.ByteString
        getStoreIdBytes() {
      java.lang.Object ref = storeId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        storeId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 17;</code>
     * @param value The storeId to set.
     * @return This builder for chaining.
     */
    public Builder setStoreId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      storeId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearStoreId() {
      
      storeId_ = getDefaultInstance().getStoreId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 17;</code>
     * @param value The bytes for storeId to set.
     * @return This builder for chaining.
     */
    public Builder setStoreIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      storeId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object terminalId_ = "";
    /**
     * <pre>
     * 终端id
     * </pre>
     *
     * <code>string terminal_id = 18;</code>
     * @return The terminalId.
     */
    public java.lang.String getTerminalId() {
      java.lang.Object ref = terminalId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        terminalId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 终端id
     * </pre>
     *
     * <code>string terminal_id = 18;</code>
     * @return The bytes for terminalId.
     */
    public com.google.protobuf.ByteString
        getTerminalIdBytes() {
      java.lang.Object ref = terminalId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        terminalId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 终端id
     * </pre>
     *
     * <code>string terminal_id = 18;</code>
     * @param value The terminalId to set.
     * @return This builder for chaining.
     */
    public Builder setTerminalId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      terminalId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 终端id
     * </pre>
     *
     * <code>string terminal_id = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearTerminalId() {
      
      terminalId_ = getDefaultInstance().getTerminalId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 终端id
     * </pre>
     *
     * <code>string terminal_id = 18;</code>
     * @param value The bytes for terminalId to set.
     * @return This builder for chaining.
     */
    public Builder setTerminalIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      terminalId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object apiVersion_ = "";
    /**
     * <pre>
     * 第三方api版本
     * </pre>
     *
     * <code>string api_version = 19;</code>
     * @return The apiVersion.
     */
    public java.lang.String getApiVersion() {
      java.lang.Object ref = apiVersion_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        apiVersion_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 第三方api版本
     * </pre>
     *
     * <code>string api_version = 19;</code>
     * @return The bytes for apiVersion.
     */
    public com.google.protobuf.ByteString
        getApiVersionBytes() {
      java.lang.Object ref = apiVersion_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        apiVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 第三方api版本
     * </pre>
     *
     * <code>string api_version = 19;</code>
     * @param value The apiVersion to set.
     * @return This builder for chaining.
     */
    public Builder setApiVersion(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      apiVersion_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方api版本
     * </pre>
     *
     * <code>string api_version = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearApiVersion() {
      
      apiVersion_ = getDefaultInstance().getApiVersion();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方api版本
     * </pre>
     *
     * <code>string api_version = 19;</code>
     * @param value The bytes for apiVersion to set.
     * @return This builder for chaining.
     */
    public Builder setApiVersionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      apiVersion_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object subMerchantId_ = "";
    /**
     * <pre>
     * 子商户号
     * </pre>
     *
     * <code>string sub_merchant_id = 20;</code>
     * @return The subMerchantId.
     */
    public java.lang.String getSubMerchantId() {
      java.lang.Object ref = subMerchantId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        subMerchantId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 子商户号
     * </pre>
     *
     * <code>string sub_merchant_id = 20;</code>
     * @return The bytes for subMerchantId.
     */
    public com.google.protobuf.ByteString
        getSubMerchantIdBytes() {
      java.lang.Object ref = subMerchantId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        subMerchantId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 子商户号
     * </pre>
     *
     * <code>string sub_merchant_id = 20;</code>
     * @param value The subMerchantId to set.
     * @return This builder for chaining.
     */
    public Builder setSubMerchantId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      subMerchantId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 子商户号
     * </pre>
     *
     * <code>string sub_merchant_id = 20;</code>
     * @return This builder for chaining.
     */
    public Builder clearSubMerchantId() {
      
      subMerchantId_ = getDefaultInstance().getSubMerchantId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 子商户号
     * </pre>
     *
     * <code>string sub_merchant_id = 20;</code>
     * @param value The bytes for subMerchantId to set.
     * @return This builder for chaining.
     */
    public Builder setSubMerchantIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      subMerchantId_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.Access)
  }

  // @@protoc_insertion_point(class_scope:channel.Access)
  private static final cn.hexcloud.pbis.common.service.facade.channel.Access DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.Access();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.Access getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Access>
      PARSER = new com.google.protobuf.AbstractParser<Access>() {
    @java.lang.Override
    public Access parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Access(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Access> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Access> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.Access getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

