// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 获取渠道密钥信息
 * </pre>
 *
 * Protobuf type {@code channel.AccessConfig}
 */
public final class AccessConfig extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.AccessConfig)
    AccessConfigOrBuilder {
private static final long serialVersionUID = 0L;
  // Use AccessConfig.newBuilder() to construct.
  private AccessConfig(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private AccessConfig() {
    merchantId_ = "";
    appId_ = "";
    appKey_ = "";
    accessKey_ = "";
    cert_ = "";
    privateKey_ = "";
    publicKey_ = "";
    terminalId_ = "";
    gatewayUrl_ = "";
    authToken_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new AccessConfig();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private AccessConfig(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            merchantId_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            appId_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            appKey_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            accessKey_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            cert_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            privateKey_ = s;
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            publicKey_ = s;
            break;
          }
          case 66: {
            java.lang.String s = input.readStringRequireUtf8();

            terminalId_ = s;
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            gatewayUrl_ = s;
            break;
          }
          case 82: {
            java.lang.String s = input.readStringRequireUtf8();

            authToken_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AccessConfig_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AccessConfig_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.class, cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.Builder.class);
  }

  public static final int MERCHANT_ID_FIELD_NUMBER = 1;
  private volatile java.lang.Object merchantId_;
  /**
   * <pre>
   *商户id
   * </pre>
   *
   * <code>string merchant_id = 1;</code>
   * @return The merchantId.
   */
  @java.lang.Override
  public java.lang.String getMerchantId() {
    java.lang.Object ref = merchantId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      merchantId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *商户id
   * </pre>
   *
   * <code>string merchant_id = 1;</code>
   * @return The bytes for merchantId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMerchantIdBytes() {
    java.lang.Object ref = merchantId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      merchantId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int APP_ID_FIELD_NUMBER = 2;
  private volatile java.lang.Object appId_;
  /**
   * <pre>
   *小程序id
   * </pre>
   *
   * <code>string app_id = 2;</code>
   * @return The appId.
   */
  @java.lang.Override
  public java.lang.String getAppId() {
    java.lang.Object ref = appId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      appId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *小程序id
   * </pre>
   *
   * <code>string app_id = 2;</code>
   * @return The bytes for appId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAppIdBytes() {
    java.lang.Object ref = appId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      appId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int APP_KEY_FIELD_NUMBER = 3;
  private volatile java.lang.Object appKey_;
  /**
   * <pre>
   *应用密钥
   * </pre>
   *
   * <code>string app_key = 3;</code>
   * @return The appKey.
   */
  @java.lang.Override
  public java.lang.String getAppKey() {
    java.lang.Object ref = appKey_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      appKey_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *应用密钥
   * </pre>
   *
   * <code>string app_key = 3;</code>
   * @return The bytes for appKey.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAppKeyBytes() {
    java.lang.Object ref = appKey_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      appKey_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACCESS_KEY_FIELD_NUMBER = 4;
  private volatile java.lang.Object accessKey_;
  /**
   * <pre>
   *授权密钥
   * </pre>
   *
   * <code>string access_key = 4;</code>
   * @return The accessKey.
   */
  @java.lang.Override
  public java.lang.String getAccessKey() {
    java.lang.Object ref = accessKey_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      accessKey_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *授权密钥
   * </pre>
   *
   * <code>string access_key = 4;</code>
   * @return The bytes for accessKey.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAccessKeyBytes() {
    java.lang.Object ref = accessKey_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      accessKey_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CERT_FIELD_NUMBER = 5;
  private volatile java.lang.Object cert_;
  /**
   * <pre>
   *证书信息
   * </pre>
   *
   * <code>string cert = 5;</code>
   * @return The cert.
   */
  @java.lang.Override
  public java.lang.String getCert() {
    java.lang.Object ref = cert_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      cert_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *证书信息
   * </pre>
   *
   * <code>string cert = 5;</code>
   * @return The bytes for cert.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCertBytes() {
    java.lang.Object ref = cert_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      cert_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PRIVATE_KEY_FIELD_NUMBER = 6;
  private volatile java.lang.Object privateKey_;
  /**
   * <pre>
   *私钥
   * </pre>
   *
   * <code>string private_key = 6;</code>
   * @return The privateKey.
   */
  @java.lang.Override
  public java.lang.String getPrivateKey() {
    java.lang.Object ref = privateKey_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      privateKey_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *私钥
   * </pre>
   *
   * <code>string private_key = 6;</code>
   * @return The bytes for privateKey.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPrivateKeyBytes() {
    java.lang.Object ref = privateKey_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      privateKey_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PUBLIC_KEY_FIELD_NUMBER = 7;
  private volatile java.lang.Object publicKey_;
  /**
   * <pre>
   *公钥
   * </pre>
   *
   * <code>string public_key = 7;</code>
   * @return The publicKey.
   */
  @java.lang.Override
  public java.lang.String getPublicKey() {
    java.lang.Object ref = publicKey_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      publicKey_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *公钥
   * </pre>
   *
   * <code>string public_key = 7;</code>
   * @return The bytes for publicKey.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPublicKeyBytes() {
    java.lang.Object ref = publicKey_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      publicKey_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TERMINAL_ID_FIELD_NUMBER = 8;
  private volatile java.lang.Object terminalId_;
  /**
   * <pre>
   *终端ID
   * </pre>
   *
   * <code>string terminal_id = 8;</code>
   * @return The terminalId.
   */
  @java.lang.Override
  public java.lang.String getTerminalId() {
    java.lang.Object ref = terminalId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      terminalId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *终端ID
   * </pre>
   *
   * <code>string terminal_id = 8;</code>
   * @return The bytes for terminalId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTerminalIdBytes() {
    java.lang.Object ref = terminalId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      terminalId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int GATEWAY_URL_FIELD_NUMBER = 9;
  private volatile java.lang.Object gatewayUrl_;
  /**
   * <pre>
   *网关地址
   * </pre>
   *
   * <code>string gateway_url = 9;</code>
   * @return The gatewayUrl.
   */
  @java.lang.Override
  public java.lang.String getGatewayUrl() {
    java.lang.Object ref = gatewayUrl_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      gatewayUrl_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *网关地址
   * </pre>
   *
   * <code>string gateway_url = 9;</code>
   * @return The bytes for gatewayUrl.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getGatewayUrlBytes() {
    java.lang.Object ref = gatewayUrl_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      gatewayUrl_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AUTH_TOKEN_FIELD_NUMBER = 10;
  private volatile java.lang.Object authToken_;
  /**
   * <pre>
   *认证token
   * </pre>
   *
   * <code>string auth_token = 10;</code>
   * @return The authToken.
   */
  @java.lang.Override
  public java.lang.String getAuthToken() {
    java.lang.Object ref = authToken_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      authToken_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *认证token
   * </pre>
   *
   * <code>string auth_token = 10;</code>
   * @return The bytes for authToken.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAuthTokenBytes() {
    java.lang.Object ref = authToken_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      authToken_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getMerchantIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, merchantId_);
    }
    if (!getAppIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, appId_);
    }
    if (!getAppKeyBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, appKey_);
    }
    if (!getAccessKeyBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, accessKey_);
    }
    if (!getCertBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, cert_);
    }
    if (!getPrivateKeyBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, privateKey_);
    }
    if (!getPublicKeyBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, publicKey_);
    }
    if (!getTerminalIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, terminalId_);
    }
    if (!getGatewayUrlBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, gatewayUrl_);
    }
    if (!getAuthTokenBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, authToken_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getMerchantIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, merchantId_);
    }
    if (!getAppIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, appId_);
    }
    if (!getAppKeyBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, appKey_);
    }
    if (!getAccessKeyBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, accessKey_);
    }
    if (!getCertBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, cert_);
    }
    if (!getPrivateKeyBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, privateKey_);
    }
    if (!getPublicKeyBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, publicKey_);
    }
    if (!getTerminalIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, terminalId_);
    }
    if (!getGatewayUrlBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, gatewayUrl_);
    }
    if (!getAuthTokenBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, authToken_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.AccessConfig)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.AccessConfig other = (cn.hexcloud.pbis.common.service.facade.channel.AccessConfig) obj;

    if (!getMerchantId()
        .equals(other.getMerchantId())) return false;
    if (!getAppId()
        .equals(other.getAppId())) return false;
    if (!getAppKey()
        .equals(other.getAppKey())) return false;
    if (!getAccessKey()
        .equals(other.getAccessKey())) return false;
    if (!getCert()
        .equals(other.getCert())) return false;
    if (!getPrivateKey()
        .equals(other.getPrivateKey())) return false;
    if (!getPublicKey()
        .equals(other.getPublicKey())) return false;
    if (!getTerminalId()
        .equals(other.getTerminalId())) return false;
    if (!getGatewayUrl()
        .equals(other.getGatewayUrl())) return false;
    if (!getAuthToken()
        .equals(other.getAuthToken())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + MERCHANT_ID_FIELD_NUMBER;
    hash = (53 * hash) + getMerchantId().hashCode();
    hash = (37 * hash) + APP_ID_FIELD_NUMBER;
    hash = (53 * hash) + getAppId().hashCode();
    hash = (37 * hash) + APP_KEY_FIELD_NUMBER;
    hash = (53 * hash) + getAppKey().hashCode();
    hash = (37 * hash) + ACCESS_KEY_FIELD_NUMBER;
    hash = (53 * hash) + getAccessKey().hashCode();
    hash = (37 * hash) + CERT_FIELD_NUMBER;
    hash = (53 * hash) + getCert().hashCode();
    hash = (37 * hash) + PRIVATE_KEY_FIELD_NUMBER;
    hash = (53 * hash) + getPrivateKey().hashCode();
    hash = (37 * hash) + PUBLIC_KEY_FIELD_NUMBER;
    hash = (53 * hash) + getPublicKey().hashCode();
    hash = (37 * hash) + TERMINAL_ID_FIELD_NUMBER;
    hash = (53 * hash) + getTerminalId().hashCode();
    hash = (37 * hash) + GATEWAY_URL_FIELD_NUMBER;
    hash = (53 * hash) + getGatewayUrl().hashCode();
    hash = (37 * hash) + AUTH_TOKEN_FIELD_NUMBER;
    hash = (53 * hash) + getAuthToken().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.AccessConfig parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessConfig parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessConfig parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessConfig parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessConfig parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessConfig parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessConfig parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessConfig parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessConfig parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessConfig parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessConfig parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessConfig parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.AccessConfig prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 获取渠道密钥信息
   * </pre>
   *
   * Protobuf type {@code channel.AccessConfig}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.AccessConfig)
      cn.hexcloud.pbis.common.service.facade.channel.AccessConfigOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AccessConfig_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AccessConfig_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.class, cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      merchantId_ = "";

      appId_ = "";

      appKey_ = "";

      accessKey_ = "";

      cert_ = "";

      privateKey_ = "";

      publicKey_ = "";

      terminalId_ = "";

      gatewayUrl_ = "";

      authToken_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AccessConfig_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AccessConfig getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AccessConfig build() {
      cn.hexcloud.pbis.common.service.facade.channel.AccessConfig result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AccessConfig buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.AccessConfig result = new cn.hexcloud.pbis.common.service.facade.channel.AccessConfig(this);
      result.merchantId_ = merchantId_;
      result.appId_ = appId_;
      result.appKey_ = appKey_;
      result.accessKey_ = accessKey_;
      result.cert_ = cert_;
      result.privateKey_ = privateKey_;
      result.publicKey_ = publicKey_;
      result.terminalId_ = terminalId_;
      result.gatewayUrl_ = gatewayUrl_;
      result.authToken_ = authToken_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.AccessConfig) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.AccessConfig)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.AccessConfig other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.getDefaultInstance()) return this;
      if (!other.getMerchantId().isEmpty()) {
        merchantId_ = other.merchantId_;
        onChanged();
      }
      if (!other.getAppId().isEmpty()) {
        appId_ = other.appId_;
        onChanged();
      }
      if (!other.getAppKey().isEmpty()) {
        appKey_ = other.appKey_;
        onChanged();
      }
      if (!other.getAccessKey().isEmpty()) {
        accessKey_ = other.accessKey_;
        onChanged();
      }
      if (!other.getCert().isEmpty()) {
        cert_ = other.cert_;
        onChanged();
      }
      if (!other.getPrivateKey().isEmpty()) {
        privateKey_ = other.privateKey_;
        onChanged();
      }
      if (!other.getPublicKey().isEmpty()) {
        publicKey_ = other.publicKey_;
        onChanged();
      }
      if (!other.getTerminalId().isEmpty()) {
        terminalId_ = other.terminalId_;
        onChanged();
      }
      if (!other.getGatewayUrl().isEmpty()) {
        gatewayUrl_ = other.gatewayUrl_;
        onChanged();
      }
      if (!other.getAuthToken().isEmpty()) {
        authToken_ = other.authToken_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.AccessConfig parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.AccessConfig) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object merchantId_ = "";
    /**
     * <pre>
     *商户id
     * </pre>
     *
     * <code>string merchant_id = 1;</code>
     * @return The merchantId.
     */
    public java.lang.String getMerchantId() {
      java.lang.Object ref = merchantId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        merchantId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *商户id
     * </pre>
     *
     * <code>string merchant_id = 1;</code>
     * @return The bytes for merchantId.
     */
    public com.google.protobuf.ByteString
        getMerchantIdBytes() {
      java.lang.Object ref = merchantId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        merchantId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *商户id
     * </pre>
     *
     * <code>string merchant_id = 1;</code>
     * @param value The merchantId to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      merchantId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商户id
     * </pre>
     *
     * <code>string merchant_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearMerchantId() {
      
      merchantId_ = getDefaultInstance().getMerchantId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商户id
     * </pre>
     *
     * <code>string merchant_id = 1;</code>
     * @param value The bytes for merchantId to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      merchantId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object appId_ = "";
    /**
     * <pre>
     *小程序id
     * </pre>
     *
     * <code>string app_id = 2;</code>
     * @return The appId.
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *小程序id
     * </pre>
     *
     * <code>string app_id = 2;</code>
     * @return The bytes for appId.
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *小程序id
     * </pre>
     *
     * <code>string app_id = 2;</code>
     * @param value The appId to set.
     * @return This builder for chaining.
     */
    public Builder setAppId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      appId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *小程序id
     * </pre>
     *
     * <code>string app_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppId() {
      
      appId_ = getDefaultInstance().getAppId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *小程序id
     * </pre>
     *
     * <code>string app_id = 2;</code>
     * @param value The bytes for appId to set.
     * @return This builder for chaining.
     */
    public Builder setAppIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      appId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object appKey_ = "";
    /**
     * <pre>
     *应用密钥
     * </pre>
     *
     * <code>string app_key = 3;</code>
     * @return The appKey.
     */
    public java.lang.String getAppKey() {
      java.lang.Object ref = appKey_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appKey_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *应用密钥
     * </pre>
     *
     * <code>string app_key = 3;</code>
     * @return The bytes for appKey.
     */
    public com.google.protobuf.ByteString
        getAppKeyBytes() {
      java.lang.Object ref = appKey_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *应用密钥
     * </pre>
     *
     * <code>string app_key = 3;</code>
     * @param value The appKey to set.
     * @return This builder for chaining.
     */
    public Builder setAppKey(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      appKey_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *应用密钥
     * </pre>
     *
     * <code>string app_key = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppKey() {
      
      appKey_ = getDefaultInstance().getAppKey();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *应用密钥
     * </pre>
     *
     * <code>string app_key = 3;</code>
     * @param value The bytes for appKey to set.
     * @return This builder for chaining.
     */
    public Builder setAppKeyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      appKey_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object accessKey_ = "";
    /**
     * <pre>
     *授权密钥
     * </pre>
     *
     * <code>string access_key = 4;</code>
     * @return The accessKey.
     */
    public java.lang.String getAccessKey() {
      java.lang.Object ref = accessKey_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        accessKey_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *授权密钥
     * </pre>
     *
     * <code>string access_key = 4;</code>
     * @return The bytes for accessKey.
     */
    public com.google.protobuf.ByteString
        getAccessKeyBytes() {
      java.lang.Object ref = accessKey_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        accessKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *授权密钥
     * </pre>
     *
     * <code>string access_key = 4;</code>
     * @param value The accessKey to set.
     * @return This builder for chaining.
     */
    public Builder setAccessKey(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      accessKey_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *授权密钥
     * </pre>
     *
     * <code>string access_key = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearAccessKey() {
      
      accessKey_ = getDefaultInstance().getAccessKey();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *授权密钥
     * </pre>
     *
     * <code>string access_key = 4;</code>
     * @param value The bytes for accessKey to set.
     * @return This builder for chaining.
     */
    public Builder setAccessKeyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      accessKey_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object cert_ = "";
    /**
     * <pre>
     *证书信息
     * </pre>
     *
     * <code>string cert = 5;</code>
     * @return The cert.
     */
    public java.lang.String getCert() {
      java.lang.Object ref = cert_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        cert_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *证书信息
     * </pre>
     *
     * <code>string cert = 5;</code>
     * @return The bytes for cert.
     */
    public com.google.protobuf.ByteString
        getCertBytes() {
      java.lang.Object ref = cert_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cert_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *证书信息
     * </pre>
     *
     * <code>string cert = 5;</code>
     * @param value The cert to set.
     * @return This builder for chaining.
     */
    public Builder setCert(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      cert_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *证书信息
     * </pre>
     *
     * <code>string cert = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearCert() {
      
      cert_ = getDefaultInstance().getCert();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *证书信息
     * </pre>
     *
     * <code>string cert = 5;</code>
     * @param value The bytes for cert to set.
     * @return This builder for chaining.
     */
    public Builder setCertBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      cert_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object privateKey_ = "";
    /**
     * <pre>
     *私钥
     * </pre>
     *
     * <code>string private_key = 6;</code>
     * @return The privateKey.
     */
    public java.lang.String getPrivateKey() {
      java.lang.Object ref = privateKey_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        privateKey_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *私钥
     * </pre>
     *
     * <code>string private_key = 6;</code>
     * @return The bytes for privateKey.
     */
    public com.google.protobuf.ByteString
        getPrivateKeyBytes() {
      java.lang.Object ref = privateKey_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        privateKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *私钥
     * </pre>
     *
     * <code>string private_key = 6;</code>
     * @param value The privateKey to set.
     * @return This builder for chaining.
     */
    public Builder setPrivateKey(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      privateKey_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *私钥
     * </pre>
     *
     * <code>string private_key = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrivateKey() {
      
      privateKey_ = getDefaultInstance().getPrivateKey();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *私钥
     * </pre>
     *
     * <code>string private_key = 6;</code>
     * @param value The bytes for privateKey to set.
     * @return This builder for chaining.
     */
    public Builder setPrivateKeyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      privateKey_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object publicKey_ = "";
    /**
     * <pre>
     *公钥
     * </pre>
     *
     * <code>string public_key = 7;</code>
     * @return The publicKey.
     */
    public java.lang.String getPublicKey() {
      java.lang.Object ref = publicKey_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        publicKey_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *公钥
     * </pre>
     *
     * <code>string public_key = 7;</code>
     * @return The bytes for publicKey.
     */
    public com.google.protobuf.ByteString
        getPublicKeyBytes() {
      java.lang.Object ref = publicKey_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        publicKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *公钥
     * </pre>
     *
     * <code>string public_key = 7;</code>
     * @param value The publicKey to set.
     * @return This builder for chaining.
     */
    public Builder setPublicKey(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      publicKey_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *公钥
     * </pre>
     *
     * <code>string public_key = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearPublicKey() {
      
      publicKey_ = getDefaultInstance().getPublicKey();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *公钥
     * </pre>
     *
     * <code>string public_key = 7;</code>
     * @param value The bytes for publicKey to set.
     * @return This builder for chaining.
     */
    public Builder setPublicKeyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      publicKey_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object terminalId_ = "";
    /**
     * <pre>
     *终端ID
     * </pre>
     *
     * <code>string terminal_id = 8;</code>
     * @return The terminalId.
     */
    public java.lang.String getTerminalId() {
      java.lang.Object ref = terminalId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        terminalId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *终端ID
     * </pre>
     *
     * <code>string terminal_id = 8;</code>
     * @return The bytes for terminalId.
     */
    public com.google.protobuf.ByteString
        getTerminalIdBytes() {
      java.lang.Object ref = terminalId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        terminalId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *终端ID
     * </pre>
     *
     * <code>string terminal_id = 8;</code>
     * @param value The terminalId to set.
     * @return This builder for chaining.
     */
    public Builder setTerminalId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      terminalId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *终端ID
     * </pre>
     *
     * <code>string terminal_id = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearTerminalId() {
      
      terminalId_ = getDefaultInstance().getTerminalId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *终端ID
     * </pre>
     *
     * <code>string terminal_id = 8;</code>
     * @param value The bytes for terminalId to set.
     * @return This builder for chaining.
     */
    public Builder setTerminalIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      terminalId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object gatewayUrl_ = "";
    /**
     * <pre>
     *网关地址
     * </pre>
     *
     * <code>string gateway_url = 9;</code>
     * @return The gatewayUrl.
     */
    public java.lang.String getGatewayUrl() {
      java.lang.Object ref = gatewayUrl_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        gatewayUrl_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *网关地址
     * </pre>
     *
     * <code>string gateway_url = 9;</code>
     * @return The bytes for gatewayUrl.
     */
    public com.google.protobuf.ByteString
        getGatewayUrlBytes() {
      java.lang.Object ref = gatewayUrl_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gatewayUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *网关地址
     * </pre>
     *
     * <code>string gateway_url = 9;</code>
     * @param value The gatewayUrl to set.
     * @return This builder for chaining.
     */
    public Builder setGatewayUrl(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      gatewayUrl_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *网关地址
     * </pre>
     *
     * <code>string gateway_url = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearGatewayUrl() {
      
      gatewayUrl_ = getDefaultInstance().getGatewayUrl();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *网关地址
     * </pre>
     *
     * <code>string gateway_url = 9;</code>
     * @param value The bytes for gatewayUrl to set.
     * @return This builder for chaining.
     */
    public Builder setGatewayUrlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      gatewayUrl_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object authToken_ = "";
    /**
     * <pre>
     *认证token
     * </pre>
     *
     * <code>string auth_token = 10;</code>
     * @return The authToken.
     */
    public java.lang.String getAuthToken() {
      java.lang.Object ref = authToken_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        authToken_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *认证token
     * </pre>
     *
     * <code>string auth_token = 10;</code>
     * @return The bytes for authToken.
     */
    public com.google.protobuf.ByteString
        getAuthTokenBytes() {
      java.lang.Object ref = authToken_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        authToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *认证token
     * </pre>
     *
     * <code>string auth_token = 10;</code>
     * @param value The authToken to set.
     * @return This builder for chaining.
     */
    public Builder setAuthToken(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      authToken_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *认证token
     * </pre>
     *
     * <code>string auth_token = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearAuthToken() {
      
      authToken_ = getDefaultInstance().getAuthToken();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *认证token
     * </pre>
     *
     * <code>string auth_token = 10;</code>
     * @param value The bytes for authToken to set.
     * @return This builder for chaining.
     */
    public Builder setAuthTokenBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      authToken_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.AccessConfig)
  }

  // @@protoc_insertion_point(class_scope:channel.AccessConfig)
  private static final cn.hexcloud.pbis.common.service.facade.channel.AccessConfig DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.AccessConfig();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.AccessConfig getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AccessConfig>
      PARSER = new com.google.protobuf.AbstractParser<AccessConfig>() {
    @java.lang.Override
    public AccessConfig parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new AccessConfig(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<AccessConfig> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AccessConfig> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AccessConfig getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

