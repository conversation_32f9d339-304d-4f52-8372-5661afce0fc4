// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface AccessConfigOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.AccessConfig)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *商户id
   * </pre>
   *
   * <code>string merchant_id = 1;</code>
   * @return The merchantId.
   */
  java.lang.String getMerchantId();
  /**
   * <pre>
   *商户id
   * </pre>
   *
   * <code>string merchant_id = 1;</code>
   * @return The bytes for merchantId.
   */
  com.google.protobuf.ByteString
      getMerchantIdBytes();

  /**
   * <pre>
   *小程序id
   * </pre>
   *
   * <code>string app_id = 2;</code>
   * @return The appId.
   */
  java.lang.String getAppId();
  /**
   * <pre>
   *小程序id
   * </pre>
   *
   * <code>string app_id = 2;</code>
   * @return The bytes for appId.
   */
  com.google.protobuf.ByteString
      getAppIdBytes();

  /**
   * <pre>
   *应用密钥
   * </pre>
   *
   * <code>string app_key = 3;</code>
   * @return The appKey.
   */
  java.lang.String getAppKey();
  /**
   * <pre>
   *应用密钥
   * </pre>
   *
   * <code>string app_key = 3;</code>
   * @return The bytes for appKey.
   */
  com.google.protobuf.ByteString
      getAppKeyBytes();

  /**
   * <pre>
   *授权密钥
   * </pre>
   *
   * <code>string access_key = 4;</code>
   * @return The accessKey.
   */
  java.lang.String getAccessKey();
  /**
   * <pre>
   *授权密钥
   * </pre>
   *
   * <code>string access_key = 4;</code>
   * @return The bytes for accessKey.
   */
  com.google.protobuf.ByteString
      getAccessKeyBytes();

  /**
   * <pre>
   *证书信息
   * </pre>
   *
   * <code>string cert = 5;</code>
   * @return The cert.
   */
  java.lang.String getCert();
  /**
   * <pre>
   *证书信息
   * </pre>
   *
   * <code>string cert = 5;</code>
   * @return The bytes for cert.
   */
  com.google.protobuf.ByteString
      getCertBytes();

  /**
   * <pre>
   *私钥
   * </pre>
   *
   * <code>string private_key = 6;</code>
   * @return The privateKey.
   */
  java.lang.String getPrivateKey();
  /**
   * <pre>
   *私钥
   * </pre>
   *
   * <code>string private_key = 6;</code>
   * @return The bytes for privateKey.
   */
  com.google.protobuf.ByteString
      getPrivateKeyBytes();

  /**
   * <pre>
   *公钥
   * </pre>
   *
   * <code>string public_key = 7;</code>
   * @return The publicKey.
   */
  java.lang.String getPublicKey();
  /**
   * <pre>
   *公钥
   * </pre>
   *
   * <code>string public_key = 7;</code>
   * @return The bytes for publicKey.
   */
  com.google.protobuf.ByteString
      getPublicKeyBytes();

  /**
   * <pre>
   *终端ID
   * </pre>
   *
   * <code>string terminal_id = 8;</code>
   * @return The terminalId.
   */
  java.lang.String getTerminalId();
  /**
   * <pre>
   *终端ID
   * </pre>
   *
   * <code>string terminal_id = 8;</code>
   * @return The bytes for terminalId.
   */
  com.google.protobuf.ByteString
      getTerminalIdBytes();

  /**
   * <pre>
   *网关地址
   * </pre>
   *
   * <code>string gateway_url = 9;</code>
   * @return The gatewayUrl.
   */
  java.lang.String getGatewayUrl();
  /**
   * <pre>
   *网关地址
   * </pre>
   *
   * <code>string gateway_url = 9;</code>
   * @return The bytes for gatewayUrl.
   */
  com.google.protobuf.ByteString
      getGatewayUrlBytes();

  /**
   * <pre>
   *认证token
   * </pre>
   *
   * <code>string auth_token = 10;</code>
   * @return The authToken.
   */
  java.lang.String getAuthToken();
  /**
   * <pre>
   *认证token
   * </pre>
   *
   * <code>string auth_token = 10;</code>
   * @return The bytes for authToken.
   */
  com.google.protobuf.ByteString
      getAuthTokenBytes();
}
