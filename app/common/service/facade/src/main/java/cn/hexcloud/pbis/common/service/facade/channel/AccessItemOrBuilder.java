// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface AccessItemOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.AccessItem)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string store_id = 1;</code>
   * @return The storeId.
   */
  java.lang.String getStoreId();
  /**
   * <code>string store_id = 1;</code>
   * @return The bytes for storeId.
   */
  com.google.protobuf.ByteString
      getStoreIdBytes();

  /**
   * <code>string company_id = 2;</code>
   * @return The companyId.
   */
  java.lang.String getCompanyId();
  /**
   * <code>string company_id = 2;</code>
   * @return The bytes for companyId.
   */
  com.google.protobuf.ByteString
      getCompanyIdBytes();

  /**
   * <code>string partner_id = 3;</code>
   * @return The partnerId.
   */
  java.lang.String getPartnerId();
  /**
   * <code>string partner_id = 3;</code>
   * @return The bytes for partnerId.
   */
  com.google.protobuf.ByteString
      getPartnerIdBytes();

  /**
   * <code>string name = 4;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <code>string name = 4;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <code>string channel_name_item = 5;</code>
   * @return The channelNameItem.
   */
  java.lang.String getChannelNameItem();
  /**
   * <code>string channel_name_item = 5;</code>
   * @return The bytes for channelNameItem.
   */
  com.google.protobuf.ByteString
      getChannelNameItemBytes();

  /**
   * <code>string update_time = 6;</code>
   * @return The updateTime.
   */
  java.lang.String getUpdateTime();
  /**
   * <code>string update_time = 6;</code>
   * @return The bytes for updateTime.
   */
  com.google.protobuf.ByteString
      getUpdateTimeBytes();
}
