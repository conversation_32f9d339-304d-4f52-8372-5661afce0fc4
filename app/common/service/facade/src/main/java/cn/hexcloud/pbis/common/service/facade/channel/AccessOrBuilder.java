// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface AccessOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.Access)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 渠道代码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  java.lang.String getChannelCode();
  /**
   * <pre>
   * 渠道代码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  com.google.protobuf.ByteString
      getChannelCodeBytes();

  /**
   * <pre>
   * 配置层级，0 租户；1 公司；2 门店
   * </pre>
   *
   * <code>int32 apply_to = 2;</code>
   * @return The applyTo.
   */
  int getApplyTo();

  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget> 
      getApplyTargetsList();
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget getApplyTargets(int index);
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
   */
  int getApplyTargetsCount();
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder> 
      getApplyTargetsOrBuilderList();
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_targets = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder getApplyTargetsOrBuilder(
      int index);

  /**
   * <pre>
   * 渠道业务代码
   * </pre>
   *
   * <code>string business_code = 4;</code>
   * @return The businessCode.
   */
  java.lang.String getBusinessCode();
  /**
   * <pre>
   * 渠道业务代码
   * </pre>
   *
   * <code>string business_code = 4;</code>
   * @return The bytes for businessCode.
   */
  com.google.protobuf.ByteString
      getBusinessCodeBytes();

  /**
   * <pre>
   * 第三方商户PID
   * </pre>
   *
   * <code>string merchant_id = 5;</code>
   * @return The merchantId.
   */
  java.lang.String getMerchantId();
  /**
   * <pre>
   * 第三方商户PID
   * </pre>
   *
   * <code>string merchant_id = 5;</code>
   * @return The bytes for merchantId.
   */
  com.google.protobuf.ByteString
      getMerchantIdBytes();

  /**
   * <pre>
   * 第三方app id
   * </pre>
   *
   * <code>string app_id = 6;</code>
   * @return The appId.
   */
  java.lang.String getAppId();
  /**
   * <pre>
   * 第三方app id
   * </pre>
   *
   * <code>string app_id = 6;</code>
   * @return The bytes for appId.
   */
  com.google.protobuf.ByteString
      getAppIdBytes();

  /**
   * <pre>
   * 第三方app key
   * </pre>
   *
   * <code>string app_key = 7;</code>
   * @return The appKey.
   */
  java.lang.String getAppKey();
  /**
   * <pre>
   * 第三方app key
   * </pre>
   *
   * <code>string app_key = 7;</code>
   * @return The bytes for appKey.
   */
  com.google.protobuf.ByteString
      getAppKeyBytes();

  /**
   * <pre>
   * 第三方access key
   * </pre>
   *
   * <code>string access_key = 8;</code>
   * @return The accessKey.
   */
  java.lang.String getAccessKey();
  /**
   * <pre>
   * 第三方access key
   * </pre>
   *
   * <code>string access_key = 8;</code>
   * @return The bytes for accessKey.
   */
  com.google.protobuf.ByteString
      getAccessKeyBytes();

  /**
   * <pre>
   * 第三方cert
   * </pre>
   *
   * <code>string cert = 9;</code>
   * @return The cert.
   */
  java.lang.String getCert();
  /**
   * <pre>
   * 第三方cert
   * </pre>
   *
   * <code>string cert = 9;</code>
   * @return The bytes for cert.
   */
  com.google.protobuf.ByteString
      getCertBytes();

  /**
   * <pre>
   * 第三方private key
   * </pre>
   *
   * <code>string private_key = 10;</code>
   * @return The privateKey.
   */
  java.lang.String getPrivateKey();
  /**
   * <pre>
   * 第三方private key
   * </pre>
   *
   * <code>string private_key = 10;</code>
   * @return The bytes for privateKey.
   */
  com.google.protobuf.ByteString
      getPrivateKeyBytes();

  /**
   * <pre>
   * 第三方public key
   * </pre>
   *
   * <code>string public_key = 11;</code>
   * @return The publicKey.
   */
  java.lang.String getPublicKey();
  /**
   * <pre>
   * 第三方public key
   * </pre>
   *
   * <code>string public_key = 11;</code>
   * @return The bytes for publicKey.
   */
  com.google.protobuf.ByteString
      getPublicKeyBytes();

  /**
   * <pre>
   * 第三方网关URL
   * </pre>
   *
   * <code>string gateway_url = 12;</code>
   * @return The gatewayUrl.
   */
  java.lang.String getGatewayUrl();
  /**
   * <pre>
   * 第三方网关URL
   * </pre>
   *
   * <code>string gateway_url = 12;</code>
   * @return The bytes for gatewayUrl.
   */
  com.google.protobuf.ByteString
      getGatewayUrlBytes();

  /**
   * <pre>
   * 支付名称集合
   * </pre>
   *
   * <code>string channelNameItems = 13;</code>
   * @return The channelNameItems.
   */
  java.lang.String getChannelNameItems();
  /**
   * <pre>
   * 支付名称集合
   * </pre>
   *
   * <code>string channelNameItems = 13;</code>
   * @return The bytes for channelNameItems.
   */
  com.google.protobuf.ByteString
      getChannelNameItemsBytes();

  /**
   * <pre>
   * 渠道访问授权配置id
   * </pre>
   *
   * <code>int64 access_id = 14;</code>
   * @return The accessId.
   */
  long getAccessId();

  /**
   * <pre>
   * 租户id
   * </pre>
   *
   * <code>string partner_id = 15;</code>
   * @return The partnerId.
   */
  java.lang.String getPartnerId();
  /**
   * <pre>
   * 租户id
   * </pre>
   *
   * <code>string partner_id = 15;</code>
   * @return The bytes for partnerId.
   */
  com.google.protobuf.ByteString
      getPartnerIdBytes();

  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 16;</code>
   * @return The companyId.
   */
  java.lang.String getCompanyId();
  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 16;</code>
   * @return The bytes for companyId.
   */
  com.google.protobuf.ByteString
      getCompanyIdBytes();

  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 17;</code>
   * @return The storeId.
   */
  java.lang.String getStoreId();
  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 17;</code>
   * @return The bytes for storeId.
   */
  com.google.protobuf.ByteString
      getStoreIdBytes();

  /**
   * <pre>
   * 终端id
   * </pre>
   *
   * <code>string terminal_id = 18;</code>
   * @return The terminalId.
   */
  java.lang.String getTerminalId();
  /**
   * <pre>
   * 终端id
   * </pre>
   *
   * <code>string terminal_id = 18;</code>
   * @return The bytes for terminalId.
   */
  com.google.protobuf.ByteString
      getTerminalIdBytes();

  /**
   * <pre>
   * 第三方api版本
   * </pre>
   *
   * <code>string api_version = 19;</code>
   * @return The apiVersion.
   */
  java.lang.String getApiVersion();
  /**
   * <pre>
   * 第三方api版本
   * </pre>
   *
   * <code>string api_version = 19;</code>
   * @return The bytes for apiVersion.
   */
  com.google.protobuf.ByteString
      getApiVersionBytes();

  /**
   * <pre>
   * 子商户号
   * </pre>
   *
   * <code>string sub_merchant_id = 20;</code>
   * @return The subMerchantId.
   */
  java.lang.String getSubMerchantId();
  /**
   * <pre>
   * 子商户号
   * </pre>
   *
   * <code>string sub_merchant_id = 20;</code>
   * @return The bytes for subMerchantId.
   */
  com.google.protobuf.ByteString
      getSubMerchantIdBytes();
}
