// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 查询/门店/公司/全局访问授权配置
 * </pre>
 *
 * Protobuf type {@code channel.AccessReportItem}
 */
public final class AccessReportItem extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.AccessReportItem)
    AccessReportItemOrBuilder {
private static final long serialVersionUID = 0L;
  // Use AccessReportItem.newBuilder() to construct.
  private AccessReportItem(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private AccessReportItem() {
    storeId_ = "";
    companyId_ = "";
    channelNameItem_ = "";
    updateTime_ = "";
    partnerId_ = "";
    name_ = "";
    code_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new AccessReportItem();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private AccessReportItem(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            storeId_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            companyId_ = s;
            break;
          }
          case 40: {

            level_ = input.readInt32();
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            channelNameItem_ = s;
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            updateTime_ = s;
            break;
          }
          case 66: {
            java.lang.String s = input.readStringRequireUtf8();

            partnerId_ = s;
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 82: {
            java.lang.String s = input.readStringRequireUtf8();

            code_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AccessReportItem_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AccessReportItem_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem.class, cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem.Builder.class);
  }

  public static final int STORE_ID_FIELD_NUMBER = 1;
  private volatile java.lang.Object storeId_;
  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 1;</code>
   * @return The storeId.
   */
  @java.lang.Override
  public java.lang.String getStoreId() {
    java.lang.Object ref = storeId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      storeId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 1;</code>
   * @return The bytes for storeId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStoreIdBytes() {
    java.lang.Object ref = storeId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      storeId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int COMPANY_ID_FIELD_NUMBER = 2;
  private volatile java.lang.Object companyId_;
  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 2;</code>
   * @return The companyId.
   */
  @java.lang.Override
  public java.lang.String getCompanyId() {
    java.lang.Object ref = companyId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      companyId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 2;</code>
   * @return The bytes for companyId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCompanyIdBytes() {
    java.lang.Object ref = companyId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      companyId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LEVEL_FIELD_NUMBER = 5;
  private int level_;
  /**
   * <pre>
   * 配置等级
   * </pre>
   *
   * <code>int32 level = 5;</code>
   * @return The level.
   */
  @java.lang.Override
  public int getLevel() {
    return level_;
  }

  public static final int CHANNEL_NAME_ITEM_FIELD_NUMBER = 6;
  private volatile java.lang.Object channelNameItem_;
  /**
   * <pre>
   * 渠道名称
   * </pre>
   *
   * <code>string channel_name_item = 6;</code>
   * @return The channelNameItem.
   */
  @java.lang.Override
  public java.lang.String getChannelNameItem() {
    java.lang.Object ref = channelNameItem_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelNameItem_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道名称
   * </pre>
   *
   * <code>string channel_name_item = 6;</code>
   * @return The bytes for channelNameItem.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelNameItemBytes() {
    java.lang.Object ref = channelNameItem_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelNameItem_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int UPDATE_TIME_FIELD_NUMBER = 7;
  private volatile java.lang.Object updateTime_;
  /**
   * <pre>
   * 更新时间
   * </pre>
   *
   * <code>string update_time = 7;</code>
   * @return The updateTime.
   */
  @java.lang.Override
  public java.lang.String getUpdateTime() {
    java.lang.Object ref = updateTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      updateTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 更新时间
   * </pre>
   *
   * <code>string update_time = 7;</code>
   * @return The bytes for updateTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUpdateTimeBytes() {
    java.lang.Object ref = updateTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      updateTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PARTNER_ID_FIELD_NUMBER = 8;
  private volatile java.lang.Object partnerId_;
  /**
   * <pre>
   * 租户id
   * </pre>
   *
   * <code>string partner_id = 8;</code>
   * @return The partnerId.
   */
  @java.lang.Override
  public java.lang.String getPartnerId() {
    java.lang.Object ref = partnerId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      partnerId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 租户id
   * </pre>
   *
   * <code>string partner_id = 8;</code>
   * @return The bytes for partnerId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPartnerIdBytes() {
    java.lang.Object ref = partnerId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      partnerId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 9;
  private volatile java.lang.Object name_;
  /**
   * <pre>
   * 门店/公司名称
   * </pre>
   *
   * <code>string name = 9;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 门店/公司名称
   * </pre>
   *
   * <code>string name = 9;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CODE_FIELD_NUMBER = 10;
  private volatile java.lang.Object code_;
  /**
   * <pre>
   * 门店/公司名称
   * </pre>
   *
   * <code>string code = 10;</code>
   * @return The code.
   */
  @java.lang.Override
  public java.lang.String getCode() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      code_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 门店/公司名称
   * </pre>
   *
   * <code>string code = 10;</code>
   * @return The bytes for code.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCodeBytes() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      code_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getStoreIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, storeId_);
    }
    if (!getCompanyIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, companyId_);
    }
    if (level_ != 0) {
      output.writeInt32(5, level_);
    }
    if (!getChannelNameItemBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, channelNameItem_);
    }
    if (!getUpdateTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, updateTime_);
    }
    if (!getPartnerIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, partnerId_);
    }
    if (!getNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, name_);
    }
    if (!getCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, code_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getStoreIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, storeId_);
    }
    if (!getCompanyIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, companyId_);
    }
    if (level_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, level_);
    }
    if (!getChannelNameItemBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, channelNameItem_);
    }
    if (!getUpdateTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, updateTime_);
    }
    if (!getPartnerIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, partnerId_);
    }
    if (!getNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, name_);
    }
    if (!getCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, code_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem other = (cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem) obj;

    if (!getStoreId()
        .equals(other.getStoreId())) return false;
    if (!getCompanyId()
        .equals(other.getCompanyId())) return false;
    if (getLevel()
        != other.getLevel()) return false;
    if (!getChannelNameItem()
        .equals(other.getChannelNameItem())) return false;
    if (!getUpdateTime()
        .equals(other.getUpdateTime())) return false;
    if (!getPartnerId()
        .equals(other.getPartnerId())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (!getCode()
        .equals(other.getCode())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + STORE_ID_FIELD_NUMBER;
    hash = (53 * hash) + getStoreId().hashCode();
    hash = (37 * hash) + COMPANY_ID_FIELD_NUMBER;
    hash = (53 * hash) + getCompanyId().hashCode();
    hash = (37 * hash) + LEVEL_FIELD_NUMBER;
    hash = (53 * hash) + getLevel();
    hash = (37 * hash) + CHANNEL_NAME_ITEM_FIELD_NUMBER;
    hash = (53 * hash) + getChannelNameItem().hashCode();
    hash = (37 * hash) + UPDATE_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getUpdateTime().hashCode();
    hash = (37 * hash) + PARTNER_ID_FIELD_NUMBER;
    hash = (53 * hash) + getPartnerId().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCode().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 查询/门店/公司/全局访问授权配置
   * </pre>
   *
   * Protobuf type {@code channel.AccessReportItem}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.AccessReportItem)
      cn.hexcloud.pbis.common.service.facade.channel.AccessReportItemOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AccessReportItem_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AccessReportItem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem.class, cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      storeId_ = "";

      companyId_ = "";

      level_ = 0;

      channelNameItem_ = "";

      updateTime_ = "";

      partnerId_ = "";

      name_ = "";

      code_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AccessReportItem_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem build() {
      cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem result = new cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem(this);
      result.storeId_ = storeId_;
      result.companyId_ = companyId_;
      result.level_ = level_;
      result.channelNameItem_ = channelNameItem_;
      result.updateTime_ = updateTime_;
      result.partnerId_ = partnerId_;
      result.name_ = name_;
      result.code_ = code_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem.getDefaultInstance()) return this;
      if (!other.getStoreId().isEmpty()) {
        storeId_ = other.storeId_;
        onChanged();
      }
      if (!other.getCompanyId().isEmpty()) {
        companyId_ = other.companyId_;
        onChanged();
      }
      if (other.getLevel() != 0) {
        setLevel(other.getLevel());
      }
      if (!other.getChannelNameItem().isEmpty()) {
        channelNameItem_ = other.channelNameItem_;
        onChanged();
      }
      if (!other.getUpdateTime().isEmpty()) {
        updateTime_ = other.updateTime_;
        onChanged();
      }
      if (!other.getPartnerId().isEmpty()) {
        partnerId_ = other.partnerId_;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (!other.getCode().isEmpty()) {
        code_ = other.code_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object storeId_ = "";
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 1;</code>
     * @return The storeId.
     */
    public java.lang.String getStoreId() {
      java.lang.Object ref = storeId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        storeId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 1;</code>
     * @return The bytes for storeId.
     */
    public com.google.protobuf.ByteString
        getStoreIdBytes() {
      java.lang.Object ref = storeId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        storeId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 1;</code>
     * @param value The storeId to set.
     * @return This builder for chaining.
     */
    public Builder setStoreId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      storeId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearStoreId() {
      
      storeId_ = getDefaultInstance().getStoreId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 1;</code>
     * @param value The bytes for storeId to set.
     * @return This builder for chaining.
     */
    public Builder setStoreIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      storeId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object companyId_ = "";
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 2;</code>
     * @return The companyId.
     */
    public java.lang.String getCompanyId() {
      java.lang.Object ref = companyId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        companyId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 2;</code>
     * @return The bytes for companyId.
     */
    public com.google.protobuf.ByteString
        getCompanyIdBytes() {
      java.lang.Object ref = companyId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        companyId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 2;</code>
     * @param value The companyId to set.
     * @return This builder for chaining.
     */
    public Builder setCompanyId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      companyId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearCompanyId() {
      
      companyId_ = getDefaultInstance().getCompanyId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 2;</code>
     * @param value The bytes for companyId to set.
     * @return This builder for chaining.
     */
    public Builder setCompanyIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      companyId_ = value;
      onChanged();
      return this;
    }

    private int level_ ;
    /**
     * <pre>
     * 配置等级
     * </pre>
     *
     * <code>int32 level = 5;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }
    /**
     * <pre>
     * 配置等级
     * </pre>
     *
     * <code>int32 level = 5;</code>
     * @param value The level to set.
     * @return This builder for chaining.
     */
    public Builder setLevel(int value) {
      
      level_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 配置等级
     * </pre>
     *
     * <code>int32 level = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearLevel() {
      
      level_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object channelNameItem_ = "";
    /**
     * <pre>
     * 渠道名称
     * </pre>
     *
     * <code>string channel_name_item = 6;</code>
     * @return The channelNameItem.
     */
    public java.lang.String getChannelNameItem() {
      java.lang.Object ref = channelNameItem_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelNameItem_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道名称
     * </pre>
     *
     * <code>string channel_name_item = 6;</code>
     * @return The bytes for channelNameItem.
     */
    public com.google.protobuf.ByteString
        getChannelNameItemBytes() {
      java.lang.Object ref = channelNameItem_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelNameItem_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道名称
     * </pre>
     *
     * <code>string channel_name_item = 6;</code>
     * @param value The channelNameItem to set.
     * @return This builder for chaining.
     */
    public Builder setChannelNameItem(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelNameItem_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道名称
     * </pre>
     *
     * <code>string channel_name_item = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelNameItem() {
      
      channelNameItem_ = getDefaultInstance().getChannelNameItem();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道名称
     * </pre>
     *
     * <code>string channel_name_item = 6;</code>
     * @param value The bytes for channelNameItem to set.
     * @return This builder for chaining.
     */
    public Builder setChannelNameItemBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelNameItem_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object updateTime_ = "";
    /**
     * <pre>
     * 更新时间
     * </pre>
     *
     * <code>string update_time = 7;</code>
     * @return The updateTime.
     */
    public java.lang.String getUpdateTime() {
      java.lang.Object ref = updateTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        updateTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 更新时间
     * </pre>
     *
     * <code>string update_time = 7;</code>
     * @return The bytes for updateTime.
     */
    public com.google.protobuf.ByteString
        getUpdateTimeBytes() {
      java.lang.Object ref = updateTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        updateTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 更新时间
     * </pre>
     *
     * <code>string update_time = 7;</code>
     * @param value The updateTime to set.
     * @return This builder for chaining.
     */
    public Builder setUpdateTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      updateTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 更新时间
     * </pre>
     *
     * <code>string update_time = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearUpdateTime() {
      
      updateTime_ = getDefaultInstance().getUpdateTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 更新时间
     * </pre>
     *
     * <code>string update_time = 7;</code>
     * @param value The bytes for updateTime to set.
     * @return This builder for chaining.
     */
    public Builder setUpdateTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      updateTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object partnerId_ = "";
    /**
     * <pre>
     * 租户id
     * </pre>
     *
     * <code>string partner_id = 8;</code>
     * @return The partnerId.
     */
    public java.lang.String getPartnerId() {
      java.lang.Object ref = partnerId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        partnerId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 租户id
     * </pre>
     *
     * <code>string partner_id = 8;</code>
     * @return The bytes for partnerId.
     */
    public com.google.protobuf.ByteString
        getPartnerIdBytes() {
      java.lang.Object ref = partnerId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        partnerId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 租户id
     * </pre>
     *
     * <code>string partner_id = 8;</code>
     * @param value The partnerId to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      partnerId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 租户id
     * </pre>
     *
     * <code>string partner_id = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearPartnerId() {
      
      partnerId_ = getDefaultInstance().getPartnerId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 租户id
     * </pre>
     *
     * <code>string partner_id = 8;</code>
     * @param value The bytes for partnerId to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      partnerId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <pre>
     * 门店/公司名称
     * </pre>
     *
     * <code>string name = 9;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 门店/公司名称
     * </pre>
     *
     * <code>string name = 9;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 门店/公司名称
     * </pre>
     *
     * <code>string name = 9;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店/公司名称
     * </pre>
     *
     * <code>string name = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店/公司名称
     * </pre>
     *
     * <code>string name = 9;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object code_ = "";
    /**
     * <pre>
     * 门店/公司名称
     * </pre>
     *
     * <code>string code = 10;</code>
     * @return The code.
     */
    public java.lang.String getCode() {
      java.lang.Object ref = code_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        code_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 门店/公司名称
     * </pre>
     *
     * <code>string code = 10;</code>
     * @return The bytes for code.
     */
    public com.google.protobuf.ByteString
        getCodeBytes() {
      java.lang.Object ref = code_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        code_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 门店/公司名称
     * </pre>
     *
     * <code>string code = 10;</code>
     * @param value The code to set.
     * @return This builder for chaining.
     */
    public Builder setCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      code_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店/公司名称
     * </pre>
     *
     * <code>string code = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearCode() {
      
      code_ = getDefaultInstance().getCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店/公司名称
     * </pre>
     *
     * <code>string code = 10;</code>
     * @param value The bytes for code to set.
     * @return This builder for chaining.
     */
    public Builder setCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      code_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.AccessReportItem)
  }

  // @@protoc_insertion_point(class_scope:channel.AccessReportItem)
  private static final cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AccessReportItem>
      PARSER = new com.google.protobuf.AbstractParser<AccessReportItem>() {
    @java.lang.Override
    public AccessReportItem parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new AccessReportItem(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<AccessReportItem> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AccessReportItem> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AccessReportItem getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

