// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface AccessReportItemOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.AccessReportItem)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 1;</code>
   * @return The storeId.
   */
  java.lang.String getStoreId();
  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 1;</code>
   * @return The bytes for storeId.
   */
  com.google.protobuf.ByteString
      getStoreIdBytes();

  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 2;</code>
   * @return The companyId.
   */
  java.lang.String getCompanyId();
  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 2;</code>
   * @return The bytes for companyId.
   */
  com.google.protobuf.ByteString
      getCompanyIdBytes();

  /**
   * <pre>
   * 配置等级
   * </pre>
   *
   * <code>int32 level = 5;</code>
   * @return The level.
   */
  int getLevel();

  /**
   * <pre>
   * 渠道名称
   * </pre>
   *
   * <code>string channel_name_item = 6;</code>
   * @return The channelNameItem.
   */
  java.lang.String getChannelNameItem();
  /**
   * <pre>
   * 渠道名称
   * </pre>
   *
   * <code>string channel_name_item = 6;</code>
   * @return The bytes for channelNameItem.
   */
  com.google.protobuf.ByteString
      getChannelNameItemBytes();

  /**
   * <pre>
   * 更新时间
   * </pre>
   *
   * <code>string update_time = 7;</code>
   * @return The updateTime.
   */
  java.lang.String getUpdateTime();
  /**
   * <pre>
   * 更新时间
   * </pre>
   *
   * <code>string update_time = 7;</code>
   * @return The bytes for updateTime.
   */
  com.google.protobuf.ByteString
      getUpdateTimeBytes();

  /**
   * <pre>
   * 租户id
   * </pre>
   *
   * <code>string partner_id = 8;</code>
   * @return The partnerId.
   */
  java.lang.String getPartnerId();
  /**
   * <pre>
   * 租户id
   * </pre>
   *
   * <code>string partner_id = 8;</code>
   * @return The bytes for partnerId.
   */
  com.google.protobuf.ByteString
      getPartnerIdBytes();

  /**
   * <pre>
   * 门店/公司名称
   * </pre>
   *
   * <code>string name = 9;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   * 门店/公司名称
   * </pre>
   *
   * <code>string name = 9;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * 门店/公司名称
   * </pre>
   *
   * <code>string code = 10;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <pre>
   * 门店/公司名称
   * </pre>
   *
   * <code>string code = 10;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();
}
