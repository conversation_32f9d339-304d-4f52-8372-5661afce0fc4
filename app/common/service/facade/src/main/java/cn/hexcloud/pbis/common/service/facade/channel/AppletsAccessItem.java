// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 小程序保存授权配置集合
 * </pre>
 *
 * Protobuf type {@code channel.AppletsAccessItem}
 */
public final class AppletsAccessItem extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.AppletsAccessItem)
    AppletsAccessItemOrBuilder {
private static final long serialVersionUID = 0L;
  // Use AppletsAccessItem.newBuilder() to construct.
  private AppletsAccessItem(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private AppletsAccessItem() {
    channelCode_ = "";
    propsItem_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new AppletsAccessItem();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private AppletsAccessItem(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            channelCode_ = s;
            break;
          }
          case 18: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              propsItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.Access>();
              mutable_bitField0_ |= 0x00000001;
            }
            propsItem_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.Access.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        propsItem_ = java.util.Collections.unmodifiableList(propsItem_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AppletsAccessItem_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AppletsAccessItem_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.class, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder.class);
  }

  public static final int CHANNEL_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object channelCode_;
  /**
   * <pre>
   * 渠道名称
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  @java.lang.Override
  public java.lang.String getChannelCode() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道名称
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelCodeBytes() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PROPS_ITEM_FIELD_NUMBER = 2;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access> propsItem_;
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.Access props_item = 2;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access> getPropsItemList() {
    return propsItem_;
  }
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.Access props_item = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> 
      getPropsItemOrBuilderList() {
    return propsItem_;
  }
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.Access props_item = 2;</code>
   */
  @java.lang.Override
  public int getPropsItemCount() {
    return propsItem_.size();
  }
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.Access props_item = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.Access getPropsItem(int index) {
    return propsItem_.get(index);
  }
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.Access props_item = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder getPropsItemOrBuilder(
      int index) {
    return propsItem_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getChannelCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelCode_);
    }
    for (int i = 0; i < propsItem_.size(); i++) {
      output.writeMessage(2, propsItem_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getChannelCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelCode_);
    }
    for (int i = 0; i < propsItem_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, propsItem_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem other = (cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem) obj;

    if (!getChannelCode()
        .equals(other.getChannelCode())) return false;
    if (!getPropsItemList()
        .equals(other.getPropsItemList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHANNEL_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getChannelCode().hashCode();
    if (getPropsItemCount() > 0) {
      hash = (37 * hash) + PROPS_ITEM_FIELD_NUMBER;
      hash = (53 * hash) + getPropsItemList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 小程序保存授权配置集合
   * </pre>
   *
   * Protobuf type {@code channel.AppletsAccessItem}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.AppletsAccessItem)
      cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AppletsAccessItem_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AppletsAccessItem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.class, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getPropsItemFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channelCode_ = "";

      if (propsItemBuilder_ == null) {
        propsItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        propsItemBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AppletsAccessItem_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem build() {
      cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem result = new cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem(this);
      int from_bitField0_ = bitField0_;
      result.channelCode_ = channelCode_;
      if (propsItemBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          propsItem_ = java.util.Collections.unmodifiableList(propsItem_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.propsItem_ = propsItem_;
      } else {
        result.propsItem_ = propsItemBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.getDefaultInstance()) return this;
      if (!other.getChannelCode().isEmpty()) {
        channelCode_ = other.channelCode_;
        onChanged();
      }
      if (propsItemBuilder_ == null) {
        if (!other.propsItem_.isEmpty()) {
          if (propsItem_.isEmpty()) {
            propsItem_ = other.propsItem_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePropsItemIsMutable();
            propsItem_.addAll(other.propsItem_);
          }
          onChanged();
        }
      } else {
        if (!other.propsItem_.isEmpty()) {
          if (propsItemBuilder_.isEmpty()) {
            propsItemBuilder_.dispose();
            propsItemBuilder_ = null;
            propsItem_ = other.propsItem_;
            bitField0_ = (bitField0_ & ~0x00000001);
            propsItemBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getPropsItemFieldBuilder() : null;
          } else {
            propsItemBuilder_.addAllMessages(other.propsItem_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object channelCode_ = "";
    /**
     * <pre>
     * 渠道名称
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return The channelCode.
     */
    public java.lang.String getChannelCode() {
      java.lang.Object ref = channelCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道名称
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return The bytes for channelCode.
     */
    public com.google.protobuf.ByteString
        getChannelCodeBytes() {
      java.lang.Object ref = channelCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道名称
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @param value The channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道名称
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelCode() {
      
      channelCode_ = getDefaultInstance().getChannelCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道名称
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @param value The bytes for channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelCode_ = value;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access> propsItem_ =
      java.util.Collections.emptyList();
    private void ensurePropsItemIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        propsItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.Access>(propsItem_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Access, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> propsItemBuilder_;

    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access> getPropsItemList() {
      if (propsItemBuilder_ == null) {
        return java.util.Collections.unmodifiableList(propsItem_);
      } else {
        return propsItemBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public int getPropsItemCount() {
      if (propsItemBuilder_ == null) {
        return propsItem_.size();
      } else {
        return propsItemBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Access getPropsItem(int index) {
      if (propsItemBuilder_ == null) {
        return propsItem_.get(index);
      } else {
        return propsItemBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public Builder setPropsItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Access value) {
      if (propsItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePropsItemIsMutable();
        propsItem_.set(index, value);
        onChanged();
      } else {
        propsItemBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public Builder setPropsItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder builderForValue) {
      if (propsItemBuilder_ == null) {
        ensurePropsItemIsMutable();
        propsItem_.set(index, builderForValue.build());
        onChanged();
      } else {
        propsItemBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public Builder addPropsItem(cn.hexcloud.pbis.common.service.facade.channel.Access value) {
      if (propsItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePropsItemIsMutable();
        propsItem_.add(value);
        onChanged();
      } else {
        propsItemBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public Builder addPropsItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Access value) {
      if (propsItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePropsItemIsMutable();
        propsItem_.add(index, value);
        onChanged();
      } else {
        propsItemBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public Builder addPropsItem(
        cn.hexcloud.pbis.common.service.facade.channel.Access.Builder builderForValue) {
      if (propsItemBuilder_ == null) {
        ensurePropsItemIsMutable();
        propsItem_.add(builderForValue.build());
        onChanged();
      } else {
        propsItemBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public Builder addPropsItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder builderForValue) {
      if (propsItemBuilder_ == null) {
        ensurePropsItemIsMutable();
        propsItem_.add(index, builderForValue.build());
        onChanged();
      } else {
        propsItemBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public Builder addAllPropsItem(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.channel.Access> values) {
      if (propsItemBuilder_ == null) {
        ensurePropsItemIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, propsItem_);
        onChanged();
      } else {
        propsItemBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public Builder clearPropsItem() {
      if (propsItemBuilder_ == null) {
        propsItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        propsItemBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public Builder removePropsItem(int index) {
      if (propsItemBuilder_ == null) {
        ensurePropsItemIsMutable();
        propsItem_.remove(index);
        onChanged();
      } else {
        propsItemBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Access.Builder getPropsItemBuilder(
        int index) {
      return getPropsItemFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder getPropsItemOrBuilder(
        int index) {
      if (propsItemBuilder_ == null) {
        return propsItem_.get(index);  } else {
        return propsItemBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> 
         getPropsItemOrBuilderList() {
      if (propsItemBuilder_ != null) {
        return propsItemBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(propsItem_);
      }
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Access.Builder addPropsItemBuilder() {
      return getPropsItemFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.channel.Access.getDefaultInstance());
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Access.Builder addPropsItemBuilder(
        int index) {
      return getPropsItemFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.channel.Access.getDefaultInstance());
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.Access props_item = 2;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access.Builder> 
         getPropsItemBuilderList() {
      return getPropsItemFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Access, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> 
        getPropsItemFieldBuilder() {
      if (propsItemBuilder_ == null) {
        propsItemBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.Access, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder>(
                propsItem_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        propsItem_ = null;
      }
      return propsItemBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.AppletsAccessItem)
  }

  // @@protoc_insertion_point(class_scope:channel.AppletsAccessItem)
  private static final cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AppletsAccessItem>
      PARSER = new com.google.protobuf.AbstractParser<AppletsAccessItem>() {
    @java.lang.Override
    public AppletsAccessItem parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new AppletsAccessItem(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<AppletsAccessItem> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AppletsAccessItem> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

