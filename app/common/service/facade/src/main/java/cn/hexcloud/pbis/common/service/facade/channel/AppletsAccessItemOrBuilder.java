// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface AppletsAccessItemOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.AppletsAccessItem)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 渠道名称
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  java.lang.String getChannelCode();
  /**
   * <pre>
   * 渠道名称
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  com.google.protobuf.ByteString
      getChannelCodeBytes();

  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.Access props_item = 2;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access> 
      getPropsItemList();
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.Access props_item = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.Access getPropsItem(int index);
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.Access props_item = 2;</code>
   */
  int getPropsItemCount();
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.Access props_item = 2;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> 
      getPropsItemOrBuilderList();
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.Access props_item = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder getPropsItemOrBuilder(
      int index);
}
