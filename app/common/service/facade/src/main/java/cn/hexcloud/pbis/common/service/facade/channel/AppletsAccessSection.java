// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 小程序授权配置保存对象
 * </pre>
 *
 * Protobuf type {@code channel.AppletsAccessSection}
 */
public final class AppletsAccessSection extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.AppletsAccessSection)
    AppletsAccessSectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use AppletsAccessSection.newBuilder() to construct.
  private AppletsAccessSection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private AppletsAccessSection() {
    applyTargetItem_ = java.util.Collections.emptyList();
    appletsAccessItem_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new AppletsAccessSection();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private AppletsAccessSection(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              applyTargetItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget>();
              mutable_bitField0_ |= 0x00000001;
            }
            applyTargetItem_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.parser(), extensionRegistry));
            break;
          }
          case 18: {
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              appletsAccessItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem>();
              mutable_bitField0_ |= 0x00000002;
            }
            appletsAccessItem_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        applyTargetItem_ = java.util.Collections.unmodifiableList(applyTargetItem_);
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        appletsAccessItem_ = java.util.Collections.unmodifiableList(appletsAccessItem_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AppletsAccessSection_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AppletsAccessSection_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.class, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.Builder.class);
  }

  public static final int APPLY_TARGET_ITEM_FIELD_NUMBER = 1;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget> applyTargetItem_;
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget> getApplyTargetItemList() {
    return applyTargetItem_;
  }
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder> 
      getApplyTargetItemOrBuilderList() {
    return applyTargetItem_;
  }
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
   */
  @java.lang.Override
  public int getApplyTargetItemCount() {
    return applyTargetItem_.size();
  }
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget getApplyTargetItem(int index) {
    return applyTargetItem_.get(index);
  }
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder getApplyTargetItemOrBuilder(
      int index) {
    return applyTargetItem_.get(index);
  }

  public static final int APPLETS_ACCESS_ITEM_FIELD_NUMBER = 2;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem> appletsAccessItem_;
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem> getAppletsAccessItemList() {
    return appletsAccessItem_;
  }
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder> 
      getAppletsAccessItemOrBuilderList() {
    return appletsAccessItem_;
  }
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
   */
  @java.lang.Override
  public int getAppletsAccessItemCount() {
    return appletsAccessItem_.size();
  }
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem getAppletsAccessItem(int index) {
    return appletsAccessItem_.get(index);
  }
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder getAppletsAccessItemOrBuilder(
      int index) {
    return appletsAccessItem_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < applyTargetItem_.size(); i++) {
      output.writeMessage(1, applyTargetItem_.get(i));
    }
    for (int i = 0; i < appletsAccessItem_.size(); i++) {
      output.writeMessage(2, appletsAccessItem_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < applyTargetItem_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, applyTargetItem_.get(i));
    }
    for (int i = 0; i < appletsAccessItem_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, appletsAccessItem_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection other = (cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection) obj;

    if (!getApplyTargetItemList()
        .equals(other.getApplyTargetItemList())) return false;
    if (!getAppletsAccessItemList()
        .equals(other.getAppletsAccessItemList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getApplyTargetItemCount() > 0) {
      hash = (37 * hash) + APPLY_TARGET_ITEM_FIELD_NUMBER;
      hash = (53 * hash) + getApplyTargetItemList().hashCode();
    }
    if (getAppletsAccessItemCount() > 0) {
      hash = (37 * hash) + APPLETS_ACCESS_ITEM_FIELD_NUMBER;
      hash = (53 * hash) + getAppletsAccessItemList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 小程序授权配置保存对象
   * </pre>
   *
   * Protobuf type {@code channel.AppletsAccessSection}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.AppletsAccessSection)
      cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AppletsAccessSection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AppletsAccessSection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.class, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getApplyTargetItemFieldBuilder();
        getAppletsAccessItemFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (applyTargetItemBuilder_ == null) {
        applyTargetItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        applyTargetItemBuilder_.clear();
      }
      if (appletsAccessItemBuilder_ == null) {
        appletsAccessItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
      } else {
        appletsAccessItemBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AppletsAccessSection_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection build() {
      cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection result = new cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection(this);
      int from_bitField0_ = bitField0_;
      if (applyTargetItemBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          applyTargetItem_ = java.util.Collections.unmodifiableList(applyTargetItem_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.applyTargetItem_ = applyTargetItem_;
      } else {
        result.applyTargetItem_ = applyTargetItemBuilder_.build();
      }
      if (appletsAccessItemBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          appletsAccessItem_ = java.util.Collections.unmodifiableList(appletsAccessItem_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.appletsAccessItem_ = appletsAccessItem_;
      } else {
        result.appletsAccessItem_ = appletsAccessItemBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.getDefaultInstance()) return this;
      if (applyTargetItemBuilder_ == null) {
        if (!other.applyTargetItem_.isEmpty()) {
          if (applyTargetItem_.isEmpty()) {
            applyTargetItem_ = other.applyTargetItem_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureApplyTargetItemIsMutable();
            applyTargetItem_.addAll(other.applyTargetItem_);
          }
          onChanged();
        }
      } else {
        if (!other.applyTargetItem_.isEmpty()) {
          if (applyTargetItemBuilder_.isEmpty()) {
            applyTargetItemBuilder_.dispose();
            applyTargetItemBuilder_ = null;
            applyTargetItem_ = other.applyTargetItem_;
            bitField0_ = (bitField0_ & ~0x00000001);
            applyTargetItemBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getApplyTargetItemFieldBuilder() : null;
          } else {
            applyTargetItemBuilder_.addAllMessages(other.applyTargetItem_);
          }
        }
      }
      if (appletsAccessItemBuilder_ == null) {
        if (!other.appletsAccessItem_.isEmpty()) {
          if (appletsAccessItem_.isEmpty()) {
            appletsAccessItem_ = other.appletsAccessItem_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureAppletsAccessItemIsMutable();
            appletsAccessItem_.addAll(other.appletsAccessItem_);
          }
          onChanged();
        }
      } else {
        if (!other.appletsAccessItem_.isEmpty()) {
          if (appletsAccessItemBuilder_.isEmpty()) {
            appletsAccessItemBuilder_.dispose();
            appletsAccessItemBuilder_ = null;
            appletsAccessItem_ = other.appletsAccessItem_;
            bitField0_ = (bitField0_ & ~0x00000002);
            appletsAccessItemBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getAppletsAccessItemFieldBuilder() : null;
          } else {
            appletsAccessItemBuilder_.addAllMessages(other.appletsAccessItem_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget> applyTargetItem_ =
      java.util.Collections.emptyList();
    private void ensureApplyTargetItemIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        applyTargetItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget>(applyTargetItem_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder, cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder> applyTargetItemBuilder_;

    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget> getApplyTargetItemList() {
      if (applyTargetItemBuilder_ == null) {
        return java.util.Collections.unmodifiableList(applyTargetItem_);
      } else {
        return applyTargetItemBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public int getApplyTargetItemCount() {
      if (applyTargetItemBuilder_ == null) {
        return applyTargetItem_.size();
      } else {
        return applyTargetItemBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget getApplyTargetItem(int index) {
      if (applyTargetItemBuilder_ == null) {
        return applyTargetItem_.get(index);
      } else {
        return applyTargetItemBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public Builder setApplyTargetItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget value) {
      if (applyTargetItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureApplyTargetItemIsMutable();
        applyTargetItem_.set(index, value);
        onChanged();
      } else {
        applyTargetItemBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public Builder setApplyTargetItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder builderForValue) {
      if (applyTargetItemBuilder_ == null) {
        ensureApplyTargetItemIsMutable();
        applyTargetItem_.set(index, builderForValue.build());
        onChanged();
      } else {
        applyTargetItemBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public Builder addApplyTargetItem(cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget value) {
      if (applyTargetItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureApplyTargetItemIsMutable();
        applyTargetItem_.add(value);
        onChanged();
      } else {
        applyTargetItemBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public Builder addApplyTargetItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget value) {
      if (applyTargetItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureApplyTargetItemIsMutable();
        applyTargetItem_.add(index, value);
        onChanged();
      } else {
        applyTargetItemBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public Builder addApplyTargetItem(
        cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder builderForValue) {
      if (applyTargetItemBuilder_ == null) {
        ensureApplyTargetItemIsMutable();
        applyTargetItem_.add(builderForValue.build());
        onChanged();
      } else {
        applyTargetItemBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public Builder addApplyTargetItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder builderForValue) {
      if (applyTargetItemBuilder_ == null) {
        ensureApplyTargetItemIsMutable();
        applyTargetItem_.add(index, builderForValue.build());
        onChanged();
      } else {
        applyTargetItemBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public Builder addAllApplyTargetItem(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget> values) {
      if (applyTargetItemBuilder_ == null) {
        ensureApplyTargetItemIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, applyTargetItem_);
        onChanged();
      } else {
        applyTargetItemBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public Builder clearApplyTargetItem() {
      if (applyTargetItemBuilder_ == null) {
        applyTargetItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        applyTargetItemBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public Builder removeApplyTargetItem(int index) {
      if (applyTargetItemBuilder_ == null) {
        ensureApplyTargetItemIsMutable();
        applyTargetItem_.remove(index);
        onChanged();
      } else {
        applyTargetItemBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder getApplyTargetItemBuilder(
        int index) {
      return getApplyTargetItemFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder getApplyTargetItemOrBuilder(
        int index) {
      if (applyTargetItemBuilder_ == null) {
        return applyTargetItem_.get(index);  } else {
        return applyTargetItemBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder> 
         getApplyTargetItemOrBuilderList() {
      if (applyTargetItemBuilder_ != null) {
        return applyTargetItemBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(applyTargetItem_);
      }
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder addApplyTargetItemBuilder() {
      return getApplyTargetItemFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.getDefaultInstance());
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder addApplyTargetItemBuilder(
        int index) {
      return getApplyTargetItemFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.getDefaultInstance());
    }
    /**
     * <pre>
     * 配置目标，租户id/公司id列表/门店id列表
     * </pre>
     *
     * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder> 
         getApplyTargetItemBuilderList() {
      return getApplyTargetItemFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder, cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder> 
        getApplyTargetItemFieldBuilder() {
      if (applyTargetItemBuilder_ == null) {
        applyTargetItemBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget, cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget.Builder, cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder>(
                applyTargetItem_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        applyTargetItem_ = null;
      }
      return applyTargetItemBuilder_;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem> appletsAccessItem_ =
      java.util.Collections.emptyList();
    private void ensureAppletsAccessItemIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        appletsAccessItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem>(appletsAccessItem_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder> appletsAccessItemBuilder_;

    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem> getAppletsAccessItemList() {
      if (appletsAccessItemBuilder_ == null) {
        return java.util.Collections.unmodifiableList(appletsAccessItem_);
      } else {
        return appletsAccessItemBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public int getAppletsAccessItemCount() {
      if (appletsAccessItemBuilder_ == null) {
        return appletsAccessItem_.size();
      } else {
        return appletsAccessItemBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem getAppletsAccessItem(int index) {
      if (appletsAccessItemBuilder_ == null) {
        return appletsAccessItem_.get(index);
      } else {
        return appletsAccessItemBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public Builder setAppletsAccessItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem value) {
      if (appletsAccessItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAppletsAccessItemIsMutable();
        appletsAccessItem_.set(index, value);
        onChanged();
      } else {
        appletsAccessItemBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public Builder setAppletsAccessItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder builderForValue) {
      if (appletsAccessItemBuilder_ == null) {
        ensureAppletsAccessItemIsMutable();
        appletsAccessItem_.set(index, builderForValue.build());
        onChanged();
      } else {
        appletsAccessItemBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public Builder addAppletsAccessItem(cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem value) {
      if (appletsAccessItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAppletsAccessItemIsMutable();
        appletsAccessItem_.add(value);
        onChanged();
      } else {
        appletsAccessItemBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public Builder addAppletsAccessItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem value) {
      if (appletsAccessItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAppletsAccessItemIsMutable();
        appletsAccessItem_.add(index, value);
        onChanged();
      } else {
        appletsAccessItemBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public Builder addAppletsAccessItem(
        cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder builderForValue) {
      if (appletsAccessItemBuilder_ == null) {
        ensureAppletsAccessItemIsMutable();
        appletsAccessItem_.add(builderForValue.build());
        onChanged();
      } else {
        appletsAccessItemBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public Builder addAppletsAccessItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder builderForValue) {
      if (appletsAccessItemBuilder_ == null) {
        ensureAppletsAccessItemIsMutable();
        appletsAccessItem_.add(index, builderForValue.build());
        onChanged();
      } else {
        appletsAccessItemBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public Builder addAllAppletsAccessItem(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem> values) {
      if (appletsAccessItemBuilder_ == null) {
        ensureAppletsAccessItemIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, appletsAccessItem_);
        onChanged();
      } else {
        appletsAccessItemBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public Builder clearAppletsAccessItem() {
      if (appletsAccessItemBuilder_ == null) {
        appletsAccessItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        appletsAccessItemBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public Builder removeAppletsAccessItem(int index) {
      if (appletsAccessItemBuilder_ == null) {
        ensureAppletsAccessItemIsMutable();
        appletsAccessItem_.remove(index);
        onChanged();
      } else {
        appletsAccessItemBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder getAppletsAccessItemBuilder(
        int index) {
      return getAppletsAccessItemFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder getAppletsAccessItemOrBuilder(
        int index) {
      if (appletsAccessItemBuilder_ == null) {
        return appletsAccessItem_.get(index);  } else {
        return appletsAccessItemBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder> 
         getAppletsAccessItemOrBuilderList() {
      if (appletsAccessItemBuilder_ != null) {
        return appletsAccessItemBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(appletsAccessItem_);
      }
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder addAppletsAccessItemBuilder() {
      return getAppletsAccessItemFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.getDefaultInstance());
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder addAppletsAccessItemBuilder(
        int index) {
      return getAppletsAccessItemFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.getDefaultInstance());
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder> 
         getAppletsAccessItemBuilderList() {
      return getAppletsAccessItemFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder> 
        getAppletsAccessItemFieldBuilder() {
      if (appletsAccessItemBuilder_ == null) {
        appletsAccessItemBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder>(
                appletsAccessItem_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        appletsAccessItem_ = null;
      }
      return appletsAccessItemBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.AppletsAccessSection)
  }

  // @@protoc_insertion_point(class_scope:channel.AppletsAccessSection)
  private static final cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AppletsAccessSection>
      PARSER = new com.google.protobuf.AbstractParser<AppletsAccessSection>() {
    @java.lang.Override
    public AppletsAccessSection parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new AppletsAccessSection(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<AppletsAccessSection> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AppletsAccessSection> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

