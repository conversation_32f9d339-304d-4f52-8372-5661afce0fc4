// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface AppletsAccessSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.AppletsAccessSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget> 
      getApplyTargetItemList();
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.ApplyTarget getApplyTargetItem(int index);
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
   */
  int getApplyTargetItemCount();
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder> 
      getApplyTargetItemOrBuilderList();
  /**
   * <pre>
   * 配置目标，租户id/公司id列表/门店id列表
   * </pre>
   *
   * <code>repeated .channel.ApplyTarget apply_target_item = 1;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.ApplyTargetOrBuilder getApplyTargetItemOrBuilder(
      int index);

  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem> 
      getAppletsAccessItemList();
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem getAppletsAccessItem(int index);
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
   */
  int getAppletsAccessItemCount();
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder> 
      getAppletsAccessItemOrBuilderList();
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder getAppletsAccessItemOrBuilder(
      int index);
}
