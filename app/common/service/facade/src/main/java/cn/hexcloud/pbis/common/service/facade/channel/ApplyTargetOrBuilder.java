// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ApplyTargetOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ApplyTarget)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 1;</code>
   * @return The storeId.
   */
  java.lang.String getStoreId();
  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 1;</code>
   * @return The bytes for storeId.
   */
  com.google.protobuf.ByteString
      getStoreIdBytes();

  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 2;</code>
   * @return The companyId.
   */
  java.lang.String getCompanyId();
  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 2;</code>
   * @return The bytes for companyId.
   */
  com.google.protobuf.ByteString
      getCompanyIdBytes();
}
