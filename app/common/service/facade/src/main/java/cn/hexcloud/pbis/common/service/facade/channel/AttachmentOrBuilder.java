// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface AttachmentOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.Attachment)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   **
   * （必传）附件名称
   * 支付宝当面付待签约附件信息
   * 营业执照：BusinessLicensePic，店铺门头照片：ShopSignBoardPic，店铺内景照片：ShopScenePic
   * </pre>
   *
   * <code>string name = 1;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   **
   * （必传）附件名称
   * 支付宝当面付待签约附件信息
   * 营业执照：BusinessLicensePic，店铺门头照片：ShopSignBoardPic，店铺内景照片：ShopScenePic
   * </pre>
   *
   * <code>string name = 1;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * （可选）附件文件名（需携带文件后缀名）
   * </pre>
   *
   * <code>string fileName = 2;</code>
   * @return The fileName.
   */
  java.lang.String getFileName();
  /**
   * <pre>
   * （可选）附件文件名（需携带文件后缀名）
   * </pre>
   *
   * <code>string fileName = 2;</code>
   * @return The bytes for fileName.
   */
  com.google.protobuf.ByteString
      getFileNameBytes();

  /**
   * <pre>
   * （必传）附件URL
   * </pre>
   *
   * <code>string url = 3;</code>
   * @return The url.
   */
  java.lang.String getUrl();
  /**
   * <pre>
   * （必传）附件URL
   * </pre>
   *
   * <code>string url = 3;</code>
   * @return The bytes for url.
   */
  com.google.protobuf.ByteString
      getUrlBytes();

  /**
   * <pre>
   * （可选）附件内容
   * </pre>
   *
   * <code>bytes content = 4;</code>
   * @return The content.
   */
  com.google.protobuf.ByteString getContent();
}
