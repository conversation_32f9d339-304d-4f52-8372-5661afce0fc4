// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道签约授权信息对象
 * </pre>
 *
 * Protobuf type {@code channel.AuthorizationItem}
 */
public final class AuthorizationItem extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.AuthorizationItem)
    AuthorizationItemOrBuilder {
private static final long serialVersionUID = 0L;
  // Use AuthorizationItem.newBuilder() to construct.
  private AuthorizationItem(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private AuthorizationItem() {
    channelCode_ = "";
    merchantId_ = "";
    signupNo_ = "";
    signupState_ = "";
    grantState_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new AuthorizationItem();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private AuthorizationItem(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            authId_ = input.readInt32();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            channelCode_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            merchantId_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            signupNo_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            signupState_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            grantState_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AuthorizationItem_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AuthorizationItem_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.class, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.Builder.class);
  }

  public static final int AUTH_ID_FIELD_NUMBER = 1;
  private int authId_;
  /**
   * <pre>
   * （必传）签约授权ID
   * </pre>
   *
   * <code>int32 auth_id = 1;</code>
   * @return The authId.
   */
  @java.lang.Override
  public int getAuthId() {
    return authId_;
  }

  public static final int CHANNEL_CODE_FIELD_NUMBER = 2;
  private volatile java.lang.Object channelCode_;
  /**
   * <pre>
   * （必传）渠道代码
   * </pre>
   *
   * <code>string channel_code = 2;</code>
   * @return The channelCode.
   */
  @java.lang.Override
  public java.lang.String getChannelCode() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）渠道代码
   * </pre>
   *
   * <code>string channel_code = 2;</code>
   * @return The bytes for channelCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelCodeBytes() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MERCHANT_ID_FIELD_NUMBER = 3;
  private volatile java.lang.Object merchantId_;
  /**
   * <pre>
   * （必传）第三方商户PID
   * </pre>
   *
   * <code>string merchant_id = 3;</code>
   * @return The merchantId.
   */
  @java.lang.Override
  public java.lang.String getMerchantId() {
    java.lang.Object ref = merchantId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      merchantId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）第三方商户PID
   * </pre>
   *
   * <code>string merchant_id = 3;</code>
   * @return The bytes for merchantId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMerchantIdBytes() {
    java.lang.Object ref = merchantId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      merchantId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SIGNUP_NO_FIELD_NUMBER = 4;
  private volatile java.lang.Object signupNo_;
  /**
   * <pre>
   * （必传）签约单号
   * </pre>
   *
   * <code>string signup_no = 4;</code>
   * @return The signupNo.
   */
  @java.lang.Override
  public java.lang.String getSignupNo() {
    java.lang.Object ref = signupNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      signupNo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）签约单号
   * </pre>
   *
   * <code>string signup_no = 4;</code>
   * @return The bytes for signupNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSignupNoBytes() {
    java.lang.Object ref = signupNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      signupNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SIGNUP_STATE_FIELD_NUMBER = 5;
  private volatile java.lang.Object signupState_;
  /**
   * <pre>
   * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
   * </pre>
   *
   * <code>string signup_state = 5;</code>
   * @return The signupState.
   */
  @java.lang.Override
  public java.lang.String getSignupState() {
    java.lang.Object ref = signupState_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      signupState_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
   * </pre>
   *
   * <code>string signup_state = 5;</code>
   * @return The bytes for signupState.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSignupStateBytes() {
    java.lang.Object ref = signupState_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      signupState_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int GRANT_STATE_FIELD_NUMBER = 6;
  private volatile java.lang.Object grantState_;
  /**
   * <pre>
   * （必传）授权状态，WAITING 待授权；COMPLETE 已授权
   * </pre>
   *
   * <code>string grant_state = 6;</code>
   * @return The grantState.
   */
  @java.lang.Override
  public java.lang.String getGrantState() {
    java.lang.Object ref = grantState_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      grantState_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）授权状态，WAITING 待授权；COMPLETE 已授权
   * </pre>
   *
   * <code>string grant_state = 6;</code>
   * @return The bytes for grantState.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getGrantStateBytes() {
    java.lang.Object ref = grantState_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      grantState_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (authId_ != 0) {
      output.writeInt32(1, authId_);
    }
    if (!getChannelCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, channelCode_);
    }
    if (!getMerchantIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, merchantId_);
    }
    if (!getSignupNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, signupNo_);
    }
    if (!getSignupStateBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, signupState_);
    }
    if (!getGrantStateBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, grantState_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (authId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, authId_);
    }
    if (!getChannelCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, channelCode_);
    }
    if (!getMerchantIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, merchantId_);
    }
    if (!getSignupNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, signupNo_);
    }
    if (!getSignupStateBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, signupState_);
    }
    if (!getGrantStateBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, grantState_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem other = (cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem) obj;

    if (getAuthId()
        != other.getAuthId()) return false;
    if (!getChannelCode()
        .equals(other.getChannelCode())) return false;
    if (!getMerchantId()
        .equals(other.getMerchantId())) return false;
    if (!getSignupNo()
        .equals(other.getSignupNo())) return false;
    if (!getSignupState()
        .equals(other.getSignupState())) return false;
    if (!getGrantState()
        .equals(other.getGrantState())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + AUTH_ID_FIELD_NUMBER;
    hash = (53 * hash) + getAuthId();
    hash = (37 * hash) + CHANNEL_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getChannelCode().hashCode();
    hash = (37 * hash) + MERCHANT_ID_FIELD_NUMBER;
    hash = (53 * hash) + getMerchantId().hashCode();
    hash = (37 * hash) + SIGNUP_NO_FIELD_NUMBER;
    hash = (53 * hash) + getSignupNo().hashCode();
    hash = (37 * hash) + SIGNUP_STATE_FIELD_NUMBER;
    hash = (53 * hash) + getSignupState().hashCode();
    hash = (37 * hash) + GRANT_STATE_FIELD_NUMBER;
    hash = (53 * hash) + getGrantState().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道签约授权信息对象
   * </pre>
   *
   * Protobuf type {@code channel.AuthorizationItem}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.AuthorizationItem)
      cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItemOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AuthorizationItem_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AuthorizationItem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.class, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      authId_ = 0;

      channelCode_ = "";

      merchantId_ = "";

      signupNo_ = "";

      signupState_ = "";

      grantState_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AuthorizationItem_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem build() {
      cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem result = new cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem(this);
      result.authId_ = authId_;
      result.channelCode_ = channelCode_;
      result.merchantId_ = merchantId_;
      result.signupNo_ = signupNo_;
      result.signupState_ = signupState_;
      result.grantState_ = grantState_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.getDefaultInstance()) return this;
      if (other.getAuthId() != 0) {
        setAuthId(other.getAuthId());
      }
      if (!other.getChannelCode().isEmpty()) {
        channelCode_ = other.channelCode_;
        onChanged();
      }
      if (!other.getMerchantId().isEmpty()) {
        merchantId_ = other.merchantId_;
        onChanged();
      }
      if (!other.getSignupNo().isEmpty()) {
        signupNo_ = other.signupNo_;
        onChanged();
      }
      if (!other.getSignupState().isEmpty()) {
        signupState_ = other.signupState_;
        onChanged();
      }
      if (!other.getGrantState().isEmpty()) {
        grantState_ = other.grantState_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int authId_ ;
    /**
     * <pre>
     * （必传）签约授权ID
     * </pre>
     *
     * <code>int32 auth_id = 1;</code>
     * @return The authId.
     */
    @java.lang.Override
    public int getAuthId() {
      return authId_;
    }
    /**
     * <pre>
     * （必传）签约授权ID
     * </pre>
     *
     * <code>int32 auth_id = 1;</code>
     * @param value The authId to set.
     * @return This builder for chaining.
     */
    public Builder setAuthId(int value) {
      
      authId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）签约授权ID
     * </pre>
     *
     * <code>int32 auth_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearAuthId() {
      
      authId_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object channelCode_ = "";
    /**
     * <pre>
     * （必传）渠道代码
     * </pre>
     *
     * <code>string channel_code = 2;</code>
     * @return The channelCode.
     */
    public java.lang.String getChannelCode() {
      java.lang.Object ref = channelCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）渠道代码
     * </pre>
     *
     * <code>string channel_code = 2;</code>
     * @return The bytes for channelCode.
     */
    public com.google.protobuf.ByteString
        getChannelCodeBytes() {
      java.lang.Object ref = channelCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）渠道代码
     * </pre>
     *
     * <code>string channel_code = 2;</code>
     * @param value The channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）渠道代码
     * </pre>
     *
     * <code>string channel_code = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelCode() {
      
      channelCode_ = getDefaultInstance().getChannelCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）渠道代码
     * </pre>
     *
     * <code>string channel_code = 2;</code>
     * @param value The bytes for channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object merchantId_ = "";
    /**
     * <pre>
     * （必传）第三方商户PID
     * </pre>
     *
     * <code>string merchant_id = 3;</code>
     * @return The merchantId.
     */
    public java.lang.String getMerchantId() {
      java.lang.Object ref = merchantId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        merchantId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）第三方商户PID
     * </pre>
     *
     * <code>string merchant_id = 3;</code>
     * @return The bytes for merchantId.
     */
    public com.google.protobuf.ByteString
        getMerchantIdBytes() {
      java.lang.Object ref = merchantId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        merchantId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）第三方商户PID
     * </pre>
     *
     * <code>string merchant_id = 3;</code>
     * @param value The merchantId to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      merchantId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）第三方商户PID
     * </pre>
     *
     * <code>string merchant_id = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearMerchantId() {
      
      merchantId_ = getDefaultInstance().getMerchantId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）第三方商户PID
     * </pre>
     *
     * <code>string merchant_id = 3;</code>
     * @param value The bytes for merchantId to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      merchantId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object signupNo_ = "";
    /**
     * <pre>
     * （必传）签约单号
     * </pre>
     *
     * <code>string signup_no = 4;</code>
     * @return The signupNo.
     */
    public java.lang.String getSignupNo() {
      java.lang.Object ref = signupNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        signupNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）签约单号
     * </pre>
     *
     * <code>string signup_no = 4;</code>
     * @return The bytes for signupNo.
     */
    public com.google.protobuf.ByteString
        getSignupNoBytes() {
      java.lang.Object ref = signupNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        signupNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）签约单号
     * </pre>
     *
     * <code>string signup_no = 4;</code>
     * @param value The signupNo to set.
     * @return This builder for chaining.
     */
    public Builder setSignupNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      signupNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）签约单号
     * </pre>
     *
     * <code>string signup_no = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearSignupNo() {
      
      signupNo_ = getDefaultInstance().getSignupNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）签约单号
     * </pre>
     *
     * <code>string signup_no = 4;</code>
     * @param value The bytes for signupNo to set.
     * @return This builder for chaining.
     */
    public Builder setSignupNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      signupNo_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object signupState_ = "";
    /**
     * <pre>
     * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
     * </pre>
     *
     * <code>string signup_state = 5;</code>
     * @return The signupState.
     */
    public java.lang.String getSignupState() {
      java.lang.Object ref = signupState_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        signupState_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
     * </pre>
     *
     * <code>string signup_state = 5;</code>
     * @return The bytes for signupState.
     */
    public com.google.protobuf.ByteString
        getSignupStateBytes() {
      java.lang.Object ref = signupState_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        signupState_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
     * </pre>
     *
     * <code>string signup_state = 5;</code>
     * @param value The signupState to set.
     * @return This builder for chaining.
     */
    public Builder setSignupState(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      signupState_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
     * </pre>
     *
     * <code>string signup_state = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearSignupState() {
      
      signupState_ = getDefaultInstance().getSignupState();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
     * </pre>
     *
     * <code>string signup_state = 5;</code>
     * @param value The bytes for signupState to set.
     * @return This builder for chaining.
     */
    public Builder setSignupStateBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      signupState_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object grantState_ = "";
    /**
     * <pre>
     * （必传）授权状态，WAITING 待授权；COMPLETE 已授权
     * </pre>
     *
     * <code>string grant_state = 6;</code>
     * @return The grantState.
     */
    public java.lang.String getGrantState() {
      java.lang.Object ref = grantState_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        grantState_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）授权状态，WAITING 待授权；COMPLETE 已授权
     * </pre>
     *
     * <code>string grant_state = 6;</code>
     * @return The bytes for grantState.
     */
    public com.google.protobuf.ByteString
        getGrantStateBytes() {
      java.lang.Object ref = grantState_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        grantState_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）授权状态，WAITING 待授权；COMPLETE 已授权
     * </pre>
     *
     * <code>string grant_state = 6;</code>
     * @param value The grantState to set.
     * @return This builder for chaining.
     */
    public Builder setGrantState(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      grantState_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）授权状态，WAITING 待授权；COMPLETE 已授权
     * </pre>
     *
     * <code>string grant_state = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearGrantState() {
      
      grantState_ = getDefaultInstance().getGrantState();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）授权状态，WAITING 待授权；COMPLETE 已授权
     * </pre>
     *
     * <code>string grant_state = 6;</code>
     * @param value The bytes for grantState to set.
     * @return This builder for chaining.
     */
    public Builder setGrantStateBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      grantState_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.AuthorizationItem)
  }

  // @@protoc_insertion_point(class_scope:channel.AuthorizationItem)
  private static final cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AuthorizationItem>
      PARSER = new com.google.protobuf.AbstractParser<AuthorizationItem>() {
    @java.lang.Override
    public AuthorizationItem parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new AuthorizationItem(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<AuthorizationItem> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AuthorizationItem> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

