// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface AuthorizationItemOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.AuthorizationItem)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）签约授权ID
   * </pre>
   *
   * <code>int32 auth_id = 1;</code>
   * @return The authId.
   */
  int getAuthId();

  /**
   * <pre>
   * （必传）渠道代码
   * </pre>
   *
   * <code>string channel_code = 2;</code>
   * @return The channelCode.
   */
  java.lang.String getChannelCode();
  /**
   * <pre>
   * （必传）渠道代码
   * </pre>
   *
   * <code>string channel_code = 2;</code>
   * @return The bytes for channelCode.
   */
  com.google.protobuf.ByteString
      getChannelCodeBytes();

  /**
   * <pre>
   * （必传）第三方商户PID
   * </pre>
   *
   * <code>string merchant_id = 3;</code>
   * @return The merchantId.
   */
  java.lang.String getMerchantId();
  /**
   * <pre>
   * （必传）第三方商户PID
   * </pre>
   *
   * <code>string merchant_id = 3;</code>
   * @return The bytes for merchantId.
   */
  com.google.protobuf.ByteString
      getMerchantIdBytes();

  /**
   * <pre>
   * （必传）签约单号
   * </pre>
   *
   * <code>string signup_no = 4;</code>
   * @return The signupNo.
   */
  java.lang.String getSignupNo();
  /**
   * <pre>
   * （必传）签约单号
   * </pre>
   *
   * <code>string signup_no = 4;</code>
   * @return The bytes for signupNo.
   */
  com.google.protobuf.ByteString
      getSignupNoBytes();

  /**
   * <pre>
   * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
   * </pre>
   *
   * <code>string signup_state = 5;</code>
   * @return The signupState.
   */
  java.lang.String getSignupState();
  /**
   * <pre>
   * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
   * </pre>
   *
   * <code>string signup_state = 5;</code>
   * @return The bytes for signupState.
   */
  com.google.protobuf.ByteString
      getSignupStateBytes();

  /**
   * <pre>
   * （必传）授权状态，WAITING 待授权；COMPLETE 已授权
   * </pre>
   *
   * <code>string grant_state = 6;</code>
   * @return The grantState.
   */
  java.lang.String getGrantState();
  /**
   * <pre>
   * （必传）授权状态，WAITING 待授权；COMPLETE 已授权
   * </pre>
   *
   * <code>string grant_state = 6;</code>
   * @return The bytes for grantState.
   */
  com.google.protobuf.ByteString
      getGrantStateBytes();
}
