// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道签约授权信息查询结果
 * </pre>
 *
 * Protobuf type {@code channel.AuthorizationListSection}
 */
public final class AuthorizationListSection extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.AuthorizationListSection)
    AuthorizationListSectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use AuthorizationListSection.newBuilder() to construct.
  private AuthorizationListSection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private AuthorizationListSection() {
    authorizationItem_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new AuthorizationListSection();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private AuthorizationListSection(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            pageIndex_ = input.readInt32();
            break;
          }
          case 16: {

            pageSize_ = input.readInt32();
            break;
          }
          case 24: {

            pageCount_ = input.readInt32();
            break;
          }
          case 32: {

            total_ = input.readInt64();
            break;
          }
          case 42: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              authorizationItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem>();
              mutable_bitField0_ |= 0x00000001;
            }
            authorizationItem_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        authorizationItem_ = java.util.Collections.unmodifiableList(authorizationItem_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AuthorizationListSection_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AuthorizationListSection_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.class, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.Builder.class);
  }

  public static final int PAGE_INDEX_FIELD_NUMBER = 1;
  private int pageIndex_;
  /**
   * <pre>
   * （必传）当前页码
   * </pre>
   *
   * <code>int32 page_index = 1;</code>
   * @return The pageIndex.
   */
  @java.lang.Override
  public int getPageIndex() {
    return pageIndex_;
  }

  public static final int PAGE_SIZE_FIELD_NUMBER = 2;
  private int pageSize_;
  /**
   * <pre>
   * （必传）分页步长
   * </pre>
   *
   * <code>int32 page_size = 2;</code>
   * @return The pageSize.
   */
  @java.lang.Override
  public int getPageSize() {
    return pageSize_;
  }

  public static final int PAGE_COUNT_FIELD_NUMBER = 3;
  private int pageCount_;
  /**
   * <pre>
   * （必传）总页数
   * </pre>
   *
   * <code>int32 page_count = 3;</code>
   * @return The pageCount.
   */
  @java.lang.Override
  public int getPageCount() {
    return pageCount_;
  }

  public static final int TOTAL_FIELD_NUMBER = 4;
  private long total_;
  /**
   * <pre>
   * （必传）总条数
   * </pre>
   *
   * <code>int64 total = 4;</code>
   * @return The total.
   */
  @java.lang.Override
  public long getTotal() {
    return total_;
  }

  public static final int AUTHORIZATION_ITEM_FIELD_NUMBER = 5;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem> authorizationItem_;
  /**
   * <pre>
   * （必传）渠道签约授权信息
   * </pre>
   *
   * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem> getAuthorizationItemList() {
    return authorizationItem_;
  }
  /**
   * <pre>
   * （必传）渠道签约授权信息
   * </pre>
   *
   * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItemOrBuilder> 
      getAuthorizationItemOrBuilderList() {
    return authorizationItem_;
  }
  /**
   * <pre>
   * （必传）渠道签约授权信息
   * </pre>
   *
   * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
   */
  @java.lang.Override
  public int getAuthorizationItemCount() {
    return authorizationItem_.size();
  }
  /**
   * <pre>
   * （必传）渠道签约授权信息
   * </pre>
   *
   * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem getAuthorizationItem(int index) {
    return authorizationItem_.get(index);
  }
  /**
   * <pre>
   * （必传）渠道签约授权信息
   * </pre>
   *
   * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItemOrBuilder getAuthorizationItemOrBuilder(
      int index) {
    return authorizationItem_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (pageIndex_ != 0) {
      output.writeInt32(1, pageIndex_);
    }
    if (pageSize_ != 0) {
      output.writeInt32(2, pageSize_);
    }
    if (pageCount_ != 0) {
      output.writeInt32(3, pageCount_);
    }
    if (total_ != 0L) {
      output.writeInt64(4, total_);
    }
    for (int i = 0; i < authorizationItem_.size(); i++) {
      output.writeMessage(5, authorizationItem_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (pageIndex_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, pageIndex_);
    }
    if (pageSize_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, pageSize_);
    }
    if (pageCount_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, pageCount_);
    }
    if (total_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, total_);
    }
    for (int i = 0; i < authorizationItem_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, authorizationItem_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection other = (cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection) obj;

    if (getPageIndex()
        != other.getPageIndex()) return false;
    if (getPageSize()
        != other.getPageSize()) return false;
    if (getPageCount()
        != other.getPageCount()) return false;
    if (getTotal()
        != other.getTotal()) return false;
    if (!getAuthorizationItemList()
        .equals(other.getAuthorizationItemList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + PAGE_INDEX_FIELD_NUMBER;
    hash = (53 * hash) + getPageIndex();
    hash = (37 * hash) + PAGE_SIZE_FIELD_NUMBER;
    hash = (53 * hash) + getPageSize();
    hash = (37 * hash) + PAGE_COUNT_FIELD_NUMBER;
    hash = (53 * hash) + getPageCount();
    hash = (37 * hash) + TOTAL_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTotal());
    if (getAuthorizationItemCount() > 0) {
      hash = (37 * hash) + AUTHORIZATION_ITEM_FIELD_NUMBER;
      hash = (53 * hash) + getAuthorizationItemList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道签约授权信息查询结果
   * </pre>
   *
   * Protobuf type {@code channel.AuthorizationListSection}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.AuthorizationListSection)
      cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AuthorizationListSection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AuthorizationListSection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.class, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getAuthorizationItemFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      pageIndex_ = 0;

      pageSize_ = 0;

      pageCount_ = 0;

      total_ = 0L;

      if (authorizationItemBuilder_ == null) {
        authorizationItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        authorizationItemBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_AuthorizationListSection_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection build() {
      cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection result = new cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection(this);
      int from_bitField0_ = bitField0_;
      result.pageIndex_ = pageIndex_;
      result.pageSize_ = pageSize_;
      result.pageCount_ = pageCount_;
      result.total_ = total_;
      if (authorizationItemBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          authorizationItem_ = java.util.Collections.unmodifiableList(authorizationItem_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.authorizationItem_ = authorizationItem_;
      } else {
        result.authorizationItem_ = authorizationItemBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.getDefaultInstance()) return this;
      if (other.getPageIndex() != 0) {
        setPageIndex(other.getPageIndex());
      }
      if (other.getPageSize() != 0) {
        setPageSize(other.getPageSize());
      }
      if (other.getPageCount() != 0) {
        setPageCount(other.getPageCount());
      }
      if (other.getTotal() != 0L) {
        setTotal(other.getTotal());
      }
      if (authorizationItemBuilder_ == null) {
        if (!other.authorizationItem_.isEmpty()) {
          if (authorizationItem_.isEmpty()) {
            authorizationItem_ = other.authorizationItem_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureAuthorizationItemIsMutable();
            authorizationItem_.addAll(other.authorizationItem_);
          }
          onChanged();
        }
      } else {
        if (!other.authorizationItem_.isEmpty()) {
          if (authorizationItemBuilder_.isEmpty()) {
            authorizationItemBuilder_.dispose();
            authorizationItemBuilder_ = null;
            authorizationItem_ = other.authorizationItem_;
            bitField0_ = (bitField0_ & ~0x00000001);
            authorizationItemBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getAuthorizationItemFieldBuilder() : null;
          } else {
            authorizationItemBuilder_.addAllMessages(other.authorizationItem_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private int pageIndex_ ;
    /**
     * <pre>
     * （必传）当前页码
     * </pre>
     *
     * <code>int32 page_index = 1;</code>
     * @return The pageIndex.
     */
    @java.lang.Override
    public int getPageIndex() {
      return pageIndex_;
    }
    /**
     * <pre>
     * （必传）当前页码
     * </pre>
     *
     * <code>int32 page_index = 1;</code>
     * @param value The pageIndex to set.
     * @return This builder for chaining.
     */
    public Builder setPageIndex(int value) {
      
      pageIndex_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）当前页码
     * </pre>
     *
     * <code>int32 page_index = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearPageIndex() {
      
      pageIndex_ = 0;
      onChanged();
      return this;
    }

    private int pageSize_ ;
    /**
     * <pre>
     * （必传）分页步长
     * </pre>
     *
     * <code>int32 page_size = 2;</code>
     * @return The pageSize.
     */
    @java.lang.Override
    public int getPageSize() {
      return pageSize_;
    }
    /**
     * <pre>
     * （必传）分页步长
     * </pre>
     *
     * <code>int32 page_size = 2;</code>
     * @param value The pageSize to set.
     * @return This builder for chaining.
     */
    public Builder setPageSize(int value) {
      
      pageSize_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）分页步长
     * </pre>
     *
     * <code>int32 page_size = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearPageSize() {
      
      pageSize_ = 0;
      onChanged();
      return this;
    }

    private int pageCount_ ;
    /**
     * <pre>
     * （必传）总页数
     * </pre>
     *
     * <code>int32 page_count = 3;</code>
     * @return The pageCount.
     */
    @java.lang.Override
    public int getPageCount() {
      return pageCount_;
    }
    /**
     * <pre>
     * （必传）总页数
     * </pre>
     *
     * <code>int32 page_count = 3;</code>
     * @param value The pageCount to set.
     * @return This builder for chaining.
     */
    public Builder setPageCount(int value) {
      
      pageCount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）总页数
     * </pre>
     *
     * <code>int32 page_count = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearPageCount() {
      
      pageCount_ = 0;
      onChanged();
      return this;
    }

    private long total_ ;
    /**
     * <pre>
     * （必传）总条数
     * </pre>
     *
     * <code>int64 total = 4;</code>
     * @return The total.
     */
    @java.lang.Override
    public long getTotal() {
      return total_;
    }
    /**
     * <pre>
     * （必传）总条数
     * </pre>
     *
     * <code>int64 total = 4;</code>
     * @param value The total to set.
     * @return This builder for chaining.
     */
    public Builder setTotal(long value) {
      
      total_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）总条数
     * </pre>
     *
     * <code>int64 total = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearTotal() {
      
      total_ = 0L;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem> authorizationItem_ =
      java.util.Collections.emptyList();
    private void ensureAuthorizationItemIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        authorizationItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem>(authorizationItem_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItemOrBuilder> authorizationItemBuilder_;

    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem> getAuthorizationItemList() {
      if (authorizationItemBuilder_ == null) {
        return java.util.Collections.unmodifiableList(authorizationItem_);
      } else {
        return authorizationItemBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public int getAuthorizationItemCount() {
      if (authorizationItemBuilder_ == null) {
        return authorizationItem_.size();
      } else {
        return authorizationItemBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem getAuthorizationItem(int index) {
      if (authorizationItemBuilder_ == null) {
        return authorizationItem_.get(index);
      } else {
        return authorizationItemBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public Builder setAuthorizationItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem value) {
      if (authorizationItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAuthorizationItemIsMutable();
        authorizationItem_.set(index, value);
        onChanged();
      } else {
        authorizationItemBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public Builder setAuthorizationItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.Builder builderForValue) {
      if (authorizationItemBuilder_ == null) {
        ensureAuthorizationItemIsMutable();
        authorizationItem_.set(index, builderForValue.build());
        onChanged();
      } else {
        authorizationItemBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public Builder addAuthorizationItem(cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem value) {
      if (authorizationItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAuthorizationItemIsMutable();
        authorizationItem_.add(value);
        onChanged();
      } else {
        authorizationItemBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public Builder addAuthorizationItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem value) {
      if (authorizationItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAuthorizationItemIsMutable();
        authorizationItem_.add(index, value);
        onChanged();
      } else {
        authorizationItemBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public Builder addAuthorizationItem(
        cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.Builder builderForValue) {
      if (authorizationItemBuilder_ == null) {
        ensureAuthorizationItemIsMutable();
        authorizationItem_.add(builderForValue.build());
        onChanged();
      } else {
        authorizationItemBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public Builder addAuthorizationItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.Builder builderForValue) {
      if (authorizationItemBuilder_ == null) {
        ensureAuthorizationItemIsMutable();
        authorizationItem_.add(index, builderForValue.build());
        onChanged();
      } else {
        authorizationItemBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public Builder addAllAuthorizationItem(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem> values) {
      if (authorizationItemBuilder_ == null) {
        ensureAuthorizationItemIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, authorizationItem_);
        onChanged();
      } else {
        authorizationItemBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public Builder clearAuthorizationItem() {
      if (authorizationItemBuilder_ == null) {
        authorizationItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        authorizationItemBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public Builder removeAuthorizationItem(int index) {
      if (authorizationItemBuilder_ == null) {
        ensureAuthorizationItemIsMutable();
        authorizationItem_.remove(index);
        onChanged();
      } else {
        authorizationItemBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.Builder getAuthorizationItemBuilder(
        int index) {
      return getAuthorizationItemFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItemOrBuilder getAuthorizationItemOrBuilder(
        int index) {
      if (authorizationItemBuilder_ == null) {
        return authorizationItem_.get(index);  } else {
        return authorizationItemBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItemOrBuilder> 
         getAuthorizationItemOrBuilderList() {
      if (authorizationItemBuilder_ != null) {
        return authorizationItemBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(authorizationItem_);
      }
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.Builder addAuthorizationItemBuilder() {
      return getAuthorizationItemFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.getDefaultInstance());
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.Builder addAuthorizationItemBuilder(
        int index) {
      return getAuthorizationItemFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.getDefaultInstance());
    }
    /**
     * <pre>
     * （必传）渠道签约授权信息
     * </pre>
     *
     * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.Builder> 
         getAuthorizationItemBuilderList() {
      return getAuthorizationItemFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItemOrBuilder> 
        getAuthorizationItemFieldBuilder() {
      if (authorizationItemBuilder_ == null) {
        authorizationItemBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItemOrBuilder>(
                authorizationItem_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        authorizationItem_ = null;
      }
      return authorizationItemBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.AuthorizationListSection)
  }

  // @@protoc_insertion_point(class_scope:channel.AuthorizationListSection)
  private static final cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AuthorizationListSection>
      PARSER = new com.google.protobuf.AbstractParser<AuthorizationListSection>() {
    @java.lang.Override
    public AuthorizationListSection parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new AuthorizationListSection(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<AuthorizationListSection> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AuthorizationListSection> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

