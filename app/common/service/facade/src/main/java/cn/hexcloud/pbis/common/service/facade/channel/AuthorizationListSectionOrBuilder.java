// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface AuthorizationListSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.AuthorizationListSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）当前页码
   * </pre>
   *
   * <code>int32 page_index = 1;</code>
   * @return The pageIndex.
   */
  int getPageIndex();

  /**
   * <pre>
   * （必传）分页步长
   * </pre>
   *
   * <code>int32 page_size = 2;</code>
   * @return The pageSize.
   */
  int getPageSize();

  /**
   * <pre>
   * （必传）总页数
   * </pre>
   *
   * <code>int32 page_count = 3;</code>
   * @return The pageCount.
   */
  int getPageCount();

  /**
   * <pre>
   * （必传）总条数
   * </pre>
   *
   * <code>int64 total = 4;</code>
   * @return The total.
   */
  long getTotal();

  /**
   * <pre>
   * （必传）渠道签约授权信息
   * </pre>
   *
   * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem> 
      getAuthorizationItemList();
  /**
   * <pre>
   * （必传）渠道签约授权信息
   * </pre>
   *
   * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItem getAuthorizationItem(int index);
  /**
   * <pre>
   * （必传）渠道签约授权信息
   * </pre>
   *
   * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
   */
  int getAuthorizationItemCount();
  /**
   * <pre>
   * （必传）渠道签约授权信息
   * </pre>
   *
   * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItemOrBuilder> 
      getAuthorizationItemOrBuilderList();
  /**
   * <pre>
   * （必传）渠道签约授权信息
   * </pre>
   *
   * <code>repeated .channel.AuthorizationItem authorization_item = 5;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.AuthorizationItemOrBuilder getAuthorizationItemOrBuilder(
      int index);
}
