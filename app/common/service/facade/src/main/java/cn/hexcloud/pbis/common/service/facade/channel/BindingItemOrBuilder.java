// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface BindingItemOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.BindingItem)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *（必传）渠道代码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  java.lang.String getChannelCode();
  /**
   * <pre>
   *（必传）渠道代码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  com.google.protobuf.ByteString
      getChannelCodeBytes();

  /**
   * <pre>
   *（必传）渠道名称
   * </pre>
   *
   * <code>string channel_name = 2;</code>
   * @return The channelName.
   */
  java.lang.String getChannelName();
  /**
   * <pre>
   *（必传）渠道名称
   * </pre>
   *
   * <code>string channel_name = 2;</code>
   * @return The bytes for channelName.
   */
  com.google.protobuf.ByteString
      getChannelNameBytes();

  /**
   * <pre>
   *（必传）渠道业务代码，","分割
   * </pre>
   *
   * <code>string business_code = 3;</code>
   * @return The businessCode.
   */
  java.lang.String getBusinessCode();
  /**
   * <pre>
   *（必传）渠道业务代码，","分割
   * </pre>
   *
   * <code>string business_code = 3;</code>
   * @return The bytes for businessCode.
   */
  com.google.protobuf.ByteString
      getBusinessCodeBytes();

  /**
   * <pre>
   *（必传）渠道性质，KA 品牌商；ISV 服务商
   * </pre>
   *
   * <code>string channel_type = 4;</code>
   * @return The channelType.
   */
  java.lang.String getChannelType();
  /**
   * <pre>
   *（必传）渠道性质，KA 品牌商；ISV 服务商
   * </pre>
   *
   * <code>string channel_type = 4;</code>
   * @return The bytes for channelType.
   */
  com.google.protobuf.ByteString
      getChannelTypeBytes();

  /**
   * <pre>
   *（必传）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
   * </pre>
   *
   * <code>string channel_category = 5;</code>
   * @return The channelCategory.
   */
  java.lang.String getChannelCategory();
  /**
   * <pre>
   *（必传）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
   * </pre>
   *
   * <code>string channel_category = 5;</code>
   * @return The bytes for channelCategory.
   */
  com.google.protobuf.ByteString
      getChannelCategoryBytes();

  /**
   * <pre>
   *（可选）渠道二级分类，AM 小程序&amp;会员；COUPON 卡券
   * </pre>
   *
   * <code>string channel_sub_category = 6;</code>
   * @return The channelSubCategory.
   */
  java.lang.String getChannelSubCategory();
  /**
   * <pre>
   *（可选）渠道二级分类，AM 小程序&amp;会员；COUPON 卡券
   * </pre>
   *
   * <code>string channel_sub_category = 6;</code>
   * @return The bytes for channelSubCategory.
   */
  com.google.protobuf.ByteString
      getChannelSubCategoryBytes();

  /**
   * <pre>
   *（可选）渠道标签，逗号分割
   * </pre>
   *
   * <code>string channel_labels = 7;</code>
   * @return The channelLabels.
   */
  java.lang.String getChannelLabels();
  /**
   * <pre>
   *（可选）渠道标签，逗号分割
   * </pre>
   *
   * <code>string channel_labels = 7;</code>
   * @return The bytes for channelLabels.
   */
  com.google.protobuf.ByteString
      getChannelLabelsBytes();

  /**
   * <pre>
   *（必传）是否启用
   * </pre>
   *
   * <code>string enabled = 8;</code>
   * @return The enabled.
   */
  java.lang.String getEnabled();
  /**
   * <pre>
   *（必传）是否启用
   * </pre>
   *
   * <code>string enabled = 8;</code>
   * @return The bytes for enabled.
   */
  com.google.protobuf.ByteString
      getEnabledBytes();
}
