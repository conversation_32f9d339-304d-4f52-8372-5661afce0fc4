// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道绑定关系查询结果
 * </pre>
 *
 * Protobuf type {@code channel.BindingListSection}
 */
public final class BindingListSection extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.BindingListSection)
    BindingListSectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use BindingListSection.newBuilder() to construct.
  private BindingListSection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private BindingListSection() {
    bindingItem_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new BindingListSection();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private BindingListSection(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              bindingItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.BindingItem>();
              mutable_bitField0_ |= 0x00000001;
            }
            bindingItem_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.BindingItem.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        bindingItem_ = java.util.Collections.unmodifiableList(bindingItem_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_BindingListSection_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_BindingListSection_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.class, cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.Builder.class);
  }

  public static final int BINDING_ITEM_FIELD_NUMBER = 1;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.BindingItem> bindingItem_;
  /**
   * <pre>
   * （必传）渠道绑定关系信息
   * </pre>
   *
   * <code>repeated .channel.BindingItem binding_item = 1;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.BindingItem> getBindingItemList() {
    return bindingItem_;
  }
  /**
   * <pre>
   * （必传）渠道绑定关系信息
   * </pre>
   *
   * <code>repeated .channel.BindingItem binding_item = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.BindingItemOrBuilder> 
      getBindingItemOrBuilderList() {
    return bindingItem_;
  }
  /**
   * <pre>
   * （必传）渠道绑定关系信息
   * </pre>
   *
   * <code>repeated .channel.BindingItem binding_item = 1;</code>
   */
  @java.lang.Override
  public int getBindingItemCount() {
    return bindingItem_.size();
  }
  /**
   * <pre>
   * （必传）渠道绑定关系信息
   * </pre>
   *
   * <code>repeated .channel.BindingItem binding_item = 1;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.BindingItem getBindingItem(int index) {
    return bindingItem_.get(index);
  }
  /**
   * <pre>
   * （必传）渠道绑定关系信息
   * </pre>
   *
   * <code>repeated .channel.BindingItem binding_item = 1;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.BindingItemOrBuilder getBindingItemOrBuilder(
      int index) {
    return bindingItem_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < bindingItem_.size(); i++) {
      output.writeMessage(1, bindingItem_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < bindingItem_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, bindingItem_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.BindingListSection)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.BindingListSection other = (cn.hexcloud.pbis.common.service.facade.channel.BindingListSection) obj;

    if (!getBindingItemList()
        .equals(other.getBindingItemList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getBindingItemCount() > 0) {
      hash = (37 * hash) + BINDING_ITEM_FIELD_NUMBER;
      hash = (53 * hash) + getBindingItemList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.BindingListSection parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.BindingListSection parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.BindingListSection parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.BindingListSection parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.BindingListSection parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.BindingListSection parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.BindingListSection parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.BindingListSection parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.BindingListSection parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.BindingListSection parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.BindingListSection parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.BindingListSection parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.BindingListSection prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道绑定关系查询结果
   * </pre>
   *
   * Protobuf type {@code channel.BindingListSection}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.BindingListSection)
      cn.hexcloud.pbis.common.service.facade.channel.BindingListSectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_BindingListSection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_BindingListSection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.class, cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getBindingItemFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (bindingItemBuilder_ == null) {
        bindingItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        bindingItemBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_BindingListSection_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.BindingListSection getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.BindingListSection build() {
      cn.hexcloud.pbis.common.service.facade.channel.BindingListSection result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.BindingListSection buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.BindingListSection result = new cn.hexcloud.pbis.common.service.facade.channel.BindingListSection(this);
      int from_bitField0_ = bitField0_;
      if (bindingItemBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          bindingItem_ = java.util.Collections.unmodifiableList(bindingItem_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.bindingItem_ = bindingItem_;
      } else {
        result.bindingItem_ = bindingItemBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.BindingListSection) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.BindingListSection)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.BindingListSection other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.getDefaultInstance()) return this;
      if (bindingItemBuilder_ == null) {
        if (!other.bindingItem_.isEmpty()) {
          if (bindingItem_.isEmpty()) {
            bindingItem_ = other.bindingItem_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureBindingItemIsMutable();
            bindingItem_.addAll(other.bindingItem_);
          }
          onChanged();
        }
      } else {
        if (!other.bindingItem_.isEmpty()) {
          if (bindingItemBuilder_.isEmpty()) {
            bindingItemBuilder_.dispose();
            bindingItemBuilder_ = null;
            bindingItem_ = other.bindingItem_;
            bitField0_ = (bitField0_ & ~0x00000001);
            bindingItemBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getBindingItemFieldBuilder() : null;
          } else {
            bindingItemBuilder_.addAllMessages(other.bindingItem_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.BindingListSection parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.BindingListSection) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.BindingItem> bindingItem_ =
      java.util.Collections.emptyList();
    private void ensureBindingItemIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        bindingItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.BindingItem>(bindingItem_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.BindingItem, cn.hexcloud.pbis.common.service.facade.channel.BindingItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.BindingItemOrBuilder> bindingItemBuilder_;

    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.BindingItem> getBindingItemList() {
      if (bindingItemBuilder_ == null) {
        return java.util.Collections.unmodifiableList(bindingItem_);
      } else {
        return bindingItemBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public int getBindingItemCount() {
      if (bindingItemBuilder_ == null) {
        return bindingItem_.size();
      } else {
        return bindingItemBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.BindingItem getBindingItem(int index) {
      if (bindingItemBuilder_ == null) {
        return bindingItem_.get(index);
      } else {
        return bindingItemBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public Builder setBindingItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.BindingItem value) {
      if (bindingItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBindingItemIsMutable();
        bindingItem_.set(index, value);
        onChanged();
      } else {
        bindingItemBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public Builder setBindingItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.BindingItem.Builder builderForValue) {
      if (bindingItemBuilder_ == null) {
        ensureBindingItemIsMutable();
        bindingItem_.set(index, builderForValue.build());
        onChanged();
      } else {
        bindingItemBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public Builder addBindingItem(cn.hexcloud.pbis.common.service.facade.channel.BindingItem value) {
      if (bindingItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBindingItemIsMutable();
        bindingItem_.add(value);
        onChanged();
      } else {
        bindingItemBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public Builder addBindingItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.BindingItem value) {
      if (bindingItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureBindingItemIsMutable();
        bindingItem_.add(index, value);
        onChanged();
      } else {
        bindingItemBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public Builder addBindingItem(
        cn.hexcloud.pbis.common.service.facade.channel.BindingItem.Builder builderForValue) {
      if (bindingItemBuilder_ == null) {
        ensureBindingItemIsMutable();
        bindingItem_.add(builderForValue.build());
        onChanged();
      } else {
        bindingItemBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public Builder addBindingItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.BindingItem.Builder builderForValue) {
      if (bindingItemBuilder_ == null) {
        ensureBindingItemIsMutable();
        bindingItem_.add(index, builderForValue.build());
        onChanged();
      } else {
        bindingItemBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public Builder addAllBindingItem(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.channel.BindingItem> values) {
      if (bindingItemBuilder_ == null) {
        ensureBindingItemIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, bindingItem_);
        onChanged();
      } else {
        bindingItemBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public Builder clearBindingItem() {
      if (bindingItemBuilder_ == null) {
        bindingItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        bindingItemBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public Builder removeBindingItem(int index) {
      if (bindingItemBuilder_ == null) {
        ensureBindingItemIsMutable();
        bindingItem_.remove(index);
        onChanged();
      } else {
        bindingItemBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.BindingItem.Builder getBindingItemBuilder(
        int index) {
      return getBindingItemFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.BindingItemOrBuilder getBindingItemOrBuilder(
        int index) {
      if (bindingItemBuilder_ == null) {
        return bindingItem_.get(index);  } else {
        return bindingItemBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.BindingItemOrBuilder> 
         getBindingItemOrBuilderList() {
      if (bindingItemBuilder_ != null) {
        return bindingItemBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(bindingItem_);
      }
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.BindingItem.Builder addBindingItemBuilder() {
      return getBindingItemFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.channel.BindingItem.getDefaultInstance());
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.BindingItem.Builder addBindingItemBuilder(
        int index) {
      return getBindingItemFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.channel.BindingItem.getDefaultInstance());
    }
    /**
     * <pre>
     * （必传）渠道绑定关系信息
     * </pre>
     *
     * <code>repeated .channel.BindingItem binding_item = 1;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.BindingItem.Builder> 
         getBindingItemBuilderList() {
      return getBindingItemFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.BindingItem, cn.hexcloud.pbis.common.service.facade.channel.BindingItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.BindingItemOrBuilder> 
        getBindingItemFieldBuilder() {
      if (bindingItemBuilder_ == null) {
        bindingItemBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.BindingItem, cn.hexcloud.pbis.common.service.facade.channel.BindingItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.BindingItemOrBuilder>(
                bindingItem_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        bindingItem_ = null;
      }
      return bindingItemBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.BindingListSection)
  }

  // @@protoc_insertion_point(class_scope:channel.BindingListSection)
  private static final cn.hexcloud.pbis.common.service.facade.channel.BindingListSection DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.BindingListSection();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.BindingListSection getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<BindingListSection>
      PARSER = new com.google.protobuf.AbstractParser<BindingListSection>() {
    @java.lang.Override
    public BindingListSection parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new BindingListSection(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<BindingListSection> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<BindingListSection> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.BindingListSection getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

