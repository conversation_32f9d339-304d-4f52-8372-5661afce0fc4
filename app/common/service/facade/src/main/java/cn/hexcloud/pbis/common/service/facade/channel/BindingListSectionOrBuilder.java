// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface BindingListSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.BindingListSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）渠道绑定关系信息
   * </pre>
   *
   * <code>repeated .channel.BindingItem binding_item = 1;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.channel.BindingItem> 
      getBindingItemList();
  /**
   * <pre>
   * （必传）渠道绑定关系信息
   * </pre>
   *
   * <code>repeated .channel.BindingItem binding_item = 1;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.BindingItem getBindingItem(int index);
  /**
   * <pre>
   * （必传）渠道绑定关系信息
   * </pre>
   *
   * <code>repeated .channel.BindingItem binding_item = 1;</code>
   */
  int getBindingItemCount();
  /**
   * <pre>
   * （必传）渠道绑定关系信息
   * </pre>
   *
   * <code>repeated .channel.BindingItem binding_item = 1;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.BindingItemOrBuilder> 
      getBindingItemOrBuilderList();
  /**
   * <pre>
   * （必传）渠道绑定关系信息
   * </pre>
   *
   * <code>repeated .channel.BindingItem binding_item = 1;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.BindingItemOrBuilder getBindingItemOrBuilder(
      int index);
}
