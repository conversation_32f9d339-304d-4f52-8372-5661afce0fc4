// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * Protobuf type {@code channel.Channel}
 */
public final class Channel extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.Channel)
    ChannelOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Channel.newBuilder() to construct.
  private Channel(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Channel() {
    channelCode_ = "";
    channelName_ = "";
    channelLogo_ = "";
    channelType_ = "";
    channelCategory_ = "";
    description_ = "";
    channelProperties_ = "";
    channelLabels_ = "";
    channelSubCategory_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Channel();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Channel(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            channelCode_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            channelName_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            channelLogo_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            channelType_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            channelCategory_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            description_ = s;
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            channelProperties_ = s;
            break;
          }
          case 64: {

            channelId_ = input.readInt64();
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            channelLabels_ = s;
            break;
          }
          case 82: {
            java.lang.String s = input.readStringRequireUtf8();

            channelSubCategory_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_Channel_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_Channel_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.Channel.class, cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder.class);
  }

  public static final int CHANNEL_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object channelCode_;
  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  @java.lang.Override
  public java.lang.String getChannelCode() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelCodeBytes() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHANNEL_NAME_FIELD_NUMBER = 2;
  private volatile java.lang.Object channelName_;
  /**
   * <pre>
   * 渠道名称
   * </pre>
   *
   * <code>string channel_name = 2;</code>
   * @return The channelName.
   */
  @java.lang.Override
  public java.lang.String getChannelName() {
    java.lang.Object ref = channelName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道名称
   * </pre>
   *
   * <code>string channel_name = 2;</code>
   * @return The bytes for channelName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelNameBytes() {
    java.lang.Object ref = channelName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHANNEL_LOGO_FIELD_NUMBER = 3;
  private volatile java.lang.Object channelLogo_;
  /**
   * <pre>
   * 渠道logo
   * </pre>
   *
   * <code>string channel_logo = 3;</code>
   * @return The channelLogo.
   */
  @java.lang.Override
  public java.lang.String getChannelLogo() {
    java.lang.Object ref = channelLogo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelLogo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道logo
   * </pre>
   *
   * <code>string channel_logo = 3;</code>
   * @return The bytes for channelLogo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelLogoBytes() {
    java.lang.Object ref = channelLogo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelLogo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHANNEL_TYPE_FIELD_NUMBER = 4;
  private volatile java.lang.Object channelType_;
  /**
   * <pre>
   * 渠道性质，KA 品牌商；ISV 服务商
   * </pre>
   *
   * <code>string channel_type = 4;</code>
   * @return The channelType.
   */
  @java.lang.Override
  public java.lang.String getChannelType() {
    java.lang.Object ref = channelType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelType_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道性质，KA 品牌商；ISV 服务商
   * </pre>
   *
   * <code>string channel_type = 4;</code>
   * @return The bytes for channelType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelTypeBytes() {
    java.lang.Object ref = channelType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHANNEL_CATEGORY_FIELD_NUMBER = 5;
  private volatile java.lang.Object channelCategory_;
  /**
   * <pre>
   * 渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
   * </pre>
   *
   * <code>string channel_category = 5;</code>
   * @return The channelCategory.
   */
  @java.lang.Override
  public java.lang.String getChannelCategory() {
    java.lang.Object ref = channelCategory_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelCategory_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
   * </pre>
   *
   * <code>string channel_category = 5;</code>
   * @return The bytes for channelCategory.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelCategoryBytes() {
    java.lang.Object ref = channelCategory_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelCategory_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DESCRIPTION_FIELD_NUMBER = 6;
  private volatile java.lang.Object description_;
  /**
   * <pre>
   * 渠道描述
   * </pre>
   *
   * <code>string description = 6;</code>
   * @return The description.
   */
  @java.lang.Override
  public java.lang.String getDescription() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      description_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道描述
   * </pre>
   *
   * <code>string description = 6;</code>
   * @return The bytes for description.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescriptionBytes() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      description_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHANNEL_PROPERTIES_FIELD_NUMBER = 7;
  private volatile java.lang.Object channelProperties_;
  /**
   * <pre>
   * 渠道附加信息，JSON格式
   * </pre>
   *
   * <code>string channel_properties = 7;</code>
   * @return The channelProperties.
   */
  @java.lang.Override
  public java.lang.String getChannelProperties() {
    java.lang.Object ref = channelProperties_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelProperties_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道附加信息，JSON格式
   * </pre>
   *
   * <code>string channel_properties = 7;</code>
   * @return The bytes for channelProperties.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelPropertiesBytes() {
    java.lang.Object ref = channelProperties_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelProperties_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHANNEL_ID_FIELD_NUMBER = 8;
  private long channelId_;
  /**
   * <pre>
   * 渠道主键
   * </pre>
   *
   * <code>int64 channel_id = 8;</code>
   * @return The channelId.
   */
  @java.lang.Override
  public long getChannelId() {
    return channelId_;
  }

  public static final int CHANNEL_LABELS_FIELD_NUMBER = 9;
  private volatile java.lang.Object channelLabels_;
  /**
   * <pre>
   * 渠道标签
   * </pre>
   *
   * <code>string channel_labels = 9;</code>
   * @return The channelLabels.
   */
  @java.lang.Override
  public java.lang.String getChannelLabels() {
    java.lang.Object ref = channelLabels_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelLabels_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道标签
   * </pre>
   *
   * <code>string channel_labels = 9;</code>
   * @return The bytes for channelLabels.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelLabelsBytes() {
    java.lang.Object ref = channelLabels_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelLabels_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHANNEL_SUB_CATEGORY_FIELD_NUMBER = 10;
  private volatile java.lang.Object channelSubCategory_;
  /**
   * <pre>
   * 渠道二级分类，AM 小程序&amp;会员；COUPON 卡券
   * </pre>
   *
   * <code>string channel_sub_category = 10;</code>
   * @return The channelSubCategory.
   */
  @java.lang.Override
  public java.lang.String getChannelSubCategory() {
    java.lang.Object ref = channelSubCategory_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelSubCategory_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道二级分类，AM 小程序&amp;会员；COUPON 卡券
   * </pre>
   *
   * <code>string channel_sub_category = 10;</code>
   * @return The bytes for channelSubCategory.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelSubCategoryBytes() {
    java.lang.Object ref = channelSubCategory_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelSubCategory_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getChannelCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelCode_);
    }
    if (!getChannelNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, channelName_);
    }
    if (!getChannelLogoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, channelLogo_);
    }
    if (!getChannelTypeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, channelType_);
    }
    if (!getChannelCategoryBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, channelCategory_);
    }
    if (!getDescriptionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, description_);
    }
    if (!getChannelPropertiesBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, channelProperties_);
    }
    if (channelId_ != 0L) {
      output.writeInt64(8, channelId_);
    }
    if (!getChannelLabelsBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, channelLabels_);
    }
    if (!getChannelSubCategoryBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, channelSubCategory_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getChannelCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelCode_);
    }
    if (!getChannelNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, channelName_);
    }
    if (!getChannelLogoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, channelLogo_);
    }
    if (!getChannelTypeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, channelType_);
    }
    if (!getChannelCategoryBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, channelCategory_);
    }
    if (!getDescriptionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, description_);
    }
    if (!getChannelPropertiesBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, channelProperties_);
    }
    if (channelId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(8, channelId_);
    }
    if (!getChannelLabelsBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, channelLabels_);
    }
    if (!getChannelSubCategoryBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, channelSubCategory_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.Channel)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.Channel other = (cn.hexcloud.pbis.common.service.facade.channel.Channel) obj;

    if (!getChannelCode()
        .equals(other.getChannelCode())) return false;
    if (!getChannelName()
        .equals(other.getChannelName())) return false;
    if (!getChannelLogo()
        .equals(other.getChannelLogo())) return false;
    if (!getChannelType()
        .equals(other.getChannelType())) return false;
    if (!getChannelCategory()
        .equals(other.getChannelCategory())) return false;
    if (!getDescription()
        .equals(other.getDescription())) return false;
    if (!getChannelProperties()
        .equals(other.getChannelProperties())) return false;
    if (getChannelId()
        != other.getChannelId()) return false;
    if (!getChannelLabels()
        .equals(other.getChannelLabels())) return false;
    if (!getChannelSubCategory()
        .equals(other.getChannelSubCategory())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHANNEL_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getChannelCode().hashCode();
    hash = (37 * hash) + CHANNEL_NAME_FIELD_NUMBER;
    hash = (53 * hash) + getChannelName().hashCode();
    hash = (37 * hash) + CHANNEL_LOGO_FIELD_NUMBER;
    hash = (53 * hash) + getChannelLogo().hashCode();
    hash = (37 * hash) + CHANNEL_TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getChannelType().hashCode();
    hash = (37 * hash) + CHANNEL_CATEGORY_FIELD_NUMBER;
    hash = (53 * hash) + getChannelCategory().hashCode();
    hash = (37 * hash) + DESCRIPTION_FIELD_NUMBER;
    hash = (53 * hash) + getDescription().hashCode();
    hash = (37 * hash) + CHANNEL_PROPERTIES_FIELD_NUMBER;
    hash = (53 * hash) + getChannelProperties().hashCode();
    hash = (37 * hash) + CHANNEL_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getChannelId());
    hash = (37 * hash) + CHANNEL_LABELS_FIELD_NUMBER;
    hash = (53 * hash) + getChannelLabels().hashCode();
    hash = (37 * hash) + CHANNEL_SUB_CATEGORY_FIELD_NUMBER;
    hash = (53 * hash) + getChannelSubCategory().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.Channel parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Channel parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Channel parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Channel parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Channel parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Channel parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Channel parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Channel parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Channel parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Channel parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Channel parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Channel parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.Channel prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code channel.Channel}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.Channel)
      cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_Channel_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_Channel_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.Channel.class, cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.Channel.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channelCode_ = "";

      channelName_ = "";

      channelLogo_ = "";

      channelType_ = "";

      channelCategory_ = "";

      description_ = "";

      channelProperties_ = "";

      channelId_ = 0L;

      channelLabels_ = "";

      channelSubCategory_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_Channel_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.Channel getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.Channel.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.Channel build() {
      cn.hexcloud.pbis.common.service.facade.channel.Channel result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.Channel buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.Channel result = new cn.hexcloud.pbis.common.service.facade.channel.Channel(this);
      result.channelCode_ = channelCode_;
      result.channelName_ = channelName_;
      result.channelLogo_ = channelLogo_;
      result.channelType_ = channelType_;
      result.channelCategory_ = channelCategory_;
      result.description_ = description_;
      result.channelProperties_ = channelProperties_;
      result.channelId_ = channelId_;
      result.channelLabels_ = channelLabels_;
      result.channelSubCategory_ = channelSubCategory_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.Channel) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.Channel)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.Channel other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.Channel.getDefaultInstance()) return this;
      if (!other.getChannelCode().isEmpty()) {
        channelCode_ = other.channelCode_;
        onChanged();
      }
      if (!other.getChannelName().isEmpty()) {
        channelName_ = other.channelName_;
        onChanged();
      }
      if (!other.getChannelLogo().isEmpty()) {
        channelLogo_ = other.channelLogo_;
        onChanged();
      }
      if (!other.getChannelType().isEmpty()) {
        channelType_ = other.channelType_;
        onChanged();
      }
      if (!other.getChannelCategory().isEmpty()) {
        channelCategory_ = other.channelCategory_;
        onChanged();
      }
      if (!other.getDescription().isEmpty()) {
        description_ = other.description_;
        onChanged();
      }
      if (!other.getChannelProperties().isEmpty()) {
        channelProperties_ = other.channelProperties_;
        onChanged();
      }
      if (other.getChannelId() != 0L) {
        setChannelId(other.getChannelId());
      }
      if (!other.getChannelLabels().isEmpty()) {
        channelLabels_ = other.channelLabels_;
        onChanged();
      }
      if (!other.getChannelSubCategory().isEmpty()) {
        channelSubCategory_ = other.channelSubCategory_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.Channel parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.Channel) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object channelCode_ = "";
    /**
     * <pre>
     * 渠道编码
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return The channelCode.
     */
    public java.lang.String getChannelCode() {
      java.lang.Object ref = channelCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道编码
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return The bytes for channelCode.
     */
    public com.google.protobuf.ByteString
        getChannelCodeBytes() {
      java.lang.Object ref = channelCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道编码
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @param value The channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道编码
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelCode() {
      
      channelCode_ = getDefaultInstance().getChannelCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道编码
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @param value The bytes for channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object channelName_ = "";
    /**
     * <pre>
     * 渠道名称
     * </pre>
     *
     * <code>string channel_name = 2;</code>
     * @return The channelName.
     */
    public java.lang.String getChannelName() {
      java.lang.Object ref = channelName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道名称
     * </pre>
     *
     * <code>string channel_name = 2;</code>
     * @return The bytes for channelName.
     */
    public com.google.protobuf.ByteString
        getChannelNameBytes() {
      java.lang.Object ref = channelName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道名称
     * </pre>
     *
     * <code>string channel_name = 2;</code>
     * @param value The channelName to set.
     * @return This builder for chaining.
     */
    public Builder setChannelName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelName_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道名称
     * </pre>
     *
     * <code>string channel_name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelName() {
      
      channelName_ = getDefaultInstance().getChannelName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道名称
     * </pre>
     *
     * <code>string channel_name = 2;</code>
     * @param value The bytes for channelName to set.
     * @return This builder for chaining.
     */
    public Builder setChannelNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelName_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object channelLogo_ = "";
    /**
     * <pre>
     * 渠道logo
     * </pre>
     *
     * <code>string channel_logo = 3;</code>
     * @return The channelLogo.
     */
    public java.lang.String getChannelLogo() {
      java.lang.Object ref = channelLogo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelLogo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道logo
     * </pre>
     *
     * <code>string channel_logo = 3;</code>
     * @return The bytes for channelLogo.
     */
    public com.google.protobuf.ByteString
        getChannelLogoBytes() {
      java.lang.Object ref = channelLogo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelLogo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道logo
     * </pre>
     *
     * <code>string channel_logo = 3;</code>
     * @param value The channelLogo to set.
     * @return This builder for chaining.
     */
    public Builder setChannelLogo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelLogo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道logo
     * </pre>
     *
     * <code>string channel_logo = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelLogo() {
      
      channelLogo_ = getDefaultInstance().getChannelLogo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道logo
     * </pre>
     *
     * <code>string channel_logo = 3;</code>
     * @param value The bytes for channelLogo to set.
     * @return This builder for chaining.
     */
    public Builder setChannelLogoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelLogo_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object channelType_ = "";
    /**
     * <pre>
     * 渠道性质，KA 品牌商；ISV 服务商
     * </pre>
     *
     * <code>string channel_type = 4;</code>
     * @return The channelType.
     */
    public java.lang.String getChannelType() {
      java.lang.Object ref = channelType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelType_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道性质，KA 品牌商；ISV 服务商
     * </pre>
     *
     * <code>string channel_type = 4;</code>
     * @return The bytes for channelType.
     */
    public com.google.protobuf.ByteString
        getChannelTypeBytes() {
      java.lang.Object ref = channelType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道性质，KA 品牌商；ISV 服务商
     * </pre>
     *
     * <code>string channel_type = 4;</code>
     * @param value The channelType to set.
     * @return This builder for chaining.
     */
    public Builder setChannelType(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道性质，KA 品牌商；ISV 服务商
     * </pre>
     *
     * <code>string channel_type = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelType() {
      
      channelType_ = getDefaultInstance().getChannelType();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道性质，KA 品牌商；ISV 服务商
     * </pre>
     *
     * <code>string channel_type = 4;</code>
     * @param value The bytes for channelType to set.
     * @return This builder for chaining.
     */
    public Builder setChannelTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelType_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object channelCategory_ = "";
    /**
     * <pre>
     * 渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
     * </pre>
     *
     * <code>string channel_category = 5;</code>
     * @return The channelCategory.
     */
    public java.lang.String getChannelCategory() {
      java.lang.Object ref = channelCategory_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelCategory_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
     * </pre>
     *
     * <code>string channel_category = 5;</code>
     * @return The bytes for channelCategory.
     */
    public com.google.protobuf.ByteString
        getChannelCategoryBytes() {
      java.lang.Object ref = channelCategory_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelCategory_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
     * </pre>
     *
     * <code>string channel_category = 5;</code>
     * @param value The channelCategory to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCategory(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelCategory_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
     * </pre>
     *
     * <code>string channel_category = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelCategory() {
      
      channelCategory_ = getDefaultInstance().getChannelCategory();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
     * </pre>
     *
     * <code>string channel_category = 5;</code>
     * @param value The bytes for channelCategory to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCategoryBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelCategory_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object description_ = "";
    /**
     * <pre>
     * 渠道描述
     * </pre>
     *
     * <code>string description = 6;</code>
     * @return The description.
     */
    public java.lang.String getDescription() {
      java.lang.Object ref = description_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        description_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道描述
     * </pre>
     *
     * <code>string description = 6;</code>
     * @return The bytes for description.
     */
    public com.google.protobuf.ByteString
        getDescriptionBytes() {
      java.lang.Object ref = description_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        description_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道描述
     * </pre>
     *
     * <code>string description = 6;</code>
     * @param value The description to set.
     * @return This builder for chaining.
     */
    public Builder setDescription(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      description_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道描述
     * </pre>
     *
     * <code>string description = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearDescription() {
      
      description_ = getDefaultInstance().getDescription();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道描述
     * </pre>
     *
     * <code>string description = 6;</code>
     * @param value The bytes for description to set.
     * @return This builder for chaining.
     */
    public Builder setDescriptionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      description_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object channelProperties_ = "";
    /**
     * <pre>
     * 渠道附加信息，JSON格式
     * </pre>
     *
     * <code>string channel_properties = 7;</code>
     * @return The channelProperties.
     */
    public java.lang.String getChannelProperties() {
      java.lang.Object ref = channelProperties_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelProperties_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道附加信息，JSON格式
     * </pre>
     *
     * <code>string channel_properties = 7;</code>
     * @return The bytes for channelProperties.
     */
    public com.google.protobuf.ByteString
        getChannelPropertiesBytes() {
      java.lang.Object ref = channelProperties_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelProperties_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道附加信息，JSON格式
     * </pre>
     *
     * <code>string channel_properties = 7;</code>
     * @param value The channelProperties to set.
     * @return This builder for chaining.
     */
    public Builder setChannelProperties(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelProperties_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道附加信息，JSON格式
     * </pre>
     *
     * <code>string channel_properties = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelProperties() {
      
      channelProperties_ = getDefaultInstance().getChannelProperties();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道附加信息，JSON格式
     * </pre>
     *
     * <code>string channel_properties = 7;</code>
     * @param value The bytes for channelProperties to set.
     * @return This builder for chaining.
     */
    public Builder setChannelPropertiesBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelProperties_ = value;
      onChanged();
      return this;
    }

    private long channelId_ ;
    /**
     * <pre>
     * 渠道主键
     * </pre>
     *
     * <code>int64 channel_id = 8;</code>
     * @return The channelId.
     */
    @java.lang.Override
    public long getChannelId() {
      return channelId_;
    }
    /**
     * <pre>
     * 渠道主键
     * </pre>
     *
     * <code>int64 channel_id = 8;</code>
     * @param value The channelId to set.
     * @return This builder for chaining.
     */
    public Builder setChannelId(long value) {
      
      channelId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道主键
     * </pre>
     *
     * <code>int64 channel_id = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelId() {
      
      channelId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object channelLabels_ = "";
    /**
     * <pre>
     * 渠道标签
     * </pre>
     *
     * <code>string channel_labels = 9;</code>
     * @return The channelLabels.
     */
    public java.lang.String getChannelLabels() {
      java.lang.Object ref = channelLabels_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelLabels_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道标签
     * </pre>
     *
     * <code>string channel_labels = 9;</code>
     * @return The bytes for channelLabels.
     */
    public com.google.protobuf.ByteString
        getChannelLabelsBytes() {
      java.lang.Object ref = channelLabels_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelLabels_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道标签
     * </pre>
     *
     * <code>string channel_labels = 9;</code>
     * @param value The channelLabels to set.
     * @return This builder for chaining.
     */
    public Builder setChannelLabels(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelLabels_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道标签
     * </pre>
     *
     * <code>string channel_labels = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelLabels() {
      
      channelLabels_ = getDefaultInstance().getChannelLabels();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道标签
     * </pre>
     *
     * <code>string channel_labels = 9;</code>
     * @param value The bytes for channelLabels to set.
     * @return This builder for chaining.
     */
    public Builder setChannelLabelsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelLabels_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object channelSubCategory_ = "";
    /**
     * <pre>
     * 渠道二级分类，AM 小程序&amp;会员；COUPON 卡券
     * </pre>
     *
     * <code>string channel_sub_category = 10;</code>
     * @return The channelSubCategory.
     */
    public java.lang.String getChannelSubCategory() {
      java.lang.Object ref = channelSubCategory_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelSubCategory_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道二级分类，AM 小程序&amp;会员；COUPON 卡券
     * </pre>
     *
     * <code>string channel_sub_category = 10;</code>
     * @return The bytes for channelSubCategory.
     */
    public com.google.protobuf.ByteString
        getChannelSubCategoryBytes() {
      java.lang.Object ref = channelSubCategory_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelSubCategory_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道二级分类，AM 小程序&amp;会员；COUPON 卡券
     * </pre>
     *
     * <code>string channel_sub_category = 10;</code>
     * @param value The channelSubCategory to set.
     * @return This builder for chaining.
     */
    public Builder setChannelSubCategory(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelSubCategory_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道二级分类，AM 小程序&amp;会员；COUPON 卡券
     * </pre>
     *
     * <code>string channel_sub_category = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelSubCategory() {
      
      channelSubCategory_ = getDefaultInstance().getChannelSubCategory();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道二级分类，AM 小程序&amp;会员；COUPON 卡券
     * </pre>
     *
     * <code>string channel_sub_category = 10;</code>
     * @param value The bytes for channelSubCategory to set.
     * @return This builder for chaining.
     */
    public Builder setChannelSubCategoryBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelSubCategory_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.Channel)
  }

  // @@protoc_insertion_point(class_scope:channel.Channel)
  private static final cn.hexcloud.pbis.common.service.facade.channel.Channel DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.Channel();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.Channel getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Channel>
      PARSER = new com.google.protobuf.AbstractParser<Channel>() {
    @java.lang.Override
    public Channel parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Channel(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Channel> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Channel> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.Channel getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

