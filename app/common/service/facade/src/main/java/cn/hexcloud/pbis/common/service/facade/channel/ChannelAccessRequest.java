// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道授权访问
 * </pre>
 *
 * Protobuf type {@code channel.ChannelAccessRequest}
 */
public final class ChannelAccessRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.ChannelAccessRequest)
    ChannelAccessRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ChannelAccessRequest.newBuilder() to construct.
  private ChannelAccessRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ChannelAccessRequest() {
    action_ = "";
    listAccess_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ChannelAccessRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ChannelAccessRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            action_ = s;
            break;
          }
          case 18: {
            cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.Builder subBuilder = null;
            if (listAccessQuery_ != null) {
              subBuilder = listAccessQuery_.toBuilder();
            }
            listAccessQuery_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(listAccessQuery_);
              listAccessQuery_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              listAccess_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.Access>();
              mutable_bitField0_ |= 0x00000001;
            }
            listAccess_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.Access.parser(), extensionRegistry));
            break;
          }
          case 34: {
            cn.hexcloud.pbis.common.service.facade.channel.Access.Builder subBuilder = null;
            if (editAccess_ != null) {
              subBuilder = editAccess_.toBuilder();
            }
            editAccess_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.Access.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(editAccess_);
              editAccess_ = subBuilder.buildPartial();
            }

            break;
          }
          case 42: {
            cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.Builder subBuilder = null;
            if (appletsAccessSection_ != null) {
              subBuilder = appletsAccessSection_.toBuilder();
            }
            appletsAccessSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(appletsAccessSection_);
              appletsAccessSection_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        listAccess_ = java.util.Collections.unmodifiableList(listAccess_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAccessRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAccessRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest.Builder.class);
  }

  public static final int ACTION_FIELD_NUMBER = 1;
  private volatile java.lang.Object action_;
  /**
   * <pre>
   * 业务操作
   * </pre>
   *
   * <code>string action = 1;</code>
   * @return The action.
   */
  @java.lang.Override
  public java.lang.String getAction() {
    java.lang.Object ref = action_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      action_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 业务操作
   * </pre>
   *
   * <code>string action = 1;</code>
   * @return The bytes for action.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getActionBytes() {
    java.lang.Object ref = action_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      action_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LIST_ACCESS_QUERY_FIELD_NUMBER = 2;
  private cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery listAccessQuery_;
  /**
   * <pre>
   * (可选)查询渠道列表
   * </pre>
   *
   * <code>.channel.ListAccessQuery list_access_query = 2;</code>
   * @return Whether the listAccessQuery field is set.
   */
  @java.lang.Override
  public boolean hasListAccessQuery() {
    return listAccessQuery_ != null;
  }
  /**
   * <pre>
   * (可选)查询渠道列表
   * </pre>
   *
   * <code>.channel.ListAccessQuery list_access_query = 2;</code>
   * @return The listAccessQuery.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery getListAccessQuery() {
    return listAccessQuery_ == null ? cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.getDefaultInstance() : listAccessQuery_;
  }
  /**
   * <pre>
   * (可选)查询渠道列表
   * </pre>
   *
   * <code>.channel.ListAccessQuery list_access_query = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ListAccessQueryOrBuilder getListAccessQueryOrBuilder() {
    return getListAccessQuery();
  }

  public static final int LIST_ACCESS_FIELD_NUMBER = 3;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access> listAccess_;
  /**
   * <pre>
   * 创建access
   * </pre>
   *
   * <code>repeated .channel.Access list_access = 3;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access> getListAccessList() {
    return listAccess_;
  }
  /**
   * <pre>
   * 创建access
   * </pre>
   *
   * <code>repeated .channel.Access list_access = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> 
      getListAccessOrBuilderList() {
    return listAccess_;
  }
  /**
   * <pre>
   * 创建access
   * </pre>
   *
   * <code>repeated .channel.Access list_access = 3;</code>
   */
  @java.lang.Override
  public int getListAccessCount() {
    return listAccess_.size();
  }
  /**
   * <pre>
   * 创建access
   * </pre>
   *
   * <code>repeated .channel.Access list_access = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.Access getListAccess(int index) {
    return listAccess_.get(index);
  }
  /**
   * <pre>
   * 创建access
   * </pre>
   *
   * <code>repeated .channel.Access list_access = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder getListAccessOrBuilder(
      int index) {
    return listAccess_.get(index);
  }

  public static final int EDIT_ACCESS_FIELD_NUMBER = 4;
  private cn.hexcloud.pbis.common.service.facade.channel.Access editAccess_;
  /**
   * <pre>
   * 编辑授权
   * </pre>
   *
   * <code>.channel.Access edit_access = 4;</code>
   * @return Whether the editAccess field is set.
   */
  @java.lang.Override
  public boolean hasEditAccess() {
    return editAccess_ != null;
  }
  /**
   * <pre>
   * 编辑授权
   * </pre>
   *
   * <code>.channel.Access edit_access = 4;</code>
   * @return The editAccess.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.Access getEditAccess() {
    return editAccess_ == null ? cn.hexcloud.pbis.common.service.facade.channel.Access.getDefaultInstance() : editAccess_;
  }
  /**
   * <pre>
   * 编辑授权
   * </pre>
   *
   * <code>.channel.Access edit_access = 4;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder getEditAccessOrBuilder() {
    return getEditAccess();
  }

  public static final int APPLETS_ACCESS_SECTION_FIELD_NUMBER = 5;
  private cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection appletsAccessSection_;
  /**
   * <pre>
   * 保存会员渠道授权
   * </pre>
   *
   * <code>.channel.AppletsAccessSection applets_access_section = 5;</code>
   * @return Whether the appletsAccessSection field is set.
   */
  @java.lang.Override
  public boolean hasAppletsAccessSection() {
    return appletsAccessSection_ != null;
  }
  /**
   * <pre>
   * 保存会员渠道授权
   * </pre>
   *
   * <code>.channel.AppletsAccessSection applets_access_section = 5;</code>
   * @return The appletsAccessSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection getAppletsAccessSection() {
    return appletsAccessSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.getDefaultInstance() : appletsAccessSection_;
  }
  /**
   * <pre>
   * 保存会员渠道授权
   * </pre>
   *
   * <code>.channel.AppletsAccessSection applets_access_section = 5;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSectionOrBuilder getAppletsAccessSectionOrBuilder() {
    return getAppletsAccessSection();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getActionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, action_);
    }
    if (listAccessQuery_ != null) {
      output.writeMessage(2, getListAccessQuery());
    }
    for (int i = 0; i < listAccess_.size(); i++) {
      output.writeMessage(3, listAccess_.get(i));
    }
    if (editAccess_ != null) {
      output.writeMessage(4, getEditAccess());
    }
    if (appletsAccessSection_ != null) {
      output.writeMessage(5, getAppletsAccessSection());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getActionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, action_);
    }
    if (listAccessQuery_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getListAccessQuery());
    }
    for (int i = 0; i < listAccess_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, listAccess_.get(i));
    }
    if (editAccess_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getEditAccess());
    }
    if (appletsAccessSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getAppletsAccessSection());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest other = (cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest) obj;

    if (!getAction()
        .equals(other.getAction())) return false;
    if (hasListAccessQuery() != other.hasListAccessQuery()) return false;
    if (hasListAccessQuery()) {
      if (!getListAccessQuery()
          .equals(other.getListAccessQuery())) return false;
    }
    if (!getListAccessList()
        .equals(other.getListAccessList())) return false;
    if (hasEditAccess() != other.hasEditAccess()) return false;
    if (hasEditAccess()) {
      if (!getEditAccess()
          .equals(other.getEditAccess())) return false;
    }
    if (hasAppletsAccessSection() != other.hasAppletsAccessSection()) return false;
    if (hasAppletsAccessSection()) {
      if (!getAppletsAccessSection()
          .equals(other.getAppletsAccessSection())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ACTION_FIELD_NUMBER;
    hash = (53 * hash) + getAction().hashCode();
    if (hasListAccessQuery()) {
      hash = (37 * hash) + LIST_ACCESS_QUERY_FIELD_NUMBER;
      hash = (53 * hash) + getListAccessQuery().hashCode();
    }
    if (getListAccessCount() > 0) {
      hash = (37 * hash) + LIST_ACCESS_FIELD_NUMBER;
      hash = (53 * hash) + getListAccessList().hashCode();
    }
    if (hasEditAccess()) {
      hash = (37 * hash) + EDIT_ACCESS_FIELD_NUMBER;
      hash = (53 * hash) + getEditAccess().hashCode();
    }
    if (hasAppletsAccessSection()) {
      hash = (37 * hash) + APPLETS_ACCESS_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getAppletsAccessSection().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道授权访问
   * </pre>
   *
   * Protobuf type {@code channel.ChannelAccessRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.ChannelAccessRequest)
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAccessRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAccessRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getListAccessFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      action_ = "";

      if (listAccessQueryBuilder_ == null) {
        listAccessQuery_ = null;
      } else {
        listAccessQuery_ = null;
        listAccessQueryBuilder_ = null;
      }
      if (listAccessBuilder_ == null) {
        listAccess_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        listAccessBuilder_.clear();
      }
      if (editAccessBuilder_ == null) {
        editAccess_ = null;
      } else {
        editAccess_ = null;
        editAccessBuilder_ = null;
      }
      if (appletsAccessSectionBuilder_ == null) {
        appletsAccessSection_ = null;
      } else {
        appletsAccessSection_ = null;
        appletsAccessSectionBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAccessRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest build() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest result = new cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest(this);
      int from_bitField0_ = bitField0_;
      result.action_ = action_;
      if (listAccessQueryBuilder_ == null) {
        result.listAccessQuery_ = listAccessQuery_;
      } else {
        result.listAccessQuery_ = listAccessQueryBuilder_.build();
      }
      if (listAccessBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          listAccess_ = java.util.Collections.unmodifiableList(listAccess_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.listAccess_ = listAccess_;
      } else {
        result.listAccess_ = listAccessBuilder_.build();
      }
      if (editAccessBuilder_ == null) {
        result.editAccess_ = editAccess_;
      } else {
        result.editAccess_ = editAccessBuilder_.build();
      }
      if (appletsAccessSectionBuilder_ == null) {
        result.appletsAccessSection_ = appletsAccessSection_;
      } else {
        result.appletsAccessSection_ = appletsAccessSectionBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest.getDefaultInstance()) return this;
      if (!other.getAction().isEmpty()) {
        action_ = other.action_;
        onChanged();
      }
      if (other.hasListAccessQuery()) {
        mergeListAccessQuery(other.getListAccessQuery());
      }
      if (listAccessBuilder_ == null) {
        if (!other.listAccess_.isEmpty()) {
          if (listAccess_.isEmpty()) {
            listAccess_ = other.listAccess_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureListAccessIsMutable();
            listAccess_.addAll(other.listAccess_);
          }
          onChanged();
        }
      } else {
        if (!other.listAccess_.isEmpty()) {
          if (listAccessBuilder_.isEmpty()) {
            listAccessBuilder_.dispose();
            listAccessBuilder_ = null;
            listAccess_ = other.listAccess_;
            bitField0_ = (bitField0_ & ~0x00000001);
            listAccessBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getListAccessFieldBuilder() : null;
          } else {
            listAccessBuilder_.addAllMessages(other.listAccess_);
          }
        }
      }
      if (other.hasEditAccess()) {
        mergeEditAccess(other.getEditAccess());
      }
      if (other.hasAppletsAccessSection()) {
        mergeAppletsAccessSection(other.getAppletsAccessSection());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object action_ = "";
    /**
     * <pre>
     * 业务操作
     * </pre>
     *
     * <code>string action = 1;</code>
     * @return The action.
     */
    public java.lang.String getAction() {
      java.lang.Object ref = action_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        action_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 业务操作
     * </pre>
     *
     * <code>string action = 1;</code>
     * @return The bytes for action.
     */
    public com.google.protobuf.ByteString
        getActionBytes() {
      java.lang.Object ref = action_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        action_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 业务操作
     * </pre>
     *
     * <code>string action = 1;</code>
     * @param value The action to set.
     * @return This builder for chaining.
     */
    public Builder setAction(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      action_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 业务操作
     * </pre>
     *
     * <code>string action = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearAction() {
      
      action_ = getDefaultInstance().getAction();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 业务操作
     * </pre>
     *
     * <code>string action = 1;</code>
     * @param value The bytes for action to set.
     * @return This builder for chaining.
     */
    public Builder setActionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      action_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery listAccessQuery_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery, cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.Builder, cn.hexcloud.pbis.common.service.facade.channel.ListAccessQueryOrBuilder> listAccessQueryBuilder_;
    /**
     * <pre>
     * (可选)查询渠道列表
     * </pre>
     *
     * <code>.channel.ListAccessQuery list_access_query = 2;</code>
     * @return Whether the listAccessQuery field is set.
     */
    public boolean hasListAccessQuery() {
      return listAccessQueryBuilder_ != null || listAccessQuery_ != null;
    }
    /**
     * <pre>
     * (可选)查询渠道列表
     * </pre>
     *
     * <code>.channel.ListAccessQuery list_access_query = 2;</code>
     * @return The listAccessQuery.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery getListAccessQuery() {
      if (listAccessQueryBuilder_ == null) {
        return listAccessQuery_ == null ? cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.getDefaultInstance() : listAccessQuery_;
      } else {
        return listAccessQueryBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * (可选)查询渠道列表
     * </pre>
     *
     * <code>.channel.ListAccessQuery list_access_query = 2;</code>
     */
    public Builder setListAccessQuery(cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery value) {
      if (listAccessQueryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        listAccessQuery_ = value;
        onChanged();
      } else {
        listAccessQueryBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * (可选)查询渠道列表
     * </pre>
     *
     * <code>.channel.ListAccessQuery list_access_query = 2;</code>
     */
    public Builder setListAccessQuery(
        cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.Builder builderForValue) {
      if (listAccessQueryBuilder_ == null) {
        listAccessQuery_ = builderForValue.build();
        onChanged();
      } else {
        listAccessQueryBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * (可选)查询渠道列表
     * </pre>
     *
     * <code>.channel.ListAccessQuery list_access_query = 2;</code>
     */
    public Builder mergeListAccessQuery(cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery value) {
      if (listAccessQueryBuilder_ == null) {
        if (listAccessQuery_ != null) {
          listAccessQuery_ =
            cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.newBuilder(listAccessQuery_).mergeFrom(value).buildPartial();
        } else {
          listAccessQuery_ = value;
        }
        onChanged();
      } else {
        listAccessQueryBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * (可选)查询渠道列表
     * </pre>
     *
     * <code>.channel.ListAccessQuery list_access_query = 2;</code>
     */
    public Builder clearListAccessQuery() {
      if (listAccessQueryBuilder_ == null) {
        listAccessQuery_ = null;
        onChanged();
      } else {
        listAccessQuery_ = null;
        listAccessQueryBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * (可选)查询渠道列表
     * </pre>
     *
     * <code>.channel.ListAccessQuery list_access_query = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.Builder getListAccessQueryBuilder() {
      
      onChanged();
      return getListAccessQueryFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * (可选)查询渠道列表
     * </pre>
     *
     * <code>.channel.ListAccessQuery list_access_query = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ListAccessQueryOrBuilder getListAccessQueryOrBuilder() {
      if (listAccessQueryBuilder_ != null) {
        return listAccessQueryBuilder_.getMessageOrBuilder();
      } else {
        return listAccessQuery_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.getDefaultInstance() : listAccessQuery_;
      }
    }
    /**
     * <pre>
     * (可选)查询渠道列表
     * </pre>
     *
     * <code>.channel.ListAccessQuery list_access_query = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery, cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.Builder, cn.hexcloud.pbis.common.service.facade.channel.ListAccessQueryOrBuilder> 
        getListAccessQueryFieldBuilder() {
      if (listAccessQueryBuilder_ == null) {
        listAccessQueryBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery, cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.Builder, cn.hexcloud.pbis.common.service.facade.channel.ListAccessQueryOrBuilder>(
                getListAccessQuery(),
                getParentForChildren(),
                isClean());
        listAccessQuery_ = null;
      }
      return listAccessQueryBuilder_;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access> listAccess_ =
      java.util.Collections.emptyList();
    private void ensureListAccessIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        listAccess_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.Access>(listAccess_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Access, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> listAccessBuilder_;

    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access> getListAccessList() {
      if (listAccessBuilder_ == null) {
        return java.util.Collections.unmodifiableList(listAccess_);
      } else {
        return listAccessBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public int getListAccessCount() {
      if (listAccessBuilder_ == null) {
        return listAccess_.size();
      } else {
        return listAccessBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Access getListAccess(int index) {
      if (listAccessBuilder_ == null) {
        return listAccess_.get(index);
      } else {
        return listAccessBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public Builder setListAccess(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Access value) {
      if (listAccessBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureListAccessIsMutable();
        listAccess_.set(index, value);
        onChanged();
      } else {
        listAccessBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public Builder setListAccess(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder builderForValue) {
      if (listAccessBuilder_ == null) {
        ensureListAccessIsMutable();
        listAccess_.set(index, builderForValue.build());
        onChanged();
      } else {
        listAccessBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public Builder addListAccess(cn.hexcloud.pbis.common.service.facade.channel.Access value) {
      if (listAccessBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureListAccessIsMutable();
        listAccess_.add(value);
        onChanged();
      } else {
        listAccessBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public Builder addListAccess(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Access value) {
      if (listAccessBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureListAccessIsMutable();
        listAccess_.add(index, value);
        onChanged();
      } else {
        listAccessBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public Builder addListAccess(
        cn.hexcloud.pbis.common.service.facade.channel.Access.Builder builderForValue) {
      if (listAccessBuilder_ == null) {
        ensureListAccessIsMutable();
        listAccess_.add(builderForValue.build());
        onChanged();
      } else {
        listAccessBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public Builder addListAccess(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder builderForValue) {
      if (listAccessBuilder_ == null) {
        ensureListAccessIsMutable();
        listAccess_.add(index, builderForValue.build());
        onChanged();
      } else {
        listAccessBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public Builder addAllListAccess(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.channel.Access> values) {
      if (listAccessBuilder_ == null) {
        ensureListAccessIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, listAccess_);
        onChanged();
      } else {
        listAccessBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public Builder clearListAccess() {
      if (listAccessBuilder_ == null) {
        listAccess_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        listAccessBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public Builder removeListAccess(int index) {
      if (listAccessBuilder_ == null) {
        ensureListAccessIsMutable();
        listAccess_.remove(index);
        onChanged();
      } else {
        listAccessBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Access.Builder getListAccessBuilder(
        int index) {
      return getListAccessFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder getListAccessOrBuilder(
        int index) {
      if (listAccessBuilder_ == null) {
        return listAccess_.get(index);  } else {
        return listAccessBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> 
         getListAccessOrBuilderList() {
      if (listAccessBuilder_ != null) {
        return listAccessBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(listAccess_);
      }
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Access.Builder addListAccessBuilder() {
      return getListAccessFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.channel.Access.getDefaultInstance());
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Access.Builder addListAccessBuilder(
        int index) {
      return getListAccessFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.channel.Access.getDefaultInstance());
    }
    /**
     * <pre>
     * 创建access
     * </pre>
     *
     * <code>repeated .channel.Access list_access = 3;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access.Builder> 
         getListAccessBuilderList() {
      return getListAccessFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Access, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> 
        getListAccessFieldBuilder() {
      if (listAccessBuilder_ == null) {
        listAccessBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.Access, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder>(
                listAccess_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        listAccess_ = null;
      }
      return listAccessBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.Access editAccess_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Access, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> editAccessBuilder_;
    /**
     * <pre>
     * 编辑授权
     * </pre>
     *
     * <code>.channel.Access edit_access = 4;</code>
     * @return Whether the editAccess field is set.
     */
    public boolean hasEditAccess() {
      return editAccessBuilder_ != null || editAccess_ != null;
    }
    /**
     * <pre>
     * 编辑授权
     * </pre>
     *
     * <code>.channel.Access edit_access = 4;</code>
     * @return The editAccess.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Access getEditAccess() {
      if (editAccessBuilder_ == null) {
        return editAccess_ == null ? cn.hexcloud.pbis.common.service.facade.channel.Access.getDefaultInstance() : editAccess_;
      } else {
        return editAccessBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 编辑授权
     * </pre>
     *
     * <code>.channel.Access edit_access = 4;</code>
     */
    public Builder setEditAccess(cn.hexcloud.pbis.common.service.facade.channel.Access value) {
      if (editAccessBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        editAccess_ = value;
        onChanged();
      } else {
        editAccessBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 编辑授权
     * </pre>
     *
     * <code>.channel.Access edit_access = 4;</code>
     */
    public Builder setEditAccess(
        cn.hexcloud.pbis.common.service.facade.channel.Access.Builder builderForValue) {
      if (editAccessBuilder_ == null) {
        editAccess_ = builderForValue.build();
        onChanged();
      } else {
        editAccessBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 编辑授权
     * </pre>
     *
     * <code>.channel.Access edit_access = 4;</code>
     */
    public Builder mergeEditAccess(cn.hexcloud.pbis.common.service.facade.channel.Access value) {
      if (editAccessBuilder_ == null) {
        if (editAccess_ != null) {
          editAccess_ =
            cn.hexcloud.pbis.common.service.facade.channel.Access.newBuilder(editAccess_).mergeFrom(value).buildPartial();
        } else {
          editAccess_ = value;
        }
        onChanged();
      } else {
        editAccessBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 编辑授权
     * </pre>
     *
     * <code>.channel.Access edit_access = 4;</code>
     */
    public Builder clearEditAccess() {
      if (editAccessBuilder_ == null) {
        editAccess_ = null;
        onChanged();
      } else {
        editAccess_ = null;
        editAccessBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 编辑授权
     * </pre>
     *
     * <code>.channel.Access edit_access = 4;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Access.Builder getEditAccessBuilder() {
      
      onChanged();
      return getEditAccessFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 编辑授权
     * </pre>
     *
     * <code>.channel.Access edit_access = 4;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder getEditAccessOrBuilder() {
      if (editAccessBuilder_ != null) {
        return editAccessBuilder_.getMessageOrBuilder();
      } else {
        return editAccess_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.Access.getDefaultInstance() : editAccess_;
      }
    }
    /**
     * <pre>
     * 编辑授权
     * </pre>
     *
     * <code>.channel.Access edit_access = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Access, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> 
        getEditAccessFieldBuilder() {
      if (editAccessBuilder_ == null) {
        editAccessBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.Access, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder>(
                getEditAccess(),
                getParentForChildren(),
                isClean());
        editAccess_ = null;
      }
      return editAccessBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection appletsAccessSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSectionOrBuilder> appletsAccessSectionBuilder_;
    /**
     * <pre>
     * 保存会员渠道授权
     * </pre>
     *
     * <code>.channel.AppletsAccessSection applets_access_section = 5;</code>
     * @return Whether the appletsAccessSection field is set.
     */
    public boolean hasAppletsAccessSection() {
      return appletsAccessSectionBuilder_ != null || appletsAccessSection_ != null;
    }
    /**
     * <pre>
     * 保存会员渠道授权
     * </pre>
     *
     * <code>.channel.AppletsAccessSection applets_access_section = 5;</code>
     * @return The appletsAccessSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection getAppletsAccessSection() {
      if (appletsAccessSectionBuilder_ == null) {
        return appletsAccessSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.getDefaultInstance() : appletsAccessSection_;
      } else {
        return appletsAccessSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 保存会员渠道授权
     * </pre>
     *
     * <code>.channel.AppletsAccessSection applets_access_section = 5;</code>
     */
    public Builder setAppletsAccessSection(cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection value) {
      if (appletsAccessSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        appletsAccessSection_ = value;
        onChanged();
      } else {
        appletsAccessSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 保存会员渠道授权
     * </pre>
     *
     * <code>.channel.AppletsAccessSection applets_access_section = 5;</code>
     */
    public Builder setAppletsAccessSection(
        cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.Builder builderForValue) {
      if (appletsAccessSectionBuilder_ == null) {
        appletsAccessSection_ = builderForValue.build();
        onChanged();
      } else {
        appletsAccessSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 保存会员渠道授权
     * </pre>
     *
     * <code>.channel.AppletsAccessSection applets_access_section = 5;</code>
     */
    public Builder mergeAppletsAccessSection(cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection value) {
      if (appletsAccessSectionBuilder_ == null) {
        if (appletsAccessSection_ != null) {
          appletsAccessSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.newBuilder(appletsAccessSection_).mergeFrom(value).buildPartial();
        } else {
          appletsAccessSection_ = value;
        }
        onChanged();
      } else {
        appletsAccessSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 保存会员渠道授权
     * </pre>
     *
     * <code>.channel.AppletsAccessSection applets_access_section = 5;</code>
     */
    public Builder clearAppletsAccessSection() {
      if (appletsAccessSectionBuilder_ == null) {
        appletsAccessSection_ = null;
        onChanged();
      } else {
        appletsAccessSection_ = null;
        appletsAccessSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 保存会员渠道授权
     * </pre>
     *
     * <code>.channel.AppletsAccessSection applets_access_section = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.Builder getAppletsAccessSectionBuilder() {
      
      onChanged();
      return getAppletsAccessSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 保存会员渠道授权
     * </pre>
     *
     * <code>.channel.AppletsAccessSection applets_access_section = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSectionOrBuilder getAppletsAccessSectionOrBuilder() {
      if (appletsAccessSectionBuilder_ != null) {
        return appletsAccessSectionBuilder_.getMessageOrBuilder();
      } else {
        return appletsAccessSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.getDefaultInstance() : appletsAccessSection_;
      }
    }
    /**
     * <pre>
     * 保存会员渠道授权
     * </pre>
     *
     * <code>.channel.AppletsAccessSection applets_access_section = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSectionOrBuilder> 
        getAppletsAccessSectionFieldBuilder() {
      if (appletsAccessSectionBuilder_ == null) {
        appletsAccessSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSectionOrBuilder>(
                getAppletsAccessSection(),
                getParentForChildren(),
                isClean());
        appletsAccessSection_ = null;
      }
      return appletsAccessSectionBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.ChannelAccessRequest)
  }

  // @@protoc_insertion_point(class_scope:channel.ChannelAccessRequest)
  private static final cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ChannelAccessRequest>
      PARSER = new com.google.protobuf.AbstractParser<ChannelAccessRequest>() {
    @java.lang.Override
    public ChannelAccessRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ChannelAccessRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ChannelAccessRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ChannelAccessRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

