// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ChannelAccessRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ChannelAccessRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 业务操作
   * </pre>
   *
   * <code>string action = 1;</code>
   * @return The action.
   */
  java.lang.String getAction();
  /**
   * <pre>
   * 业务操作
   * </pre>
   *
   * <code>string action = 1;</code>
   * @return The bytes for action.
   */
  com.google.protobuf.ByteString
      getActionBytes();

  /**
   * <pre>
   * (可选)查询渠道列表
   * </pre>
   *
   * <code>.channel.ListAccessQuery list_access_query = 2;</code>
   * @return Whether the listAccessQuery field is set.
   */
  boolean hasListAccessQuery();
  /**
   * <pre>
   * (可选)查询渠道列表
   * </pre>
   *
   * <code>.channel.ListAccessQuery list_access_query = 2;</code>
   * @return The listAccessQuery.
   */
  cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery getListAccessQuery();
  /**
   * <pre>
   * (可选)查询渠道列表
   * </pre>
   *
   * <code>.channel.ListAccessQuery list_access_query = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.ListAccessQueryOrBuilder getListAccessQueryOrBuilder();

  /**
   * <pre>
   * 创建access
   * </pre>
   *
   * <code>repeated .channel.Access list_access = 3;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access> 
      getListAccessList();
  /**
   * <pre>
   * 创建access
   * </pre>
   *
   * <code>repeated .channel.Access list_access = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.Access getListAccess(int index);
  /**
   * <pre>
   * 创建access
   * </pre>
   *
   * <code>repeated .channel.Access list_access = 3;</code>
   */
  int getListAccessCount();
  /**
   * <pre>
   * 创建access
   * </pre>
   *
   * <code>repeated .channel.Access list_access = 3;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> 
      getListAccessOrBuilderList();
  /**
   * <pre>
   * 创建access
   * </pre>
   *
   * <code>repeated .channel.Access list_access = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder getListAccessOrBuilder(
      int index);

  /**
   * <pre>
   * 编辑授权
   * </pre>
   *
   * <code>.channel.Access edit_access = 4;</code>
   * @return Whether the editAccess field is set.
   */
  boolean hasEditAccess();
  /**
   * <pre>
   * 编辑授权
   * </pre>
   *
   * <code>.channel.Access edit_access = 4;</code>
   * @return The editAccess.
   */
  cn.hexcloud.pbis.common.service.facade.channel.Access getEditAccess();
  /**
   * <pre>
   * 编辑授权
   * </pre>
   *
   * <code>.channel.Access edit_access = 4;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder getEditAccessOrBuilder();

  /**
   * <pre>
   * 保存会员渠道授权
   * </pre>
   *
   * <code>.channel.AppletsAccessSection applets_access_section = 5;</code>
   * @return Whether the appletsAccessSection field is set.
   */
  boolean hasAppletsAccessSection();
  /**
   * <pre>
   * 保存会员渠道授权
   * </pre>
   *
   * <code>.channel.AppletsAccessSection applets_access_section = 5;</code>
   * @return The appletsAccessSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSection getAppletsAccessSection();
  /**
   * <pre>
   * 保存会员渠道授权
   * </pre>
   *
   * <code>.channel.AppletsAccessSection applets_access_section = 5;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessSectionOrBuilder getAppletsAccessSectionOrBuilder();
}
