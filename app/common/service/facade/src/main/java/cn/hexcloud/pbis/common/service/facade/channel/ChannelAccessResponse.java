// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道管理接口响应
 * </pre>
 *
 * Protobuf type {@code channel.ChannelAccessResponse}
 */
public final class ChannelAccessResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.ChannelAccessResponse)
    ChannelAccessResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ChannelAccessResponse.newBuilder() to construct.
  private ChannelAccessResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ChannelAccessResponse() {
    errorCode_ = "";
    errorMessage_ = "";
    accessItem_ = java.util.Collections.emptyList();
    appletsAccessItem_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ChannelAccessResponse();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ChannelAccessResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            errorCode_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            errorMessage_ = s;
            break;
          }
          case 26: {
            cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection.Builder subBuilder = null;
            if (accessReportSection_ != null) {
              subBuilder = accessReportSection_.toBuilder();
            }
            accessReportSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(accessReportSection_);
              accessReportSection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 34: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              accessItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.Access>();
              mutable_bitField0_ |= 0x00000001;
            }
            accessItem_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.Access.parser(), extensionRegistry));
            break;
          }
          case 42: {
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              appletsAccessItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem>();
              mutable_bitField0_ |= 0x00000002;
            }
            appletsAccessItem_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        accessItem_ = java.util.Collections.unmodifiableList(accessItem_);
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        appletsAccessItem_ = java.util.Collections.unmodifiableList(appletsAccessItem_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAccessResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAccessResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse.Builder.class);
  }

  public static final int ERROR_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object errorCode_;
  /**
   * <pre>
   *（必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  @java.lang.Override
  public java.lang.String getErrorCode() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *（必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorCodeBytes() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ERROR_MESSAGE_FIELD_NUMBER = 2;
  private volatile java.lang.Object errorMessage_;
  /**
   * <pre>
   *（必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  @java.lang.Override
  public java.lang.String getErrorMessage() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorMessage_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *（必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorMessageBytes() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorMessage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACCESS_REPORT_SECTION_FIELD_NUMBER = 3;
  private cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection accessReportSection_;
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>.channel.AccessReportSection access_report_section = 3;</code>
   * @return Whether the accessReportSection field is set.
   */
  @java.lang.Override
  public boolean hasAccessReportSection() {
    return accessReportSection_ != null;
  }
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>.channel.AccessReportSection access_report_section = 3;</code>
   * @return The accessReportSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection getAccessReportSection() {
    return accessReportSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection.getDefaultInstance() : accessReportSection_;
  }
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>.channel.AccessReportSection access_report_section = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AccessReportSectionOrBuilder getAccessReportSectionOrBuilder() {
    return getAccessReportSection();
  }

  public static final int ACCESS_ITEM_FIELD_NUMBER = 4;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access> accessItem_;
  /**
   * <pre>
   * 查询/门店/公司/全局访问授权配置
   * </pre>
   *
   * <code>repeated .channel.Access access_item = 4;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access> getAccessItemList() {
    return accessItem_;
  }
  /**
   * <pre>
   * 查询/门店/公司/全局访问授权配置
   * </pre>
   *
   * <code>repeated .channel.Access access_item = 4;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> 
      getAccessItemOrBuilderList() {
    return accessItem_;
  }
  /**
   * <pre>
   * 查询/门店/公司/全局访问授权配置
   * </pre>
   *
   * <code>repeated .channel.Access access_item = 4;</code>
   */
  @java.lang.Override
  public int getAccessItemCount() {
    return accessItem_.size();
  }
  /**
   * <pre>
   * 查询/门店/公司/全局访问授权配置
   * </pre>
   *
   * <code>repeated .channel.Access access_item = 4;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.Access getAccessItem(int index) {
    return accessItem_.get(index);
  }
  /**
   * <pre>
   * 查询/门店/公司/全局访问授权配置
   * </pre>
   *
   * <code>repeated .channel.Access access_item = 4;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder getAccessItemOrBuilder(
      int index) {
    return accessItem_.get(index);
  }

  public static final int APPLETS_ACCESS_ITEM_FIELD_NUMBER = 5;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem> appletsAccessItem_;
  /**
   * <pre>
   * 带分组结构的AccessList
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem> getAppletsAccessItemList() {
    return appletsAccessItem_;
  }
  /**
   * <pre>
   * 带分组结构的AccessList
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder> 
      getAppletsAccessItemOrBuilderList() {
    return appletsAccessItem_;
  }
  /**
   * <pre>
   * 带分组结构的AccessList
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
   */
  @java.lang.Override
  public int getAppletsAccessItemCount() {
    return appletsAccessItem_.size();
  }
  /**
   * <pre>
   * 带分组结构的AccessList
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem getAppletsAccessItem(int index) {
    return appletsAccessItem_.get(index);
  }
  /**
   * <pre>
   * 带分组结构的AccessList
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder getAppletsAccessItemOrBuilder(
      int index) {
    return appletsAccessItem_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getErrorCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, errorMessage_);
    }
    if (accessReportSection_ != null) {
      output.writeMessage(3, getAccessReportSection());
    }
    for (int i = 0; i < accessItem_.size(); i++) {
      output.writeMessage(4, accessItem_.get(i));
    }
    for (int i = 0; i < appletsAccessItem_.size(); i++) {
      output.writeMessage(5, appletsAccessItem_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getErrorCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, errorMessage_);
    }
    if (accessReportSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getAccessReportSection());
    }
    for (int i = 0; i < accessItem_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, accessItem_.get(i));
    }
    for (int i = 0; i < appletsAccessItem_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, appletsAccessItem_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse other = (cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse) obj;

    if (!getErrorCode()
        .equals(other.getErrorCode())) return false;
    if (!getErrorMessage()
        .equals(other.getErrorMessage())) return false;
    if (hasAccessReportSection() != other.hasAccessReportSection()) return false;
    if (hasAccessReportSection()) {
      if (!getAccessReportSection()
          .equals(other.getAccessReportSection())) return false;
    }
    if (!getAccessItemList()
        .equals(other.getAccessItemList())) return false;
    if (!getAppletsAccessItemList()
        .equals(other.getAppletsAccessItemList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ERROR_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorCode().hashCode();
    hash = (37 * hash) + ERROR_MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorMessage().hashCode();
    if (hasAccessReportSection()) {
      hash = (37 * hash) + ACCESS_REPORT_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getAccessReportSection().hashCode();
    }
    if (getAccessItemCount() > 0) {
      hash = (37 * hash) + ACCESS_ITEM_FIELD_NUMBER;
      hash = (53 * hash) + getAccessItemList().hashCode();
    }
    if (getAppletsAccessItemCount() > 0) {
      hash = (37 * hash) + APPLETS_ACCESS_ITEM_FIELD_NUMBER;
      hash = (53 * hash) + getAppletsAccessItemList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道管理接口响应
   * </pre>
   *
   * Protobuf type {@code channel.ChannelAccessResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.ChannelAccessResponse)
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAccessResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAccessResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getAccessItemFieldBuilder();
        getAppletsAccessItemFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      errorCode_ = "";

      errorMessage_ = "";

      if (accessReportSectionBuilder_ == null) {
        accessReportSection_ = null;
      } else {
        accessReportSection_ = null;
        accessReportSectionBuilder_ = null;
      }
      if (accessItemBuilder_ == null) {
        accessItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        accessItemBuilder_.clear();
      }
      if (appletsAccessItemBuilder_ == null) {
        appletsAccessItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
      } else {
        appletsAccessItemBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAccessResponse_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse build() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse result = new cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse(this);
      int from_bitField0_ = bitField0_;
      result.errorCode_ = errorCode_;
      result.errorMessage_ = errorMessage_;
      if (accessReportSectionBuilder_ == null) {
        result.accessReportSection_ = accessReportSection_;
      } else {
        result.accessReportSection_ = accessReportSectionBuilder_.build();
      }
      if (accessItemBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          accessItem_ = java.util.Collections.unmodifiableList(accessItem_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.accessItem_ = accessItem_;
      } else {
        result.accessItem_ = accessItemBuilder_.build();
      }
      if (appletsAccessItemBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          appletsAccessItem_ = java.util.Collections.unmodifiableList(appletsAccessItem_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.appletsAccessItem_ = appletsAccessItem_;
      } else {
        result.appletsAccessItem_ = appletsAccessItemBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse.getDefaultInstance()) return this;
      if (!other.getErrorCode().isEmpty()) {
        errorCode_ = other.errorCode_;
        onChanged();
      }
      if (!other.getErrorMessage().isEmpty()) {
        errorMessage_ = other.errorMessage_;
        onChanged();
      }
      if (other.hasAccessReportSection()) {
        mergeAccessReportSection(other.getAccessReportSection());
      }
      if (accessItemBuilder_ == null) {
        if (!other.accessItem_.isEmpty()) {
          if (accessItem_.isEmpty()) {
            accessItem_ = other.accessItem_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureAccessItemIsMutable();
            accessItem_.addAll(other.accessItem_);
          }
          onChanged();
        }
      } else {
        if (!other.accessItem_.isEmpty()) {
          if (accessItemBuilder_.isEmpty()) {
            accessItemBuilder_.dispose();
            accessItemBuilder_ = null;
            accessItem_ = other.accessItem_;
            bitField0_ = (bitField0_ & ~0x00000001);
            accessItemBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getAccessItemFieldBuilder() : null;
          } else {
            accessItemBuilder_.addAllMessages(other.accessItem_);
          }
        }
      }
      if (appletsAccessItemBuilder_ == null) {
        if (!other.appletsAccessItem_.isEmpty()) {
          if (appletsAccessItem_.isEmpty()) {
            appletsAccessItem_ = other.appletsAccessItem_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureAppletsAccessItemIsMutable();
            appletsAccessItem_.addAll(other.appletsAccessItem_);
          }
          onChanged();
        }
      } else {
        if (!other.appletsAccessItem_.isEmpty()) {
          if (appletsAccessItemBuilder_.isEmpty()) {
            appletsAccessItemBuilder_.dispose();
            appletsAccessItemBuilder_ = null;
            appletsAccessItem_ = other.appletsAccessItem_;
            bitField0_ = (bitField0_ & ~0x00000002);
            appletsAccessItemBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getAppletsAccessItemFieldBuilder() : null;
          } else {
            appletsAccessItemBuilder_.addAllMessages(other.appletsAccessItem_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object errorCode_ = "";
    /**
     * <pre>
     *（必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return The errorCode.
     */
    public java.lang.String getErrorCode() {
      java.lang.Object ref = errorCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *（必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return The bytes for errorCode.
     */
    public com.google.protobuf.ByteString
        getErrorCodeBytes() {
      java.lang.Object ref = errorCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *（必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @param value The errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorCode() {
      
      errorCode_ = getDefaultInstance().getErrorCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @param value The bytes for errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object errorMessage_ = "";
    /**
     * <pre>
     *（必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return The errorMessage.
     */
    public java.lang.String getErrorMessage() {
      java.lang.Object ref = errorMessage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorMessage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *（必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return The bytes for errorMessage.
     */
    public com.google.protobuf.ByteString
        getErrorMessageBytes() {
      java.lang.Object ref = errorMessage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorMessage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *（必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @param value The errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessage(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorMessage_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorMessage() {
      
      errorMessage_ = getDefaultInstance().getErrorMessage();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @param value The bytes for errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorMessage_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection accessReportSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection, cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessReportSectionOrBuilder> accessReportSectionBuilder_;
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>.channel.AccessReportSection access_report_section = 3;</code>
     * @return Whether the accessReportSection field is set.
     */
    public boolean hasAccessReportSection() {
      return accessReportSectionBuilder_ != null || accessReportSection_ != null;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>.channel.AccessReportSection access_report_section = 3;</code>
     * @return The accessReportSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection getAccessReportSection() {
      if (accessReportSectionBuilder_ == null) {
        return accessReportSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection.getDefaultInstance() : accessReportSection_;
      } else {
        return accessReportSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>.channel.AccessReportSection access_report_section = 3;</code>
     */
    public Builder setAccessReportSection(cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection value) {
      if (accessReportSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        accessReportSection_ = value;
        onChanged();
      } else {
        accessReportSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>.channel.AccessReportSection access_report_section = 3;</code>
     */
    public Builder setAccessReportSection(
        cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection.Builder builderForValue) {
      if (accessReportSectionBuilder_ == null) {
        accessReportSection_ = builderForValue.build();
        onChanged();
      } else {
        accessReportSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>.channel.AccessReportSection access_report_section = 3;</code>
     */
    public Builder mergeAccessReportSection(cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection value) {
      if (accessReportSectionBuilder_ == null) {
        if (accessReportSection_ != null) {
          accessReportSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection.newBuilder(accessReportSection_).mergeFrom(value).buildPartial();
        } else {
          accessReportSection_ = value;
        }
        onChanged();
      } else {
        accessReportSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>.channel.AccessReportSection access_report_section = 3;</code>
     */
    public Builder clearAccessReportSection() {
      if (accessReportSectionBuilder_ == null) {
        accessReportSection_ = null;
        onChanged();
      } else {
        accessReportSection_ = null;
        accessReportSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>.channel.AccessReportSection access_report_section = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection.Builder getAccessReportSectionBuilder() {
      
      onChanged();
      return getAccessReportSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>.channel.AccessReportSection access_report_section = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AccessReportSectionOrBuilder getAccessReportSectionOrBuilder() {
      if (accessReportSectionBuilder_ != null) {
        return accessReportSectionBuilder_.getMessageOrBuilder();
      } else {
        return accessReportSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection.getDefaultInstance() : accessReportSection_;
      }
    }
    /**
     * <pre>
     * 授权列表
     * </pre>
     *
     * <code>.channel.AccessReportSection access_report_section = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection, cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessReportSectionOrBuilder> 
        getAccessReportSectionFieldBuilder() {
      if (accessReportSectionBuilder_ == null) {
        accessReportSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection, cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessReportSectionOrBuilder>(
                getAccessReportSection(),
                getParentForChildren(),
                isClean());
        accessReportSection_ = null;
      }
      return accessReportSectionBuilder_;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access> accessItem_ =
      java.util.Collections.emptyList();
    private void ensureAccessItemIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        accessItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.Access>(accessItem_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Access, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> accessItemBuilder_;

    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access> getAccessItemList() {
      if (accessItemBuilder_ == null) {
        return java.util.Collections.unmodifiableList(accessItem_);
      } else {
        return accessItemBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public int getAccessItemCount() {
      if (accessItemBuilder_ == null) {
        return accessItem_.size();
      } else {
        return accessItemBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Access getAccessItem(int index) {
      if (accessItemBuilder_ == null) {
        return accessItem_.get(index);
      } else {
        return accessItemBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public Builder setAccessItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Access value) {
      if (accessItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAccessItemIsMutable();
        accessItem_.set(index, value);
        onChanged();
      } else {
        accessItemBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public Builder setAccessItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder builderForValue) {
      if (accessItemBuilder_ == null) {
        ensureAccessItemIsMutable();
        accessItem_.set(index, builderForValue.build());
        onChanged();
      } else {
        accessItemBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public Builder addAccessItem(cn.hexcloud.pbis.common.service.facade.channel.Access value) {
      if (accessItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAccessItemIsMutable();
        accessItem_.add(value);
        onChanged();
      } else {
        accessItemBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public Builder addAccessItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Access value) {
      if (accessItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAccessItemIsMutable();
        accessItem_.add(index, value);
        onChanged();
      } else {
        accessItemBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public Builder addAccessItem(
        cn.hexcloud.pbis.common.service.facade.channel.Access.Builder builderForValue) {
      if (accessItemBuilder_ == null) {
        ensureAccessItemIsMutable();
        accessItem_.add(builderForValue.build());
        onChanged();
      } else {
        accessItemBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public Builder addAccessItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder builderForValue) {
      if (accessItemBuilder_ == null) {
        ensureAccessItemIsMutable();
        accessItem_.add(index, builderForValue.build());
        onChanged();
      } else {
        accessItemBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public Builder addAllAccessItem(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.channel.Access> values) {
      if (accessItemBuilder_ == null) {
        ensureAccessItemIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, accessItem_);
        onChanged();
      } else {
        accessItemBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public Builder clearAccessItem() {
      if (accessItemBuilder_ == null) {
        accessItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        accessItemBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public Builder removeAccessItem(int index) {
      if (accessItemBuilder_ == null) {
        ensureAccessItemIsMutable();
        accessItem_.remove(index);
        onChanged();
      } else {
        accessItemBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Access.Builder getAccessItemBuilder(
        int index) {
      return getAccessItemFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder getAccessItemOrBuilder(
        int index) {
      if (accessItemBuilder_ == null) {
        return accessItem_.get(index);  } else {
        return accessItemBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> 
         getAccessItemOrBuilderList() {
      if (accessItemBuilder_ != null) {
        return accessItemBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(accessItem_);
      }
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Access.Builder addAccessItemBuilder() {
      return getAccessItemFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.channel.Access.getDefaultInstance());
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Access.Builder addAccessItemBuilder(
        int index) {
      return getAccessItemFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.channel.Access.getDefaultInstance());
    }
    /**
     * <pre>
     * 查询/门店/公司/全局访问授权配置
     * </pre>
     *
     * <code>repeated .channel.Access access_item = 4;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access.Builder> 
         getAccessItemBuilderList() {
      return getAccessItemFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Access, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> 
        getAccessItemFieldBuilder() {
      if (accessItemBuilder_ == null) {
        accessItemBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.Access, cn.hexcloud.pbis.common.service.facade.channel.Access.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder>(
                accessItem_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        accessItem_ = null;
      }
      return accessItemBuilder_;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem> appletsAccessItem_ =
      java.util.Collections.emptyList();
    private void ensureAppletsAccessItemIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        appletsAccessItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem>(appletsAccessItem_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder> appletsAccessItemBuilder_;

    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem> getAppletsAccessItemList() {
      if (appletsAccessItemBuilder_ == null) {
        return java.util.Collections.unmodifiableList(appletsAccessItem_);
      } else {
        return appletsAccessItemBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public int getAppletsAccessItemCount() {
      if (appletsAccessItemBuilder_ == null) {
        return appletsAccessItem_.size();
      } else {
        return appletsAccessItemBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem getAppletsAccessItem(int index) {
      if (appletsAccessItemBuilder_ == null) {
        return appletsAccessItem_.get(index);
      } else {
        return appletsAccessItemBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public Builder setAppletsAccessItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem value) {
      if (appletsAccessItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAppletsAccessItemIsMutable();
        appletsAccessItem_.set(index, value);
        onChanged();
      } else {
        appletsAccessItemBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public Builder setAppletsAccessItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder builderForValue) {
      if (appletsAccessItemBuilder_ == null) {
        ensureAppletsAccessItemIsMutable();
        appletsAccessItem_.set(index, builderForValue.build());
        onChanged();
      } else {
        appletsAccessItemBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public Builder addAppletsAccessItem(cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem value) {
      if (appletsAccessItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAppletsAccessItemIsMutable();
        appletsAccessItem_.add(value);
        onChanged();
      } else {
        appletsAccessItemBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public Builder addAppletsAccessItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem value) {
      if (appletsAccessItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAppletsAccessItemIsMutable();
        appletsAccessItem_.add(index, value);
        onChanged();
      } else {
        appletsAccessItemBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public Builder addAppletsAccessItem(
        cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder builderForValue) {
      if (appletsAccessItemBuilder_ == null) {
        ensureAppletsAccessItemIsMutable();
        appletsAccessItem_.add(builderForValue.build());
        onChanged();
      } else {
        appletsAccessItemBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public Builder addAppletsAccessItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder builderForValue) {
      if (appletsAccessItemBuilder_ == null) {
        ensureAppletsAccessItemIsMutable();
        appletsAccessItem_.add(index, builderForValue.build());
        onChanged();
      } else {
        appletsAccessItemBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public Builder addAllAppletsAccessItem(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem> values) {
      if (appletsAccessItemBuilder_ == null) {
        ensureAppletsAccessItemIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, appletsAccessItem_);
        onChanged();
      } else {
        appletsAccessItemBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public Builder clearAppletsAccessItem() {
      if (appletsAccessItemBuilder_ == null) {
        appletsAccessItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        appletsAccessItemBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public Builder removeAppletsAccessItem(int index) {
      if (appletsAccessItemBuilder_ == null) {
        ensureAppletsAccessItemIsMutable();
        appletsAccessItem_.remove(index);
        onChanged();
      } else {
        appletsAccessItemBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder getAppletsAccessItemBuilder(
        int index) {
      return getAppletsAccessItemFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder getAppletsAccessItemOrBuilder(
        int index) {
      if (appletsAccessItemBuilder_ == null) {
        return appletsAccessItem_.get(index);  } else {
        return appletsAccessItemBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder> 
         getAppletsAccessItemOrBuilderList() {
      if (appletsAccessItemBuilder_ != null) {
        return appletsAccessItemBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(appletsAccessItem_);
      }
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder addAppletsAccessItemBuilder() {
      return getAppletsAccessItemFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.getDefaultInstance());
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder addAppletsAccessItemBuilder(
        int index) {
      return getAppletsAccessItemFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.getDefaultInstance());
    }
    /**
     * <pre>
     * 带分组结构的AccessList
     * </pre>
     *
     * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder> 
         getAppletsAccessItemBuilderList() {
      return getAppletsAccessItemFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder> 
        getAppletsAccessItemFieldBuilder() {
      if (appletsAccessItemBuilder_ == null) {
        appletsAccessItemBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder>(
                appletsAccessItem_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        appletsAccessItem_ = null;
      }
      return appletsAccessItemBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.ChannelAccessResponse)
  }

  // @@protoc_insertion_point(class_scope:channel.ChannelAccessResponse)
  private static final cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ChannelAccessResponse>
      PARSER = new com.google.protobuf.AbstractParser<ChannelAccessResponse>() {
    @java.lang.Override
    public ChannelAccessResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ChannelAccessResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ChannelAccessResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ChannelAccessResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

