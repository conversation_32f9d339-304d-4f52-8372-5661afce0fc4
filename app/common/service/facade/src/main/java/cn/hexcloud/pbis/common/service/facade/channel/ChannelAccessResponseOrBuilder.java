// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ChannelAccessResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ChannelAccessResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *（必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  java.lang.String getErrorCode();
  /**
   * <pre>
   *（必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  com.google.protobuf.ByteString
      getErrorCodeBytes();

  /**
   * <pre>
   *（必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  java.lang.String getErrorMessage();
  /**
   * <pre>
   *（必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  com.google.protobuf.ByteString
      getErrorMessageBytes();

  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>.channel.AccessReportSection access_report_section = 3;</code>
   * @return Whether the accessReportSection field is set.
   */
  boolean hasAccessReportSection();
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>.channel.AccessReportSection access_report_section = 3;</code>
   * @return The accessReportSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.AccessReportSection getAccessReportSection();
  /**
   * <pre>
   * 授权列表
   * </pre>
   *
   * <code>.channel.AccessReportSection access_report_section = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.AccessReportSectionOrBuilder getAccessReportSectionOrBuilder();

  /**
   * <pre>
   * 查询/门店/公司/全局访问授权配置
   * </pre>
   *
   * <code>repeated .channel.Access access_item = 4;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Access> 
      getAccessItemList();
  /**
   * <pre>
   * 查询/门店/公司/全局访问授权配置
   * </pre>
   *
   * <code>repeated .channel.Access access_item = 4;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.Access getAccessItem(int index);
  /**
   * <pre>
   * 查询/门店/公司/全局访问授权配置
   * </pre>
   *
   * <code>repeated .channel.Access access_item = 4;</code>
   */
  int getAccessItemCount();
  /**
   * <pre>
   * 查询/门店/公司/全局访问授权配置
   * </pre>
   *
   * <code>repeated .channel.Access access_item = 4;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder> 
      getAccessItemOrBuilderList();
  /**
   * <pre>
   * 查询/门店/公司/全局访问授权配置
   * </pre>
   *
   * <code>repeated .channel.Access access_item = 4;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.AccessOrBuilder getAccessItemOrBuilder(
      int index);

  /**
   * <pre>
   * 带分组结构的AccessList
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem> 
      getAppletsAccessItemList();
  /**
   * <pre>
   * 带分组结构的AccessList
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItem getAppletsAccessItem(int index);
  /**
   * <pre>
   * 带分组结构的AccessList
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
   */
  int getAppletsAccessItemCount();
  /**
   * <pre>
   * 带分组结构的AccessList
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder> 
      getAppletsAccessItemOrBuilderList();
  /**
   * <pre>
   * 带分组结构的AccessList
   * </pre>
   *
   * <code>repeated .channel.AppletsAccessItem applets_access_item = 5;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.AppletsAccessItemOrBuilder getAppletsAccessItemOrBuilder(
      int index);
}
