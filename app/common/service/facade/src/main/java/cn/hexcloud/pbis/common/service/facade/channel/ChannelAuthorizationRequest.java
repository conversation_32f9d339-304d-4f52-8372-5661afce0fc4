// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道签约授权请求信息
 * </pre>
 *
 * Protobuf type {@code channel.ChannelAuthorizationRequest}
 */
public final class ChannelAuthorizationRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.ChannelAuthorizationRequest)
    ChannelAuthorizationRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ChannelAuthorizationRequest.newBuilder() to construct.
  private ChannelAuthorizationRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ChannelAuthorizationRequest() {
    action_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ChannelAuthorizationRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ChannelAuthorizationRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            action_ = s;
            break;
          }
          case 18: {
            cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.Builder subBuilder = null;
            if (listBindingSection_ != null) {
              subBuilder = listBindingSection_.toBuilder();
            }
            listBindingSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(listBindingSection_);
              listBindingSection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection.Builder subBuilder = null;
            if (switchBindingSection_ != null) {
              subBuilder = switchBindingSection_.toBuilder();
            }
            switchBindingSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(switchBindingSection_);
              switchBindingSection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 34: {
            cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection.Builder subBuilder = null;
            if (listAuthorizationSection_ != null) {
              subBuilder = listAuthorizationSection_.toBuilder();
            }
            listAuthorizationSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(listAuthorizationSection_);
              listAuthorizationSection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 42: {
            cn.hexcloud.pbis.common.service.facade.channel.SignupSection.Builder subBuilder = null;
            if (signupSection_ != null) {
              subBuilder = signupSection_.toBuilder();
            }
            signupSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.SignupSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(signupSection_);
              signupSection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 50: {
            cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection.Builder subBuilder = null;
            if (querySignupSection_ != null) {
              subBuilder = querySignupSection_.toBuilder();
            }
            querySignupSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(querySignupSection_);
              querySignupSection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 58: {
            cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.Builder subBuilder = null;
            if (refreshSignupSection_ != null) {
              subBuilder = refreshSignupSection_.toBuilder();
            }
            refreshSignupSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(refreshSignupSection_);
              refreshSignupSection_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAuthorizationRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAuthorizationRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest.Builder.class);
  }

  public static final int ACTION_FIELD_NUMBER = 1;
  private volatile java.lang.Object action_;
  /**
   * <pre>
   * （必传）业务操作
   * </pre>
   *
   * <code>string action = 1;</code>
   * @return The action.
   */
  @java.lang.Override
  public java.lang.String getAction() {
    java.lang.Object ref = action_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      action_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）业务操作
   * </pre>
   *
   * <code>string action = 1;</code>
   * @return The bytes for action.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getActionBytes() {
    java.lang.Object ref = action_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      action_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LIST_BINDING_SECTION_FIELD_NUMBER = 2;
  private cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection listBindingSection_;
  /**
   * <pre>
   * （可选）渠道绑定关系查询请求对象
   * </pre>
   *
   * <code>.channel.ListBindingSection list_binding_section = 2;</code>
   * @return Whether the listBindingSection field is set.
   */
  @java.lang.Override
  public boolean hasListBindingSection() {
    return listBindingSection_ != null;
  }
  /**
   * <pre>
   * （可选）渠道绑定关系查询请求对象
   * </pre>
   *
   * <code>.channel.ListBindingSection list_binding_section = 2;</code>
   * @return The listBindingSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection getListBindingSection() {
    return listBindingSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.getDefaultInstance() : listBindingSection_;
  }
  /**
   * <pre>
   * （可选）渠道绑定关系查询请求对象
   * </pre>
   *
   * <code>.channel.ListBindingSection list_binding_section = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ListBindingSectionOrBuilder getListBindingSectionOrBuilder() {
    return getListBindingSection();
  }

  public static final int SWITCH_BINDING_SECTION_FIELD_NUMBER = 3;
  private cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection switchBindingSection_;
  /**
   * <pre>
   * （可选）绑定/解绑渠道请求对象
   * </pre>
   *
   * <code>.channel.SwitchBindingSection switch_binding_section = 3;</code>
   * @return Whether the switchBindingSection field is set.
   */
  @java.lang.Override
  public boolean hasSwitchBindingSection() {
    return switchBindingSection_ != null;
  }
  /**
   * <pre>
   * （可选）绑定/解绑渠道请求对象
   * </pre>
   *
   * <code>.channel.SwitchBindingSection switch_binding_section = 3;</code>
   * @return The switchBindingSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection getSwitchBindingSection() {
    return switchBindingSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection.getDefaultInstance() : switchBindingSection_;
  }
  /**
   * <pre>
   * （可选）绑定/解绑渠道请求对象
   * </pre>
   *
   * <code>.channel.SwitchBindingSection switch_binding_section = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSectionOrBuilder getSwitchBindingSectionOrBuilder() {
    return getSwitchBindingSection();
  }

  public static final int LIST_AUTHORIZATION_SECTION_FIELD_NUMBER = 4;
  private cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection listAuthorizationSection_;
  /**
   * <pre>
   * （可选）渠道签约授权信息查询请求对象
   * </pre>
   *
   * <code>.channel.ListAuthorizationSection list_authorization_section = 4;</code>
   * @return Whether the listAuthorizationSection field is set.
   */
  @java.lang.Override
  public boolean hasListAuthorizationSection() {
    return listAuthorizationSection_ != null;
  }
  /**
   * <pre>
   * （可选）渠道签约授权信息查询请求对象
   * </pre>
   *
   * <code>.channel.ListAuthorizationSection list_authorization_section = 4;</code>
   * @return The listAuthorizationSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection getListAuthorizationSection() {
    return listAuthorizationSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection.getDefaultInstance() : listAuthorizationSection_;
  }
  /**
   * <pre>
   * （可选）渠道签约授权信息查询请求对象
   * </pre>
   *
   * <code>.channel.ListAuthorizationSection list_authorization_section = 4;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSectionOrBuilder getListAuthorizationSectionOrBuilder() {
    return getListAuthorizationSection();
  }

  public static final int SIGNUP_SECTION_FIELD_NUMBER = 5;
  private cn.hexcloud.pbis.common.service.facade.channel.SignupSection signupSection_;
  /**
   * <pre>
   * （可选）渠道签约请求对象
   * </pre>
   *
   * <code>.channel.SignupSection signup_section = 5;</code>
   * @return Whether the signupSection field is set.
   */
  @java.lang.Override
  public boolean hasSignupSection() {
    return signupSection_ != null;
  }
  /**
   * <pre>
   * （可选）渠道签约请求对象
   * </pre>
   *
   * <code>.channel.SignupSection signup_section = 5;</code>
   * @return The signupSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SignupSection getSignupSection() {
    return signupSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.SignupSection.getDefaultInstance() : signupSection_;
  }
  /**
   * <pre>
   * （可选）渠道签约请求对象
   * </pre>
   *
   * <code>.channel.SignupSection signup_section = 5;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SignupSectionOrBuilder getSignupSectionOrBuilder() {
    return getSignupSection();
  }

  public static final int QUERY_SIGNUP_SECTION_FIELD_NUMBER = 6;
  private cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection querySignupSection_;
  /**
   * <pre>
   * （可选）渠道签约详情查询请求对象
   * </pre>
   *
   * <code>.channel.QuerySignupSection query_signup_section = 6;</code>
   * @return Whether the querySignupSection field is set.
   */
  @java.lang.Override
  public boolean hasQuerySignupSection() {
    return querySignupSection_ != null;
  }
  /**
   * <pre>
   * （可选）渠道签约详情查询请求对象
   * </pre>
   *
   * <code>.channel.QuerySignupSection query_signup_section = 6;</code>
   * @return The querySignupSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection getQuerySignupSection() {
    return querySignupSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection.getDefaultInstance() : querySignupSection_;
  }
  /**
   * <pre>
   * （可选）渠道签约详情查询请求对象
   * </pre>
   *
   * <code>.channel.QuerySignupSection query_signup_section = 6;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSectionOrBuilder getQuerySignupSectionOrBuilder() {
    return getQuerySignupSection();
  }

  public static final int REFRESH_SIGNUP_SECTION_FIELD_NUMBER = 7;
  private cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection refreshSignupSection_;
  /**
   * <pre>
   * （可选）渠道签约状态刷新请求对象
   * </pre>
   *
   * <code>.channel.RefreshSignupSection refresh_signup_section = 7;</code>
   * @return Whether the refreshSignupSection field is set.
   */
  @java.lang.Override
  public boolean hasRefreshSignupSection() {
    return refreshSignupSection_ != null;
  }
  /**
   * <pre>
   * （可选）渠道签约状态刷新请求对象
   * </pre>
   *
   * <code>.channel.RefreshSignupSection refresh_signup_section = 7;</code>
   * @return The refreshSignupSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection getRefreshSignupSection() {
    return refreshSignupSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.getDefaultInstance() : refreshSignupSection_;
  }
  /**
   * <pre>
   * （可选）渠道签约状态刷新请求对象
   * </pre>
   *
   * <code>.channel.RefreshSignupSection refresh_signup_section = 7;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSectionOrBuilder getRefreshSignupSectionOrBuilder() {
    return getRefreshSignupSection();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getActionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, action_);
    }
    if (listBindingSection_ != null) {
      output.writeMessage(2, getListBindingSection());
    }
    if (switchBindingSection_ != null) {
      output.writeMessage(3, getSwitchBindingSection());
    }
    if (listAuthorizationSection_ != null) {
      output.writeMessage(4, getListAuthorizationSection());
    }
    if (signupSection_ != null) {
      output.writeMessage(5, getSignupSection());
    }
    if (querySignupSection_ != null) {
      output.writeMessage(6, getQuerySignupSection());
    }
    if (refreshSignupSection_ != null) {
      output.writeMessage(7, getRefreshSignupSection());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getActionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, action_);
    }
    if (listBindingSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getListBindingSection());
    }
    if (switchBindingSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getSwitchBindingSection());
    }
    if (listAuthorizationSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getListAuthorizationSection());
    }
    if (signupSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getSignupSection());
    }
    if (querySignupSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, getQuerySignupSection());
    }
    if (refreshSignupSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getRefreshSignupSection());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest other = (cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest) obj;

    if (!getAction()
        .equals(other.getAction())) return false;
    if (hasListBindingSection() != other.hasListBindingSection()) return false;
    if (hasListBindingSection()) {
      if (!getListBindingSection()
          .equals(other.getListBindingSection())) return false;
    }
    if (hasSwitchBindingSection() != other.hasSwitchBindingSection()) return false;
    if (hasSwitchBindingSection()) {
      if (!getSwitchBindingSection()
          .equals(other.getSwitchBindingSection())) return false;
    }
    if (hasListAuthorizationSection() != other.hasListAuthorizationSection()) return false;
    if (hasListAuthorizationSection()) {
      if (!getListAuthorizationSection()
          .equals(other.getListAuthorizationSection())) return false;
    }
    if (hasSignupSection() != other.hasSignupSection()) return false;
    if (hasSignupSection()) {
      if (!getSignupSection()
          .equals(other.getSignupSection())) return false;
    }
    if (hasQuerySignupSection() != other.hasQuerySignupSection()) return false;
    if (hasQuerySignupSection()) {
      if (!getQuerySignupSection()
          .equals(other.getQuerySignupSection())) return false;
    }
    if (hasRefreshSignupSection() != other.hasRefreshSignupSection()) return false;
    if (hasRefreshSignupSection()) {
      if (!getRefreshSignupSection()
          .equals(other.getRefreshSignupSection())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ACTION_FIELD_NUMBER;
    hash = (53 * hash) + getAction().hashCode();
    if (hasListBindingSection()) {
      hash = (37 * hash) + LIST_BINDING_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getListBindingSection().hashCode();
    }
    if (hasSwitchBindingSection()) {
      hash = (37 * hash) + SWITCH_BINDING_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getSwitchBindingSection().hashCode();
    }
    if (hasListAuthorizationSection()) {
      hash = (37 * hash) + LIST_AUTHORIZATION_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getListAuthorizationSection().hashCode();
    }
    if (hasSignupSection()) {
      hash = (37 * hash) + SIGNUP_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getSignupSection().hashCode();
    }
    if (hasQuerySignupSection()) {
      hash = (37 * hash) + QUERY_SIGNUP_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getQuerySignupSection().hashCode();
    }
    if (hasRefreshSignupSection()) {
      hash = (37 * hash) + REFRESH_SIGNUP_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getRefreshSignupSection().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道签约授权请求信息
   * </pre>
   *
   * Protobuf type {@code channel.ChannelAuthorizationRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.ChannelAuthorizationRequest)
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAuthorizationRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAuthorizationRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      action_ = "";

      if (listBindingSectionBuilder_ == null) {
        listBindingSection_ = null;
      } else {
        listBindingSection_ = null;
        listBindingSectionBuilder_ = null;
      }
      if (switchBindingSectionBuilder_ == null) {
        switchBindingSection_ = null;
      } else {
        switchBindingSection_ = null;
        switchBindingSectionBuilder_ = null;
      }
      if (listAuthorizationSectionBuilder_ == null) {
        listAuthorizationSection_ = null;
      } else {
        listAuthorizationSection_ = null;
        listAuthorizationSectionBuilder_ = null;
      }
      if (signupSectionBuilder_ == null) {
        signupSection_ = null;
      } else {
        signupSection_ = null;
        signupSectionBuilder_ = null;
      }
      if (querySignupSectionBuilder_ == null) {
        querySignupSection_ = null;
      } else {
        querySignupSection_ = null;
        querySignupSectionBuilder_ = null;
      }
      if (refreshSignupSectionBuilder_ == null) {
        refreshSignupSection_ = null;
      } else {
        refreshSignupSection_ = null;
        refreshSignupSectionBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAuthorizationRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest build() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest result = new cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest(this);
      result.action_ = action_;
      if (listBindingSectionBuilder_ == null) {
        result.listBindingSection_ = listBindingSection_;
      } else {
        result.listBindingSection_ = listBindingSectionBuilder_.build();
      }
      if (switchBindingSectionBuilder_ == null) {
        result.switchBindingSection_ = switchBindingSection_;
      } else {
        result.switchBindingSection_ = switchBindingSectionBuilder_.build();
      }
      if (listAuthorizationSectionBuilder_ == null) {
        result.listAuthorizationSection_ = listAuthorizationSection_;
      } else {
        result.listAuthorizationSection_ = listAuthorizationSectionBuilder_.build();
      }
      if (signupSectionBuilder_ == null) {
        result.signupSection_ = signupSection_;
      } else {
        result.signupSection_ = signupSectionBuilder_.build();
      }
      if (querySignupSectionBuilder_ == null) {
        result.querySignupSection_ = querySignupSection_;
      } else {
        result.querySignupSection_ = querySignupSectionBuilder_.build();
      }
      if (refreshSignupSectionBuilder_ == null) {
        result.refreshSignupSection_ = refreshSignupSection_;
      } else {
        result.refreshSignupSection_ = refreshSignupSectionBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest.getDefaultInstance()) return this;
      if (!other.getAction().isEmpty()) {
        action_ = other.action_;
        onChanged();
      }
      if (other.hasListBindingSection()) {
        mergeListBindingSection(other.getListBindingSection());
      }
      if (other.hasSwitchBindingSection()) {
        mergeSwitchBindingSection(other.getSwitchBindingSection());
      }
      if (other.hasListAuthorizationSection()) {
        mergeListAuthorizationSection(other.getListAuthorizationSection());
      }
      if (other.hasSignupSection()) {
        mergeSignupSection(other.getSignupSection());
      }
      if (other.hasQuerySignupSection()) {
        mergeQuerySignupSection(other.getQuerySignupSection());
      }
      if (other.hasRefreshSignupSection()) {
        mergeRefreshSignupSection(other.getRefreshSignupSection());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object action_ = "";
    /**
     * <pre>
     * （必传）业务操作
     * </pre>
     *
     * <code>string action = 1;</code>
     * @return The action.
     */
    public java.lang.String getAction() {
      java.lang.Object ref = action_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        action_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）业务操作
     * </pre>
     *
     * <code>string action = 1;</code>
     * @return The bytes for action.
     */
    public com.google.protobuf.ByteString
        getActionBytes() {
      java.lang.Object ref = action_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        action_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）业务操作
     * </pre>
     *
     * <code>string action = 1;</code>
     * @param value The action to set.
     * @return This builder for chaining.
     */
    public Builder setAction(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      action_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）业务操作
     * </pre>
     *
     * <code>string action = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearAction() {
      
      action_ = getDefaultInstance().getAction();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）业务操作
     * </pre>
     *
     * <code>string action = 1;</code>
     * @param value The bytes for action to set.
     * @return This builder for chaining.
     */
    public Builder setActionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      action_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection listBindingSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection, cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.ListBindingSectionOrBuilder> listBindingSectionBuilder_;
    /**
     * <pre>
     * （可选）渠道绑定关系查询请求对象
     * </pre>
     *
     * <code>.channel.ListBindingSection list_binding_section = 2;</code>
     * @return Whether the listBindingSection field is set.
     */
    public boolean hasListBindingSection() {
      return listBindingSectionBuilder_ != null || listBindingSection_ != null;
    }
    /**
     * <pre>
     * （可选）渠道绑定关系查询请求对象
     * </pre>
     *
     * <code>.channel.ListBindingSection list_binding_section = 2;</code>
     * @return The listBindingSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection getListBindingSection() {
      if (listBindingSectionBuilder_ == null) {
        return listBindingSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.getDefaultInstance() : listBindingSection_;
      } else {
        return listBindingSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）渠道绑定关系查询请求对象
     * </pre>
     *
     * <code>.channel.ListBindingSection list_binding_section = 2;</code>
     */
    public Builder setListBindingSection(cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection value) {
      if (listBindingSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        listBindingSection_ = value;
        onChanged();
      } else {
        listBindingSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道绑定关系查询请求对象
     * </pre>
     *
     * <code>.channel.ListBindingSection list_binding_section = 2;</code>
     */
    public Builder setListBindingSection(
        cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.Builder builderForValue) {
      if (listBindingSectionBuilder_ == null) {
        listBindingSection_ = builderForValue.build();
        onChanged();
      } else {
        listBindingSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道绑定关系查询请求对象
     * </pre>
     *
     * <code>.channel.ListBindingSection list_binding_section = 2;</code>
     */
    public Builder mergeListBindingSection(cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection value) {
      if (listBindingSectionBuilder_ == null) {
        if (listBindingSection_ != null) {
          listBindingSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.newBuilder(listBindingSection_).mergeFrom(value).buildPartial();
        } else {
          listBindingSection_ = value;
        }
        onChanged();
      } else {
        listBindingSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道绑定关系查询请求对象
     * </pre>
     *
     * <code>.channel.ListBindingSection list_binding_section = 2;</code>
     */
    public Builder clearListBindingSection() {
      if (listBindingSectionBuilder_ == null) {
        listBindingSection_ = null;
        onChanged();
      } else {
        listBindingSection_ = null;
        listBindingSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道绑定关系查询请求对象
     * </pre>
     *
     * <code>.channel.ListBindingSection list_binding_section = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.Builder getListBindingSectionBuilder() {
      
      onChanged();
      return getListBindingSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）渠道绑定关系查询请求对象
     * </pre>
     *
     * <code>.channel.ListBindingSection list_binding_section = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ListBindingSectionOrBuilder getListBindingSectionOrBuilder() {
      if (listBindingSectionBuilder_ != null) {
        return listBindingSectionBuilder_.getMessageOrBuilder();
      } else {
        return listBindingSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.getDefaultInstance() : listBindingSection_;
      }
    }
    /**
     * <pre>
     * （可选）渠道绑定关系查询请求对象
     * </pre>
     *
     * <code>.channel.ListBindingSection list_binding_section = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection, cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.ListBindingSectionOrBuilder> 
        getListBindingSectionFieldBuilder() {
      if (listBindingSectionBuilder_ == null) {
        listBindingSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection, cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.ListBindingSectionOrBuilder>(
                getListBindingSection(),
                getParentForChildren(),
                isClean());
        listBindingSection_ = null;
      }
      return listBindingSectionBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection switchBindingSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection, cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSectionOrBuilder> switchBindingSectionBuilder_;
    /**
     * <pre>
     * （可选）绑定/解绑渠道请求对象
     * </pre>
     *
     * <code>.channel.SwitchBindingSection switch_binding_section = 3;</code>
     * @return Whether the switchBindingSection field is set.
     */
    public boolean hasSwitchBindingSection() {
      return switchBindingSectionBuilder_ != null || switchBindingSection_ != null;
    }
    /**
     * <pre>
     * （可选）绑定/解绑渠道请求对象
     * </pre>
     *
     * <code>.channel.SwitchBindingSection switch_binding_section = 3;</code>
     * @return The switchBindingSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection getSwitchBindingSection() {
      if (switchBindingSectionBuilder_ == null) {
        return switchBindingSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection.getDefaultInstance() : switchBindingSection_;
      } else {
        return switchBindingSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）绑定/解绑渠道请求对象
     * </pre>
     *
     * <code>.channel.SwitchBindingSection switch_binding_section = 3;</code>
     */
    public Builder setSwitchBindingSection(cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection value) {
      if (switchBindingSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        switchBindingSection_ = value;
        onChanged();
      } else {
        switchBindingSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）绑定/解绑渠道请求对象
     * </pre>
     *
     * <code>.channel.SwitchBindingSection switch_binding_section = 3;</code>
     */
    public Builder setSwitchBindingSection(
        cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection.Builder builderForValue) {
      if (switchBindingSectionBuilder_ == null) {
        switchBindingSection_ = builderForValue.build();
        onChanged();
      } else {
        switchBindingSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）绑定/解绑渠道请求对象
     * </pre>
     *
     * <code>.channel.SwitchBindingSection switch_binding_section = 3;</code>
     */
    public Builder mergeSwitchBindingSection(cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection value) {
      if (switchBindingSectionBuilder_ == null) {
        if (switchBindingSection_ != null) {
          switchBindingSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection.newBuilder(switchBindingSection_).mergeFrom(value).buildPartial();
        } else {
          switchBindingSection_ = value;
        }
        onChanged();
      } else {
        switchBindingSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）绑定/解绑渠道请求对象
     * </pre>
     *
     * <code>.channel.SwitchBindingSection switch_binding_section = 3;</code>
     */
    public Builder clearSwitchBindingSection() {
      if (switchBindingSectionBuilder_ == null) {
        switchBindingSection_ = null;
        onChanged();
      } else {
        switchBindingSection_ = null;
        switchBindingSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）绑定/解绑渠道请求对象
     * </pre>
     *
     * <code>.channel.SwitchBindingSection switch_binding_section = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection.Builder getSwitchBindingSectionBuilder() {
      
      onChanged();
      return getSwitchBindingSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）绑定/解绑渠道请求对象
     * </pre>
     *
     * <code>.channel.SwitchBindingSection switch_binding_section = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSectionOrBuilder getSwitchBindingSectionOrBuilder() {
      if (switchBindingSectionBuilder_ != null) {
        return switchBindingSectionBuilder_.getMessageOrBuilder();
      } else {
        return switchBindingSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection.getDefaultInstance() : switchBindingSection_;
      }
    }
    /**
     * <pre>
     * （可选）绑定/解绑渠道请求对象
     * </pre>
     *
     * <code>.channel.SwitchBindingSection switch_binding_section = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection, cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSectionOrBuilder> 
        getSwitchBindingSectionFieldBuilder() {
      if (switchBindingSectionBuilder_ == null) {
        switchBindingSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection, cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSectionOrBuilder>(
                getSwitchBindingSection(),
                getParentForChildren(),
                isClean());
        switchBindingSection_ = null;
      }
      return switchBindingSectionBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection listAuthorizationSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection, cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSectionOrBuilder> listAuthorizationSectionBuilder_;
    /**
     * <pre>
     * （可选）渠道签约授权信息查询请求对象
     * </pre>
     *
     * <code>.channel.ListAuthorizationSection list_authorization_section = 4;</code>
     * @return Whether the listAuthorizationSection field is set.
     */
    public boolean hasListAuthorizationSection() {
      return listAuthorizationSectionBuilder_ != null || listAuthorizationSection_ != null;
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息查询请求对象
     * </pre>
     *
     * <code>.channel.ListAuthorizationSection list_authorization_section = 4;</code>
     * @return The listAuthorizationSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection getListAuthorizationSection() {
      if (listAuthorizationSectionBuilder_ == null) {
        return listAuthorizationSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection.getDefaultInstance() : listAuthorizationSection_;
      } else {
        return listAuthorizationSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息查询请求对象
     * </pre>
     *
     * <code>.channel.ListAuthorizationSection list_authorization_section = 4;</code>
     */
    public Builder setListAuthorizationSection(cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection value) {
      if (listAuthorizationSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        listAuthorizationSection_ = value;
        onChanged();
      } else {
        listAuthorizationSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息查询请求对象
     * </pre>
     *
     * <code>.channel.ListAuthorizationSection list_authorization_section = 4;</code>
     */
    public Builder setListAuthorizationSection(
        cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection.Builder builderForValue) {
      if (listAuthorizationSectionBuilder_ == null) {
        listAuthorizationSection_ = builderForValue.build();
        onChanged();
      } else {
        listAuthorizationSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息查询请求对象
     * </pre>
     *
     * <code>.channel.ListAuthorizationSection list_authorization_section = 4;</code>
     */
    public Builder mergeListAuthorizationSection(cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection value) {
      if (listAuthorizationSectionBuilder_ == null) {
        if (listAuthorizationSection_ != null) {
          listAuthorizationSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection.newBuilder(listAuthorizationSection_).mergeFrom(value).buildPartial();
        } else {
          listAuthorizationSection_ = value;
        }
        onChanged();
      } else {
        listAuthorizationSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息查询请求对象
     * </pre>
     *
     * <code>.channel.ListAuthorizationSection list_authorization_section = 4;</code>
     */
    public Builder clearListAuthorizationSection() {
      if (listAuthorizationSectionBuilder_ == null) {
        listAuthorizationSection_ = null;
        onChanged();
      } else {
        listAuthorizationSection_ = null;
        listAuthorizationSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息查询请求对象
     * </pre>
     *
     * <code>.channel.ListAuthorizationSection list_authorization_section = 4;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection.Builder getListAuthorizationSectionBuilder() {
      
      onChanged();
      return getListAuthorizationSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息查询请求对象
     * </pre>
     *
     * <code>.channel.ListAuthorizationSection list_authorization_section = 4;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSectionOrBuilder getListAuthorizationSectionOrBuilder() {
      if (listAuthorizationSectionBuilder_ != null) {
        return listAuthorizationSectionBuilder_.getMessageOrBuilder();
      } else {
        return listAuthorizationSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection.getDefaultInstance() : listAuthorizationSection_;
      }
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息查询请求对象
     * </pre>
     *
     * <code>.channel.ListAuthorizationSection list_authorization_section = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection, cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSectionOrBuilder> 
        getListAuthorizationSectionFieldBuilder() {
      if (listAuthorizationSectionBuilder_ == null) {
        listAuthorizationSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection, cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSectionOrBuilder>(
                getListAuthorizationSection(),
                getParentForChildren(),
                isClean());
        listAuthorizationSection_ = null;
      }
      return listAuthorizationSectionBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.SignupSection signupSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.SignupSection, cn.hexcloud.pbis.common.service.facade.channel.SignupSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SignupSectionOrBuilder> signupSectionBuilder_;
    /**
     * <pre>
     * （可选）渠道签约请求对象
     * </pre>
     *
     * <code>.channel.SignupSection signup_section = 5;</code>
     * @return Whether the signupSection field is set.
     */
    public boolean hasSignupSection() {
      return signupSectionBuilder_ != null || signupSection_ != null;
    }
    /**
     * <pre>
     * （可选）渠道签约请求对象
     * </pre>
     *
     * <code>.channel.SignupSection signup_section = 5;</code>
     * @return The signupSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SignupSection getSignupSection() {
      if (signupSectionBuilder_ == null) {
        return signupSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.SignupSection.getDefaultInstance() : signupSection_;
      } else {
        return signupSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）渠道签约请求对象
     * </pre>
     *
     * <code>.channel.SignupSection signup_section = 5;</code>
     */
    public Builder setSignupSection(cn.hexcloud.pbis.common.service.facade.channel.SignupSection value) {
      if (signupSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        signupSection_ = value;
        onChanged();
      } else {
        signupSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约请求对象
     * </pre>
     *
     * <code>.channel.SignupSection signup_section = 5;</code>
     */
    public Builder setSignupSection(
        cn.hexcloud.pbis.common.service.facade.channel.SignupSection.Builder builderForValue) {
      if (signupSectionBuilder_ == null) {
        signupSection_ = builderForValue.build();
        onChanged();
      } else {
        signupSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约请求对象
     * </pre>
     *
     * <code>.channel.SignupSection signup_section = 5;</code>
     */
    public Builder mergeSignupSection(cn.hexcloud.pbis.common.service.facade.channel.SignupSection value) {
      if (signupSectionBuilder_ == null) {
        if (signupSection_ != null) {
          signupSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.SignupSection.newBuilder(signupSection_).mergeFrom(value).buildPartial();
        } else {
          signupSection_ = value;
        }
        onChanged();
      } else {
        signupSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约请求对象
     * </pre>
     *
     * <code>.channel.SignupSection signup_section = 5;</code>
     */
    public Builder clearSignupSection() {
      if (signupSectionBuilder_ == null) {
        signupSection_ = null;
        onChanged();
      } else {
        signupSection_ = null;
        signupSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约请求对象
     * </pre>
     *
     * <code>.channel.SignupSection signup_section = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SignupSection.Builder getSignupSectionBuilder() {
      
      onChanged();
      return getSignupSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）渠道签约请求对象
     * </pre>
     *
     * <code>.channel.SignupSection signup_section = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SignupSectionOrBuilder getSignupSectionOrBuilder() {
      if (signupSectionBuilder_ != null) {
        return signupSectionBuilder_.getMessageOrBuilder();
      } else {
        return signupSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.SignupSection.getDefaultInstance() : signupSection_;
      }
    }
    /**
     * <pre>
     * （可选）渠道签约请求对象
     * </pre>
     *
     * <code>.channel.SignupSection signup_section = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.SignupSection, cn.hexcloud.pbis.common.service.facade.channel.SignupSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SignupSectionOrBuilder> 
        getSignupSectionFieldBuilder() {
      if (signupSectionBuilder_ == null) {
        signupSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.SignupSection, cn.hexcloud.pbis.common.service.facade.channel.SignupSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SignupSectionOrBuilder>(
                getSignupSection(),
                getParentForChildren(),
                isClean());
        signupSection_ = null;
      }
      return signupSectionBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection querySignupSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection, cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSectionOrBuilder> querySignupSectionBuilder_;
    /**
     * <pre>
     * （可选）渠道签约详情查询请求对象
     * </pre>
     *
     * <code>.channel.QuerySignupSection query_signup_section = 6;</code>
     * @return Whether the querySignupSection field is set.
     */
    public boolean hasQuerySignupSection() {
      return querySignupSectionBuilder_ != null || querySignupSection_ != null;
    }
    /**
     * <pre>
     * （可选）渠道签约详情查询请求对象
     * </pre>
     *
     * <code>.channel.QuerySignupSection query_signup_section = 6;</code>
     * @return The querySignupSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection getQuerySignupSection() {
      if (querySignupSectionBuilder_ == null) {
        return querySignupSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection.getDefaultInstance() : querySignupSection_;
      } else {
        return querySignupSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）渠道签约详情查询请求对象
     * </pre>
     *
     * <code>.channel.QuerySignupSection query_signup_section = 6;</code>
     */
    public Builder setQuerySignupSection(cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection value) {
      if (querySignupSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        querySignupSection_ = value;
        onChanged();
      } else {
        querySignupSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约详情查询请求对象
     * </pre>
     *
     * <code>.channel.QuerySignupSection query_signup_section = 6;</code>
     */
    public Builder setQuerySignupSection(
        cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection.Builder builderForValue) {
      if (querySignupSectionBuilder_ == null) {
        querySignupSection_ = builderForValue.build();
        onChanged();
      } else {
        querySignupSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约详情查询请求对象
     * </pre>
     *
     * <code>.channel.QuerySignupSection query_signup_section = 6;</code>
     */
    public Builder mergeQuerySignupSection(cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection value) {
      if (querySignupSectionBuilder_ == null) {
        if (querySignupSection_ != null) {
          querySignupSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection.newBuilder(querySignupSection_).mergeFrom(value).buildPartial();
        } else {
          querySignupSection_ = value;
        }
        onChanged();
      } else {
        querySignupSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约详情查询请求对象
     * </pre>
     *
     * <code>.channel.QuerySignupSection query_signup_section = 6;</code>
     */
    public Builder clearQuerySignupSection() {
      if (querySignupSectionBuilder_ == null) {
        querySignupSection_ = null;
        onChanged();
      } else {
        querySignupSection_ = null;
        querySignupSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约详情查询请求对象
     * </pre>
     *
     * <code>.channel.QuerySignupSection query_signup_section = 6;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection.Builder getQuerySignupSectionBuilder() {
      
      onChanged();
      return getQuerySignupSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）渠道签约详情查询请求对象
     * </pre>
     *
     * <code>.channel.QuerySignupSection query_signup_section = 6;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSectionOrBuilder getQuerySignupSectionOrBuilder() {
      if (querySignupSectionBuilder_ != null) {
        return querySignupSectionBuilder_.getMessageOrBuilder();
      } else {
        return querySignupSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection.getDefaultInstance() : querySignupSection_;
      }
    }
    /**
     * <pre>
     * （可选）渠道签约详情查询请求对象
     * </pre>
     *
     * <code>.channel.QuerySignupSection query_signup_section = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection, cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSectionOrBuilder> 
        getQuerySignupSectionFieldBuilder() {
      if (querySignupSectionBuilder_ == null) {
        querySignupSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection, cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSectionOrBuilder>(
                getQuerySignupSection(),
                getParentForChildren(),
                isClean());
        querySignupSection_ = null;
      }
      return querySignupSectionBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection refreshSignupSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSectionOrBuilder> refreshSignupSectionBuilder_;
    /**
     * <pre>
     * （可选）渠道签约状态刷新请求对象
     * </pre>
     *
     * <code>.channel.RefreshSignupSection refresh_signup_section = 7;</code>
     * @return Whether the refreshSignupSection field is set.
     */
    public boolean hasRefreshSignupSection() {
      return refreshSignupSectionBuilder_ != null || refreshSignupSection_ != null;
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新请求对象
     * </pre>
     *
     * <code>.channel.RefreshSignupSection refresh_signup_section = 7;</code>
     * @return The refreshSignupSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection getRefreshSignupSection() {
      if (refreshSignupSectionBuilder_ == null) {
        return refreshSignupSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.getDefaultInstance() : refreshSignupSection_;
      } else {
        return refreshSignupSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新请求对象
     * </pre>
     *
     * <code>.channel.RefreshSignupSection refresh_signup_section = 7;</code>
     */
    public Builder setRefreshSignupSection(cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection value) {
      if (refreshSignupSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        refreshSignupSection_ = value;
        onChanged();
      } else {
        refreshSignupSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新请求对象
     * </pre>
     *
     * <code>.channel.RefreshSignupSection refresh_signup_section = 7;</code>
     */
    public Builder setRefreshSignupSection(
        cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.Builder builderForValue) {
      if (refreshSignupSectionBuilder_ == null) {
        refreshSignupSection_ = builderForValue.build();
        onChanged();
      } else {
        refreshSignupSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新请求对象
     * </pre>
     *
     * <code>.channel.RefreshSignupSection refresh_signup_section = 7;</code>
     */
    public Builder mergeRefreshSignupSection(cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection value) {
      if (refreshSignupSectionBuilder_ == null) {
        if (refreshSignupSection_ != null) {
          refreshSignupSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.newBuilder(refreshSignupSection_).mergeFrom(value).buildPartial();
        } else {
          refreshSignupSection_ = value;
        }
        onChanged();
      } else {
        refreshSignupSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新请求对象
     * </pre>
     *
     * <code>.channel.RefreshSignupSection refresh_signup_section = 7;</code>
     */
    public Builder clearRefreshSignupSection() {
      if (refreshSignupSectionBuilder_ == null) {
        refreshSignupSection_ = null;
        onChanged();
      } else {
        refreshSignupSection_ = null;
        refreshSignupSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新请求对象
     * </pre>
     *
     * <code>.channel.RefreshSignupSection refresh_signup_section = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.Builder getRefreshSignupSectionBuilder() {
      
      onChanged();
      return getRefreshSignupSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新请求对象
     * </pre>
     *
     * <code>.channel.RefreshSignupSection refresh_signup_section = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSectionOrBuilder getRefreshSignupSectionOrBuilder() {
      if (refreshSignupSectionBuilder_ != null) {
        return refreshSignupSectionBuilder_.getMessageOrBuilder();
      } else {
        return refreshSignupSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.getDefaultInstance() : refreshSignupSection_;
      }
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新请求对象
     * </pre>
     *
     * <code>.channel.RefreshSignupSection refresh_signup_section = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSectionOrBuilder> 
        getRefreshSignupSectionFieldBuilder() {
      if (refreshSignupSectionBuilder_ == null) {
        refreshSignupSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSectionOrBuilder>(
                getRefreshSignupSection(),
                getParentForChildren(),
                isClean());
        refreshSignupSection_ = null;
      }
      return refreshSignupSectionBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.ChannelAuthorizationRequest)
  }

  // @@protoc_insertion_point(class_scope:channel.ChannelAuthorizationRequest)
  private static final cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ChannelAuthorizationRequest>
      PARSER = new com.google.protobuf.AbstractParser<ChannelAuthorizationRequest>() {
    @java.lang.Override
    public ChannelAuthorizationRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ChannelAuthorizationRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ChannelAuthorizationRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ChannelAuthorizationRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

