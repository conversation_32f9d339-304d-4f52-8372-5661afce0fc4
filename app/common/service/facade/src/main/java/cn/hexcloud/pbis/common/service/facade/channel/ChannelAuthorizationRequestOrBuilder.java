// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ChannelAuthorizationRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ChannelAuthorizationRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）业务操作
   * </pre>
   *
   * <code>string action = 1;</code>
   * @return The action.
   */
  java.lang.String getAction();
  /**
   * <pre>
   * （必传）业务操作
   * </pre>
   *
   * <code>string action = 1;</code>
   * @return The bytes for action.
   */
  com.google.protobuf.ByteString
      getActionBytes();

  /**
   * <pre>
   * （可选）渠道绑定关系查询请求对象
   * </pre>
   *
   * <code>.channel.ListBindingSection list_binding_section = 2;</code>
   * @return Whether the listBindingSection field is set.
   */
  boolean hasListBindingSection();
  /**
   * <pre>
   * （可选）渠道绑定关系查询请求对象
   * </pre>
   *
   * <code>.channel.ListBindingSection list_binding_section = 2;</code>
   * @return The listBindingSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection getListBindingSection();
  /**
   * <pre>
   * （可选）渠道绑定关系查询请求对象
   * </pre>
   *
   * <code>.channel.ListBindingSection list_binding_section = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.ListBindingSectionOrBuilder getListBindingSectionOrBuilder();

  /**
   * <pre>
   * （可选）绑定/解绑渠道请求对象
   * </pre>
   *
   * <code>.channel.SwitchBindingSection switch_binding_section = 3;</code>
   * @return Whether the switchBindingSection field is set.
   */
  boolean hasSwitchBindingSection();
  /**
   * <pre>
   * （可选）绑定/解绑渠道请求对象
   * </pre>
   *
   * <code>.channel.SwitchBindingSection switch_binding_section = 3;</code>
   * @return The switchBindingSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSection getSwitchBindingSection();
  /**
   * <pre>
   * （可选）绑定/解绑渠道请求对象
   * </pre>
   *
   * <code>.channel.SwitchBindingSection switch_binding_section = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.SwitchBindingSectionOrBuilder getSwitchBindingSectionOrBuilder();

  /**
   * <pre>
   * （可选）渠道签约授权信息查询请求对象
   * </pre>
   *
   * <code>.channel.ListAuthorizationSection list_authorization_section = 4;</code>
   * @return Whether the listAuthorizationSection field is set.
   */
  boolean hasListAuthorizationSection();
  /**
   * <pre>
   * （可选）渠道签约授权信息查询请求对象
   * </pre>
   *
   * <code>.channel.ListAuthorizationSection list_authorization_section = 4;</code>
   * @return The listAuthorizationSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSection getListAuthorizationSection();
  /**
   * <pre>
   * （可选）渠道签约授权信息查询请求对象
   * </pre>
   *
   * <code>.channel.ListAuthorizationSection list_authorization_section = 4;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.ListAuthorizationSectionOrBuilder getListAuthorizationSectionOrBuilder();

  /**
   * <pre>
   * （可选）渠道签约请求对象
   * </pre>
   *
   * <code>.channel.SignupSection signup_section = 5;</code>
   * @return Whether the signupSection field is set.
   */
  boolean hasSignupSection();
  /**
   * <pre>
   * （可选）渠道签约请求对象
   * </pre>
   *
   * <code>.channel.SignupSection signup_section = 5;</code>
   * @return The signupSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.SignupSection getSignupSection();
  /**
   * <pre>
   * （可选）渠道签约请求对象
   * </pre>
   *
   * <code>.channel.SignupSection signup_section = 5;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.SignupSectionOrBuilder getSignupSectionOrBuilder();

  /**
   * <pre>
   * （可选）渠道签约详情查询请求对象
   * </pre>
   *
   * <code>.channel.QuerySignupSection query_signup_section = 6;</code>
   * @return Whether the querySignupSection field is set.
   */
  boolean hasQuerySignupSection();
  /**
   * <pre>
   * （可选）渠道签约详情查询请求对象
   * </pre>
   *
   * <code>.channel.QuerySignupSection query_signup_section = 6;</code>
   * @return The querySignupSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSection getQuerySignupSection();
  /**
   * <pre>
   * （可选）渠道签约详情查询请求对象
   * </pre>
   *
   * <code>.channel.QuerySignupSection query_signup_section = 6;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.QuerySignupSectionOrBuilder getQuerySignupSectionOrBuilder();

  /**
   * <pre>
   * （可选）渠道签约状态刷新请求对象
   * </pre>
   *
   * <code>.channel.RefreshSignupSection refresh_signup_section = 7;</code>
   * @return Whether the refreshSignupSection field is set.
   */
  boolean hasRefreshSignupSection();
  /**
   * <pre>
   * （可选）渠道签约状态刷新请求对象
   * </pre>
   *
   * <code>.channel.RefreshSignupSection refresh_signup_section = 7;</code>
   * @return The refreshSignupSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection getRefreshSignupSection();
  /**
   * <pre>
   * （可选）渠道签约状态刷新请求对象
   * </pre>
   *
   * <code>.channel.RefreshSignupSection refresh_signup_section = 7;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSectionOrBuilder getRefreshSignupSectionOrBuilder();
}
