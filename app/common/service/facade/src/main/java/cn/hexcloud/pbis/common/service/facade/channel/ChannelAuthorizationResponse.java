// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道签约授权响应信息
 * </pre>
 *
 * Protobuf type {@code channel.ChannelAuthorizationResponse}
 */
public final class ChannelAuthorizationResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.ChannelAuthorizationResponse)
    ChannelAuthorizationResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ChannelAuthorizationResponse.newBuilder() to construct.
  private ChannelAuthorizationResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ChannelAuthorizationResponse() {
    errorCode_ = "";
    errorMessage_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ChannelAuthorizationResponse();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ChannelAuthorizationResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            errorCode_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            errorMessage_ = s;
            break;
          }
          case 26: {
            cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.Builder subBuilder = null;
            if (bindingListSection_ != null) {
              subBuilder = bindingListSection_.toBuilder();
            }
            bindingListSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(bindingListSection_);
              bindingListSection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 34: {
            cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.Builder subBuilder = null;
            if (authorizationListSection_ != null) {
              subBuilder = authorizationListSection_.toBuilder();
            }
            authorizationListSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(authorizationListSection_);
              authorizationListSection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 42: {
            cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection.Builder subBuilder = null;
            if (signupResultSection_ != null) {
              subBuilder = signupResultSection_.toBuilder();
            }
            signupResultSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(signupResultSection_);
              signupResultSection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 50: {
            cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection.Builder subBuilder = null;
            if (signupDetailSection_ != null) {
              subBuilder = signupDetailSection_.toBuilder();
            }
            signupDetailSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(signupDetailSection_);
              signupDetailSection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 58: {
            cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.Builder subBuilder = null;
            if (refreshSignupResultSection_ != null) {
              subBuilder = refreshSignupResultSection_.toBuilder();
            }
            refreshSignupResultSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(refreshSignupResultSection_);
              refreshSignupResultSection_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAuthorizationResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAuthorizationResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse.Builder.class);
  }

  public static final int ERROR_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object errorCode_;
  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  @java.lang.Override
  public java.lang.String getErrorCode() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorCodeBytes() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ERROR_MESSAGE_FIELD_NUMBER = 2;
  private volatile java.lang.Object errorMessage_;
  /**
   * <pre>
   * （必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  @java.lang.Override
  public java.lang.String getErrorMessage() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorMessage_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorMessageBytes() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorMessage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BINDING_LIST_SECTION_FIELD_NUMBER = 3;
  private cn.hexcloud.pbis.common.service.facade.channel.BindingListSection bindingListSection_;
  /**
   * <pre>
   * （可选）渠道绑定关系信息
   * </pre>
   *
   * <code>.channel.BindingListSection binding_list_section = 3;</code>
   * @return Whether the bindingListSection field is set.
   */
  @java.lang.Override
  public boolean hasBindingListSection() {
    return bindingListSection_ != null;
  }
  /**
   * <pre>
   * （可选）渠道绑定关系信息
   * </pre>
   *
   * <code>.channel.BindingListSection binding_list_section = 3;</code>
   * @return The bindingListSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.BindingListSection getBindingListSection() {
    return bindingListSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.getDefaultInstance() : bindingListSection_;
  }
  /**
   * <pre>
   * （可选）渠道绑定关系信息
   * </pre>
   *
   * <code>.channel.BindingListSection binding_list_section = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.BindingListSectionOrBuilder getBindingListSectionOrBuilder() {
    return getBindingListSection();
  }

  public static final int AUTHORIZATION_LIST_SECTION_FIELD_NUMBER = 4;
  private cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection authorizationListSection_;
  /**
   * <pre>
   * （可选）渠道签约授权信息
   * </pre>
   *
   * <code>.channel.AuthorizationListSection authorization_list_section = 4;</code>
   * @return Whether the authorizationListSection field is set.
   */
  @java.lang.Override
  public boolean hasAuthorizationListSection() {
    return authorizationListSection_ != null;
  }
  /**
   * <pre>
   * （可选）渠道签约授权信息
   * </pre>
   *
   * <code>.channel.AuthorizationListSection authorization_list_section = 4;</code>
   * @return The authorizationListSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection getAuthorizationListSection() {
    return authorizationListSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.getDefaultInstance() : authorizationListSection_;
  }
  /**
   * <pre>
   * （可选）渠道签约授权信息
   * </pre>
   *
   * <code>.channel.AuthorizationListSection authorization_list_section = 4;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSectionOrBuilder getAuthorizationListSectionOrBuilder() {
    return getAuthorizationListSection();
  }

  public static final int SIGNUP_RESULT_SECTION_FIELD_NUMBER = 5;
  private cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection signupResultSection_;
  /**
   * <pre>
   * （可选）渠道签约结果
   * </pre>
   *
   * <code>.channel.SignupResultSection signup_result_section = 5;</code>
   * @return Whether the signupResultSection field is set.
   */
  @java.lang.Override
  public boolean hasSignupResultSection() {
    return signupResultSection_ != null;
  }
  /**
   * <pre>
   * （可选）渠道签约结果
   * </pre>
   *
   * <code>.channel.SignupResultSection signup_result_section = 5;</code>
   * @return The signupResultSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection getSignupResultSection() {
    return signupResultSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection.getDefaultInstance() : signupResultSection_;
  }
  /**
   * <pre>
   * （可选）渠道签约结果
   * </pre>
   *
   * <code>.channel.SignupResultSection signup_result_section = 5;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SignupResultSectionOrBuilder getSignupResultSectionOrBuilder() {
    return getSignupResultSection();
  }

  public static final int SIGNUP_DETAIL_SECTION_FIELD_NUMBER = 6;
  private cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection signupDetailSection_;
  /**
   * <pre>
   * （可选）渠道签约详情
   * </pre>
   *
   * <code>.channel.SignupDetailSection signup_detail_section = 6;</code>
   * @return Whether the signupDetailSection field is set.
   */
  @java.lang.Override
  public boolean hasSignupDetailSection() {
    return signupDetailSection_ != null;
  }
  /**
   * <pre>
   * （可选）渠道签约详情
   * </pre>
   *
   * <code>.channel.SignupDetailSection signup_detail_section = 6;</code>
   * @return The signupDetailSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection getSignupDetailSection() {
    return signupDetailSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection.getDefaultInstance() : signupDetailSection_;
  }
  /**
   * <pre>
   * （可选）渠道签约详情
   * </pre>
   *
   * <code>.channel.SignupDetailSection signup_detail_section = 6;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSectionOrBuilder getSignupDetailSectionOrBuilder() {
    return getSignupDetailSection();
  }

  public static final int REFRESH_SIGNUP_RESULT_SECTION_FIELD_NUMBER = 7;
  private cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection refreshSignupResultSection_;
  /**
   * <pre>
   * （可选）渠道签约状态刷新结果
   * </pre>
   *
   * <code>.channel.RefreshSignupResultSection refresh_signup_result_section = 7;</code>
   * @return Whether the refreshSignupResultSection field is set.
   */
  @java.lang.Override
  public boolean hasRefreshSignupResultSection() {
    return refreshSignupResultSection_ != null;
  }
  /**
   * <pre>
   * （可选）渠道签约状态刷新结果
   * </pre>
   *
   * <code>.channel.RefreshSignupResultSection refresh_signup_result_section = 7;</code>
   * @return The refreshSignupResultSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection getRefreshSignupResultSection() {
    return refreshSignupResultSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.getDefaultInstance() : refreshSignupResultSection_;
  }
  /**
   * <pre>
   * （可选）渠道签约状态刷新结果
   * </pre>
   *
   * <code>.channel.RefreshSignupResultSection refresh_signup_result_section = 7;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSectionOrBuilder getRefreshSignupResultSectionOrBuilder() {
    return getRefreshSignupResultSection();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getErrorCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, errorMessage_);
    }
    if (bindingListSection_ != null) {
      output.writeMessage(3, getBindingListSection());
    }
    if (authorizationListSection_ != null) {
      output.writeMessage(4, getAuthorizationListSection());
    }
    if (signupResultSection_ != null) {
      output.writeMessage(5, getSignupResultSection());
    }
    if (signupDetailSection_ != null) {
      output.writeMessage(6, getSignupDetailSection());
    }
    if (refreshSignupResultSection_ != null) {
      output.writeMessage(7, getRefreshSignupResultSection());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getErrorCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, errorMessage_);
    }
    if (bindingListSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getBindingListSection());
    }
    if (authorizationListSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getAuthorizationListSection());
    }
    if (signupResultSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getSignupResultSection());
    }
    if (signupDetailSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, getSignupDetailSection());
    }
    if (refreshSignupResultSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getRefreshSignupResultSection());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse other = (cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse) obj;

    if (!getErrorCode()
        .equals(other.getErrorCode())) return false;
    if (!getErrorMessage()
        .equals(other.getErrorMessage())) return false;
    if (hasBindingListSection() != other.hasBindingListSection()) return false;
    if (hasBindingListSection()) {
      if (!getBindingListSection()
          .equals(other.getBindingListSection())) return false;
    }
    if (hasAuthorizationListSection() != other.hasAuthorizationListSection()) return false;
    if (hasAuthorizationListSection()) {
      if (!getAuthorizationListSection()
          .equals(other.getAuthorizationListSection())) return false;
    }
    if (hasSignupResultSection() != other.hasSignupResultSection()) return false;
    if (hasSignupResultSection()) {
      if (!getSignupResultSection()
          .equals(other.getSignupResultSection())) return false;
    }
    if (hasSignupDetailSection() != other.hasSignupDetailSection()) return false;
    if (hasSignupDetailSection()) {
      if (!getSignupDetailSection()
          .equals(other.getSignupDetailSection())) return false;
    }
    if (hasRefreshSignupResultSection() != other.hasRefreshSignupResultSection()) return false;
    if (hasRefreshSignupResultSection()) {
      if (!getRefreshSignupResultSection()
          .equals(other.getRefreshSignupResultSection())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ERROR_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorCode().hashCode();
    hash = (37 * hash) + ERROR_MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorMessage().hashCode();
    if (hasBindingListSection()) {
      hash = (37 * hash) + BINDING_LIST_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getBindingListSection().hashCode();
    }
    if (hasAuthorizationListSection()) {
      hash = (37 * hash) + AUTHORIZATION_LIST_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getAuthorizationListSection().hashCode();
    }
    if (hasSignupResultSection()) {
      hash = (37 * hash) + SIGNUP_RESULT_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getSignupResultSection().hashCode();
    }
    if (hasSignupDetailSection()) {
      hash = (37 * hash) + SIGNUP_DETAIL_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getSignupDetailSection().hashCode();
    }
    if (hasRefreshSignupResultSection()) {
      hash = (37 * hash) + REFRESH_SIGNUP_RESULT_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getRefreshSignupResultSection().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道签约授权响应信息
   * </pre>
   *
   * Protobuf type {@code channel.ChannelAuthorizationResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.ChannelAuthorizationResponse)
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAuthorizationResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAuthorizationResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      errorCode_ = "";

      errorMessage_ = "";

      if (bindingListSectionBuilder_ == null) {
        bindingListSection_ = null;
      } else {
        bindingListSection_ = null;
        bindingListSectionBuilder_ = null;
      }
      if (authorizationListSectionBuilder_ == null) {
        authorizationListSection_ = null;
      } else {
        authorizationListSection_ = null;
        authorizationListSectionBuilder_ = null;
      }
      if (signupResultSectionBuilder_ == null) {
        signupResultSection_ = null;
      } else {
        signupResultSection_ = null;
        signupResultSectionBuilder_ = null;
      }
      if (signupDetailSectionBuilder_ == null) {
        signupDetailSection_ = null;
      } else {
        signupDetailSection_ = null;
        signupDetailSectionBuilder_ = null;
      }
      if (refreshSignupResultSectionBuilder_ == null) {
        refreshSignupResultSection_ = null;
      } else {
        refreshSignupResultSection_ = null;
        refreshSignupResultSectionBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelAuthorizationResponse_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse build() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse result = new cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse(this);
      result.errorCode_ = errorCode_;
      result.errorMessage_ = errorMessage_;
      if (bindingListSectionBuilder_ == null) {
        result.bindingListSection_ = bindingListSection_;
      } else {
        result.bindingListSection_ = bindingListSectionBuilder_.build();
      }
      if (authorizationListSectionBuilder_ == null) {
        result.authorizationListSection_ = authorizationListSection_;
      } else {
        result.authorizationListSection_ = authorizationListSectionBuilder_.build();
      }
      if (signupResultSectionBuilder_ == null) {
        result.signupResultSection_ = signupResultSection_;
      } else {
        result.signupResultSection_ = signupResultSectionBuilder_.build();
      }
      if (signupDetailSectionBuilder_ == null) {
        result.signupDetailSection_ = signupDetailSection_;
      } else {
        result.signupDetailSection_ = signupDetailSectionBuilder_.build();
      }
      if (refreshSignupResultSectionBuilder_ == null) {
        result.refreshSignupResultSection_ = refreshSignupResultSection_;
      } else {
        result.refreshSignupResultSection_ = refreshSignupResultSectionBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse.getDefaultInstance()) return this;
      if (!other.getErrorCode().isEmpty()) {
        errorCode_ = other.errorCode_;
        onChanged();
      }
      if (!other.getErrorMessage().isEmpty()) {
        errorMessage_ = other.errorMessage_;
        onChanged();
      }
      if (other.hasBindingListSection()) {
        mergeBindingListSection(other.getBindingListSection());
      }
      if (other.hasAuthorizationListSection()) {
        mergeAuthorizationListSection(other.getAuthorizationListSection());
      }
      if (other.hasSignupResultSection()) {
        mergeSignupResultSection(other.getSignupResultSection());
      }
      if (other.hasSignupDetailSection()) {
        mergeSignupDetailSection(other.getSignupDetailSection());
      }
      if (other.hasRefreshSignupResultSection()) {
        mergeRefreshSignupResultSection(other.getRefreshSignupResultSection());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object errorCode_ = "";
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return The errorCode.
     */
    public java.lang.String getErrorCode() {
      java.lang.Object ref = errorCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return The bytes for errorCode.
     */
    public com.google.protobuf.ByteString
        getErrorCodeBytes() {
      java.lang.Object ref = errorCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @param value The errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorCode() {
      
      errorCode_ = getDefaultInstance().getErrorCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @param value The bytes for errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object errorMessage_ = "";
    /**
     * <pre>
     * （必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return The errorMessage.
     */
    public java.lang.String getErrorMessage() {
      java.lang.Object ref = errorMessage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorMessage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return The bytes for errorMessage.
     */
    public com.google.protobuf.ByteString
        getErrorMessageBytes() {
      java.lang.Object ref = errorMessage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorMessage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @param value The errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessage(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorMessage_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorMessage() {
      
      errorMessage_ = getDefaultInstance().getErrorMessage();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @param value The bytes for errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorMessage_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.BindingListSection bindingListSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.BindingListSection, cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.BindingListSectionOrBuilder> bindingListSectionBuilder_;
    /**
     * <pre>
     * （可选）渠道绑定关系信息
     * </pre>
     *
     * <code>.channel.BindingListSection binding_list_section = 3;</code>
     * @return Whether the bindingListSection field is set.
     */
    public boolean hasBindingListSection() {
      return bindingListSectionBuilder_ != null || bindingListSection_ != null;
    }
    /**
     * <pre>
     * （可选）渠道绑定关系信息
     * </pre>
     *
     * <code>.channel.BindingListSection binding_list_section = 3;</code>
     * @return The bindingListSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.BindingListSection getBindingListSection() {
      if (bindingListSectionBuilder_ == null) {
        return bindingListSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.getDefaultInstance() : bindingListSection_;
      } else {
        return bindingListSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）渠道绑定关系信息
     * </pre>
     *
     * <code>.channel.BindingListSection binding_list_section = 3;</code>
     */
    public Builder setBindingListSection(cn.hexcloud.pbis.common.service.facade.channel.BindingListSection value) {
      if (bindingListSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        bindingListSection_ = value;
        onChanged();
      } else {
        bindingListSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道绑定关系信息
     * </pre>
     *
     * <code>.channel.BindingListSection binding_list_section = 3;</code>
     */
    public Builder setBindingListSection(
        cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.Builder builderForValue) {
      if (bindingListSectionBuilder_ == null) {
        bindingListSection_ = builderForValue.build();
        onChanged();
      } else {
        bindingListSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道绑定关系信息
     * </pre>
     *
     * <code>.channel.BindingListSection binding_list_section = 3;</code>
     */
    public Builder mergeBindingListSection(cn.hexcloud.pbis.common.service.facade.channel.BindingListSection value) {
      if (bindingListSectionBuilder_ == null) {
        if (bindingListSection_ != null) {
          bindingListSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.newBuilder(bindingListSection_).mergeFrom(value).buildPartial();
        } else {
          bindingListSection_ = value;
        }
        onChanged();
      } else {
        bindingListSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道绑定关系信息
     * </pre>
     *
     * <code>.channel.BindingListSection binding_list_section = 3;</code>
     */
    public Builder clearBindingListSection() {
      if (bindingListSectionBuilder_ == null) {
        bindingListSection_ = null;
        onChanged();
      } else {
        bindingListSection_ = null;
        bindingListSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道绑定关系信息
     * </pre>
     *
     * <code>.channel.BindingListSection binding_list_section = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.Builder getBindingListSectionBuilder() {
      
      onChanged();
      return getBindingListSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）渠道绑定关系信息
     * </pre>
     *
     * <code>.channel.BindingListSection binding_list_section = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.BindingListSectionOrBuilder getBindingListSectionOrBuilder() {
      if (bindingListSectionBuilder_ != null) {
        return bindingListSectionBuilder_.getMessageOrBuilder();
      } else {
        return bindingListSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.getDefaultInstance() : bindingListSection_;
      }
    }
    /**
     * <pre>
     * （可选）渠道绑定关系信息
     * </pre>
     *
     * <code>.channel.BindingListSection binding_list_section = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.BindingListSection, cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.BindingListSectionOrBuilder> 
        getBindingListSectionFieldBuilder() {
      if (bindingListSectionBuilder_ == null) {
        bindingListSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.BindingListSection, cn.hexcloud.pbis.common.service.facade.channel.BindingListSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.BindingListSectionOrBuilder>(
                getBindingListSection(),
                getParentForChildren(),
                isClean());
        bindingListSection_ = null;
      }
      return bindingListSectionBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection authorizationListSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSectionOrBuilder> authorizationListSectionBuilder_;
    /**
     * <pre>
     * （可选）渠道签约授权信息
     * </pre>
     *
     * <code>.channel.AuthorizationListSection authorization_list_section = 4;</code>
     * @return Whether the authorizationListSection field is set.
     */
    public boolean hasAuthorizationListSection() {
      return authorizationListSectionBuilder_ != null || authorizationListSection_ != null;
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息
     * </pre>
     *
     * <code>.channel.AuthorizationListSection authorization_list_section = 4;</code>
     * @return The authorizationListSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection getAuthorizationListSection() {
      if (authorizationListSectionBuilder_ == null) {
        return authorizationListSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.getDefaultInstance() : authorizationListSection_;
      } else {
        return authorizationListSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息
     * </pre>
     *
     * <code>.channel.AuthorizationListSection authorization_list_section = 4;</code>
     */
    public Builder setAuthorizationListSection(cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection value) {
      if (authorizationListSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        authorizationListSection_ = value;
        onChanged();
      } else {
        authorizationListSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息
     * </pre>
     *
     * <code>.channel.AuthorizationListSection authorization_list_section = 4;</code>
     */
    public Builder setAuthorizationListSection(
        cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.Builder builderForValue) {
      if (authorizationListSectionBuilder_ == null) {
        authorizationListSection_ = builderForValue.build();
        onChanged();
      } else {
        authorizationListSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息
     * </pre>
     *
     * <code>.channel.AuthorizationListSection authorization_list_section = 4;</code>
     */
    public Builder mergeAuthorizationListSection(cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection value) {
      if (authorizationListSectionBuilder_ == null) {
        if (authorizationListSection_ != null) {
          authorizationListSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.newBuilder(authorizationListSection_).mergeFrom(value).buildPartial();
        } else {
          authorizationListSection_ = value;
        }
        onChanged();
      } else {
        authorizationListSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息
     * </pre>
     *
     * <code>.channel.AuthorizationListSection authorization_list_section = 4;</code>
     */
    public Builder clearAuthorizationListSection() {
      if (authorizationListSectionBuilder_ == null) {
        authorizationListSection_ = null;
        onChanged();
      } else {
        authorizationListSection_ = null;
        authorizationListSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息
     * </pre>
     *
     * <code>.channel.AuthorizationListSection authorization_list_section = 4;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.Builder getAuthorizationListSectionBuilder() {
      
      onChanged();
      return getAuthorizationListSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息
     * </pre>
     *
     * <code>.channel.AuthorizationListSection authorization_list_section = 4;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSectionOrBuilder getAuthorizationListSectionOrBuilder() {
      if (authorizationListSectionBuilder_ != null) {
        return authorizationListSectionBuilder_.getMessageOrBuilder();
      } else {
        return authorizationListSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.getDefaultInstance() : authorizationListSection_;
      }
    }
    /**
     * <pre>
     * （可选）渠道签约授权信息
     * </pre>
     *
     * <code>.channel.AuthorizationListSection authorization_list_section = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSectionOrBuilder> 
        getAuthorizationListSectionFieldBuilder() {
      if (authorizationListSectionBuilder_ == null) {
        authorizationListSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSectionOrBuilder>(
                getAuthorizationListSection(),
                getParentForChildren(),
                isClean());
        authorizationListSection_ = null;
      }
      return authorizationListSectionBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection signupResultSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection, cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SignupResultSectionOrBuilder> signupResultSectionBuilder_;
    /**
     * <pre>
     * （可选）渠道签约结果
     * </pre>
     *
     * <code>.channel.SignupResultSection signup_result_section = 5;</code>
     * @return Whether the signupResultSection field is set.
     */
    public boolean hasSignupResultSection() {
      return signupResultSectionBuilder_ != null || signupResultSection_ != null;
    }
    /**
     * <pre>
     * （可选）渠道签约结果
     * </pre>
     *
     * <code>.channel.SignupResultSection signup_result_section = 5;</code>
     * @return The signupResultSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection getSignupResultSection() {
      if (signupResultSectionBuilder_ == null) {
        return signupResultSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection.getDefaultInstance() : signupResultSection_;
      } else {
        return signupResultSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）渠道签约结果
     * </pre>
     *
     * <code>.channel.SignupResultSection signup_result_section = 5;</code>
     */
    public Builder setSignupResultSection(cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection value) {
      if (signupResultSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        signupResultSection_ = value;
        onChanged();
      } else {
        signupResultSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约结果
     * </pre>
     *
     * <code>.channel.SignupResultSection signup_result_section = 5;</code>
     */
    public Builder setSignupResultSection(
        cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection.Builder builderForValue) {
      if (signupResultSectionBuilder_ == null) {
        signupResultSection_ = builderForValue.build();
        onChanged();
      } else {
        signupResultSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约结果
     * </pre>
     *
     * <code>.channel.SignupResultSection signup_result_section = 5;</code>
     */
    public Builder mergeSignupResultSection(cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection value) {
      if (signupResultSectionBuilder_ == null) {
        if (signupResultSection_ != null) {
          signupResultSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection.newBuilder(signupResultSection_).mergeFrom(value).buildPartial();
        } else {
          signupResultSection_ = value;
        }
        onChanged();
      } else {
        signupResultSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约结果
     * </pre>
     *
     * <code>.channel.SignupResultSection signup_result_section = 5;</code>
     */
    public Builder clearSignupResultSection() {
      if (signupResultSectionBuilder_ == null) {
        signupResultSection_ = null;
        onChanged();
      } else {
        signupResultSection_ = null;
        signupResultSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约结果
     * </pre>
     *
     * <code>.channel.SignupResultSection signup_result_section = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection.Builder getSignupResultSectionBuilder() {
      
      onChanged();
      return getSignupResultSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）渠道签约结果
     * </pre>
     *
     * <code>.channel.SignupResultSection signup_result_section = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SignupResultSectionOrBuilder getSignupResultSectionOrBuilder() {
      if (signupResultSectionBuilder_ != null) {
        return signupResultSectionBuilder_.getMessageOrBuilder();
      } else {
        return signupResultSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection.getDefaultInstance() : signupResultSection_;
      }
    }
    /**
     * <pre>
     * （可选）渠道签约结果
     * </pre>
     *
     * <code>.channel.SignupResultSection signup_result_section = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection, cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SignupResultSectionOrBuilder> 
        getSignupResultSectionFieldBuilder() {
      if (signupResultSectionBuilder_ == null) {
        signupResultSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection, cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SignupResultSectionOrBuilder>(
                getSignupResultSection(),
                getParentForChildren(),
                isClean());
        signupResultSection_ = null;
      }
      return signupResultSectionBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection signupDetailSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection, cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSectionOrBuilder> signupDetailSectionBuilder_;
    /**
     * <pre>
     * （可选）渠道签约详情
     * </pre>
     *
     * <code>.channel.SignupDetailSection signup_detail_section = 6;</code>
     * @return Whether the signupDetailSection field is set.
     */
    public boolean hasSignupDetailSection() {
      return signupDetailSectionBuilder_ != null || signupDetailSection_ != null;
    }
    /**
     * <pre>
     * （可选）渠道签约详情
     * </pre>
     *
     * <code>.channel.SignupDetailSection signup_detail_section = 6;</code>
     * @return The signupDetailSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection getSignupDetailSection() {
      if (signupDetailSectionBuilder_ == null) {
        return signupDetailSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection.getDefaultInstance() : signupDetailSection_;
      } else {
        return signupDetailSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）渠道签约详情
     * </pre>
     *
     * <code>.channel.SignupDetailSection signup_detail_section = 6;</code>
     */
    public Builder setSignupDetailSection(cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection value) {
      if (signupDetailSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        signupDetailSection_ = value;
        onChanged();
      } else {
        signupDetailSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约详情
     * </pre>
     *
     * <code>.channel.SignupDetailSection signup_detail_section = 6;</code>
     */
    public Builder setSignupDetailSection(
        cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection.Builder builderForValue) {
      if (signupDetailSectionBuilder_ == null) {
        signupDetailSection_ = builderForValue.build();
        onChanged();
      } else {
        signupDetailSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约详情
     * </pre>
     *
     * <code>.channel.SignupDetailSection signup_detail_section = 6;</code>
     */
    public Builder mergeSignupDetailSection(cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection value) {
      if (signupDetailSectionBuilder_ == null) {
        if (signupDetailSection_ != null) {
          signupDetailSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection.newBuilder(signupDetailSection_).mergeFrom(value).buildPartial();
        } else {
          signupDetailSection_ = value;
        }
        onChanged();
      } else {
        signupDetailSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约详情
     * </pre>
     *
     * <code>.channel.SignupDetailSection signup_detail_section = 6;</code>
     */
    public Builder clearSignupDetailSection() {
      if (signupDetailSectionBuilder_ == null) {
        signupDetailSection_ = null;
        onChanged();
      } else {
        signupDetailSection_ = null;
        signupDetailSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约详情
     * </pre>
     *
     * <code>.channel.SignupDetailSection signup_detail_section = 6;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection.Builder getSignupDetailSectionBuilder() {
      
      onChanged();
      return getSignupDetailSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）渠道签约详情
     * </pre>
     *
     * <code>.channel.SignupDetailSection signup_detail_section = 6;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSectionOrBuilder getSignupDetailSectionOrBuilder() {
      if (signupDetailSectionBuilder_ != null) {
        return signupDetailSectionBuilder_.getMessageOrBuilder();
      } else {
        return signupDetailSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection.getDefaultInstance() : signupDetailSection_;
      }
    }
    /**
     * <pre>
     * （可选）渠道签约详情
     * </pre>
     *
     * <code>.channel.SignupDetailSection signup_detail_section = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection, cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSectionOrBuilder> 
        getSignupDetailSectionFieldBuilder() {
      if (signupDetailSectionBuilder_ == null) {
        signupDetailSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection, cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSectionOrBuilder>(
                getSignupDetailSection(),
                getParentForChildren(),
                isClean());
        signupDetailSection_ = null;
      }
      return signupDetailSectionBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection refreshSignupResultSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSectionOrBuilder> refreshSignupResultSectionBuilder_;
    /**
     * <pre>
     * （可选）渠道签约状态刷新结果
     * </pre>
     *
     * <code>.channel.RefreshSignupResultSection refresh_signup_result_section = 7;</code>
     * @return Whether the refreshSignupResultSection field is set.
     */
    public boolean hasRefreshSignupResultSection() {
      return refreshSignupResultSectionBuilder_ != null || refreshSignupResultSection_ != null;
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新结果
     * </pre>
     *
     * <code>.channel.RefreshSignupResultSection refresh_signup_result_section = 7;</code>
     * @return The refreshSignupResultSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection getRefreshSignupResultSection() {
      if (refreshSignupResultSectionBuilder_ == null) {
        return refreshSignupResultSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.getDefaultInstance() : refreshSignupResultSection_;
      } else {
        return refreshSignupResultSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新结果
     * </pre>
     *
     * <code>.channel.RefreshSignupResultSection refresh_signup_result_section = 7;</code>
     */
    public Builder setRefreshSignupResultSection(cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection value) {
      if (refreshSignupResultSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        refreshSignupResultSection_ = value;
        onChanged();
      } else {
        refreshSignupResultSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新结果
     * </pre>
     *
     * <code>.channel.RefreshSignupResultSection refresh_signup_result_section = 7;</code>
     */
    public Builder setRefreshSignupResultSection(
        cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.Builder builderForValue) {
      if (refreshSignupResultSectionBuilder_ == null) {
        refreshSignupResultSection_ = builderForValue.build();
        onChanged();
      } else {
        refreshSignupResultSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新结果
     * </pre>
     *
     * <code>.channel.RefreshSignupResultSection refresh_signup_result_section = 7;</code>
     */
    public Builder mergeRefreshSignupResultSection(cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection value) {
      if (refreshSignupResultSectionBuilder_ == null) {
        if (refreshSignupResultSection_ != null) {
          refreshSignupResultSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.newBuilder(refreshSignupResultSection_).mergeFrom(value).buildPartial();
        } else {
          refreshSignupResultSection_ = value;
        }
        onChanged();
      } else {
        refreshSignupResultSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新结果
     * </pre>
     *
     * <code>.channel.RefreshSignupResultSection refresh_signup_result_section = 7;</code>
     */
    public Builder clearRefreshSignupResultSection() {
      if (refreshSignupResultSectionBuilder_ == null) {
        refreshSignupResultSection_ = null;
        onChanged();
      } else {
        refreshSignupResultSection_ = null;
        refreshSignupResultSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新结果
     * </pre>
     *
     * <code>.channel.RefreshSignupResultSection refresh_signup_result_section = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.Builder getRefreshSignupResultSectionBuilder() {
      
      onChanged();
      return getRefreshSignupResultSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新结果
     * </pre>
     *
     * <code>.channel.RefreshSignupResultSection refresh_signup_result_section = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSectionOrBuilder getRefreshSignupResultSectionOrBuilder() {
      if (refreshSignupResultSectionBuilder_ != null) {
        return refreshSignupResultSectionBuilder_.getMessageOrBuilder();
      } else {
        return refreshSignupResultSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.getDefaultInstance() : refreshSignupResultSection_;
      }
    }
    /**
     * <pre>
     * （可选）渠道签约状态刷新结果
     * </pre>
     *
     * <code>.channel.RefreshSignupResultSection refresh_signup_result_section = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSectionOrBuilder> 
        getRefreshSignupResultSectionFieldBuilder() {
      if (refreshSignupResultSectionBuilder_ == null) {
        refreshSignupResultSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSectionOrBuilder>(
                getRefreshSignupResultSection(),
                getParentForChildren(),
                isClean());
        refreshSignupResultSection_ = null;
      }
      return refreshSignupResultSectionBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.ChannelAuthorizationResponse)
  }

  // @@protoc_insertion_point(class_scope:channel.ChannelAuthorizationResponse)
  private static final cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ChannelAuthorizationResponse>
      PARSER = new com.google.protobuf.AbstractParser<ChannelAuthorizationResponse>() {
    @java.lang.Override
    public ChannelAuthorizationResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ChannelAuthorizationResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ChannelAuthorizationResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ChannelAuthorizationResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

