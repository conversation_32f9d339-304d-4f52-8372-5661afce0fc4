// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ChannelAuthorizationResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ChannelAuthorizationResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  java.lang.String getErrorCode();
  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  com.google.protobuf.ByteString
      getErrorCodeBytes();

  /**
   * <pre>
   * （必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  java.lang.String getErrorMessage();
  /**
   * <pre>
   * （必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  com.google.protobuf.ByteString
      getErrorMessageBytes();

  /**
   * <pre>
   * （可选）渠道绑定关系信息
   * </pre>
   *
   * <code>.channel.BindingListSection binding_list_section = 3;</code>
   * @return Whether the bindingListSection field is set.
   */
  boolean hasBindingListSection();
  /**
   * <pre>
   * （可选）渠道绑定关系信息
   * </pre>
   *
   * <code>.channel.BindingListSection binding_list_section = 3;</code>
   * @return The bindingListSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.BindingListSection getBindingListSection();
  /**
   * <pre>
   * （可选）渠道绑定关系信息
   * </pre>
   *
   * <code>.channel.BindingListSection binding_list_section = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.BindingListSectionOrBuilder getBindingListSectionOrBuilder();

  /**
   * <pre>
   * （可选）渠道签约授权信息
   * </pre>
   *
   * <code>.channel.AuthorizationListSection authorization_list_section = 4;</code>
   * @return Whether the authorizationListSection field is set.
   */
  boolean hasAuthorizationListSection();
  /**
   * <pre>
   * （可选）渠道签约授权信息
   * </pre>
   *
   * <code>.channel.AuthorizationListSection authorization_list_section = 4;</code>
   * @return The authorizationListSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSection getAuthorizationListSection();
  /**
   * <pre>
   * （可选）渠道签约授权信息
   * </pre>
   *
   * <code>.channel.AuthorizationListSection authorization_list_section = 4;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.AuthorizationListSectionOrBuilder getAuthorizationListSectionOrBuilder();

  /**
   * <pre>
   * （可选）渠道签约结果
   * </pre>
   *
   * <code>.channel.SignupResultSection signup_result_section = 5;</code>
   * @return Whether the signupResultSection field is set.
   */
  boolean hasSignupResultSection();
  /**
   * <pre>
   * （可选）渠道签约结果
   * </pre>
   *
   * <code>.channel.SignupResultSection signup_result_section = 5;</code>
   * @return The signupResultSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.SignupResultSection getSignupResultSection();
  /**
   * <pre>
   * （可选）渠道签约结果
   * </pre>
   *
   * <code>.channel.SignupResultSection signup_result_section = 5;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.SignupResultSectionOrBuilder getSignupResultSectionOrBuilder();

  /**
   * <pre>
   * （可选）渠道签约详情
   * </pre>
   *
   * <code>.channel.SignupDetailSection signup_detail_section = 6;</code>
   * @return Whether the signupDetailSection field is set.
   */
  boolean hasSignupDetailSection();
  /**
   * <pre>
   * （可选）渠道签约详情
   * </pre>
   *
   * <code>.channel.SignupDetailSection signup_detail_section = 6;</code>
   * @return The signupDetailSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSection getSignupDetailSection();
  /**
   * <pre>
   * （可选）渠道签约详情
   * </pre>
   *
   * <code>.channel.SignupDetailSection signup_detail_section = 6;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.SignupDetailSectionOrBuilder getSignupDetailSectionOrBuilder();

  /**
   * <pre>
   * （可选）渠道签约状态刷新结果
   * </pre>
   *
   * <code>.channel.RefreshSignupResultSection refresh_signup_result_section = 7;</code>
   * @return Whether the refreshSignupResultSection field is set.
   */
  boolean hasRefreshSignupResultSection();
  /**
   * <pre>
   * （可选）渠道签约状态刷新结果
   * </pre>
   *
   * <code>.channel.RefreshSignupResultSection refresh_signup_result_section = 7;</code>
   * @return The refreshSignupResultSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection getRefreshSignupResultSection();
  /**
   * <pre>
   * （可选）渠道签约状态刷新结果
   * </pre>
   *
   * <code>.channel.RefreshSignupResultSection refresh_signup_result_section = 7;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSectionOrBuilder getRefreshSignupResultSectionOrBuilder();
}
