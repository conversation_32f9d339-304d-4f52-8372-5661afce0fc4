// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道基础服务请求信息
 * </pre>
 *
 * Protobuf type {@code channel.ChannelInfraRequest}
 */
public final class ChannelInfraRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.ChannelInfraRequest)
    ChannelInfraRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ChannelInfraRequest.newBuilder() to construct.
  private ChannelInfraRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ChannelInfraRequest() {
    action_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ChannelInfraRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ChannelInfraRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            action_ = s;
            break;
          }
          case 18: {
            cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.Builder subBuilder = null;
            if (sendSmsCodeSection_ != null) {
              subBuilder = sendSmsCodeSection_.toBuilder();
            }
            sendSmsCodeSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(sendSmsCodeSection_);
              sendSmsCodeSection_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelInfraRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelInfraRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest.Builder.class);
  }

  public static final int ACTION_FIELD_NUMBER = 1;
  private volatile java.lang.Object action_;
  /**
   * <pre>
   * （必传）业务操作
   * </pre>
   *
   * <code>string action = 1;</code>
   * @return The action.
   */
  @java.lang.Override
  public java.lang.String getAction() {
    java.lang.Object ref = action_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      action_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）业务操作
   * </pre>
   *
   * <code>string action = 1;</code>
   * @return The bytes for action.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getActionBytes() {
    java.lang.Object ref = action_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      action_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SEND_SMS_CODE_SECTION_FIELD_NUMBER = 2;
  private cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection sendSmsCodeSection_;
  /**
   * <pre>
   * （可选）短信验证码请求信息
   * </pre>
   *
   * <code>.channel.SendSMSCodeSection send_sms_code_section = 2;</code>
   * @return Whether the sendSmsCodeSection field is set.
   */
  @java.lang.Override
  public boolean hasSendSmsCodeSection() {
    return sendSmsCodeSection_ != null;
  }
  /**
   * <pre>
   * （可选）短信验证码请求信息
   * </pre>
   *
   * <code>.channel.SendSMSCodeSection send_sms_code_section = 2;</code>
   * @return The sendSmsCodeSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection getSendSmsCodeSection() {
    return sendSmsCodeSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.getDefaultInstance() : sendSmsCodeSection_;
  }
  /**
   * <pre>
   * （可选）短信验证码请求信息
   * </pre>
   *
   * <code>.channel.SendSMSCodeSection send_sms_code_section = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSectionOrBuilder getSendSmsCodeSectionOrBuilder() {
    return getSendSmsCodeSection();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getActionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, action_);
    }
    if (sendSmsCodeSection_ != null) {
      output.writeMessage(2, getSendSmsCodeSection());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getActionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, action_);
    }
    if (sendSmsCodeSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getSendSmsCodeSection());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest other = (cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest) obj;

    if (!getAction()
        .equals(other.getAction())) return false;
    if (hasSendSmsCodeSection() != other.hasSendSmsCodeSection()) return false;
    if (hasSendSmsCodeSection()) {
      if (!getSendSmsCodeSection()
          .equals(other.getSendSmsCodeSection())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ACTION_FIELD_NUMBER;
    hash = (53 * hash) + getAction().hashCode();
    if (hasSendSmsCodeSection()) {
      hash = (37 * hash) + SEND_SMS_CODE_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getSendSmsCodeSection().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道基础服务请求信息
   * </pre>
   *
   * Protobuf type {@code channel.ChannelInfraRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.ChannelInfraRequest)
      cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelInfraRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelInfraRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      action_ = "";

      if (sendSmsCodeSectionBuilder_ == null) {
        sendSmsCodeSection_ = null;
      } else {
        sendSmsCodeSection_ = null;
        sendSmsCodeSectionBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelInfraRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest build() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest result = new cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest(this);
      result.action_ = action_;
      if (sendSmsCodeSectionBuilder_ == null) {
        result.sendSmsCodeSection_ = sendSmsCodeSection_;
      } else {
        result.sendSmsCodeSection_ = sendSmsCodeSectionBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest.getDefaultInstance()) return this;
      if (!other.getAction().isEmpty()) {
        action_ = other.action_;
        onChanged();
      }
      if (other.hasSendSmsCodeSection()) {
        mergeSendSmsCodeSection(other.getSendSmsCodeSection());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object action_ = "";
    /**
     * <pre>
     * （必传）业务操作
     * </pre>
     *
     * <code>string action = 1;</code>
     * @return The action.
     */
    public java.lang.String getAction() {
      java.lang.Object ref = action_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        action_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）业务操作
     * </pre>
     *
     * <code>string action = 1;</code>
     * @return The bytes for action.
     */
    public com.google.protobuf.ByteString
        getActionBytes() {
      java.lang.Object ref = action_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        action_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）业务操作
     * </pre>
     *
     * <code>string action = 1;</code>
     * @param value The action to set.
     * @return This builder for chaining.
     */
    public Builder setAction(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      action_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）业务操作
     * </pre>
     *
     * <code>string action = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearAction() {
      
      action_ = getDefaultInstance().getAction();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）业务操作
     * </pre>
     *
     * <code>string action = 1;</code>
     * @param value The bytes for action to set.
     * @return This builder for chaining.
     */
    public Builder setActionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      action_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection sendSmsCodeSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection, cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSectionOrBuilder> sendSmsCodeSectionBuilder_;
    /**
     * <pre>
     * （可选）短信验证码请求信息
     * </pre>
     *
     * <code>.channel.SendSMSCodeSection send_sms_code_section = 2;</code>
     * @return Whether the sendSmsCodeSection field is set.
     */
    public boolean hasSendSmsCodeSection() {
      return sendSmsCodeSectionBuilder_ != null || sendSmsCodeSection_ != null;
    }
    /**
     * <pre>
     * （可选）短信验证码请求信息
     * </pre>
     *
     * <code>.channel.SendSMSCodeSection send_sms_code_section = 2;</code>
     * @return The sendSmsCodeSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection getSendSmsCodeSection() {
      if (sendSmsCodeSectionBuilder_ == null) {
        return sendSmsCodeSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.getDefaultInstance() : sendSmsCodeSection_;
      } else {
        return sendSmsCodeSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）短信验证码请求信息
     * </pre>
     *
     * <code>.channel.SendSMSCodeSection send_sms_code_section = 2;</code>
     */
    public Builder setSendSmsCodeSection(cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection value) {
      if (sendSmsCodeSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        sendSmsCodeSection_ = value;
        onChanged();
      } else {
        sendSmsCodeSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）短信验证码请求信息
     * </pre>
     *
     * <code>.channel.SendSMSCodeSection send_sms_code_section = 2;</code>
     */
    public Builder setSendSmsCodeSection(
        cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.Builder builderForValue) {
      if (sendSmsCodeSectionBuilder_ == null) {
        sendSmsCodeSection_ = builderForValue.build();
        onChanged();
      } else {
        sendSmsCodeSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）短信验证码请求信息
     * </pre>
     *
     * <code>.channel.SendSMSCodeSection send_sms_code_section = 2;</code>
     */
    public Builder mergeSendSmsCodeSection(cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection value) {
      if (sendSmsCodeSectionBuilder_ == null) {
        if (sendSmsCodeSection_ != null) {
          sendSmsCodeSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.newBuilder(sendSmsCodeSection_).mergeFrom(value).buildPartial();
        } else {
          sendSmsCodeSection_ = value;
        }
        onChanged();
      } else {
        sendSmsCodeSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）短信验证码请求信息
     * </pre>
     *
     * <code>.channel.SendSMSCodeSection send_sms_code_section = 2;</code>
     */
    public Builder clearSendSmsCodeSection() {
      if (sendSmsCodeSectionBuilder_ == null) {
        sendSmsCodeSection_ = null;
        onChanged();
      } else {
        sendSmsCodeSection_ = null;
        sendSmsCodeSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）短信验证码请求信息
     * </pre>
     *
     * <code>.channel.SendSMSCodeSection send_sms_code_section = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.Builder getSendSmsCodeSectionBuilder() {
      
      onChanged();
      return getSendSmsCodeSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）短信验证码请求信息
     * </pre>
     *
     * <code>.channel.SendSMSCodeSection send_sms_code_section = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSectionOrBuilder getSendSmsCodeSectionOrBuilder() {
      if (sendSmsCodeSectionBuilder_ != null) {
        return sendSmsCodeSectionBuilder_.getMessageOrBuilder();
      } else {
        return sendSmsCodeSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.getDefaultInstance() : sendSmsCodeSection_;
      }
    }
    /**
     * <pre>
     * （可选）短信验证码请求信息
     * </pre>
     *
     * <code>.channel.SendSMSCodeSection send_sms_code_section = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection, cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSectionOrBuilder> 
        getSendSmsCodeSectionFieldBuilder() {
      if (sendSmsCodeSectionBuilder_ == null) {
        sendSmsCodeSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection, cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSectionOrBuilder>(
                getSendSmsCodeSection(),
                getParentForChildren(),
                isClean());
        sendSmsCodeSection_ = null;
      }
      return sendSmsCodeSectionBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.ChannelInfraRequest)
  }

  // @@protoc_insertion_point(class_scope:channel.ChannelInfraRequest)
  private static final cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ChannelInfraRequest>
      PARSER = new com.google.protobuf.AbstractParser<ChannelInfraRequest>() {
    @java.lang.Override
    public ChannelInfraRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ChannelInfraRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ChannelInfraRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ChannelInfraRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

