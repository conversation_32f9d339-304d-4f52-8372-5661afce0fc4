// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ChannelInfraRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ChannelInfraRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）业务操作
   * </pre>
   *
   * <code>string action = 1;</code>
   * @return The action.
   */
  java.lang.String getAction();
  /**
   * <pre>
   * （必传）业务操作
   * </pre>
   *
   * <code>string action = 1;</code>
   * @return The bytes for action.
   */
  com.google.protobuf.ByteString
      getActionBytes();

  /**
   * <pre>
   * （可选）短信验证码请求信息
   * </pre>
   *
   * <code>.channel.SendSMSCodeSection send_sms_code_section = 2;</code>
   * @return Whether the sendSmsCodeSection field is set.
   */
  boolean hasSendSmsCodeSection();
  /**
   * <pre>
   * （可选）短信验证码请求信息
   * </pre>
   *
   * <code>.channel.SendSMSCodeSection send_sms_code_section = 2;</code>
   * @return The sendSmsCodeSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection getSendSmsCodeSection();
  /**
   * <pre>
   * （可选）短信验证码请求信息
   * </pre>
   *
   * <code>.channel.SendSMSCodeSection send_sms_code_section = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSectionOrBuilder getSendSmsCodeSectionOrBuilder();
}
