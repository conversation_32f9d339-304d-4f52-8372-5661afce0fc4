// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道基础服务响应信息
 * </pre>
 *
 * Protobuf type {@code channel.ChannelInfraResponse}
 */
public final class ChannelInfraResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.ChannelInfraResponse)
    ChannelInfraResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ChannelInfraResponse.newBuilder() to construct.
  private ChannelInfraResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ChannelInfraResponse() {
    errorCode_ = "";
    errorMessage_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ChannelInfraResponse();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ChannelInfraResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            errorCode_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            errorMessage_ = s;
            break;
          }
          case 26: {
            cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection.Builder subBuilder = null;
            if (smsResultSection_ != null) {
              subBuilder = smsResultSection_.toBuilder();
            }
            smsResultSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(smsResultSection_);
              smsResultSection_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelInfraResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelInfraResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse.Builder.class);
  }

  public static final int ERROR_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object errorCode_;
  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  @java.lang.Override
  public java.lang.String getErrorCode() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorCodeBytes() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ERROR_MESSAGE_FIELD_NUMBER = 2;
  private volatile java.lang.Object errorMessage_;
  /**
   * <pre>
   * （必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  @java.lang.Override
  public java.lang.String getErrorMessage() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorMessage_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorMessageBytes() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorMessage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SMS_RESULT_SECTION_FIELD_NUMBER = 3;
  private cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection smsResultSection_;
  /**
   * <pre>
   * （可选）短信验证码响应信息
   * </pre>
   *
   * <code>.channel.SMSResultSection sms_result_section = 3;</code>
   * @return Whether the smsResultSection field is set.
   */
  @java.lang.Override
  public boolean hasSmsResultSection() {
    return smsResultSection_ != null;
  }
  /**
   * <pre>
   * （可选）短信验证码响应信息
   * </pre>
   *
   * <code>.channel.SMSResultSection sms_result_section = 3;</code>
   * @return The smsResultSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection getSmsResultSection() {
    return smsResultSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection.getDefaultInstance() : smsResultSection_;
  }
  /**
   * <pre>
   * （可选）短信验证码响应信息
   * </pre>
   *
   * <code>.channel.SMSResultSection sms_result_section = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SMSResultSectionOrBuilder getSmsResultSectionOrBuilder() {
    return getSmsResultSection();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getErrorCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, errorMessage_);
    }
    if (smsResultSection_ != null) {
      output.writeMessage(3, getSmsResultSection());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getErrorCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, errorMessage_);
    }
    if (smsResultSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getSmsResultSection());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse other = (cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse) obj;

    if (!getErrorCode()
        .equals(other.getErrorCode())) return false;
    if (!getErrorMessage()
        .equals(other.getErrorMessage())) return false;
    if (hasSmsResultSection() != other.hasSmsResultSection()) return false;
    if (hasSmsResultSection()) {
      if (!getSmsResultSection()
          .equals(other.getSmsResultSection())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ERROR_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorCode().hashCode();
    hash = (37 * hash) + ERROR_MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorMessage().hashCode();
    if (hasSmsResultSection()) {
      hash = (37 * hash) + SMS_RESULT_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getSmsResultSection().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道基础服务响应信息
   * </pre>
   *
   * Protobuf type {@code channel.ChannelInfraResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.ChannelInfraResponse)
      cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelInfraResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelInfraResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      errorCode_ = "";

      errorMessage_ = "";

      if (smsResultSectionBuilder_ == null) {
        smsResultSection_ = null;
      } else {
        smsResultSection_ = null;
        smsResultSectionBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelInfraResponse_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse build() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse result = new cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse(this);
      result.errorCode_ = errorCode_;
      result.errorMessage_ = errorMessage_;
      if (smsResultSectionBuilder_ == null) {
        result.smsResultSection_ = smsResultSection_;
      } else {
        result.smsResultSection_ = smsResultSectionBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse.getDefaultInstance()) return this;
      if (!other.getErrorCode().isEmpty()) {
        errorCode_ = other.errorCode_;
        onChanged();
      }
      if (!other.getErrorMessage().isEmpty()) {
        errorMessage_ = other.errorMessage_;
        onChanged();
      }
      if (other.hasSmsResultSection()) {
        mergeSmsResultSection(other.getSmsResultSection());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object errorCode_ = "";
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return The errorCode.
     */
    public java.lang.String getErrorCode() {
      java.lang.Object ref = errorCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return The bytes for errorCode.
     */
    public com.google.protobuf.ByteString
        getErrorCodeBytes() {
      java.lang.Object ref = errorCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @param value The errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorCode() {
      
      errorCode_ = getDefaultInstance().getErrorCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @param value The bytes for errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object errorMessage_ = "";
    /**
     * <pre>
     * （必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return The errorMessage.
     */
    public java.lang.String getErrorMessage() {
      java.lang.Object ref = errorMessage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorMessage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return The bytes for errorMessage.
     */
    public com.google.protobuf.ByteString
        getErrorMessageBytes() {
      java.lang.Object ref = errorMessage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorMessage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @param value The errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessage(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorMessage_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorMessage() {
      
      errorMessage_ = getDefaultInstance().getErrorMessage();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @param value The bytes for errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorMessage_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection smsResultSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection, cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SMSResultSectionOrBuilder> smsResultSectionBuilder_;
    /**
     * <pre>
     * （可选）短信验证码响应信息
     * </pre>
     *
     * <code>.channel.SMSResultSection sms_result_section = 3;</code>
     * @return Whether the smsResultSection field is set.
     */
    public boolean hasSmsResultSection() {
      return smsResultSectionBuilder_ != null || smsResultSection_ != null;
    }
    /**
     * <pre>
     * （可选）短信验证码响应信息
     * </pre>
     *
     * <code>.channel.SMSResultSection sms_result_section = 3;</code>
     * @return The smsResultSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection getSmsResultSection() {
      if (smsResultSectionBuilder_ == null) {
        return smsResultSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection.getDefaultInstance() : smsResultSection_;
      } else {
        return smsResultSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）短信验证码响应信息
     * </pre>
     *
     * <code>.channel.SMSResultSection sms_result_section = 3;</code>
     */
    public Builder setSmsResultSection(cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection value) {
      if (smsResultSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        smsResultSection_ = value;
        onChanged();
      } else {
        smsResultSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）短信验证码响应信息
     * </pre>
     *
     * <code>.channel.SMSResultSection sms_result_section = 3;</code>
     */
    public Builder setSmsResultSection(
        cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection.Builder builderForValue) {
      if (smsResultSectionBuilder_ == null) {
        smsResultSection_ = builderForValue.build();
        onChanged();
      } else {
        smsResultSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）短信验证码响应信息
     * </pre>
     *
     * <code>.channel.SMSResultSection sms_result_section = 3;</code>
     */
    public Builder mergeSmsResultSection(cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection value) {
      if (smsResultSectionBuilder_ == null) {
        if (smsResultSection_ != null) {
          smsResultSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection.newBuilder(smsResultSection_).mergeFrom(value).buildPartial();
        } else {
          smsResultSection_ = value;
        }
        onChanged();
      } else {
        smsResultSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）短信验证码响应信息
     * </pre>
     *
     * <code>.channel.SMSResultSection sms_result_section = 3;</code>
     */
    public Builder clearSmsResultSection() {
      if (smsResultSectionBuilder_ == null) {
        smsResultSection_ = null;
        onChanged();
      } else {
        smsResultSection_ = null;
        smsResultSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）短信验证码响应信息
     * </pre>
     *
     * <code>.channel.SMSResultSection sms_result_section = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection.Builder getSmsResultSectionBuilder() {
      
      onChanged();
      return getSmsResultSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）短信验证码响应信息
     * </pre>
     *
     * <code>.channel.SMSResultSection sms_result_section = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SMSResultSectionOrBuilder getSmsResultSectionOrBuilder() {
      if (smsResultSectionBuilder_ != null) {
        return smsResultSectionBuilder_.getMessageOrBuilder();
      } else {
        return smsResultSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection.getDefaultInstance() : smsResultSection_;
      }
    }
    /**
     * <pre>
     * （可选）短信验证码响应信息
     * </pre>
     *
     * <code>.channel.SMSResultSection sms_result_section = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection, cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SMSResultSectionOrBuilder> 
        getSmsResultSectionFieldBuilder() {
      if (smsResultSectionBuilder_ == null) {
        smsResultSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection, cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.SMSResultSectionOrBuilder>(
                getSmsResultSection(),
                getParentForChildren(),
                isClean());
        smsResultSection_ = null;
      }
      return smsResultSectionBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.ChannelInfraResponse)
  }

  // @@protoc_insertion_point(class_scope:channel.ChannelInfraResponse)
  private static final cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ChannelInfraResponse>
      PARSER = new com.google.protobuf.AbstractParser<ChannelInfraResponse>() {
    @java.lang.Override
    public ChannelInfraResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ChannelInfraResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ChannelInfraResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ChannelInfraResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

