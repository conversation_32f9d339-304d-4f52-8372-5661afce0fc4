// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ChannelInfraResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ChannelInfraResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  java.lang.String getErrorCode();
  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  com.google.protobuf.ByteString
      getErrorCodeBytes();

  /**
   * <pre>
   * （必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  java.lang.String getErrorMessage();
  /**
   * <pre>
   * （必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  com.google.protobuf.ByteString
      getErrorMessageBytes();

  /**
   * <pre>
   * （可选）短信验证码响应信息
   * </pre>
   *
   * <code>.channel.SMSResultSection sms_result_section = 3;</code>
   * @return Whether the smsResultSection field is set.
   */
  boolean hasSmsResultSection();
  /**
   * <pre>
   * （可选）短信验证码响应信息
   * </pre>
   *
   * <code>.channel.SMSResultSection sms_result_section = 3;</code>
   * @return The smsResultSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.SMSResultSection getSmsResultSection();
  /**
   * <pre>
   * （可选）短信验证码响应信息
   * </pre>
   *
   * <code>.channel.SMSResultSection sms_result_section = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.SMSResultSectionOrBuilder getSmsResultSectionOrBuilder();
}
