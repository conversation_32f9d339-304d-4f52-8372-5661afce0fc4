package cn.hexcloud.pbis.common.service.facade.channel;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 * <pre>
 * 渠道管理服务
 * </pre>
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.40.1)",
    comments = "Source: ChannelManagement.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class ChannelManagementGrpc {

  private ChannelManagementGrpc() {}

  public static final String SERVICE_NAME = "channel.ChannelManagement";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigRequest,
      cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigResponse> getGetTokenPayConfigMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "getTokenPayConfig",
      requestType = cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigRequest.class,
      responseType = cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigRequest,
      cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigResponse> getGetTokenPayConfigMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigRequest, cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigResponse> getGetTokenPayConfigMethod;
    if ((getGetTokenPayConfigMethod = ChannelManagementGrpc.getGetTokenPayConfigMethod) == null) {
      synchronized (ChannelManagementGrpc.class) {
        if ((getGetTokenPayConfigMethod = ChannelManagementGrpc.getGetTokenPayConfigMethod) == null) {
          ChannelManagementGrpc.getGetTokenPayConfigMethod = getGetTokenPayConfigMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigRequest, cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "getTokenPayConfig"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ChannelManagementMethodDescriptorSupplier("getTokenPayConfig"))
              .build();
        }
      }
    }
    return getGetTokenPayConfigMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest,
      cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse> getGetAccessConfigMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "getAccessConfig",
      requestType = cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest.class,
      responseType = cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest,
      cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse> getGetAccessConfigMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest, cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse> getGetAccessConfigMethod;
    if ((getGetAccessConfigMethod = ChannelManagementGrpc.getGetAccessConfigMethod) == null) {
      synchronized (ChannelManagementGrpc.class) {
        if ((getGetAccessConfigMethod = ChannelManagementGrpc.getGetAccessConfigMethod) == null) {
          ChannelManagementGrpc.getGetAccessConfigMethod = getGetAccessConfigMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest, cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "getAccessConfig"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ChannelManagementMethodDescriptorSupplier("getAccessConfig"))
              .build();
        }
      }
    }
    return getGetAccessConfigMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest,
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse> getChannelAuthorizationMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "channelAuthorization",
      requestType = cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest.class,
      responseType = cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest,
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse> getChannelAuthorizationMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest, cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse> getChannelAuthorizationMethod;
    if ((getChannelAuthorizationMethod = ChannelManagementGrpc.getChannelAuthorizationMethod) == null) {
      synchronized (ChannelManagementGrpc.class) {
        if ((getChannelAuthorizationMethod = ChannelManagementGrpc.getChannelAuthorizationMethod) == null) {
          ChannelManagementGrpc.getChannelAuthorizationMethod = getChannelAuthorizationMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest, cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "channelAuthorization"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ChannelManagementMethodDescriptorSupplier("channelAuthorization"))
              .build();
        }
      }
    }
    return getChannelAuthorizationMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest,
      cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse> getChannelManagementMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "channelManagement",
      requestType = cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest.class,
      responseType = cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest,
      cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse> getChannelManagementMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest, cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse> getChannelManagementMethod;
    if ((getChannelManagementMethod = ChannelManagementGrpc.getChannelManagementMethod) == null) {
      synchronized (ChannelManagementGrpc.class) {
        if ((getChannelManagementMethod = ChannelManagementGrpc.getChannelManagementMethod) == null) {
          ChannelManagementGrpc.getChannelManagementMethod = getChannelManagementMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest, cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "channelManagement"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ChannelManagementMethodDescriptorSupplier("channelManagement"))
              .build();
        }
      }
    }
    return getChannelManagementMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest,
      cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse> getChannelScriptMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "channelScript",
      requestType = cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest.class,
      responseType = cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest,
      cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse> getChannelScriptMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse> getChannelScriptMethod;
    if ((getChannelScriptMethod = ChannelManagementGrpc.getChannelScriptMethod) == null) {
      synchronized (ChannelManagementGrpc.class) {
        if ((getChannelScriptMethod = ChannelManagementGrpc.getChannelScriptMethod) == null) {
          ChannelManagementGrpc.getChannelScriptMethod = getChannelScriptMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "channelScript"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ChannelManagementMethodDescriptorSupplier("channelScript"))
              .build();
        }
      }
    }
    return getChannelScriptMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest,
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse> getChannelAccessMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "channelAccess",
      requestType = cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest.class,
      responseType = cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest,
      cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse> getChannelAccessMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest, cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse> getChannelAccessMethod;
    if ((getChannelAccessMethod = ChannelManagementGrpc.getChannelAccessMethod) == null) {
      synchronized (ChannelManagementGrpc.class) {
        if ((getChannelAccessMethod = ChannelManagementGrpc.getChannelAccessMethod) == null) {
          ChannelManagementGrpc.getChannelAccessMethod = getChannelAccessMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest, cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "channelAccess"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ChannelManagementMethodDescriptorSupplier("channelAccess"))
              .build();
        }
      }
    }
    return getChannelAccessMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest,
      cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse> getChannelInfraMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "channelInfra",
      requestType = cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest.class,
      responseType = cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest,
      cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse> getChannelInfraMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest, cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse> getChannelInfraMethod;
    if ((getChannelInfraMethod = ChannelManagementGrpc.getChannelInfraMethod) == null) {
      synchronized (ChannelManagementGrpc.class) {
        if ((getChannelInfraMethod = ChannelManagementGrpc.getChannelInfraMethod) == null) {
          ChannelManagementGrpc.getChannelInfraMethod = getChannelInfraMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest, cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "channelInfra"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ChannelManagementMethodDescriptorSupplier("channelInfra"))
              .build();
        }
      }
    }
    return getChannelInfraMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static ChannelManagementStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ChannelManagementStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ChannelManagementStub>() {
        @java.lang.Override
        public ChannelManagementStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ChannelManagementStub(channel, callOptions);
        }
      };
    return ChannelManagementStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static ChannelManagementBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ChannelManagementBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ChannelManagementBlockingStub>() {
        @java.lang.Override
        public ChannelManagementBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ChannelManagementBlockingStub(channel, callOptions);
        }
      };
    return ChannelManagementBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static ChannelManagementFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ChannelManagementFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ChannelManagementFutureStub>() {
        @java.lang.Override
        public ChannelManagementFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ChannelManagementFutureStub(channel, callOptions);
        }
      };
    return ChannelManagementFutureStub.newStub(factory, channel);
  }

  /**
   * <pre>
   * 渠道管理服务
   * </pre>
   */
  public static abstract class ChannelManagementImplBase implements io.grpc.BindableService {

    /**
     * <pre>
     * 获取TokenPay配置
     * </pre>
     */
    public void getTokenPayConfig(cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetTokenPayConfigMethod(), responseObserver);
    }

    /**
     * <pre>
     * 获取密钥信息
     * </pre>
     */
    public void getAccessConfig(cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetAccessConfigMethod(), responseObserver);
    }

    /**
     * <pre>
     * 渠道签约授权管理接口
     * </pre>
     */
    public void channelAuthorization(cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getChannelAuthorizationMethod(), responseObserver);
    }

    /**
     * <pre>
     * 渠道管理接口
     * </pre>
     */
    public void channelManagement(cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getChannelManagementMethod(), responseObserver);
    }

    /**
     * <pre>
     * 渠道脚本接口
     * </pre>
     */
    public void channelScript(cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getChannelScriptMethod(), responseObserver);
    }

    /**
     * <pre>
     * 渠道签约授权管理接口
     * </pre>
     */
    public void channelAccess(cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getChannelAccessMethod(), responseObserver);
    }

    /**
     * <pre>
     * 短信验证码接口
     * </pre>
     */
    public void channelInfra(cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getChannelInfraMethod(), responseObserver);
    }

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getGetTokenPayConfigMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigRequest,
                cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigResponse>(
                  this, METHODID_GET_TOKEN_PAY_CONFIG)))
          .addMethod(
            getGetAccessConfigMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest,
                cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse>(
                  this, METHODID_GET_ACCESS_CONFIG)))
          .addMethod(
            getChannelAuthorizationMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest,
                cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse>(
                  this, METHODID_CHANNEL_AUTHORIZATION)))
          .addMethod(
            getChannelManagementMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest,
                cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse>(
                  this, METHODID_CHANNEL_MANAGEMENT)))
          .addMethod(
            getChannelScriptMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest,
                cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse>(
                  this, METHODID_CHANNEL_SCRIPT)))
          .addMethod(
            getChannelAccessMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest,
                cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse>(
                  this, METHODID_CHANNEL_ACCESS)))
          .addMethod(
            getChannelInfraMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest,
                cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse>(
                  this, METHODID_CHANNEL_INFRA)))
          .build();
    }
  }

  /**
   * <pre>
   * 渠道管理服务
   * </pre>
   */
  public static final class ChannelManagementStub extends io.grpc.stub.AbstractAsyncStub<ChannelManagementStub> {
    private ChannelManagementStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ChannelManagementStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ChannelManagementStub(channel, callOptions);
    }

    /**
     * <pre>
     * 获取TokenPay配置
     * </pre>
     */
    public void getTokenPayConfig(cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetTokenPayConfigMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 获取密钥信息
     * </pre>
     */
    public void getAccessConfig(cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetAccessConfigMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 渠道签约授权管理接口
     * </pre>
     */
    public void channelAuthorization(cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getChannelAuthorizationMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 渠道管理接口
     * </pre>
     */
    public void channelManagement(cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getChannelManagementMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 渠道脚本接口
     * </pre>
     */
    public void channelScript(cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getChannelScriptMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 渠道签约授权管理接口
     * </pre>
     */
    public void channelAccess(cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getChannelAccessMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 短信验证码接口
     * </pre>
     */
    public void channelInfra(cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getChannelInfraMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * <pre>
   * 渠道管理服务
   * </pre>
   */
  public static final class ChannelManagementBlockingStub extends io.grpc.stub.AbstractBlockingStub<ChannelManagementBlockingStub> {
    private ChannelManagementBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ChannelManagementBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ChannelManagementBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * 获取TokenPay配置
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigResponse getTokenPayConfig(cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetTokenPayConfigMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 获取密钥信息
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse getAccessConfig(cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetAccessConfigMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 渠道签约授权管理接口
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse channelAuthorization(cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getChannelAuthorizationMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 渠道管理接口
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse channelManagement(cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getChannelManagementMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 渠道脚本接口
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse channelScript(cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getChannelScriptMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 渠道签约授权管理接口
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse channelAccess(cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getChannelAccessMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 短信验证码接口
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse channelInfra(cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getChannelInfraMethod(), getCallOptions(), request);
    }
  }

  /**
   * <pre>
   * 渠道管理服务
   * </pre>
   */
  public static final class ChannelManagementFutureStub extends io.grpc.stub.AbstractFutureStub<ChannelManagementFutureStub> {
    private ChannelManagementFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected ChannelManagementFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ChannelManagementFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * 获取TokenPay配置
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigResponse> getTokenPayConfig(
        cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetTokenPayConfigMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 获取密钥信息
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse> getAccessConfig(
        cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetAccessConfigMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 渠道签约授权管理接口
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse> channelAuthorization(
        cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getChannelAuthorizationMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 渠道管理接口
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse> channelManagement(
        cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getChannelManagementMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 渠道脚本接口
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse> channelScript(
        cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getChannelScriptMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 渠道签约授权管理接口
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse> channelAccess(
        cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getChannelAccessMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 短信验证码接口
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse> channelInfra(
        cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getChannelInfraMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_GET_TOKEN_PAY_CONFIG = 0;
  private static final int METHODID_GET_ACCESS_CONFIG = 1;
  private static final int METHODID_CHANNEL_AUTHORIZATION = 2;
  private static final int METHODID_CHANNEL_MANAGEMENT = 3;
  private static final int METHODID_CHANNEL_SCRIPT = 4;
  private static final int METHODID_CHANNEL_ACCESS = 5;
  private static final int METHODID_CHANNEL_INFRA = 6;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final ChannelManagementImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(ChannelManagementImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_GET_TOKEN_PAY_CONFIG:
          serviceImpl.getTokenPayConfig((cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.GetTokenPayConfigResponse>) responseObserver);
          break;
        case METHODID_GET_ACCESS_CONFIG:
          serviceImpl.getAccessConfig((cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse>) responseObserver);
          break;
        case METHODID_CHANNEL_AUTHORIZATION:
          serviceImpl.channelAuthorization((cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.ChannelAuthorizationResponse>) responseObserver);
          break;
        case METHODID_CHANNEL_MANAGEMENT:
          serviceImpl.channelManagement((cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse>) responseObserver);
          break;
        case METHODID_CHANNEL_SCRIPT:
          serviceImpl.channelScript((cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse>) responseObserver);
          break;
        case METHODID_CHANNEL_ACCESS:
          serviceImpl.channelAccess((cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.ChannelAccessResponse>) responseObserver);
          break;
        case METHODID_CHANNEL_INFRA:
          serviceImpl.channelInfra((cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.channel.ChannelInfraResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class ChannelManagementBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    ChannelManagementBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("ChannelManagement");
    }
  }

  private static final class ChannelManagementFileDescriptorSupplier
      extends ChannelManagementBaseDescriptorSupplier {
    ChannelManagementFileDescriptorSupplier() {}
  }

  private static final class ChannelManagementMethodDescriptorSupplier
      extends ChannelManagementBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    ChannelManagementMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (ChannelManagementGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new ChannelManagementFileDescriptorSupplier())
              .addMethod(getGetTokenPayConfigMethod())
              .addMethod(getGetAccessConfigMethod())
              .addMethod(getChannelAuthorizationMethod())
              .addMethod(getChannelManagementMethod())
              .addMethod(getChannelScriptMethod())
              .addMethod(getChannelAccessMethod())
              .addMethod(getChannelInfraMethod())
              .build();
        }
      }
    }
    return result;
  }
}
