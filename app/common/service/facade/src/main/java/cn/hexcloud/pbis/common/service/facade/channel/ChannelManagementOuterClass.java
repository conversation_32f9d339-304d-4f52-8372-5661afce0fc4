// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public final class ChannelManagementOuterClass {
  private ChannelManagementOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ChannelScriptRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ChannelScriptRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ChannelScriptResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ChannelScriptResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ChannelScriptItem_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ChannelScriptItem_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ChannelScriptSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ChannelScriptSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_DelScriptSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_DelScriptSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ListScriptQuery_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ListScriptQuery_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ChannelAccessRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ChannelAccessRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ApplyTarget_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ApplyTarget_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_AppletsAccessSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_AppletsAccessSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_AppletsAccessItem_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_AppletsAccessItem_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ChannelAccessResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ChannelAccessResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_AccessReportSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_AccessReportSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_AccessReportItem_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_AccessReportItem_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_Access_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_Access_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ListAccessQuery_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ListAccessQuery_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_GetAccessConfigRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_GetAccessConfigRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_GetAccessConfigResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_GetAccessConfigResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_AccessConfig_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_AccessConfig_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ChannelManagementRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ChannelManagementRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ChannelManagementResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ChannelManagementResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ListChannelQuery_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ListChannelQuery_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_Channel_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_Channel_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ChannelAuthorizationRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ChannelAuthorizationRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ChannelAuthorizationResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ChannelAuthorizationResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ChannelInfraRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ChannelInfraRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ChannelInfraResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ChannelInfraResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ListBindingSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ListBindingSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_BindingListSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_BindingListSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_BindingItem_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_BindingItem_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_SwitchBindingSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_SwitchBindingSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_ListAuthorizationSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_ListAuthorizationSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_AuthorizationListSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_AuthorizationListSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_AuthorizationItem_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_AuthorizationItem_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_SignupSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_SignupSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_SignupResultSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_SignupResultSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_QuerySignupSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_QuerySignupSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_SignupDetailSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_SignupDetailSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_RefreshSignupSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_RefreshSignupSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_RefreshSignupResultSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_RefreshSignupResultSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_SignupStateItem_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_SignupStateItem_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_Location_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_Location_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_Contact_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_Contact_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_Attachment_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_Attachment_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_SendSMSCodeSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_SendSMSCodeSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_SMSResultSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_SMSResultSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_PayConfig_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_PayConfig_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_GetTokenPayConfigRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_GetTokenPayConfigRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_channel_GetTokenPayConfigResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_channel_GetTokenPayConfigResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\027ChannelManagement.proto\022\007channel\032\034goog" +
      "le/api/annotations.proto\032\034google/protobu" +
      "f/struct.proto\"\321\001\n\024ChannelScriptRequest\022" +
      "\016\n\006action\030\001 \001(\t\0223\n\021list_script_query\030\002 \001" +
      "(\0132\030.channel.ListScriptQuery\022=\n\026channel_" +
      "script_section\030\003 \001(\0132\035.channel.ChannelSc" +
      "riptSection\0225\n\022del_script_section\030\004 \001(\0132" +
      "\031.channel.DelScriptSection\"{\n\025ChannelScr" +
      "iptResponse\022\022\n\nerror_code\030\001 \001(\t\022\025\n\rerror" +
      "_message\030\002 \001(\t\0227\n\023channel_script_item\030\003 " +
      "\003(\0132\032.channel.ChannelScriptItem\"\204\001\n\021Chan" +
      "nelScriptItem\022\n\n\002id\030\001 \001(\005\022\024\n\014channel_cod" +
      "e\030\002 \001(\t\022\022\n\nscript_key\030\003 \001(\t\022\023\n\013script_te" +
      "xt\030\004 \001(\t\022\017\n\007version\030\005 \001(\t\022\023\n\013create_time" +
      "\030\006 \001(\t\"a\n\024ChannelScriptSection\022\n\n\002id\030\001 \001" +
      "(\005\022\024\n\014channel_code\030\002 \001(\t\022\022\n\nscript_key\030\003" +
      " \001(\t\022\023\n\013script_text\030\004 \001(\t\"6\n\020DelScriptSe" +
      "ction\022\n\n\002id\030\001 \001(\003\022\026\n\016del_cache_only\030\002 \001(" +
      "\t\"]\n\017ListScriptQuery\022\017\n\007enabled\030\001 \001(\t\022\024\n" +
      "\014channel_code\030\002 \001(\t\022\022\n\nscript_key\030\003 \001(\t\022" +
      "\017\n\007version\030\004 \001(\t\"\346\001\n\024ChannelAccessReques" +
      "t\022\016\n\006action\030\001 \001(\t\0223\n\021list_access_query\030\002" +
      " \001(\0132\030.channel.ListAccessQuery\022$\n\013list_a" +
      "ccess\030\003 \003(\0132\017.channel.Access\022$\n\013edit_acc" +
      "ess\030\004 \001(\0132\017.channel.Access\022=\n\026applets_ac" +
      "cess_section\030\005 \001(\0132\035.channel.AppletsAcce" +
      "ssSection\"3\n\013ApplyTarget\022\020\n\010store_id\030\001 \001" +
      "(\t\022\022\n\ncompany_id\030\002 \001(\t\"\200\001\n\024AppletsAccess" +
      "Section\022/\n\021apply_target_item\030\001 \003(\0132\024.cha" +
      "nnel.ApplyTarget\0227\n\023applets_access_item\030" +
      "\002 \003(\0132\032.channel.AppletsAccessItem\"N\n\021App" +
      "letsAccessItem\022\024\n\014channel_code\030\001 \001(\t\022#\n\n" +
      "props_item\030\002 \003(\0132\017.channel.Access\"\336\001\n\025Ch" +
      "annelAccessResponse\022\022\n\nerror_code\030\001 \001(\t\022" +
      "\025\n\rerror_message\030\002 \001(\t\022;\n\025access_report_" +
      "section\030\003 \001(\0132\034.channel.AccessReportSect" +
      "ion\022$\n\013access_item\030\004 \003(\0132\017.channel.Acces" +
      "s\0227\n\023applets_access_item\030\005 \003(\0132\032.channel" +
      ".AppletsAccessItem\"\226\001\n\023AccessReportSecti" +
      "on\022\022\n\npage_index\030\001 \001(\005\022\021\n\tpage_size\030\002 \001(" +
      "\005\022\022\n\npage_count\030\003 \001(\005\022\r\n\005total\030\004 \001(\003\0225\n\022" +
      "access_report_item\030\005 \003(\0132\031.channel.Acces" +
      "sReportItem\"\247\001\n\020AccessReportItem\022\020\n\010stor" +
      "e_id\030\001 \001(\t\022\022\n\ncompany_id\030\002 \001(\t\022\r\n\005level\030" +
      "\005 \001(\005\022\031\n\021channel_name_item\030\006 \001(\t\022\023\n\013upda" +
      "te_time\030\007 \001(\t\022\022\n\npartner_id\030\010 \001(\t\022\014\n\004nam" +
      "e\030\t \001(\t\022\014\n\004code\030\n \001(\t\"\264\003\n\006Access\022\024\n\014chan" +
      "nel_code\030\001 \001(\t\022\020\n\010apply_to\030\002 \001(\005\022+\n\rappl" +
      "y_targets\030\003 \003(\0132\024.channel.ApplyTarget\022\025\n" +
      "\rbusiness_code\030\004 \001(\t\022\023\n\013merchant_id\030\005 \001(" +
      "\t\022\016\n\006app_id\030\006 \001(\t\022\017\n\007app_key\030\007 \001(\t\022\022\n\nac" +
      "cess_key\030\010 \001(\t\022\014\n\004cert\030\t \001(\t\022\023\n\013private_" +
      "key\030\n \001(\t\022\022\n\npublic_key\030\013 \001(\t\022\023\n\013gateway" +
      "_url\030\014 \001(\t\022\030\n\020channelNameItems\030\r \001(\t\022\021\n\t" +
      "access_id\030\016 \001(\003\022\022\n\npartner_id\030\017 \001(\t\022\022\n\nc" +
      "ompany_id\030\020 \001(\t\022\020\n\010store_id\030\021 \001(\t\022\023\n\013ter" +
      "minal_id\030\022 \001(\t\022\023\n\013api_version\030\023 \001(\t\022\027\n\017s" +
      "ub_merchant_id\030\024 \001(\t\"\363\001\n\017ListAccessQuery" +
      "\022\024\n\014channel_code\030\001 \001(\t\022\022\n\npartner_id\030\002 \001" +
      "(\t\022\022\n\ncompany_id\030\003 \001(\t\022\020\n\010store_id\030\004 \001(\t" +
      "\022\025\n\rbusiness_code\030\005 \001(\t\022\r\n\005level\030\006 \001(\005\022\022" +
      "\n\npage_index\030\007 \001(\005\022\021\n\tpage_size\030\010 \001(\005\022\016\n" +
      "\006search\030\t \001(\t\022\025\n\rsearch_fields\030\n \001(\t\022\034\n\024" +
      "channel_sub_category\030\013 \001(\t\"k\n\026GetAccessC" +
      "onfigRequest\022\024\n\014channel_code\030\001 \001(\t\022\022\n\nco" +
      "mpany_id\030\002 \001(\t\022\020\n\010store_id\030\003 \001(\t\022\025\n\rbusi" +
      "ness_code\030\004 \001(\t\"r\n\027GetAccessConfigRespon" +
      "se\022\022\n\nerror_code\030\001 \001(\t\022\025\n\rerror_message\030" +
      "\002 \001(\t\022,\n\raccess_config\030\003 \001(\0132\025.channel.A" +
      "ccessConfig\"\315\001\n\014AccessConfig\022\023\n\013merchant" +
      "_id\030\001 \001(\t\022\016\n\006app_id\030\002 \001(\t\022\017\n\007app_key\030\003 \001" +
      "(\t\022\022\n\naccess_key\030\004 \001(\t\022\014\n\004cert\030\005 \001(\t\022\023\n\013" +
      "private_key\030\006 \001(\t\022\022\n\npublic_key\030\007 \001(\t\022\023\n" +
      "\013terminal_id\030\010 \001(\t\022\023\n\013gateway_url\030\t \001(\t\022" +
      "\022\n\nauth_token\030\n \001(\t\"\204\001\n\030ChannelManagemen" +
      "tRequest\022\016\n\006action\030\001 \001(\t\0225\n\022list_channel" +
      "_query\030\002 \001(\0132\031.channel.ListChannelQuery\022" +
      "!\n\007channel\030\003 \001(\0132\020.channel.Channel\"n\n\031Ch" +
      "annelManagementResponse\022\022\n\nerror_code\030\001 " +
      "\001(\t\022\025\n\rerror_message\030\002 \001(\t\022&\n\014channel_li" +
      "st\030\003 \003(\0132\020.channel.Channel\"u\n\020ListChanne" +
      "lQuery\022\030\n\020channel_category\030\001 \001(\t\022\023\n\013sear" +
      "ch_name\030\002 \001(\t\022\024\n\014channel_code\030\003 \001(\t\022\034\n\024c" +
      "hannel_sub_category\030\004 \001(\t\"\366\001\n\007Channel\022\024\n" +
      "\014channel_code\030\001 \001(\t\022\024\n\014channel_name\030\002 \001(" +
      "\t\022\024\n\014channel_logo\030\003 \001(\t\022\024\n\014channel_type\030" +
      "\004 \001(\t\022\030\n\020channel_category\030\005 \001(\t\022\023\n\013descr" +
      "iption\030\006 \001(\t\022\032\n\022channel_properties\030\007 \001(\t" +
      "\022\022\n\nchannel_id\030\010 \001(\003\022\026\n\016channel_labels\030\t" +
      " \001(\t\022\034\n\024channel_sub_category\030\n \001(\t\"\230\003\n\033C" +
      "hannelAuthorizationRequest\022\016\n\006action\030\001 \001" +
      "(\t\0229\n\024list_binding_section\030\002 \001(\0132\033.chann" +
      "el.ListBindingSection\022=\n\026switch_binding_" +
      "section\030\003 \001(\0132\035.channel.SwitchBindingSec" +
      "tion\022E\n\032list_authorization_section\030\004 \001(\013" +
      "2!.channel.ListAuthorizationSection\022.\n\016s" +
      "ignup_section\030\005 \001(\0132\026.channel.SignupSect" +
      "ion\0229\n\024query_signup_section\030\006 \001(\0132\033.chan" +
      "nel.QuerySignupSection\022=\n\026refresh_signup" +
      "_section\030\007 \001(\0132\035.channel.RefreshSignupSe" +
      "ction\"\221\003\n\034ChannelAuthorizationResponse\022\022" +
      "\n\nerror_code\030\001 \001(\t\022\025\n\rerror_message\030\002 \001(" +
      "\t\0229\n\024binding_list_section\030\003 \001(\0132\033.channe" +
      "l.BindingListSection\022E\n\032authorization_li" +
      "st_section\030\004 \001(\0132!.channel.Authorization" +
      "ListSection\022;\n\025signup_result_section\030\005 \001" +
      "(\0132\034.channel.SignupResultSection\022;\n\025sign" +
      "up_detail_section\030\006 \001(\0132\034.channel.Signup" +
      "DetailSection\022J\n\035refresh_signup_result_s" +
      "ection\030\007 \001(\0132#.channel.RefreshSignupResu" +
      "ltSection\"a\n\023ChannelInfraRequest\022\016\n\006acti" +
      "on\030\001 \001(\t\022:\n\025send_sms_code_section\030\002 \001(\0132" +
      "\033.channel.SendSMSCodeSection\"x\n\024ChannelI" +
      "nfraResponse\022\022\n\nerror_code\030\001 \001(\t\022\025\n\rerro" +
      "r_message\030\002 \001(\t\0225\n\022sms_result_section\030\003 " +
      "\001(\0132\031.channel.SMSResultSection\"n\n\022ListBi" +
      "ndingSection\022\030\n\020channel_category\030\001 \001(\t\022\017" +
      "\n\007enabled\030\002 \001(\t\022\026\n\016channel_labels\030\003 \001(\t\022" +
      "\025\n\rbusiness_code\030\004 \001(\t\"@\n\022BindingListSec" +
      "tion\022*\n\014binding_item\030\001 \003(\0132\024.channel.Bin" +
      "dingItem\"\307\001\n\013BindingItem\022\024\n\014channel_code" +
      "\030\001 \001(\t\022\024\n\014channel_name\030\002 \001(\t\022\025\n\rbusiness" +
      "_code\030\003 \001(\t\022\024\n\014channel_type\030\004 \001(\t\022\030\n\020cha" +
      "nnel_category\030\005 \001(\t\022\034\n\024channel_sub_categ" +
      "ory\030\006 \001(\t\022\026\n\016channel_labels\030\007 \001(\t\022\017\n\007ena" +
      "bled\030\010 \001(\t\"T\n\024SwitchBindingSection\022\024\n\014ch" +
      "annel_code\030\001 \001(\t\022\017\n\007enabled\030\002 \001(\t\022\025\n\rbus" +
      "iness_code\030\003 \001(\t\"\235\001\n\030ListAuthorizationSe" +
      "ction\022\024\n\014channel_code\030\001 \001(\t\022\024\n\014signup_st" +
      "ate\030\002 \001(\t\022\023\n\013grant_state\030\003 \001(\t\022\031\n\021comple" +
      "x_criterion\030\004 \001(\t\022\022\n\npage_index\030\005 \001(\005\022\021\n" +
      "\tpage_size\030\006 \001(\005\"\234\001\n\030AuthorizationListSe" +
      "ction\022\022\n\npage_index\030\001 \001(\005\022\021\n\tpage_size\030\002" +
      " \001(\005\022\022\n\npage_count\030\003 \001(\005\022\r\n\005total\030\004 \001(\003\022" +
      "6\n\022authorization_item\030\005 \003(\0132\032.channel.Au" +
      "thorizationItem\"\215\001\n\021AuthorizationItem\022\017\n" +
      "\007auth_id\030\001 \001(\005\022\024\n\014channel_code\030\002 \001(\t\022\023\n\013" +
      "merchant_id\030\003 \001(\t\022\021\n\tsignup_no\030\004 \001(\t\022\024\n\014" +
      "signup_state\030\005 \001(\t\022\023\n\013grant_state\030\006 \001(\t\"" +
      "\231\002\n\rSignupSection\022\024\n\014channel_code\030\001 \001(\t\022" +
      "\023\n\013merchant_id\030\002 \001(\t\022\031\n\021business_categor" +
      "y\030\003 \001(\t\022\024\n\014service_rate\030\004 \001(\001\022\022\n\nparty_n" +
      "ame\030\005 \001(\t\022)\n\016party_location\030\006 \001(\0132\021.chan" +
      "nel.Location\022!\n\007contact\030\007 \001(\0132\020.channel." +
      "Contact\022(\n\013attachments\030\010 \003(\0132\023.channel.A" +
      "ttachment\022\016\n\006sms_id\030\t \001(\t\022\020\n\010sms_code\030\n " +
      "\001(\t\"U\n\023SignupResultSection\022\021\n\tsignup_no\030" +
      "\001 \001(\t\022\024\n\014signup_state\030\002 \001(\t\022\025\n\rsignup_re" +
      "sult\030\003 \001(\t\"-\n\022QuerySignupSection\022\027\n\017chan" +
      "nel_auth_id\030\001 \001(\005\"\347\001\n\023SignupDetailSectio" +
      "n\022\024\n\014channel_code\030\001 \001(\t\022\023\n\013merchant_id\030\002" +
      " \001(\t\022\031\n\021business_category\030\003 \001(\t\022\022\n\nparty" +
      "_name\030\004 \001(\t\022)\n\016party_location\030\005 \001(\0132\021.ch" +
      "annel.Location\022!\n\007contact\030\006 \001(\0132\020.channe" +
      "l.Contact\022(\n\013attachments\030\007 \003(\0132\023.channel" +
      ".Attachment\"/\n\024RefreshSignupSection\022\027\n\017c" +
      "hannel_auth_id\030\001 \003(\005\"Q\n\032RefreshSignupRes" +
      "ultSection\0223\n\021signup_state_item\030\001 \003(\0132\030." +
      "channel.SignupStateItem\"W\n\017SignupStateIt" +
      "em\022\027\n\017channel_auth_id\030\001 \001(\005\022\024\n\014signup_st" +
      "ate\030\002 \001(\t\022\025\n\rsignup_result\030\003 \001(\t\"r\n\010Loca" +
      "tion\022\024\n\014country_code\030\001 \001(\t\022\025\n\rprovince_c" +
      "ode\030\002 \001(\t\022\021\n\tcity_code\030\003 \001(\t\022\025\n\rdistrict" +
      "_code\030\004 \001(\t\022\017\n\007address\030\005 \001(\t\"@\n\007Contact\022" +
      "\014\n\004name\030\001 \001(\t\022\r\n\005email\030\002 \001(\t\022\030\n\020cellphon" +
      "e_number\030\003 \001(\t\"J\n\nAttachment\022\014\n\004name\030\001 \001" +
      "(\t\022\020\n\010fileName\030\002 \001(\t\022\013\n\003url\030\003 \001(\t\022\017\n\007con" +
      "tent\030\004 \001(\014\">\n\022SendSMSCodeSection\022\021\n\tsms_" +
      "scene\030\001 \001(\t\022\025\n\rmobile_number\030\002 \001(\t\"\"\n\020SM" +
      "SResultSection\022\016\n\006sms_id\030\003 \001(\t\"\353\001\n\tPayCo" +
      "nfig\022\023\n\013merchant_id\030\001 \001(\t\022\016\n\006app_id\030\002 \001(" +
      "\t\022\017\n\007app_key\030\003 \001(\t\022\022\n\naccess_key\030\004 \001(\t\022\023" +
      "\n\013private_key\030\005 \001(\t\022\022\n\npublic_key\030\006 \001(\t\022" +
      "*\n\tapple_pay\030\007 \001(\0132\027.google.protobuf.Str" +
      "uct\022+\n\ngoogle_pay\030\010 \001(\0132\027.google.protobu" +
      "f.Struct\022\022\n\nurl_scheme\030\t \001(\t\"\210\001\n\030GetToke" +
      "nPayConfigRequest\022\024\n\014channel_code\030\001 \001(\t\022" +
      "\020\n\010store_id\030\002 \001(\t\022\020\n\010currency\030\003 \001(\t\022\016\n\006a" +
      "mount\030\004 \001(\t\022\020\n\010order_no\030\005 \001(\t\022\020\n\010languag" +
      "e\030\006 \001(\t\"n\n\031GetTokenPayConfigResponse\022\022\n\n" +
      "error_code\030\001 \001(\t\022\025\n\rerror_message\030\002 \001(\t\022" +
      "&\n\npay_config\030\003 \001(\0132\022.channel.PayConfig2" +
      "\372\006\n\021ChannelManagement\022\220\001\n\021getTokenPayCon" +
      "fig\022!.channel.GetTokenPayConfigRequest\032\"" +
      ".channel.GetTokenPayConfigResponse\"4\202\323\344\223" +
      "\002.\")/api/v1/pbis/channel/access/token-pa" +
      "y/get:\001*\022V\n\017getAccessConfig\022\037.channel.Ge" +
      "tAccessConfigRequest\032 .channel.GetAccess" +
      "ConfigResponse\"\000\022\214\001\n\024channelAuthorizatio" +
      "n\022$.channel.ChannelAuthorizationRequest\032" +
      "%.channel.ChannelAuthorizationResponse\"\'" +
      "\202\323\344\223\002!\"\034/api/v1/pbis/channel/auth/do:\001*\022" +
      "~\n\021channelManagement\022!.channel.ChannelMa" +
      "nagementRequest\032\".channel.ChannelManagem" +
      "entResponse\"\"\202\323\344\223\002\034\"\027/api/v1/pbis/channe" +
      "l/do:\001*\022y\n\rchannelScript\022\035.channel.Chann" +
      "elScriptRequest\032\036.channel.ChannelScriptR" +
      "esponse\")\202\323\344\223\002#\"\036/api/v1/pbis/channel/sc" +
      "ript/do:\001*\022y\n\rchannelAccess\022\035.channel.Ch" +
      "annelAccessRequest\032\036.channel.ChannelAcce" +
      "ssResponse\")\202\323\344\223\002#\"\036/api/v1/pbis/channel" +
      "/access/do:\001*\022u\n\014channelInfra\022\034.channel." +
      "ChannelInfraRequest\032\035.channel.ChannelInf" +
      "raResponse\"(\202\323\344\223\002\"\"\035/api/v1/pbis/channel" +
      "/infra/do:\001*B2\n.cn.hexcloud.pbis.common." +
      "service.facade.channelP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.api.AnnotationsProto.getDescriptor(),
          com.google.protobuf.StructProto.getDescriptor(),
        });
    internal_static_channel_ChannelScriptRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_channel_ChannelScriptRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ChannelScriptRequest_descriptor,
        new java.lang.String[] { "Action", "ListScriptQuery", "ChannelScriptSection", "DelScriptSection", });
    internal_static_channel_ChannelScriptResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_channel_ChannelScriptResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ChannelScriptResponse_descriptor,
        new java.lang.String[] { "ErrorCode", "ErrorMessage", "ChannelScriptItem", });
    internal_static_channel_ChannelScriptItem_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_channel_ChannelScriptItem_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ChannelScriptItem_descriptor,
        new java.lang.String[] { "Id", "ChannelCode", "ScriptKey", "ScriptText", "Version", "CreateTime", });
    internal_static_channel_ChannelScriptSection_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_channel_ChannelScriptSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ChannelScriptSection_descriptor,
        new java.lang.String[] { "Id", "ChannelCode", "ScriptKey", "ScriptText", });
    internal_static_channel_DelScriptSection_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_channel_DelScriptSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_DelScriptSection_descriptor,
        new java.lang.String[] { "Id", "DelCacheOnly", });
    internal_static_channel_ListScriptQuery_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_channel_ListScriptQuery_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ListScriptQuery_descriptor,
        new java.lang.String[] { "Enabled", "ChannelCode", "ScriptKey", "Version", });
    internal_static_channel_ChannelAccessRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_channel_ChannelAccessRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ChannelAccessRequest_descriptor,
        new java.lang.String[] { "Action", "ListAccessQuery", "ListAccess", "EditAccess", "AppletsAccessSection", });
    internal_static_channel_ApplyTarget_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_channel_ApplyTarget_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ApplyTarget_descriptor,
        new java.lang.String[] { "StoreId", "CompanyId", });
    internal_static_channel_AppletsAccessSection_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_channel_AppletsAccessSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_AppletsAccessSection_descriptor,
        new java.lang.String[] { "ApplyTargetItem", "AppletsAccessItem", });
    internal_static_channel_AppletsAccessItem_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_channel_AppletsAccessItem_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_AppletsAccessItem_descriptor,
        new java.lang.String[] { "ChannelCode", "PropsItem", });
    internal_static_channel_ChannelAccessResponse_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_channel_ChannelAccessResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ChannelAccessResponse_descriptor,
        new java.lang.String[] { "ErrorCode", "ErrorMessage", "AccessReportSection", "AccessItem", "AppletsAccessItem", });
    internal_static_channel_AccessReportSection_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_channel_AccessReportSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_AccessReportSection_descriptor,
        new java.lang.String[] { "PageIndex", "PageSize", "PageCount", "Total", "AccessReportItem", });
    internal_static_channel_AccessReportItem_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_channel_AccessReportItem_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_AccessReportItem_descriptor,
        new java.lang.String[] { "StoreId", "CompanyId", "Level", "ChannelNameItem", "UpdateTime", "PartnerId", "Name", "Code", });
    internal_static_channel_Access_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_channel_Access_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_Access_descriptor,
        new java.lang.String[] { "ChannelCode", "ApplyTo", "ApplyTargets", "BusinessCode", "MerchantId", "AppId", "AppKey", "AccessKey", "Cert", "PrivateKey", "PublicKey", "GatewayUrl", "ChannelNameItems", "AccessId", "PartnerId", "CompanyId", "StoreId", "TerminalId", "ApiVersion", "SubMerchantId", });
    internal_static_channel_ListAccessQuery_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_channel_ListAccessQuery_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ListAccessQuery_descriptor,
        new java.lang.String[] { "ChannelCode", "PartnerId", "CompanyId", "StoreId", "BusinessCode", "Level", "PageIndex", "PageSize", "Search", "SearchFields", "ChannelSubCategory", });
    internal_static_channel_GetAccessConfigRequest_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_channel_GetAccessConfigRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_GetAccessConfigRequest_descriptor,
        new java.lang.String[] { "ChannelCode", "CompanyId", "StoreId", "BusinessCode", });
    internal_static_channel_GetAccessConfigResponse_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_channel_GetAccessConfigResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_GetAccessConfigResponse_descriptor,
        new java.lang.String[] { "ErrorCode", "ErrorMessage", "AccessConfig", });
    internal_static_channel_AccessConfig_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_channel_AccessConfig_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_AccessConfig_descriptor,
        new java.lang.String[] { "MerchantId", "AppId", "AppKey", "AccessKey", "Cert", "PrivateKey", "PublicKey", "TerminalId", "GatewayUrl", "AuthToken", });
    internal_static_channel_ChannelManagementRequest_descriptor =
      getDescriptor().getMessageTypes().get(18);
    internal_static_channel_ChannelManagementRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ChannelManagementRequest_descriptor,
        new java.lang.String[] { "Action", "ListChannelQuery", "Channel", });
    internal_static_channel_ChannelManagementResponse_descriptor =
      getDescriptor().getMessageTypes().get(19);
    internal_static_channel_ChannelManagementResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ChannelManagementResponse_descriptor,
        new java.lang.String[] { "ErrorCode", "ErrorMessage", "ChannelList", });
    internal_static_channel_ListChannelQuery_descriptor =
      getDescriptor().getMessageTypes().get(20);
    internal_static_channel_ListChannelQuery_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ListChannelQuery_descriptor,
        new java.lang.String[] { "ChannelCategory", "SearchName", "ChannelCode", "ChannelSubCategory", });
    internal_static_channel_Channel_descriptor =
      getDescriptor().getMessageTypes().get(21);
    internal_static_channel_Channel_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_Channel_descriptor,
        new java.lang.String[] { "ChannelCode", "ChannelName", "ChannelLogo", "ChannelType", "ChannelCategory", "Description", "ChannelProperties", "ChannelId", "ChannelLabels", "ChannelSubCategory", });
    internal_static_channel_ChannelAuthorizationRequest_descriptor =
      getDescriptor().getMessageTypes().get(22);
    internal_static_channel_ChannelAuthorizationRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ChannelAuthorizationRequest_descriptor,
        new java.lang.String[] { "Action", "ListBindingSection", "SwitchBindingSection", "ListAuthorizationSection", "SignupSection", "QuerySignupSection", "RefreshSignupSection", });
    internal_static_channel_ChannelAuthorizationResponse_descriptor =
      getDescriptor().getMessageTypes().get(23);
    internal_static_channel_ChannelAuthorizationResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ChannelAuthorizationResponse_descriptor,
        new java.lang.String[] { "ErrorCode", "ErrorMessage", "BindingListSection", "AuthorizationListSection", "SignupResultSection", "SignupDetailSection", "RefreshSignupResultSection", });
    internal_static_channel_ChannelInfraRequest_descriptor =
      getDescriptor().getMessageTypes().get(24);
    internal_static_channel_ChannelInfraRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ChannelInfraRequest_descriptor,
        new java.lang.String[] { "Action", "SendSmsCodeSection", });
    internal_static_channel_ChannelInfraResponse_descriptor =
      getDescriptor().getMessageTypes().get(25);
    internal_static_channel_ChannelInfraResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ChannelInfraResponse_descriptor,
        new java.lang.String[] { "ErrorCode", "ErrorMessage", "SmsResultSection", });
    internal_static_channel_ListBindingSection_descriptor =
      getDescriptor().getMessageTypes().get(26);
    internal_static_channel_ListBindingSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ListBindingSection_descriptor,
        new java.lang.String[] { "ChannelCategory", "Enabled", "ChannelLabels", "BusinessCode", });
    internal_static_channel_BindingListSection_descriptor =
      getDescriptor().getMessageTypes().get(27);
    internal_static_channel_BindingListSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_BindingListSection_descriptor,
        new java.lang.String[] { "BindingItem", });
    internal_static_channel_BindingItem_descriptor =
      getDescriptor().getMessageTypes().get(28);
    internal_static_channel_BindingItem_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_BindingItem_descriptor,
        new java.lang.String[] { "ChannelCode", "ChannelName", "BusinessCode", "ChannelType", "ChannelCategory", "ChannelSubCategory", "ChannelLabels", "Enabled", });
    internal_static_channel_SwitchBindingSection_descriptor =
      getDescriptor().getMessageTypes().get(29);
    internal_static_channel_SwitchBindingSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_SwitchBindingSection_descriptor,
        new java.lang.String[] { "ChannelCode", "Enabled", "BusinessCode", });
    internal_static_channel_ListAuthorizationSection_descriptor =
      getDescriptor().getMessageTypes().get(30);
    internal_static_channel_ListAuthorizationSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_ListAuthorizationSection_descriptor,
        new java.lang.String[] { "ChannelCode", "SignupState", "GrantState", "ComplexCriterion", "PageIndex", "PageSize", });
    internal_static_channel_AuthorizationListSection_descriptor =
      getDescriptor().getMessageTypes().get(31);
    internal_static_channel_AuthorizationListSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_AuthorizationListSection_descriptor,
        new java.lang.String[] { "PageIndex", "PageSize", "PageCount", "Total", "AuthorizationItem", });
    internal_static_channel_AuthorizationItem_descriptor =
      getDescriptor().getMessageTypes().get(32);
    internal_static_channel_AuthorizationItem_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_AuthorizationItem_descriptor,
        new java.lang.String[] { "AuthId", "ChannelCode", "MerchantId", "SignupNo", "SignupState", "GrantState", });
    internal_static_channel_SignupSection_descriptor =
      getDescriptor().getMessageTypes().get(33);
    internal_static_channel_SignupSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_SignupSection_descriptor,
        new java.lang.String[] { "ChannelCode", "MerchantId", "BusinessCategory", "ServiceRate", "PartyName", "PartyLocation", "Contact", "Attachments", "SmsId", "SmsCode", });
    internal_static_channel_SignupResultSection_descriptor =
      getDescriptor().getMessageTypes().get(34);
    internal_static_channel_SignupResultSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_SignupResultSection_descriptor,
        new java.lang.String[] { "SignupNo", "SignupState", "SignupResult", });
    internal_static_channel_QuerySignupSection_descriptor =
      getDescriptor().getMessageTypes().get(35);
    internal_static_channel_QuerySignupSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_QuerySignupSection_descriptor,
        new java.lang.String[] { "ChannelAuthId", });
    internal_static_channel_SignupDetailSection_descriptor =
      getDescriptor().getMessageTypes().get(36);
    internal_static_channel_SignupDetailSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_SignupDetailSection_descriptor,
        new java.lang.String[] { "ChannelCode", "MerchantId", "BusinessCategory", "PartyName", "PartyLocation", "Contact", "Attachments", });
    internal_static_channel_RefreshSignupSection_descriptor =
      getDescriptor().getMessageTypes().get(37);
    internal_static_channel_RefreshSignupSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_RefreshSignupSection_descriptor,
        new java.lang.String[] { "ChannelAuthId", });
    internal_static_channel_RefreshSignupResultSection_descriptor =
      getDescriptor().getMessageTypes().get(38);
    internal_static_channel_RefreshSignupResultSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_RefreshSignupResultSection_descriptor,
        new java.lang.String[] { "SignupStateItem", });
    internal_static_channel_SignupStateItem_descriptor =
      getDescriptor().getMessageTypes().get(39);
    internal_static_channel_SignupStateItem_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_SignupStateItem_descriptor,
        new java.lang.String[] { "ChannelAuthId", "SignupState", "SignupResult", });
    internal_static_channel_Location_descriptor =
      getDescriptor().getMessageTypes().get(40);
    internal_static_channel_Location_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_Location_descriptor,
        new java.lang.String[] { "CountryCode", "ProvinceCode", "CityCode", "DistrictCode", "Address", });
    internal_static_channel_Contact_descriptor =
      getDescriptor().getMessageTypes().get(41);
    internal_static_channel_Contact_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_Contact_descriptor,
        new java.lang.String[] { "Name", "Email", "CellphoneNumber", });
    internal_static_channel_Attachment_descriptor =
      getDescriptor().getMessageTypes().get(42);
    internal_static_channel_Attachment_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_Attachment_descriptor,
        new java.lang.String[] { "Name", "FileName", "Url", "Content", });
    internal_static_channel_SendSMSCodeSection_descriptor =
      getDescriptor().getMessageTypes().get(43);
    internal_static_channel_SendSMSCodeSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_SendSMSCodeSection_descriptor,
        new java.lang.String[] { "SmsScene", "MobileNumber", });
    internal_static_channel_SMSResultSection_descriptor =
      getDescriptor().getMessageTypes().get(44);
    internal_static_channel_SMSResultSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_SMSResultSection_descriptor,
        new java.lang.String[] { "SmsId", });
    internal_static_channel_PayConfig_descriptor =
      getDescriptor().getMessageTypes().get(45);
    internal_static_channel_PayConfig_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_PayConfig_descriptor,
        new java.lang.String[] { "MerchantId", "AppId", "AppKey", "AccessKey", "PrivateKey", "PublicKey", "ApplePay", "GooglePay", "UrlScheme", });
    internal_static_channel_GetTokenPayConfigRequest_descriptor =
      getDescriptor().getMessageTypes().get(46);
    internal_static_channel_GetTokenPayConfigRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_GetTokenPayConfigRequest_descriptor,
        new java.lang.String[] { "ChannelCode", "StoreId", "Currency", "Amount", "OrderNo", "Language", });
    internal_static_channel_GetTokenPayConfigResponse_descriptor =
      getDescriptor().getMessageTypes().get(47);
    internal_static_channel_GetTokenPayConfigResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_channel_GetTokenPayConfigResponse_descriptor,
        new java.lang.String[] { "ErrorCode", "ErrorMessage", "PayConfig", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(com.google.api.AnnotationsProto.http);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    com.google.api.AnnotationsProto.getDescriptor();
    com.google.protobuf.StructProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
