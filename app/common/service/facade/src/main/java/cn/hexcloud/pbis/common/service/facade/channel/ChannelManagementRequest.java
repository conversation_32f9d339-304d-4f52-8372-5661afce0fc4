// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道管理请求
 * </pre>
 *
 * Protobuf type {@code channel.ChannelManagementRequest}
 */
public final class ChannelManagementRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.ChannelManagementRequest)
    ChannelManagementRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ChannelManagementRequest.newBuilder() to construct.
  private ChannelManagementRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ChannelManagementRequest() {
    action_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ChannelManagementRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ChannelManagementRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            action_ = s;
            break;
          }
          case 18: {
            cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.Builder subBuilder = null;
            if (listChannelQuery_ != null) {
              subBuilder = listChannelQuery_.toBuilder();
            }
            listChannelQuery_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(listChannelQuery_);
              listChannelQuery_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder subBuilder = null;
            if (channel_ != null) {
              subBuilder = channel_.toBuilder();
            }
            channel_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.Channel.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(channel_);
              channel_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelManagementRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelManagementRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest.Builder.class);
  }

  public static final int ACTION_FIELD_NUMBER = 1;
  private volatile java.lang.Object action_;
  /**
   * <code>string action = 1;</code>
   * @return The action.
   */
  @java.lang.Override
  public java.lang.String getAction() {
    java.lang.Object ref = action_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      action_ = s;
      return s;
    }
  }
  /**
   * <code>string action = 1;</code>
   * @return The bytes for action.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getActionBytes() {
    java.lang.Object ref = action_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      action_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LIST_CHANNEL_QUERY_FIELD_NUMBER = 2;
  private cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery listChannelQuery_;
  /**
   * <code>.channel.ListChannelQuery list_channel_query = 2;</code>
   * @return Whether the listChannelQuery field is set.
   */
  @java.lang.Override
  public boolean hasListChannelQuery() {
    return listChannelQuery_ != null;
  }
  /**
   * <code>.channel.ListChannelQuery list_channel_query = 2;</code>
   * @return The listChannelQuery.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery getListChannelQuery() {
    return listChannelQuery_ == null ? cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.getDefaultInstance() : listChannelQuery_;
  }
  /**
   * <code>.channel.ListChannelQuery list_channel_query = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ListChannelQueryOrBuilder getListChannelQueryOrBuilder() {
    return getListChannelQuery();
  }

  public static final int CHANNEL_FIELD_NUMBER = 3;
  private cn.hexcloud.pbis.common.service.facade.channel.Channel channel_;
  /**
   * <code>.channel.Channel channel = 3;</code>
   * @return Whether the channel field is set.
   */
  @java.lang.Override
  public boolean hasChannel() {
    return channel_ != null;
  }
  /**
   * <code>.channel.Channel channel = 3;</code>
   * @return The channel.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.Channel getChannel() {
    return channel_ == null ? cn.hexcloud.pbis.common.service.facade.channel.Channel.getDefaultInstance() : channel_;
  }
  /**
   * <code>.channel.Channel channel = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder getChannelOrBuilder() {
    return getChannel();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getActionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, action_);
    }
    if (listChannelQuery_ != null) {
      output.writeMessage(2, getListChannelQuery());
    }
    if (channel_ != null) {
      output.writeMessage(3, getChannel());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getActionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, action_);
    }
    if (listChannelQuery_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getListChannelQuery());
    }
    if (channel_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getChannel());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest other = (cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest) obj;

    if (!getAction()
        .equals(other.getAction())) return false;
    if (hasListChannelQuery() != other.hasListChannelQuery()) return false;
    if (hasListChannelQuery()) {
      if (!getListChannelQuery()
          .equals(other.getListChannelQuery())) return false;
    }
    if (hasChannel() != other.hasChannel()) return false;
    if (hasChannel()) {
      if (!getChannel()
          .equals(other.getChannel())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ACTION_FIELD_NUMBER;
    hash = (53 * hash) + getAction().hashCode();
    if (hasListChannelQuery()) {
      hash = (37 * hash) + LIST_CHANNEL_QUERY_FIELD_NUMBER;
      hash = (53 * hash) + getListChannelQuery().hashCode();
    }
    if (hasChannel()) {
      hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
      hash = (53 * hash) + getChannel().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道管理请求
   * </pre>
   *
   * Protobuf type {@code channel.ChannelManagementRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.ChannelManagementRequest)
      cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelManagementRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelManagementRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      action_ = "";

      if (listChannelQueryBuilder_ == null) {
        listChannelQuery_ = null;
      } else {
        listChannelQuery_ = null;
        listChannelQueryBuilder_ = null;
      }
      if (channelBuilder_ == null) {
        channel_ = null;
      } else {
        channel_ = null;
        channelBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelManagementRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest build() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest result = new cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest(this);
      result.action_ = action_;
      if (listChannelQueryBuilder_ == null) {
        result.listChannelQuery_ = listChannelQuery_;
      } else {
        result.listChannelQuery_ = listChannelQueryBuilder_.build();
      }
      if (channelBuilder_ == null) {
        result.channel_ = channel_;
      } else {
        result.channel_ = channelBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest.getDefaultInstance()) return this;
      if (!other.getAction().isEmpty()) {
        action_ = other.action_;
        onChanged();
      }
      if (other.hasListChannelQuery()) {
        mergeListChannelQuery(other.getListChannelQuery());
      }
      if (other.hasChannel()) {
        mergeChannel(other.getChannel());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object action_ = "";
    /**
     * <code>string action = 1;</code>
     * @return The action.
     */
    public java.lang.String getAction() {
      java.lang.Object ref = action_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        action_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string action = 1;</code>
     * @return The bytes for action.
     */
    public com.google.protobuf.ByteString
        getActionBytes() {
      java.lang.Object ref = action_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        action_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string action = 1;</code>
     * @param value The action to set.
     * @return This builder for chaining.
     */
    public Builder setAction(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      action_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string action = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearAction() {
      
      action_ = getDefaultInstance().getAction();
      onChanged();
      return this;
    }
    /**
     * <code>string action = 1;</code>
     * @param value The bytes for action to set.
     * @return This builder for chaining.
     */
    public Builder setActionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      action_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery listChannelQuery_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery, cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.Builder, cn.hexcloud.pbis.common.service.facade.channel.ListChannelQueryOrBuilder> listChannelQueryBuilder_;
    /**
     * <code>.channel.ListChannelQuery list_channel_query = 2;</code>
     * @return Whether the listChannelQuery field is set.
     */
    public boolean hasListChannelQuery() {
      return listChannelQueryBuilder_ != null || listChannelQuery_ != null;
    }
    /**
     * <code>.channel.ListChannelQuery list_channel_query = 2;</code>
     * @return The listChannelQuery.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery getListChannelQuery() {
      if (listChannelQueryBuilder_ == null) {
        return listChannelQuery_ == null ? cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.getDefaultInstance() : listChannelQuery_;
      } else {
        return listChannelQueryBuilder_.getMessage();
      }
    }
    /**
     * <code>.channel.ListChannelQuery list_channel_query = 2;</code>
     */
    public Builder setListChannelQuery(cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery value) {
      if (listChannelQueryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        listChannelQuery_ = value;
        onChanged();
      } else {
        listChannelQueryBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <code>.channel.ListChannelQuery list_channel_query = 2;</code>
     */
    public Builder setListChannelQuery(
        cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.Builder builderForValue) {
      if (listChannelQueryBuilder_ == null) {
        listChannelQuery_ = builderForValue.build();
        onChanged();
      } else {
        listChannelQueryBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <code>.channel.ListChannelQuery list_channel_query = 2;</code>
     */
    public Builder mergeListChannelQuery(cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery value) {
      if (listChannelQueryBuilder_ == null) {
        if (listChannelQuery_ != null) {
          listChannelQuery_ =
            cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.newBuilder(listChannelQuery_).mergeFrom(value).buildPartial();
        } else {
          listChannelQuery_ = value;
        }
        onChanged();
      } else {
        listChannelQueryBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <code>.channel.ListChannelQuery list_channel_query = 2;</code>
     */
    public Builder clearListChannelQuery() {
      if (listChannelQueryBuilder_ == null) {
        listChannelQuery_ = null;
        onChanged();
      } else {
        listChannelQuery_ = null;
        listChannelQueryBuilder_ = null;
      }

      return this;
    }
    /**
     * <code>.channel.ListChannelQuery list_channel_query = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.Builder getListChannelQueryBuilder() {
      
      onChanged();
      return getListChannelQueryFieldBuilder().getBuilder();
    }
    /**
     * <code>.channel.ListChannelQuery list_channel_query = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ListChannelQueryOrBuilder getListChannelQueryOrBuilder() {
      if (listChannelQueryBuilder_ != null) {
        return listChannelQueryBuilder_.getMessageOrBuilder();
      } else {
        return listChannelQuery_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.getDefaultInstance() : listChannelQuery_;
      }
    }
    /**
     * <code>.channel.ListChannelQuery list_channel_query = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery, cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.Builder, cn.hexcloud.pbis.common.service.facade.channel.ListChannelQueryOrBuilder> 
        getListChannelQueryFieldBuilder() {
      if (listChannelQueryBuilder_ == null) {
        listChannelQueryBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery, cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.Builder, cn.hexcloud.pbis.common.service.facade.channel.ListChannelQueryOrBuilder>(
                getListChannelQuery(),
                getParentForChildren(),
                isClean());
        listChannelQuery_ = null;
      }
      return listChannelQueryBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.Channel channel_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Channel, cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder, cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder> channelBuilder_;
    /**
     * <code>.channel.Channel channel = 3;</code>
     * @return Whether the channel field is set.
     */
    public boolean hasChannel() {
      return channelBuilder_ != null || channel_ != null;
    }
    /**
     * <code>.channel.Channel channel = 3;</code>
     * @return The channel.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Channel getChannel() {
      if (channelBuilder_ == null) {
        return channel_ == null ? cn.hexcloud.pbis.common.service.facade.channel.Channel.getDefaultInstance() : channel_;
      } else {
        return channelBuilder_.getMessage();
      }
    }
    /**
     * <code>.channel.Channel channel = 3;</code>
     */
    public Builder setChannel(cn.hexcloud.pbis.common.service.facade.channel.Channel value) {
      if (channelBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        channel_ = value;
        onChanged();
      } else {
        channelBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <code>.channel.Channel channel = 3;</code>
     */
    public Builder setChannel(
        cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder builderForValue) {
      if (channelBuilder_ == null) {
        channel_ = builderForValue.build();
        onChanged();
      } else {
        channelBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <code>.channel.Channel channel = 3;</code>
     */
    public Builder mergeChannel(cn.hexcloud.pbis.common.service.facade.channel.Channel value) {
      if (channelBuilder_ == null) {
        if (channel_ != null) {
          channel_ =
            cn.hexcloud.pbis.common.service.facade.channel.Channel.newBuilder(channel_).mergeFrom(value).buildPartial();
        } else {
          channel_ = value;
        }
        onChanged();
      } else {
        channelBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <code>.channel.Channel channel = 3;</code>
     */
    public Builder clearChannel() {
      if (channelBuilder_ == null) {
        channel_ = null;
        onChanged();
      } else {
        channel_ = null;
        channelBuilder_ = null;
      }

      return this;
    }
    /**
     * <code>.channel.Channel channel = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder getChannelBuilder() {
      
      onChanged();
      return getChannelFieldBuilder().getBuilder();
    }
    /**
     * <code>.channel.Channel channel = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder getChannelOrBuilder() {
      if (channelBuilder_ != null) {
        return channelBuilder_.getMessageOrBuilder();
      } else {
        return channel_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.Channel.getDefaultInstance() : channel_;
      }
    }
    /**
     * <code>.channel.Channel channel = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Channel, cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder, cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder> 
        getChannelFieldBuilder() {
      if (channelBuilder_ == null) {
        channelBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.Channel, cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder, cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder>(
                getChannel(),
                getParentForChildren(),
                isClean());
        channel_ = null;
      }
      return channelBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.ChannelManagementRequest)
  }

  // @@protoc_insertion_point(class_scope:channel.ChannelManagementRequest)
  private static final cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ChannelManagementRequest>
      PARSER = new com.google.protobuf.AbstractParser<ChannelManagementRequest>() {
    @java.lang.Override
    public ChannelManagementRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ChannelManagementRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ChannelManagementRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ChannelManagementRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

