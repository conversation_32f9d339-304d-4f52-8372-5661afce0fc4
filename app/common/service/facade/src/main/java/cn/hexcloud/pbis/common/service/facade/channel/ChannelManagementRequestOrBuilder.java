// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ChannelManagementRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ChannelManagementRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string action = 1;</code>
   * @return The action.
   */
  java.lang.String getAction();
  /**
   * <code>string action = 1;</code>
   * @return The bytes for action.
   */
  com.google.protobuf.ByteString
      getActionBytes();

  /**
   * <code>.channel.ListChannelQuery list_channel_query = 2;</code>
   * @return Whether the listChannelQuery field is set.
   */
  boolean hasListChannelQuery();
  /**
   * <code>.channel.ListChannelQuery list_channel_query = 2;</code>
   * @return The listChannelQuery.
   */
  cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery getListChannelQuery();
  /**
   * <code>.channel.ListChannelQuery list_channel_query = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.ListChannelQueryOrBuilder getListChannelQueryOrBuilder();

  /**
   * <code>.channel.Channel channel = 3;</code>
   * @return Whether the channel field is set.
   */
  boolean hasChannel();
  /**
   * <code>.channel.Channel channel = 3;</code>
   * @return The channel.
   */
  cn.hexcloud.pbis.common.service.facade.channel.Channel getChannel();
  /**
   * <code>.channel.Channel channel = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder getChannelOrBuilder();
}
