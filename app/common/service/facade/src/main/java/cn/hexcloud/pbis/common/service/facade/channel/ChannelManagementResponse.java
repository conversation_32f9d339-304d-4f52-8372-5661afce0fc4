// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道管理接口响应
 * </pre>
 *
 * Protobuf type {@code channel.ChannelManagementResponse}
 */
public final class ChannelManagementResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.ChannelManagementResponse)
    ChannelManagementResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ChannelManagementResponse.newBuilder() to construct.
  private ChannelManagementResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ChannelManagementResponse() {
    errorCode_ = "";
    errorMessage_ = "";
    channelList_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ChannelManagementResponse();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ChannelManagementResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            errorCode_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            errorMessage_ = s;
            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              channelList_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.Channel>();
              mutable_bitField0_ |= 0x00000001;
            }
            channelList_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.Channel.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        channelList_ = java.util.Collections.unmodifiableList(channelList_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelManagementResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelManagementResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse.Builder.class);
  }

  public static final int ERROR_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object errorCode_;
  /**
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  @java.lang.Override
  public java.lang.String getErrorCode() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorCode_ = s;
      return s;
    }
  }
  /**
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorCodeBytes() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ERROR_MESSAGE_FIELD_NUMBER = 2;
  private volatile java.lang.Object errorMessage_;
  /**
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  @java.lang.Override
  public java.lang.String getErrorMessage() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorMessage_ = s;
      return s;
    }
  }
  /**
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorMessageBytes() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorMessage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHANNEL_LIST_FIELD_NUMBER = 3;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Channel> channelList_;
  /**
   * <code>repeated .channel.Channel channel_list = 3;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Channel> getChannelListList() {
    return channelList_;
  }
  /**
   * <code>repeated .channel.Channel channel_list = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder> 
      getChannelListOrBuilderList() {
    return channelList_;
  }
  /**
   * <code>repeated .channel.Channel channel_list = 3;</code>
   */
  @java.lang.Override
  public int getChannelListCount() {
    return channelList_.size();
  }
  /**
   * <code>repeated .channel.Channel channel_list = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.Channel getChannelList(int index) {
    return channelList_.get(index);
  }
  /**
   * <code>repeated .channel.Channel channel_list = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder getChannelListOrBuilder(
      int index) {
    return channelList_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getErrorCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, errorMessage_);
    }
    for (int i = 0; i < channelList_.size(); i++) {
      output.writeMessage(3, channelList_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getErrorCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, errorMessage_);
    }
    for (int i = 0; i < channelList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, channelList_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse other = (cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse) obj;

    if (!getErrorCode()
        .equals(other.getErrorCode())) return false;
    if (!getErrorMessage()
        .equals(other.getErrorMessage())) return false;
    if (!getChannelListList()
        .equals(other.getChannelListList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ERROR_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorCode().hashCode();
    hash = (37 * hash) + ERROR_MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorMessage().hashCode();
    if (getChannelListCount() > 0) {
      hash = (37 * hash) + CHANNEL_LIST_FIELD_NUMBER;
      hash = (53 * hash) + getChannelListList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道管理接口响应
   * </pre>
   *
   * Protobuf type {@code channel.ChannelManagementResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.ChannelManagementResponse)
      cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelManagementResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelManagementResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getChannelListFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      errorCode_ = "";

      errorMessage_ = "";

      if (channelListBuilder_ == null) {
        channelList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        channelListBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelManagementResponse_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse build() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse result = new cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse(this);
      int from_bitField0_ = bitField0_;
      result.errorCode_ = errorCode_;
      result.errorMessage_ = errorMessage_;
      if (channelListBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          channelList_ = java.util.Collections.unmodifiableList(channelList_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.channelList_ = channelList_;
      } else {
        result.channelList_ = channelListBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse.getDefaultInstance()) return this;
      if (!other.getErrorCode().isEmpty()) {
        errorCode_ = other.errorCode_;
        onChanged();
      }
      if (!other.getErrorMessage().isEmpty()) {
        errorMessage_ = other.errorMessage_;
        onChanged();
      }
      if (channelListBuilder_ == null) {
        if (!other.channelList_.isEmpty()) {
          if (channelList_.isEmpty()) {
            channelList_ = other.channelList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureChannelListIsMutable();
            channelList_.addAll(other.channelList_);
          }
          onChanged();
        }
      } else {
        if (!other.channelList_.isEmpty()) {
          if (channelListBuilder_.isEmpty()) {
            channelListBuilder_.dispose();
            channelListBuilder_ = null;
            channelList_ = other.channelList_;
            bitField0_ = (bitField0_ & ~0x00000001);
            channelListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getChannelListFieldBuilder() : null;
          } else {
            channelListBuilder_.addAllMessages(other.channelList_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object errorCode_ = "";
    /**
     * <code>string error_code = 1;</code>
     * @return The errorCode.
     */
    public java.lang.String getErrorCode() {
      java.lang.Object ref = errorCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string error_code = 1;</code>
     * @return The bytes for errorCode.
     */
    public com.google.protobuf.ByteString
        getErrorCodeBytes() {
      java.lang.Object ref = errorCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string error_code = 1;</code>
     * @param value The errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string error_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorCode() {
      
      errorCode_ = getDefaultInstance().getErrorCode();
      onChanged();
      return this;
    }
    /**
     * <code>string error_code = 1;</code>
     * @param value The bytes for errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object errorMessage_ = "";
    /**
     * <code>string error_message = 2;</code>
     * @return The errorMessage.
     */
    public java.lang.String getErrorMessage() {
      java.lang.Object ref = errorMessage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorMessage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string error_message = 2;</code>
     * @return The bytes for errorMessage.
     */
    public com.google.protobuf.ByteString
        getErrorMessageBytes() {
      java.lang.Object ref = errorMessage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorMessage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string error_message = 2;</code>
     * @param value The errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessage(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorMessage_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string error_message = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorMessage() {
      
      errorMessage_ = getDefaultInstance().getErrorMessage();
      onChanged();
      return this;
    }
    /**
     * <code>string error_message = 2;</code>
     * @param value The bytes for errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorMessage_ = value;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Channel> channelList_ =
      java.util.Collections.emptyList();
    private void ensureChannelListIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        channelList_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.Channel>(channelList_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Channel, cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder, cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder> channelListBuilder_;

    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Channel> getChannelListList() {
      if (channelListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(channelList_);
      } else {
        return channelListBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public int getChannelListCount() {
      if (channelListBuilder_ == null) {
        return channelList_.size();
      } else {
        return channelListBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Channel getChannelList(int index) {
      if (channelListBuilder_ == null) {
        return channelList_.get(index);
      } else {
        return channelListBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public Builder setChannelList(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Channel value) {
      if (channelListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureChannelListIsMutable();
        channelList_.set(index, value);
        onChanged();
      } else {
        channelListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public Builder setChannelList(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder builderForValue) {
      if (channelListBuilder_ == null) {
        ensureChannelListIsMutable();
        channelList_.set(index, builderForValue.build());
        onChanged();
      } else {
        channelListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public Builder addChannelList(cn.hexcloud.pbis.common.service.facade.channel.Channel value) {
      if (channelListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureChannelListIsMutable();
        channelList_.add(value);
        onChanged();
      } else {
        channelListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public Builder addChannelList(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Channel value) {
      if (channelListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureChannelListIsMutable();
        channelList_.add(index, value);
        onChanged();
      } else {
        channelListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public Builder addChannelList(
        cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder builderForValue) {
      if (channelListBuilder_ == null) {
        ensureChannelListIsMutable();
        channelList_.add(builderForValue.build());
        onChanged();
      } else {
        channelListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public Builder addChannelList(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder builderForValue) {
      if (channelListBuilder_ == null) {
        ensureChannelListIsMutable();
        channelList_.add(index, builderForValue.build());
        onChanged();
      } else {
        channelListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public Builder addAllChannelList(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.channel.Channel> values) {
      if (channelListBuilder_ == null) {
        ensureChannelListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, channelList_);
        onChanged();
      } else {
        channelListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public Builder clearChannelList() {
      if (channelListBuilder_ == null) {
        channelList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        channelListBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public Builder removeChannelList(int index) {
      if (channelListBuilder_ == null) {
        ensureChannelListIsMutable();
        channelList_.remove(index);
        onChanged();
      } else {
        channelListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder getChannelListBuilder(
        int index) {
      return getChannelListFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder getChannelListOrBuilder(
        int index) {
      if (channelListBuilder_ == null) {
        return channelList_.get(index);  } else {
        return channelListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder> 
         getChannelListOrBuilderList() {
      if (channelListBuilder_ != null) {
        return channelListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(channelList_);
      }
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder addChannelListBuilder() {
      return getChannelListFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.channel.Channel.getDefaultInstance());
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder addChannelListBuilder(
        int index) {
      return getChannelListFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.channel.Channel.getDefaultInstance());
    }
    /**
     * <code>repeated .channel.Channel channel_list = 3;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder> 
         getChannelListBuilderList() {
      return getChannelListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Channel, cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder, cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder> 
        getChannelListFieldBuilder() {
      if (channelListBuilder_ == null) {
        channelListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.Channel, cn.hexcloud.pbis.common.service.facade.channel.Channel.Builder, cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder>(
                channelList_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        channelList_ = null;
      }
      return channelListBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.ChannelManagementResponse)
  }

  // @@protoc_insertion_point(class_scope:channel.ChannelManagementResponse)
  private static final cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ChannelManagementResponse>
      PARSER = new com.google.protobuf.AbstractParser<ChannelManagementResponse>() {
    @java.lang.Override
    public ChannelManagementResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ChannelManagementResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ChannelManagementResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ChannelManagementResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

