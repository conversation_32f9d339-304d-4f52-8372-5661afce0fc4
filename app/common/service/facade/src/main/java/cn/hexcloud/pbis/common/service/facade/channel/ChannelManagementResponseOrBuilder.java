// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ChannelManagementResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ChannelManagementResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  java.lang.String getErrorCode();
  /**
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  com.google.protobuf.ByteString
      getErrorCodeBytes();

  /**
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  java.lang.String getErrorMessage();
  /**
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  com.google.protobuf.ByteString
      getErrorMessageBytes();

  /**
   * <code>repeated .channel.Channel channel_list = 3;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Channel> 
      getChannelListList();
  /**
   * <code>repeated .channel.Channel channel_list = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.Channel getChannelList(int index);
  /**
   * <code>repeated .channel.Channel channel_list = 3;</code>
   */
  int getChannelListCount();
  /**
   * <code>repeated .channel.Channel channel_list = 3;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder> 
      getChannelListOrBuilderList();
  /**
   * <code>repeated .channel.Channel channel_list = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.ChannelOrBuilder getChannelListOrBuilder(
      int index);
}
