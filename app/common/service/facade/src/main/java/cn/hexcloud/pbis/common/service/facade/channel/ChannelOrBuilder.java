// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ChannelOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.Channel)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  java.lang.String getChannelCode();
  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  com.google.protobuf.ByteString
      getChannelCodeBytes();

  /**
   * <pre>
   * 渠道名称
   * </pre>
   *
   * <code>string channel_name = 2;</code>
   * @return The channelName.
   */
  java.lang.String getChannelName();
  /**
   * <pre>
   * 渠道名称
   * </pre>
   *
   * <code>string channel_name = 2;</code>
   * @return The bytes for channelName.
   */
  com.google.protobuf.ByteString
      getChannelNameBytes();

  /**
   * <pre>
   * 渠道logo
   * </pre>
   *
   * <code>string channel_logo = 3;</code>
   * @return The channelLogo.
   */
  java.lang.String getChannelLogo();
  /**
   * <pre>
   * 渠道logo
   * </pre>
   *
   * <code>string channel_logo = 3;</code>
   * @return The bytes for channelLogo.
   */
  com.google.protobuf.ByteString
      getChannelLogoBytes();

  /**
   * <pre>
   * 渠道性质，KA 品牌商；ISV 服务商
   * </pre>
   *
   * <code>string channel_type = 4;</code>
   * @return The channelType.
   */
  java.lang.String getChannelType();
  /**
   * <pre>
   * 渠道性质，KA 品牌商；ISV 服务商
   * </pre>
   *
   * <code>string channel_type = 4;</code>
   * @return The bytes for channelType.
   */
  com.google.protobuf.ByteString
      getChannelTypeBytes();

  /**
   * <pre>
   * 渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
   * </pre>
   *
   * <code>string channel_category = 5;</code>
   * @return The channelCategory.
   */
  java.lang.String getChannelCategory();
  /**
   * <pre>
   * 渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
   * </pre>
   *
   * <code>string channel_category = 5;</code>
   * @return The bytes for channelCategory.
   */
  com.google.protobuf.ByteString
      getChannelCategoryBytes();

  /**
   * <pre>
   * 渠道描述
   * </pre>
   *
   * <code>string description = 6;</code>
   * @return The description.
   */
  java.lang.String getDescription();
  /**
   * <pre>
   * 渠道描述
   * </pre>
   *
   * <code>string description = 6;</code>
   * @return The bytes for description.
   */
  com.google.protobuf.ByteString
      getDescriptionBytes();

  /**
   * <pre>
   * 渠道附加信息，JSON格式
   * </pre>
   *
   * <code>string channel_properties = 7;</code>
   * @return The channelProperties.
   */
  java.lang.String getChannelProperties();
  /**
   * <pre>
   * 渠道附加信息，JSON格式
   * </pre>
   *
   * <code>string channel_properties = 7;</code>
   * @return The bytes for channelProperties.
   */
  com.google.protobuf.ByteString
      getChannelPropertiesBytes();

  /**
   * <pre>
   * 渠道主键
   * </pre>
   *
   * <code>int64 channel_id = 8;</code>
   * @return The channelId.
   */
  long getChannelId();

  /**
   * <pre>
   * 渠道标签
   * </pre>
   *
   * <code>string channel_labels = 9;</code>
   * @return The channelLabels.
   */
  java.lang.String getChannelLabels();
  /**
   * <pre>
   * 渠道标签
   * </pre>
   *
   * <code>string channel_labels = 9;</code>
   * @return The bytes for channelLabels.
   */
  com.google.protobuf.ByteString
      getChannelLabelsBytes();

  /**
   * <pre>
   * 渠道二级分类，AM 小程序&amp;会员；COUPON 卡券
   * </pre>
   *
   * <code>string channel_sub_category = 10;</code>
   * @return The channelSubCategory.
   */
  java.lang.String getChannelSubCategory();
  /**
   * <pre>
   * 渠道二级分类，AM 小程序&amp;会员；COUPON 卡券
   * </pre>
   *
   * <code>string channel_sub_category = 10;</code>
   * @return The bytes for channelSubCategory.
   */
  com.google.protobuf.ByteString
      getChannelSubCategoryBytes();
}
