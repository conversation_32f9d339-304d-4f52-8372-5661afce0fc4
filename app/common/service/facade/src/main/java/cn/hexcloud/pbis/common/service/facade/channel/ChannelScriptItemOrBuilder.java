// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ChannelScriptItemOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ChannelScriptItem)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int32 id = 1;</code>
   * @return The id.
   */
  int getId();

  /**
   * <code>string channel_code = 2;</code>
   * @return The channelCode.
   */
  java.lang.String getChannelCode();
  /**
   * <code>string channel_code = 2;</code>
   * @return The bytes for channelCode.
   */
  com.google.protobuf.ByteString
      getChannelCodeBytes();

  /**
   * <code>string script_key = 3;</code>
   * @return The scriptKey.
   */
  java.lang.String getScriptKey();
  /**
   * <code>string script_key = 3;</code>
   * @return The bytes for scriptKey.
   */
  com.google.protobuf.ByteString
      getScriptKeyBytes();

  /**
   * <code>string script_text = 4;</code>
   * @return The scriptText.
   */
  java.lang.String getScriptText();
  /**
   * <code>string script_text = 4;</code>
   * @return The bytes for scriptText.
   */
  com.google.protobuf.ByteString
      getScriptTextBytes();

  /**
   * <code>string version = 5;</code>
   * @return The version.
   */
  java.lang.String getVersion();
  /**
   * <code>string version = 5;</code>
   * @return The bytes for version.
   */
  com.google.protobuf.ByteString
      getVersionBytes();

  /**
   * <code>string create_time = 6;</code>
   * @return The createTime.
   */
  java.lang.String getCreateTime();
  /**
   * <code>string create_time = 6;</code>
   * @return The bytes for createTime.
   */
  com.google.protobuf.ByteString
      getCreateTimeBytes();
}
