// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道脚本管理入参
 * </pre>
 *
 * Protobuf type {@code channel.ChannelScriptRequest}
 */
public final class ChannelScriptRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.ChannelScriptRequest)
    ChannelScriptRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ChannelScriptRequest.newBuilder() to construct.
  private ChannelScriptRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ChannelScriptRequest() {
    action_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ChannelScriptRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ChannelScriptRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            action_ = s;
            break;
          }
          case 18: {
            cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery.Builder subBuilder = null;
            if (listScriptQuery_ != null) {
              subBuilder = listScriptQuery_.toBuilder();
            }
            listScriptQuery_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(listScriptQuery_);
              listScriptQuery_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection.Builder subBuilder = null;
            if (channelScriptSection_ != null) {
              subBuilder = channelScriptSection_.toBuilder();
            }
            channelScriptSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(channelScriptSection_);
              channelScriptSection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 34: {
            cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection.Builder subBuilder = null;
            if (delScriptSection_ != null) {
              subBuilder = delScriptSection_.toBuilder();
            }
            delScriptSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(delScriptSection_);
              delScriptSection_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelScriptRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelScriptRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest.Builder.class);
  }

  public static final int ACTION_FIELD_NUMBER = 1;
  private volatile java.lang.Object action_;
  /**
   * <code>string action = 1;</code>
   * @return The action.
   */
  @java.lang.Override
  public java.lang.String getAction() {
    java.lang.Object ref = action_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      action_ = s;
      return s;
    }
  }
  /**
   * <code>string action = 1;</code>
   * @return The bytes for action.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getActionBytes() {
    java.lang.Object ref = action_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      action_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LIST_SCRIPT_QUERY_FIELD_NUMBER = 2;
  private cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery listScriptQuery_;
  /**
   * <code>.channel.ListScriptQuery list_script_query = 2;</code>
   * @return Whether the listScriptQuery field is set.
   */
  @java.lang.Override
  public boolean hasListScriptQuery() {
    return listScriptQuery_ != null;
  }
  /**
   * <code>.channel.ListScriptQuery list_script_query = 2;</code>
   * @return The listScriptQuery.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery getListScriptQuery() {
    return listScriptQuery_ == null ? cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery.getDefaultInstance() : listScriptQuery_;
  }
  /**
   * <code>.channel.ListScriptQuery list_script_query = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ListScriptQueryOrBuilder getListScriptQueryOrBuilder() {
    return getListScriptQuery();
  }

  public static final int CHANNEL_SCRIPT_SECTION_FIELD_NUMBER = 3;
  private cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection channelScriptSection_;
  /**
   * <code>.channel.ChannelScriptSection channel_script_section = 3;</code>
   * @return Whether the channelScriptSection field is set.
   */
  @java.lang.Override
  public boolean hasChannelScriptSection() {
    return channelScriptSection_ != null;
  }
  /**
   * <code>.channel.ChannelScriptSection channel_script_section = 3;</code>
   * @return The channelScriptSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection getChannelScriptSection() {
    return channelScriptSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection.getDefaultInstance() : channelScriptSection_;
  }
  /**
   * <code>.channel.ChannelScriptSection channel_script_section = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSectionOrBuilder getChannelScriptSectionOrBuilder() {
    return getChannelScriptSection();
  }

  public static final int DEL_SCRIPT_SECTION_FIELD_NUMBER = 4;
  private cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection delScriptSection_;
  /**
   * <code>.channel.DelScriptSection del_script_section = 4;</code>
   * @return Whether the delScriptSection field is set.
   */
  @java.lang.Override
  public boolean hasDelScriptSection() {
    return delScriptSection_ != null;
  }
  /**
   * <code>.channel.DelScriptSection del_script_section = 4;</code>
   * @return The delScriptSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection getDelScriptSection() {
    return delScriptSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection.getDefaultInstance() : delScriptSection_;
  }
  /**
   * <code>.channel.DelScriptSection del_script_section = 4;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.DelScriptSectionOrBuilder getDelScriptSectionOrBuilder() {
    return getDelScriptSection();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getActionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, action_);
    }
    if (listScriptQuery_ != null) {
      output.writeMessage(2, getListScriptQuery());
    }
    if (channelScriptSection_ != null) {
      output.writeMessage(3, getChannelScriptSection());
    }
    if (delScriptSection_ != null) {
      output.writeMessage(4, getDelScriptSection());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getActionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, action_);
    }
    if (listScriptQuery_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getListScriptQuery());
    }
    if (channelScriptSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getChannelScriptSection());
    }
    if (delScriptSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getDelScriptSection());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest other = (cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest) obj;

    if (!getAction()
        .equals(other.getAction())) return false;
    if (hasListScriptQuery() != other.hasListScriptQuery()) return false;
    if (hasListScriptQuery()) {
      if (!getListScriptQuery()
          .equals(other.getListScriptQuery())) return false;
    }
    if (hasChannelScriptSection() != other.hasChannelScriptSection()) return false;
    if (hasChannelScriptSection()) {
      if (!getChannelScriptSection()
          .equals(other.getChannelScriptSection())) return false;
    }
    if (hasDelScriptSection() != other.hasDelScriptSection()) return false;
    if (hasDelScriptSection()) {
      if (!getDelScriptSection()
          .equals(other.getDelScriptSection())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ACTION_FIELD_NUMBER;
    hash = (53 * hash) + getAction().hashCode();
    if (hasListScriptQuery()) {
      hash = (37 * hash) + LIST_SCRIPT_QUERY_FIELD_NUMBER;
      hash = (53 * hash) + getListScriptQuery().hashCode();
    }
    if (hasChannelScriptSection()) {
      hash = (37 * hash) + CHANNEL_SCRIPT_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getChannelScriptSection().hashCode();
    }
    if (hasDelScriptSection()) {
      hash = (37 * hash) + DEL_SCRIPT_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getDelScriptSection().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道脚本管理入参
   * </pre>
   *
   * Protobuf type {@code channel.ChannelScriptRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.ChannelScriptRequest)
      cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelScriptRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelScriptRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      action_ = "";

      if (listScriptQueryBuilder_ == null) {
        listScriptQuery_ = null;
      } else {
        listScriptQuery_ = null;
        listScriptQueryBuilder_ = null;
      }
      if (channelScriptSectionBuilder_ == null) {
        channelScriptSection_ = null;
      } else {
        channelScriptSection_ = null;
        channelScriptSectionBuilder_ = null;
      }
      if (delScriptSectionBuilder_ == null) {
        delScriptSection_ = null;
      } else {
        delScriptSection_ = null;
        delScriptSectionBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelScriptRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest build() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest result = new cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest(this);
      result.action_ = action_;
      if (listScriptQueryBuilder_ == null) {
        result.listScriptQuery_ = listScriptQuery_;
      } else {
        result.listScriptQuery_ = listScriptQueryBuilder_.build();
      }
      if (channelScriptSectionBuilder_ == null) {
        result.channelScriptSection_ = channelScriptSection_;
      } else {
        result.channelScriptSection_ = channelScriptSectionBuilder_.build();
      }
      if (delScriptSectionBuilder_ == null) {
        result.delScriptSection_ = delScriptSection_;
      } else {
        result.delScriptSection_ = delScriptSectionBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest.getDefaultInstance()) return this;
      if (!other.getAction().isEmpty()) {
        action_ = other.action_;
        onChanged();
      }
      if (other.hasListScriptQuery()) {
        mergeListScriptQuery(other.getListScriptQuery());
      }
      if (other.hasChannelScriptSection()) {
        mergeChannelScriptSection(other.getChannelScriptSection());
      }
      if (other.hasDelScriptSection()) {
        mergeDelScriptSection(other.getDelScriptSection());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object action_ = "";
    /**
     * <code>string action = 1;</code>
     * @return The action.
     */
    public java.lang.String getAction() {
      java.lang.Object ref = action_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        action_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string action = 1;</code>
     * @return The bytes for action.
     */
    public com.google.protobuf.ByteString
        getActionBytes() {
      java.lang.Object ref = action_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        action_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string action = 1;</code>
     * @param value The action to set.
     * @return This builder for chaining.
     */
    public Builder setAction(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      action_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string action = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearAction() {
      
      action_ = getDefaultInstance().getAction();
      onChanged();
      return this;
    }
    /**
     * <code>string action = 1;</code>
     * @param value The bytes for action to set.
     * @return This builder for chaining.
     */
    public Builder setActionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      action_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery listScriptQuery_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery, cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery.Builder, cn.hexcloud.pbis.common.service.facade.channel.ListScriptQueryOrBuilder> listScriptQueryBuilder_;
    /**
     * <code>.channel.ListScriptQuery list_script_query = 2;</code>
     * @return Whether the listScriptQuery field is set.
     */
    public boolean hasListScriptQuery() {
      return listScriptQueryBuilder_ != null || listScriptQuery_ != null;
    }
    /**
     * <code>.channel.ListScriptQuery list_script_query = 2;</code>
     * @return The listScriptQuery.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery getListScriptQuery() {
      if (listScriptQueryBuilder_ == null) {
        return listScriptQuery_ == null ? cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery.getDefaultInstance() : listScriptQuery_;
      } else {
        return listScriptQueryBuilder_.getMessage();
      }
    }
    /**
     * <code>.channel.ListScriptQuery list_script_query = 2;</code>
     */
    public Builder setListScriptQuery(cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery value) {
      if (listScriptQueryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        listScriptQuery_ = value;
        onChanged();
      } else {
        listScriptQueryBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <code>.channel.ListScriptQuery list_script_query = 2;</code>
     */
    public Builder setListScriptQuery(
        cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery.Builder builderForValue) {
      if (listScriptQueryBuilder_ == null) {
        listScriptQuery_ = builderForValue.build();
        onChanged();
      } else {
        listScriptQueryBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <code>.channel.ListScriptQuery list_script_query = 2;</code>
     */
    public Builder mergeListScriptQuery(cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery value) {
      if (listScriptQueryBuilder_ == null) {
        if (listScriptQuery_ != null) {
          listScriptQuery_ =
            cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery.newBuilder(listScriptQuery_).mergeFrom(value).buildPartial();
        } else {
          listScriptQuery_ = value;
        }
        onChanged();
      } else {
        listScriptQueryBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <code>.channel.ListScriptQuery list_script_query = 2;</code>
     */
    public Builder clearListScriptQuery() {
      if (listScriptQueryBuilder_ == null) {
        listScriptQuery_ = null;
        onChanged();
      } else {
        listScriptQuery_ = null;
        listScriptQueryBuilder_ = null;
      }

      return this;
    }
    /**
     * <code>.channel.ListScriptQuery list_script_query = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery.Builder getListScriptQueryBuilder() {
      
      onChanged();
      return getListScriptQueryFieldBuilder().getBuilder();
    }
    /**
     * <code>.channel.ListScriptQuery list_script_query = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ListScriptQueryOrBuilder getListScriptQueryOrBuilder() {
      if (listScriptQueryBuilder_ != null) {
        return listScriptQueryBuilder_.getMessageOrBuilder();
      } else {
        return listScriptQuery_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery.getDefaultInstance() : listScriptQuery_;
      }
    }
    /**
     * <code>.channel.ListScriptQuery list_script_query = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery, cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery.Builder, cn.hexcloud.pbis.common.service.facade.channel.ListScriptQueryOrBuilder> 
        getListScriptQueryFieldBuilder() {
      if (listScriptQueryBuilder_ == null) {
        listScriptQueryBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery, cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery.Builder, cn.hexcloud.pbis.common.service.facade.channel.ListScriptQueryOrBuilder>(
                getListScriptQuery(),
                getParentForChildren(),
                isClean());
        listScriptQuery_ = null;
      }
      return listScriptQueryBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection channelScriptSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSectionOrBuilder> channelScriptSectionBuilder_;
    /**
     * <code>.channel.ChannelScriptSection channel_script_section = 3;</code>
     * @return Whether the channelScriptSection field is set.
     */
    public boolean hasChannelScriptSection() {
      return channelScriptSectionBuilder_ != null || channelScriptSection_ != null;
    }
    /**
     * <code>.channel.ChannelScriptSection channel_script_section = 3;</code>
     * @return The channelScriptSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection getChannelScriptSection() {
      if (channelScriptSectionBuilder_ == null) {
        return channelScriptSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection.getDefaultInstance() : channelScriptSection_;
      } else {
        return channelScriptSectionBuilder_.getMessage();
      }
    }
    /**
     * <code>.channel.ChannelScriptSection channel_script_section = 3;</code>
     */
    public Builder setChannelScriptSection(cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection value) {
      if (channelScriptSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        channelScriptSection_ = value;
        onChanged();
      } else {
        channelScriptSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <code>.channel.ChannelScriptSection channel_script_section = 3;</code>
     */
    public Builder setChannelScriptSection(
        cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection.Builder builderForValue) {
      if (channelScriptSectionBuilder_ == null) {
        channelScriptSection_ = builderForValue.build();
        onChanged();
      } else {
        channelScriptSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <code>.channel.ChannelScriptSection channel_script_section = 3;</code>
     */
    public Builder mergeChannelScriptSection(cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection value) {
      if (channelScriptSectionBuilder_ == null) {
        if (channelScriptSection_ != null) {
          channelScriptSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection.newBuilder(channelScriptSection_).mergeFrom(value).buildPartial();
        } else {
          channelScriptSection_ = value;
        }
        onChanged();
      } else {
        channelScriptSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <code>.channel.ChannelScriptSection channel_script_section = 3;</code>
     */
    public Builder clearChannelScriptSection() {
      if (channelScriptSectionBuilder_ == null) {
        channelScriptSection_ = null;
        onChanged();
      } else {
        channelScriptSection_ = null;
        channelScriptSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <code>.channel.ChannelScriptSection channel_script_section = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection.Builder getChannelScriptSectionBuilder() {
      
      onChanged();
      return getChannelScriptSectionFieldBuilder().getBuilder();
    }
    /**
     * <code>.channel.ChannelScriptSection channel_script_section = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSectionOrBuilder getChannelScriptSectionOrBuilder() {
      if (channelScriptSectionBuilder_ != null) {
        return channelScriptSectionBuilder_.getMessageOrBuilder();
      } else {
        return channelScriptSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection.getDefaultInstance() : channelScriptSection_;
      }
    }
    /**
     * <code>.channel.ChannelScriptSection channel_script_section = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSectionOrBuilder> 
        getChannelScriptSectionFieldBuilder() {
      if (channelScriptSectionBuilder_ == null) {
        channelScriptSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSectionOrBuilder>(
                getChannelScriptSection(),
                getParentForChildren(),
                isClean());
        channelScriptSection_ = null;
      }
      return channelScriptSectionBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection delScriptSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection, cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.DelScriptSectionOrBuilder> delScriptSectionBuilder_;
    /**
     * <code>.channel.DelScriptSection del_script_section = 4;</code>
     * @return Whether the delScriptSection field is set.
     */
    public boolean hasDelScriptSection() {
      return delScriptSectionBuilder_ != null || delScriptSection_ != null;
    }
    /**
     * <code>.channel.DelScriptSection del_script_section = 4;</code>
     * @return The delScriptSection.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection getDelScriptSection() {
      if (delScriptSectionBuilder_ == null) {
        return delScriptSection_ == null ? cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection.getDefaultInstance() : delScriptSection_;
      } else {
        return delScriptSectionBuilder_.getMessage();
      }
    }
    /**
     * <code>.channel.DelScriptSection del_script_section = 4;</code>
     */
    public Builder setDelScriptSection(cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection value) {
      if (delScriptSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        delScriptSection_ = value;
        onChanged();
      } else {
        delScriptSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <code>.channel.DelScriptSection del_script_section = 4;</code>
     */
    public Builder setDelScriptSection(
        cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection.Builder builderForValue) {
      if (delScriptSectionBuilder_ == null) {
        delScriptSection_ = builderForValue.build();
        onChanged();
      } else {
        delScriptSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <code>.channel.DelScriptSection del_script_section = 4;</code>
     */
    public Builder mergeDelScriptSection(cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection value) {
      if (delScriptSectionBuilder_ == null) {
        if (delScriptSection_ != null) {
          delScriptSection_ =
            cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection.newBuilder(delScriptSection_).mergeFrom(value).buildPartial();
        } else {
          delScriptSection_ = value;
        }
        onChanged();
      } else {
        delScriptSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <code>.channel.DelScriptSection del_script_section = 4;</code>
     */
    public Builder clearDelScriptSection() {
      if (delScriptSectionBuilder_ == null) {
        delScriptSection_ = null;
        onChanged();
      } else {
        delScriptSection_ = null;
        delScriptSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <code>.channel.DelScriptSection del_script_section = 4;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection.Builder getDelScriptSectionBuilder() {
      
      onChanged();
      return getDelScriptSectionFieldBuilder().getBuilder();
    }
    /**
     * <code>.channel.DelScriptSection del_script_section = 4;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.DelScriptSectionOrBuilder getDelScriptSectionOrBuilder() {
      if (delScriptSectionBuilder_ != null) {
        return delScriptSectionBuilder_.getMessageOrBuilder();
      } else {
        return delScriptSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection.getDefaultInstance() : delScriptSection_;
      }
    }
    /**
     * <code>.channel.DelScriptSection del_script_section = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection, cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.DelScriptSectionOrBuilder> 
        getDelScriptSectionFieldBuilder() {
      if (delScriptSectionBuilder_ == null) {
        delScriptSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection, cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection.Builder, cn.hexcloud.pbis.common.service.facade.channel.DelScriptSectionOrBuilder>(
                getDelScriptSection(),
                getParentForChildren(),
                isClean());
        delScriptSection_ = null;
      }
      return delScriptSectionBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.ChannelScriptRequest)
  }

  // @@protoc_insertion_point(class_scope:channel.ChannelScriptRequest)
  private static final cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ChannelScriptRequest>
      PARSER = new com.google.protobuf.AbstractParser<ChannelScriptRequest>() {
    @java.lang.Override
    public ChannelScriptRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ChannelScriptRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ChannelScriptRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ChannelScriptRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

