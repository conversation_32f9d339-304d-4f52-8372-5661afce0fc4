// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ChannelScriptRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ChannelScriptRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string action = 1;</code>
   * @return The action.
   */
  java.lang.String getAction();
  /**
   * <code>string action = 1;</code>
   * @return The bytes for action.
   */
  com.google.protobuf.ByteString
      getActionBytes();

  /**
   * <code>.channel.ListScriptQuery list_script_query = 2;</code>
   * @return Whether the listScriptQuery field is set.
   */
  boolean hasListScriptQuery();
  /**
   * <code>.channel.ListScriptQuery list_script_query = 2;</code>
   * @return The listScriptQuery.
   */
  cn.hexcloud.pbis.common.service.facade.channel.ListScriptQuery getListScriptQuery();
  /**
   * <code>.channel.ListScriptQuery list_script_query = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.ListScriptQueryOrBuilder getListScriptQueryOrBuilder();

  /**
   * <code>.channel.ChannelScriptSection channel_script_section = 3;</code>
   * @return Whether the channelScriptSection field is set.
   */
  boolean hasChannelScriptSection();
  /**
   * <code>.channel.ChannelScriptSection channel_script_section = 3;</code>
   * @return The channelScriptSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSection getChannelScriptSection();
  /**
   * <code>.channel.ChannelScriptSection channel_script_section = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptSectionOrBuilder getChannelScriptSectionOrBuilder();

  /**
   * <code>.channel.DelScriptSection del_script_section = 4;</code>
   * @return Whether the delScriptSection field is set.
   */
  boolean hasDelScriptSection();
  /**
   * <code>.channel.DelScriptSection del_script_section = 4;</code>
   * @return The delScriptSection.
   */
  cn.hexcloud.pbis.common.service.facade.channel.DelScriptSection getDelScriptSection();
  /**
   * <code>.channel.DelScriptSection del_script_section = 4;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.DelScriptSectionOrBuilder getDelScriptSectionOrBuilder();
}
