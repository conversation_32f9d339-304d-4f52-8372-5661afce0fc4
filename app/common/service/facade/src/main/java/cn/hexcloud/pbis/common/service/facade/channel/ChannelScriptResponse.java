// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道脚本管理入参
 * </pre>
 *
 * Protobuf type {@code channel.ChannelScriptResponse}
 */
public final class ChannelScriptResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.ChannelScriptResponse)
    ChannelScriptResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ChannelScriptResponse.newBuilder() to construct.
  private ChannelScriptResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ChannelScriptResponse() {
    errorCode_ = "";
    errorMessage_ = "";
    channelScriptItem_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ChannelScriptResponse();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ChannelScriptResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            errorCode_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            errorMessage_ = s;
            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              channelScriptItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem>();
              mutable_bitField0_ |= 0x00000001;
            }
            channelScriptItem_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        channelScriptItem_ = java.util.Collections.unmodifiableList(channelScriptItem_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelScriptResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelScriptResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse.Builder.class);
  }

  public static final int ERROR_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object errorCode_;
  /**
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  @java.lang.Override
  public java.lang.String getErrorCode() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorCode_ = s;
      return s;
    }
  }
  /**
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorCodeBytes() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ERROR_MESSAGE_FIELD_NUMBER = 2;
  private volatile java.lang.Object errorMessage_;
  /**
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  @java.lang.Override
  public java.lang.String getErrorMessage() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorMessage_ = s;
      return s;
    }
  }
  /**
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorMessageBytes() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorMessage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHANNEL_SCRIPT_ITEM_FIELD_NUMBER = 3;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem> channelScriptItem_;
  /**
   * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem> getChannelScriptItemList() {
    return channelScriptItem_;
  }
  /**
   * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItemOrBuilder> 
      getChannelScriptItemOrBuilderList() {
    return channelScriptItem_;
  }
  /**
   * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
   */
  @java.lang.Override
  public int getChannelScriptItemCount() {
    return channelScriptItem_.size();
  }
  /**
   * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem getChannelScriptItem(int index) {
    return channelScriptItem_.get(index);
  }
  /**
   * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItemOrBuilder getChannelScriptItemOrBuilder(
      int index) {
    return channelScriptItem_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getErrorCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, errorMessage_);
    }
    for (int i = 0; i < channelScriptItem_.size(); i++) {
      output.writeMessage(3, channelScriptItem_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getErrorCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, errorMessage_);
    }
    for (int i = 0; i < channelScriptItem_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, channelScriptItem_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse other = (cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse) obj;

    if (!getErrorCode()
        .equals(other.getErrorCode())) return false;
    if (!getErrorMessage()
        .equals(other.getErrorMessage())) return false;
    if (!getChannelScriptItemList()
        .equals(other.getChannelScriptItemList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ERROR_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorCode().hashCode();
    hash = (37 * hash) + ERROR_MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorMessage().hashCode();
    if (getChannelScriptItemCount() > 0) {
      hash = (37 * hash) + CHANNEL_SCRIPT_ITEM_FIELD_NUMBER;
      hash = (53 * hash) + getChannelScriptItemList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道脚本管理入参
   * </pre>
   *
   * Protobuf type {@code channel.ChannelScriptResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.ChannelScriptResponse)
      cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelScriptResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelScriptResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse.class, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getChannelScriptItemFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      errorCode_ = "";

      errorMessage_ = "";

      if (channelScriptItemBuilder_ == null) {
        channelScriptItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        channelScriptItemBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ChannelScriptResponse_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse build() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse result = new cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse(this);
      int from_bitField0_ = bitField0_;
      result.errorCode_ = errorCode_;
      result.errorMessage_ = errorMessage_;
      if (channelScriptItemBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          channelScriptItem_ = java.util.Collections.unmodifiableList(channelScriptItem_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.channelScriptItem_ = channelScriptItem_;
      } else {
        result.channelScriptItem_ = channelScriptItemBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse.getDefaultInstance()) return this;
      if (!other.getErrorCode().isEmpty()) {
        errorCode_ = other.errorCode_;
        onChanged();
      }
      if (!other.getErrorMessage().isEmpty()) {
        errorMessage_ = other.errorMessage_;
        onChanged();
      }
      if (channelScriptItemBuilder_ == null) {
        if (!other.channelScriptItem_.isEmpty()) {
          if (channelScriptItem_.isEmpty()) {
            channelScriptItem_ = other.channelScriptItem_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureChannelScriptItemIsMutable();
            channelScriptItem_.addAll(other.channelScriptItem_);
          }
          onChanged();
        }
      } else {
        if (!other.channelScriptItem_.isEmpty()) {
          if (channelScriptItemBuilder_.isEmpty()) {
            channelScriptItemBuilder_.dispose();
            channelScriptItemBuilder_ = null;
            channelScriptItem_ = other.channelScriptItem_;
            bitField0_ = (bitField0_ & ~0x00000001);
            channelScriptItemBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getChannelScriptItemFieldBuilder() : null;
          } else {
            channelScriptItemBuilder_.addAllMessages(other.channelScriptItem_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object errorCode_ = "";
    /**
     * <code>string error_code = 1;</code>
     * @return The errorCode.
     */
    public java.lang.String getErrorCode() {
      java.lang.Object ref = errorCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string error_code = 1;</code>
     * @return The bytes for errorCode.
     */
    public com.google.protobuf.ByteString
        getErrorCodeBytes() {
      java.lang.Object ref = errorCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string error_code = 1;</code>
     * @param value The errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string error_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorCode() {
      
      errorCode_ = getDefaultInstance().getErrorCode();
      onChanged();
      return this;
    }
    /**
     * <code>string error_code = 1;</code>
     * @param value The bytes for errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object errorMessage_ = "";
    /**
     * <code>string error_message = 2;</code>
     * @return The errorMessage.
     */
    public java.lang.String getErrorMessage() {
      java.lang.Object ref = errorMessage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorMessage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string error_message = 2;</code>
     * @return The bytes for errorMessage.
     */
    public com.google.protobuf.ByteString
        getErrorMessageBytes() {
      java.lang.Object ref = errorMessage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorMessage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string error_message = 2;</code>
     * @param value The errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessage(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorMessage_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string error_message = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorMessage() {
      
      errorMessage_ = getDefaultInstance().getErrorMessage();
      onChanged();
      return this;
    }
    /**
     * <code>string error_message = 2;</code>
     * @param value The bytes for errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorMessage_ = value;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem> channelScriptItem_ =
      java.util.Collections.emptyList();
    private void ensureChannelScriptItemIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        channelScriptItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem>(channelScriptItem_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItemOrBuilder> channelScriptItemBuilder_;

    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem> getChannelScriptItemList() {
      if (channelScriptItemBuilder_ == null) {
        return java.util.Collections.unmodifiableList(channelScriptItem_);
      } else {
        return channelScriptItemBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public int getChannelScriptItemCount() {
      if (channelScriptItemBuilder_ == null) {
        return channelScriptItem_.size();
      } else {
        return channelScriptItemBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem getChannelScriptItem(int index) {
      if (channelScriptItemBuilder_ == null) {
        return channelScriptItem_.get(index);
      } else {
        return channelScriptItemBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public Builder setChannelScriptItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem value) {
      if (channelScriptItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureChannelScriptItemIsMutable();
        channelScriptItem_.set(index, value);
        onChanged();
      } else {
        channelScriptItemBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public Builder setChannelScriptItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem.Builder builderForValue) {
      if (channelScriptItemBuilder_ == null) {
        ensureChannelScriptItemIsMutable();
        channelScriptItem_.set(index, builderForValue.build());
        onChanged();
      } else {
        channelScriptItemBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public Builder addChannelScriptItem(cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem value) {
      if (channelScriptItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureChannelScriptItemIsMutable();
        channelScriptItem_.add(value);
        onChanged();
      } else {
        channelScriptItemBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public Builder addChannelScriptItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem value) {
      if (channelScriptItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureChannelScriptItemIsMutable();
        channelScriptItem_.add(index, value);
        onChanged();
      } else {
        channelScriptItemBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public Builder addChannelScriptItem(
        cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem.Builder builderForValue) {
      if (channelScriptItemBuilder_ == null) {
        ensureChannelScriptItemIsMutable();
        channelScriptItem_.add(builderForValue.build());
        onChanged();
      } else {
        channelScriptItemBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public Builder addChannelScriptItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem.Builder builderForValue) {
      if (channelScriptItemBuilder_ == null) {
        ensureChannelScriptItemIsMutable();
        channelScriptItem_.add(index, builderForValue.build());
        onChanged();
      } else {
        channelScriptItemBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public Builder addAllChannelScriptItem(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem> values) {
      if (channelScriptItemBuilder_ == null) {
        ensureChannelScriptItemIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, channelScriptItem_);
        onChanged();
      } else {
        channelScriptItemBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public Builder clearChannelScriptItem() {
      if (channelScriptItemBuilder_ == null) {
        channelScriptItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        channelScriptItemBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public Builder removeChannelScriptItem(int index) {
      if (channelScriptItemBuilder_ == null) {
        ensureChannelScriptItemIsMutable();
        channelScriptItem_.remove(index);
        onChanged();
      } else {
        channelScriptItemBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem.Builder getChannelScriptItemBuilder(
        int index) {
      return getChannelScriptItemFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItemOrBuilder getChannelScriptItemOrBuilder(
        int index) {
      if (channelScriptItemBuilder_ == null) {
        return channelScriptItem_.get(index);  } else {
        return channelScriptItemBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItemOrBuilder> 
         getChannelScriptItemOrBuilderList() {
      if (channelScriptItemBuilder_ != null) {
        return channelScriptItemBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(channelScriptItem_);
      }
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem.Builder addChannelScriptItemBuilder() {
      return getChannelScriptItemFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem.getDefaultInstance());
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem.Builder addChannelScriptItemBuilder(
        int index) {
      return getChannelScriptItemFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem.getDefaultInstance());
    }
    /**
     * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem.Builder> 
         getChannelScriptItemBuilderList() {
      return getChannelScriptItemFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItemOrBuilder> 
        getChannelScriptItemFieldBuilder() {
      if (channelScriptItemBuilder_ == null) {
        channelScriptItemBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItemOrBuilder>(
                channelScriptItem_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        channelScriptItem_ = null;
      }
      return channelScriptItemBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.ChannelScriptResponse)
  }

  // @@protoc_insertion_point(class_scope:channel.ChannelScriptResponse)
  private static final cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ChannelScriptResponse>
      PARSER = new com.google.protobuf.AbstractParser<ChannelScriptResponse>() {
    @java.lang.Override
    public ChannelScriptResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ChannelScriptResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ChannelScriptResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ChannelScriptResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

