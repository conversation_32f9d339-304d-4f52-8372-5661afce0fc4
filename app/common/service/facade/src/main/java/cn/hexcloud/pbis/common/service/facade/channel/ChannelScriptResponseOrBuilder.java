// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ChannelScriptResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ChannelScriptResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  java.lang.String getErrorCode();
  /**
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  com.google.protobuf.ByteString
      getErrorCodeBytes();

  /**
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  java.lang.String getErrorMessage();
  /**
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  com.google.protobuf.ByteString
      getErrorMessageBytes();

  /**
   * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem> 
      getChannelScriptItemList();
  /**
   * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItem getChannelScriptItem(int index);
  /**
   * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
   */
  int getChannelScriptItemCount();
  /**
   * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItemOrBuilder> 
      getChannelScriptItemOrBuilderList();
  /**
   * <code>repeated .channel.ChannelScriptItem channel_script_item = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.ChannelScriptItemOrBuilder getChannelScriptItemOrBuilder(
      int index);
}
