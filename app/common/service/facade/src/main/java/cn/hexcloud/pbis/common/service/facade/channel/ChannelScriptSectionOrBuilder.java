// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ChannelScriptSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ChannelScriptSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int32 id = 1;</code>
   * @return The id.
   */
  int getId();

  /**
   * <code>string channel_code = 2;</code>
   * @return The channelCode.
   */
  java.lang.String getChannelCode();
  /**
   * <code>string channel_code = 2;</code>
   * @return The bytes for channelCode.
   */
  com.google.protobuf.ByteString
      getChannelCodeBytes();

  /**
   * <code>string script_key = 3;</code>
   * @return The scriptKey.
   */
  java.lang.String getScriptKey();
  /**
   * <code>string script_key = 3;</code>
   * @return The bytes for scriptKey.
   */
  com.google.protobuf.ByteString
      getScriptKeyBytes();

  /**
   * <code>string script_text = 4;</code>
   * @return The scriptText.
   */
  java.lang.String getScriptText();
  /**
   * <code>string script_text = 4;</code>
   * @return The bytes for scriptText.
   */
  com.google.protobuf.ByteString
      getScriptTextBytes();
}
