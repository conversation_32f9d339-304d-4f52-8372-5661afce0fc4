// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ContactOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.Contact)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （可选）联系人姓名
   * </pre>
   *
   * <code>string name = 1;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   * （可选）联系人姓名
   * </pre>
   *
   * <code>string name = 1;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * （可选）联系人Email
   * </pre>
   *
   * <code>string email = 2;</code>
   * @return The email.
   */
  java.lang.String getEmail();
  /**
   * <pre>
   * （可选）联系人Email
   * </pre>
   *
   * <code>string email = 2;</code>
   * @return The bytes for email.
   */
  com.google.protobuf.ByteString
      getEmailBytes();

  /**
   * <pre>
   * （可选）联系人手机号
   * </pre>
   *
   * <code>string cellphone_number = 3;</code>
   * @return The cellphoneNumber.
   */
  java.lang.String getCellphoneNumber();
  /**
   * <pre>
   * （可选）联系人手机号
   * </pre>
   *
   * <code>string cellphone_number = 3;</code>
   * @return The bytes for cellphoneNumber.
   */
  com.google.protobuf.ByteString
      getCellphoneNumberBytes();
}
