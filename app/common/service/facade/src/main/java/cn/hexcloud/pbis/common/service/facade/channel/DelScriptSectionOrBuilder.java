// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface DelScriptSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.DelScriptSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int64 id = 1;</code>
   * @return The id.
   */
  long getId();

  /**
   * <code>string del_cache_only = 2;</code>
   * @return The delCacheOnly.
   */
  java.lang.String getDelCacheOnly();
  /**
   * <code>string del_cache_only = 2;</code>
   * @return The bytes for delCacheOnly.
   */
  com.google.protobuf.ByteString
      getDelCacheOnlyBytes();
}
