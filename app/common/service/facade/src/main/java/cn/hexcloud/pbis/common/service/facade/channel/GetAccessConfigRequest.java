// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 获取渠道密钥信息
 * </pre>
 *
 * Protobuf type {@code channel.GetAccessConfigRequest}
 */
public final class GetAccessConfigRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.GetAccessConfigRequest)
    GetAccessConfigRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use GetAccessConfigRequest.newBuilder() to construct.
  private GetAccessConfigRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private GetAccessConfigRequest() {
    channelCode_ = "";
    companyId_ = "";
    storeId_ = "";
    businessCode_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new GetAccessConfigRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private GetAccessConfigRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            channelCode_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            companyId_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            storeId_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            businessCode_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_GetAccessConfigRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_GetAccessConfigRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest.class, cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest.Builder.class);
  }

  public static final int CHANNEL_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object channelCode_;
  /**
   * <pre>
   *渠道code ： WXPay ,AliPay
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  @java.lang.Override
  public java.lang.String getChannelCode() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *渠道code ： WXPay ,AliPay
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelCodeBytes() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int COMPANY_ID_FIELD_NUMBER = 2;
  private volatile java.lang.Object companyId_;
  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 2;</code>
   * @return The companyId.
   */
  @java.lang.Override
  public java.lang.String getCompanyId() {
    java.lang.Object ref = companyId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      companyId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 2;</code>
   * @return The bytes for companyId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCompanyIdBytes() {
    java.lang.Object ref = companyId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      companyId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STORE_ID_FIELD_NUMBER = 3;
  private volatile java.lang.Object storeId_;
  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 3;</code>
   * @return The storeId.
   */
  @java.lang.Override
  public java.lang.String getStoreId() {
    java.lang.Object ref = storeId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      storeId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 3;</code>
   * @return The bytes for storeId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStoreIdBytes() {
    java.lang.Object ref = storeId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      storeId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BUSINESS_CODE_FIELD_NUMBER = 4;
  private volatile java.lang.Object businessCode_;
  /**
   * <pre>
   * 渠道业务代码
   * </pre>
   *
   * <code>string business_code = 4;</code>
   * @return The businessCode.
   */
  @java.lang.Override
  public java.lang.String getBusinessCode() {
    java.lang.Object ref = businessCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      businessCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道业务代码
   * </pre>
   *
   * <code>string business_code = 4;</code>
   * @return The bytes for businessCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBusinessCodeBytes() {
    java.lang.Object ref = businessCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      businessCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getChannelCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelCode_);
    }
    if (!getCompanyIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, companyId_);
    }
    if (!getStoreIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, storeId_);
    }
    if (!getBusinessCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, businessCode_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getChannelCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelCode_);
    }
    if (!getCompanyIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, companyId_);
    }
    if (!getStoreIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, storeId_);
    }
    if (!getBusinessCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, businessCode_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest other = (cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest) obj;

    if (!getChannelCode()
        .equals(other.getChannelCode())) return false;
    if (!getCompanyId()
        .equals(other.getCompanyId())) return false;
    if (!getStoreId()
        .equals(other.getStoreId())) return false;
    if (!getBusinessCode()
        .equals(other.getBusinessCode())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHANNEL_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getChannelCode().hashCode();
    hash = (37 * hash) + COMPANY_ID_FIELD_NUMBER;
    hash = (53 * hash) + getCompanyId().hashCode();
    hash = (37 * hash) + STORE_ID_FIELD_NUMBER;
    hash = (53 * hash) + getStoreId().hashCode();
    hash = (37 * hash) + BUSINESS_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getBusinessCode().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 获取渠道密钥信息
   * </pre>
   *
   * Protobuf type {@code channel.GetAccessConfigRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.GetAccessConfigRequest)
      cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_GetAccessConfigRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_GetAccessConfigRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest.class, cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channelCode_ = "";

      companyId_ = "";

      storeId_ = "";

      businessCode_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_GetAccessConfigRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest build() {
      cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest result = new cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest(this);
      result.channelCode_ = channelCode_;
      result.companyId_ = companyId_;
      result.storeId_ = storeId_;
      result.businessCode_ = businessCode_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest.getDefaultInstance()) return this;
      if (!other.getChannelCode().isEmpty()) {
        channelCode_ = other.channelCode_;
        onChanged();
      }
      if (!other.getCompanyId().isEmpty()) {
        companyId_ = other.companyId_;
        onChanged();
      }
      if (!other.getStoreId().isEmpty()) {
        storeId_ = other.storeId_;
        onChanged();
      }
      if (!other.getBusinessCode().isEmpty()) {
        businessCode_ = other.businessCode_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object channelCode_ = "";
    /**
     * <pre>
     *渠道code ： WXPay ,AliPay
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return The channelCode.
     */
    public java.lang.String getChannelCode() {
      java.lang.Object ref = channelCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *渠道code ： WXPay ,AliPay
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return The bytes for channelCode.
     */
    public com.google.protobuf.ByteString
        getChannelCodeBytes() {
      java.lang.Object ref = channelCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *渠道code ： WXPay ,AliPay
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @param value The channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *渠道code ： WXPay ,AliPay
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelCode() {
      
      channelCode_ = getDefaultInstance().getChannelCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *渠道code ： WXPay ,AliPay
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @param value The bytes for channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object companyId_ = "";
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 2;</code>
     * @return The companyId.
     */
    public java.lang.String getCompanyId() {
      java.lang.Object ref = companyId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        companyId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 2;</code>
     * @return The bytes for companyId.
     */
    public com.google.protobuf.ByteString
        getCompanyIdBytes() {
      java.lang.Object ref = companyId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        companyId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 2;</code>
     * @param value The companyId to set.
     * @return This builder for chaining.
     */
    public Builder setCompanyId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      companyId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearCompanyId() {
      
      companyId_ = getDefaultInstance().getCompanyId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 2;</code>
     * @param value The bytes for companyId to set.
     * @return This builder for chaining.
     */
    public Builder setCompanyIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      companyId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object storeId_ = "";
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 3;</code>
     * @return The storeId.
     */
    public java.lang.String getStoreId() {
      java.lang.Object ref = storeId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        storeId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 3;</code>
     * @return The bytes for storeId.
     */
    public com.google.protobuf.ByteString
        getStoreIdBytes() {
      java.lang.Object ref = storeId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        storeId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 3;</code>
     * @param value The storeId to set.
     * @return This builder for chaining.
     */
    public Builder setStoreId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      storeId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearStoreId() {
      
      storeId_ = getDefaultInstance().getStoreId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 3;</code>
     * @param value The bytes for storeId to set.
     * @return This builder for chaining.
     */
    public Builder setStoreIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      storeId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object businessCode_ = "";
    /**
     * <pre>
     * 渠道业务代码
     * </pre>
     *
     * <code>string business_code = 4;</code>
     * @return The businessCode.
     */
    public java.lang.String getBusinessCode() {
      java.lang.Object ref = businessCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        businessCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道业务代码
     * </pre>
     *
     * <code>string business_code = 4;</code>
     * @return The bytes for businessCode.
     */
    public com.google.protobuf.ByteString
        getBusinessCodeBytes() {
      java.lang.Object ref = businessCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        businessCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道业务代码
     * </pre>
     *
     * <code>string business_code = 4;</code>
     * @param value The businessCode to set.
     * @return This builder for chaining.
     */
    public Builder setBusinessCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      businessCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道业务代码
     * </pre>
     *
     * <code>string business_code = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearBusinessCode() {
      
      businessCode_ = getDefaultInstance().getBusinessCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道业务代码
     * </pre>
     *
     * <code>string business_code = 4;</code>
     * @param value The bytes for businessCode to set.
     * @return This builder for chaining.
     */
    public Builder setBusinessCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      businessCode_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.GetAccessConfigRequest)
  }

  // @@protoc_insertion_point(class_scope:channel.GetAccessConfigRequest)
  private static final cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GetAccessConfigRequest>
      PARSER = new com.google.protobuf.AbstractParser<GetAccessConfigRequest>() {
    @java.lang.Override
    public GetAccessConfigRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new GetAccessConfigRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<GetAccessConfigRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GetAccessConfigRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

