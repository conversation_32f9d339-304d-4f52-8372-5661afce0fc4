// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface GetAccessConfigRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.GetAccessConfigRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *渠道code ： WXPay ,AliPay
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  java.lang.String getChannelCode();
  /**
   * <pre>
   *渠道code ： WXPay ,AliPay
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  com.google.protobuf.ByteString
      getChannelCodeBytes();

  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 2;</code>
   * @return The companyId.
   */
  java.lang.String getCompanyId();
  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 2;</code>
   * @return The bytes for companyId.
   */
  com.google.protobuf.ByteString
      getCompanyIdBytes();

  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 3;</code>
   * @return The storeId.
   */
  java.lang.String getStoreId();
  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 3;</code>
   * @return The bytes for storeId.
   */
  com.google.protobuf.ByteString
      getStoreIdBytes();

  /**
   * <pre>
   * 渠道业务代码
   * </pre>
   *
   * <code>string business_code = 4;</code>
   * @return The businessCode.
   */
  java.lang.String getBusinessCode();
  /**
   * <pre>
   * 渠道业务代码
   * </pre>
   *
   * <code>string business_code = 4;</code>
   * @return The bytes for businessCode.
   */
  com.google.protobuf.ByteString
      getBusinessCodeBytes();
}
