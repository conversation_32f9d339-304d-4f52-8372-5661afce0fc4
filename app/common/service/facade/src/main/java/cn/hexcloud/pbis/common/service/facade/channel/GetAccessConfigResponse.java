// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 获取渠道密钥信息
 * </pre>
 *
 * Protobuf type {@code channel.GetAccessConfigResponse}
 */
public final class GetAccessConfigResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.GetAccessConfigResponse)
    GetAccessConfigResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use GetAccessConfigResponse.newBuilder() to construct.
  private GetAccessConfigResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private GetAccessConfigResponse() {
    errorCode_ = "";
    errorMessage_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new GetAccessConfigResponse();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private GetAccessConfigResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            errorCode_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            errorMessage_ = s;
            break;
          }
          case 26: {
            cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.Builder subBuilder = null;
            if (accessConfig_ != null) {
              subBuilder = accessConfig_.toBuilder();
            }
            accessConfig_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(accessConfig_);
              accessConfig_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_GetAccessConfigResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_GetAccessConfigResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse.class, cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse.Builder.class);
  }

  public static final int ERROR_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object errorCode_;
  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  @java.lang.Override
  public java.lang.String getErrorCode() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorCodeBytes() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ERROR_MESSAGE_FIELD_NUMBER = 2;
  private volatile java.lang.Object errorMessage_;
  /**
   * <pre>
   * （必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  @java.lang.Override
  public java.lang.String getErrorMessage() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorMessage_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorMessageBytes() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorMessage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACCESS_CONFIG_FIELD_NUMBER = 3;
  private cn.hexcloud.pbis.common.service.facade.channel.AccessConfig accessConfig_;
  /**
   * <pre>
   * 渠道配置
   * </pre>
   *
   * <code>.channel.AccessConfig access_config = 3;</code>
   * @return Whether the accessConfig field is set.
   */
  @java.lang.Override
  public boolean hasAccessConfig() {
    return accessConfig_ != null;
  }
  /**
   * <pre>
   * 渠道配置
   * </pre>
   *
   * <code>.channel.AccessConfig access_config = 3;</code>
   * @return The accessConfig.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AccessConfig getAccessConfig() {
    return accessConfig_ == null ? cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.getDefaultInstance() : accessConfig_;
  }
  /**
   * <pre>
   * 渠道配置
   * </pre>
   *
   * <code>.channel.AccessConfig access_config = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AccessConfigOrBuilder getAccessConfigOrBuilder() {
    return getAccessConfig();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getErrorCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, errorMessage_);
    }
    if (accessConfig_ != null) {
      output.writeMessage(3, getAccessConfig());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getErrorCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, errorMessage_);
    }
    if (accessConfig_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getAccessConfig());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse other = (cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse) obj;

    if (!getErrorCode()
        .equals(other.getErrorCode())) return false;
    if (!getErrorMessage()
        .equals(other.getErrorMessage())) return false;
    if (hasAccessConfig() != other.hasAccessConfig()) return false;
    if (hasAccessConfig()) {
      if (!getAccessConfig()
          .equals(other.getAccessConfig())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ERROR_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorCode().hashCode();
    hash = (37 * hash) + ERROR_MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorMessage().hashCode();
    if (hasAccessConfig()) {
      hash = (37 * hash) + ACCESS_CONFIG_FIELD_NUMBER;
      hash = (53 * hash) + getAccessConfig().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 获取渠道密钥信息
   * </pre>
   *
   * Protobuf type {@code channel.GetAccessConfigResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.GetAccessConfigResponse)
      cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_GetAccessConfigResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_GetAccessConfigResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse.class, cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      errorCode_ = "";

      errorMessage_ = "";

      if (accessConfigBuilder_ == null) {
        accessConfig_ = null;
      } else {
        accessConfig_ = null;
        accessConfigBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_GetAccessConfigResponse_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse build() {
      cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse result = new cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse(this);
      result.errorCode_ = errorCode_;
      result.errorMessage_ = errorMessage_;
      if (accessConfigBuilder_ == null) {
        result.accessConfig_ = accessConfig_;
      } else {
        result.accessConfig_ = accessConfigBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse.getDefaultInstance()) return this;
      if (!other.getErrorCode().isEmpty()) {
        errorCode_ = other.errorCode_;
        onChanged();
      }
      if (!other.getErrorMessage().isEmpty()) {
        errorMessage_ = other.errorMessage_;
        onChanged();
      }
      if (other.hasAccessConfig()) {
        mergeAccessConfig(other.getAccessConfig());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object errorCode_ = "";
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return The errorCode.
     */
    public java.lang.String getErrorCode() {
      java.lang.Object ref = errorCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return The bytes for errorCode.
     */
    public com.google.protobuf.ByteString
        getErrorCodeBytes() {
      java.lang.Object ref = errorCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @param value The errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorCode() {
      
      errorCode_ = getDefaultInstance().getErrorCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @param value The bytes for errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object errorMessage_ = "";
    /**
     * <pre>
     * （必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return The errorMessage.
     */
    public java.lang.String getErrorMessage() {
      java.lang.Object ref = errorMessage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorMessage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return The bytes for errorMessage.
     */
    public com.google.protobuf.ByteString
        getErrorMessageBytes() {
      java.lang.Object ref = errorMessage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorMessage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @param value The errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessage(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorMessage_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorMessage() {
      
      errorMessage_ = getDefaultInstance().getErrorMessage();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @param value The bytes for errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorMessage_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.AccessConfig accessConfig_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.AccessConfig, cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessConfigOrBuilder> accessConfigBuilder_;
    /**
     * <pre>
     * 渠道配置
     * </pre>
     *
     * <code>.channel.AccessConfig access_config = 3;</code>
     * @return Whether the accessConfig field is set.
     */
    public boolean hasAccessConfig() {
      return accessConfigBuilder_ != null || accessConfig_ != null;
    }
    /**
     * <pre>
     * 渠道配置
     * </pre>
     *
     * <code>.channel.AccessConfig access_config = 3;</code>
     * @return The accessConfig.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AccessConfig getAccessConfig() {
      if (accessConfigBuilder_ == null) {
        return accessConfig_ == null ? cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.getDefaultInstance() : accessConfig_;
      } else {
        return accessConfigBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 渠道配置
     * </pre>
     *
     * <code>.channel.AccessConfig access_config = 3;</code>
     */
    public Builder setAccessConfig(cn.hexcloud.pbis.common.service.facade.channel.AccessConfig value) {
      if (accessConfigBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        accessConfig_ = value;
        onChanged();
      } else {
        accessConfigBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 渠道配置
     * </pre>
     *
     * <code>.channel.AccessConfig access_config = 3;</code>
     */
    public Builder setAccessConfig(
        cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.Builder builderForValue) {
      if (accessConfigBuilder_ == null) {
        accessConfig_ = builderForValue.build();
        onChanged();
      } else {
        accessConfigBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 渠道配置
     * </pre>
     *
     * <code>.channel.AccessConfig access_config = 3;</code>
     */
    public Builder mergeAccessConfig(cn.hexcloud.pbis.common.service.facade.channel.AccessConfig value) {
      if (accessConfigBuilder_ == null) {
        if (accessConfig_ != null) {
          accessConfig_ =
            cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.newBuilder(accessConfig_).mergeFrom(value).buildPartial();
        } else {
          accessConfig_ = value;
        }
        onChanged();
      } else {
        accessConfigBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 渠道配置
     * </pre>
     *
     * <code>.channel.AccessConfig access_config = 3;</code>
     */
    public Builder clearAccessConfig() {
      if (accessConfigBuilder_ == null) {
        accessConfig_ = null;
        onChanged();
      } else {
        accessConfig_ = null;
        accessConfigBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 渠道配置
     * </pre>
     *
     * <code>.channel.AccessConfig access_config = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.Builder getAccessConfigBuilder() {
      
      onChanged();
      return getAccessConfigFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 渠道配置
     * </pre>
     *
     * <code>.channel.AccessConfig access_config = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AccessConfigOrBuilder getAccessConfigOrBuilder() {
      if (accessConfigBuilder_ != null) {
        return accessConfigBuilder_.getMessageOrBuilder();
      } else {
        return accessConfig_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.getDefaultInstance() : accessConfig_;
      }
    }
    /**
     * <pre>
     * 渠道配置
     * </pre>
     *
     * <code>.channel.AccessConfig access_config = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.AccessConfig, cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessConfigOrBuilder> 
        getAccessConfigFieldBuilder() {
      if (accessConfigBuilder_ == null) {
        accessConfigBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.AccessConfig, cn.hexcloud.pbis.common.service.facade.channel.AccessConfig.Builder, cn.hexcloud.pbis.common.service.facade.channel.AccessConfigOrBuilder>(
                getAccessConfig(),
                getParentForChildren(),
                isClean());
        accessConfig_ = null;
      }
      return accessConfigBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.GetAccessConfigResponse)
  }

  // @@protoc_insertion_point(class_scope:channel.GetAccessConfigResponse)
  private static final cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GetAccessConfigResponse>
      PARSER = new com.google.protobuf.AbstractParser<GetAccessConfigResponse>() {
    @java.lang.Override
    public GetAccessConfigResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new GetAccessConfigResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<GetAccessConfigResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GetAccessConfigResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.GetAccessConfigResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

