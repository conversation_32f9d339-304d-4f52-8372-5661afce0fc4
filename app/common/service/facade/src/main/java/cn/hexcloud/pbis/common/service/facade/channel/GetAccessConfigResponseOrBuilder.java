// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface GetAccessConfigResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.GetAccessConfigResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  java.lang.String getErrorCode();
  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  com.google.protobuf.ByteString
      getErrorCodeBytes();

  /**
   * <pre>
   * （必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  java.lang.String getErrorMessage();
  /**
   * <pre>
   * （必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  com.google.protobuf.ByteString
      getErrorMessageBytes();

  /**
   * <pre>
   * 渠道配置
   * </pre>
   *
   * <code>.channel.AccessConfig access_config = 3;</code>
   * @return Whether the accessConfig field is set.
   */
  boolean hasAccessConfig();
  /**
   * <pre>
   * 渠道配置
   * </pre>
   *
   * <code>.channel.AccessConfig access_config = 3;</code>
   * @return The accessConfig.
   */
  cn.hexcloud.pbis.common.service.facade.channel.AccessConfig getAccessConfig();
  /**
   * <pre>
   * 渠道配置
   * </pre>
   *
   * <code>.channel.AccessConfig access_config = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.AccessConfigOrBuilder getAccessConfigOrBuilder();
}
