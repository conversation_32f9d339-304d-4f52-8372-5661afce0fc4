// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface GetTokenPayConfigRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.GetTokenPayConfigRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  java.lang.String getChannelCode();
  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  com.google.protobuf.ByteString
      getChannelCodeBytes();

  /**
   * <pre>
   * 门店编码
   * </pre>
   *
   * <code>string store_id = 2;</code>
   * @return The storeId.
   */
  java.lang.String getStoreId();
  /**
   * <pre>
   * 门店编码
   * </pre>
   *
   * <code>string store_id = 2;</code>
   * @return The bytes for storeId.
   */
  com.google.protobuf.ByteString
      getStoreIdBytes();

  /**
   * <pre>
   * 币种
   * </pre>
   *
   * <code>string currency = 3;</code>
   * @return The currency.
   */
  java.lang.String getCurrency();
  /**
   * <pre>
   * 币种
   * </pre>
   *
   * <code>string currency = 3;</code>
   * @return The bytes for currency.
   */
  com.google.protobuf.ByteString
      getCurrencyBytes();

  /**
   * <pre>
   * 支付金额
   * </pre>
   *
   * <code>string amount = 4;</code>
   * @return The amount.
   */
  java.lang.String getAmount();
  /**
   * <pre>
   * 支付金额
   * </pre>
   *
   * <code>string amount = 4;</code>
   * @return The bytes for amount.
   */
  com.google.protobuf.ByteString
      getAmountBytes();

  /**
   * <pre>
   * 单号
   * </pre>
   *
   * <code>string order_no = 5;</code>
   * @return The orderNo.
   */
  java.lang.String getOrderNo();
  /**
   * <pre>
   * 单号
   * </pre>
   *
   * <code>string order_no = 5;</code>
   * @return The bytes for orderNo.
   */
  com.google.protobuf.ByteString
      getOrderNoBytes();

  /**
   * <pre>
   *语言
   * </pre>
   *
   * <code>string language = 6;</code>
   * @return The language.
   */
  java.lang.String getLanguage();
  /**
   * <pre>
   *语言
   * </pre>
   *
   * <code>string language = 6;</code>
   * @return The bytes for language.
   */
  com.google.protobuf.ByteString
      getLanguageBytes();
}
