// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface GetTokenPayConfigResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.GetTokenPayConfigResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  java.lang.String getErrorCode();
  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  com.google.protobuf.ByteString
      getErrorCodeBytes();

  /**
   * <pre>
   * （必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  java.lang.String getErrorMessage();
  /**
   * <pre>
   * （必传）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  com.google.protobuf.ByteString
      getErrorMessageBytes();

  /**
   * <pre>
   * 支付渠道配置
   * </pre>
   *
   * <code>.channel.PayConfig pay_config = 3;</code>
   * @return Whether the payConfig field is set.
   */
  boolean hasPayConfig();
  /**
   * <pre>
   * 支付渠道配置
   * </pre>
   *
   * <code>.channel.PayConfig pay_config = 3;</code>
   * @return The payConfig.
   */
  cn.hexcloud.pbis.common.service.facade.channel.PayConfig getPayConfig();
  /**
   * <pre>
   * 支付渠道配置
   * </pre>
   *
   * <code>.channel.PayConfig pay_config = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.PayConfigOrBuilder getPayConfigOrBuilder();
}
