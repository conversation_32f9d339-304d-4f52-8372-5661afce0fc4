// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * Protobuf type {@code channel.ListAccessQuery}
 */
public final class ListAccessQuery extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.ListAccessQuery)
    ListAccessQueryOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ListAccessQuery.newBuilder() to construct.
  private ListAccessQuery(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ListAccessQuery() {
    channelCode_ = "";
    partnerId_ = "";
    companyId_ = "";
    storeId_ = "";
    businessCode_ = "";
    search_ = "";
    searchFields_ = "";
    channelSubCategory_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ListAccessQuery();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ListAccessQuery(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            channelCode_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            partnerId_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            companyId_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            storeId_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            businessCode_ = s;
            break;
          }
          case 48: {

            level_ = input.readInt32();
            break;
          }
          case 56: {

            pageIndex_ = input.readInt32();
            break;
          }
          case 64: {

            pageSize_ = input.readInt32();
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            search_ = s;
            break;
          }
          case 82: {
            java.lang.String s = input.readStringRequireUtf8();

            searchFields_ = s;
            break;
          }
          case 90: {
            java.lang.String s = input.readStringRequireUtf8();

            channelSubCategory_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ListAccessQuery_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ListAccessQuery_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.class, cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.Builder.class);
  }

  public static final int CHANNEL_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object channelCode_;
  /**
   * <pre>
   * （必传）查询类型
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  @java.lang.Override
  public java.lang.String getChannelCode() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）查询类型
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelCodeBytes() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PARTNER_ID_FIELD_NUMBER = 2;
  private volatile java.lang.Object partnerId_;
  /**
   * <pre>
   * 租户id
   * </pre>
   *
   * <code>string partner_id = 2;</code>
   * @return The partnerId.
   */
  @java.lang.Override
  public java.lang.String getPartnerId() {
    java.lang.Object ref = partnerId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      partnerId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 租户id
   * </pre>
   *
   * <code>string partner_id = 2;</code>
   * @return The bytes for partnerId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPartnerIdBytes() {
    java.lang.Object ref = partnerId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      partnerId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int COMPANY_ID_FIELD_NUMBER = 3;
  private volatile java.lang.Object companyId_;
  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 3;</code>
   * @return The companyId.
   */
  @java.lang.Override
  public java.lang.String getCompanyId() {
    java.lang.Object ref = companyId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      companyId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 3;</code>
   * @return The bytes for companyId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCompanyIdBytes() {
    java.lang.Object ref = companyId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      companyId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STORE_ID_FIELD_NUMBER = 4;
  private volatile java.lang.Object storeId_;
  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 4;</code>
   * @return The storeId.
   */
  @java.lang.Override
  public java.lang.String getStoreId() {
    java.lang.Object ref = storeId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      storeId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 4;</code>
   * @return The bytes for storeId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStoreIdBytes() {
    java.lang.Object ref = storeId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      storeId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BUSINESS_CODE_FIELD_NUMBER = 5;
  private volatile java.lang.Object businessCode_;
  /**
   * <pre>
   * 业务id
   * </pre>
   *
   * <code>string business_code = 5;</code>
   * @return The businessCode.
   */
  @java.lang.Override
  public java.lang.String getBusinessCode() {
    java.lang.Object ref = businessCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      businessCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 业务id
   * </pre>
   *
   * <code>string business_code = 5;</code>
   * @return The bytes for businessCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBusinessCodeBytes() {
    java.lang.Object ref = businessCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      businessCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LEVEL_FIELD_NUMBER = 6;
  private int level_;
  /**
   * <pre>
   * 配置层级
   * </pre>
   *
   * <code>int32 level = 6;</code>
   * @return The level.
   */
  @java.lang.Override
  public int getLevel() {
    return level_;
  }

  public static final int PAGE_INDEX_FIELD_NUMBER = 7;
  private int pageIndex_;
  /**
   * <pre>
   * 分页
   * </pre>
   *
   * <code>int32 page_index = 7;</code>
   * @return The pageIndex.
   */
  @java.lang.Override
  public int getPageIndex() {
    return pageIndex_;
  }

  public static final int PAGE_SIZE_FIELD_NUMBER = 8;
  private int pageSize_;
  /**
   * <code>int32 page_size = 8;</code>
   * @return The pageSize.
   */
  @java.lang.Override
  public int getPageSize() {
    return pageSize_;
  }

  public static final int SEARCH_FIELD_NUMBER = 9;
  private volatile java.lang.Object search_;
  /**
   * <pre>
   * 搜索字段
   * </pre>
   *
   * <code>string search = 9;</code>
   * @return The search.
   */
  @java.lang.Override
  public java.lang.String getSearch() {
    java.lang.Object ref = search_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      search_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 搜索字段
   * </pre>
   *
   * <code>string search = 9;</code>
   * @return The bytes for search.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSearchBytes() {
    java.lang.Object ref = search_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      search_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SEARCH_FIELDS_FIELD_NUMBER = 10;
  private volatile java.lang.Object searchFields_;
  /**
   * <pre>
   * 搜索表字段,分隔
   * </pre>
   *
   * <code>string search_fields = 10;</code>
   * @return The searchFields.
   */
  @java.lang.Override
  public java.lang.String getSearchFields() {
    java.lang.Object ref = searchFields_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      searchFields_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 搜索表字段,分隔
   * </pre>
   *
   * <code>string search_fields = 10;</code>
   * @return The bytes for searchFields.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSearchFieldsBytes() {
    java.lang.Object ref = searchFields_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      searchFields_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHANNEL_SUB_CATEGORY_FIELD_NUMBER = 11;
  private volatile java.lang.Object channelSubCategory_;
  /**
   * <pre>
   * 子分类
   * </pre>
   *
   * <code>string channel_sub_category = 11;</code>
   * @return The channelSubCategory.
   */
  @java.lang.Override
  public java.lang.String getChannelSubCategory() {
    java.lang.Object ref = channelSubCategory_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelSubCategory_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 子分类
   * </pre>
   *
   * <code>string channel_sub_category = 11;</code>
   * @return The bytes for channelSubCategory.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelSubCategoryBytes() {
    java.lang.Object ref = channelSubCategory_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelSubCategory_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getChannelCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelCode_);
    }
    if (!getPartnerIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, partnerId_);
    }
    if (!getCompanyIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, companyId_);
    }
    if (!getStoreIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, storeId_);
    }
    if (!getBusinessCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, businessCode_);
    }
    if (level_ != 0) {
      output.writeInt32(6, level_);
    }
    if (pageIndex_ != 0) {
      output.writeInt32(7, pageIndex_);
    }
    if (pageSize_ != 0) {
      output.writeInt32(8, pageSize_);
    }
    if (!getSearchBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, search_);
    }
    if (!getSearchFieldsBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, searchFields_);
    }
    if (!getChannelSubCategoryBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, channelSubCategory_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getChannelCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelCode_);
    }
    if (!getPartnerIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, partnerId_);
    }
    if (!getCompanyIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, companyId_);
    }
    if (!getStoreIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, storeId_);
    }
    if (!getBusinessCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, businessCode_);
    }
    if (level_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, level_);
    }
    if (pageIndex_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, pageIndex_);
    }
    if (pageSize_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, pageSize_);
    }
    if (!getSearchBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, search_);
    }
    if (!getSearchFieldsBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, searchFields_);
    }
    if (!getChannelSubCategoryBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, channelSubCategory_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery other = (cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery) obj;

    if (!getChannelCode()
        .equals(other.getChannelCode())) return false;
    if (!getPartnerId()
        .equals(other.getPartnerId())) return false;
    if (!getCompanyId()
        .equals(other.getCompanyId())) return false;
    if (!getStoreId()
        .equals(other.getStoreId())) return false;
    if (!getBusinessCode()
        .equals(other.getBusinessCode())) return false;
    if (getLevel()
        != other.getLevel()) return false;
    if (getPageIndex()
        != other.getPageIndex()) return false;
    if (getPageSize()
        != other.getPageSize()) return false;
    if (!getSearch()
        .equals(other.getSearch())) return false;
    if (!getSearchFields()
        .equals(other.getSearchFields())) return false;
    if (!getChannelSubCategory()
        .equals(other.getChannelSubCategory())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHANNEL_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getChannelCode().hashCode();
    hash = (37 * hash) + PARTNER_ID_FIELD_NUMBER;
    hash = (53 * hash) + getPartnerId().hashCode();
    hash = (37 * hash) + COMPANY_ID_FIELD_NUMBER;
    hash = (53 * hash) + getCompanyId().hashCode();
    hash = (37 * hash) + STORE_ID_FIELD_NUMBER;
    hash = (53 * hash) + getStoreId().hashCode();
    hash = (37 * hash) + BUSINESS_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getBusinessCode().hashCode();
    hash = (37 * hash) + LEVEL_FIELD_NUMBER;
    hash = (53 * hash) + getLevel();
    hash = (37 * hash) + PAGE_INDEX_FIELD_NUMBER;
    hash = (53 * hash) + getPageIndex();
    hash = (37 * hash) + PAGE_SIZE_FIELD_NUMBER;
    hash = (53 * hash) + getPageSize();
    hash = (37 * hash) + SEARCH_FIELD_NUMBER;
    hash = (53 * hash) + getSearch().hashCode();
    hash = (37 * hash) + SEARCH_FIELDS_FIELD_NUMBER;
    hash = (53 * hash) + getSearchFields().hashCode();
    hash = (37 * hash) + CHANNEL_SUB_CATEGORY_FIELD_NUMBER;
    hash = (53 * hash) + getChannelSubCategory().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code channel.ListAccessQuery}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.ListAccessQuery)
      cn.hexcloud.pbis.common.service.facade.channel.ListAccessQueryOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ListAccessQuery_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ListAccessQuery_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.class, cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channelCode_ = "";

      partnerId_ = "";

      companyId_ = "";

      storeId_ = "";

      businessCode_ = "";

      level_ = 0;

      pageIndex_ = 0;

      pageSize_ = 0;

      search_ = "";

      searchFields_ = "";

      channelSubCategory_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ListAccessQuery_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery build() {
      cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery result = new cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery(this);
      result.channelCode_ = channelCode_;
      result.partnerId_ = partnerId_;
      result.companyId_ = companyId_;
      result.storeId_ = storeId_;
      result.businessCode_ = businessCode_;
      result.level_ = level_;
      result.pageIndex_ = pageIndex_;
      result.pageSize_ = pageSize_;
      result.search_ = search_;
      result.searchFields_ = searchFields_;
      result.channelSubCategory_ = channelSubCategory_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery.getDefaultInstance()) return this;
      if (!other.getChannelCode().isEmpty()) {
        channelCode_ = other.channelCode_;
        onChanged();
      }
      if (!other.getPartnerId().isEmpty()) {
        partnerId_ = other.partnerId_;
        onChanged();
      }
      if (!other.getCompanyId().isEmpty()) {
        companyId_ = other.companyId_;
        onChanged();
      }
      if (!other.getStoreId().isEmpty()) {
        storeId_ = other.storeId_;
        onChanged();
      }
      if (!other.getBusinessCode().isEmpty()) {
        businessCode_ = other.businessCode_;
        onChanged();
      }
      if (other.getLevel() != 0) {
        setLevel(other.getLevel());
      }
      if (other.getPageIndex() != 0) {
        setPageIndex(other.getPageIndex());
      }
      if (other.getPageSize() != 0) {
        setPageSize(other.getPageSize());
      }
      if (!other.getSearch().isEmpty()) {
        search_ = other.search_;
        onChanged();
      }
      if (!other.getSearchFields().isEmpty()) {
        searchFields_ = other.searchFields_;
        onChanged();
      }
      if (!other.getChannelSubCategory().isEmpty()) {
        channelSubCategory_ = other.channelSubCategory_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object channelCode_ = "";
    /**
     * <pre>
     * （必传）查询类型
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return The channelCode.
     */
    public java.lang.String getChannelCode() {
      java.lang.Object ref = channelCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）查询类型
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return The bytes for channelCode.
     */
    public com.google.protobuf.ByteString
        getChannelCodeBytes() {
      java.lang.Object ref = channelCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）查询类型
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @param value The channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）查询类型
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelCode() {
      
      channelCode_ = getDefaultInstance().getChannelCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）查询类型
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @param value The bytes for channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object partnerId_ = "";
    /**
     * <pre>
     * 租户id
     * </pre>
     *
     * <code>string partner_id = 2;</code>
     * @return The partnerId.
     */
    public java.lang.String getPartnerId() {
      java.lang.Object ref = partnerId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        partnerId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 租户id
     * </pre>
     *
     * <code>string partner_id = 2;</code>
     * @return The bytes for partnerId.
     */
    public com.google.protobuf.ByteString
        getPartnerIdBytes() {
      java.lang.Object ref = partnerId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        partnerId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 租户id
     * </pre>
     *
     * <code>string partner_id = 2;</code>
     * @param value The partnerId to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      partnerId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 租户id
     * </pre>
     *
     * <code>string partner_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearPartnerId() {
      
      partnerId_ = getDefaultInstance().getPartnerId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 租户id
     * </pre>
     *
     * <code>string partner_id = 2;</code>
     * @param value The bytes for partnerId to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      partnerId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object companyId_ = "";
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 3;</code>
     * @return The companyId.
     */
    public java.lang.String getCompanyId() {
      java.lang.Object ref = companyId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        companyId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 3;</code>
     * @return The bytes for companyId.
     */
    public com.google.protobuf.ByteString
        getCompanyIdBytes() {
      java.lang.Object ref = companyId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        companyId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 3;</code>
     * @param value The companyId to set.
     * @return This builder for chaining.
     */
    public Builder setCompanyId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      companyId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearCompanyId() {
      
      companyId_ = getDefaultInstance().getCompanyId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 公司id
     * </pre>
     *
     * <code>string company_id = 3;</code>
     * @param value The bytes for companyId to set.
     * @return This builder for chaining.
     */
    public Builder setCompanyIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      companyId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object storeId_ = "";
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 4;</code>
     * @return The storeId.
     */
    public java.lang.String getStoreId() {
      java.lang.Object ref = storeId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        storeId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 4;</code>
     * @return The bytes for storeId.
     */
    public com.google.protobuf.ByteString
        getStoreIdBytes() {
      java.lang.Object ref = storeId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        storeId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 4;</code>
     * @param value The storeId to set.
     * @return This builder for chaining.
     */
    public Builder setStoreId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      storeId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearStoreId() {
      
      storeId_ = getDefaultInstance().getStoreId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>string store_id = 4;</code>
     * @param value The bytes for storeId to set.
     * @return This builder for chaining.
     */
    public Builder setStoreIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      storeId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object businessCode_ = "";
    /**
     * <pre>
     * 业务id
     * </pre>
     *
     * <code>string business_code = 5;</code>
     * @return The businessCode.
     */
    public java.lang.String getBusinessCode() {
      java.lang.Object ref = businessCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        businessCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 业务id
     * </pre>
     *
     * <code>string business_code = 5;</code>
     * @return The bytes for businessCode.
     */
    public com.google.protobuf.ByteString
        getBusinessCodeBytes() {
      java.lang.Object ref = businessCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        businessCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 业务id
     * </pre>
     *
     * <code>string business_code = 5;</code>
     * @param value The businessCode to set.
     * @return This builder for chaining.
     */
    public Builder setBusinessCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      businessCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 业务id
     * </pre>
     *
     * <code>string business_code = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearBusinessCode() {
      
      businessCode_ = getDefaultInstance().getBusinessCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 业务id
     * </pre>
     *
     * <code>string business_code = 5;</code>
     * @param value The bytes for businessCode to set.
     * @return This builder for chaining.
     */
    public Builder setBusinessCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      businessCode_ = value;
      onChanged();
      return this;
    }

    private int level_ ;
    /**
     * <pre>
     * 配置层级
     * </pre>
     *
     * <code>int32 level = 6;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }
    /**
     * <pre>
     * 配置层级
     * </pre>
     *
     * <code>int32 level = 6;</code>
     * @param value The level to set.
     * @return This builder for chaining.
     */
    public Builder setLevel(int value) {
      
      level_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 配置层级
     * </pre>
     *
     * <code>int32 level = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearLevel() {
      
      level_ = 0;
      onChanged();
      return this;
    }

    private int pageIndex_ ;
    /**
     * <pre>
     * 分页
     * </pre>
     *
     * <code>int32 page_index = 7;</code>
     * @return The pageIndex.
     */
    @java.lang.Override
    public int getPageIndex() {
      return pageIndex_;
    }
    /**
     * <pre>
     * 分页
     * </pre>
     *
     * <code>int32 page_index = 7;</code>
     * @param value The pageIndex to set.
     * @return This builder for chaining.
     */
    public Builder setPageIndex(int value) {
      
      pageIndex_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 分页
     * </pre>
     *
     * <code>int32 page_index = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearPageIndex() {
      
      pageIndex_ = 0;
      onChanged();
      return this;
    }

    private int pageSize_ ;
    /**
     * <code>int32 page_size = 8;</code>
     * @return The pageSize.
     */
    @java.lang.Override
    public int getPageSize() {
      return pageSize_;
    }
    /**
     * <code>int32 page_size = 8;</code>
     * @param value The pageSize to set.
     * @return This builder for chaining.
     */
    public Builder setPageSize(int value) {
      
      pageSize_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>int32 page_size = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearPageSize() {
      
      pageSize_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object search_ = "";
    /**
     * <pre>
     * 搜索字段
     * </pre>
     *
     * <code>string search = 9;</code>
     * @return The search.
     */
    public java.lang.String getSearch() {
      java.lang.Object ref = search_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        search_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 搜索字段
     * </pre>
     *
     * <code>string search = 9;</code>
     * @return The bytes for search.
     */
    public com.google.protobuf.ByteString
        getSearchBytes() {
      java.lang.Object ref = search_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        search_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 搜索字段
     * </pre>
     *
     * <code>string search = 9;</code>
     * @param value The search to set.
     * @return This builder for chaining.
     */
    public Builder setSearch(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      search_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 搜索字段
     * </pre>
     *
     * <code>string search = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearSearch() {
      
      search_ = getDefaultInstance().getSearch();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 搜索字段
     * </pre>
     *
     * <code>string search = 9;</code>
     * @param value The bytes for search to set.
     * @return This builder for chaining.
     */
    public Builder setSearchBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      search_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object searchFields_ = "";
    /**
     * <pre>
     * 搜索表字段,分隔
     * </pre>
     *
     * <code>string search_fields = 10;</code>
     * @return The searchFields.
     */
    public java.lang.String getSearchFields() {
      java.lang.Object ref = searchFields_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        searchFields_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 搜索表字段,分隔
     * </pre>
     *
     * <code>string search_fields = 10;</code>
     * @return The bytes for searchFields.
     */
    public com.google.protobuf.ByteString
        getSearchFieldsBytes() {
      java.lang.Object ref = searchFields_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        searchFields_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 搜索表字段,分隔
     * </pre>
     *
     * <code>string search_fields = 10;</code>
     * @param value The searchFields to set.
     * @return This builder for chaining.
     */
    public Builder setSearchFields(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      searchFields_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 搜索表字段,分隔
     * </pre>
     *
     * <code>string search_fields = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearSearchFields() {
      
      searchFields_ = getDefaultInstance().getSearchFields();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 搜索表字段,分隔
     * </pre>
     *
     * <code>string search_fields = 10;</code>
     * @param value The bytes for searchFields to set.
     * @return This builder for chaining.
     */
    public Builder setSearchFieldsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      searchFields_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object channelSubCategory_ = "";
    /**
     * <pre>
     * 子分类
     * </pre>
     *
     * <code>string channel_sub_category = 11;</code>
     * @return The channelSubCategory.
     */
    public java.lang.String getChannelSubCategory() {
      java.lang.Object ref = channelSubCategory_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelSubCategory_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 子分类
     * </pre>
     *
     * <code>string channel_sub_category = 11;</code>
     * @return The bytes for channelSubCategory.
     */
    public com.google.protobuf.ByteString
        getChannelSubCategoryBytes() {
      java.lang.Object ref = channelSubCategory_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelSubCategory_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 子分类
     * </pre>
     *
     * <code>string channel_sub_category = 11;</code>
     * @param value The channelSubCategory to set.
     * @return This builder for chaining.
     */
    public Builder setChannelSubCategory(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelSubCategory_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 子分类
     * </pre>
     *
     * <code>string channel_sub_category = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelSubCategory() {
      
      channelSubCategory_ = getDefaultInstance().getChannelSubCategory();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 子分类
     * </pre>
     *
     * <code>string channel_sub_category = 11;</code>
     * @param value The bytes for channelSubCategory to set.
     * @return This builder for chaining.
     */
    public Builder setChannelSubCategoryBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelSubCategory_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.ListAccessQuery)
  }

  // @@protoc_insertion_point(class_scope:channel.ListAccessQuery)
  private static final cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ListAccessQuery>
      PARSER = new com.google.protobuf.AbstractParser<ListAccessQuery>() {
    @java.lang.Override
    public ListAccessQuery parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ListAccessQuery(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ListAccessQuery> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ListAccessQuery> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ListAccessQuery getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

