// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ListAccessQueryOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ListAccessQuery)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）查询类型
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  java.lang.String getChannelCode();
  /**
   * <pre>
   * （必传）查询类型
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  com.google.protobuf.ByteString
      getChannelCodeBytes();

  /**
   * <pre>
   * 租户id
   * </pre>
   *
   * <code>string partner_id = 2;</code>
   * @return The partnerId.
   */
  java.lang.String getPartnerId();
  /**
   * <pre>
   * 租户id
   * </pre>
   *
   * <code>string partner_id = 2;</code>
   * @return The bytes for partnerId.
   */
  com.google.protobuf.ByteString
      getPartnerIdBytes();

  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 3;</code>
   * @return The companyId.
   */
  java.lang.String getCompanyId();
  /**
   * <pre>
   * 公司id
   * </pre>
   *
   * <code>string company_id = 3;</code>
   * @return The bytes for companyId.
   */
  com.google.protobuf.ByteString
      getCompanyIdBytes();

  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 4;</code>
   * @return The storeId.
   */
  java.lang.String getStoreId();
  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>string store_id = 4;</code>
   * @return The bytes for storeId.
   */
  com.google.protobuf.ByteString
      getStoreIdBytes();

  /**
   * <pre>
   * 业务id
   * </pre>
   *
   * <code>string business_code = 5;</code>
   * @return The businessCode.
   */
  java.lang.String getBusinessCode();
  /**
   * <pre>
   * 业务id
   * </pre>
   *
   * <code>string business_code = 5;</code>
   * @return The bytes for businessCode.
   */
  com.google.protobuf.ByteString
      getBusinessCodeBytes();

  /**
   * <pre>
   * 配置层级
   * </pre>
   *
   * <code>int32 level = 6;</code>
   * @return The level.
   */
  int getLevel();

  /**
   * <pre>
   * 分页
   * </pre>
   *
   * <code>int32 page_index = 7;</code>
   * @return The pageIndex.
   */
  int getPageIndex();

  /**
   * <code>int32 page_size = 8;</code>
   * @return The pageSize.
   */
  int getPageSize();

  /**
   * <pre>
   * 搜索字段
   * </pre>
   *
   * <code>string search = 9;</code>
   * @return The search.
   */
  java.lang.String getSearch();
  /**
   * <pre>
   * 搜索字段
   * </pre>
   *
   * <code>string search = 9;</code>
   * @return The bytes for search.
   */
  com.google.protobuf.ByteString
      getSearchBytes();

  /**
   * <pre>
   * 搜索表字段,分隔
   * </pre>
   *
   * <code>string search_fields = 10;</code>
   * @return The searchFields.
   */
  java.lang.String getSearchFields();
  /**
   * <pre>
   * 搜索表字段,分隔
   * </pre>
   *
   * <code>string search_fields = 10;</code>
   * @return The bytes for searchFields.
   */
  com.google.protobuf.ByteString
      getSearchFieldsBytes();

  /**
   * <pre>
   * 子分类
   * </pre>
   *
   * <code>string channel_sub_category = 11;</code>
   * @return The channelSubCategory.
   */
  java.lang.String getChannelSubCategory();
  /**
   * <pre>
   * 子分类
   * </pre>
   *
   * <code>string channel_sub_category = 11;</code>
   * @return The bytes for channelSubCategory.
   */
  com.google.protobuf.ByteString
      getChannelSubCategoryBytes();
}
