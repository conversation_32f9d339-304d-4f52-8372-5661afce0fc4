// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ListAuthorizationSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ListAuthorizationSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）渠道代码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  java.lang.String getChannelCode();
  /**
   * <pre>
   * （必传）渠道代码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  com.google.protobuf.ByteString
      getChannelCodeBytes();

  /**
   * <pre>
   * （可选）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
   * </pre>
   *
   * <code>string signup_state = 2;</code>
   * @return The signupState.
   */
  java.lang.String getSignupState();
  /**
   * <pre>
   * （可选）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
   * </pre>
   *
   * <code>string signup_state = 2;</code>
   * @return The bytes for signupState.
   */
  com.google.protobuf.ByteString
      getSignupStateBytes();

  /**
   * <pre>
   * （可选）授权状态，WAITING 待授权；COMPLETE 已授权
   * </pre>
   *
   * <code>string grant_state = 3;</code>
   * @return The grantState.
   */
  java.lang.String getGrantState();
  /**
   * <pre>
   * （可选）授权状态，WAITING 待授权；COMPLETE 已授权
   * </pre>
   *
   * <code>string grant_state = 3;</code>
   * @return The bytes for grantState.
   */
  com.google.protobuf.ByteString
      getGrantStateBytes();

  /**
   * <pre>
   * （可选）复合查询条件，商户第三方商户账号、签约流水号
   * </pre>
   *
   * <code>string complex_criterion = 4;</code>
   * @return The complexCriterion.
   */
  java.lang.String getComplexCriterion();
  /**
   * <pre>
   * （可选）复合查询条件，商户第三方商户账号、签约流水号
   * </pre>
   *
   * <code>string complex_criterion = 4;</code>
   * @return The bytes for complexCriterion.
   */
  com.google.protobuf.ByteString
      getComplexCriterionBytes();

  /**
   * <pre>
   * （可选）当前页码
   * </pre>
   *
   * <code>int32 page_index = 5;</code>
   * @return The pageIndex.
   */
  int getPageIndex();

  /**
   * <pre>
   * （可选）分页步长
   * </pre>
   *
   * <code>int32 page_size = 6;</code>
   * @return The pageSize.
   */
  int getPageSize();
}
