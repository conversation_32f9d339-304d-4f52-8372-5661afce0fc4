// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道绑定关系查询对象
 * </pre>
 *
 * Protobuf type {@code channel.ListBindingSection}
 */
public final class ListBindingSection extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.ListBindingSection)
    ListBindingSectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ListBindingSection.newBuilder() to construct.
  private ListBindingSection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ListBindingSection() {
    channelCategory_ = "";
    enabled_ = "";
    channelLabels_ = "";
    businessCode_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ListBindingSection();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ListBindingSection(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            channelCategory_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            enabled_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            channelLabels_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            businessCode_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ListBindingSection_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ListBindingSection_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.class, cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.Builder.class);
  }

  public static final int CHANNEL_CATEGORY_FIELD_NUMBER = 1;
  private volatile java.lang.Object channelCategory_;
  /**
   * <pre>
   * （可选）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送，多个渠道代码使用","分隔
   * </pre>
   *
   * <code>string channel_category = 1;</code>
   * @return The channelCategory.
   */
  @java.lang.Override
  public java.lang.String getChannelCategory() {
    java.lang.Object ref = channelCategory_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelCategory_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送，多个渠道代码使用","分隔
   * </pre>
   *
   * <code>string channel_category = 1;</code>
   * @return The bytes for channelCategory.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelCategoryBytes() {
    java.lang.Object ref = channelCategory_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelCategory_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ENABLED_FIELD_NUMBER = 2;
  private volatile java.lang.Object enabled_;
  /**
   * <pre>
   * （可选）渠道是否启用
   * </pre>
   *
   * <code>string enabled = 2;</code>
   * @return The enabled.
   */
  @java.lang.Override
  public java.lang.String getEnabled() {
    java.lang.Object ref = enabled_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      enabled_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）渠道是否启用
   * </pre>
   *
   * <code>string enabled = 2;</code>
   * @return The bytes for enabled.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getEnabledBytes() {
    java.lang.Object ref = enabled_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      enabled_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHANNEL_LABELS_FIELD_NUMBER = 3;
  private volatile java.lang.Object channelLabels_;
  /**
   * <pre>
   * （可选）渠道标签，多个以","分割
   * </pre>
   *
   * <code>string channel_labels = 3;</code>
   * @return The channelLabels.
   */
  @java.lang.Override
  public java.lang.String getChannelLabels() {
    java.lang.Object ref = channelLabels_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelLabels_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）渠道标签，多个以","分割
   * </pre>
   *
   * <code>string channel_labels = 3;</code>
   * @return The bytes for channelLabels.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelLabelsBytes() {
    java.lang.Object ref = channelLabels_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelLabels_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BUSINESS_CODE_FIELD_NUMBER = 4;
  private volatile java.lang.Object businessCode_;
  /**
   * <pre>
   *（可选）渠道业务代码，多个以","分割
   * </pre>
   *
   * <code>string business_code = 4;</code>
   * @return The businessCode.
   */
  @java.lang.Override
  public java.lang.String getBusinessCode() {
    java.lang.Object ref = businessCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      businessCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *（可选）渠道业务代码，多个以","分割
   * </pre>
   *
   * <code>string business_code = 4;</code>
   * @return The bytes for businessCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBusinessCodeBytes() {
    java.lang.Object ref = businessCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      businessCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getChannelCategoryBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelCategory_);
    }
    if (!getEnabledBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, enabled_);
    }
    if (!getChannelLabelsBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, channelLabels_);
    }
    if (!getBusinessCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, businessCode_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getChannelCategoryBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelCategory_);
    }
    if (!getEnabledBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, enabled_);
    }
    if (!getChannelLabelsBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, channelLabels_);
    }
    if (!getBusinessCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, businessCode_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection other = (cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection) obj;

    if (!getChannelCategory()
        .equals(other.getChannelCategory())) return false;
    if (!getEnabled()
        .equals(other.getEnabled())) return false;
    if (!getChannelLabels()
        .equals(other.getChannelLabels())) return false;
    if (!getBusinessCode()
        .equals(other.getBusinessCode())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHANNEL_CATEGORY_FIELD_NUMBER;
    hash = (53 * hash) + getChannelCategory().hashCode();
    hash = (37 * hash) + ENABLED_FIELD_NUMBER;
    hash = (53 * hash) + getEnabled().hashCode();
    hash = (37 * hash) + CHANNEL_LABELS_FIELD_NUMBER;
    hash = (53 * hash) + getChannelLabels().hashCode();
    hash = (37 * hash) + BUSINESS_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getBusinessCode().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道绑定关系查询对象
   * </pre>
   *
   * Protobuf type {@code channel.ListBindingSection}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.ListBindingSection)
      cn.hexcloud.pbis.common.service.facade.channel.ListBindingSectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ListBindingSection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ListBindingSection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.class, cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channelCategory_ = "";

      enabled_ = "";

      channelLabels_ = "";

      businessCode_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ListBindingSection_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection build() {
      cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection result = new cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection(this);
      result.channelCategory_ = channelCategory_;
      result.enabled_ = enabled_;
      result.channelLabels_ = channelLabels_;
      result.businessCode_ = businessCode_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection.getDefaultInstance()) return this;
      if (!other.getChannelCategory().isEmpty()) {
        channelCategory_ = other.channelCategory_;
        onChanged();
      }
      if (!other.getEnabled().isEmpty()) {
        enabled_ = other.enabled_;
        onChanged();
      }
      if (!other.getChannelLabels().isEmpty()) {
        channelLabels_ = other.channelLabels_;
        onChanged();
      }
      if (!other.getBusinessCode().isEmpty()) {
        businessCode_ = other.businessCode_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object channelCategory_ = "";
    /**
     * <pre>
     * （可选）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送，多个渠道代码使用","分隔
     * </pre>
     *
     * <code>string channel_category = 1;</code>
     * @return The channelCategory.
     */
    public java.lang.String getChannelCategory() {
      java.lang.Object ref = channelCategory_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelCategory_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送，多个渠道代码使用","分隔
     * </pre>
     *
     * <code>string channel_category = 1;</code>
     * @return The bytes for channelCategory.
     */
    public com.google.protobuf.ByteString
        getChannelCategoryBytes() {
      java.lang.Object ref = channelCategory_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelCategory_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送，多个渠道代码使用","分隔
     * </pre>
     *
     * <code>string channel_category = 1;</code>
     * @param value The channelCategory to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCategory(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelCategory_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送，多个渠道代码使用","分隔
     * </pre>
     *
     * <code>string channel_category = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelCategory() {
      
      channelCategory_ = getDefaultInstance().getChannelCategory();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送，多个渠道代码使用","分隔
     * </pre>
     *
     * <code>string channel_category = 1;</code>
     * @param value The bytes for channelCategory to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCategoryBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelCategory_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object enabled_ = "";
    /**
     * <pre>
     * （可选）渠道是否启用
     * </pre>
     *
     * <code>string enabled = 2;</code>
     * @return The enabled.
     */
    public java.lang.String getEnabled() {
      java.lang.Object ref = enabled_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        enabled_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）渠道是否启用
     * </pre>
     *
     * <code>string enabled = 2;</code>
     * @return The bytes for enabled.
     */
    public com.google.protobuf.ByteString
        getEnabledBytes() {
      java.lang.Object ref = enabled_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        enabled_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）渠道是否启用
     * </pre>
     *
     * <code>string enabled = 2;</code>
     * @param value The enabled to set.
     * @return This builder for chaining.
     */
    public Builder setEnabled(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      enabled_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）渠道是否启用
     * </pre>
     *
     * <code>string enabled = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearEnabled() {
      
      enabled_ = getDefaultInstance().getEnabled();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）渠道是否启用
     * </pre>
     *
     * <code>string enabled = 2;</code>
     * @param value The bytes for enabled to set.
     * @return This builder for chaining.
     */
    public Builder setEnabledBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      enabled_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object channelLabels_ = "";
    /**
     * <pre>
     * （可选）渠道标签，多个以","分割
     * </pre>
     *
     * <code>string channel_labels = 3;</code>
     * @return The channelLabels.
     */
    public java.lang.String getChannelLabels() {
      java.lang.Object ref = channelLabels_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelLabels_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）渠道标签，多个以","分割
     * </pre>
     *
     * <code>string channel_labels = 3;</code>
     * @return The bytes for channelLabels.
     */
    public com.google.protobuf.ByteString
        getChannelLabelsBytes() {
      java.lang.Object ref = channelLabels_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelLabels_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）渠道标签，多个以","分割
     * </pre>
     *
     * <code>string channel_labels = 3;</code>
     * @param value The channelLabels to set.
     * @return This builder for chaining.
     */
    public Builder setChannelLabels(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelLabels_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）渠道标签，多个以","分割
     * </pre>
     *
     * <code>string channel_labels = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelLabels() {
      
      channelLabels_ = getDefaultInstance().getChannelLabels();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）渠道标签，多个以","分割
     * </pre>
     *
     * <code>string channel_labels = 3;</code>
     * @param value The bytes for channelLabels to set.
     * @return This builder for chaining.
     */
    public Builder setChannelLabelsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelLabels_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object businessCode_ = "";
    /**
     * <pre>
     *（可选）渠道业务代码，多个以","分割
     * </pre>
     *
     * <code>string business_code = 4;</code>
     * @return The businessCode.
     */
    public java.lang.String getBusinessCode() {
      java.lang.Object ref = businessCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        businessCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *（可选）渠道业务代码，多个以","分割
     * </pre>
     *
     * <code>string business_code = 4;</code>
     * @return The bytes for businessCode.
     */
    public com.google.protobuf.ByteString
        getBusinessCodeBytes() {
      java.lang.Object ref = businessCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        businessCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *（可选）渠道业务代码，多个以","分割
     * </pre>
     *
     * <code>string business_code = 4;</code>
     * @param value The businessCode to set.
     * @return This builder for chaining.
     */
    public Builder setBusinessCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      businessCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（可选）渠道业务代码，多个以","分割
     * </pre>
     *
     * <code>string business_code = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearBusinessCode() {
      
      businessCode_ = getDefaultInstance().getBusinessCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（可选）渠道业务代码，多个以","分割
     * </pre>
     *
     * <code>string business_code = 4;</code>
     * @param value The bytes for businessCode to set.
     * @return This builder for chaining.
     */
    public Builder setBusinessCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      businessCode_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.ListBindingSection)
  }

  // @@protoc_insertion_point(class_scope:channel.ListBindingSection)
  private static final cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ListBindingSection>
      PARSER = new com.google.protobuf.AbstractParser<ListBindingSection>() {
    @java.lang.Override
    public ListBindingSection parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ListBindingSection(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ListBindingSection> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ListBindingSection> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ListBindingSection getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

