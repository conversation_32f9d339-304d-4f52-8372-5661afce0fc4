// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ListBindingSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ListBindingSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （可选）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送，多个渠道代码使用","分隔
   * </pre>
   *
   * <code>string channel_category = 1;</code>
   * @return The channelCategory.
   */
  java.lang.String getChannelCategory();
  /**
   * <pre>
   * （可选）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送，多个渠道代码使用","分隔
   * </pre>
   *
   * <code>string channel_category = 1;</code>
   * @return The bytes for channelCategory.
   */
  com.google.protobuf.ByteString
      getChannelCategoryBytes();

  /**
   * <pre>
   * （可选）渠道是否启用
   * </pre>
   *
   * <code>string enabled = 2;</code>
   * @return The enabled.
   */
  java.lang.String getEnabled();
  /**
   * <pre>
   * （可选）渠道是否启用
   * </pre>
   *
   * <code>string enabled = 2;</code>
   * @return The bytes for enabled.
   */
  com.google.protobuf.ByteString
      getEnabledBytes();

  /**
   * <pre>
   * （可选）渠道标签，多个以","分割
   * </pre>
   *
   * <code>string channel_labels = 3;</code>
   * @return The channelLabels.
   */
  java.lang.String getChannelLabels();
  /**
   * <pre>
   * （可选）渠道标签，多个以","分割
   * </pre>
   *
   * <code>string channel_labels = 3;</code>
   * @return The bytes for channelLabels.
   */
  com.google.protobuf.ByteString
      getChannelLabelsBytes();

  /**
   * <pre>
   *（可选）渠道业务代码，多个以","分割
   * </pre>
   *
   * <code>string business_code = 4;</code>
   * @return The businessCode.
   */
  java.lang.String getBusinessCode();
  /**
   * <pre>
   *（可选）渠道业务代码，多个以","分割
   * </pre>
   *
   * <code>string business_code = 4;</code>
   * @return The bytes for businessCode.
   */
  com.google.protobuf.ByteString
      getBusinessCodeBytes();
}
