// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道列表查询
 * </pre>
 *
 * Protobuf type {@code channel.ListChannelQuery}
 */
public final class ListChannelQuery extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.ListChannelQuery)
    ListChannelQueryOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ListChannelQuery.newBuilder() to construct.
  private ListChannelQuery(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ListChannelQuery() {
    channelCategory_ = "";
    searchName_ = "";
    channelCode_ = "";
    channelSubCategory_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ListChannelQuery();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ListChannelQuery(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            channelCategory_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            searchName_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            channelCode_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            channelSubCategory_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ListChannelQuery_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ListChannelQuery_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.class, cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.Builder.class);
  }

  public static final int CHANNEL_CATEGORY_FIELD_NUMBER = 1;
  private volatile java.lang.Object channelCategory_;
  /**
   * <pre>
   * （必传）查询类型
   * </pre>
   *
   * <code>string channel_category = 1;</code>
   * @return The channelCategory.
   */
  @java.lang.Override
  public java.lang.String getChannelCategory() {
    java.lang.Object ref = channelCategory_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelCategory_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）查询类型
   * </pre>
   *
   * <code>string channel_category = 1;</code>
   * @return The bytes for channelCategory.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelCategoryBytes() {
    java.lang.Object ref = channelCategory_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelCategory_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SEARCH_NAME_FIELD_NUMBER = 2;
  private volatile java.lang.Object searchName_;
  /**
   * <pre>
   * 搜索名称
   * </pre>
   *
   * <code>string search_name = 2;</code>
   * @return The searchName.
   */
  @java.lang.Override
  public java.lang.String getSearchName() {
    java.lang.Object ref = searchName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      searchName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 搜索名称
   * </pre>
   *
   * <code>string search_name = 2;</code>
   * @return The bytes for searchName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSearchNameBytes() {
    java.lang.Object ref = searchName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      searchName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHANNEL_CODE_FIELD_NUMBER = 3;
  private volatile java.lang.Object channelCode_;
  /**
   * <pre>
   * 渠道code
   * </pre>
   *
   * <code>string channel_code = 3;</code>
   * @return The channelCode.
   */
  @java.lang.Override
  public java.lang.String getChannelCode() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道code
   * </pre>
   *
   * <code>string channel_code = 3;</code>
   * @return The bytes for channelCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelCodeBytes() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHANNEL_SUB_CATEGORY_FIELD_NUMBER = 4;
  private volatile java.lang.Object channelSubCategory_;
  /**
   * <pre>
   * 子分类
   * </pre>
   *
   * <code>string channel_sub_category = 4;</code>
   * @return The channelSubCategory.
   */
  @java.lang.Override
  public java.lang.String getChannelSubCategory() {
    java.lang.Object ref = channelSubCategory_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelSubCategory_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 子分类
   * </pre>
   *
   * <code>string channel_sub_category = 4;</code>
   * @return The bytes for channelSubCategory.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelSubCategoryBytes() {
    java.lang.Object ref = channelSubCategory_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelSubCategory_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getChannelCategoryBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelCategory_);
    }
    if (!getSearchNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, searchName_);
    }
    if (!getChannelCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, channelCode_);
    }
    if (!getChannelSubCategoryBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, channelSubCategory_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getChannelCategoryBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelCategory_);
    }
    if (!getSearchNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, searchName_);
    }
    if (!getChannelCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, channelCode_);
    }
    if (!getChannelSubCategoryBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, channelSubCategory_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery other = (cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery) obj;

    if (!getChannelCategory()
        .equals(other.getChannelCategory())) return false;
    if (!getSearchName()
        .equals(other.getSearchName())) return false;
    if (!getChannelCode()
        .equals(other.getChannelCode())) return false;
    if (!getChannelSubCategory()
        .equals(other.getChannelSubCategory())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHANNEL_CATEGORY_FIELD_NUMBER;
    hash = (53 * hash) + getChannelCategory().hashCode();
    hash = (37 * hash) + SEARCH_NAME_FIELD_NUMBER;
    hash = (53 * hash) + getSearchName().hashCode();
    hash = (37 * hash) + CHANNEL_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getChannelCode().hashCode();
    hash = (37 * hash) + CHANNEL_SUB_CATEGORY_FIELD_NUMBER;
    hash = (53 * hash) + getChannelSubCategory().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道列表查询
   * </pre>
   *
   * Protobuf type {@code channel.ListChannelQuery}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.ListChannelQuery)
      cn.hexcloud.pbis.common.service.facade.channel.ListChannelQueryOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ListChannelQuery_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ListChannelQuery_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.class, cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channelCategory_ = "";

      searchName_ = "";

      channelCode_ = "";

      channelSubCategory_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_ListChannelQuery_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery build() {
      cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery result = new cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery(this);
      result.channelCategory_ = channelCategory_;
      result.searchName_ = searchName_;
      result.channelCode_ = channelCode_;
      result.channelSubCategory_ = channelSubCategory_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery.getDefaultInstance()) return this;
      if (!other.getChannelCategory().isEmpty()) {
        channelCategory_ = other.channelCategory_;
        onChanged();
      }
      if (!other.getSearchName().isEmpty()) {
        searchName_ = other.searchName_;
        onChanged();
      }
      if (!other.getChannelCode().isEmpty()) {
        channelCode_ = other.channelCode_;
        onChanged();
      }
      if (!other.getChannelSubCategory().isEmpty()) {
        channelSubCategory_ = other.channelSubCategory_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object channelCategory_ = "";
    /**
     * <pre>
     * （必传）查询类型
     * </pre>
     *
     * <code>string channel_category = 1;</code>
     * @return The channelCategory.
     */
    public java.lang.String getChannelCategory() {
      java.lang.Object ref = channelCategory_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelCategory_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）查询类型
     * </pre>
     *
     * <code>string channel_category = 1;</code>
     * @return The bytes for channelCategory.
     */
    public com.google.protobuf.ByteString
        getChannelCategoryBytes() {
      java.lang.Object ref = channelCategory_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelCategory_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）查询类型
     * </pre>
     *
     * <code>string channel_category = 1;</code>
     * @param value The channelCategory to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCategory(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelCategory_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）查询类型
     * </pre>
     *
     * <code>string channel_category = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelCategory() {
      
      channelCategory_ = getDefaultInstance().getChannelCategory();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）查询类型
     * </pre>
     *
     * <code>string channel_category = 1;</code>
     * @param value The bytes for channelCategory to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCategoryBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelCategory_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object searchName_ = "";
    /**
     * <pre>
     * 搜索名称
     * </pre>
     *
     * <code>string search_name = 2;</code>
     * @return The searchName.
     */
    public java.lang.String getSearchName() {
      java.lang.Object ref = searchName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        searchName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 搜索名称
     * </pre>
     *
     * <code>string search_name = 2;</code>
     * @return The bytes for searchName.
     */
    public com.google.protobuf.ByteString
        getSearchNameBytes() {
      java.lang.Object ref = searchName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        searchName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 搜索名称
     * </pre>
     *
     * <code>string search_name = 2;</code>
     * @param value The searchName to set.
     * @return This builder for chaining.
     */
    public Builder setSearchName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      searchName_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 搜索名称
     * </pre>
     *
     * <code>string search_name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSearchName() {
      
      searchName_ = getDefaultInstance().getSearchName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 搜索名称
     * </pre>
     *
     * <code>string search_name = 2;</code>
     * @param value The bytes for searchName to set.
     * @return This builder for chaining.
     */
    public Builder setSearchNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      searchName_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object channelCode_ = "";
    /**
     * <pre>
     * 渠道code
     * </pre>
     *
     * <code>string channel_code = 3;</code>
     * @return The channelCode.
     */
    public java.lang.String getChannelCode() {
      java.lang.Object ref = channelCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道code
     * </pre>
     *
     * <code>string channel_code = 3;</code>
     * @return The bytes for channelCode.
     */
    public com.google.protobuf.ByteString
        getChannelCodeBytes() {
      java.lang.Object ref = channelCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道code
     * </pre>
     *
     * <code>string channel_code = 3;</code>
     * @param value The channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道code
     * </pre>
     *
     * <code>string channel_code = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelCode() {
      
      channelCode_ = getDefaultInstance().getChannelCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道code
     * </pre>
     *
     * <code>string channel_code = 3;</code>
     * @param value The bytes for channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object channelSubCategory_ = "";
    /**
     * <pre>
     * 子分类
     * </pre>
     *
     * <code>string channel_sub_category = 4;</code>
     * @return The channelSubCategory.
     */
    public java.lang.String getChannelSubCategory() {
      java.lang.Object ref = channelSubCategory_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelSubCategory_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 子分类
     * </pre>
     *
     * <code>string channel_sub_category = 4;</code>
     * @return The bytes for channelSubCategory.
     */
    public com.google.protobuf.ByteString
        getChannelSubCategoryBytes() {
      java.lang.Object ref = channelSubCategory_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelSubCategory_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 子分类
     * </pre>
     *
     * <code>string channel_sub_category = 4;</code>
     * @param value The channelSubCategory to set.
     * @return This builder for chaining.
     */
    public Builder setChannelSubCategory(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelSubCategory_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 子分类
     * </pre>
     *
     * <code>string channel_sub_category = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelSubCategory() {
      
      channelSubCategory_ = getDefaultInstance().getChannelSubCategory();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 子分类
     * </pre>
     *
     * <code>string channel_sub_category = 4;</code>
     * @param value The bytes for channelSubCategory to set.
     * @return This builder for chaining.
     */
    public Builder setChannelSubCategoryBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelSubCategory_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.ListChannelQuery)
  }

  // @@protoc_insertion_point(class_scope:channel.ListChannelQuery)
  private static final cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ListChannelQuery>
      PARSER = new com.google.protobuf.AbstractParser<ListChannelQuery>() {
    @java.lang.Override
    public ListChannelQuery parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ListChannelQuery(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ListChannelQuery> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ListChannelQuery> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ListChannelQuery getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

