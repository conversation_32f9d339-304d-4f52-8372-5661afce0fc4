// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ListChannelQueryOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ListChannelQuery)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）查询类型
   * </pre>
   *
   * <code>string channel_category = 1;</code>
   * @return The channelCategory.
   */
  java.lang.String getChannelCategory();
  /**
   * <pre>
   * （必传）查询类型
   * </pre>
   *
   * <code>string channel_category = 1;</code>
   * @return The bytes for channelCategory.
   */
  com.google.protobuf.ByteString
      getChannelCategoryBytes();

  /**
   * <pre>
   * 搜索名称
   * </pre>
   *
   * <code>string search_name = 2;</code>
   * @return The searchName.
   */
  java.lang.String getSearchName();
  /**
   * <pre>
   * 搜索名称
   * </pre>
   *
   * <code>string search_name = 2;</code>
   * @return The bytes for searchName.
   */
  com.google.protobuf.ByteString
      getSearchNameBytes();

  /**
   * <pre>
   * 渠道code
   * </pre>
   *
   * <code>string channel_code = 3;</code>
   * @return The channelCode.
   */
  java.lang.String getChannelCode();
  /**
   * <pre>
   * 渠道code
   * </pre>
   *
   * <code>string channel_code = 3;</code>
   * @return The bytes for channelCode.
   */
  com.google.protobuf.ByteString
      getChannelCodeBytes();

  /**
   * <pre>
   * 子分类
   * </pre>
   *
   * <code>string channel_sub_category = 4;</code>
   * @return The channelSubCategory.
   */
  java.lang.String getChannelSubCategory();
  /**
   * <pre>
   * 子分类
   * </pre>
   *
   * <code>string channel_sub_category = 4;</code>
   * @return The bytes for channelSubCategory.
   */
  com.google.protobuf.ByteString
      getChannelSubCategoryBytes();
}
