// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface ListScriptQueryOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.ListScriptQuery)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string enabled = 1;</code>
   * @return The enabled.
   */
  java.lang.String getEnabled();
  /**
   * <code>string enabled = 1;</code>
   * @return The bytes for enabled.
   */
  com.google.protobuf.ByteString
      getEnabledBytes();

  /**
   * <code>string channel_code = 2;</code>
   * @return The channelCode.
   */
  java.lang.String getChannelCode();
  /**
   * <code>string channel_code = 2;</code>
   * @return The bytes for channelCode.
   */
  com.google.protobuf.ByteString
      getChannelCodeBytes();

  /**
   * <code>string script_key = 3;</code>
   * @return The scriptKey.
   */
  java.lang.String getScriptKey();
  /**
   * <code>string script_key = 3;</code>
   * @return The bytes for scriptKey.
   */
  com.google.protobuf.ByteString
      getScriptKeyBytes();

  /**
   * <code>string version = 4;</code>
   * @return The version.
   */
  java.lang.String getVersion();
  /**
   * <code>string version = 4;</code>
   * @return The bytes for version.
   */
  com.google.protobuf.ByteString
      getVersionBytes();
}
