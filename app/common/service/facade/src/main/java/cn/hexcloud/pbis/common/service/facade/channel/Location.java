// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 所在地信息
 * </pre>
 *
 * Protobuf type {@code channel.Location}
 */
public final class Location extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.Location)
    LocationOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Location.newBuilder() to construct.
  private Location(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Location() {
    countryCode_ = "";
    provinceCode_ = "";
    cityCode_ = "";
    districtCode_ = "";
    address_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Location();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Location(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            countryCode_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            provinceCode_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            cityCode_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            districtCode_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            address_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_Location_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_Location_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.Location.class, cn.hexcloud.pbis.common.service.facade.channel.Location.Builder.class);
  }

  public static final int COUNTRY_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object countryCode_;
  /**
   * <pre>
   * （可选）国家代码
   * </pre>
   *
   * <code>string country_code = 1;</code>
   * @return The countryCode.
   */
  @java.lang.Override
  public java.lang.String getCountryCode() {
    java.lang.Object ref = countryCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      countryCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）国家代码
   * </pre>
   *
   * <code>string country_code = 1;</code>
   * @return The bytes for countryCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCountryCodeBytes() {
    java.lang.Object ref = countryCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      countryCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PROVINCE_CODE_FIELD_NUMBER = 2;
  private volatile java.lang.Object provinceCode_;
  /**
   * <pre>
   * （可选）省份代码
   * </pre>
   *
   * <code>string province_code = 2;</code>
   * @return The provinceCode.
   */
  @java.lang.Override
  public java.lang.String getProvinceCode() {
    java.lang.Object ref = provinceCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      provinceCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）省份代码
   * </pre>
   *
   * <code>string province_code = 2;</code>
   * @return The bytes for provinceCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getProvinceCodeBytes() {
    java.lang.Object ref = provinceCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      provinceCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CITY_CODE_FIELD_NUMBER = 3;
  private volatile java.lang.Object cityCode_;
  /**
   * <pre>
   * （可选）城市代码
   * </pre>
   *
   * <code>string city_code = 3;</code>
   * @return The cityCode.
   */
  @java.lang.Override
  public java.lang.String getCityCode() {
    java.lang.Object ref = cityCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      cityCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）城市代码
   * </pre>
   *
   * <code>string city_code = 3;</code>
   * @return The bytes for cityCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCityCodeBytes() {
    java.lang.Object ref = cityCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      cityCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DISTRICT_CODE_FIELD_NUMBER = 4;
  private volatile java.lang.Object districtCode_;
  /**
   * <pre>
   * （可选）行政区代码
   * </pre>
   *
   * <code>string district_code = 4;</code>
   * @return The districtCode.
   */
  @java.lang.Override
  public java.lang.String getDistrictCode() {
    java.lang.Object ref = districtCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      districtCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）行政区代码
   * </pre>
   *
   * <code>string district_code = 4;</code>
   * @return The bytes for districtCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDistrictCodeBytes() {
    java.lang.Object ref = districtCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      districtCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ADDRESS_FIELD_NUMBER = 5;
  private volatile java.lang.Object address_;
  /**
   * <pre>
   * （可选）详细地址
   * </pre>
   *
   * <code>string address = 5;</code>
   * @return The address.
   */
  @java.lang.Override
  public java.lang.String getAddress() {
    java.lang.Object ref = address_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      address_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）详细地址
   * </pre>
   *
   * <code>string address = 5;</code>
   * @return The bytes for address.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAddressBytes() {
    java.lang.Object ref = address_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      address_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getCountryCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, countryCode_);
    }
    if (!getProvinceCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, provinceCode_);
    }
    if (!getCityCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, cityCode_);
    }
    if (!getDistrictCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, districtCode_);
    }
    if (!getAddressBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, address_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getCountryCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, countryCode_);
    }
    if (!getProvinceCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, provinceCode_);
    }
    if (!getCityCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, cityCode_);
    }
    if (!getDistrictCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, districtCode_);
    }
    if (!getAddressBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, address_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.Location)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.Location other = (cn.hexcloud.pbis.common.service.facade.channel.Location) obj;

    if (!getCountryCode()
        .equals(other.getCountryCode())) return false;
    if (!getProvinceCode()
        .equals(other.getProvinceCode())) return false;
    if (!getCityCode()
        .equals(other.getCityCode())) return false;
    if (!getDistrictCode()
        .equals(other.getDistrictCode())) return false;
    if (!getAddress()
        .equals(other.getAddress())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + COUNTRY_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCountryCode().hashCode();
    hash = (37 * hash) + PROVINCE_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getProvinceCode().hashCode();
    hash = (37 * hash) + CITY_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCityCode().hashCode();
    hash = (37 * hash) + DISTRICT_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getDistrictCode().hashCode();
    hash = (37 * hash) + ADDRESS_FIELD_NUMBER;
    hash = (53 * hash) + getAddress().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.Location parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Location parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Location parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Location parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Location parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Location parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Location parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Location parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Location parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Location parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Location parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.Location parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.Location prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 所在地信息
   * </pre>
   *
   * Protobuf type {@code channel.Location}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.Location)
      cn.hexcloud.pbis.common.service.facade.channel.LocationOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_Location_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_Location_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.Location.class, cn.hexcloud.pbis.common.service.facade.channel.Location.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.Location.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      countryCode_ = "";

      provinceCode_ = "";

      cityCode_ = "";

      districtCode_ = "";

      address_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_Location_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.Location getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.Location.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.Location build() {
      cn.hexcloud.pbis.common.service.facade.channel.Location result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.Location buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.Location result = new cn.hexcloud.pbis.common.service.facade.channel.Location(this);
      result.countryCode_ = countryCode_;
      result.provinceCode_ = provinceCode_;
      result.cityCode_ = cityCode_;
      result.districtCode_ = districtCode_;
      result.address_ = address_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.Location) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.Location)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.Location other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.Location.getDefaultInstance()) return this;
      if (!other.getCountryCode().isEmpty()) {
        countryCode_ = other.countryCode_;
        onChanged();
      }
      if (!other.getProvinceCode().isEmpty()) {
        provinceCode_ = other.provinceCode_;
        onChanged();
      }
      if (!other.getCityCode().isEmpty()) {
        cityCode_ = other.cityCode_;
        onChanged();
      }
      if (!other.getDistrictCode().isEmpty()) {
        districtCode_ = other.districtCode_;
        onChanged();
      }
      if (!other.getAddress().isEmpty()) {
        address_ = other.address_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.Location parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.Location) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object countryCode_ = "";
    /**
     * <pre>
     * （可选）国家代码
     * </pre>
     *
     * <code>string country_code = 1;</code>
     * @return The countryCode.
     */
    public java.lang.String getCountryCode() {
      java.lang.Object ref = countryCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        countryCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）国家代码
     * </pre>
     *
     * <code>string country_code = 1;</code>
     * @return The bytes for countryCode.
     */
    public com.google.protobuf.ByteString
        getCountryCodeBytes() {
      java.lang.Object ref = countryCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        countryCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）国家代码
     * </pre>
     *
     * <code>string country_code = 1;</code>
     * @param value The countryCode to set.
     * @return This builder for chaining.
     */
    public Builder setCountryCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      countryCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）国家代码
     * </pre>
     *
     * <code>string country_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearCountryCode() {
      
      countryCode_ = getDefaultInstance().getCountryCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）国家代码
     * </pre>
     *
     * <code>string country_code = 1;</code>
     * @param value The bytes for countryCode to set.
     * @return This builder for chaining.
     */
    public Builder setCountryCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      countryCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object provinceCode_ = "";
    /**
     * <pre>
     * （可选）省份代码
     * </pre>
     *
     * <code>string province_code = 2;</code>
     * @return The provinceCode.
     */
    public java.lang.String getProvinceCode() {
      java.lang.Object ref = provinceCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        provinceCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）省份代码
     * </pre>
     *
     * <code>string province_code = 2;</code>
     * @return The bytes for provinceCode.
     */
    public com.google.protobuf.ByteString
        getProvinceCodeBytes() {
      java.lang.Object ref = provinceCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        provinceCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）省份代码
     * </pre>
     *
     * <code>string province_code = 2;</code>
     * @param value The provinceCode to set.
     * @return This builder for chaining.
     */
    public Builder setProvinceCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      provinceCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）省份代码
     * </pre>
     *
     * <code>string province_code = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearProvinceCode() {
      
      provinceCode_ = getDefaultInstance().getProvinceCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）省份代码
     * </pre>
     *
     * <code>string province_code = 2;</code>
     * @param value The bytes for provinceCode to set.
     * @return This builder for chaining.
     */
    public Builder setProvinceCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      provinceCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object cityCode_ = "";
    /**
     * <pre>
     * （可选）城市代码
     * </pre>
     *
     * <code>string city_code = 3;</code>
     * @return The cityCode.
     */
    public java.lang.String getCityCode() {
      java.lang.Object ref = cityCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        cityCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）城市代码
     * </pre>
     *
     * <code>string city_code = 3;</code>
     * @return The bytes for cityCode.
     */
    public com.google.protobuf.ByteString
        getCityCodeBytes() {
      java.lang.Object ref = cityCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cityCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）城市代码
     * </pre>
     *
     * <code>string city_code = 3;</code>
     * @param value The cityCode to set.
     * @return This builder for chaining.
     */
    public Builder setCityCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      cityCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）城市代码
     * </pre>
     *
     * <code>string city_code = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearCityCode() {
      
      cityCode_ = getDefaultInstance().getCityCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）城市代码
     * </pre>
     *
     * <code>string city_code = 3;</code>
     * @param value The bytes for cityCode to set.
     * @return This builder for chaining.
     */
    public Builder setCityCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      cityCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object districtCode_ = "";
    /**
     * <pre>
     * （可选）行政区代码
     * </pre>
     *
     * <code>string district_code = 4;</code>
     * @return The districtCode.
     */
    public java.lang.String getDistrictCode() {
      java.lang.Object ref = districtCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        districtCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）行政区代码
     * </pre>
     *
     * <code>string district_code = 4;</code>
     * @return The bytes for districtCode.
     */
    public com.google.protobuf.ByteString
        getDistrictCodeBytes() {
      java.lang.Object ref = districtCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        districtCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）行政区代码
     * </pre>
     *
     * <code>string district_code = 4;</code>
     * @param value The districtCode to set.
     * @return This builder for chaining.
     */
    public Builder setDistrictCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      districtCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）行政区代码
     * </pre>
     *
     * <code>string district_code = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearDistrictCode() {
      
      districtCode_ = getDefaultInstance().getDistrictCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）行政区代码
     * </pre>
     *
     * <code>string district_code = 4;</code>
     * @param value The bytes for districtCode to set.
     * @return This builder for chaining.
     */
    public Builder setDistrictCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      districtCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object address_ = "";
    /**
     * <pre>
     * （可选）详细地址
     * </pre>
     *
     * <code>string address = 5;</code>
     * @return The address.
     */
    public java.lang.String getAddress() {
      java.lang.Object ref = address_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        address_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）详细地址
     * </pre>
     *
     * <code>string address = 5;</code>
     * @return The bytes for address.
     */
    public com.google.protobuf.ByteString
        getAddressBytes() {
      java.lang.Object ref = address_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        address_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）详细地址
     * </pre>
     *
     * <code>string address = 5;</code>
     * @param value The address to set.
     * @return This builder for chaining.
     */
    public Builder setAddress(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      address_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）详细地址
     * </pre>
     *
     * <code>string address = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearAddress() {
      
      address_ = getDefaultInstance().getAddress();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）详细地址
     * </pre>
     *
     * <code>string address = 5;</code>
     * @param value The bytes for address to set.
     * @return This builder for chaining.
     */
    public Builder setAddressBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      address_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.Location)
  }

  // @@protoc_insertion_point(class_scope:channel.Location)
  private static final cn.hexcloud.pbis.common.service.facade.channel.Location DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.Location();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.Location getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Location>
      PARSER = new com.google.protobuf.AbstractParser<Location>() {
    @java.lang.Override
    public Location parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Location(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Location> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Location> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.Location getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

