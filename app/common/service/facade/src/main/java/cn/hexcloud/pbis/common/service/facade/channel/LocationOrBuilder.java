// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface LocationOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.Location)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （可选）国家代码
   * </pre>
   *
   * <code>string country_code = 1;</code>
   * @return The countryCode.
   */
  java.lang.String getCountryCode();
  /**
   * <pre>
   * （可选）国家代码
   * </pre>
   *
   * <code>string country_code = 1;</code>
   * @return The bytes for countryCode.
   */
  com.google.protobuf.ByteString
      getCountryCodeBytes();

  /**
   * <pre>
   * （可选）省份代码
   * </pre>
   *
   * <code>string province_code = 2;</code>
   * @return The provinceCode.
   */
  java.lang.String getProvinceCode();
  /**
   * <pre>
   * （可选）省份代码
   * </pre>
   *
   * <code>string province_code = 2;</code>
   * @return The bytes for provinceCode.
   */
  com.google.protobuf.ByteString
      getProvinceCodeBytes();

  /**
   * <pre>
   * （可选）城市代码
   * </pre>
   *
   * <code>string city_code = 3;</code>
   * @return The cityCode.
   */
  java.lang.String getCityCode();
  /**
   * <pre>
   * （可选）城市代码
   * </pre>
   *
   * <code>string city_code = 3;</code>
   * @return The bytes for cityCode.
   */
  com.google.protobuf.ByteString
      getCityCodeBytes();

  /**
   * <pre>
   * （可选）行政区代码
   * </pre>
   *
   * <code>string district_code = 4;</code>
   * @return The districtCode.
   */
  java.lang.String getDistrictCode();
  /**
   * <pre>
   * （可选）行政区代码
   * </pre>
   *
   * <code>string district_code = 4;</code>
   * @return The bytes for districtCode.
   */
  com.google.protobuf.ByteString
      getDistrictCodeBytes();

  /**
   * <pre>
   * （可选）详细地址
   * </pre>
   *
   * <code>string address = 5;</code>
   * @return The address.
   */
  java.lang.String getAddress();
  /**
   * <pre>
   * （可选）详细地址
   * </pre>
   *
   * <code>string address = 5;</code>
   * @return The bytes for address.
   */
  com.google.protobuf.ByteString
      getAddressBytes();
}
