// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 支付配置
 * </pre>
 *
 * Protobuf type {@code channel.PayConfig}
 */
public final class PayConfig extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.PayConfig)
    PayConfigOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PayConfig.newBuilder() to construct.
  private PayConfig(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PayConfig() {
    merchantId_ = "";
    appId_ = "";
    appKey_ = "";
    accessKey_ = "";
    privateKey_ = "";
    publicKey_ = "";
    urlScheme_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PayConfig();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private PayConfig(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            merchantId_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            appId_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            appKey_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            accessKey_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            privateKey_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            publicKey_ = s;
            break;
          }
          case 58: {
            com.google.protobuf.Struct.Builder subBuilder = null;
            if (applePay_ != null) {
              subBuilder = applePay_.toBuilder();
            }
            applePay_ = input.readMessage(com.google.protobuf.Struct.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(applePay_);
              applePay_ = subBuilder.buildPartial();
            }

            break;
          }
          case 66: {
            com.google.protobuf.Struct.Builder subBuilder = null;
            if (googlePay_ != null) {
              subBuilder = googlePay_.toBuilder();
            }
            googlePay_ = input.readMessage(com.google.protobuf.Struct.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(googlePay_);
              googlePay_ = subBuilder.buildPartial();
            }

            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            urlScheme_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_PayConfig_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_PayConfig_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.PayConfig.class, cn.hexcloud.pbis.common.service.facade.channel.PayConfig.Builder.class);
  }

  public static final int MERCHANT_ID_FIELD_NUMBER = 1;
  private volatile java.lang.Object merchantId_;
  /**
   * <pre>
   *商户id
   * </pre>
   *
   * <code>string merchant_id = 1;</code>
   * @return The merchantId.
   */
  @java.lang.Override
  public java.lang.String getMerchantId() {
    java.lang.Object ref = merchantId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      merchantId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *商户id
   * </pre>
   *
   * <code>string merchant_id = 1;</code>
   * @return The bytes for merchantId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMerchantIdBytes() {
    java.lang.Object ref = merchantId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      merchantId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int APP_ID_FIELD_NUMBER = 2;
  private volatile java.lang.Object appId_;
  /**
   * <pre>
   *小程序id
   * </pre>
   *
   * <code>string app_id = 2;</code>
   * @return The appId.
   */
  @java.lang.Override
  public java.lang.String getAppId() {
    java.lang.Object ref = appId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      appId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *小程序id
   * </pre>
   *
   * <code>string app_id = 2;</code>
   * @return The bytes for appId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAppIdBytes() {
    java.lang.Object ref = appId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      appId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int APP_KEY_FIELD_NUMBER = 3;
  private volatile java.lang.Object appKey_;
  /**
   * <pre>
   *应用密钥
   * </pre>
   *
   * <code>string app_key = 3;</code>
   * @return The appKey.
   */
  @java.lang.Override
  public java.lang.String getAppKey() {
    java.lang.Object ref = appKey_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      appKey_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *应用密钥
   * </pre>
   *
   * <code>string app_key = 3;</code>
   * @return The bytes for appKey.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAppKeyBytes() {
    java.lang.Object ref = appKey_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      appKey_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACCESS_KEY_FIELD_NUMBER = 4;
  private volatile java.lang.Object accessKey_;
  /**
   * <pre>
   *授权密钥
   * </pre>
   *
   * <code>string access_key = 4;</code>
   * @return The accessKey.
   */
  @java.lang.Override
  public java.lang.String getAccessKey() {
    java.lang.Object ref = accessKey_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      accessKey_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *授权密钥
   * </pre>
   *
   * <code>string access_key = 4;</code>
   * @return The bytes for accessKey.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAccessKeyBytes() {
    java.lang.Object ref = accessKey_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      accessKey_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PRIVATE_KEY_FIELD_NUMBER = 5;
  private volatile java.lang.Object privateKey_;
  /**
   * <pre>
   *私钥
   * </pre>
   *
   * <code>string private_key = 5;</code>
   * @return The privateKey.
   */
  @java.lang.Override
  public java.lang.String getPrivateKey() {
    java.lang.Object ref = privateKey_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      privateKey_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *私钥
   * </pre>
   *
   * <code>string private_key = 5;</code>
   * @return The bytes for privateKey.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPrivateKeyBytes() {
    java.lang.Object ref = privateKey_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      privateKey_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PUBLIC_KEY_FIELD_NUMBER = 6;
  private volatile java.lang.Object publicKey_;
  /**
   * <pre>
   *公钥
   * </pre>
   *
   * <code>string public_key = 6;</code>
   * @return The publicKey.
   */
  @java.lang.Override
  public java.lang.String getPublicKey() {
    java.lang.Object ref = publicKey_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      publicKey_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *公钥
   * </pre>
   *
   * <code>string public_key = 6;</code>
   * @return The bytes for publicKey.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPublicKeyBytes() {
    java.lang.Object ref = publicKey_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      publicKey_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int APPLE_PAY_FIELD_NUMBER = 7;
  private com.google.protobuf.Struct applePay_;
  /**
   * <pre>
   *ApplePay
   * </pre>
   *
   * <code>.google.protobuf.Struct apple_pay = 7;</code>
   * @return Whether the applePay field is set.
   */
  @java.lang.Override
  public boolean hasApplePay() {
    return applePay_ != null;
  }
  /**
   * <pre>
   *ApplePay
   * </pre>
   *
   * <code>.google.protobuf.Struct apple_pay = 7;</code>
   * @return The applePay.
   */
  @java.lang.Override
  public com.google.protobuf.Struct getApplePay() {
    return applePay_ == null ? com.google.protobuf.Struct.getDefaultInstance() : applePay_;
  }
  /**
   * <pre>
   *ApplePay
   * </pre>
   *
   * <code>.google.protobuf.Struct apple_pay = 7;</code>
   */
  @java.lang.Override
  public com.google.protobuf.StructOrBuilder getApplePayOrBuilder() {
    return getApplePay();
  }

  public static final int GOOGLE_PAY_FIELD_NUMBER = 8;
  private com.google.protobuf.Struct googlePay_;
  /**
   * <pre>
   *GooglePay
   * </pre>
   *
   * <code>.google.protobuf.Struct google_pay = 8;</code>
   * @return Whether the googlePay field is set.
   */
  @java.lang.Override
  public boolean hasGooglePay() {
    return googlePay_ != null;
  }
  /**
   * <pre>
   *GooglePay
   * </pre>
   *
   * <code>.google.protobuf.Struct google_pay = 8;</code>
   * @return The googlePay.
   */
  @java.lang.Override
  public com.google.protobuf.Struct getGooglePay() {
    return googlePay_ == null ? com.google.protobuf.Struct.getDefaultInstance() : googlePay_;
  }
  /**
   * <pre>
   *GooglePay
   * </pre>
   *
   * <code>.google.protobuf.Struct google_pay = 8;</code>
   */
  @java.lang.Override
  public com.google.protobuf.StructOrBuilder getGooglePayOrBuilder() {
    return getGooglePay();
  }

  public static final int URL_SCHEME_FIELD_NUMBER = 9;
  private volatile java.lang.Object urlScheme_;
  /**
   * <pre>
   *返回URL
   * </pre>
   *
   * <code>string url_scheme = 9;</code>
   * @return The urlScheme.
   */
  @java.lang.Override
  public java.lang.String getUrlScheme() {
    java.lang.Object ref = urlScheme_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      urlScheme_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *返回URL
   * </pre>
   *
   * <code>string url_scheme = 9;</code>
   * @return The bytes for urlScheme.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUrlSchemeBytes() {
    java.lang.Object ref = urlScheme_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      urlScheme_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getMerchantIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, merchantId_);
    }
    if (!getAppIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, appId_);
    }
    if (!getAppKeyBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, appKey_);
    }
    if (!getAccessKeyBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, accessKey_);
    }
    if (!getPrivateKeyBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, privateKey_);
    }
    if (!getPublicKeyBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, publicKey_);
    }
    if (applePay_ != null) {
      output.writeMessage(7, getApplePay());
    }
    if (googlePay_ != null) {
      output.writeMessage(8, getGooglePay());
    }
    if (!getUrlSchemeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, urlScheme_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getMerchantIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, merchantId_);
    }
    if (!getAppIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, appId_);
    }
    if (!getAppKeyBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, appKey_);
    }
    if (!getAccessKeyBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, accessKey_);
    }
    if (!getPrivateKeyBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, privateKey_);
    }
    if (!getPublicKeyBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, publicKey_);
    }
    if (applePay_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getApplePay());
    }
    if (googlePay_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, getGooglePay());
    }
    if (!getUrlSchemeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, urlScheme_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.PayConfig)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.PayConfig other = (cn.hexcloud.pbis.common.service.facade.channel.PayConfig) obj;

    if (!getMerchantId()
        .equals(other.getMerchantId())) return false;
    if (!getAppId()
        .equals(other.getAppId())) return false;
    if (!getAppKey()
        .equals(other.getAppKey())) return false;
    if (!getAccessKey()
        .equals(other.getAccessKey())) return false;
    if (!getPrivateKey()
        .equals(other.getPrivateKey())) return false;
    if (!getPublicKey()
        .equals(other.getPublicKey())) return false;
    if (hasApplePay() != other.hasApplePay()) return false;
    if (hasApplePay()) {
      if (!getApplePay()
          .equals(other.getApplePay())) return false;
    }
    if (hasGooglePay() != other.hasGooglePay()) return false;
    if (hasGooglePay()) {
      if (!getGooglePay()
          .equals(other.getGooglePay())) return false;
    }
    if (!getUrlScheme()
        .equals(other.getUrlScheme())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + MERCHANT_ID_FIELD_NUMBER;
    hash = (53 * hash) + getMerchantId().hashCode();
    hash = (37 * hash) + APP_ID_FIELD_NUMBER;
    hash = (53 * hash) + getAppId().hashCode();
    hash = (37 * hash) + APP_KEY_FIELD_NUMBER;
    hash = (53 * hash) + getAppKey().hashCode();
    hash = (37 * hash) + ACCESS_KEY_FIELD_NUMBER;
    hash = (53 * hash) + getAccessKey().hashCode();
    hash = (37 * hash) + PRIVATE_KEY_FIELD_NUMBER;
    hash = (53 * hash) + getPrivateKey().hashCode();
    hash = (37 * hash) + PUBLIC_KEY_FIELD_NUMBER;
    hash = (53 * hash) + getPublicKey().hashCode();
    if (hasApplePay()) {
      hash = (37 * hash) + APPLE_PAY_FIELD_NUMBER;
      hash = (53 * hash) + getApplePay().hashCode();
    }
    if (hasGooglePay()) {
      hash = (37 * hash) + GOOGLE_PAY_FIELD_NUMBER;
      hash = (53 * hash) + getGooglePay().hashCode();
    }
    hash = (37 * hash) + URL_SCHEME_FIELD_NUMBER;
    hash = (53 * hash) + getUrlScheme().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.PayConfig parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.PayConfig parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.PayConfig parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.PayConfig parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.PayConfig parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.PayConfig parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.PayConfig parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.PayConfig parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.PayConfig parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.PayConfig parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.PayConfig parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.PayConfig parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.PayConfig prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 支付配置
   * </pre>
   *
   * Protobuf type {@code channel.PayConfig}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.PayConfig)
      cn.hexcloud.pbis.common.service.facade.channel.PayConfigOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_PayConfig_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_PayConfig_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.PayConfig.class, cn.hexcloud.pbis.common.service.facade.channel.PayConfig.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.PayConfig.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      merchantId_ = "";

      appId_ = "";

      appKey_ = "";

      accessKey_ = "";

      privateKey_ = "";

      publicKey_ = "";

      if (applePayBuilder_ == null) {
        applePay_ = null;
      } else {
        applePay_ = null;
        applePayBuilder_ = null;
      }
      if (googlePayBuilder_ == null) {
        googlePay_ = null;
      } else {
        googlePay_ = null;
        googlePayBuilder_ = null;
      }
      urlScheme_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_PayConfig_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.PayConfig getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.PayConfig.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.PayConfig build() {
      cn.hexcloud.pbis.common.service.facade.channel.PayConfig result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.PayConfig buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.PayConfig result = new cn.hexcloud.pbis.common.service.facade.channel.PayConfig(this);
      result.merchantId_ = merchantId_;
      result.appId_ = appId_;
      result.appKey_ = appKey_;
      result.accessKey_ = accessKey_;
      result.privateKey_ = privateKey_;
      result.publicKey_ = publicKey_;
      if (applePayBuilder_ == null) {
        result.applePay_ = applePay_;
      } else {
        result.applePay_ = applePayBuilder_.build();
      }
      if (googlePayBuilder_ == null) {
        result.googlePay_ = googlePay_;
      } else {
        result.googlePay_ = googlePayBuilder_.build();
      }
      result.urlScheme_ = urlScheme_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.PayConfig) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.PayConfig)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.PayConfig other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.PayConfig.getDefaultInstance()) return this;
      if (!other.getMerchantId().isEmpty()) {
        merchantId_ = other.merchantId_;
        onChanged();
      }
      if (!other.getAppId().isEmpty()) {
        appId_ = other.appId_;
        onChanged();
      }
      if (!other.getAppKey().isEmpty()) {
        appKey_ = other.appKey_;
        onChanged();
      }
      if (!other.getAccessKey().isEmpty()) {
        accessKey_ = other.accessKey_;
        onChanged();
      }
      if (!other.getPrivateKey().isEmpty()) {
        privateKey_ = other.privateKey_;
        onChanged();
      }
      if (!other.getPublicKey().isEmpty()) {
        publicKey_ = other.publicKey_;
        onChanged();
      }
      if (other.hasApplePay()) {
        mergeApplePay(other.getApplePay());
      }
      if (other.hasGooglePay()) {
        mergeGooglePay(other.getGooglePay());
      }
      if (!other.getUrlScheme().isEmpty()) {
        urlScheme_ = other.urlScheme_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.PayConfig parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.PayConfig) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object merchantId_ = "";
    /**
     * <pre>
     *商户id
     * </pre>
     *
     * <code>string merchant_id = 1;</code>
     * @return The merchantId.
     */
    public java.lang.String getMerchantId() {
      java.lang.Object ref = merchantId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        merchantId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *商户id
     * </pre>
     *
     * <code>string merchant_id = 1;</code>
     * @return The bytes for merchantId.
     */
    public com.google.protobuf.ByteString
        getMerchantIdBytes() {
      java.lang.Object ref = merchantId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        merchantId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *商户id
     * </pre>
     *
     * <code>string merchant_id = 1;</code>
     * @param value The merchantId to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      merchantId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商户id
     * </pre>
     *
     * <code>string merchant_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearMerchantId() {
      
      merchantId_ = getDefaultInstance().getMerchantId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商户id
     * </pre>
     *
     * <code>string merchant_id = 1;</code>
     * @param value The bytes for merchantId to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      merchantId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object appId_ = "";
    /**
     * <pre>
     *小程序id
     * </pre>
     *
     * <code>string app_id = 2;</code>
     * @return The appId.
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *小程序id
     * </pre>
     *
     * <code>string app_id = 2;</code>
     * @return The bytes for appId.
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *小程序id
     * </pre>
     *
     * <code>string app_id = 2;</code>
     * @param value The appId to set.
     * @return This builder for chaining.
     */
    public Builder setAppId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      appId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *小程序id
     * </pre>
     *
     * <code>string app_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppId() {
      
      appId_ = getDefaultInstance().getAppId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *小程序id
     * </pre>
     *
     * <code>string app_id = 2;</code>
     * @param value The bytes for appId to set.
     * @return This builder for chaining.
     */
    public Builder setAppIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      appId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object appKey_ = "";
    /**
     * <pre>
     *应用密钥
     * </pre>
     *
     * <code>string app_key = 3;</code>
     * @return The appKey.
     */
    public java.lang.String getAppKey() {
      java.lang.Object ref = appKey_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appKey_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *应用密钥
     * </pre>
     *
     * <code>string app_key = 3;</code>
     * @return The bytes for appKey.
     */
    public com.google.protobuf.ByteString
        getAppKeyBytes() {
      java.lang.Object ref = appKey_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *应用密钥
     * </pre>
     *
     * <code>string app_key = 3;</code>
     * @param value The appKey to set.
     * @return This builder for chaining.
     */
    public Builder setAppKey(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      appKey_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *应用密钥
     * </pre>
     *
     * <code>string app_key = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearAppKey() {
      
      appKey_ = getDefaultInstance().getAppKey();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *应用密钥
     * </pre>
     *
     * <code>string app_key = 3;</code>
     * @param value The bytes for appKey to set.
     * @return This builder for chaining.
     */
    public Builder setAppKeyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      appKey_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object accessKey_ = "";
    /**
     * <pre>
     *授权密钥
     * </pre>
     *
     * <code>string access_key = 4;</code>
     * @return The accessKey.
     */
    public java.lang.String getAccessKey() {
      java.lang.Object ref = accessKey_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        accessKey_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *授权密钥
     * </pre>
     *
     * <code>string access_key = 4;</code>
     * @return The bytes for accessKey.
     */
    public com.google.protobuf.ByteString
        getAccessKeyBytes() {
      java.lang.Object ref = accessKey_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        accessKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *授权密钥
     * </pre>
     *
     * <code>string access_key = 4;</code>
     * @param value The accessKey to set.
     * @return This builder for chaining.
     */
    public Builder setAccessKey(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      accessKey_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *授权密钥
     * </pre>
     *
     * <code>string access_key = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearAccessKey() {
      
      accessKey_ = getDefaultInstance().getAccessKey();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *授权密钥
     * </pre>
     *
     * <code>string access_key = 4;</code>
     * @param value The bytes for accessKey to set.
     * @return This builder for chaining.
     */
    public Builder setAccessKeyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      accessKey_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object privateKey_ = "";
    /**
     * <pre>
     *私钥
     * </pre>
     *
     * <code>string private_key = 5;</code>
     * @return The privateKey.
     */
    public java.lang.String getPrivateKey() {
      java.lang.Object ref = privateKey_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        privateKey_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *私钥
     * </pre>
     *
     * <code>string private_key = 5;</code>
     * @return The bytes for privateKey.
     */
    public com.google.protobuf.ByteString
        getPrivateKeyBytes() {
      java.lang.Object ref = privateKey_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        privateKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *私钥
     * </pre>
     *
     * <code>string private_key = 5;</code>
     * @param value The privateKey to set.
     * @return This builder for chaining.
     */
    public Builder setPrivateKey(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      privateKey_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *私钥
     * </pre>
     *
     * <code>string private_key = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrivateKey() {
      
      privateKey_ = getDefaultInstance().getPrivateKey();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *私钥
     * </pre>
     *
     * <code>string private_key = 5;</code>
     * @param value The bytes for privateKey to set.
     * @return This builder for chaining.
     */
    public Builder setPrivateKeyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      privateKey_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object publicKey_ = "";
    /**
     * <pre>
     *公钥
     * </pre>
     *
     * <code>string public_key = 6;</code>
     * @return The publicKey.
     */
    public java.lang.String getPublicKey() {
      java.lang.Object ref = publicKey_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        publicKey_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *公钥
     * </pre>
     *
     * <code>string public_key = 6;</code>
     * @return The bytes for publicKey.
     */
    public com.google.protobuf.ByteString
        getPublicKeyBytes() {
      java.lang.Object ref = publicKey_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        publicKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *公钥
     * </pre>
     *
     * <code>string public_key = 6;</code>
     * @param value The publicKey to set.
     * @return This builder for chaining.
     */
    public Builder setPublicKey(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      publicKey_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *公钥
     * </pre>
     *
     * <code>string public_key = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearPublicKey() {
      
      publicKey_ = getDefaultInstance().getPublicKey();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *公钥
     * </pre>
     *
     * <code>string public_key = 6;</code>
     * @param value The bytes for publicKey to set.
     * @return This builder for chaining.
     */
    public Builder setPublicKeyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      publicKey_ = value;
      onChanged();
      return this;
    }

    private com.google.protobuf.Struct applePay_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> applePayBuilder_;
    /**
     * <pre>
     *ApplePay
     * </pre>
     *
     * <code>.google.protobuf.Struct apple_pay = 7;</code>
     * @return Whether the applePay field is set.
     */
    public boolean hasApplePay() {
      return applePayBuilder_ != null || applePay_ != null;
    }
    /**
     * <pre>
     *ApplePay
     * </pre>
     *
     * <code>.google.protobuf.Struct apple_pay = 7;</code>
     * @return The applePay.
     */
    public com.google.protobuf.Struct getApplePay() {
      if (applePayBuilder_ == null) {
        return applePay_ == null ? com.google.protobuf.Struct.getDefaultInstance() : applePay_;
      } else {
        return applePayBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *ApplePay
     * </pre>
     *
     * <code>.google.protobuf.Struct apple_pay = 7;</code>
     */
    public Builder setApplePay(com.google.protobuf.Struct value) {
      if (applePayBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        applePay_ = value;
        onChanged();
      } else {
        applePayBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *ApplePay
     * </pre>
     *
     * <code>.google.protobuf.Struct apple_pay = 7;</code>
     */
    public Builder setApplePay(
        com.google.protobuf.Struct.Builder builderForValue) {
      if (applePayBuilder_ == null) {
        applePay_ = builderForValue.build();
        onChanged();
      } else {
        applePayBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *ApplePay
     * </pre>
     *
     * <code>.google.protobuf.Struct apple_pay = 7;</code>
     */
    public Builder mergeApplePay(com.google.protobuf.Struct value) {
      if (applePayBuilder_ == null) {
        if (applePay_ != null) {
          applePay_ =
            com.google.protobuf.Struct.newBuilder(applePay_).mergeFrom(value).buildPartial();
        } else {
          applePay_ = value;
        }
        onChanged();
      } else {
        applePayBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *ApplePay
     * </pre>
     *
     * <code>.google.protobuf.Struct apple_pay = 7;</code>
     */
    public Builder clearApplePay() {
      if (applePayBuilder_ == null) {
        applePay_ = null;
        onChanged();
      } else {
        applePay_ = null;
        applePayBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *ApplePay
     * </pre>
     *
     * <code>.google.protobuf.Struct apple_pay = 7;</code>
     */
    public com.google.protobuf.Struct.Builder getApplePayBuilder() {
      
      onChanged();
      return getApplePayFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *ApplePay
     * </pre>
     *
     * <code>.google.protobuf.Struct apple_pay = 7;</code>
     */
    public com.google.protobuf.StructOrBuilder getApplePayOrBuilder() {
      if (applePayBuilder_ != null) {
        return applePayBuilder_.getMessageOrBuilder();
      } else {
        return applePay_ == null ?
            com.google.protobuf.Struct.getDefaultInstance() : applePay_;
      }
    }
    /**
     * <pre>
     *ApplePay
     * </pre>
     *
     * <code>.google.protobuf.Struct apple_pay = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> 
        getApplePayFieldBuilder() {
      if (applePayBuilder_ == null) {
        applePayBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder>(
                getApplePay(),
                getParentForChildren(),
                isClean());
        applePay_ = null;
      }
      return applePayBuilder_;
    }

    private com.google.protobuf.Struct googlePay_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> googlePayBuilder_;
    /**
     * <pre>
     *GooglePay
     * </pre>
     *
     * <code>.google.protobuf.Struct google_pay = 8;</code>
     * @return Whether the googlePay field is set.
     */
    public boolean hasGooglePay() {
      return googlePayBuilder_ != null || googlePay_ != null;
    }
    /**
     * <pre>
     *GooglePay
     * </pre>
     *
     * <code>.google.protobuf.Struct google_pay = 8;</code>
     * @return The googlePay.
     */
    public com.google.protobuf.Struct getGooglePay() {
      if (googlePayBuilder_ == null) {
        return googlePay_ == null ? com.google.protobuf.Struct.getDefaultInstance() : googlePay_;
      } else {
        return googlePayBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *GooglePay
     * </pre>
     *
     * <code>.google.protobuf.Struct google_pay = 8;</code>
     */
    public Builder setGooglePay(com.google.protobuf.Struct value) {
      if (googlePayBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        googlePay_ = value;
        onChanged();
      } else {
        googlePayBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *GooglePay
     * </pre>
     *
     * <code>.google.protobuf.Struct google_pay = 8;</code>
     */
    public Builder setGooglePay(
        com.google.protobuf.Struct.Builder builderForValue) {
      if (googlePayBuilder_ == null) {
        googlePay_ = builderForValue.build();
        onChanged();
      } else {
        googlePayBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *GooglePay
     * </pre>
     *
     * <code>.google.protobuf.Struct google_pay = 8;</code>
     */
    public Builder mergeGooglePay(com.google.protobuf.Struct value) {
      if (googlePayBuilder_ == null) {
        if (googlePay_ != null) {
          googlePay_ =
            com.google.protobuf.Struct.newBuilder(googlePay_).mergeFrom(value).buildPartial();
        } else {
          googlePay_ = value;
        }
        onChanged();
      } else {
        googlePayBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *GooglePay
     * </pre>
     *
     * <code>.google.protobuf.Struct google_pay = 8;</code>
     */
    public Builder clearGooglePay() {
      if (googlePayBuilder_ == null) {
        googlePay_ = null;
        onChanged();
      } else {
        googlePay_ = null;
        googlePayBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *GooglePay
     * </pre>
     *
     * <code>.google.protobuf.Struct google_pay = 8;</code>
     */
    public com.google.protobuf.Struct.Builder getGooglePayBuilder() {
      
      onChanged();
      return getGooglePayFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *GooglePay
     * </pre>
     *
     * <code>.google.protobuf.Struct google_pay = 8;</code>
     */
    public com.google.protobuf.StructOrBuilder getGooglePayOrBuilder() {
      if (googlePayBuilder_ != null) {
        return googlePayBuilder_.getMessageOrBuilder();
      } else {
        return googlePay_ == null ?
            com.google.protobuf.Struct.getDefaultInstance() : googlePay_;
      }
    }
    /**
     * <pre>
     *GooglePay
     * </pre>
     *
     * <code>.google.protobuf.Struct google_pay = 8;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> 
        getGooglePayFieldBuilder() {
      if (googlePayBuilder_ == null) {
        googlePayBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder>(
                getGooglePay(),
                getParentForChildren(),
                isClean());
        googlePay_ = null;
      }
      return googlePayBuilder_;
    }

    private java.lang.Object urlScheme_ = "";
    /**
     * <pre>
     *返回URL
     * </pre>
     *
     * <code>string url_scheme = 9;</code>
     * @return The urlScheme.
     */
    public java.lang.String getUrlScheme() {
      java.lang.Object ref = urlScheme_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        urlScheme_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *返回URL
     * </pre>
     *
     * <code>string url_scheme = 9;</code>
     * @return The bytes for urlScheme.
     */
    public com.google.protobuf.ByteString
        getUrlSchemeBytes() {
      java.lang.Object ref = urlScheme_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        urlScheme_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *返回URL
     * </pre>
     *
     * <code>string url_scheme = 9;</code>
     * @param value The urlScheme to set.
     * @return This builder for chaining.
     */
    public Builder setUrlScheme(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      urlScheme_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *返回URL
     * </pre>
     *
     * <code>string url_scheme = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearUrlScheme() {
      
      urlScheme_ = getDefaultInstance().getUrlScheme();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *返回URL
     * </pre>
     *
     * <code>string url_scheme = 9;</code>
     * @param value The bytes for urlScheme to set.
     * @return This builder for chaining.
     */
    public Builder setUrlSchemeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      urlScheme_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.PayConfig)
  }

  // @@protoc_insertion_point(class_scope:channel.PayConfig)
  private static final cn.hexcloud.pbis.common.service.facade.channel.PayConfig DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.PayConfig();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.PayConfig getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PayConfig>
      PARSER = new com.google.protobuf.AbstractParser<PayConfig>() {
    @java.lang.Override
    public PayConfig parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new PayConfig(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<PayConfig> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PayConfig> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.PayConfig getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

