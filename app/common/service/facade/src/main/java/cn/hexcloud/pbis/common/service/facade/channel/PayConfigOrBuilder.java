// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface PayConfigOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.PayConfig)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *商户id
   * </pre>
   *
   * <code>string merchant_id = 1;</code>
   * @return The merchantId.
   */
  java.lang.String getMerchantId();
  /**
   * <pre>
   *商户id
   * </pre>
   *
   * <code>string merchant_id = 1;</code>
   * @return The bytes for merchantId.
   */
  com.google.protobuf.ByteString
      getMerchantIdBytes();

  /**
   * <pre>
   *小程序id
   * </pre>
   *
   * <code>string app_id = 2;</code>
   * @return The appId.
   */
  java.lang.String getAppId();
  /**
   * <pre>
   *小程序id
   * </pre>
   *
   * <code>string app_id = 2;</code>
   * @return The bytes for appId.
   */
  com.google.protobuf.ByteString
      getAppIdBytes();

  /**
   * <pre>
   *应用密钥
   * </pre>
   *
   * <code>string app_key = 3;</code>
   * @return The appKey.
   */
  java.lang.String getAppKey();
  /**
   * <pre>
   *应用密钥
   * </pre>
   *
   * <code>string app_key = 3;</code>
   * @return The bytes for appKey.
   */
  com.google.protobuf.ByteString
      getAppKeyBytes();

  /**
   * <pre>
   *授权密钥
   * </pre>
   *
   * <code>string access_key = 4;</code>
   * @return The accessKey.
   */
  java.lang.String getAccessKey();
  /**
   * <pre>
   *授权密钥
   * </pre>
   *
   * <code>string access_key = 4;</code>
   * @return The bytes for accessKey.
   */
  com.google.protobuf.ByteString
      getAccessKeyBytes();

  /**
   * <pre>
   *私钥
   * </pre>
   *
   * <code>string private_key = 5;</code>
   * @return The privateKey.
   */
  java.lang.String getPrivateKey();
  /**
   * <pre>
   *私钥
   * </pre>
   *
   * <code>string private_key = 5;</code>
   * @return The bytes for privateKey.
   */
  com.google.protobuf.ByteString
      getPrivateKeyBytes();

  /**
   * <pre>
   *公钥
   * </pre>
   *
   * <code>string public_key = 6;</code>
   * @return The publicKey.
   */
  java.lang.String getPublicKey();
  /**
   * <pre>
   *公钥
   * </pre>
   *
   * <code>string public_key = 6;</code>
   * @return The bytes for publicKey.
   */
  com.google.protobuf.ByteString
      getPublicKeyBytes();

  /**
   * <pre>
   *ApplePay
   * </pre>
   *
   * <code>.google.protobuf.Struct apple_pay = 7;</code>
   * @return Whether the applePay field is set.
   */
  boolean hasApplePay();
  /**
   * <pre>
   *ApplePay
   * </pre>
   *
   * <code>.google.protobuf.Struct apple_pay = 7;</code>
   * @return The applePay.
   */
  com.google.protobuf.Struct getApplePay();
  /**
   * <pre>
   *ApplePay
   * </pre>
   *
   * <code>.google.protobuf.Struct apple_pay = 7;</code>
   */
  com.google.protobuf.StructOrBuilder getApplePayOrBuilder();

  /**
   * <pre>
   *GooglePay
   * </pre>
   *
   * <code>.google.protobuf.Struct google_pay = 8;</code>
   * @return Whether the googlePay field is set.
   */
  boolean hasGooglePay();
  /**
   * <pre>
   *GooglePay
   * </pre>
   *
   * <code>.google.protobuf.Struct google_pay = 8;</code>
   * @return The googlePay.
   */
  com.google.protobuf.Struct getGooglePay();
  /**
   * <pre>
   *GooglePay
   * </pre>
   *
   * <code>.google.protobuf.Struct google_pay = 8;</code>
   */
  com.google.protobuf.StructOrBuilder getGooglePayOrBuilder();

  /**
   * <pre>
   *返回URL
   * </pre>
   *
   * <code>string url_scheme = 9;</code>
   * @return The urlScheme.
   */
  java.lang.String getUrlScheme();
  /**
   * <pre>
   *返回URL
   * </pre>
   *
   * <code>string url_scheme = 9;</code>
   * @return The bytes for urlScheme.
   */
  com.google.protobuf.ByteString
      getUrlSchemeBytes();
}
