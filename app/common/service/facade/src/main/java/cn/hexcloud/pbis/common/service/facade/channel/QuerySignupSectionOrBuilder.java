// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface QuerySignupSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.QuerySignupSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）渠道签约授权id
   * </pre>
   *
   * <code>int32 channel_auth_id = 1;</code>
   * @return The channelAuthId.
   */
  int getChannelAuthId();
}
