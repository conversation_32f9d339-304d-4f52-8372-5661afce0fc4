// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道签约状态刷新结果
 * </pre>
 *
 * Protobuf type {@code channel.RefreshSignupResultSection}
 */
public final class RefreshSignupResultSection extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.RefreshSignupResultSection)
    RefreshSignupResultSectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RefreshSignupResultSection.newBuilder() to construct.
  private RefreshSignupResultSection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RefreshSignupResultSection() {
    signupStateItem_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new RefreshSignupResultSection();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private RefreshSignupResultSection(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              signupStateItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem>();
              mutable_bitField0_ |= 0x00000001;
            }
            signupStateItem_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        signupStateItem_ = java.util.Collections.unmodifiableList(signupStateItem_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_RefreshSignupResultSection_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_RefreshSignupResultSection_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.class, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.Builder.class);
  }

  public static final int SIGNUP_STATE_ITEM_FIELD_NUMBER = 1;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem> signupStateItem_;
  /**
   * <pre>
   * （必传）渠道签约状态信息
   * </pre>
   *
   * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem> getSignupStateItemList() {
    return signupStateItem_;
  }
  /**
   * <pre>
   * （必传）渠道签约状态信息
   * </pre>
   *
   * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.SignupStateItemOrBuilder> 
      getSignupStateItemOrBuilderList() {
    return signupStateItem_;
  }
  /**
   * <pre>
   * （必传）渠道签约状态信息
   * </pre>
   *
   * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
   */
  @java.lang.Override
  public int getSignupStateItemCount() {
    return signupStateItem_.size();
  }
  /**
   * <pre>
   * （必传）渠道签约状态信息
   * </pre>
   *
   * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem getSignupStateItem(int index) {
    return signupStateItem_.get(index);
  }
  /**
   * <pre>
   * （必传）渠道签约状态信息
   * </pre>
   *
   * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SignupStateItemOrBuilder getSignupStateItemOrBuilder(
      int index) {
    return signupStateItem_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < signupStateItem_.size(); i++) {
      output.writeMessage(1, signupStateItem_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < signupStateItem_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, signupStateItem_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection other = (cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection) obj;

    if (!getSignupStateItemList()
        .equals(other.getSignupStateItemList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getSignupStateItemCount() > 0) {
      hash = (37 * hash) + SIGNUP_STATE_ITEM_FIELD_NUMBER;
      hash = (53 * hash) + getSignupStateItemList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道签约状态刷新结果
   * </pre>
   *
   * Protobuf type {@code channel.RefreshSignupResultSection}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.RefreshSignupResultSection)
      cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_RefreshSignupResultSection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_RefreshSignupResultSection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.class, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getSignupStateItemFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (signupStateItemBuilder_ == null) {
        signupStateItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        signupStateItemBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_RefreshSignupResultSection_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection build() {
      cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection result = new cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection(this);
      int from_bitField0_ = bitField0_;
      if (signupStateItemBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          signupStateItem_ = java.util.Collections.unmodifiableList(signupStateItem_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.signupStateItem_ = signupStateItem_;
      } else {
        result.signupStateItem_ = signupStateItemBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection.getDefaultInstance()) return this;
      if (signupStateItemBuilder_ == null) {
        if (!other.signupStateItem_.isEmpty()) {
          if (signupStateItem_.isEmpty()) {
            signupStateItem_ = other.signupStateItem_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureSignupStateItemIsMutable();
            signupStateItem_.addAll(other.signupStateItem_);
          }
          onChanged();
        }
      } else {
        if (!other.signupStateItem_.isEmpty()) {
          if (signupStateItemBuilder_.isEmpty()) {
            signupStateItemBuilder_.dispose();
            signupStateItemBuilder_ = null;
            signupStateItem_ = other.signupStateItem_;
            bitField0_ = (bitField0_ & ~0x00000001);
            signupStateItemBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getSignupStateItemFieldBuilder() : null;
          } else {
            signupStateItemBuilder_.addAllMessages(other.signupStateItem_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem> signupStateItem_ =
      java.util.Collections.emptyList();
    private void ensureSignupStateItemIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        signupStateItem_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem>(signupStateItem_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem, cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.SignupStateItemOrBuilder> signupStateItemBuilder_;

    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem> getSignupStateItemList() {
      if (signupStateItemBuilder_ == null) {
        return java.util.Collections.unmodifiableList(signupStateItem_);
      } else {
        return signupStateItemBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public int getSignupStateItemCount() {
      if (signupStateItemBuilder_ == null) {
        return signupStateItem_.size();
      } else {
        return signupStateItemBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem getSignupStateItem(int index) {
      if (signupStateItemBuilder_ == null) {
        return signupStateItem_.get(index);
      } else {
        return signupStateItemBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public Builder setSignupStateItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem value) {
      if (signupStateItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSignupStateItemIsMutable();
        signupStateItem_.set(index, value);
        onChanged();
      } else {
        signupStateItemBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public Builder setSignupStateItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.Builder builderForValue) {
      if (signupStateItemBuilder_ == null) {
        ensureSignupStateItemIsMutable();
        signupStateItem_.set(index, builderForValue.build());
        onChanged();
      } else {
        signupStateItemBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public Builder addSignupStateItem(cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem value) {
      if (signupStateItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSignupStateItemIsMutable();
        signupStateItem_.add(value);
        onChanged();
      } else {
        signupStateItemBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public Builder addSignupStateItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem value) {
      if (signupStateItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSignupStateItemIsMutable();
        signupStateItem_.add(index, value);
        onChanged();
      } else {
        signupStateItemBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public Builder addSignupStateItem(
        cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.Builder builderForValue) {
      if (signupStateItemBuilder_ == null) {
        ensureSignupStateItemIsMutable();
        signupStateItem_.add(builderForValue.build());
        onChanged();
      } else {
        signupStateItemBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public Builder addSignupStateItem(
        int index, cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.Builder builderForValue) {
      if (signupStateItemBuilder_ == null) {
        ensureSignupStateItemIsMutable();
        signupStateItem_.add(index, builderForValue.build());
        onChanged();
      } else {
        signupStateItemBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public Builder addAllSignupStateItem(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem> values) {
      if (signupStateItemBuilder_ == null) {
        ensureSignupStateItemIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, signupStateItem_);
        onChanged();
      } else {
        signupStateItemBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public Builder clearSignupStateItem() {
      if (signupStateItemBuilder_ == null) {
        signupStateItem_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        signupStateItemBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public Builder removeSignupStateItem(int index) {
      if (signupStateItemBuilder_ == null) {
        ensureSignupStateItemIsMutable();
        signupStateItem_.remove(index);
        onChanged();
      } else {
        signupStateItemBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.Builder getSignupStateItemBuilder(
        int index) {
      return getSignupStateItemFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SignupStateItemOrBuilder getSignupStateItemOrBuilder(
        int index) {
      if (signupStateItemBuilder_ == null) {
        return signupStateItem_.get(index);  } else {
        return signupStateItemBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.SignupStateItemOrBuilder> 
         getSignupStateItemOrBuilderList() {
      if (signupStateItemBuilder_ != null) {
        return signupStateItemBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(signupStateItem_);
      }
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.Builder addSignupStateItemBuilder() {
      return getSignupStateItemFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.getDefaultInstance());
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.Builder addSignupStateItemBuilder(
        int index) {
      return getSignupStateItemFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.getDefaultInstance());
    }
    /**
     * <pre>
     * （必传）渠道签约状态信息
     * </pre>
     *
     * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.Builder> 
         getSignupStateItemBuilderList() {
      return getSignupStateItemFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem, cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.SignupStateItemOrBuilder> 
        getSignupStateItemFieldBuilder() {
      if (signupStateItemBuilder_ == null) {
        signupStateItemBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem, cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.Builder, cn.hexcloud.pbis.common.service.facade.channel.SignupStateItemOrBuilder>(
                signupStateItem_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        signupStateItem_ = null;
      }
      return signupStateItemBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.RefreshSignupResultSection)
  }

  // @@protoc_insertion_point(class_scope:channel.RefreshSignupResultSection)
  private static final cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RefreshSignupResultSection>
      PARSER = new com.google.protobuf.AbstractParser<RefreshSignupResultSection>() {
    @java.lang.Override
    public RefreshSignupResultSection parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new RefreshSignupResultSection(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<RefreshSignupResultSection> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RefreshSignupResultSection> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupResultSection getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

