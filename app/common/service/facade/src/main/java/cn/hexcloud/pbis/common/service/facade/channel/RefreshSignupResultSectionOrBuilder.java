// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface RefreshSignupResultSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.RefreshSignupResultSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）渠道签约状态信息
   * </pre>
   *
   * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem> 
      getSignupStateItemList();
  /**
   * <pre>
   * （必传）渠道签约状态信息
   * </pre>
   *
   * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem getSignupStateItem(int index);
  /**
   * <pre>
   * （必传）渠道签约状态信息
   * </pre>
   *
   * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
   */
  int getSignupStateItemCount();
  /**
   * <pre>
   * （必传）渠道签约状态信息
   * </pre>
   *
   * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.SignupStateItemOrBuilder> 
      getSignupStateItemOrBuilderList();
  /**
   * <pre>
   * （必传）渠道签约状态信息
   * </pre>
   *
   * <code>repeated .channel.SignupStateItem signup_state_item = 1;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.SignupStateItemOrBuilder getSignupStateItemOrBuilder(
      int index);
}
