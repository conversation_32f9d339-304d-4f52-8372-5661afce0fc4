// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道签约状态刷新请求对象
 * </pre>
 *
 * Protobuf type {@code channel.RefreshSignupSection}
 */
public final class RefreshSignupSection extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.RefreshSignupSection)
    RefreshSignupSectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RefreshSignupSection.newBuilder() to construct.
  private RefreshSignupSection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RefreshSignupSection() {
    channelAuthId_ = emptyIntList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new RefreshSignupSection();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private RefreshSignupSection(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              channelAuthId_ = newIntList();
              mutable_bitField0_ |= 0x00000001;
            }
            channelAuthId_.addInt(input.readInt32());
            break;
          }
          case 10: {
            int length = input.readRawVarint32();
            int limit = input.pushLimit(length);
            if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
              channelAuthId_ = newIntList();
              mutable_bitField0_ |= 0x00000001;
            }
            while (input.getBytesUntilLimit() > 0) {
              channelAuthId_.addInt(input.readInt32());
            }
            input.popLimit(limit);
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        channelAuthId_.makeImmutable(); // C
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_RefreshSignupSection_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_RefreshSignupSection_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.class, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.Builder.class);
  }

  public static final int CHANNEL_AUTH_ID_FIELD_NUMBER = 1;
  private com.google.protobuf.Internal.IntList channelAuthId_;
  /**
   * <pre>
   * （必传）渠道签约授权id
   * </pre>
   *
   * <code>repeated int32 channel_auth_id = 1;</code>
   * @return A list containing the channelAuthId.
   */
  @java.lang.Override
  public java.util.List<java.lang.Integer>
      getChannelAuthIdList() {
    return channelAuthId_;
  }
  /**
   * <pre>
   * （必传）渠道签约授权id
   * </pre>
   *
   * <code>repeated int32 channel_auth_id = 1;</code>
   * @return The count of channelAuthId.
   */
  public int getChannelAuthIdCount() {
    return channelAuthId_.size();
  }
  /**
   * <pre>
   * （必传）渠道签约授权id
   * </pre>
   *
   * <code>repeated int32 channel_auth_id = 1;</code>
   * @param index The index of the element to return.
   * @return The channelAuthId at the given index.
   */
  public int getChannelAuthId(int index) {
    return channelAuthId_.getInt(index);
  }
  private int channelAuthIdMemoizedSerializedSize = -1;

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    getSerializedSize();
    if (getChannelAuthIdList().size() > 0) {
      output.writeUInt32NoTag(10);
      output.writeUInt32NoTag(channelAuthIdMemoizedSerializedSize);
    }
    for (int i = 0; i < channelAuthId_.size(); i++) {
      output.writeInt32NoTag(channelAuthId_.getInt(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    {
      int dataSize = 0;
      for (int i = 0; i < channelAuthId_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt32SizeNoTag(channelAuthId_.getInt(i));
      }
      size += dataSize;
      if (!getChannelAuthIdList().isEmpty()) {
        size += 1;
        size += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(dataSize);
      }
      channelAuthIdMemoizedSerializedSize = dataSize;
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection other = (cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection) obj;

    if (!getChannelAuthIdList()
        .equals(other.getChannelAuthIdList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getChannelAuthIdCount() > 0) {
      hash = (37 * hash) + CHANNEL_AUTH_ID_FIELD_NUMBER;
      hash = (53 * hash) + getChannelAuthIdList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道签约状态刷新请求对象
   * </pre>
   *
   * Protobuf type {@code channel.RefreshSignupSection}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.RefreshSignupSection)
      cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_RefreshSignupSection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_RefreshSignupSection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.class, cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channelAuthId_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000001);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_RefreshSignupSection_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection build() {
      cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection result = new cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection(this);
      int from_bitField0_ = bitField0_;
      if (((bitField0_ & 0x00000001) != 0)) {
        channelAuthId_.makeImmutable();
        bitField0_ = (bitField0_ & ~0x00000001);
      }
      result.channelAuthId_ = channelAuthId_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection.getDefaultInstance()) return this;
      if (!other.channelAuthId_.isEmpty()) {
        if (channelAuthId_.isEmpty()) {
          channelAuthId_ = other.channelAuthId_;
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          ensureChannelAuthIdIsMutable();
          channelAuthId_.addAll(other.channelAuthId_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private com.google.protobuf.Internal.IntList channelAuthId_ = emptyIntList();
    private void ensureChannelAuthIdIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        channelAuthId_ = mutableCopy(channelAuthId_);
        bitField0_ |= 0x00000001;
       }
    }
    /**
     * <pre>
     * （必传）渠道签约授权id
     * </pre>
     *
     * <code>repeated int32 channel_auth_id = 1;</code>
     * @return A list containing the channelAuthId.
     */
    public java.util.List<java.lang.Integer>
        getChannelAuthIdList() {
      return ((bitField0_ & 0x00000001) != 0) ?
               java.util.Collections.unmodifiableList(channelAuthId_) : channelAuthId_;
    }
    /**
     * <pre>
     * （必传）渠道签约授权id
     * </pre>
     *
     * <code>repeated int32 channel_auth_id = 1;</code>
     * @return The count of channelAuthId.
     */
    public int getChannelAuthIdCount() {
      return channelAuthId_.size();
    }
    /**
     * <pre>
     * （必传）渠道签约授权id
     * </pre>
     *
     * <code>repeated int32 channel_auth_id = 1;</code>
     * @param index The index of the element to return.
     * @return The channelAuthId at the given index.
     */
    public int getChannelAuthId(int index) {
      return channelAuthId_.getInt(index);
    }
    /**
     * <pre>
     * （必传）渠道签约授权id
     * </pre>
     *
     * <code>repeated int32 channel_auth_id = 1;</code>
     * @param index The index to set the value at.
     * @param value The channelAuthId to set.
     * @return This builder for chaining.
     */
    public Builder setChannelAuthId(
        int index, int value) {
      ensureChannelAuthIdIsMutable();
      channelAuthId_.setInt(index, value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约授权id
     * </pre>
     *
     * <code>repeated int32 channel_auth_id = 1;</code>
     * @param value The channelAuthId to add.
     * @return This builder for chaining.
     */
    public Builder addChannelAuthId(int value) {
      ensureChannelAuthIdIsMutable();
      channelAuthId_.addInt(value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约授权id
     * </pre>
     *
     * <code>repeated int32 channel_auth_id = 1;</code>
     * @param values The channelAuthId to add.
     * @return This builder for chaining.
     */
    public Builder addAllChannelAuthId(
        java.lang.Iterable<? extends java.lang.Integer> values) {
      ensureChannelAuthIdIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, channelAuthId_);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约授权id
     * </pre>
     *
     * <code>repeated int32 channel_auth_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelAuthId() {
      channelAuthId_ = emptyIntList();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.RefreshSignupSection)
  }

  // @@protoc_insertion_point(class_scope:channel.RefreshSignupSection)
  private static final cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RefreshSignupSection>
      PARSER = new com.google.protobuf.AbstractParser<RefreshSignupSection>() {
    @java.lang.Override
    public RefreshSignupSection parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new RefreshSignupSection(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<RefreshSignupSection> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RefreshSignupSection> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.RefreshSignupSection getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

