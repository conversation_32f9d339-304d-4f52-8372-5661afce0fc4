// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface RefreshSignupSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.RefreshSignupSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）渠道签约授权id
   * </pre>
   *
   * <code>repeated int32 channel_auth_id = 1;</code>
   * @return A list containing the channelAuthId.
   */
  java.util.List<java.lang.Integer> getChannelAuthIdList();
  /**
   * <pre>
   * （必传）渠道签约授权id
   * </pre>
   *
   * <code>repeated int32 channel_auth_id = 1;</code>
   * @return The count of channelAuthId.
   */
  int getChannelAuthIdCount();
  /**
   * <pre>
   * （必传）渠道签约授权id
   * </pre>
   *
   * <code>repeated int32 channel_auth_id = 1;</code>
   * @param index The index of the element to return.
   * @return The channelAuthId at the given index.
   */
  int getChannelAuthId(int index);
}
