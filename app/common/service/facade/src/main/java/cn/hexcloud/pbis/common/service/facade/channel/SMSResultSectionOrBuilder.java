// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface SMSResultSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.SMSResultSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）短信id
   * </pre>
   *
   * <code>string sms_id = 3;</code>
   * @return The smsId.
   */
  java.lang.String getSmsId();
  /**
   * <pre>
   * （必传）短信id
   * </pre>
   *
   * <code>string sms_id = 3;</code>
   * @return The bytes for smsId.
   */
  com.google.protobuf.ByteString
      getSmsIdBytes();
}
