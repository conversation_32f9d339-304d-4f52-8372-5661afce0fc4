// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 短信验证码请求信息
 * </pre>
 *
 * Protobuf type {@code channel.SendSMSCodeSection}
 */
public final class SendSMSCodeSection extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.SendSMSCodeSection)
    SendSMSCodeSectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use SendSMSCodeSection.newBuilder() to construct.
  private SendSMSCodeSection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private SendSMSCodeSection() {
    smsScene_ = "";
    mobileNumber_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new SendSMSCodeSection();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private SendSMSCodeSection(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            smsScene_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            mobileNumber_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_SendSMSCodeSection_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_SendSMSCodeSection_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.class, cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.Builder.class);
  }

  public static final int SMS_SCENE_FIELD_NUMBER = 1;
  private volatile java.lang.Object smsScene_;
  /**
   * <pre>
   **
   *（必传）短信验证码场景
   * 支付宝当面付ISV代签短信验证码：alipayISV_signup
   * </pre>
   *
   * <code>string sms_scene = 1;</code>
   * @return The smsScene.
   */
  @java.lang.Override
  public java.lang.String getSmsScene() {
    java.lang.Object ref = smsScene_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      smsScene_ = s;
      return s;
    }
  }
  /**
   * <pre>
   **
   *（必传）短信验证码场景
   * 支付宝当面付ISV代签短信验证码：alipayISV_signup
   * </pre>
   *
   * <code>string sms_scene = 1;</code>
   * @return The bytes for smsScene.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSmsSceneBytes() {
    java.lang.Object ref = smsScene_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      smsScene_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MOBILE_NUMBER_FIELD_NUMBER = 2;
  private volatile java.lang.Object mobileNumber_;
  /**
   * <pre>
   * （必传）手机号
   * </pre>
   *
   * <code>string mobile_number = 2;</code>
   * @return The mobileNumber.
   */
  @java.lang.Override
  public java.lang.String getMobileNumber() {
    java.lang.Object ref = mobileNumber_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      mobileNumber_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）手机号
   * </pre>
   *
   * <code>string mobile_number = 2;</code>
   * @return The bytes for mobileNumber.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMobileNumberBytes() {
    java.lang.Object ref = mobileNumber_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      mobileNumber_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getSmsSceneBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, smsScene_);
    }
    if (!getMobileNumberBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, mobileNumber_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getSmsSceneBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, smsScene_);
    }
    if (!getMobileNumberBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, mobileNumber_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection other = (cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection) obj;

    if (!getSmsScene()
        .equals(other.getSmsScene())) return false;
    if (!getMobileNumber()
        .equals(other.getMobileNumber())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SMS_SCENE_FIELD_NUMBER;
    hash = (53 * hash) + getSmsScene().hashCode();
    hash = (37 * hash) + MOBILE_NUMBER_FIELD_NUMBER;
    hash = (53 * hash) + getMobileNumber().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 短信验证码请求信息
   * </pre>
   *
   * Protobuf type {@code channel.SendSMSCodeSection}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.SendSMSCodeSection)
      cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_SendSMSCodeSection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_SendSMSCodeSection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.class, cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      smsScene_ = "";

      mobileNumber_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_SendSMSCodeSection_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection build() {
      cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection result = new cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection(this);
      result.smsScene_ = smsScene_;
      result.mobileNumber_ = mobileNumber_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection.getDefaultInstance()) return this;
      if (!other.getSmsScene().isEmpty()) {
        smsScene_ = other.smsScene_;
        onChanged();
      }
      if (!other.getMobileNumber().isEmpty()) {
        mobileNumber_ = other.mobileNumber_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object smsScene_ = "";
    /**
     * <pre>
     **
     *（必传）短信验证码场景
     * 支付宝当面付ISV代签短信验证码：alipayISV_signup
     * </pre>
     *
     * <code>string sms_scene = 1;</code>
     * @return The smsScene.
     */
    public java.lang.String getSmsScene() {
      java.lang.Object ref = smsScene_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        smsScene_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     **
     *（必传）短信验证码场景
     * 支付宝当面付ISV代签短信验证码：alipayISV_signup
     * </pre>
     *
     * <code>string sms_scene = 1;</code>
     * @return The bytes for smsScene.
     */
    public com.google.protobuf.ByteString
        getSmsSceneBytes() {
      java.lang.Object ref = smsScene_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        smsScene_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     **
     *（必传）短信验证码场景
     * 支付宝当面付ISV代签短信验证码：alipayISV_signup
     * </pre>
     *
     * <code>string sms_scene = 1;</code>
     * @param value The smsScene to set.
     * @return This builder for chaining.
     */
    public Builder setSmsScene(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      smsScene_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     **
     *（必传）短信验证码场景
     * 支付宝当面付ISV代签短信验证码：alipayISV_signup
     * </pre>
     *
     * <code>string sms_scene = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSmsScene() {
      
      smsScene_ = getDefaultInstance().getSmsScene();
      onChanged();
      return this;
    }
    /**
     * <pre>
     **
     *（必传）短信验证码场景
     * 支付宝当面付ISV代签短信验证码：alipayISV_signup
     * </pre>
     *
     * <code>string sms_scene = 1;</code>
     * @param value The bytes for smsScene to set.
     * @return This builder for chaining.
     */
    public Builder setSmsSceneBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      smsScene_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object mobileNumber_ = "";
    /**
     * <pre>
     * （必传）手机号
     * </pre>
     *
     * <code>string mobile_number = 2;</code>
     * @return The mobileNumber.
     */
    public java.lang.String getMobileNumber() {
      java.lang.Object ref = mobileNumber_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        mobileNumber_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）手机号
     * </pre>
     *
     * <code>string mobile_number = 2;</code>
     * @return The bytes for mobileNumber.
     */
    public com.google.protobuf.ByteString
        getMobileNumberBytes() {
      java.lang.Object ref = mobileNumber_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        mobileNumber_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）手机号
     * </pre>
     *
     * <code>string mobile_number = 2;</code>
     * @param value The mobileNumber to set.
     * @return This builder for chaining.
     */
    public Builder setMobileNumber(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      mobileNumber_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）手机号
     * </pre>
     *
     * <code>string mobile_number = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMobileNumber() {
      
      mobileNumber_ = getDefaultInstance().getMobileNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）手机号
     * </pre>
     *
     * <code>string mobile_number = 2;</code>
     * @param value The bytes for mobileNumber to set.
     * @return This builder for chaining.
     */
    public Builder setMobileNumberBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      mobileNumber_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.SendSMSCodeSection)
  }

  // @@protoc_insertion_point(class_scope:channel.SendSMSCodeSection)
  private static final cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SendSMSCodeSection>
      PARSER = new com.google.protobuf.AbstractParser<SendSMSCodeSection>() {
    @java.lang.Override
    public SendSMSCodeSection parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new SendSMSCodeSection(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<SendSMSCodeSection> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SendSMSCodeSection> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SendSMSCodeSection getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

