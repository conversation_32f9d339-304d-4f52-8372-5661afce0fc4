// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface SendSMSCodeSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.SendSMSCodeSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   **
   *（必传）短信验证码场景
   * 支付宝当面付ISV代签短信验证码：alipayISV_signup
   * </pre>
   *
   * <code>string sms_scene = 1;</code>
   * @return The smsScene.
   */
  java.lang.String getSmsScene();
  /**
   * <pre>
   **
   *（必传）短信验证码场景
   * 支付宝当面付ISV代签短信验证码：alipayISV_signup
   * </pre>
   *
   * <code>string sms_scene = 1;</code>
   * @return The bytes for smsScene.
   */
  com.google.protobuf.ByteString
      getSmsSceneBytes();

  /**
   * <pre>
   * （必传）手机号
   * </pre>
   *
   * <code>string mobile_number = 2;</code>
   * @return The mobileNumber.
   */
  java.lang.String getMobileNumber();
  /**
   * <pre>
   * （必传）手机号
   * </pre>
   *
   * <code>string mobile_number = 2;</code>
   * @return The bytes for mobileNumber.
   */
  com.google.protobuf.ByteString
      getMobileNumberBytes();
}
