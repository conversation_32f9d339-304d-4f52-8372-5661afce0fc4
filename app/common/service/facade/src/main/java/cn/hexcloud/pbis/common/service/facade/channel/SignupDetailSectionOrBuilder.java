// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface SignupDetailSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.SignupDetailSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）渠道代码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  java.lang.String getChannelCode();
  /**
   * <pre>
   * （必传）渠道代码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  com.google.protobuf.ByteString
      getChannelCodeBytes();

  /**
   * <pre>
   * （必传）第三方商户PID
   * </pre>
   *
   * <code>string merchant_id = 2;</code>
   * @return The merchantId.
   */
  java.lang.String getMerchantId();
  /**
   * <pre>
   * （必传）第三方商户PID
   * </pre>
   *
   * <code>string merchant_id = 2;</code>
   * @return The bytes for merchantId.
   */
  com.google.protobuf.ByteString
      getMerchantIdBytes();

  /**
   * <pre>
   * （必传）经营类目
   * </pre>
   *
   * <code>string business_category = 3;</code>
   * @return The businessCategory.
   */
  java.lang.String getBusinessCategory();
  /**
   * <pre>
   * （必传）经营类目
   * </pre>
   *
   * <code>string business_category = 3;</code>
   * @return The bytes for businessCategory.
   */
  com.google.protobuf.ByteString
      getBusinessCategoryBytes();

  /**
   * <pre>
   * （必传）签约主体
   * </pre>
   *
   * <code>string party_name = 4;</code>
   * @return The partyName.
   */
  java.lang.String getPartyName();
  /**
   * <pre>
   * （必传）签约主体
   * </pre>
   *
   * <code>string party_name = 4;</code>
   * @return The bytes for partyName.
   */
  com.google.protobuf.ByteString
      getPartyNameBytes();

  /**
   * <pre>
   * （可选）签约主体所在地
   * </pre>
   *
   * <code>.channel.Location party_location = 5;</code>
   * @return Whether the partyLocation field is set.
   */
  boolean hasPartyLocation();
  /**
   * <pre>
   * （可选）签约主体所在地
   * </pre>
   *
   * <code>.channel.Location party_location = 5;</code>
   * @return The partyLocation.
   */
  cn.hexcloud.pbis.common.service.facade.channel.Location getPartyLocation();
  /**
   * <pre>
   * （可选）签约主体所在地
   * </pre>
   *
   * <code>.channel.Location party_location = 5;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.LocationOrBuilder getPartyLocationOrBuilder();

  /**
   * <pre>
   * （可选）联系人
   * </pre>
   *
   * <code>.channel.Contact contact = 6;</code>
   * @return Whether the contact field is set.
   */
  boolean hasContact();
  /**
   * <pre>
   * （可选）联系人
   * </pre>
   *
   * <code>.channel.Contact contact = 6;</code>
   * @return The contact.
   */
  cn.hexcloud.pbis.common.service.facade.channel.Contact getContact();
  /**
   * <pre>
   * （可选）联系人
   * </pre>
   *
   * <code>.channel.Contact contact = 6;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.ContactOrBuilder getContactOrBuilder();

  /**
   * <pre>
   * （可选）签约附件
   * </pre>
   *
   * <code>repeated .channel.Attachment attachments = 7;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Attachment> 
      getAttachmentsList();
  /**
   * <pre>
   * （可选）签约附件
   * </pre>
   *
   * <code>repeated .channel.Attachment attachments = 7;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.Attachment getAttachments(int index);
  /**
   * <pre>
   * （可选）签约附件
   * </pre>
   *
   * <code>repeated .channel.Attachment attachments = 7;</code>
   */
  int getAttachmentsCount();
  /**
   * <pre>
   * （可选）签约附件
   * </pre>
   *
   * <code>repeated .channel.Attachment attachments = 7;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AttachmentOrBuilder> 
      getAttachmentsOrBuilderList();
  /**
   * <pre>
   * （可选）签约附件
   * </pre>
   *
   * <code>repeated .channel.Attachment attachments = 7;</code>
   */
  cn.hexcloud.pbis.common.service.facade.channel.AttachmentOrBuilder getAttachmentsOrBuilder(
      int index);
}
