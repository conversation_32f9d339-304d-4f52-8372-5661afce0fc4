// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface SignupResultSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.SignupResultSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）签约流水号
   * </pre>
   *
   * <code>string signup_no = 1;</code>
   * @return The signupNo.
   */
  java.lang.String getSignupNo();
  /**
   * <pre>
   * （必传）签约流水号
   * </pre>
   *
   * <code>string signup_no = 1;</code>
   * @return The bytes for signupNo.
   */
  com.google.protobuf.ByteString
      getSignupNoBytes();

  /**
   * <pre>
   * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
   * </pre>
   *
   * <code>string signup_state = 2;</code>
   * @return The signupState.
   */
  java.lang.String getSignupState();
  /**
   * <pre>
   * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
   * </pre>
   *
   * <code>string signup_state = 2;</code>
   * @return The bytes for signupState.
   */
  com.google.protobuf.ByteString
      getSignupStateBytes();

  /**
   * <pre>
   * （可选）签约结果
   * </pre>
   *
   * <code>string signup_result = 3;</code>
   * @return The signupResult.
   */
  java.lang.String getSignupResult();
  /**
   * <pre>
   * （可选）签约结果
   * </pre>
   *
   * <code>string signup_result = 3;</code>
   * @return The bytes for signupResult.
   */
  com.google.protobuf.ByteString
      getSignupResultBytes();
}
