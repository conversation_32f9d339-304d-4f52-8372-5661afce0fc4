// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道签约请求对象
 * </pre>
 *
 * Protobuf type {@code channel.SignupSection}
 */
public final class SignupSection extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.SignupSection)
    SignupSectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use SignupSection.newBuilder() to construct.
  private SignupSection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private SignupSection() {
    channelCode_ = "";
    merchantId_ = "";
    businessCategory_ = "";
    partyName_ = "";
    attachments_ = java.util.Collections.emptyList();
    smsId_ = "";
    smsCode_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new SignupSection();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private SignupSection(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            channelCode_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            merchantId_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            businessCategory_ = s;
            break;
          }
          case 33: {

            serviceRate_ = input.readDouble();
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            partyName_ = s;
            break;
          }
          case 50: {
            cn.hexcloud.pbis.common.service.facade.channel.Location.Builder subBuilder = null;
            if (partyLocation_ != null) {
              subBuilder = partyLocation_.toBuilder();
            }
            partyLocation_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.Location.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(partyLocation_);
              partyLocation_ = subBuilder.buildPartial();
            }

            break;
          }
          case 58: {
            cn.hexcloud.pbis.common.service.facade.channel.Contact.Builder subBuilder = null;
            if (contact_ != null) {
              subBuilder = contact_.toBuilder();
            }
            contact_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.Contact.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(contact_);
              contact_ = subBuilder.buildPartial();
            }

            break;
          }
          case 66: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              attachments_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.Attachment>();
              mutable_bitField0_ |= 0x00000001;
            }
            attachments_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.channel.Attachment.parser(), extensionRegistry));
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            smsId_ = s;
            break;
          }
          case 82: {
            java.lang.String s = input.readStringRequireUtf8();

            smsCode_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        attachments_ = java.util.Collections.unmodifiableList(attachments_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_SignupSection_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_SignupSection_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.SignupSection.class, cn.hexcloud.pbis.common.service.facade.channel.SignupSection.Builder.class);
  }

  public static final int CHANNEL_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object channelCode_;
  /**
   * <pre>
   * （必传）渠道代码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  @java.lang.Override
  public java.lang.String getChannelCode() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channelCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）渠道代码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelCodeBytes() {
    java.lang.Object ref = channelCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channelCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MERCHANT_ID_FIELD_NUMBER = 2;
  private volatile java.lang.Object merchantId_;
  /**
   * <pre>
   * （必传）第三方商户PID
   * </pre>
   *
   * <code>string merchant_id = 2;</code>
   * @return The merchantId.
   */
  @java.lang.Override
  public java.lang.String getMerchantId() {
    java.lang.Object ref = merchantId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      merchantId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）第三方商户PID
   * </pre>
   *
   * <code>string merchant_id = 2;</code>
   * @return The bytes for merchantId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMerchantIdBytes() {
    java.lang.Object ref = merchantId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      merchantId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BUSINESS_CATEGORY_FIELD_NUMBER = 3;
  private volatile java.lang.Object businessCategory_;
  /**
   * <pre>
   * （必传）经营类目
   * </pre>
   *
   * <code>string business_category = 3;</code>
   * @return The businessCategory.
   */
  @java.lang.Override
  public java.lang.String getBusinessCategory() {
    java.lang.Object ref = businessCategory_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      businessCategory_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）经营类目
   * </pre>
   *
   * <code>string business_category = 3;</code>
   * @return The bytes for businessCategory.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBusinessCategoryBytes() {
    java.lang.Object ref = businessCategory_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      businessCategory_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SERVICE_RATE_FIELD_NUMBER = 4;
  private double serviceRate_;
  /**
   * <pre>
   * （必传）服务费率
   * </pre>
   *
   * <code>double service_rate = 4;</code>
   * @return The serviceRate.
   */
  @java.lang.Override
  public double getServiceRate() {
    return serviceRate_;
  }

  public static final int PARTY_NAME_FIELD_NUMBER = 5;
  private volatile java.lang.Object partyName_;
  /**
   * <pre>
   * （必传）签约主体
   * </pre>
   *
   * <code>string party_name = 5;</code>
   * @return The partyName.
   */
  @java.lang.Override
  public java.lang.String getPartyName() {
    java.lang.Object ref = partyName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      partyName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）签约主体
   * </pre>
   *
   * <code>string party_name = 5;</code>
   * @return The bytes for partyName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPartyNameBytes() {
    java.lang.Object ref = partyName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      partyName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PARTY_LOCATION_FIELD_NUMBER = 6;
  private cn.hexcloud.pbis.common.service.facade.channel.Location partyLocation_;
  /**
   * <pre>
   * （可选）签约主体所在地
   * </pre>
   *
   * <code>.channel.Location party_location = 6;</code>
   * @return Whether the partyLocation field is set.
   */
  @java.lang.Override
  public boolean hasPartyLocation() {
    return partyLocation_ != null;
  }
  /**
   * <pre>
   * （可选）签约主体所在地
   * </pre>
   *
   * <code>.channel.Location party_location = 6;</code>
   * @return The partyLocation.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.Location getPartyLocation() {
    return partyLocation_ == null ? cn.hexcloud.pbis.common.service.facade.channel.Location.getDefaultInstance() : partyLocation_;
  }
  /**
   * <pre>
   * （可选）签约主体所在地
   * </pre>
   *
   * <code>.channel.Location party_location = 6;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.LocationOrBuilder getPartyLocationOrBuilder() {
    return getPartyLocation();
  }

  public static final int CONTACT_FIELD_NUMBER = 7;
  private cn.hexcloud.pbis.common.service.facade.channel.Contact contact_;
  /**
   * <pre>
   * （可选）联系人
   * </pre>
   *
   * <code>.channel.Contact contact = 7;</code>
   * @return Whether the contact field is set.
   */
  @java.lang.Override
  public boolean hasContact() {
    return contact_ != null;
  }
  /**
   * <pre>
   * （可选）联系人
   * </pre>
   *
   * <code>.channel.Contact contact = 7;</code>
   * @return The contact.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.Contact getContact() {
    return contact_ == null ? cn.hexcloud.pbis.common.service.facade.channel.Contact.getDefaultInstance() : contact_;
  }
  /**
   * <pre>
   * （可选）联系人
   * </pre>
   *
   * <code>.channel.Contact contact = 7;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.ContactOrBuilder getContactOrBuilder() {
    return getContact();
  }

  public static final int ATTACHMENTS_FIELD_NUMBER = 8;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Attachment> attachments_;
  /**
   * <pre>
   * （可选）签约附件
   * </pre>
   *
   * <code>repeated .channel.Attachment attachments = 8;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Attachment> getAttachmentsList() {
    return attachments_;
  }
  /**
   * <pre>
   * （可选）签约附件
   * </pre>
   *
   * <code>repeated .channel.Attachment attachments = 8;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AttachmentOrBuilder> 
      getAttachmentsOrBuilderList() {
    return attachments_;
  }
  /**
   * <pre>
   * （可选）签约附件
   * </pre>
   *
   * <code>repeated .channel.Attachment attachments = 8;</code>
   */
  @java.lang.Override
  public int getAttachmentsCount() {
    return attachments_.size();
  }
  /**
   * <pre>
   * （可选）签约附件
   * </pre>
   *
   * <code>repeated .channel.Attachment attachments = 8;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.Attachment getAttachments(int index) {
    return attachments_.get(index);
  }
  /**
   * <pre>
   * （可选）签约附件
   * </pre>
   *
   * <code>repeated .channel.Attachment attachments = 8;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.AttachmentOrBuilder getAttachmentsOrBuilder(
      int index) {
    return attachments_.get(index);
  }

  public static final int SMS_ID_FIELD_NUMBER = 9;
  private volatile java.lang.Object smsId_;
  /**
   * <pre>
   * （必传）短信id
   * </pre>
   *
   * <code>string sms_id = 9;</code>
   * @return The smsId.
   */
  @java.lang.Override
  public java.lang.String getSmsId() {
    java.lang.Object ref = smsId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      smsId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）短信id
   * </pre>
   *
   * <code>string sms_id = 9;</code>
   * @return The bytes for smsId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSmsIdBytes() {
    java.lang.Object ref = smsId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      smsId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SMS_CODE_FIELD_NUMBER = 10;
  private volatile java.lang.Object smsCode_;
  /**
   * <pre>
   * （必传）短信验证码
   * </pre>
   *
   * <code>string sms_code = 10;</code>
   * @return The smsCode.
   */
  @java.lang.Override
  public java.lang.String getSmsCode() {
    java.lang.Object ref = smsCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      smsCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）短信验证码
   * </pre>
   *
   * <code>string sms_code = 10;</code>
   * @return The bytes for smsCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSmsCodeBytes() {
    java.lang.Object ref = smsCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      smsCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getChannelCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channelCode_);
    }
    if (!getMerchantIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, merchantId_);
    }
    if (!getBusinessCategoryBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, businessCategory_);
    }
    if (serviceRate_ != 0D) {
      output.writeDouble(4, serviceRate_);
    }
    if (!getPartyNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, partyName_);
    }
    if (partyLocation_ != null) {
      output.writeMessage(6, getPartyLocation());
    }
    if (contact_ != null) {
      output.writeMessage(7, getContact());
    }
    for (int i = 0; i < attachments_.size(); i++) {
      output.writeMessage(8, attachments_.get(i));
    }
    if (!getSmsIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, smsId_);
    }
    if (!getSmsCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, smsCode_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getChannelCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channelCode_);
    }
    if (!getMerchantIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, merchantId_);
    }
    if (!getBusinessCategoryBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, businessCategory_);
    }
    if (serviceRate_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(4, serviceRate_);
    }
    if (!getPartyNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, partyName_);
    }
    if (partyLocation_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, getPartyLocation());
    }
    if (contact_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getContact());
    }
    for (int i = 0; i < attachments_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, attachments_.get(i));
    }
    if (!getSmsIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, smsId_);
    }
    if (!getSmsCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, smsCode_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.SignupSection)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.SignupSection other = (cn.hexcloud.pbis.common.service.facade.channel.SignupSection) obj;

    if (!getChannelCode()
        .equals(other.getChannelCode())) return false;
    if (!getMerchantId()
        .equals(other.getMerchantId())) return false;
    if (!getBusinessCategory()
        .equals(other.getBusinessCategory())) return false;
    if (java.lang.Double.doubleToLongBits(getServiceRate())
        != java.lang.Double.doubleToLongBits(
            other.getServiceRate())) return false;
    if (!getPartyName()
        .equals(other.getPartyName())) return false;
    if (hasPartyLocation() != other.hasPartyLocation()) return false;
    if (hasPartyLocation()) {
      if (!getPartyLocation()
          .equals(other.getPartyLocation())) return false;
    }
    if (hasContact() != other.hasContact()) return false;
    if (hasContact()) {
      if (!getContact()
          .equals(other.getContact())) return false;
    }
    if (!getAttachmentsList()
        .equals(other.getAttachmentsList())) return false;
    if (!getSmsId()
        .equals(other.getSmsId())) return false;
    if (!getSmsCode()
        .equals(other.getSmsCode())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHANNEL_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getChannelCode().hashCode();
    hash = (37 * hash) + MERCHANT_ID_FIELD_NUMBER;
    hash = (53 * hash) + getMerchantId().hashCode();
    hash = (37 * hash) + BUSINESS_CATEGORY_FIELD_NUMBER;
    hash = (53 * hash) + getBusinessCategory().hashCode();
    hash = (37 * hash) + SERVICE_RATE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getServiceRate()));
    hash = (37 * hash) + PARTY_NAME_FIELD_NUMBER;
    hash = (53 * hash) + getPartyName().hashCode();
    if (hasPartyLocation()) {
      hash = (37 * hash) + PARTY_LOCATION_FIELD_NUMBER;
      hash = (53 * hash) + getPartyLocation().hashCode();
    }
    if (hasContact()) {
      hash = (37 * hash) + CONTACT_FIELD_NUMBER;
      hash = (53 * hash) + getContact().hashCode();
    }
    if (getAttachmentsCount() > 0) {
      hash = (37 * hash) + ATTACHMENTS_FIELD_NUMBER;
      hash = (53 * hash) + getAttachmentsList().hashCode();
    }
    hash = (37 * hash) + SMS_ID_FIELD_NUMBER;
    hash = (53 * hash) + getSmsId().hashCode();
    hash = (37 * hash) + SMS_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getSmsCode().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.SignupSection parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupSection parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupSection parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupSection parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupSection parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupSection parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupSection parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupSection parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupSection parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupSection parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupSection parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupSection parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.SignupSection prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道签约请求对象
   * </pre>
   *
   * Protobuf type {@code channel.SignupSection}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.SignupSection)
      cn.hexcloud.pbis.common.service.facade.channel.SignupSectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_SignupSection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_SignupSection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.SignupSection.class, cn.hexcloud.pbis.common.service.facade.channel.SignupSection.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.SignupSection.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getAttachmentsFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channelCode_ = "";

      merchantId_ = "";

      businessCategory_ = "";

      serviceRate_ = 0D;

      partyName_ = "";

      if (partyLocationBuilder_ == null) {
        partyLocation_ = null;
      } else {
        partyLocation_ = null;
        partyLocationBuilder_ = null;
      }
      if (contactBuilder_ == null) {
        contact_ = null;
      } else {
        contact_ = null;
        contactBuilder_ = null;
      }
      if (attachmentsBuilder_ == null) {
        attachments_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        attachmentsBuilder_.clear();
      }
      smsId_ = "";

      smsCode_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_SignupSection_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.SignupSection getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.SignupSection.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.SignupSection build() {
      cn.hexcloud.pbis.common.service.facade.channel.SignupSection result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.SignupSection buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.SignupSection result = new cn.hexcloud.pbis.common.service.facade.channel.SignupSection(this);
      int from_bitField0_ = bitField0_;
      result.channelCode_ = channelCode_;
      result.merchantId_ = merchantId_;
      result.businessCategory_ = businessCategory_;
      result.serviceRate_ = serviceRate_;
      result.partyName_ = partyName_;
      if (partyLocationBuilder_ == null) {
        result.partyLocation_ = partyLocation_;
      } else {
        result.partyLocation_ = partyLocationBuilder_.build();
      }
      if (contactBuilder_ == null) {
        result.contact_ = contact_;
      } else {
        result.contact_ = contactBuilder_.build();
      }
      if (attachmentsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          attachments_ = java.util.Collections.unmodifiableList(attachments_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.attachments_ = attachments_;
      } else {
        result.attachments_ = attachmentsBuilder_.build();
      }
      result.smsId_ = smsId_;
      result.smsCode_ = smsCode_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.SignupSection) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.SignupSection)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.SignupSection other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.SignupSection.getDefaultInstance()) return this;
      if (!other.getChannelCode().isEmpty()) {
        channelCode_ = other.channelCode_;
        onChanged();
      }
      if (!other.getMerchantId().isEmpty()) {
        merchantId_ = other.merchantId_;
        onChanged();
      }
      if (!other.getBusinessCategory().isEmpty()) {
        businessCategory_ = other.businessCategory_;
        onChanged();
      }
      if (other.getServiceRate() != 0D) {
        setServiceRate(other.getServiceRate());
      }
      if (!other.getPartyName().isEmpty()) {
        partyName_ = other.partyName_;
        onChanged();
      }
      if (other.hasPartyLocation()) {
        mergePartyLocation(other.getPartyLocation());
      }
      if (other.hasContact()) {
        mergeContact(other.getContact());
      }
      if (attachmentsBuilder_ == null) {
        if (!other.attachments_.isEmpty()) {
          if (attachments_.isEmpty()) {
            attachments_ = other.attachments_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureAttachmentsIsMutable();
            attachments_.addAll(other.attachments_);
          }
          onChanged();
        }
      } else {
        if (!other.attachments_.isEmpty()) {
          if (attachmentsBuilder_.isEmpty()) {
            attachmentsBuilder_.dispose();
            attachmentsBuilder_ = null;
            attachments_ = other.attachments_;
            bitField0_ = (bitField0_ & ~0x00000001);
            attachmentsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getAttachmentsFieldBuilder() : null;
          } else {
            attachmentsBuilder_.addAllMessages(other.attachments_);
          }
        }
      }
      if (!other.getSmsId().isEmpty()) {
        smsId_ = other.smsId_;
        onChanged();
      }
      if (!other.getSmsCode().isEmpty()) {
        smsCode_ = other.smsCode_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.SignupSection parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.SignupSection) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object channelCode_ = "";
    /**
     * <pre>
     * （必传）渠道代码
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return The channelCode.
     */
    public java.lang.String getChannelCode() {
      java.lang.Object ref = channelCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channelCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）渠道代码
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return The bytes for channelCode.
     */
    public com.google.protobuf.ByteString
        getChannelCodeBytes() {
      java.lang.Object ref = channelCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）渠道代码
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @param value The channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channelCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）渠道代码
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelCode() {
      
      channelCode_ = getDefaultInstance().getChannelCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）渠道代码
     * </pre>
     *
     * <code>string channel_code = 1;</code>
     * @param value The bytes for channelCode to set.
     * @return This builder for chaining.
     */
    public Builder setChannelCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channelCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object merchantId_ = "";
    /**
     * <pre>
     * （必传）第三方商户PID
     * </pre>
     *
     * <code>string merchant_id = 2;</code>
     * @return The merchantId.
     */
    public java.lang.String getMerchantId() {
      java.lang.Object ref = merchantId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        merchantId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）第三方商户PID
     * </pre>
     *
     * <code>string merchant_id = 2;</code>
     * @return The bytes for merchantId.
     */
    public com.google.protobuf.ByteString
        getMerchantIdBytes() {
      java.lang.Object ref = merchantId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        merchantId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）第三方商户PID
     * </pre>
     *
     * <code>string merchant_id = 2;</code>
     * @param value The merchantId to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      merchantId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）第三方商户PID
     * </pre>
     *
     * <code>string merchant_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMerchantId() {
      
      merchantId_ = getDefaultInstance().getMerchantId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）第三方商户PID
     * </pre>
     *
     * <code>string merchant_id = 2;</code>
     * @param value The bytes for merchantId to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      merchantId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object businessCategory_ = "";
    /**
     * <pre>
     * （必传）经营类目
     * </pre>
     *
     * <code>string business_category = 3;</code>
     * @return The businessCategory.
     */
    public java.lang.String getBusinessCategory() {
      java.lang.Object ref = businessCategory_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        businessCategory_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）经营类目
     * </pre>
     *
     * <code>string business_category = 3;</code>
     * @return The bytes for businessCategory.
     */
    public com.google.protobuf.ByteString
        getBusinessCategoryBytes() {
      java.lang.Object ref = businessCategory_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        businessCategory_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）经营类目
     * </pre>
     *
     * <code>string business_category = 3;</code>
     * @param value The businessCategory to set.
     * @return This builder for chaining.
     */
    public Builder setBusinessCategory(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      businessCategory_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）经营类目
     * </pre>
     *
     * <code>string business_category = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearBusinessCategory() {
      
      businessCategory_ = getDefaultInstance().getBusinessCategory();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）经营类目
     * </pre>
     *
     * <code>string business_category = 3;</code>
     * @param value The bytes for businessCategory to set.
     * @return This builder for chaining.
     */
    public Builder setBusinessCategoryBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      businessCategory_ = value;
      onChanged();
      return this;
    }

    private double serviceRate_ ;
    /**
     * <pre>
     * （必传）服务费率
     * </pre>
     *
     * <code>double service_rate = 4;</code>
     * @return The serviceRate.
     */
    @java.lang.Override
    public double getServiceRate() {
      return serviceRate_;
    }
    /**
     * <pre>
     * （必传）服务费率
     * </pre>
     *
     * <code>double service_rate = 4;</code>
     * @param value The serviceRate to set.
     * @return This builder for chaining.
     */
    public Builder setServiceRate(double value) {
      
      serviceRate_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）服务费率
     * </pre>
     *
     * <code>double service_rate = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearServiceRate() {
      
      serviceRate_ = 0D;
      onChanged();
      return this;
    }

    private java.lang.Object partyName_ = "";
    /**
     * <pre>
     * （必传）签约主体
     * </pre>
     *
     * <code>string party_name = 5;</code>
     * @return The partyName.
     */
    public java.lang.String getPartyName() {
      java.lang.Object ref = partyName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        partyName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）签约主体
     * </pre>
     *
     * <code>string party_name = 5;</code>
     * @return The bytes for partyName.
     */
    public com.google.protobuf.ByteString
        getPartyNameBytes() {
      java.lang.Object ref = partyName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        partyName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）签约主体
     * </pre>
     *
     * <code>string party_name = 5;</code>
     * @param value The partyName to set.
     * @return This builder for chaining.
     */
    public Builder setPartyName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      partyName_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）签约主体
     * </pre>
     *
     * <code>string party_name = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearPartyName() {
      
      partyName_ = getDefaultInstance().getPartyName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）签约主体
     * </pre>
     *
     * <code>string party_name = 5;</code>
     * @param value The bytes for partyName to set.
     * @return This builder for chaining.
     */
    public Builder setPartyNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      partyName_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.Location partyLocation_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Location, cn.hexcloud.pbis.common.service.facade.channel.Location.Builder, cn.hexcloud.pbis.common.service.facade.channel.LocationOrBuilder> partyLocationBuilder_;
    /**
     * <pre>
     * （可选）签约主体所在地
     * </pre>
     *
     * <code>.channel.Location party_location = 6;</code>
     * @return Whether the partyLocation field is set.
     */
    public boolean hasPartyLocation() {
      return partyLocationBuilder_ != null || partyLocation_ != null;
    }
    /**
     * <pre>
     * （可选）签约主体所在地
     * </pre>
     *
     * <code>.channel.Location party_location = 6;</code>
     * @return The partyLocation.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Location getPartyLocation() {
      if (partyLocationBuilder_ == null) {
        return partyLocation_ == null ? cn.hexcloud.pbis.common.service.facade.channel.Location.getDefaultInstance() : partyLocation_;
      } else {
        return partyLocationBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）签约主体所在地
     * </pre>
     *
     * <code>.channel.Location party_location = 6;</code>
     */
    public Builder setPartyLocation(cn.hexcloud.pbis.common.service.facade.channel.Location value) {
      if (partyLocationBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        partyLocation_ = value;
        onChanged();
      } else {
        partyLocationBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）签约主体所在地
     * </pre>
     *
     * <code>.channel.Location party_location = 6;</code>
     */
    public Builder setPartyLocation(
        cn.hexcloud.pbis.common.service.facade.channel.Location.Builder builderForValue) {
      if (partyLocationBuilder_ == null) {
        partyLocation_ = builderForValue.build();
        onChanged();
      } else {
        partyLocationBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）签约主体所在地
     * </pre>
     *
     * <code>.channel.Location party_location = 6;</code>
     */
    public Builder mergePartyLocation(cn.hexcloud.pbis.common.service.facade.channel.Location value) {
      if (partyLocationBuilder_ == null) {
        if (partyLocation_ != null) {
          partyLocation_ =
            cn.hexcloud.pbis.common.service.facade.channel.Location.newBuilder(partyLocation_).mergeFrom(value).buildPartial();
        } else {
          partyLocation_ = value;
        }
        onChanged();
      } else {
        partyLocationBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）签约主体所在地
     * </pre>
     *
     * <code>.channel.Location party_location = 6;</code>
     */
    public Builder clearPartyLocation() {
      if (partyLocationBuilder_ == null) {
        partyLocation_ = null;
        onChanged();
      } else {
        partyLocation_ = null;
        partyLocationBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）签约主体所在地
     * </pre>
     *
     * <code>.channel.Location party_location = 6;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Location.Builder getPartyLocationBuilder() {
      
      onChanged();
      return getPartyLocationFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）签约主体所在地
     * </pre>
     *
     * <code>.channel.Location party_location = 6;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.LocationOrBuilder getPartyLocationOrBuilder() {
      if (partyLocationBuilder_ != null) {
        return partyLocationBuilder_.getMessageOrBuilder();
      } else {
        return partyLocation_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.Location.getDefaultInstance() : partyLocation_;
      }
    }
    /**
     * <pre>
     * （可选）签约主体所在地
     * </pre>
     *
     * <code>.channel.Location party_location = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Location, cn.hexcloud.pbis.common.service.facade.channel.Location.Builder, cn.hexcloud.pbis.common.service.facade.channel.LocationOrBuilder> 
        getPartyLocationFieldBuilder() {
      if (partyLocationBuilder_ == null) {
        partyLocationBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.Location, cn.hexcloud.pbis.common.service.facade.channel.Location.Builder, cn.hexcloud.pbis.common.service.facade.channel.LocationOrBuilder>(
                getPartyLocation(),
                getParentForChildren(),
                isClean());
        partyLocation_ = null;
      }
      return partyLocationBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.channel.Contact contact_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Contact, cn.hexcloud.pbis.common.service.facade.channel.Contact.Builder, cn.hexcloud.pbis.common.service.facade.channel.ContactOrBuilder> contactBuilder_;
    /**
     * <pre>
     * （可选）联系人
     * </pre>
     *
     * <code>.channel.Contact contact = 7;</code>
     * @return Whether the contact field is set.
     */
    public boolean hasContact() {
      return contactBuilder_ != null || contact_ != null;
    }
    /**
     * <pre>
     * （可选）联系人
     * </pre>
     *
     * <code>.channel.Contact contact = 7;</code>
     * @return The contact.
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Contact getContact() {
      if (contactBuilder_ == null) {
        return contact_ == null ? cn.hexcloud.pbis.common.service.facade.channel.Contact.getDefaultInstance() : contact_;
      } else {
        return contactBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）联系人
     * </pre>
     *
     * <code>.channel.Contact contact = 7;</code>
     */
    public Builder setContact(cn.hexcloud.pbis.common.service.facade.channel.Contact value) {
      if (contactBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        contact_ = value;
        onChanged();
      } else {
        contactBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）联系人
     * </pre>
     *
     * <code>.channel.Contact contact = 7;</code>
     */
    public Builder setContact(
        cn.hexcloud.pbis.common.service.facade.channel.Contact.Builder builderForValue) {
      if (contactBuilder_ == null) {
        contact_ = builderForValue.build();
        onChanged();
      } else {
        contactBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）联系人
     * </pre>
     *
     * <code>.channel.Contact contact = 7;</code>
     */
    public Builder mergeContact(cn.hexcloud.pbis.common.service.facade.channel.Contact value) {
      if (contactBuilder_ == null) {
        if (contact_ != null) {
          contact_ =
            cn.hexcloud.pbis.common.service.facade.channel.Contact.newBuilder(contact_).mergeFrom(value).buildPartial();
        } else {
          contact_ = value;
        }
        onChanged();
      } else {
        contactBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）联系人
     * </pre>
     *
     * <code>.channel.Contact contact = 7;</code>
     */
    public Builder clearContact() {
      if (contactBuilder_ == null) {
        contact_ = null;
        onChanged();
      } else {
        contact_ = null;
        contactBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）联系人
     * </pre>
     *
     * <code>.channel.Contact contact = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Contact.Builder getContactBuilder() {
      
      onChanged();
      return getContactFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）联系人
     * </pre>
     *
     * <code>.channel.Contact contact = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.ContactOrBuilder getContactOrBuilder() {
      if (contactBuilder_ != null) {
        return contactBuilder_.getMessageOrBuilder();
      } else {
        return contact_ == null ?
            cn.hexcloud.pbis.common.service.facade.channel.Contact.getDefaultInstance() : contact_;
      }
    }
    /**
     * <pre>
     * （可选）联系人
     * </pre>
     *
     * <code>.channel.Contact contact = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Contact, cn.hexcloud.pbis.common.service.facade.channel.Contact.Builder, cn.hexcloud.pbis.common.service.facade.channel.ContactOrBuilder> 
        getContactFieldBuilder() {
      if (contactBuilder_ == null) {
        contactBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.Contact, cn.hexcloud.pbis.common.service.facade.channel.Contact.Builder, cn.hexcloud.pbis.common.service.facade.channel.ContactOrBuilder>(
                getContact(),
                getParentForChildren(),
                isClean());
        contact_ = null;
      }
      return contactBuilder_;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Attachment> attachments_ =
      java.util.Collections.emptyList();
    private void ensureAttachmentsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        attachments_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.channel.Attachment>(attachments_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Attachment, cn.hexcloud.pbis.common.service.facade.channel.Attachment.Builder, cn.hexcloud.pbis.common.service.facade.channel.AttachmentOrBuilder> attachmentsBuilder_;

    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Attachment> getAttachmentsList() {
      if (attachmentsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(attachments_);
      } else {
        return attachmentsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public int getAttachmentsCount() {
      if (attachmentsBuilder_ == null) {
        return attachments_.size();
      } else {
        return attachmentsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Attachment getAttachments(int index) {
      if (attachmentsBuilder_ == null) {
        return attachments_.get(index);
      } else {
        return attachmentsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public Builder setAttachments(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Attachment value) {
      if (attachmentsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAttachmentsIsMutable();
        attachments_.set(index, value);
        onChanged();
      } else {
        attachmentsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public Builder setAttachments(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Attachment.Builder builderForValue) {
      if (attachmentsBuilder_ == null) {
        ensureAttachmentsIsMutable();
        attachments_.set(index, builderForValue.build());
        onChanged();
      } else {
        attachmentsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public Builder addAttachments(cn.hexcloud.pbis.common.service.facade.channel.Attachment value) {
      if (attachmentsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAttachmentsIsMutable();
        attachments_.add(value);
        onChanged();
      } else {
        attachmentsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public Builder addAttachments(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Attachment value) {
      if (attachmentsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAttachmentsIsMutable();
        attachments_.add(index, value);
        onChanged();
      } else {
        attachmentsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public Builder addAttachments(
        cn.hexcloud.pbis.common.service.facade.channel.Attachment.Builder builderForValue) {
      if (attachmentsBuilder_ == null) {
        ensureAttachmentsIsMutable();
        attachments_.add(builderForValue.build());
        onChanged();
      } else {
        attachmentsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public Builder addAttachments(
        int index, cn.hexcloud.pbis.common.service.facade.channel.Attachment.Builder builderForValue) {
      if (attachmentsBuilder_ == null) {
        ensureAttachmentsIsMutable();
        attachments_.add(index, builderForValue.build());
        onChanged();
      } else {
        attachmentsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public Builder addAllAttachments(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.channel.Attachment> values) {
      if (attachmentsBuilder_ == null) {
        ensureAttachmentsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, attachments_);
        onChanged();
      } else {
        attachmentsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public Builder clearAttachments() {
      if (attachmentsBuilder_ == null) {
        attachments_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        attachmentsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public Builder removeAttachments(int index) {
      if (attachmentsBuilder_ == null) {
        ensureAttachmentsIsMutable();
        attachments_.remove(index);
        onChanged();
      } else {
        attachmentsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Attachment.Builder getAttachmentsBuilder(
        int index) {
      return getAttachmentsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.AttachmentOrBuilder getAttachmentsOrBuilder(
        int index) {
      if (attachmentsBuilder_ == null) {
        return attachments_.get(index);  } else {
        return attachmentsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.channel.AttachmentOrBuilder> 
         getAttachmentsOrBuilderList() {
      if (attachmentsBuilder_ != null) {
        return attachmentsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(attachments_);
      }
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Attachment.Builder addAttachmentsBuilder() {
      return getAttachmentsFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.channel.Attachment.getDefaultInstance());
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.channel.Attachment.Builder addAttachmentsBuilder(
        int index) {
      return getAttachmentsFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.channel.Attachment.getDefaultInstance());
    }
    /**
     * <pre>
     * （可选）签约附件
     * </pre>
     *
     * <code>repeated .channel.Attachment attachments = 8;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.channel.Attachment.Builder> 
         getAttachmentsBuilderList() {
      return getAttachmentsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.channel.Attachment, cn.hexcloud.pbis.common.service.facade.channel.Attachment.Builder, cn.hexcloud.pbis.common.service.facade.channel.AttachmentOrBuilder> 
        getAttachmentsFieldBuilder() {
      if (attachmentsBuilder_ == null) {
        attachmentsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.channel.Attachment, cn.hexcloud.pbis.common.service.facade.channel.Attachment.Builder, cn.hexcloud.pbis.common.service.facade.channel.AttachmentOrBuilder>(
                attachments_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        attachments_ = null;
      }
      return attachmentsBuilder_;
    }

    private java.lang.Object smsId_ = "";
    /**
     * <pre>
     * （必传）短信id
     * </pre>
     *
     * <code>string sms_id = 9;</code>
     * @return The smsId.
     */
    public java.lang.String getSmsId() {
      java.lang.Object ref = smsId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        smsId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）短信id
     * </pre>
     *
     * <code>string sms_id = 9;</code>
     * @return The bytes for smsId.
     */
    public com.google.protobuf.ByteString
        getSmsIdBytes() {
      java.lang.Object ref = smsId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        smsId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）短信id
     * </pre>
     *
     * <code>string sms_id = 9;</code>
     * @param value The smsId to set.
     * @return This builder for chaining.
     */
    public Builder setSmsId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      smsId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）短信id
     * </pre>
     *
     * <code>string sms_id = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearSmsId() {
      
      smsId_ = getDefaultInstance().getSmsId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）短信id
     * </pre>
     *
     * <code>string sms_id = 9;</code>
     * @param value The bytes for smsId to set.
     * @return This builder for chaining.
     */
    public Builder setSmsIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      smsId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object smsCode_ = "";
    /**
     * <pre>
     * （必传）短信验证码
     * </pre>
     *
     * <code>string sms_code = 10;</code>
     * @return The smsCode.
     */
    public java.lang.String getSmsCode() {
      java.lang.Object ref = smsCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        smsCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）短信验证码
     * </pre>
     *
     * <code>string sms_code = 10;</code>
     * @return The bytes for smsCode.
     */
    public com.google.protobuf.ByteString
        getSmsCodeBytes() {
      java.lang.Object ref = smsCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        smsCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）短信验证码
     * </pre>
     *
     * <code>string sms_code = 10;</code>
     * @param value The smsCode to set.
     * @return This builder for chaining.
     */
    public Builder setSmsCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      smsCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）短信验证码
     * </pre>
     *
     * <code>string sms_code = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearSmsCode() {
      
      smsCode_ = getDefaultInstance().getSmsCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）短信验证码
     * </pre>
     *
     * <code>string sms_code = 10;</code>
     * @param value The bytes for smsCode to set.
     * @return This builder for chaining.
     */
    public Builder setSmsCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      smsCode_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.SignupSection)
  }

  // @@protoc_insertion_point(class_scope:channel.SignupSection)
  private static final cn.hexcloud.pbis.common.service.facade.channel.SignupSection DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.SignupSection();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.SignupSection getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SignupSection>
      PARSER = new com.google.protobuf.AbstractParser<SignupSection>() {
    @java.lang.Override
    public SignupSection parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new SignupSection(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<SignupSection> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SignupSection> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SignupSection getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

