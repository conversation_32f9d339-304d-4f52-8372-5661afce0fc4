// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

/**
 * <pre>
 * 渠道签约状态信息
 * </pre>
 *
 * Protobuf type {@code channel.SignupStateItem}
 */
public final class SignupStateItem extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:channel.SignupStateItem)
    SignupStateItemOrBuilder {
private static final long serialVersionUID = 0L;
  // Use SignupStateItem.newBuilder() to construct.
  private SignupStateItem(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private SignupStateItem() {
    signupState_ = "";
    signupResult_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new SignupStateItem();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private SignupStateItem(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            channelAuthId_ = input.readInt32();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            signupState_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            signupResult_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_SignupStateItem_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_SignupStateItem_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.class, cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.Builder.class);
  }

  public static final int CHANNEL_AUTH_ID_FIELD_NUMBER = 1;
  private int channelAuthId_;
  /**
   * <pre>
   * （必传）渠道签约授权id
   * </pre>
   *
   * <code>int32 channel_auth_id = 1;</code>
   * @return The channelAuthId.
   */
  @java.lang.Override
  public int getChannelAuthId() {
    return channelAuthId_;
  }

  public static final int SIGNUP_STATE_FIELD_NUMBER = 2;
  private volatile java.lang.Object signupState_;
  /**
   * <pre>
   * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
   * </pre>
   *
   * <code>string signup_state = 2;</code>
   * @return The signupState.
   */
  @java.lang.Override
  public java.lang.String getSignupState() {
    java.lang.Object ref = signupState_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      signupState_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
   * </pre>
   *
   * <code>string signup_state = 2;</code>
   * @return The bytes for signupState.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSignupStateBytes() {
    java.lang.Object ref = signupState_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      signupState_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SIGNUP_RESULT_FIELD_NUMBER = 3;
  private volatile java.lang.Object signupResult_;
  /**
   * <pre>
   * （可选）签约结果
   * </pre>
   *
   * <code>string signup_result = 3;</code>
   * @return The signupResult.
   */
  @java.lang.Override
  public java.lang.String getSignupResult() {
    java.lang.Object ref = signupResult_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      signupResult_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）签约结果
   * </pre>
   *
   * <code>string signup_result = 3;</code>
   * @return The bytes for signupResult.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSignupResultBytes() {
    java.lang.Object ref = signupResult_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      signupResult_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (channelAuthId_ != 0) {
      output.writeInt32(1, channelAuthId_);
    }
    if (!getSignupStateBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, signupState_);
    }
    if (!getSignupResultBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, signupResult_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (channelAuthId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, channelAuthId_);
    }
    if (!getSignupStateBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, signupState_);
    }
    if (!getSignupResultBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, signupResult_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem other = (cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem) obj;

    if (getChannelAuthId()
        != other.getChannelAuthId()) return false;
    if (!getSignupState()
        .equals(other.getSignupState())) return false;
    if (!getSignupResult()
        .equals(other.getSignupResult())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHANNEL_AUTH_ID_FIELD_NUMBER;
    hash = (53 * hash) + getChannelAuthId();
    hash = (37 * hash) + SIGNUP_STATE_FIELD_NUMBER;
    hash = (53 * hash) + getSignupState().hashCode();
    hash = (37 * hash) + SIGNUP_RESULT_FIELD_NUMBER;
    hash = (53 * hash) + getSignupResult().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 渠道签约状态信息
   * </pre>
   *
   * Protobuf type {@code channel.SignupStateItem}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:channel.SignupStateItem)
      cn.hexcloud.pbis.common.service.facade.channel.SignupStateItemOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_SignupStateItem_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_SignupStateItem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.class, cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channelAuthId_ = 0;

      signupState_ = "";

      signupResult_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.ChannelManagementOuterClass.internal_static_channel_SignupStateItem_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem build() {
      cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem buildPartial() {
      cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem result = new cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem(this);
      result.channelAuthId_ = channelAuthId_;
      result.signupState_ = signupState_;
      result.signupResult_ = signupResult_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem other) {
      if (other == cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem.getDefaultInstance()) return this;
      if (other.getChannelAuthId() != 0) {
        setChannelAuthId(other.getChannelAuthId());
      }
      if (!other.getSignupState().isEmpty()) {
        signupState_ = other.signupState_;
        onChanged();
      }
      if (!other.getSignupResult().isEmpty()) {
        signupResult_ = other.signupResult_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int channelAuthId_ ;
    /**
     * <pre>
     * （必传）渠道签约授权id
     * </pre>
     *
     * <code>int32 channel_auth_id = 1;</code>
     * @return The channelAuthId.
     */
    @java.lang.Override
    public int getChannelAuthId() {
      return channelAuthId_;
    }
    /**
     * <pre>
     * （必传）渠道签约授权id
     * </pre>
     *
     * <code>int32 channel_auth_id = 1;</code>
     * @param value The channelAuthId to set.
     * @return This builder for chaining.
     */
    public Builder setChannelAuthId(int value) {
      
      channelAuthId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）渠道签约授权id
     * </pre>
     *
     * <code>int32 channel_auth_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannelAuthId() {
      
      channelAuthId_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object signupState_ = "";
    /**
     * <pre>
     * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
     * </pre>
     *
     * <code>string signup_state = 2;</code>
     * @return The signupState.
     */
    public java.lang.String getSignupState() {
      java.lang.Object ref = signupState_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        signupState_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
     * </pre>
     *
     * <code>string signup_state = 2;</code>
     * @return The bytes for signupState.
     */
    public com.google.protobuf.ByteString
        getSignupStateBytes() {
      java.lang.Object ref = signupState_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        signupState_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
     * </pre>
     *
     * <code>string signup_state = 2;</code>
     * @param value The signupState to set.
     * @return This builder for chaining.
     */
    public Builder setSignupState(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      signupState_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
     * </pre>
     *
     * <code>string signup_state = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSignupState() {
      
      signupState_ = getDefaultInstance().getSignupState();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
     * </pre>
     *
     * <code>string signup_state = 2;</code>
     * @param value The bytes for signupState to set.
     * @return This builder for chaining.
     */
    public Builder setSignupStateBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      signupState_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object signupResult_ = "";
    /**
     * <pre>
     * （可选）签约结果
     * </pre>
     *
     * <code>string signup_result = 3;</code>
     * @return The signupResult.
     */
    public java.lang.String getSignupResult() {
      java.lang.Object ref = signupResult_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        signupResult_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）签约结果
     * </pre>
     *
     * <code>string signup_result = 3;</code>
     * @return The bytes for signupResult.
     */
    public com.google.protobuf.ByteString
        getSignupResultBytes() {
      java.lang.Object ref = signupResult_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        signupResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）签约结果
     * </pre>
     *
     * <code>string signup_result = 3;</code>
     * @param value The signupResult to set.
     * @return This builder for chaining.
     */
    public Builder setSignupResult(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      signupResult_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）签约结果
     * </pre>
     *
     * <code>string signup_result = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearSignupResult() {
      
      signupResult_ = getDefaultInstance().getSignupResult();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）签约结果
     * </pre>
     *
     * <code>string signup_result = 3;</code>
     * @param value The bytes for signupResult to set.
     * @return This builder for chaining.
     */
    public Builder setSignupResultBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      signupResult_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:channel.SignupStateItem)
  }

  // @@protoc_insertion_point(class_scope:channel.SignupStateItem)
  private static final cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem();
  }

  public static cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SignupStateItem>
      PARSER = new com.google.protobuf.AbstractParser<SignupStateItem>() {
    @java.lang.Override
    public SignupStateItem parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new SignupStateItem(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<SignupStateItem> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SignupStateItem> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.channel.SignupStateItem getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

