// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface SignupStateItemOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.SignupStateItem)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）渠道签约授权id
   * </pre>
   *
   * <code>int32 channel_auth_id = 1;</code>
   * @return The channelAuthId.
   */
  int getChannelAuthId();

  /**
   * <pre>
   * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
   * </pre>
   *
   * <code>string signup_state = 2;</code>
   * @return The signupState.
   */
  java.lang.String getSignupState();
  /**
   * <pre>
   * （必传）签约状态，WAITING 待签约；UNDER_REVIEW 签约审核中；UNCONFIRMED 待商家确认；COMPLETE 已签约；FAILURE 签约失败
   * </pre>
   *
   * <code>string signup_state = 2;</code>
   * @return The bytes for signupState.
   */
  com.google.protobuf.ByteString
      getSignupStateBytes();

  /**
   * <pre>
   * （可选）签约结果
   * </pre>
   *
   * <code>string signup_result = 3;</code>
   * @return The signupResult.
   */
  java.lang.String getSignupResult();
  /**
   * <pre>
   * （可选）签约结果
   * </pre>
   *
   * <code>string signup_result = 3;</code>
   * @return The bytes for signupResult.
   */
  com.google.protobuf.ByteString
      getSignupResultBytes();
}
