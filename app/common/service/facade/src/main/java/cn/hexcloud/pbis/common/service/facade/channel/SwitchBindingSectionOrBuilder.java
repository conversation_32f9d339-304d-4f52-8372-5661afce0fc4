// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ChannelManagement.proto

package cn.hexcloud.pbis.common.service.facade.channel;

public interface SwitchBindingSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:channel.SwitchBindingSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）渠道代码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The channelCode.
   */
  java.lang.String getChannelCode();
  /**
   * <pre>
   * （必传）渠道代码
   * </pre>
   *
   * <code>string channel_code = 1;</code>
   * @return The bytes for channelCode.
   */
  com.google.protobuf.ByteString
      getChannelCodeBytes();

  /**
   * <pre>
   * （可选）是否启用，与business_code不能同时为空
   * </pre>
   *
   * <code>string enabled = 2;</code>
   * @return The enabled.
   */
  java.lang.String getEnabled();
  /**
   * <pre>
   * （可选）是否启用，与business_code不能同时为空
   * </pre>
   *
   * <code>string enabled = 2;</code>
   * @return The bytes for enabled.
   */
  com.google.protobuf.ByteString
      getEnabledBytes();

  /**
   * <pre>
   * （可选）启用的渠道业务代码，","分割，与enabled不能同时为空
   * </pre>
   *
   * <code>string business_code = 3;</code>
   * @return The businessCode.
   */
  java.lang.String getBusinessCode();
  /**
   * <pre>
   * （可选）启用的渠道业务代码，","分割，与enabled不能同时为空
   * </pre>
   *
   * <code>string business_code = 3;</code>
   * @return The bytes for businessCode.
   */
  com.google.protobuf.ByteString
      getBusinessCodeBytes();
}
