// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface AbsorbedExpensesOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.AbsorbedExpenses)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 商品id
   * </pre>
   *
   * <code>string key_id = 1;</code>
   * @return The keyId.
   */
  java.lang.String getKeyId();
  /**
   * <pre>
   * 商品id
   * </pre>
   *
   * <code>string key_id = 1;</code>
   * @return The bytes for keyId.
   */
  com.google.protobuf.ByteString
      getKeyIdBytes();

  /**
   * <pre>
   * 换购商品优惠方式（AMOUNT:金额折扣，PERCENT:百分比折扣，PRICE:固定价格）
   * </pre>
   *
   * <code>string method = 2;</code>
   * @return The method.
   */
  java.lang.String getMethod();
  /**
   * <pre>
   * 换购商品优惠方式（AMOUNT:金额折扣，PERCENT:百分比折扣，PRICE:固定价格）
   * </pre>
   *
   * <code>string method = 2;</code>
   * @return The bytes for method.
   */
  com.google.protobuf.ByteString
      getMethodBytes();

  /**
   * <pre>
   * 商品价格
   * </pre>
   *
   * <code>string price = 3;</code>
   * @return The price.
   */
  java.lang.String getPrice();
  /**
   * <pre>
   * 商品价格
   * </pre>
   *
   * <code>string price = 3;</code>
   * @return The bytes for price.
   */
  com.google.protobuf.ByteString
      getPriceBytes();

  /**
   * <pre>
   * 商品数量
   * </pre>
   *
   * <code>int32 qty = 4;</code>
   * @return The qty.
   */
  int getQty();

  /**
   * <pre>
   * 折扣后总价
   * </pre>
   *
   * <code>string amt = 5;</code>
   * @return The amt.
   */
  java.lang.String getAmt();
  /**
   * <pre>
   * 折扣后总价
   * </pre>
   *
   * <code>string amt = 5;</code>
   * @return The bytes for amt.
   */
  com.google.protobuf.ByteString
      getAmtBytes();

  /**
   * <pre>
   * 换购商品折扣（根据优惠方式，数值代表的意义不同，AMOUNT:折扣金额，PERCENT:折扣百分比，PRICE:固定价格)
   * </pre>
   *
   * <code>string discount = 6;</code>
   * @return The discount.
   */
  java.lang.String getDiscount();
  /**
   * <pre>
   * 换购商品折扣（根据优惠方式，数值代表的意义不同，AMOUNT:折扣金额，PERCENT:折扣百分比，PRICE:固定价格)
   * </pre>
   *
   * <code>string discount = 6;</code>
   * @return The bytes for discount.
   */
  com.google.protobuf.ByteString
      getDiscountBytes();

  /**
   * <pre>
   * 商品折扣金额
   * </pre>
   *
   * <code>string price_discount = 7;</code>
   * @return The priceDiscount.
   */
  java.lang.String getPriceDiscount();
  /**
   * <pre>
   * 商品折扣金额
   * </pre>
   *
   * <code>string price_discount = 7;</code>
   * @return The bytes for priceDiscount.
   */
  com.google.protobuf.ByteString
      getPriceDiscountBytes();

  /**
   * <pre>
   *分摊信息
   * </pre>
   *
   * <code>repeated .coupon.ChargeInfo charge_info = 8;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.member.ChargeInfo> 
      getChargeInfoList();
  /**
   * <pre>
   *分摊信息
   * </pre>
   *
   * <code>repeated .coupon.ChargeInfo charge_info = 8;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.ChargeInfo getChargeInfo(int index);
  /**
   * <pre>
   *分摊信息
   * </pre>
   *
   * <code>repeated .coupon.ChargeInfo charge_info = 8;</code>
   */
  int getChargeInfoCount();
  /**
   * <pre>
   *分摊信息
   * </pre>
   *
   * <code>repeated .coupon.ChargeInfo charge_info = 8;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.ChargeInfoOrBuilder> 
      getChargeInfoOrBuilderList();
  /**
   * <pre>
   *分摊信息
   * </pre>
   *
   * <code>repeated .coupon.ChargeInfo charge_info = 8;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.ChargeInfoOrBuilder getChargeInfoOrBuilder(
      int index);

  /**
   * <pre>
   *商品信息扩展字段-回传
   * </pre>
   *
   * <code>string shop_id = 9;</code>
   * @return The shopId.
   */
  java.lang.String getShopId();
  /**
   * <pre>
   *商品信息扩展字段-回传
   * </pre>
   *
   * <code>string shop_id = 9;</code>
   * @return The bytes for shopId.
   */
  com.google.protobuf.ByteString
      getShopIdBytes();
}
