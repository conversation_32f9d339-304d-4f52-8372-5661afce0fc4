// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

/**
 * Protobuf type {@code coupon.AvailablePointsRequest}
 */
public final class AvailablePointsRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:coupon.AvailablePointsRequest)
    AvailablePointsRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use AvailablePointsRequest.newBuilder() to construct.
  private AvailablePointsRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private AvailablePointsRequest() {
    channel_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new AvailablePointsRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private AvailablePointsRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            channel_ = s;
            break;
          }
          case 18: {
            cn.hexcloud.pbis.common.service.facade.member.MemberContent.Builder subBuilder = null;
            if (memberContent_ != null) {
              subBuilder = memberContent_.toBuilder();
            }
            memberContent_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.member.MemberContent.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(memberContent_);
              memberContent_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            cn.hexcloud.pbis.common.service.facade.member.OrderContent.Builder subBuilder = null;
            if (orderContent_ != null) {
              subBuilder = orderContent_.toBuilder();
            }
            orderContent_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.member.OrderContent.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(orderContent_);
              orderContent_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_AvailablePointsRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_AvailablePointsRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest.class, cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest.Builder.class);
  }

  public static final int CHANNEL_FIELD_NUMBER = 1;
  private volatile java.lang.Object channel_;
  /**
   * <pre>
   * 渠道编码(必传)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  @java.lang.Override
  public java.lang.String getChannel() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channel_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道编码(必传)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelBytes() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MEMBER_CONTENT_FIELD_NUMBER = 2;
  private cn.hexcloud.pbis.common.service.facade.member.MemberContent memberContent_;
  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * <code>.coupon.MemberContent member_content = 2;</code>
   * @return Whether the memberContent field is set.
   */
  @java.lang.Override
  public boolean hasMemberContent() {
    return memberContent_ != null;
  }
  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * <code>.coupon.MemberContent member_content = 2;</code>
   * @return The memberContent.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.MemberContent getMemberContent() {
    return memberContent_ == null ? cn.hexcloud.pbis.common.service.facade.member.MemberContent.getDefaultInstance() : memberContent_;
  }
  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * <code>.coupon.MemberContent member_content = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.MemberContentOrBuilder getMemberContentOrBuilder() {
    return getMemberContent();
  }

  public static final int ORDER_CONTENT_FIELD_NUMBER = 3;
  private cn.hexcloud.pbis.common.service.facade.member.OrderContent orderContent_;
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 3;</code>
   * @return Whether the orderContent field is set.
   */
  @java.lang.Override
  public boolean hasOrderContent() {
    return orderContent_ != null;
  }
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 3;</code>
   * @return The orderContent.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.OrderContent getOrderContent() {
    return orderContent_ == null ? cn.hexcloud.pbis.common.service.facade.member.OrderContent.getDefaultInstance() : orderContent_;
  }
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.OrderContentOrBuilder getOrderContentOrBuilder() {
    return getOrderContent();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getChannelBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channel_);
    }
    if (memberContent_ != null) {
      output.writeMessage(2, getMemberContent());
    }
    if (orderContent_ != null) {
      output.writeMessage(3, getOrderContent());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getChannelBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channel_);
    }
    if (memberContent_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getMemberContent());
    }
    if (orderContent_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getOrderContent());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest other = (cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest) obj;

    if (!getChannel()
        .equals(other.getChannel())) return false;
    if (hasMemberContent() != other.hasMemberContent()) return false;
    if (hasMemberContent()) {
      if (!getMemberContent()
          .equals(other.getMemberContent())) return false;
    }
    if (hasOrderContent() != other.hasOrderContent()) return false;
    if (hasOrderContent()) {
      if (!getOrderContent()
          .equals(other.getOrderContent())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
    hash = (53 * hash) + getChannel().hashCode();
    if (hasMemberContent()) {
      hash = (37 * hash) + MEMBER_CONTENT_FIELD_NUMBER;
      hash = (53 * hash) + getMemberContent().hashCode();
    }
    if (hasOrderContent()) {
      hash = (37 * hash) + ORDER_CONTENT_FIELD_NUMBER;
      hash = (53 * hash) + getOrderContent().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code coupon.AvailablePointsRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:coupon.AvailablePointsRequest)
      cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_AvailablePointsRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_AvailablePointsRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest.class, cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channel_ = "";

      if (memberContentBuilder_ == null) {
        memberContent_ = null;
      } else {
        memberContent_ = null;
        memberContentBuilder_ = null;
      }
      if (orderContentBuilder_ == null) {
        orderContent_ = null;
      } else {
        orderContent_ = null;
        orderContentBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_AvailablePointsRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest build() {
      cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest buildPartial() {
      cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest result = new cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest(this);
      result.channel_ = channel_;
      if (memberContentBuilder_ == null) {
        result.memberContent_ = memberContent_;
      } else {
        result.memberContent_ = memberContentBuilder_.build();
      }
      if (orderContentBuilder_ == null) {
        result.orderContent_ = orderContent_;
      } else {
        result.orderContent_ = orderContentBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest other) {
      if (other == cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest.getDefaultInstance()) return this;
      if (!other.getChannel().isEmpty()) {
        channel_ = other.channel_;
        onChanged();
      }
      if (other.hasMemberContent()) {
        mergeMemberContent(other.getMemberContent());
      }
      if (other.hasOrderContent()) {
        mergeOrderContent(other.getOrderContent());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object channel_ = "";
    /**
     * <pre>
     * 渠道编码(必传)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return The channel.
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channel_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道编码(必传)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return The bytes for channel.
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道编码(必传)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @param value The channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannel(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channel_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道编码(必传)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannel() {
      
      channel_ = getDefaultInstance().getChannel();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道编码(必传)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @param value The bytes for channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channel_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.member.MemberContent memberContent_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.MemberContent, cn.hexcloud.pbis.common.service.facade.member.MemberContent.Builder, cn.hexcloud.pbis.common.service.facade.member.MemberContentOrBuilder> memberContentBuilder_;
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 2;</code>
     * @return Whether the memberContent field is set.
     */
    public boolean hasMemberContent() {
      return memberContentBuilder_ != null || memberContent_ != null;
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 2;</code>
     * @return The memberContent.
     */
    public cn.hexcloud.pbis.common.service.facade.member.MemberContent getMemberContent() {
      if (memberContentBuilder_ == null) {
        return memberContent_ == null ? cn.hexcloud.pbis.common.service.facade.member.MemberContent.getDefaultInstance() : memberContent_;
      } else {
        return memberContentBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 2;</code>
     */
    public Builder setMemberContent(cn.hexcloud.pbis.common.service.facade.member.MemberContent value) {
      if (memberContentBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        memberContent_ = value;
        onChanged();
      } else {
        memberContentBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 2;</code>
     */
    public Builder setMemberContent(
        cn.hexcloud.pbis.common.service.facade.member.MemberContent.Builder builderForValue) {
      if (memberContentBuilder_ == null) {
        memberContent_ = builderForValue.build();
        onChanged();
      } else {
        memberContentBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 2;</code>
     */
    public Builder mergeMemberContent(cn.hexcloud.pbis.common.service.facade.member.MemberContent value) {
      if (memberContentBuilder_ == null) {
        if (memberContent_ != null) {
          memberContent_ =
            cn.hexcloud.pbis.common.service.facade.member.MemberContent.newBuilder(memberContent_).mergeFrom(value).buildPartial();
        } else {
          memberContent_ = value;
        }
        onChanged();
      } else {
        memberContentBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 2;</code>
     */
    public Builder clearMemberContent() {
      if (memberContentBuilder_ == null) {
        memberContent_ = null;
        onChanged();
      } else {
        memberContent_ = null;
        memberContentBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.MemberContent.Builder getMemberContentBuilder() {
      
      onChanged();
      return getMemberContentFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.MemberContentOrBuilder getMemberContentOrBuilder() {
      if (memberContentBuilder_ != null) {
        return memberContentBuilder_.getMessageOrBuilder();
      } else {
        return memberContent_ == null ?
            cn.hexcloud.pbis.common.service.facade.member.MemberContent.getDefaultInstance() : memberContent_;
      }
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.MemberContent, cn.hexcloud.pbis.common.service.facade.member.MemberContent.Builder, cn.hexcloud.pbis.common.service.facade.member.MemberContentOrBuilder> 
        getMemberContentFieldBuilder() {
      if (memberContentBuilder_ == null) {
        memberContentBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.member.MemberContent, cn.hexcloud.pbis.common.service.facade.member.MemberContent.Builder, cn.hexcloud.pbis.common.service.facade.member.MemberContentOrBuilder>(
                getMemberContent(),
                getParentForChildren(),
                isClean());
        memberContent_ = null;
      }
      return memberContentBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.member.OrderContent orderContent_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.OrderContent, cn.hexcloud.pbis.common.service.facade.member.OrderContent.Builder, cn.hexcloud.pbis.common.service.facade.member.OrderContentOrBuilder> orderContentBuilder_;
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 3;</code>
     * @return Whether the orderContent field is set.
     */
    public boolean hasOrderContent() {
      return orderContentBuilder_ != null || orderContent_ != null;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 3;</code>
     * @return The orderContent.
     */
    public cn.hexcloud.pbis.common.service.facade.member.OrderContent getOrderContent() {
      if (orderContentBuilder_ == null) {
        return orderContent_ == null ? cn.hexcloud.pbis.common.service.facade.member.OrderContent.getDefaultInstance() : orderContent_;
      } else {
        return orderContentBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 3;</code>
     */
    public Builder setOrderContent(cn.hexcloud.pbis.common.service.facade.member.OrderContent value) {
      if (orderContentBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        orderContent_ = value;
        onChanged();
      } else {
        orderContentBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 3;</code>
     */
    public Builder setOrderContent(
        cn.hexcloud.pbis.common.service.facade.member.OrderContent.Builder builderForValue) {
      if (orderContentBuilder_ == null) {
        orderContent_ = builderForValue.build();
        onChanged();
      } else {
        orderContentBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 3;</code>
     */
    public Builder mergeOrderContent(cn.hexcloud.pbis.common.service.facade.member.OrderContent value) {
      if (orderContentBuilder_ == null) {
        if (orderContent_ != null) {
          orderContent_ =
            cn.hexcloud.pbis.common.service.facade.member.OrderContent.newBuilder(orderContent_).mergeFrom(value).buildPartial();
        } else {
          orderContent_ = value;
        }
        onChanged();
      } else {
        orderContentBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 3;</code>
     */
    public Builder clearOrderContent() {
      if (orderContentBuilder_ == null) {
        orderContent_ = null;
        onChanged();
      } else {
        orderContent_ = null;
        orderContentBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.OrderContent.Builder getOrderContentBuilder() {
      
      onChanged();
      return getOrderContentFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.OrderContentOrBuilder getOrderContentOrBuilder() {
      if (orderContentBuilder_ != null) {
        return orderContentBuilder_.getMessageOrBuilder();
      } else {
        return orderContent_ == null ?
            cn.hexcloud.pbis.common.service.facade.member.OrderContent.getDefaultInstance() : orderContent_;
      }
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.OrderContent, cn.hexcloud.pbis.common.service.facade.member.OrderContent.Builder, cn.hexcloud.pbis.common.service.facade.member.OrderContentOrBuilder> 
        getOrderContentFieldBuilder() {
      if (orderContentBuilder_ == null) {
        orderContentBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.member.OrderContent, cn.hexcloud.pbis.common.service.facade.member.OrderContent.Builder, cn.hexcloud.pbis.common.service.facade.member.OrderContentOrBuilder>(
                getOrderContent(),
                getParentForChildren(),
                isClean());
        orderContent_ = null;
      }
      return orderContentBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:coupon.AvailablePointsRequest)
  }

  // @@protoc_insertion_point(class_scope:coupon.AvailablePointsRequest)
  private static final cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest();
  }

  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AvailablePointsRequest>
      PARSER = new com.google.protobuf.AbstractParser<AvailablePointsRequest>() {
    @java.lang.Override
    public AvailablePointsRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new AvailablePointsRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<AvailablePointsRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AvailablePointsRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.AvailablePointsRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

