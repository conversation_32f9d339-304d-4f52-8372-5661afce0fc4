// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface AvailablePointsRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.AvailablePointsRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 渠道编码(必传)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  java.lang.String getChannel();
  /**
   * <pre>
   * 渠道编码(必传)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  com.google.protobuf.ByteString
      getChannelBytes();

  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * <code>.coupon.MemberContent member_content = 2;</code>
   * @return Whether the memberContent field is set.
   */
  boolean hasMemberContent();
  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * <code>.coupon.MemberContent member_content = 2;</code>
   * @return The memberContent.
   */
  cn.hexcloud.pbis.common.service.facade.member.MemberContent getMemberContent();
  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * <code>.coupon.MemberContent member_content = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.MemberContentOrBuilder getMemberContentOrBuilder();

  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 3;</code>
   * @return Whether the orderContent field is set.
   */
  boolean hasOrderContent();
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 3;</code>
   * @return The orderContent.
   */
  cn.hexcloud.pbis.common.service.facade.member.OrderContent getOrderContent();
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.OrderContentOrBuilder getOrderContentOrBuilder();
}
