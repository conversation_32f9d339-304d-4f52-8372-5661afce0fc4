// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

/**
 * Protobuf type {@code coupon.AvailablePointsResponse}
 */
public final class AvailablePointsResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:coupon.AvailablePointsResponse)
    AvailablePointsResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use AvailablePointsResponse.newBuilder() to construct.
  private AvailablePointsResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private AvailablePointsResponse() {
    sessId_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new AvailablePointsResponse();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private AvailablePointsResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            maxPoint_ = input.readInt32();
            break;
          }
          case 16: {

            minPoint_ = input.readInt32();
            break;
          }
          case 24: {

            ratio_ = input.readInt32();
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            sessId_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_AvailablePointsResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_AvailablePointsResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse.class, cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse.Builder.class);
  }

  public static final int MAXPOINT_FIELD_NUMBER = 1;
  private int maxPoint_;
  /**
   * <pre>
   * 按商品分类设置的折扣比例与商品金额换算的积分上限和
   * </pre>
   *
   * <code>int32 maxPoint = 1;</code>
   * @return The maxPoint.
   */
  @java.lang.Override
  public int getMaxPoint() {
    return maxPoint_;
  }

  public static final int MINPOINT_FIELD_NUMBER = 2;
  private int minPoint_;
  /**
   * <pre>
   * 商户维度的积分使用下限
   * </pre>
   *
   * <code>int32 minPoint = 2;</code>
   * @return The minPoint.
   */
  @java.lang.Override
  public int getMinPoint() {
    return minPoint_;
  }

  public static final int RATIO_FIELD_NUMBER = 3;
  private int ratio_;
  /**
   * <pre>
   * 积分抵扣比例,100积分=1元
   * </pre>
   *
   * <code>int32 ratio = 3;</code>
   * @return The ratio.
   */
  @java.lang.Override
  public int getRatio() {
    return ratio_;
  }

  public static final int SESSID_FIELD_NUMBER = 4;
  private volatile java.lang.Object sessId_;
  /**
   * <code>string sessId = 4;</code>
   * @return The sessId.
   */
  @java.lang.Override
  public java.lang.String getSessId() {
    java.lang.Object ref = sessId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      sessId_ = s;
      return s;
    }
  }
  /**
   * <code>string sessId = 4;</code>
   * @return The bytes for sessId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSessIdBytes() {
    java.lang.Object ref = sessId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      sessId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (maxPoint_ != 0) {
      output.writeInt32(1, maxPoint_);
    }
    if (minPoint_ != 0) {
      output.writeInt32(2, minPoint_);
    }
    if (ratio_ != 0) {
      output.writeInt32(3, ratio_);
    }
    if (!getSessIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, sessId_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (maxPoint_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, maxPoint_);
    }
    if (minPoint_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, minPoint_);
    }
    if (ratio_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, ratio_);
    }
    if (!getSessIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, sessId_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse other = (cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse) obj;

    if (getMaxPoint()
        != other.getMaxPoint()) return false;
    if (getMinPoint()
        != other.getMinPoint()) return false;
    if (getRatio()
        != other.getRatio()) return false;
    if (!getSessId()
        .equals(other.getSessId())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + MAXPOINT_FIELD_NUMBER;
    hash = (53 * hash) + getMaxPoint();
    hash = (37 * hash) + MINPOINT_FIELD_NUMBER;
    hash = (53 * hash) + getMinPoint();
    hash = (37 * hash) + RATIO_FIELD_NUMBER;
    hash = (53 * hash) + getRatio();
    hash = (37 * hash) + SESSID_FIELD_NUMBER;
    hash = (53 * hash) + getSessId().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code coupon.AvailablePointsResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:coupon.AvailablePointsResponse)
      cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_AvailablePointsResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_AvailablePointsResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse.class, cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      maxPoint_ = 0;

      minPoint_ = 0;

      ratio_ = 0;

      sessId_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_AvailablePointsResponse_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse build() {
      cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse buildPartial() {
      cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse result = new cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse(this);
      result.maxPoint_ = maxPoint_;
      result.minPoint_ = minPoint_;
      result.ratio_ = ratio_;
      result.sessId_ = sessId_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse other) {
      if (other == cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse.getDefaultInstance()) return this;
      if (other.getMaxPoint() != 0) {
        setMaxPoint(other.getMaxPoint());
      }
      if (other.getMinPoint() != 0) {
        setMinPoint(other.getMinPoint());
      }
      if (other.getRatio() != 0) {
        setRatio(other.getRatio());
      }
      if (!other.getSessId().isEmpty()) {
        sessId_ = other.sessId_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private int maxPoint_ ;
    /**
     * <pre>
     * 按商品分类设置的折扣比例与商品金额换算的积分上限和
     * </pre>
     *
     * <code>int32 maxPoint = 1;</code>
     * @return The maxPoint.
     */
    @java.lang.Override
    public int getMaxPoint() {
      return maxPoint_;
    }
    /**
     * <pre>
     * 按商品分类设置的折扣比例与商品金额换算的积分上限和
     * </pre>
     *
     * <code>int32 maxPoint = 1;</code>
     * @param value The maxPoint to set.
     * @return This builder for chaining.
     */
    public Builder setMaxPoint(int value) {
      
      maxPoint_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 按商品分类设置的折扣比例与商品金额换算的积分上限和
     * </pre>
     *
     * <code>int32 maxPoint = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearMaxPoint() {
      
      maxPoint_ = 0;
      onChanged();
      return this;
    }

    private int minPoint_ ;
    /**
     * <pre>
     * 商户维度的积分使用下限
     * </pre>
     *
     * <code>int32 minPoint = 2;</code>
     * @return The minPoint.
     */
    @java.lang.Override
    public int getMinPoint() {
      return minPoint_;
    }
    /**
     * <pre>
     * 商户维度的积分使用下限
     * </pre>
     *
     * <code>int32 minPoint = 2;</code>
     * @param value The minPoint to set.
     * @return This builder for chaining.
     */
    public Builder setMinPoint(int value) {
      
      minPoint_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 商户维度的积分使用下限
     * </pre>
     *
     * <code>int32 minPoint = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMinPoint() {
      
      minPoint_ = 0;
      onChanged();
      return this;
    }

    private int ratio_ ;
    /**
     * <pre>
     * 积分抵扣比例,100积分=1元
     * </pre>
     *
     * <code>int32 ratio = 3;</code>
     * @return The ratio.
     */
    @java.lang.Override
    public int getRatio() {
      return ratio_;
    }
    /**
     * <pre>
     * 积分抵扣比例,100积分=1元
     * </pre>
     *
     * <code>int32 ratio = 3;</code>
     * @param value The ratio to set.
     * @return This builder for chaining.
     */
    public Builder setRatio(int value) {
      
      ratio_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 积分抵扣比例,100积分=1元
     * </pre>
     *
     * <code>int32 ratio = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearRatio() {
      
      ratio_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object sessId_ = "";
    /**
     * <code>string sessId = 4;</code>
     * @return The sessId.
     */
    public java.lang.String getSessId() {
      java.lang.Object ref = sessId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sessId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string sessId = 4;</code>
     * @return The bytes for sessId.
     */
    public com.google.protobuf.ByteString
        getSessIdBytes() {
      java.lang.Object ref = sessId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sessId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string sessId = 4;</code>
     * @param value The sessId to set.
     * @return This builder for chaining.
     */
    public Builder setSessId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      sessId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string sessId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearSessId() {
      
      sessId_ = getDefaultInstance().getSessId();
      onChanged();
      return this;
    }
    /**
     * <code>string sessId = 4;</code>
     * @param value The bytes for sessId to set.
     * @return This builder for chaining.
     */
    public Builder setSessIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      sessId_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:coupon.AvailablePointsResponse)
  }

  // @@protoc_insertion_point(class_scope:coupon.AvailablePointsResponse)
  private static final cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse();
  }

  public static cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AvailablePointsResponse>
      PARSER = new com.google.protobuf.AbstractParser<AvailablePointsResponse>() {
    @java.lang.Override
    public AvailablePointsResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new AvailablePointsResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<AvailablePointsResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AvailablePointsResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.AvailablePointsResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

