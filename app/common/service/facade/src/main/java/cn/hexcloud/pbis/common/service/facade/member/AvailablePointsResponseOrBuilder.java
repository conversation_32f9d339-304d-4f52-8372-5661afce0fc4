// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface AvailablePointsResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.AvailablePointsResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 按商品分类设置的折扣比例与商品金额换算的积分上限和
   * </pre>
   *
   * <code>int32 maxPoint = 1;</code>
   * @return The maxPoint.
   */
  int getMaxPoint();

  /**
   * <pre>
   * 商户维度的积分使用下限
   * </pre>
   *
   * <code>int32 minPoint = 2;</code>
   * @return The minPoint.
   */
  int getMinPoint();

  /**
   * <pre>
   * 积分抵扣比例,100积分=1元
   * </pre>
   *
   * <code>int32 ratio = 3;</code>
   * @return The ratio.
   */
  int getRatio();

  /**
   * <code>string sessId = 4;</code>
   * @return The sessId.
   */
  java.lang.String getSessId();
  /**
   * <code>string sessId = 4;</code>
   * @return The bytes for sessId.
   */
  com.google.protobuf.ByteString
      getSessIdBytes();
}
