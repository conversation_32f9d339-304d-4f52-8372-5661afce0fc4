// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

/**
 * <pre>
 * 【第三方促销计算】请求
 * </pre>
 *
 * Protobuf type {@code coupon.CalculatePromotionRequest}
 */
public final class CalculatePromotionRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:coupon.CalculatePromotionRequest)
    CalculatePromotionRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use CalculatePromotionRequest.newBuilder() to construct.
  private CalculatePromotionRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private CalculatePromotionRequest() {
    storeCode_ = "";
    subTotal_ = "";
    lines_ = java.util.Collections.emptyList();
    channel_ = "";
    packageFee_ = "";
    deliveryFee_ = "";
    discs_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new CalculatePromotionRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private CalculatePromotionRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            storeCode_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            subTotal_ = s;
            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              lines_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.member.GoodsInfo>();
              mutable_bitField0_ |= 0x00000001;
            }
            lines_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.member.GoodsInfo.parser(), extensionRegistry));
            break;
          }
          case 32: {

            scene_ = input.readInt32();
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            channel_ = s;
            break;
          }
          case 48: {

            userId_ = input.readInt64();
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            packageFee_ = s;
            break;
          }
          case 66: {
            java.lang.String s = input.readStringRequireUtf8();

            deliveryFee_ = s;
            break;
          }
          case 74: {
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              discs_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.member.PromotionDetail>();
              mutable_bitField0_ |= 0x00000002;
            }
            discs_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.member.PromotionDetail.parser(), extensionRegistry));
            break;
          }
          case 80: {

            storeId_ = input.readUInt64();
            break;
          }
          case 88: {

            partnerId_ = input.readUInt64();
            break;
          }
          case 96: {

            scopeId_ = input.readUInt64();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        lines_ = java.util.Collections.unmodifiableList(lines_);
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        discs_ = java.util.Collections.unmodifiableList(discs_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_CalculatePromotionRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_CalculatePromotionRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest.class, cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest.Builder.class);
  }

  public static final int STORECODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object storeCode_;
  /**
   * <pre>
   *门店code
   * </pre>
   *
   * <code>string storeCode = 1;</code>
   * @return The storeCode.
   */
  @java.lang.Override
  public java.lang.String getStoreCode() {
    java.lang.Object ref = storeCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      storeCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *门店code
   * </pre>
   *
   * <code>string storeCode = 1;</code>
   * @return The bytes for storeCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStoreCodeBytes() {
    java.lang.Object ref = storeCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      storeCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SUBTOTAL_FIELD_NUMBER = 2;
  private volatile java.lang.Object subTotal_;
  /**
   * <pre>
   *订单总价
   * </pre>
   *
   * <code>string subTotal = 2;</code>
   * @return The subTotal.
   */
  @java.lang.Override
  public java.lang.String getSubTotal() {
    java.lang.Object ref = subTotal_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      subTotal_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *订单总价
   * </pre>
   *
   * <code>string subTotal = 2;</code>
   * @return The bytes for subTotal.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSubTotalBytes() {
    java.lang.Object ref = subTotal_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      subTotal_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LINES_FIELD_NUMBER = 3;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.member.GoodsInfo> lines_;
  /**
   * <pre>
   *商品列表
   * </pre>
   *
   * <code>repeated .coupon.GoodsInfo lines = 3;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.member.GoodsInfo> getLinesList() {
    return lines_;
  }
  /**
   * <pre>
   *商品列表
   * </pre>
   *
   * <code>repeated .coupon.GoodsInfo lines = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.GoodsInfoOrBuilder> 
      getLinesOrBuilderList() {
    return lines_;
  }
  /**
   * <pre>
   *商品列表
   * </pre>
   *
   * <code>repeated .coupon.GoodsInfo lines = 3;</code>
   */
  @java.lang.Override
  public int getLinesCount() {
    return lines_.size();
  }
  /**
   * <pre>
   *商品列表
   * </pre>
   *
   * <code>repeated .coupon.GoodsInfo lines = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.GoodsInfo getLines(int index) {
    return lines_.get(index);
  }
  /**
   * <pre>
   *商品列表
   * </pre>
   *
   * <code>repeated .coupon.GoodsInfo lines = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.GoodsInfoOrBuilder getLinesOrBuilder(
      int index) {
    return lines_.get(index);
  }

  public static final int SCENE_FIELD_NUMBER = 4;
  private int scene_;
  /**
   * <pre>
   *适用场景
   * </pre>
   *
   * <code>int32 scene = 4;</code>
   * @return The scene.
   */
  @java.lang.Override
  public int getScene() {
    return scene_;
  }

  public static final int CHANNEL_FIELD_NUMBER = 5;
  private volatile java.lang.Object channel_;
  /**
   * <pre>
   *适用渠道
   * </pre>
   *
   * <code>string channel = 5;</code>
   * @return The channel.
   */
  @java.lang.Override
  public java.lang.String getChannel() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channel_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *适用渠道
   * </pre>
   *
   * <code>string channel = 5;</code>
   * @return The bytes for channel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelBytes() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int USERID_FIELD_NUMBER = 6;
  private long userId_;
  /**
   * <pre>
   *用户id
   * </pre>
   *
   * <code>int64 userId = 6;</code>
   * @return The userId.
   */
  @java.lang.Override
  public long getUserId() {
    return userId_;
  }

  public static final int PACKAGEFEE_FIELD_NUMBER = 7;
  private volatile java.lang.Object packageFee_;
  /**
   * <pre>
   *打包费
   * </pre>
   *
   * <code>string packageFee = 7;</code>
   * @return The packageFee.
   */
  @java.lang.Override
  public java.lang.String getPackageFee() {
    java.lang.Object ref = packageFee_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      packageFee_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *打包费
   * </pre>
   *
   * <code>string packageFee = 7;</code>
   * @return The bytes for packageFee.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPackageFeeBytes() {
    java.lang.Object ref = packageFee_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      packageFee_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DELIVERYFEE_FIELD_NUMBER = 8;
  private volatile java.lang.Object deliveryFee_;
  /**
   * <pre>
   *配送费
   * </pre>
   *
   * <code>string deliveryFee = 8;</code>
   * @return The deliveryFee.
   */
  @java.lang.Override
  public java.lang.String getDeliveryFee() {
    java.lang.Object ref = deliveryFee_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deliveryFee_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *配送费
   * </pre>
   *
   * <code>string deliveryFee = 8;</code>
   * @return The bytes for deliveryFee.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDeliveryFeeBytes() {
    java.lang.Object ref = deliveryFee_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deliveryFee_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DISCS_FIELD_NUMBER = 9;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.member.PromotionDetail> discs_;
  /**
   * <pre>
   *优惠信息
   * </pre>
   *
   * <code>repeated .coupon.PromotionDetail discs = 9;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.member.PromotionDetail> getDiscsList() {
    return discs_;
  }
  /**
   * <pre>
   *优惠信息
   * </pre>
   *
   * <code>repeated .coupon.PromotionDetail discs = 9;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.PromotionDetailOrBuilder> 
      getDiscsOrBuilderList() {
    return discs_;
  }
  /**
   * <pre>
   *优惠信息
   * </pre>
   *
   * <code>repeated .coupon.PromotionDetail discs = 9;</code>
   */
  @java.lang.Override
  public int getDiscsCount() {
    return discs_.size();
  }
  /**
   * <pre>
   *优惠信息
   * </pre>
   *
   * <code>repeated .coupon.PromotionDetail discs = 9;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.PromotionDetail getDiscs(int index) {
    return discs_.get(index);
  }
  /**
   * <pre>
   *优惠信息
   * </pre>
   *
   * <code>repeated .coupon.PromotionDetail discs = 9;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.PromotionDetailOrBuilder getDiscsOrBuilder(
      int index) {
    return discs_.get(index);
  }

  public static final int STORE_ID_FIELD_NUMBER = 10;
  private long storeId_;
  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>uint64 store_id = 10;</code>
   * @return The storeId.
   */
  @java.lang.Override
  public long getStoreId() {
    return storeId_;
  }

  public static final int PARTNER_ID_FIELD_NUMBER = 11;
  private long partnerId_;
  /**
   * <pre>
   * 门店partner id
   * </pre>
   *
   * <code>uint64 partner_id = 11;</code>
   * @return The partnerId.
   */
  @java.lang.Override
  public long getPartnerId() {
    return partnerId_;
  }

  public static final int SCOPE_ID_FIELD_NUMBER = 12;
  private long scopeId_;
  /**
   * <pre>
   * 门店scope id，如果没有就传0
   * </pre>
   *
   * <code>uint64 scope_id = 12;</code>
   * @return The scopeId.
   */
  @java.lang.Override
  public long getScopeId() {
    return scopeId_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getStoreCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, storeCode_);
    }
    if (!getSubTotalBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, subTotal_);
    }
    for (int i = 0; i < lines_.size(); i++) {
      output.writeMessage(3, lines_.get(i));
    }
    if (scene_ != 0) {
      output.writeInt32(4, scene_);
    }
    if (!getChannelBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, channel_);
    }
    if (userId_ != 0L) {
      output.writeInt64(6, userId_);
    }
    if (!getPackageFeeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, packageFee_);
    }
    if (!getDeliveryFeeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, deliveryFee_);
    }
    for (int i = 0; i < discs_.size(); i++) {
      output.writeMessage(9, discs_.get(i));
    }
    if (storeId_ != 0L) {
      output.writeUInt64(10, storeId_);
    }
    if (partnerId_ != 0L) {
      output.writeUInt64(11, partnerId_);
    }
    if (scopeId_ != 0L) {
      output.writeUInt64(12, scopeId_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getStoreCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, storeCode_);
    }
    if (!getSubTotalBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, subTotal_);
    }
    for (int i = 0; i < lines_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, lines_.get(i));
    }
    if (scene_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, scene_);
    }
    if (!getChannelBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, channel_);
    }
    if (userId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(6, userId_);
    }
    if (!getPackageFeeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, packageFee_);
    }
    if (!getDeliveryFeeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, deliveryFee_);
    }
    for (int i = 0; i < discs_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(9, discs_.get(i));
    }
    if (storeId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(10, storeId_);
    }
    if (partnerId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(11, partnerId_);
    }
    if (scopeId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(12, scopeId_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest other = (cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest) obj;

    if (!getStoreCode()
        .equals(other.getStoreCode())) return false;
    if (!getSubTotal()
        .equals(other.getSubTotal())) return false;
    if (!getLinesList()
        .equals(other.getLinesList())) return false;
    if (getScene()
        != other.getScene()) return false;
    if (!getChannel()
        .equals(other.getChannel())) return false;
    if (getUserId()
        != other.getUserId()) return false;
    if (!getPackageFee()
        .equals(other.getPackageFee())) return false;
    if (!getDeliveryFee()
        .equals(other.getDeliveryFee())) return false;
    if (!getDiscsList()
        .equals(other.getDiscsList())) return false;
    if (getStoreId()
        != other.getStoreId()) return false;
    if (getPartnerId()
        != other.getPartnerId()) return false;
    if (getScopeId()
        != other.getScopeId()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + STORECODE_FIELD_NUMBER;
    hash = (53 * hash) + getStoreCode().hashCode();
    hash = (37 * hash) + SUBTOTAL_FIELD_NUMBER;
    hash = (53 * hash) + getSubTotal().hashCode();
    if (getLinesCount() > 0) {
      hash = (37 * hash) + LINES_FIELD_NUMBER;
      hash = (53 * hash) + getLinesList().hashCode();
    }
    hash = (37 * hash) + SCENE_FIELD_NUMBER;
    hash = (53 * hash) + getScene();
    hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
    hash = (53 * hash) + getChannel().hashCode();
    hash = (37 * hash) + USERID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getUserId());
    hash = (37 * hash) + PACKAGEFEE_FIELD_NUMBER;
    hash = (53 * hash) + getPackageFee().hashCode();
    hash = (37 * hash) + DELIVERYFEE_FIELD_NUMBER;
    hash = (53 * hash) + getDeliveryFee().hashCode();
    if (getDiscsCount() > 0) {
      hash = (37 * hash) + DISCS_FIELD_NUMBER;
      hash = (53 * hash) + getDiscsList().hashCode();
    }
    hash = (37 * hash) + STORE_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getStoreId());
    hash = (37 * hash) + PARTNER_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getPartnerId());
    hash = (37 * hash) + SCOPE_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getScopeId());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 【第三方促销计算】请求
   * </pre>
   *
   * Protobuf type {@code coupon.CalculatePromotionRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:coupon.CalculatePromotionRequest)
      cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_CalculatePromotionRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_CalculatePromotionRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest.class, cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getLinesFieldBuilder();
        getDiscsFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      storeCode_ = "";

      subTotal_ = "";

      if (linesBuilder_ == null) {
        lines_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        linesBuilder_.clear();
      }
      scene_ = 0;

      channel_ = "";

      userId_ = 0L;

      packageFee_ = "";

      deliveryFee_ = "";

      if (discsBuilder_ == null) {
        discs_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
      } else {
        discsBuilder_.clear();
      }
      storeId_ = 0L;

      partnerId_ = 0L;

      scopeId_ = 0L;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_CalculatePromotionRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest build() {
      cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest buildPartial() {
      cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest result = new cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest(this);
      int from_bitField0_ = bitField0_;
      result.storeCode_ = storeCode_;
      result.subTotal_ = subTotal_;
      if (linesBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          lines_ = java.util.Collections.unmodifiableList(lines_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.lines_ = lines_;
      } else {
        result.lines_ = linesBuilder_.build();
      }
      result.scene_ = scene_;
      result.channel_ = channel_;
      result.userId_ = userId_;
      result.packageFee_ = packageFee_;
      result.deliveryFee_ = deliveryFee_;
      if (discsBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          discs_ = java.util.Collections.unmodifiableList(discs_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.discs_ = discs_;
      } else {
        result.discs_ = discsBuilder_.build();
      }
      result.storeId_ = storeId_;
      result.partnerId_ = partnerId_;
      result.scopeId_ = scopeId_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest other) {
      if (other == cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest.getDefaultInstance()) return this;
      if (!other.getStoreCode().isEmpty()) {
        storeCode_ = other.storeCode_;
        onChanged();
      }
      if (!other.getSubTotal().isEmpty()) {
        subTotal_ = other.subTotal_;
        onChanged();
      }
      if (linesBuilder_ == null) {
        if (!other.lines_.isEmpty()) {
          if (lines_.isEmpty()) {
            lines_ = other.lines_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureLinesIsMutable();
            lines_.addAll(other.lines_);
          }
          onChanged();
        }
      } else {
        if (!other.lines_.isEmpty()) {
          if (linesBuilder_.isEmpty()) {
            linesBuilder_.dispose();
            linesBuilder_ = null;
            lines_ = other.lines_;
            bitField0_ = (bitField0_ & ~0x00000001);
            linesBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getLinesFieldBuilder() : null;
          } else {
            linesBuilder_.addAllMessages(other.lines_);
          }
        }
      }
      if (other.getScene() != 0) {
        setScene(other.getScene());
      }
      if (!other.getChannel().isEmpty()) {
        channel_ = other.channel_;
        onChanged();
      }
      if (other.getUserId() != 0L) {
        setUserId(other.getUserId());
      }
      if (!other.getPackageFee().isEmpty()) {
        packageFee_ = other.packageFee_;
        onChanged();
      }
      if (!other.getDeliveryFee().isEmpty()) {
        deliveryFee_ = other.deliveryFee_;
        onChanged();
      }
      if (discsBuilder_ == null) {
        if (!other.discs_.isEmpty()) {
          if (discs_.isEmpty()) {
            discs_ = other.discs_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDiscsIsMutable();
            discs_.addAll(other.discs_);
          }
          onChanged();
        }
      } else {
        if (!other.discs_.isEmpty()) {
          if (discsBuilder_.isEmpty()) {
            discsBuilder_.dispose();
            discsBuilder_ = null;
            discs_ = other.discs_;
            bitField0_ = (bitField0_ & ~0x00000002);
            discsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getDiscsFieldBuilder() : null;
          } else {
            discsBuilder_.addAllMessages(other.discs_);
          }
        }
      }
      if (other.getStoreId() != 0L) {
        setStoreId(other.getStoreId());
      }
      if (other.getPartnerId() != 0L) {
        setPartnerId(other.getPartnerId());
      }
      if (other.getScopeId() != 0L) {
        setScopeId(other.getScopeId());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object storeCode_ = "";
    /**
     * <pre>
     *门店code
     * </pre>
     *
     * <code>string storeCode = 1;</code>
     * @return The storeCode.
     */
    public java.lang.String getStoreCode() {
      java.lang.Object ref = storeCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        storeCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *门店code
     * </pre>
     *
     * <code>string storeCode = 1;</code>
     * @return The bytes for storeCode.
     */
    public com.google.protobuf.ByteString
        getStoreCodeBytes() {
      java.lang.Object ref = storeCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        storeCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *门店code
     * </pre>
     *
     * <code>string storeCode = 1;</code>
     * @param value The storeCode to set.
     * @return This builder for chaining.
     */
    public Builder setStoreCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      storeCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *门店code
     * </pre>
     *
     * <code>string storeCode = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearStoreCode() {
      
      storeCode_ = getDefaultInstance().getStoreCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *门店code
     * </pre>
     *
     * <code>string storeCode = 1;</code>
     * @param value The bytes for storeCode to set.
     * @return This builder for chaining.
     */
    public Builder setStoreCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      storeCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object subTotal_ = "";
    /**
     * <pre>
     *订单总价
     * </pre>
     *
     * <code>string subTotal = 2;</code>
     * @return The subTotal.
     */
    public java.lang.String getSubTotal() {
      java.lang.Object ref = subTotal_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        subTotal_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *订单总价
     * </pre>
     *
     * <code>string subTotal = 2;</code>
     * @return The bytes for subTotal.
     */
    public com.google.protobuf.ByteString
        getSubTotalBytes() {
      java.lang.Object ref = subTotal_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        subTotal_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *订单总价
     * </pre>
     *
     * <code>string subTotal = 2;</code>
     * @param value The subTotal to set.
     * @return This builder for chaining.
     */
    public Builder setSubTotal(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      subTotal_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单总价
     * </pre>
     *
     * <code>string subTotal = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSubTotal() {
      
      subTotal_ = getDefaultInstance().getSubTotal();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单总价
     * </pre>
     *
     * <code>string subTotal = 2;</code>
     * @param value The bytes for subTotal to set.
     * @return This builder for chaining.
     */
    public Builder setSubTotalBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      subTotal_ = value;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.member.GoodsInfo> lines_ =
      java.util.Collections.emptyList();
    private void ensureLinesIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        lines_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.member.GoodsInfo>(lines_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.GoodsInfo, cn.hexcloud.pbis.common.service.facade.member.GoodsInfo.Builder, cn.hexcloud.pbis.common.service.facade.member.GoodsInfoOrBuilder> linesBuilder_;

    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.member.GoodsInfo> getLinesList() {
      if (linesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(lines_);
      } else {
        return linesBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public int getLinesCount() {
      if (linesBuilder_ == null) {
        return lines_.size();
      } else {
        return linesBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.GoodsInfo getLines(int index) {
      if (linesBuilder_ == null) {
        return lines_.get(index);
      } else {
        return linesBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public Builder setLines(
        int index, cn.hexcloud.pbis.common.service.facade.member.GoodsInfo value) {
      if (linesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLinesIsMutable();
        lines_.set(index, value);
        onChanged();
      } else {
        linesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public Builder setLines(
        int index, cn.hexcloud.pbis.common.service.facade.member.GoodsInfo.Builder builderForValue) {
      if (linesBuilder_ == null) {
        ensureLinesIsMutable();
        lines_.set(index, builderForValue.build());
        onChanged();
      } else {
        linesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public Builder addLines(cn.hexcloud.pbis.common.service.facade.member.GoodsInfo value) {
      if (linesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLinesIsMutable();
        lines_.add(value);
        onChanged();
      } else {
        linesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public Builder addLines(
        int index, cn.hexcloud.pbis.common.service.facade.member.GoodsInfo value) {
      if (linesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureLinesIsMutable();
        lines_.add(index, value);
        onChanged();
      } else {
        linesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public Builder addLines(
        cn.hexcloud.pbis.common.service.facade.member.GoodsInfo.Builder builderForValue) {
      if (linesBuilder_ == null) {
        ensureLinesIsMutable();
        lines_.add(builderForValue.build());
        onChanged();
      } else {
        linesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public Builder addLines(
        int index, cn.hexcloud.pbis.common.service.facade.member.GoodsInfo.Builder builderForValue) {
      if (linesBuilder_ == null) {
        ensureLinesIsMutable();
        lines_.add(index, builderForValue.build());
        onChanged();
      } else {
        linesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public Builder addAllLines(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.member.GoodsInfo> values) {
      if (linesBuilder_ == null) {
        ensureLinesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, lines_);
        onChanged();
      } else {
        linesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public Builder clearLines() {
      if (linesBuilder_ == null) {
        lines_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        linesBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public Builder removeLines(int index) {
      if (linesBuilder_ == null) {
        ensureLinesIsMutable();
        lines_.remove(index);
        onChanged();
      } else {
        linesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.GoodsInfo.Builder getLinesBuilder(
        int index) {
      return getLinesFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.GoodsInfoOrBuilder getLinesOrBuilder(
        int index) {
      if (linesBuilder_ == null) {
        return lines_.get(index);  } else {
        return linesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.GoodsInfoOrBuilder> 
         getLinesOrBuilderList() {
      if (linesBuilder_ != null) {
        return linesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(lines_);
      }
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.GoodsInfo.Builder addLinesBuilder() {
      return getLinesFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.member.GoodsInfo.getDefaultInstance());
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.GoodsInfo.Builder addLinesBuilder(
        int index) {
      return getLinesFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.member.GoodsInfo.getDefaultInstance());
    }
    /**
     * <pre>
     *商品列表
     * </pre>
     *
     * <code>repeated .coupon.GoodsInfo lines = 3;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.member.GoodsInfo.Builder> 
         getLinesBuilderList() {
      return getLinesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.GoodsInfo, cn.hexcloud.pbis.common.service.facade.member.GoodsInfo.Builder, cn.hexcloud.pbis.common.service.facade.member.GoodsInfoOrBuilder> 
        getLinesFieldBuilder() {
      if (linesBuilder_ == null) {
        linesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.member.GoodsInfo, cn.hexcloud.pbis.common.service.facade.member.GoodsInfo.Builder, cn.hexcloud.pbis.common.service.facade.member.GoodsInfoOrBuilder>(
                lines_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        lines_ = null;
      }
      return linesBuilder_;
    }

    private int scene_ ;
    /**
     * <pre>
     *适用场景
     * </pre>
     *
     * <code>int32 scene = 4;</code>
     * @return The scene.
     */
    @java.lang.Override
    public int getScene() {
      return scene_;
    }
    /**
     * <pre>
     *适用场景
     * </pre>
     *
     * <code>int32 scene = 4;</code>
     * @param value The scene to set.
     * @return This builder for chaining.
     */
    public Builder setScene(int value) {
      
      scene_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *适用场景
     * </pre>
     *
     * <code>int32 scene = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearScene() {
      
      scene_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object channel_ = "";
    /**
     * <pre>
     *适用渠道
     * </pre>
     *
     * <code>string channel = 5;</code>
     * @return The channel.
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channel_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *适用渠道
     * </pre>
     *
     * <code>string channel = 5;</code>
     * @return The bytes for channel.
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *适用渠道
     * </pre>
     *
     * <code>string channel = 5;</code>
     * @param value The channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannel(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channel_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *适用渠道
     * </pre>
     *
     * <code>string channel = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannel() {
      
      channel_ = getDefaultInstance().getChannel();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *适用渠道
     * </pre>
     *
     * <code>string channel = 5;</code>
     * @param value The bytes for channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channel_ = value;
      onChanged();
      return this;
    }

    private long userId_ ;
    /**
     * <pre>
     *用户id
     * </pre>
     *
     * <code>int64 userId = 6;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }
    /**
     * <pre>
     *用户id
     * </pre>
     *
     * <code>int64 userId = 6;</code>
     * @param value The userId to set.
     * @return This builder for chaining.
     */
    public Builder setUserId(long value) {
      
      userId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *用户id
     * </pre>
     *
     * <code>int64 userId = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearUserId() {
      
      userId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object packageFee_ = "";
    /**
     * <pre>
     *打包费
     * </pre>
     *
     * <code>string packageFee = 7;</code>
     * @return The packageFee.
     */
    public java.lang.String getPackageFee() {
      java.lang.Object ref = packageFee_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        packageFee_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *打包费
     * </pre>
     *
     * <code>string packageFee = 7;</code>
     * @return The bytes for packageFee.
     */
    public com.google.protobuf.ByteString
        getPackageFeeBytes() {
      java.lang.Object ref = packageFee_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        packageFee_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *打包费
     * </pre>
     *
     * <code>string packageFee = 7;</code>
     * @param value The packageFee to set.
     * @return This builder for chaining.
     */
    public Builder setPackageFee(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      packageFee_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *打包费
     * </pre>
     *
     * <code>string packageFee = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearPackageFee() {
      
      packageFee_ = getDefaultInstance().getPackageFee();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *打包费
     * </pre>
     *
     * <code>string packageFee = 7;</code>
     * @param value The bytes for packageFee to set.
     * @return This builder for chaining.
     */
    public Builder setPackageFeeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      packageFee_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object deliveryFee_ = "";
    /**
     * <pre>
     *配送费
     * </pre>
     *
     * <code>string deliveryFee = 8;</code>
     * @return The deliveryFee.
     */
    public java.lang.String getDeliveryFee() {
      java.lang.Object ref = deliveryFee_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deliveryFee_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *配送费
     * </pre>
     *
     * <code>string deliveryFee = 8;</code>
     * @return The bytes for deliveryFee.
     */
    public com.google.protobuf.ByteString
        getDeliveryFeeBytes() {
      java.lang.Object ref = deliveryFee_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deliveryFee_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *配送费
     * </pre>
     *
     * <code>string deliveryFee = 8;</code>
     * @param value The deliveryFee to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryFee(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      deliveryFee_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *配送费
     * </pre>
     *
     * <code>string deliveryFee = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeliveryFee() {
      
      deliveryFee_ = getDefaultInstance().getDeliveryFee();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *配送费
     * </pre>
     *
     * <code>string deliveryFee = 8;</code>
     * @param value The bytes for deliveryFee to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryFeeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      deliveryFee_ = value;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.member.PromotionDetail> discs_ =
      java.util.Collections.emptyList();
    private void ensureDiscsIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        discs_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.member.PromotionDetail>(discs_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.PromotionDetail, cn.hexcloud.pbis.common.service.facade.member.PromotionDetail.Builder, cn.hexcloud.pbis.common.service.facade.member.PromotionDetailOrBuilder> discsBuilder_;

    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.member.PromotionDetail> getDiscsList() {
      if (discsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(discs_);
      } else {
        return discsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public int getDiscsCount() {
      if (discsBuilder_ == null) {
        return discs_.size();
      } else {
        return discsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.PromotionDetail getDiscs(int index) {
      if (discsBuilder_ == null) {
        return discs_.get(index);
      } else {
        return discsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public Builder setDiscs(
        int index, cn.hexcloud.pbis.common.service.facade.member.PromotionDetail value) {
      if (discsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDiscsIsMutable();
        discs_.set(index, value);
        onChanged();
      } else {
        discsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public Builder setDiscs(
        int index, cn.hexcloud.pbis.common.service.facade.member.PromotionDetail.Builder builderForValue) {
      if (discsBuilder_ == null) {
        ensureDiscsIsMutable();
        discs_.set(index, builderForValue.build());
        onChanged();
      } else {
        discsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public Builder addDiscs(cn.hexcloud.pbis.common.service.facade.member.PromotionDetail value) {
      if (discsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDiscsIsMutable();
        discs_.add(value);
        onChanged();
      } else {
        discsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public Builder addDiscs(
        int index, cn.hexcloud.pbis.common.service.facade.member.PromotionDetail value) {
      if (discsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDiscsIsMutable();
        discs_.add(index, value);
        onChanged();
      } else {
        discsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public Builder addDiscs(
        cn.hexcloud.pbis.common.service.facade.member.PromotionDetail.Builder builderForValue) {
      if (discsBuilder_ == null) {
        ensureDiscsIsMutable();
        discs_.add(builderForValue.build());
        onChanged();
      } else {
        discsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public Builder addDiscs(
        int index, cn.hexcloud.pbis.common.service.facade.member.PromotionDetail.Builder builderForValue) {
      if (discsBuilder_ == null) {
        ensureDiscsIsMutable();
        discs_.add(index, builderForValue.build());
        onChanged();
      } else {
        discsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public Builder addAllDiscs(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.member.PromotionDetail> values) {
      if (discsBuilder_ == null) {
        ensureDiscsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, discs_);
        onChanged();
      } else {
        discsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public Builder clearDiscs() {
      if (discsBuilder_ == null) {
        discs_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        discsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public Builder removeDiscs(int index) {
      if (discsBuilder_ == null) {
        ensureDiscsIsMutable();
        discs_.remove(index);
        onChanged();
      } else {
        discsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.PromotionDetail.Builder getDiscsBuilder(
        int index) {
      return getDiscsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.PromotionDetailOrBuilder getDiscsOrBuilder(
        int index) {
      if (discsBuilder_ == null) {
        return discs_.get(index);  } else {
        return discsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.PromotionDetailOrBuilder> 
         getDiscsOrBuilderList() {
      if (discsBuilder_ != null) {
        return discsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(discs_);
      }
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.PromotionDetail.Builder addDiscsBuilder() {
      return getDiscsFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.member.PromotionDetail.getDefaultInstance());
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.PromotionDetail.Builder addDiscsBuilder(
        int index) {
      return getDiscsFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.member.PromotionDetail.getDefaultInstance());
    }
    /**
     * <pre>
     *优惠信息
     * </pre>
     *
     * <code>repeated .coupon.PromotionDetail discs = 9;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.member.PromotionDetail.Builder> 
         getDiscsBuilderList() {
      return getDiscsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.PromotionDetail, cn.hexcloud.pbis.common.service.facade.member.PromotionDetail.Builder, cn.hexcloud.pbis.common.service.facade.member.PromotionDetailOrBuilder> 
        getDiscsFieldBuilder() {
      if (discsBuilder_ == null) {
        discsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.member.PromotionDetail, cn.hexcloud.pbis.common.service.facade.member.PromotionDetail.Builder, cn.hexcloud.pbis.common.service.facade.member.PromotionDetailOrBuilder>(
                discs_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        discs_ = null;
      }
      return discsBuilder_;
    }

    private long storeId_ ;
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>uint64 store_id = 10;</code>
     * @return The storeId.
     */
    @java.lang.Override
    public long getStoreId() {
      return storeId_;
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>uint64 store_id = 10;</code>
     * @param value The storeId to set.
     * @return This builder for chaining.
     */
    public Builder setStoreId(long value) {
      
      storeId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>uint64 store_id = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearStoreId() {
      
      storeId_ = 0L;
      onChanged();
      return this;
    }

    private long partnerId_ ;
    /**
     * <pre>
     * 门店partner id
     * </pre>
     *
     * <code>uint64 partner_id = 11;</code>
     * @return The partnerId.
     */
    @java.lang.Override
    public long getPartnerId() {
      return partnerId_;
    }
    /**
     * <pre>
     * 门店partner id
     * </pre>
     *
     * <code>uint64 partner_id = 11;</code>
     * @param value The partnerId to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerId(long value) {
      
      partnerId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店partner id
     * </pre>
     *
     * <code>uint64 partner_id = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearPartnerId() {
      
      partnerId_ = 0L;
      onChanged();
      return this;
    }

    private long scopeId_ ;
    /**
     * <pre>
     * 门店scope id，如果没有就传0
     * </pre>
     *
     * <code>uint64 scope_id = 12;</code>
     * @return The scopeId.
     */
    @java.lang.Override
    public long getScopeId() {
      return scopeId_;
    }
    /**
     * <pre>
     * 门店scope id，如果没有就传0
     * </pre>
     *
     * <code>uint64 scope_id = 12;</code>
     * @param value The scopeId to set.
     * @return This builder for chaining.
     */
    public Builder setScopeId(long value) {
      
      scopeId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店scope id，如果没有就传0
     * </pre>
     *
     * <code>uint64 scope_id = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearScopeId() {
      
      scopeId_ = 0L;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:coupon.CalculatePromotionRequest)
  }

  // @@protoc_insertion_point(class_scope:coupon.CalculatePromotionRequest)
  private static final cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest();
  }

  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CalculatePromotionRequest>
      PARSER = new com.google.protobuf.AbstractParser<CalculatePromotionRequest>() {
    @java.lang.Override
    public CalculatePromotionRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new CalculatePromotionRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<CalculatePromotionRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CalculatePromotionRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

