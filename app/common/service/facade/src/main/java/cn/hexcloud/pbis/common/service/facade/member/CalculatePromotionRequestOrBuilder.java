// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface CalculatePromotionRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.CalculatePromotionRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *门店code
   * </pre>
   *
   * <code>string storeCode = 1;</code>
   * @return The storeCode.
   */
  java.lang.String getStoreCode();
  /**
   * <pre>
   *门店code
   * </pre>
   *
   * <code>string storeCode = 1;</code>
   * @return The bytes for storeCode.
   */
  com.google.protobuf.ByteString
      getStoreCodeBytes();

  /**
   * <pre>
   *订单总价
   * </pre>
   *
   * <code>string subTotal = 2;</code>
   * @return The subTotal.
   */
  java.lang.String getSubTotal();
  /**
   * <pre>
   *订单总价
   * </pre>
   *
   * <code>string subTotal = 2;</code>
   * @return The bytes for subTotal.
   */
  com.google.protobuf.ByteString
      getSubTotalBytes();

  /**
   * <pre>
   *商品列表
   * </pre>
   *
   * <code>repeated .coupon.GoodsInfo lines = 3;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.member.GoodsInfo> 
      getLinesList();
  /**
   * <pre>
   *商品列表
   * </pre>
   *
   * <code>repeated .coupon.GoodsInfo lines = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.GoodsInfo getLines(int index);
  /**
   * <pre>
   *商品列表
   * </pre>
   *
   * <code>repeated .coupon.GoodsInfo lines = 3;</code>
   */
  int getLinesCount();
  /**
   * <pre>
   *商品列表
   * </pre>
   *
   * <code>repeated .coupon.GoodsInfo lines = 3;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.GoodsInfoOrBuilder> 
      getLinesOrBuilderList();
  /**
   * <pre>
   *商品列表
   * </pre>
   *
   * <code>repeated .coupon.GoodsInfo lines = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.GoodsInfoOrBuilder getLinesOrBuilder(
      int index);

  /**
   * <pre>
   *适用场景
   * </pre>
   *
   * <code>int32 scene = 4;</code>
   * @return The scene.
   */
  int getScene();

  /**
   * <pre>
   *适用渠道
   * </pre>
   *
   * <code>string channel = 5;</code>
   * @return The channel.
   */
  java.lang.String getChannel();
  /**
   * <pre>
   *适用渠道
   * </pre>
   *
   * <code>string channel = 5;</code>
   * @return The bytes for channel.
   */
  com.google.protobuf.ByteString
      getChannelBytes();

  /**
   * <pre>
   *用户id
   * </pre>
   *
   * <code>int64 userId = 6;</code>
   * @return The userId.
   */
  long getUserId();

  /**
   * <pre>
   *打包费
   * </pre>
   *
   * <code>string packageFee = 7;</code>
   * @return The packageFee.
   */
  java.lang.String getPackageFee();
  /**
   * <pre>
   *打包费
   * </pre>
   *
   * <code>string packageFee = 7;</code>
   * @return The bytes for packageFee.
   */
  com.google.protobuf.ByteString
      getPackageFeeBytes();

  /**
   * <pre>
   *配送费
   * </pre>
   *
   * <code>string deliveryFee = 8;</code>
   * @return The deliveryFee.
   */
  java.lang.String getDeliveryFee();
  /**
   * <pre>
   *配送费
   * </pre>
   *
   * <code>string deliveryFee = 8;</code>
   * @return The bytes for deliveryFee.
   */
  com.google.protobuf.ByteString
      getDeliveryFeeBytes();

  /**
   * <pre>
   *优惠信息
   * </pre>
   *
   * <code>repeated .coupon.PromotionDetail discs = 9;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.member.PromotionDetail> 
      getDiscsList();
  /**
   * <pre>
   *优惠信息
   * </pre>
   *
   * <code>repeated .coupon.PromotionDetail discs = 9;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.PromotionDetail getDiscs(int index);
  /**
   * <pre>
   *优惠信息
   * </pre>
   *
   * <code>repeated .coupon.PromotionDetail discs = 9;</code>
   */
  int getDiscsCount();
  /**
   * <pre>
   *优惠信息
   * </pre>
   *
   * <code>repeated .coupon.PromotionDetail discs = 9;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.PromotionDetailOrBuilder> 
      getDiscsOrBuilderList();
  /**
   * <pre>
   *优惠信息
   * </pre>
   *
   * <code>repeated .coupon.PromotionDetail discs = 9;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.PromotionDetailOrBuilder getDiscsOrBuilder(
      int index);

  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>uint64 store_id = 10;</code>
   * @return The storeId.
   */
  long getStoreId();

  /**
   * <pre>
   * 门店partner id
   * </pre>
   *
   * <code>uint64 partner_id = 11;</code>
   * @return The partnerId.
   */
  long getPartnerId();

  /**
   * <pre>
   * 门店scope id，如果没有就传0
   * </pre>
   *
   * <code>uint64 scope_id = 12;</code>
   * @return The scopeId.
   */
  long getScopeId();
}
