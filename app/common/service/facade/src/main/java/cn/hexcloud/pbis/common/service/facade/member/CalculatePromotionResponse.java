// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

/**
 * <pre>
 * 【第三方促销计算】结果
 * </pre>
 *
 * Protobuf type {@code coupon.CalculatePromotionResponse}
 */
public final class CalculatePromotionResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:coupon.CalculatePromotionResponse)
    CalculatePromotionResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use CalculatePromotionResponse.newBuilder() to construct.
  private CalculatePromotionResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private CalculatePromotionResponse() {
    discount_ = java.util.Collections.emptyList();
    channel_ = "";
    errorCode_ = "";
    message_ = "";
    responseCode_ = "";
    responseContent_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new CalculatePromotionResponse();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private CalculatePromotionResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 18: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              discount_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.member.PriceDiscount>();
              mutable_bitField0_ |= 0x00000001;
            }
            discount_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.member.PriceDiscount.parser(), extensionRegistry));
            break;
          }
          case 26: {
            cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount.Builder subBuilder = null;
            if (summary_ != null) {
              subBuilder = summary_.toBuilder();
            }
            summary_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(summary_);
              summary_ = subBuilder.buildPartial();
            }

            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            channel_ = s;
            break;
          }
          case 40: {

            success_ = input.readBool();
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            errorCode_ = s;
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            message_ = s;
            break;
          }
          case 66: {
            java.lang.String s = input.readStringRequireUtf8();

            responseCode_ = s;
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            responseContent_ = s;
            break;
          }
          case 82: {
            cn.hexcloud.pbis.common.service.facade.member.PackageFee.Builder subBuilder = null;
            if (packageFee_ != null) {
              subBuilder = packageFee_.toBuilder();
            }
            packageFee_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.member.PackageFee.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(packageFee_);
              packageFee_ = subBuilder.buildPartial();
            }

            break;
          }
          case 90: {
            cn.hexcloud.pbis.common.service.facade.member.DeliveryFee.Builder subBuilder = null;
            if (deliveryFee_ != null) {
              subBuilder = deliveryFee_.toBuilder();
            }
            deliveryFee_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.member.DeliveryFee.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(deliveryFee_);
              deliveryFee_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        discount_ = java.util.Collections.unmodifiableList(discount_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_CalculatePromotionResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_CalculatePromotionResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse.class, cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse.Builder.class);
  }

  public static final int DISCOUNT_FIELD_NUMBER = 2;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.member.PriceDiscount> discount_;
  /**
   * <pre>
   * 商品折扣信息
   * </pre>
   *
   * <code>repeated .coupon.PriceDiscount discount = 2;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.member.PriceDiscount> getDiscountList() {
    return discount_;
  }
  /**
   * <pre>
   * 商品折扣信息
   * </pre>
   *
   * <code>repeated .coupon.PriceDiscount discount = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.PriceDiscountOrBuilder> 
      getDiscountOrBuilderList() {
    return discount_;
  }
  /**
   * <pre>
   * 商品折扣信息
   * </pre>
   *
   * <code>repeated .coupon.PriceDiscount discount = 2;</code>
   */
  @java.lang.Override
  public int getDiscountCount() {
    return discount_.size();
  }
  /**
   * <pre>
   * 商品折扣信息
   * </pre>
   *
   * <code>repeated .coupon.PriceDiscount discount = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.PriceDiscount getDiscount(int index) {
    return discount_.get(index);
  }
  /**
   * <pre>
   * 商品折扣信息
   * </pre>
   *
   * <code>repeated .coupon.PriceDiscount discount = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.PriceDiscountOrBuilder getDiscountOrBuilder(
      int index) {
    return discount_.get(index);
  }

  public static final int SUMMARY_FIELD_NUMBER = 3;
  private cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount summary_;
  /**
   * <pre>
   * 折扣信息汇总
   * </pre>
   *
   * <code>.coupon.SummaryDiscount summary = 3;</code>
   * @return Whether the summary field is set.
   */
  @java.lang.Override
  public boolean hasSummary() {
    return summary_ != null;
  }
  /**
   * <pre>
   * 折扣信息汇总
   * </pre>
   *
   * <code>.coupon.SummaryDiscount summary = 3;</code>
   * @return The summary.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount getSummary() {
    return summary_ == null ? cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount.getDefaultInstance() : summary_;
  }
  /**
   * <pre>
   * 折扣信息汇总
   * </pre>
   *
   * <code>.coupon.SummaryDiscount summary = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.SummaryDiscountOrBuilder getSummaryOrBuilder() {
    return getSummary();
  }

  public static final int CHANNEL_FIELD_NUMBER = 4;
  private volatile java.lang.Object channel_;
  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel = 4;</code>
   * @return The channel.
   */
  @java.lang.Override
  public java.lang.String getChannel() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channel_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel = 4;</code>
   * @return The bytes for channel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelBytes() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SUCCESS_FIELD_NUMBER = 5;
  private boolean success_;
  /**
   * <pre>
   * 是否验证成功
   * </pre>
   *
   * <code>bool success = 5;</code>
   * @return The success.
   */
  @java.lang.Override
  public boolean getSuccess() {
    return success_;
  }

  public static final int ERROR_CODE_FIELD_NUMBER = 6;
  private volatile java.lang.Object errorCode_;
  /**
   * <pre>
   * 异常编码，查看交易接口的error_code
   * </pre>
   *
   * <code>string error_code = 6;</code>
   * @return The errorCode.
   */
  @java.lang.Override
  public java.lang.String getErrorCode() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 异常编码，查看交易接口的error_code
   * </pre>
   *
   * <code>string error_code = 6;</code>
   * @return The bytes for errorCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorCodeBytes() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MESSAGE_FIELD_NUMBER = 7;
  private volatile java.lang.Object message_;
  /**
   * <pre>
   * 异常信息
   * </pre>
   *
   * <code>string message = 7;</code>
   * @return The message.
   */
  @java.lang.Override
  public java.lang.String getMessage() {
    java.lang.Object ref = message_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      message_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 异常信息
   * </pre>
   *
   * <code>string message = 7;</code>
   * @return The bytes for message.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMessageBytes() {
    java.lang.Object ref = message_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      message_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RESPONSE_CODE_FIELD_NUMBER = 8;
  private volatile java.lang.Object responseCode_;
  /**
   * <pre>
   * 第三方编码
   * </pre>
   *
   * <code>string response_code = 8;</code>
   * @return The responseCode.
   */
  @java.lang.Override
  public java.lang.String getResponseCode() {
    java.lang.Object ref = responseCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      responseCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 第三方编码
   * </pre>
   *
   * <code>string response_code = 8;</code>
   * @return The bytes for responseCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getResponseCodeBytes() {
    java.lang.Object ref = responseCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      responseCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RESPONSE_CONTENT_FIELD_NUMBER = 9;
  private volatile java.lang.Object responseContent_;
  /**
   * <pre>
   * 第三方报文(500字符以内)
   * </pre>
   *
   * <code>string response_content = 9;</code>
   * @return The responseContent.
   */
  @java.lang.Override
  public java.lang.String getResponseContent() {
    java.lang.Object ref = responseContent_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      responseContent_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 第三方报文(500字符以内)
   * </pre>
   *
   * <code>string response_content = 9;</code>
   * @return The bytes for responseContent.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getResponseContentBytes() {
    java.lang.Object ref = responseContent_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      responseContent_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PACKAGEFEE_FIELD_NUMBER = 10;
  private cn.hexcloud.pbis.common.service.facade.member.PackageFee packageFee_;
  /**
   * <pre>
   * 打包费信息
   * </pre>
   *
   * <code>.coupon.PackageFee packageFee = 10;</code>
   * @return Whether the packageFee field is set.
   */
  @java.lang.Override
  public boolean hasPackageFee() {
    return packageFee_ != null;
  }
  /**
   * <pre>
   * 打包费信息
   * </pre>
   *
   * <code>.coupon.PackageFee packageFee = 10;</code>
   * @return The packageFee.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.PackageFee getPackageFee() {
    return packageFee_ == null ? cn.hexcloud.pbis.common.service.facade.member.PackageFee.getDefaultInstance() : packageFee_;
  }
  /**
   * <pre>
   * 打包费信息
   * </pre>
   *
   * <code>.coupon.PackageFee packageFee = 10;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.PackageFeeOrBuilder getPackageFeeOrBuilder() {
    return getPackageFee();
  }

  public static final int DELIVERYFEE_FIELD_NUMBER = 11;
  private cn.hexcloud.pbis.common.service.facade.member.DeliveryFee deliveryFee_;
  /**
   * <pre>
   * 配送费信息
   * </pre>
   *
   * <code>.coupon.DeliveryFee deliveryFee = 11;</code>
   * @return Whether the deliveryFee field is set.
   */
  @java.lang.Override
  public boolean hasDeliveryFee() {
    return deliveryFee_ != null;
  }
  /**
   * <pre>
   * 配送费信息
   * </pre>
   *
   * <code>.coupon.DeliveryFee deliveryFee = 11;</code>
   * @return The deliveryFee.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.DeliveryFee getDeliveryFee() {
    return deliveryFee_ == null ? cn.hexcloud.pbis.common.service.facade.member.DeliveryFee.getDefaultInstance() : deliveryFee_;
  }
  /**
   * <pre>
   * 配送费信息
   * </pre>
   *
   * <code>.coupon.DeliveryFee deliveryFee = 11;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.DeliveryFeeOrBuilder getDeliveryFeeOrBuilder() {
    return getDeliveryFee();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < discount_.size(); i++) {
      output.writeMessage(2, discount_.get(i));
    }
    if (summary_ != null) {
      output.writeMessage(3, getSummary());
    }
    if (!getChannelBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, channel_);
    }
    if (success_ != false) {
      output.writeBool(5, success_);
    }
    if (!getErrorCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, errorCode_);
    }
    if (!getMessageBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, message_);
    }
    if (!getResponseCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, responseCode_);
    }
    if (!getResponseContentBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, responseContent_);
    }
    if (packageFee_ != null) {
      output.writeMessage(10, getPackageFee());
    }
    if (deliveryFee_ != null) {
      output.writeMessage(11, getDeliveryFee());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < discount_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, discount_.get(i));
    }
    if (summary_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getSummary());
    }
    if (!getChannelBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, channel_);
    }
    if (success_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(5, success_);
    }
    if (!getErrorCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, errorCode_);
    }
    if (!getMessageBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, message_);
    }
    if (!getResponseCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, responseCode_);
    }
    if (!getResponseContentBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, responseContent_);
    }
    if (packageFee_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(10, getPackageFee());
    }
    if (deliveryFee_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(11, getDeliveryFee());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse other = (cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse) obj;

    if (!getDiscountList()
        .equals(other.getDiscountList())) return false;
    if (hasSummary() != other.hasSummary()) return false;
    if (hasSummary()) {
      if (!getSummary()
          .equals(other.getSummary())) return false;
    }
    if (!getChannel()
        .equals(other.getChannel())) return false;
    if (getSuccess()
        != other.getSuccess()) return false;
    if (!getErrorCode()
        .equals(other.getErrorCode())) return false;
    if (!getMessage()
        .equals(other.getMessage())) return false;
    if (!getResponseCode()
        .equals(other.getResponseCode())) return false;
    if (!getResponseContent()
        .equals(other.getResponseContent())) return false;
    if (hasPackageFee() != other.hasPackageFee()) return false;
    if (hasPackageFee()) {
      if (!getPackageFee()
          .equals(other.getPackageFee())) return false;
    }
    if (hasDeliveryFee() != other.hasDeliveryFee()) return false;
    if (hasDeliveryFee()) {
      if (!getDeliveryFee()
          .equals(other.getDeliveryFee())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getDiscountCount() > 0) {
      hash = (37 * hash) + DISCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getDiscountList().hashCode();
    }
    if (hasSummary()) {
      hash = (37 * hash) + SUMMARY_FIELD_NUMBER;
      hash = (53 * hash) + getSummary().hashCode();
    }
    hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
    hash = (53 * hash) + getChannel().hashCode();
    hash = (37 * hash) + SUCCESS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getSuccess());
    hash = (37 * hash) + ERROR_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorCode().hashCode();
    hash = (37 * hash) + MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getMessage().hashCode();
    hash = (37 * hash) + RESPONSE_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getResponseCode().hashCode();
    hash = (37 * hash) + RESPONSE_CONTENT_FIELD_NUMBER;
    hash = (53 * hash) + getResponseContent().hashCode();
    if (hasPackageFee()) {
      hash = (37 * hash) + PACKAGEFEE_FIELD_NUMBER;
      hash = (53 * hash) + getPackageFee().hashCode();
    }
    if (hasDeliveryFee()) {
      hash = (37 * hash) + DELIVERYFEE_FIELD_NUMBER;
      hash = (53 * hash) + getDeliveryFee().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 【第三方促销计算】结果
   * </pre>
   *
   * Protobuf type {@code coupon.CalculatePromotionResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:coupon.CalculatePromotionResponse)
      cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_CalculatePromotionResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_CalculatePromotionResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse.class, cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getDiscountFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (discountBuilder_ == null) {
        discount_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        discountBuilder_.clear();
      }
      if (summaryBuilder_ == null) {
        summary_ = null;
      } else {
        summary_ = null;
        summaryBuilder_ = null;
      }
      channel_ = "";

      success_ = false;

      errorCode_ = "";

      message_ = "";

      responseCode_ = "";

      responseContent_ = "";

      if (packageFeeBuilder_ == null) {
        packageFee_ = null;
      } else {
        packageFee_ = null;
        packageFeeBuilder_ = null;
      }
      if (deliveryFeeBuilder_ == null) {
        deliveryFee_ = null;
      } else {
        deliveryFee_ = null;
        deliveryFeeBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_CalculatePromotionResponse_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse build() {
      cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse buildPartial() {
      cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse result = new cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse(this);
      int from_bitField0_ = bitField0_;
      if (discountBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          discount_ = java.util.Collections.unmodifiableList(discount_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.discount_ = discount_;
      } else {
        result.discount_ = discountBuilder_.build();
      }
      if (summaryBuilder_ == null) {
        result.summary_ = summary_;
      } else {
        result.summary_ = summaryBuilder_.build();
      }
      result.channel_ = channel_;
      result.success_ = success_;
      result.errorCode_ = errorCode_;
      result.message_ = message_;
      result.responseCode_ = responseCode_;
      result.responseContent_ = responseContent_;
      if (packageFeeBuilder_ == null) {
        result.packageFee_ = packageFee_;
      } else {
        result.packageFee_ = packageFeeBuilder_.build();
      }
      if (deliveryFeeBuilder_ == null) {
        result.deliveryFee_ = deliveryFee_;
      } else {
        result.deliveryFee_ = deliveryFeeBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse other) {
      if (other == cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse.getDefaultInstance()) return this;
      if (discountBuilder_ == null) {
        if (!other.discount_.isEmpty()) {
          if (discount_.isEmpty()) {
            discount_ = other.discount_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureDiscountIsMutable();
            discount_.addAll(other.discount_);
          }
          onChanged();
        }
      } else {
        if (!other.discount_.isEmpty()) {
          if (discountBuilder_.isEmpty()) {
            discountBuilder_.dispose();
            discountBuilder_ = null;
            discount_ = other.discount_;
            bitField0_ = (bitField0_ & ~0x00000001);
            discountBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getDiscountFieldBuilder() : null;
          } else {
            discountBuilder_.addAllMessages(other.discount_);
          }
        }
      }
      if (other.hasSummary()) {
        mergeSummary(other.getSummary());
      }
      if (!other.getChannel().isEmpty()) {
        channel_ = other.channel_;
        onChanged();
      }
      if (other.getSuccess() != false) {
        setSuccess(other.getSuccess());
      }
      if (!other.getErrorCode().isEmpty()) {
        errorCode_ = other.errorCode_;
        onChanged();
      }
      if (!other.getMessage().isEmpty()) {
        message_ = other.message_;
        onChanged();
      }
      if (!other.getResponseCode().isEmpty()) {
        responseCode_ = other.responseCode_;
        onChanged();
      }
      if (!other.getResponseContent().isEmpty()) {
        responseContent_ = other.responseContent_;
        onChanged();
      }
      if (other.hasPackageFee()) {
        mergePackageFee(other.getPackageFee());
      }
      if (other.hasDeliveryFee()) {
        mergeDeliveryFee(other.getDeliveryFee());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.util.List<cn.hexcloud.pbis.common.service.facade.member.PriceDiscount> discount_ =
      java.util.Collections.emptyList();
    private void ensureDiscountIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        discount_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.member.PriceDiscount>(discount_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.PriceDiscount, cn.hexcloud.pbis.common.service.facade.member.PriceDiscount.Builder, cn.hexcloud.pbis.common.service.facade.member.PriceDiscountOrBuilder> discountBuilder_;

    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.member.PriceDiscount> getDiscountList() {
      if (discountBuilder_ == null) {
        return java.util.Collections.unmodifiableList(discount_);
      } else {
        return discountBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public int getDiscountCount() {
      if (discountBuilder_ == null) {
        return discount_.size();
      } else {
        return discountBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.PriceDiscount getDiscount(int index) {
      if (discountBuilder_ == null) {
        return discount_.get(index);
      } else {
        return discountBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public Builder setDiscount(
        int index, cn.hexcloud.pbis.common.service.facade.member.PriceDiscount value) {
      if (discountBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDiscountIsMutable();
        discount_.set(index, value);
        onChanged();
      } else {
        discountBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public Builder setDiscount(
        int index, cn.hexcloud.pbis.common.service.facade.member.PriceDiscount.Builder builderForValue) {
      if (discountBuilder_ == null) {
        ensureDiscountIsMutable();
        discount_.set(index, builderForValue.build());
        onChanged();
      } else {
        discountBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public Builder addDiscount(cn.hexcloud.pbis.common.service.facade.member.PriceDiscount value) {
      if (discountBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDiscountIsMutable();
        discount_.add(value);
        onChanged();
      } else {
        discountBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public Builder addDiscount(
        int index, cn.hexcloud.pbis.common.service.facade.member.PriceDiscount value) {
      if (discountBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDiscountIsMutable();
        discount_.add(index, value);
        onChanged();
      } else {
        discountBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public Builder addDiscount(
        cn.hexcloud.pbis.common.service.facade.member.PriceDiscount.Builder builderForValue) {
      if (discountBuilder_ == null) {
        ensureDiscountIsMutable();
        discount_.add(builderForValue.build());
        onChanged();
      } else {
        discountBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public Builder addDiscount(
        int index, cn.hexcloud.pbis.common.service.facade.member.PriceDiscount.Builder builderForValue) {
      if (discountBuilder_ == null) {
        ensureDiscountIsMutable();
        discount_.add(index, builderForValue.build());
        onChanged();
      } else {
        discountBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public Builder addAllDiscount(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.member.PriceDiscount> values) {
      if (discountBuilder_ == null) {
        ensureDiscountIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, discount_);
        onChanged();
      } else {
        discountBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public Builder clearDiscount() {
      if (discountBuilder_ == null) {
        discount_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        discountBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public Builder removeDiscount(int index) {
      if (discountBuilder_ == null) {
        ensureDiscountIsMutable();
        discount_.remove(index);
        onChanged();
      } else {
        discountBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.PriceDiscount.Builder getDiscountBuilder(
        int index) {
      return getDiscountFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.PriceDiscountOrBuilder getDiscountOrBuilder(
        int index) {
      if (discountBuilder_ == null) {
        return discount_.get(index);  } else {
        return discountBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.PriceDiscountOrBuilder> 
         getDiscountOrBuilderList() {
      if (discountBuilder_ != null) {
        return discountBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(discount_);
      }
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.PriceDiscount.Builder addDiscountBuilder() {
      return getDiscountFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.member.PriceDiscount.getDefaultInstance());
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.PriceDiscount.Builder addDiscountBuilder(
        int index) {
      return getDiscountFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.member.PriceDiscount.getDefaultInstance());
    }
    /**
     * <pre>
     * 商品折扣信息
     * </pre>
     *
     * <code>repeated .coupon.PriceDiscount discount = 2;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.member.PriceDiscount.Builder> 
         getDiscountBuilderList() {
      return getDiscountFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.PriceDiscount, cn.hexcloud.pbis.common.service.facade.member.PriceDiscount.Builder, cn.hexcloud.pbis.common.service.facade.member.PriceDiscountOrBuilder> 
        getDiscountFieldBuilder() {
      if (discountBuilder_ == null) {
        discountBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.member.PriceDiscount, cn.hexcloud.pbis.common.service.facade.member.PriceDiscount.Builder, cn.hexcloud.pbis.common.service.facade.member.PriceDiscountOrBuilder>(
                discount_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        discount_ = null;
      }
      return discountBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount summary_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount, cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount.Builder, cn.hexcloud.pbis.common.service.facade.member.SummaryDiscountOrBuilder> summaryBuilder_;
    /**
     * <pre>
     * 折扣信息汇总
     * </pre>
     *
     * <code>.coupon.SummaryDiscount summary = 3;</code>
     * @return Whether the summary field is set.
     */
    public boolean hasSummary() {
      return summaryBuilder_ != null || summary_ != null;
    }
    /**
     * <pre>
     * 折扣信息汇总
     * </pre>
     *
     * <code>.coupon.SummaryDiscount summary = 3;</code>
     * @return The summary.
     */
    public cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount getSummary() {
      if (summaryBuilder_ == null) {
        return summary_ == null ? cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount.getDefaultInstance() : summary_;
      } else {
        return summaryBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 折扣信息汇总
     * </pre>
     *
     * <code>.coupon.SummaryDiscount summary = 3;</code>
     */
    public Builder setSummary(cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount value) {
      if (summaryBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        summary_ = value;
        onChanged();
      } else {
        summaryBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 折扣信息汇总
     * </pre>
     *
     * <code>.coupon.SummaryDiscount summary = 3;</code>
     */
    public Builder setSummary(
        cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount.Builder builderForValue) {
      if (summaryBuilder_ == null) {
        summary_ = builderForValue.build();
        onChanged();
      } else {
        summaryBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 折扣信息汇总
     * </pre>
     *
     * <code>.coupon.SummaryDiscount summary = 3;</code>
     */
    public Builder mergeSummary(cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount value) {
      if (summaryBuilder_ == null) {
        if (summary_ != null) {
          summary_ =
            cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount.newBuilder(summary_).mergeFrom(value).buildPartial();
        } else {
          summary_ = value;
        }
        onChanged();
      } else {
        summaryBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 折扣信息汇总
     * </pre>
     *
     * <code>.coupon.SummaryDiscount summary = 3;</code>
     */
    public Builder clearSummary() {
      if (summaryBuilder_ == null) {
        summary_ = null;
        onChanged();
      } else {
        summary_ = null;
        summaryBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 折扣信息汇总
     * </pre>
     *
     * <code>.coupon.SummaryDiscount summary = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount.Builder getSummaryBuilder() {
      
      onChanged();
      return getSummaryFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 折扣信息汇总
     * </pre>
     *
     * <code>.coupon.SummaryDiscount summary = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.SummaryDiscountOrBuilder getSummaryOrBuilder() {
      if (summaryBuilder_ != null) {
        return summaryBuilder_.getMessageOrBuilder();
      } else {
        return summary_ == null ?
            cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount.getDefaultInstance() : summary_;
      }
    }
    /**
     * <pre>
     * 折扣信息汇总
     * </pre>
     *
     * <code>.coupon.SummaryDiscount summary = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount, cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount.Builder, cn.hexcloud.pbis.common.service.facade.member.SummaryDiscountOrBuilder> 
        getSummaryFieldBuilder() {
      if (summaryBuilder_ == null) {
        summaryBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount, cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount.Builder, cn.hexcloud.pbis.common.service.facade.member.SummaryDiscountOrBuilder>(
                getSummary(),
                getParentForChildren(),
                isClean());
        summary_ = null;
      }
      return summaryBuilder_;
    }

    private java.lang.Object channel_ = "";
    /**
     * <pre>
     * 渠道编码
     * </pre>
     *
     * <code>string channel = 4;</code>
     * @return The channel.
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channel_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道编码
     * </pre>
     *
     * <code>string channel = 4;</code>
     * @return The bytes for channel.
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道编码
     * </pre>
     *
     * <code>string channel = 4;</code>
     * @param value The channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannel(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channel_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道编码
     * </pre>
     *
     * <code>string channel = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannel() {
      
      channel_ = getDefaultInstance().getChannel();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道编码
     * </pre>
     *
     * <code>string channel = 4;</code>
     * @param value The bytes for channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channel_ = value;
      onChanged();
      return this;
    }

    private boolean success_ ;
    /**
     * <pre>
     * 是否验证成功
     * </pre>
     *
     * <code>bool success = 5;</code>
     * @return The success.
     */
    @java.lang.Override
    public boolean getSuccess() {
      return success_;
    }
    /**
     * <pre>
     * 是否验证成功
     * </pre>
     *
     * <code>bool success = 5;</code>
     * @param value The success to set.
     * @return This builder for chaining.
     */
    public Builder setSuccess(boolean value) {
      
      success_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 是否验证成功
     * </pre>
     *
     * <code>bool success = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearSuccess() {
      
      success_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object errorCode_ = "";
    /**
     * <pre>
     * 异常编码，查看交易接口的error_code
     * </pre>
     *
     * <code>string error_code = 6;</code>
     * @return The errorCode.
     */
    public java.lang.String getErrorCode() {
      java.lang.Object ref = errorCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 异常编码，查看交易接口的error_code
     * </pre>
     *
     * <code>string error_code = 6;</code>
     * @return The bytes for errorCode.
     */
    public com.google.protobuf.ByteString
        getErrorCodeBytes() {
      java.lang.Object ref = errorCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 异常编码，查看交易接口的error_code
     * </pre>
     *
     * <code>string error_code = 6;</code>
     * @param value The errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 异常编码，查看交易接口的error_code
     * </pre>
     *
     * <code>string error_code = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorCode() {
      
      errorCode_ = getDefaultInstance().getErrorCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 异常编码，查看交易接口的error_code
     * </pre>
     *
     * <code>string error_code = 6;</code>
     * @param value The bytes for errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object message_ = "";
    /**
     * <pre>
     * 异常信息
     * </pre>
     *
     * <code>string message = 7;</code>
     * @return The message.
     */
    public java.lang.String getMessage() {
      java.lang.Object ref = message_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        message_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 异常信息
     * </pre>
     *
     * <code>string message = 7;</code>
     * @return The bytes for message.
     */
    public com.google.protobuf.ByteString
        getMessageBytes() {
      java.lang.Object ref = message_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        message_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 异常信息
     * </pre>
     *
     * <code>string message = 7;</code>
     * @param value The message to set.
     * @return This builder for chaining.
     */
    public Builder setMessage(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      message_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 异常信息
     * </pre>
     *
     * <code>string message = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearMessage() {
      
      message_ = getDefaultInstance().getMessage();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 异常信息
     * </pre>
     *
     * <code>string message = 7;</code>
     * @param value The bytes for message to set.
     * @return This builder for chaining.
     */
    public Builder setMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      message_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object responseCode_ = "";
    /**
     * <pre>
     * 第三方编码
     * </pre>
     *
     * <code>string response_code = 8;</code>
     * @return The responseCode.
     */
    public java.lang.String getResponseCode() {
      java.lang.Object ref = responseCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        responseCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 第三方编码
     * </pre>
     *
     * <code>string response_code = 8;</code>
     * @return The bytes for responseCode.
     */
    public com.google.protobuf.ByteString
        getResponseCodeBytes() {
      java.lang.Object ref = responseCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        responseCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 第三方编码
     * </pre>
     *
     * <code>string response_code = 8;</code>
     * @param value The responseCode to set.
     * @return This builder for chaining.
     */
    public Builder setResponseCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      responseCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方编码
     * </pre>
     *
     * <code>string response_code = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearResponseCode() {
      
      responseCode_ = getDefaultInstance().getResponseCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方编码
     * </pre>
     *
     * <code>string response_code = 8;</code>
     * @param value The bytes for responseCode to set.
     * @return This builder for chaining.
     */
    public Builder setResponseCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      responseCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object responseContent_ = "";
    /**
     * <pre>
     * 第三方报文(500字符以内)
     * </pre>
     *
     * <code>string response_content = 9;</code>
     * @return The responseContent.
     */
    public java.lang.String getResponseContent() {
      java.lang.Object ref = responseContent_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        responseContent_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 第三方报文(500字符以内)
     * </pre>
     *
     * <code>string response_content = 9;</code>
     * @return The bytes for responseContent.
     */
    public com.google.protobuf.ByteString
        getResponseContentBytes() {
      java.lang.Object ref = responseContent_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        responseContent_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 第三方报文(500字符以内)
     * </pre>
     *
     * <code>string response_content = 9;</code>
     * @param value The responseContent to set.
     * @return This builder for chaining.
     */
    public Builder setResponseContent(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      responseContent_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方报文(500字符以内)
     * </pre>
     *
     * <code>string response_content = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearResponseContent() {
      
      responseContent_ = getDefaultInstance().getResponseContent();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方报文(500字符以内)
     * </pre>
     *
     * <code>string response_content = 9;</code>
     * @param value The bytes for responseContent to set.
     * @return This builder for chaining.
     */
    public Builder setResponseContentBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      responseContent_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.member.PackageFee packageFee_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.PackageFee, cn.hexcloud.pbis.common.service.facade.member.PackageFee.Builder, cn.hexcloud.pbis.common.service.facade.member.PackageFeeOrBuilder> packageFeeBuilder_;
    /**
     * <pre>
     * 打包费信息
     * </pre>
     *
     * <code>.coupon.PackageFee packageFee = 10;</code>
     * @return Whether the packageFee field is set.
     */
    public boolean hasPackageFee() {
      return packageFeeBuilder_ != null || packageFee_ != null;
    }
    /**
     * <pre>
     * 打包费信息
     * </pre>
     *
     * <code>.coupon.PackageFee packageFee = 10;</code>
     * @return The packageFee.
     */
    public cn.hexcloud.pbis.common.service.facade.member.PackageFee getPackageFee() {
      if (packageFeeBuilder_ == null) {
        return packageFee_ == null ? cn.hexcloud.pbis.common.service.facade.member.PackageFee.getDefaultInstance() : packageFee_;
      } else {
        return packageFeeBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 打包费信息
     * </pre>
     *
     * <code>.coupon.PackageFee packageFee = 10;</code>
     */
    public Builder setPackageFee(cn.hexcloud.pbis.common.service.facade.member.PackageFee value) {
      if (packageFeeBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        packageFee_ = value;
        onChanged();
      } else {
        packageFeeBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 打包费信息
     * </pre>
     *
     * <code>.coupon.PackageFee packageFee = 10;</code>
     */
    public Builder setPackageFee(
        cn.hexcloud.pbis.common.service.facade.member.PackageFee.Builder builderForValue) {
      if (packageFeeBuilder_ == null) {
        packageFee_ = builderForValue.build();
        onChanged();
      } else {
        packageFeeBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 打包费信息
     * </pre>
     *
     * <code>.coupon.PackageFee packageFee = 10;</code>
     */
    public Builder mergePackageFee(cn.hexcloud.pbis.common.service.facade.member.PackageFee value) {
      if (packageFeeBuilder_ == null) {
        if (packageFee_ != null) {
          packageFee_ =
            cn.hexcloud.pbis.common.service.facade.member.PackageFee.newBuilder(packageFee_).mergeFrom(value).buildPartial();
        } else {
          packageFee_ = value;
        }
        onChanged();
      } else {
        packageFeeBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 打包费信息
     * </pre>
     *
     * <code>.coupon.PackageFee packageFee = 10;</code>
     */
    public Builder clearPackageFee() {
      if (packageFeeBuilder_ == null) {
        packageFee_ = null;
        onChanged();
      } else {
        packageFee_ = null;
        packageFeeBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 打包费信息
     * </pre>
     *
     * <code>.coupon.PackageFee packageFee = 10;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.PackageFee.Builder getPackageFeeBuilder() {
      
      onChanged();
      return getPackageFeeFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 打包费信息
     * </pre>
     *
     * <code>.coupon.PackageFee packageFee = 10;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.PackageFeeOrBuilder getPackageFeeOrBuilder() {
      if (packageFeeBuilder_ != null) {
        return packageFeeBuilder_.getMessageOrBuilder();
      } else {
        return packageFee_ == null ?
            cn.hexcloud.pbis.common.service.facade.member.PackageFee.getDefaultInstance() : packageFee_;
      }
    }
    /**
     * <pre>
     * 打包费信息
     * </pre>
     *
     * <code>.coupon.PackageFee packageFee = 10;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.PackageFee, cn.hexcloud.pbis.common.service.facade.member.PackageFee.Builder, cn.hexcloud.pbis.common.service.facade.member.PackageFeeOrBuilder> 
        getPackageFeeFieldBuilder() {
      if (packageFeeBuilder_ == null) {
        packageFeeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.member.PackageFee, cn.hexcloud.pbis.common.service.facade.member.PackageFee.Builder, cn.hexcloud.pbis.common.service.facade.member.PackageFeeOrBuilder>(
                getPackageFee(),
                getParentForChildren(),
                isClean());
        packageFee_ = null;
      }
      return packageFeeBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.member.DeliveryFee deliveryFee_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.DeliveryFee, cn.hexcloud.pbis.common.service.facade.member.DeliveryFee.Builder, cn.hexcloud.pbis.common.service.facade.member.DeliveryFeeOrBuilder> deliveryFeeBuilder_;
    /**
     * <pre>
     * 配送费信息
     * </pre>
     *
     * <code>.coupon.DeliveryFee deliveryFee = 11;</code>
     * @return Whether the deliveryFee field is set.
     */
    public boolean hasDeliveryFee() {
      return deliveryFeeBuilder_ != null || deliveryFee_ != null;
    }
    /**
     * <pre>
     * 配送费信息
     * </pre>
     *
     * <code>.coupon.DeliveryFee deliveryFee = 11;</code>
     * @return The deliveryFee.
     */
    public cn.hexcloud.pbis.common.service.facade.member.DeliveryFee getDeliveryFee() {
      if (deliveryFeeBuilder_ == null) {
        return deliveryFee_ == null ? cn.hexcloud.pbis.common.service.facade.member.DeliveryFee.getDefaultInstance() : deliveryFee_;
      } else {
        return deliveryFeeBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 配送费信息
     * </pre>
     *
     * <code>.coupon.DeliveryFee deliveryFee = 11;</code>
     */
    public Builder setDeliveryFee(cn.hexcloud.pbis.common.service.facade.member.DeliveryFee value) {
      if (deliveryFeeBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        deliveryFee_ = value;
        onChanged();
      } else {
        deliveryFeeBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 配送费信息
     * </pre>
     *
     * <code>.coupon.DeliveryFee deliveryFee = 11;</code>
     */
    public Builder setDeliveryFee(
        cn.hexcloud.pbis.common.service.facade.member.DeliveryFee.Builder builderForValue) {
      if (deliveryFeeBuilder_ == null) {
        deliveryFee_ = builderForValue.build();
        onChanged();
      } else {
        deliveryFeeBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 配送费信息
     * </pre>
     *
     * <code>.coupon.DeliveryFee deliveryFee = 11;</code>
     */
    public Builder mergeDeliveryFee(cn.hexcloud.pbis.common.service.facade.member.DeliveryFee value) {
      if (deliveryFeeBuilder_ == null) {
        if (deliveryFee_ != null) {
          deliveryFee_ =
            cn.hexcloud.pbis.common.service.facade.member.DeliveryFee.newBuilder(deliveryFee_).mergeFrom(value).buildPartial();
        } else {
          deliveryFee_ = value;
        }
        onChanged();
      } else {
        deliveryFeeBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 配送费信息
     * </pre>
     *
     * <code>.coupon.DeliveryFee deliveryFee = 11;</code>
     */
    public Builder clearDeliveryFee() {
      if (deliveryFeeBuilder_ == null) {
        deliveryFee_ = null;
        onChanged();
      } else {
        deliveryFee_ = null;
        deliveryFeeBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 配送费信息
     * </pre>
     *
     * <code>.coupon.DeliveryFee deliveryFee = 11;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.DeliveryFee.Builder getDeliveryFeeBuilder() {
      
      onChanged();
      return getDeliveryFeeFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 配送费信息
     * </pre>
     *
     * <code>.coupon.DeliveryFee deliveryFee = 11;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.DeliveryFeeOrBuilder getDeliveryFeeOrBuilder() {
      if (deliveryFeeBuilder_ != null) {
        return deliveryFeeBuilder_.getMessageOrBuilder();
      } else {
        return deliveryFee_ == null ?
            cn.hexcloud.pbis.common.service.facade.member.DeliveryFee.getDefaultInstance() : deliveryFee_;
      }
    }
    /**
     * <pre>
     * 配送费信息
     * </pre>
     *
     * <code>.coupon.DeliveryFee deliveryFee = 11;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.DeliveryFee, cn.hexcloud.pbis.common.service.facade.member.DeliveryFee.Builder, cn.hexcloud.pbis.common.service.facade.member.DeliveryFeeOrBuilder> 
        getDeliveryFeeFieldBuilder() {
      if (deliveryFeeBuilder_ == null) {
        deliveryFeeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.member.DeliveryFee, cn.hexcloud.pbis.common.service.facade.member.DeliveryFee.Builder, cn.hexcloud.pbis.common.service.facade.member.DeliveryFeeOrBuilder>(
                getDeliveryFee(),
                getParentForChildren(),
                isClean());
        deliveryFee_ = null;
      }
      return deliveryFeeBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:coupon.CalculatePromotionResponse)
  }

  // @@protoc_insertion_point(class_scope:coupon.CalculatePromotionResponse)
  private static final cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse();
  }

  public static cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CalculatePromotionResponse>
      PARSER = new com.google.protobuf.AbstractParser<CalculatePromotionResponse>() {
    @java.lang.Override
    public CalculatePromotionResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new CalculatePromotionResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<CalculatePromotionResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CalculatePromotionResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.CalculatePromotionResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

