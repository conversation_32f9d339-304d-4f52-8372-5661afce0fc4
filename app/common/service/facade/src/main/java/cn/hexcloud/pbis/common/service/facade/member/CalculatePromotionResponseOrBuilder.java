// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface CalculatePromotionResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.CalculatePromotionResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 商品折扣信息
   * </pre>
   *
   * <code>repeated .coupon.PriceDiscount discount = 2;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.member.PriceDiscount> 
      getDiscountList();
  /**
   * <pre>
   * 商品折扣信息
   * </pre>
   *
   * <code>repeated .coupon.PriceDiscount discount = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.PriceDiscount getDiscount(int index);
  /**
   * <pre>
   * 商品折扣信息
   * </pre>
   *
   * <code>repeated .coupon.PriceDiscount discount = 2;</code>
   */
  int getDiscountCount();
  /**
   * <pre>
   * 商品折扣信息
   * </pre>
   *
   * <code>repeated .coupon.PriceDiscount discount = 2;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.PriceDiscountOrBuilder> 
      getDiscountOrBuilderList();
  /**
   * <pre>
   * 商品折扣信息
   * </pre>
   *
   * <code>repeated .coupon.PriceDiscount discount = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.PriceDiscountOrBuilder getDiscountOrBuilder(
      int index);

  /**
   * <pre>
   * 折扣信息汇总
   * </pre>
   *
   * <code>.coupon.SummaryDiscount summary = 3;</code>
   * @return Whether the summary field is set.
   */
  boolean hasSummary();
  /**
   * <pre>
   * 折扣信息汇总
   * </pre>
   *
   * <code>.coupon.SummaryDiscount summary = 3;</code>
   * @return The summary.
   */
  cn.hexcloud.pbis.common.service.facade.member.SummaryDiscount getSummary();
  /**
   * <pre>
   * 折扣信息汇总
   * </pre>
   *
   * <code>.coupon.SummaryDiscount summary = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.SummaryDiscountOrBuilder getSummaryOrBuilder();

  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel = 4;</code>
   * @return The channel.
   */
  java.lang.String getChannel();
  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel = 4;</code>
   * @return The bytes for channel.
   */
  com.google.protobuf.ByteString
      getChannelBytes();

  /**
   * <pre>
   * 是否验证成功
   * </pre>
   *
   * <code>bool success = 5;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <pre>
   * 异常编码，查看交易接口的error_code
   * </pre>
   *
   * <code>string error_code = 6;</code>
   * @return The errorCode.
   */
  java.lang.String getErrorCode();
  /**
   * <pre>
   * 异常编码，查看交易接口的error_code
   * </pre>
   *
   * <code>string error_code = 6;</code>
   * @return The bytes for errorCode.
   */
  com.google.protobuf.ByteString
      getErrorCodeBytes();

  /**
   * <pre>
   * 异常信息
   * </pre>
   *
   * <code>string message = 7;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <pre>
   * 异常信息
   * </pre>
   *
   * <code>string message = 7;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <pre>
   * 第三方编码
   * </pre>
   *
   * <code>string response_code = 8;</code>
   * @return The responseCode.
   */
  java.lang.String getResponseCode();
  /**
   * <pre>
   * 第三方编码
   * </pre>
   *
   * <code>string response_code = 8;</code>
   * @return The bytes for responseCode.
   */
  com.google.protobuf.ByteString
      getResponseCodeBytes();

  /**
   * <pre>
   * 第三方报文(500字符以内)
   * </pre>
   *
   * <code>string response_content = 9;</code>
   * @return The responseContent.
   */
  java.lang.String getResponseContent();
  /**
   * <pre>
   * 第三方报文(500字符以内)
   * </pre>
   *
   * <code>string response_content = 9;</code>
   * @return The bytes for responseContent.
   */
  com.google.protobuf.ByteString
      getResponseContentBytes();

  /**
   * <pre>
   * 打包费信息
   * </pre>
   *
   * <code>.coupon.PackageFee packageFee = 10;</code>
   * @return Whether the packageFee field is set.
   */
  boolean hasPackageFee();
  /**
   * <pre>
   * 打包费信息
   * </pre>
   *
   * <code>.coupon.PackageFee packageFee = 10;</code>
   * @return The packageFee.
   */
  cn.hexcloud.pbis.common.service.facade.member.PackageFee getPackageFee();
  /**
   * <pre>
   * 打包费信息
   * </pre>
   *
   * <code>.coupon.PackageFee packageFee = 10;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.PackageFeeOrBuilder getPackageFeeOrBuilder();

  /**
   * <pre>
   * 配送费信息
   * </pre>
   *
   * <code>.coupon.DeliveryFee deliveryFee = 11;</code>
   * @return Whether the deliveryFee field is set.
   */
  boolean hasDeliveryFee();
  /**
   * <pre>
   * 配送费信息
   * </pre>
   *
   * <code>.coupon.DeliveryFee deliveryFee = 11;</code>
   * @return The deliveryFee.
   */
  cn.hexcloud.pbis.common.service.facade.member.DeliveryFee getDeliveryFee();
  /**
   * <pre>
   * 配送费信息
   * </pre>
   *
   * <code>.coupon.DeliveryFee deliveryFee = 11;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.DeliveryFeeOrBuilder getDeliveryFeeOrBuilder();
}
