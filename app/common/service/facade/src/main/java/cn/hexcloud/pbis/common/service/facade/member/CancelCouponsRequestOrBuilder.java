// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface CancelCouponsRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.CancelCouponsRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 渠道编码，HEYTEAMEMBER(喜茶会员)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  java.lang.String getChannel();
  /**
   * <pre>
   * 渠道编码，HEYTEAMEMBER(喜茶会员)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  com.google.protobuf.ByteString
      getChannelBytes();

  /**
   * <pre>
   * 支付时传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string batch_ticket_id = 2;</code>
   * @return The batchTicketId.
   */
  java.lang.String getBatchTicketId();
  /**
   * <pre>
   * 支付时传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string batch_ticket_id = 2;</code>
   * @return The bytes for batchTicketId.
   */
  com.google.protobuf.ByteString
      getBatchTicketIdBytes();

  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>uint64 store_id = 3;</code>
   * @return The storeId.
   */
  long getStoreId();

  /**
   * <pre>
   * 门店partner id
   * </pre>
   *
   * <code>uint64 partner_id = 4;</code>
   * @return The partnerId.
   */
  long getPartnerId();

  /**
   * <pre>
   * 门店scope id，如果没有就传0
   * </pre>
   *
   * <code>uint64 scope_id = 5;</code>
   * @return The scopeId.
   */
  long getScopeId();

  /**
   * <pre>
   * 用户id
   * </pre>
   *
   * <code>uint64 user_id = 6;</code>
   * @return The userId.
   */
  long getUserId();

  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * <code>.coupon.MemberContent member_content = 7;</code>
   * @return Whether the memberContent field is set.
   */
  boolean hasMemberContent();
  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * <code>.coupon.MemberContent member_content = 7;</code>
   * @return The memberContent.
   */
  cn.hexcloud.pbis.common.service.facade.member.MemberContent getMemberContent();
  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * <code>.coupon.MemberContent member_content = 7;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.MemberContentOrBuilder getMemberContentOrBuilder();

  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 8;</code>
   * @return Whether the orderContent field is set.
   */
  boolean hasOrderContent();
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 8;</code>
   * @return The orderContent.
   */
  cn.hexcloud.pbis.common.service.facade.member.OrderContent getOrderContent();
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 8;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.OrderContentOrBuilder getOrderContentOrBuilder();

  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>repeated .coupon.CouponReq coupons = 9;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.member.CouponReq> 
      getCouponsList();
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>repeated .coupon.CouponReq coupons = 9;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.CouponReq getCoupons(int index);
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>repeated .coupon.CouponReq coupons = 9;</code>
   */
  int getCouponsCount();
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>repeated .coupon.CouponReq coupons = 9;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.CouponReqOrBuilder> 
      getCouponsOrBuilderList();
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>repeated .coupon.CouponReq coupons = 9;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.CouponReqOrBuilder getCouponsOrBuilder(
      int index);

  /**
   * <pre>
   * seltek门店code
   * </pre>
   *
   * <code>string storeCode = 10;</code>
   * @return The storeCode.
   */
  java.lang.String getStoreCode();
  /**
   * <pre>
   * seltek门店code
   * </pre>
   *
   * <code>string storeCode = 10;</code>
   * @return The bytes for storeCode.
   */
  com.google.protobuf.ByteString
      getStoreCodeBytes();
}
