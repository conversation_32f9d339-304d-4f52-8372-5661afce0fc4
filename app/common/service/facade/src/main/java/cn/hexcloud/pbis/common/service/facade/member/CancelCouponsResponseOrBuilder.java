// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface CancelCouponsResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.CancelCouponsResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  java.lang.String getChannel();
  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  com.google.protobuf.ByteString
      getChannelBytes();

  /**
   * <pre>
   * 是否验证成功
   * </pre>
   *
   * <code>bool success = 2;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <pre>
   * 异常编码，查看交易接口的error_code
   * </pre>
   *
   * <code>string error_code = 3;</code>
   * @return The errorCode.
   */
  java.lang.String getErrorCode();
  /**
   * <pre>
   * 异常编码，查看交易接口的error_code
   * </pre>
   *
   * <code>string error_code = 3;</code>
   * @return The bytes for errorCode.
   */
  com.google.protobuf.ByteString
      getErrorCodeBytes();

  /**
   * <pre>
   * 异常信息
   * </pre>
   *
   * <code>string message = 4;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <pre>
   * 异常信息
   * </pre>
   *
   * <code>string message = 4;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <pre>
   * 第三方编码
   * </pre>
   *
   * <code>string response_code = 5;</code>
   * @return The responseCode.
   */
  java.lang.String getResponseCode();
  /**
   * <pre>
   * 第三方编码
   * </pre>
   *
   * <code>string response_code = 5;</code>
   * @return The bytes for responseCode.
   */
  com.google.protobuf.ByteString
      getResponseCodeBytes();

  /**
   * <pre>
   * 第三方报文(500字符以内)
   * </pre>
   *
   * <code>string response_content = 6;</code>
   * @return The responseContent.
   */
  java.lang.String getResponseContent();
  /**
   * <pre>
   * 第三方报文(500字符以内)
   * </pre>
   *
   * <code>string response_content = 6;</code>
   * @return The bytes for responseContent.
   */
  com.google.protobuf.ByteString
      getResponseContentBytes();
}
