// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

/**
 * <pre>
 * 【卡券核销】请求
 * </pre>
 *
 * Protobuf type {@code coupon.ConsumeCouponsRequest}
 */
public final class ConsumeCouponsRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:coupon.ConsumeCouponsRequest)
    ConsumeCouponsRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ConsumeCouponsRequest.newBuilder() to construct.
  private ConsumeCouponsRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ConsumeCouponsRequest() {
    channel_ = "";
    batchTicketId_ = "";
    coupons_ = java.util.Collections.emptyList();
    storeCode_ = "";
    consumeToken_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ConsumeCouponsRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ConsumeCouponsRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            channel_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            batchTicketId_ = s;
            break;
          }
          case 24: {

            storeId_ = input.readUInt64();
            break;
          }
          case 32: {

            partnerId_ = input.readUInt64();
            break;
          }
          case 40: {

            scopeId_ = input.readUInt64();
            break;
          }
          case 48: {

            userId_ = input.readUInt64();
            break;
          }
          case 58: {
            cn.hexcloud.pbis.common.service.facade.member.MemberContent.Builder subBuilder = null;
            if (memberContent_ != null) {
              subBuilder = memberContent_.toBuilder();
            }
            memberContent_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.member.MemberContent.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(memberContent_);
              memberContent_ = subBuilder.buildPartial();
            }

            break;
          }
          case 66: {
            cn.hexcloud.pbis.common.service.facade.member.OrderContent.Builder subBuilder = null;
            if (orderContent_ != null) {
              subBuilder = orderContent_.toBuilder();
            }
            orderContent_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.member.OrderContent.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(orderContent_);
              orderContent_ = subBuilder.buildPartial();
            }

            break;
          }
          case 74: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              coupons_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.member.CouponReq>();
              mutable_bitField0_ |= 0x00000001;
            }
            coupons_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.member.CouponReq.parser(), extensionRegistry));
            break;
          }
          case 82: {
            java.lang.String s = input.readStringRequireUtf8();

            storeCode_ = s;
            break;
          }
          case 89: {

            usedPoints_ = input.readDouble();
            break;
          }
          case 98: {
            java.lang.String s = input.readStringRequireUtf8();

            consumeToken_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        coupons_ = java.util.Collections.unmodifiableList(coupons_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_ConsumeCouponsRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_ConsumeCouponsRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest.class, cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest.Builder.class);
  }

  public static final int CHANNEL_FIELD_NUMBER = 1;
  private volatile java.lang.Object channel_;
  /**
   * <pre>
   * 渠道编码，HEYTEAMEMBER(喜茶会员)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  @java.lang.Override
  public java.lang.String getChannel() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channel_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道编码，HEYTEAMEMBER(喜茶会员)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelBytes() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BATCH_TICKET_ID_FIELD_NUMBER = 2;
  private volatile java.lang.Object batchTicketId_;
  /**
   * <pre>
   * 支付时传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string batch_ticket_id = 2;</code>
   * @return The batchTicketId.
   */
  @java.lang.Override
  public java.lang.String getBatchTicketId() {
    java.lang.Object ref = batchTicketId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      batchTicketId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 支付时传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string batch_ticket_id = 2;</code>
   * @return The bytes for batchTicketId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBatchTicketIdBytes() {
    java.lang.Object ref = batchTicketId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      batchTicketId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STORE_ID_FIELD_NUMBER = 3;
  private long storeId_;
  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>uint64 store_id = 3;</code>
   * @return The storeId.
   */
  @java.lang.Override
  public long getStoreId() {
    return storeId_;
  }

  public static final int PARTNER_ID_FIELD_NUMBER = 4;
  private long partnerId_;
  /**
   * <pre>
   * 门店partner id
   * </pre>
   *
   * <code>uint64 partner_id = 4;</code>
   * @return The partnerId.
   */
  @java.lang.Override
  public long getPartnerId() {
    return partnerId_;
  }

  public static final int SCOPE_ID_FIELD_NUMBER = 5;
  private long scopeId_;
  /**
   * <pre>
   * 门店scope id，如果没有就传0
   * </pre>
   *
   * <code>uint64 scope_id = 5;</code>
   * @return The scopeId.
   */
  @java.lang.Override
  public long getScopeId() {
    return scopeId_;
  }

  public static final int USER_ID_FIELD_NUMBER = 6;
  private long userId_;
  /**
   * <pre>
   * 用户id
   * </pre>
   *
   * <code>uint64 user_id = 6;</code>
   * @return The userId.
   */
  @java.lang.Override
  public long getUserId() {
    return userId_;
  }

  public static final int MEMBER_CONTENT_FIELD_NUMBER = 7;
  private cn.hexcloud.pbis.common.service.facade.member.MemberContent memberContent_;
  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * <code>.coupon.MemberContent member_content = 7;</code>
   * @return Whether the memberContent field is set.
   */
  @java.lang.Override
  public boolean hasMemberContent() {
    return memberContent_ != null;
  }
  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * <code>.coupon.MemberContent member_content = 7;</code>
   * @return The memberContent.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.MemberContent getMemberContent() {
    return memberContent_ == null ? cn.hexcloud.pbis.common.service.facade.member.MemberContent.getDefaultInstance() : memberContent_;
  }
  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * <code>.coupon.MemberContent member_content = 7;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.MemberContentOrBuilder getMemberContentOrBuilder() {
    return getMemberContent();
  }

  public static final int ORDER_CONTENT_FIELD_NUMBER = 8;
  private cn.hexcloud.pbis.common.service.facade.member.OrderContent orderContent_;
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 8;</code>
   * @return Whether the orderContent field is set.
   */
  @java.lang.Override
  public boolean hasOrderContent() {
    return orderContent_ != null;
  }
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 8;</code>
   * @return The orderContent.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.OrderContent getOrderContent() {
    return orderContent_ == null ? cn.hexcloud.pbis.common.service.facade.member.OrderContent.getDefaultInstance() : orderContent_;
  }
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 8;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.OrderContentOrBuilder getOrderContentOrBuilder() {
    return getOrderContent();
  }

  public static final int COUPONS_FIELD_NUMBER = 9;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.member.CouponReq> coupons_;
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>repeated .coupon.CouponReq coupons = 9;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.member.CouponReq> getCouponsList() {
    return coupons_;
  }
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>repeated .coupon.CouponReq coupons = 9;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.CouponReqOrBuilder> 
      getCouponsOrBuilderList() {
    return coupons_;
  }
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>repeated .coupon.CouponReq coupons = 9;</code>
   */
  @java.lang.Override
  public int getCouponsCount() {
    return coupons_.size();
  }
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>repeated .coupon.CouponReq coupons = 9;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.CouponReq getCoupons(int index) {
    return coupons_.get(index);
  }
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>repeated .coupon.CouponReq coupons = 9;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.CouponReqOrBuilder getCouponsOrBuilder(
      int index) {
    return coupons_.get(index);
  }

  public static final int STORECODE_FIELD_NUMBER = 10;
  private volatile java.lang.Object storeCode_;
  /**
   * <pre>
   * seltek storecode
   * </pre>
   *
   * <code>string storeCode = 10;</code>
   * @return The storeCode.
   */
  @java.lang.Override
  public java.lang.String getStoreCode() {
    java.lang.Object ref = storeCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      storeCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * seltek storecode
   * </pre>
   *
   * <code>string storeCode = 10;</code>
   * @return The bytes for storeCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStoreCodeBytes() {
    java.lang.Object ref = storeCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      storeCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int USEDPOINTS_FIELD_NUMBER = 11;
  private double usedPoints_;
  /**
   * <pre>
   * 使用积分（没有使用积分填0）
   * </pre>
   *
   * <code>double usedPoints = 11;</code>
   * @return The usedPoints.
   */
  @java.lang.Override
  public double getUsedPoints() {
    return usedPoints_;
  }

  public static final int CONSUME_TOKEN_FIELD_NUMBER = 12;
  private volatile java.lang.Object consumeToken_;
  /**
   * <pre>
   * 券核销凭证
   * </pre>
   *
   * <code>string consume_token = 12;</code>
   * @return The consumeToken.
   */
  @java.lang.Override
  public java.lang.String getConsumeToken() {
    java.lang.Object ref = consumeToken_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      consumeToken_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 券核销凭证
   * </pre>
   *
   * <code>string consume_token = 12;</code>
   * @return The bytes for consumeToken.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getConsumeTokenBytes() {
    java.lang.Object ref = consumeToken_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      consumeToken_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getChannelBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channel_);
    }
    if (!getBatchTicketIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, batchTicketId_);
    }
    if (storeId_ != 0L) {
      output.writeUInt64(3, storeId_);
    }
    if (partnerId_ != 0L) {
      output.writeUInt64(4, partnerId_);
    }
    if (scopeId_ != 0L) {
      output.writeUInt64(5, scopeId_);
    }
    if (userId_ != 0L) {
      output.writeUInt64(6, userId_);
    }
    if (memberContent_ != null) {
      output.writeMessage(7, getMemberContent());
    }
    if (orderContent_ != null) {
      output.writeMessage(8, getOrderContent());
    }
    for (int i = 0; i < coupons_.size(); i++) {
      output.writeMessage(9, coupons_.get(i));
    }
    if (!getStoreCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, storeCode_);
    }
    if (usedPoints_ != 0D) {
      output.writeDouble(11, usedPoints_);
    }
    if (!getConsumeTokenBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 12, consumeToken_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getChannelBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channel_);
    }
    if (!getBatchTicketIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, batchTicketId_);
    }
    if (storeId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(3, storeId_);
    }
    if (partnerId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(4, partnerId_);
    }
    if (scopeId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(5, scopeId_);
    }
    if (userId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(6, userId_);
    }
    if (memberContent_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getMemberContent());
    }
    if (orderContent_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, getOrderContent());
    }
    for (int i = 0; i < coupons_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(9, coupons_.get(i));
    }
    if (!getStoreCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, storeCode_);
    }
    if (usedPoints_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(11, usedPoints_);
    }
    if (!getConsumeTokenBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, consumeToken_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest other = (cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest) obj;

    if (!getChannel()
        .equals(other.getChannel())) return false;
    if (!getBatchTicketId()
        .equals(other.getBatchTicketId())) return false;
    if (getStoreId()
        != other.getStoreId()) return false;
    if (getPartnerId()
        != other.getPartnerId()) return false;
    if (getScopeId()
        != other.getScopeId()) return false;
    if (getUserId()
        != other.getUserId()) return false;
    if (hasMemberContent() != other.hasMemberContent()) return false;
    if (hasMemberContent()) {
      if (!getMemberContent()
          .equals(other.getMemberContent())) return false;
    }
    if (hasOrderContent() != other.hasOrderContent()) return false;
    if (hasOrderContent()) {
      if (!getOrderContent()
          .equals(other.getOrderContent())) return false;
    }
    if (!getCouponsList()
        .equals(other.getCouponsList())) return false;
    if (!getStoreCode()
        .equals(other.getStoreCode())) return false;
    if (java.lang.Double.doubleToLongBits(getUsedPoints())
        != java.lang.Double.doubleToLongBits(
            other.getUsedPoints())) return false;
    if (!getConsumeToken()
        .equals(other.getConsumeToken())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
    hash = (53 * hash) + getChannel().hashCode();
    hash = (37 * hash) + BATCH_TICKET_ID_FIELD_NUMBER;
    hash = (53 * hash) + getBatchTicketId().hashCode();
    hash = (37 * hash) + STORE_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getStoreId());
    hash = (37 * hash) + PARTNER_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getPartnerId());
    hash = (37 * hash) + SCOPE_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getScopeId());
    hash = (37 * hash) + USER_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getUserId());
    if (hasMemberContent()) {
      hash = (37 * hash) + MEMBER_CONTENT_FIELD_NUMBER;
      hash = (53 * hash) + getMemberContent().hashCode();
    }
    if (hasOrderContent()) {
      hash = (37 * hash) + ORDER_CONTENT_FIELD_NUMBER;
      hash = (53 * hash) + getOrderContent().hashCode();
    }
    if (getCouponsCount() > 0) {
      hash = (37 * hash) + COUPONS_FIELD_NUMBER;
      hash = (53 * hash) + getCouponsList().hashCode();
    }
    hash = (37 * hash) + STORECODE_FIELD_NUMBER;
    hash = (53 * hash) + getStoreCode().hashCode();
    hash = (37 * hash) + USEDPOINTS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getUsedPoints()));
    hash = (37 * hash) + CONSUME_TOKEN_FIELD_NUMBER;
    hash = (53 * hash) + getConsumeToken().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 【卡券核销】请求
   * </pre>
   *
   * Protobuf type {@code coupon.ConsumeCouponsRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:coupon.ConsumeCouponsRequest)
      cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_ConsumeCouponsRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_ConsumeCouponsRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest.class, cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getCouponsFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channel_ = "";

      batchTicketId_ = "";

      storeId_ = 0L;

      partnerId_ = 0L;

      scopeId_ = 0L;

      userId_ = 0L;

      if (memberContentBuilder_ == null) {
        memberContent_ = null;
      } else {
        memberContent_ = null;
        memberContentBuilder_ = null;
      }
      if (orderContentBuilder_ == null) {
        orderContent_ = null;
      } else {
        orderContent_ = null;
        orderContentBuilder_ = null;
      }
      if (couponsBuilder_ == null) {
        coupons_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        couponsBuilder_.clear();
      }
      storeCode_ = "";

      usedPoints_ = 0D;

      consumeToken_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_ConsumeCouponsRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest build() {
      cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest buildPartial() {
      cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest result = new cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest(this);
      int from_bitField0_ = bitField0_;
      result.channel_ = channel_;
      result.batchTicketId_ = batchTicketId_;
      result.storeId_ = storeId_;
      result.partnerId_ = partnerId_;
      result.scopeId_ = scopeId_;
      result.userId_ = userId_;
      if (memberContentBuilder_ == null) {
        result.memberContent_ = memberContent_;
      } else {
        result.memberContent_ = memberContentBuilder_.build();
      }
      if (orderContentBuilder_ == null) {
        result.orderContent_ = orderContent_;
      } else {
        result.orderContent_ = orderContentBuilder_.build();
      }
      if (couponsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          coupons_ = java.util.Collections.unmodifiableList(coupons_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.coupons_ = coupons_;
      } else {
        result.coupons_ = couponsBuilder_.build();
      }
      result.storeCode_ = storeCode_;
      result.usedPoints_ = usedPoints_;
      result.consumeToken_ = consumeToken_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest other) {
      if (other == cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest.getDefaultInstance()) return this;
      if (!other.getChannel().isEmpty()) {
        channel_ = other.channel_;
        onChanged();
      }
      if (!other.getBatchTicketId().isEmpty()) {
        batchTicketId_ = other.batchTicketId_;
        onChanged();
      }
      if (other.getStoreId() != 0L) {
        setStoreId(other.getStoreId());
      }
      if (other.getPartnerId() != 0L) {
        setPartnerId(other.getPartnerId());
      }
      if (other.getScopeId() != 0L) {
        setScopeId(other.getScopeId());
      }
      if (other.getUserId() != 0L) {
        setUserId(other.getUserId());
      }
      if (other.hasMemberContent()) {
        mergeMemberContent(other.getMemberContent());
      }
      if (other.hasOrderContent()) {
        mergeOrderContent(other.getOrderContent());
      }
      if (couponsBuilder_ == null) {
        if (!other.coupons_.isEmpty()) {
          if (coupons_.isEmpty()) {
            coupons_ = other.coupons_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureCouponsIsMutable();
            coupons_.addAll(other.coupons_);
          }
          onChanged();
        }
      } else {
        if (!other.coupons_.isEmpty()) {
          if (couponsBuilder_.isEmpty()) {
            couponsBuilder_.dispose();
            couponsBuilder_ = null;
            coupons_ = other.coupons_;
            bitField0_ = (bitField0_ & ~0x00000001);
            couponsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getCouponsFieldBuilder() : null;
          } else {
            couponsBuilder_.addAllMessages(other.coupons_);
          }
        }
      }
      if (!other.getStoreCode().isEmpty()) {
        storeCode_ = other.storeCode_;
        onChanged();
      }
      if (other.getUsedPoints() != 0D) {
        setUsedPoints(other.getUsedPoints());
      }
      if (!other.getConsumeToken().isEmpty()) {
        consumeToken_ = other.consumeToken_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object channel_ = "";
    /**
     * <pre>
     * 渠道编码，HEYTEAMEMBER(喜茶会员)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return The channel.
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channel_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道编码，HEYTEAMEMBER(喜茶会员)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return The bytes for channel.
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道编码，HEYTEAMEMBER(喜茶会员)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @param value The channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannel(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channel_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道编码，HEYTEAMEMBER(喜茶会员)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannel() {
      
      channel_ = getDefaultInstance().getChannel();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道编码，HEYTEAMEMBER(喜茶会员)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @param value The bytes for channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channel_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object batchTicketId_ = "";
    /**
     * <pre>
     * 支付时传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string batch_ticket_id = 2;</code>
     * @return The batchTicketId.
     */
    public java.lang.String getBatchTicketId() {
      java.lang.Object ref = batchTicketId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        batchTicketId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 支付时传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string batch_ticket_id = 2;</code>
     * @return The bytes for batchTicketId.
     */
    public com.google.protobuf.ByteString
        getBatchTicketIdBytes() {
      java.lang.Object ref = batchTicketId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        batchTicketId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 支付时传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string batch_ticket_id = 2;</code>
     * @param value The batchTicketId to set.
     * @return This builder for chaining.
     */
    public Builder setBatchTicketId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      batchTicketId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 支付时传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string batch_ticket_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearBatchTicketId() {
      
      batchTicketId_ = getDefaultInstance().getBatchTicketId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 支付时传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string batch_ticket_id = 2;</code>
     * @param value The bytes for batchTicketId to set.
     * @return This builder for chaining.
     */
    public Builder setBatchTicketIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      batchTicketId_ = value;
      onChanged();
      return this;
    }

    private long storeId_ ;
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>uint64 store_id = 3;</code>
     * @return The storeId.
     */
    @java.lang.Override
    public long getStoreId() {
      return storeId_;
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>uint64 store_id = 3;</code>
     * @param value The storeId to set.
     * @return This builder for chaining.
     */
    public Builder setStoreId(long value) {
      
      storeId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店id
     * </pre>
     *
     * <code>uint64 store_id = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearStoreId() {
      
      storeId_ = 0L;
      onChanged();
      return this;
    }

    private long partnerId_ ;
    /**
     * <pre>
     * 门店partner id
     * </pre>
     *
     * <code>uint64 partner_id = 4;</code>
     * @return The partnerId.
     */
    @java.lang.Override
    public long getPartnerId() {
      return partnerId_;
    }
    /**
     * <pre>
     * 门店partner id
     * </pre>
     *
     * <code>uint64 partner_id = 4;</code>
     * @param value The partnerId to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerId(long value) {
      
      partnerId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店partner id
     * </pre>
     *
     * <code>uint64 partner_id = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearPartnerId() {
      
      partnerId_ = 0L;
      onChanged();
      return this;
    }

    private long scopeId_ ;
    /**
     * <pre>
     * 门店scope id，如果没有就传0
     * </pre>
     *
     * <code>uint64 scope_id = 5;</code>
     * @return The scopeId.
     */
    @java.lang.Override
    public long getScopeId() {
      return scopeId_;
    }
    /**
     * <pre>
     * 门店scope id，如果没有就传0
     * </pre>
     *
     * <code>uint64 scope_id = 5;</code>
     * @param value The scopeId to set.
     * @return This builder for chaining.
     */
    public Builder setScopeId(long value) {
      
      scopeId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店scope id，如果没有就传0
     * </pre>
     *
     * <code>uint64 scope_id = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearScopeId() {
      
      scopeId_ = 0L;
      onChanged();
      return this;
    }

    private long userId_ ;
    /**
     * <pre>
     * 用户id
     * </pre>
     *
     * <code>uint64 user_id = 6;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }
    /**
     * <pre>
     * 用户id
     * </pre>
     *
     * <code>uint64 user_id = 6;</code>
     * @param value The userId to set.
     * @return This builder for chaining.
     */
    public Builder setUserId(long value) {
      
      userId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 用户id
     * </pre>
     *
     * <code>uint64 user_id = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearUserId() {
      
      userId_ = 0L;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.member.MemberContent memberContent_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.MemberContent, cn.hexcloud.pbis.common.service.facade.member.MemberContent.Builder, cn.hexcloud.pbis.common.service.facade.member.MemberContentOrBuilder> memberContentBuilder_;
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 7;</code>
     * @return Whether the memberContent field is set.
     */
    public boolean hasMemberContent() {
      return memberContentBuilder_ != null || memberContent_ != null;
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 7;</code>
     * @return The memberContent.
     */
    public cn.hexcloud.pbis.common.service.facade.member.MemberContent getMemberContent() {
      if (memberContentBuilder_ == null) {
        return memberContent_ == null ? cn.hexcloud.pbis.common.service.facade.member.MemberContent.getDefaultInstance() : memberContent_;
      } else {
        return memberContentBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 7;</code>
     */
    public Builder setMemberContent(cn.hexcloud.pbis.common.service.facade.member.MemberContent value) {
      if (memberContentBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        memberContent_ = value;
        onChanged();
      } else {
        memberContentBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 7;</code>
     */
    public Builder setMemberContent(
        cn.hexcloud.pbis.common.service.facade.member.MemberContent.Builder builderForValue) {
      if (memberContentBuilder_ == null) {
        memberContent_ = builderForValue.build();
        onChanged();
      } else {
        memberContentBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 7;</code>
     */
    public Builder mergeMemberContent(cn.hexcloud.pbis.common.service.facade.member.MemberContent value) {
      if (memberContentBuilder_ == null) {
        if (memberContent_ != null) {
          memberContent_ =
            cn.hexcloud.pbis.common.service.facade.member.MemberContent.newBuilder(memberContent_).mergeFrom(value).buildPartial();
        } else {
          memberContent_ = value;
        }
        onChanged();
      } else {
        memberContentBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 7;</code>
     */
    public Builder clearMemberContent() {
      if (memberContentBuilder_ == null) {
        memberContent_ = null;
        onChanged();
      } else {
        memberContent_ = null;
        memberContentBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.MemberContent.Builder getMemberContentBuilder() {
      
      onChanged();
      return getMemberContentFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.MemberContentOrBuilder getMemberContentOrBuilder() {
      if (memberContentBuilder_ != null) {
        return memberContentBuilder_.getMessageOrBuilder();
      } else {
        return memberContent_ == null ?
            cn.hexcloud.pbis.common.service.facade.member.MemberContent.getDefaultInstance() : memberContent_;
      }
    }
    /**
     * <pre>
     * 会员信息
     * </pre>
     *
     * <code>.coupon.MemberContent member_content = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.MemberContent, cn.hexcloud.pbis.common.service.facade.member.MemberContent.Builder, cn.hexcloud.pbis.common.service.facade.member.MemberContentOrBuilder> 
        getMemberContentFieldBuilder() {
      if (memberContentBuilder_ == null) {
        memberContentBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.member.MemberContent, cn.hexcloud.pbis.common.service.facade.member.MemberContent.Builder, cn.hexcloud.pbis.common.service.facade.member.MemberContentOrBuilder>(
                getMemberContent(),
                getParentForChildren(),
                isClean());
        memberContent_ = null;
      }
      return memberContentBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.member.OrderContent orderContent_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.OrderContent, cn.hexcloud.pbis.common.service.facade.member.OrderContent.Builder, cn.hexcloud.pbis.common.service.facade.member.OrderContentOrBuilder> orderContentBuilder_;
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 8;</code>
     * @return Whether the orderContent field is set.
     */
    public boolean hasOrderContent() {
      return orderContentBuilder_ != null || orderContent_ != null;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 8;</code>
     * @return The orderContent.
     */
    public cn.hexcloud.pbis.common.service.facade.member.OrderContent getOrderContent() {
      if (orderContentBuilder_ == null) {
        return orderContent_ == null ? cn.hexcloud.pbis.common.service.facade.member.OrderContent.getDefaultInstance() : orderContent_;
      } else {
        return orderContentBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 8;</code>
     */
    public Builder setOrderContent(cn.hexcloud.pbis.common.service.facade.member.OrderContent value) {
      if (orderContentBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        orderContent_ = value;
        onChanged();
      } else {
        orderContentBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 8;</code>
     */
    public Builder setOrderContent(
        cn.hexcloud.pbis.common.service.facade.member.OrderContent.Builder builderForValue) {
      if (orderContentBuilder_ == null) {
        orderContent_ = builderForValue.build();
        onChanged();
      } else {
        orderContentBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 8;</code>
     */
    public Builder mergeOrderContent(cn.hexcloud.pbis.common.service.facade.member.OrderContent value) {
      if (orderContentBuilder_ == null) {
        if (orderContent_ != null) {
          orderContent_ =
            cn.hexcloud.pbis.common.service.facade.member.OrderContent.newBuilder(orderContent_).mergeFrom(value).buildPartial();
        } else {
          orderContent_ = value;
        }
        onChanged();
      } else {
        orderContentBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 8;</code>
     */
    public Builder clearOrderContent() {
      if (orderContentBuilder_ == null) {
        orderContent_ = null;
        onChanged();
      } else {
        orderContent_ = null;
        orderContentBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 8;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.OrderContent.Builder getOrderContentBuilder() {
      
      onChanged();
      return getOrderContentFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 8;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.OrderContentOrBuilder getOrderContentOrBuilder() {
      if (orderContentBuilder_ != null) {
        return orderContentBuilder_.getMessageOrBuilder();
      } else {
        return orderContent_ == null ?
            cn.hexcloud.pbis.common.service.facade.member.OrderContent.getDefaultInstance() : orderContent_;
      }
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>.coupon.OrderContent order_content = 8;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.OrderContent, cn.hexcloud.pbis.common.service.facade.member.OrderContent.Builder, cn.hexcloud.pbis.common.service.facade.member.OrderContentOrBuilder> 
        getOrderContentFieldBuilder() {
      if (orderContentBuilder_ == null) {
        orderContentBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.member.OrderContent, cn.hexcloud.pbis.common.service.facade.member.OrderContent.Builder, cn.hexcloud.pbis.common.service.facade.member.OrderContentOrBuilder>(
                getOrderContent(),
                getParentForChildren(),
                isClean());
        orderContent_ = null;
      }
      return orderContentBuilder_;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.member.CouponReq> coupons_ =
      java.util.Collections.emptyList();
    private void ensureCouponsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        coupons_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.member.CouponReq>(coupons_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.CouponReq, cn.hexcloud.pbis.common.service.facade.member.CouponReq.Builder, cn.hexcloud.pbis.common.service.facade.member.CouponReqOrBuilder> couponsBuilder_;

    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.member.CouponReq> getCouponsList() {
      if (couponsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(coupons_);
      } else {
        return couponsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public int getCouponsCount() {
      if (couponsBuilder_ == null) {
        return coupons_.size();
      } else {
        return couponsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.CouponReq getCoupons(int index) {
      if (couponsBuilder_ == null) {
        return coupons_.get(index);
      } else {
        return couponsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public Builder setCoupons(
        int index, cn.hexcloud.pbis.common.service.facade.member.CouponReq value) {
      if (couponsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCouponsIsMutable();
        coupons_.set(index, value);
        onChanged();
      } else {
        couponsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public Builder setCoupons(
        int index, cn.hexcloud.pbis.common.service.facade.member.CouponReq.Builder builderForValue) {
      if (couponsBuilder_ == null) {
        ensureCouponsIsMutable();
        coupons_.set(index, builderForValue.build());
        onChanged();
      } else {
        couponsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public Builder addCoupons(cn.hexcloud.pbis.common.service.facade.member.CouponReq value) {
      if (couponsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCouponsIsMutable();
        coupons_.add(value);
        onChanged();
      } else {
        couponsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public Builder addCoupons(
        int index, cn.hexcloud.pbis.common.service.facade.member.CouponReq value) {
      if (couponsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCouponsIsMutable();
        coupons_.add(index, value);
        onChanged();
      } else {
        couponsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public Builder addCoupons(
        cn.hexcloud.pbis.common.service.facade.member.CouponReq.Builder builderForValue) {
      if (couponsBuilder_ == null) {
        ensureCouponsIsMutable();
        coupons_.add(builderForValue.build());
        onChanged();
      } else {
        couponsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public Builder addCoupons(
        int index, cn.hexcloud.pbis.common.service.facade.member.CouponReq.Builder builderForValue) {
      if (couponsBuilder_ == null) {
        ensureCouponsIsMutable();
        coupons_.add(index, builderForValue.build());
        onChanged();
      } else {
        couponsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public Builder addAllCoupons(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.member.CouponReq> values) {
      if (couponsBuilder_ == null) {
        ensureCouponsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, coupons_);
        onChanged();
      } else {
        couponsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public Builder clearCoupons() {
      if (couponsBuilder_ == null) {
        coupons_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        couponsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public Builder removeCoupons(int index) {
      if (couponsBuilder_ == null) {
        ensureCouponsIsMutable();
        coupons_.remove(index);
        onChanged();
      } else {
        couponsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.CouponReq.Builder getCouponsBuilder(
        int index) {
      return getCouponsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.CouponReqOrBuilder getCouponsOrBuilder(
        int index) {
      if (couponsBuilder_ == null) {
        return coupons_.get(index);  } else {
        return couponsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.CouponReqOrBuilder> 
         getCouponsOrBuilderList() {
      if (couponsBuilder_ != null) {
        return couponsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(coupons_);
      }
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.CouponReq.Builder addCouponsBuilder() {
      return getCouponsFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.member.CouponReq.getDefaultInstance());
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.CouponReq.Builder addCouponsBuilder(
        int index) {
      return getCouponsFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.member.CouponReq.getDefaultInstance());
    }
    /**
     * <pre>
     * 订单信息
     * </pre>
     *
     * <code>repeated .coupon.CouponReq coupons = 9;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.member.CouponReq.Builder> 
         getCouponsBuilderList() {
      return getCouponsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.CouponReq, cn.hexcloud.pbis.common.service.facade.member.CouponReq.Builder, cn.hexcloud.pbis.common.service.facade.member.CouponReqOrBuilder> 
        getCouponsFieldBuilder() {
      if (couponsBuilder_ == null) {
        couponsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.member.CouponReq, cn.hexcloud.pbis.common.service.facade.member.CouponReq.Builder, cn.hexcloud.pbis.common.service.facade.member.CouponReqOrBuilder>(
                coupons_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        coupons_ = null;
      }
      return couponsBuilder_;
    }

    private java.lang.Object storeCode_ = "";
    /**
     * <pre>
     * seltek storecode
     * </pre>
     *
     * <code>string storeCode = 10;</code>
     * @return The storeCode.
     */
    public java.lang.String getStoreCode() {
      java.lang.Object ref = storeCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        storeCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * seltek storecode
     * </pre>
     *
     * <code>string storeCode = 10;</code>
     * @return The bytes for storeCode.
     */
    public com.google.protobuf.ByteString
        getStoreCodeBytes() {
      java.lang.Object ref = storeCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        storeCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * seltek storecode
     * </pre>
     *
     * <code>string storeCode = 10;</code>
     * @param value The storeCode to set.
     * @return This builder for chaining.
     */
    public Builder setStoreCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      storeCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * seltek storecode
     * </pre>
     *
     * <code>string storeCode = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearStoreCode() {
      
      storeCode_ = getDefaultInstance().getStoreCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * seltek storecode
     * </pre>
     *
     * <code>string storeCode = 10;</code>
     * @param value The bytes for storeCode to set.
     * @return This builder for chaining.
     */
    public Builder setStoreCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      storeCode_ = value;
      onChanged();
      return this;
    }

    private double usedPoints_ ;
    /**
     * <pre>
     * 使用积分（没有使用积分填0）
     * </pre>
     *
     * <code>double usedPoints = 11;</code>
     * @return The usedPoints.
     */
    @java.lang.Override
    public double getUsedPoints() {
      return usedPoints_;
    }
    /**
     * <pre>
     * 使用积分（没有使用积分填0）
     * </pre>
     *
     * <code>double usedPoints = 11;</code>
     * @param value The usedPoints to set.
     * @return This builder for chaining.
     */
    public Builder setUsedPoints(double value) {
      
      usedPoints_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 使用积分（没有使用积分填0）
     * </pre>
     *
     * <code>double usedPoints = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearUsedPoints() {
      
      usedPoints_ = 0D;
      onChanged();
      return this;
    }

    private java.lang.Object consumeToken_ = "";
    /**
     * <pre>
     * 券核销凭证
     * </pre>
     *
     * <code>string consume_token = 12;</code>
     * @return The consumeToken.
     */
    public java.lang.String getConsumeToken() {
      java.lang.Object ref = consumeToken_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        consumeToken_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 券核销凭证
     * </pre>
     *
     * <code>string consume_token = 12;</code>
     * @return The bytes for consumeToken.
     */
    public com.google.protobuf.ByteString
        getConsumeTokenBytes() {
      java.lang.Object ref = consumeToken_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        consumeToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 券核销凭证
     * </pre>
     *
     * <code>string consume_token = 12;</code>
     * @param value The consumeToken to set.
     * @return This builder for chaining.
     */
    public Builder setConsumeToken(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      consumeToken_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 券核销凭证
     * </pre>
     *
     * <code>string consume_token = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearConsumeToken() {
      
      consumeToken_ = getDefaultInstance().getConsumeToken();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 券核销凭证
     * </pre>
     *
     * <code>string consume_token = 12;</code>
     * @param value The bytes for consumeToken to set.
     * @return This builder for chaining.
     */
    public Builder setConsumeTokenBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      consumeToken_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:coupon.ConsumeCouponsRequest)
  }

  // @@protoc_insertion_point(class_scope:coupon.ConsumeCouponsRequest)
  private static final cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest();
  }

  public static cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ConsumeCouponsRequest>
      PARSER = new com.google.protobuf.AbstractParser<ConsumeCouponsRequest>() {
    @java.lang.Override
    public ConsumeCouponsRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ConsumeCouponsRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ConsumeCouponsRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ConsumeCouponsRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.ConsumeCouponsRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

