// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface GetMemberNewResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.GetMemberNewResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  java.lang.String getChannel();
  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  com.google.protobuf.ByteString
      getChannelBytes();

  /**
   * <pre>
   * 账户余额
   * </pre>
   *
   * <code>double account_balance = 2;</code>
   * @return The accountBalance.
   */
  double getAccountBalance();

  /**
   * <pre>
   * 历史总积分
   * </pre>
   *
   * <code>int32 credit_total = 3;</code>
   * @return The creditTotal.
   */
  int getCreditTotal();

  /**
   * <pre>
   * 积分余额
   * </pre>
   *
   * <code>int32 credit_balance = 4;</code>
   * @return The creditBalance.
   */
  int getCreditBalance();

  /**
   * <pre>
   * 会员卡号
   * </pre>
   *
   * <code>string card_no = 5;</code>
   * @return The cardNo.
   */
  java.lang.String getCardNo();
  /**
   * <pre>
   * 会员卡号
   * </pre>
   *
   * <code>string card_no = 5;</code>
   * @return The bytes for cardNo.
   */
  com.google.protobuf.ByteString
      getCardNoBytes();

  /**
   * <pre>
   * 会员ID
   * </pre>
   *
   * <code>string member_id = 6;</code>
   * @return The memberId.
   */
  java.lang.String getMemberId();
  /**
   * <pre>
   * 会员ID
   * </pre>
   *
   * <code>string member_id = 6;</code>
   * @return The bytes for memberId.
   */
  com.google.protobuf.ByteString
      getMemberIdBytes();

  /**
   * <pre>
   * 会员编号
   * </pre>
   *
   * <code>string member_code = 7;</code>
   * @return The memberCode.
   */
  java.lang.String getMemberCode();
  /**
   * <pre>
   * 会员编号
   * </pre>
   *
   * <code>string member_code = 7;</code>
   * @return The bytes for memberCode.
   */
  com.google.protobuf.ByteString
      getMemberCodeBytes();

  /**
   * <pre>
   * 会员名字
   * </pre>
   *
   * <code>string name = 8;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   * 会员名字
   * </pre>
   *
   * <code>string name = 8;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * 性别：MALE/FEMALE/NA(未知)
   * </pre>
   *
   * <code>string gender = 9;</code>
   * @return The gender.
   */
  java.lang.String getGender();
  /**
   * <pre>
   * 性别：MALE/FEMALE/NA(未知)
   * </pre>
   *
   * <code>string gender = 9;</code>
   * @return The bytes for gender.
   */
  com.google.protobuf.ByteString
      getGenderBytes();

  /**
   * <pre>
   * 邮箱
   * </pre>
   *
   * <code>string email = 10;</code>
   * @return The email.
   */
  java.lang.String getEmail();
  /**
   * <pre>
   * 邮箱
   * </pre>
   *
   * <code>string email = 10;</code>
   * @return The bytes for email.
   */
  com.google.protobuf.ByteString
      getEmailBytes();

  /**
   * <pre>
   * 手机号码
   * </pre>
   *
   * <code>string mobile = 11;</code>
   * @return The mobile.
   */
  java.lang.String getMobile();
  /**
   * <pre>
   * 手机号码
   * </pre>
   *
   * <code>string mobile = 11;</code>
   * @return The bytes for mobile.
   */
  com.google.protobuf.ByteString
      getMobileBytes();

  /**
   * <pre>
   * 会员状态(TODO枚举类型待定义)
   * </pre>
   *
   * <code>int32 status = 12;</code>
   * @return The status.
   */
  int getStatus();

  /**
   * <pre>
   * 会员等级id
   * </pre>
   *
   * <code>string grade_id = 13;</code>
   * @return The gradeId.
   */
  java.lang.String getGradeId();
  /**
   * <pre>
   * 会员等级id
   * </pre>
   *
   * <code>string grade_id = 13;</code>
   * @return The bytes for gradeId.
   */
  com.google.protobuf.ByteString
      getGradeIdBytes();

  /**
   * <pre>
   * 会员等级名称
   * </pre>
   *
   * <code>string grade_name = 14;</code>
   * @return The gradeName.
   */
  java.lang.String getGradeName();
  /**
   * <pre>
   * 会员等级名称
   * </pre>
   *
   * <code>string grade_name = 14;</code>
   * @return The bytes for gradeName.
   */
  com.google.protobuf.ByteString
      getGradeNameBytes();

  /**
   * <pre>
   * 会员授予时间
   * </pre>
   *
   * <code>string grant_date = 15;</code>
   * @return The grantDate.
   */
  java.lang.String getGrantDate();
  /**
   * <pre>
   * 会员授予时间
   * </pre>
   *
   * <code>string grant_date = 15;</code>
   * @return The bytes for grantDate.
   */
  com.google.protobuf.ByteString
      getGrantDateBytes();

  /**
   * <pre>
   * 是否员工
   * </pre>
   *
   * <code>bool is_employee = 16;</code>
   * @return The isEmployee.
   */
  boolean getIsEmployee();

  /**
   * <pre>
   * 头像
   * </pre>
   *
   * <code>string avatar = 17;</code>
   * @return The avatar.
   */
  java.lang.String getAvatar();
  /**
   * <pre>
   * 头像
   * </pre>
   *
   * <code>string avatar = 17;</code>
   * @return The bytes for avatar.
   */
  com.google.protobuf.ByteString
      getAvatarBytes();

  /**
   * <pre>
   * 可以用于支付的编码，根据不用厂商，可以是card_no或member_code
   * </pre>
   *
   * <code>string paycode = 18;</code>
   * @return The paycode.
   */
  java.lang.String getPaycode();
  /**
   * <pre>
   * 可以用于支付的编码，根据不用厂商，可以是card_no或member_code
   * </pre>
   *
   * <code>string paycode = 18;</code>
   * @return The bytes for paycode.
   */
  com.google.protobuf.ByteString
      getPaycodeBytes();

  /**
   * <pre>
   * 根据会员等级id(grade_id)获取的Hex促销id, 多个以逗号","隔开。POS后台的会员等级主档单独维护一个扩展字段用来映射grade_id，字段名称在集成过程中定义
   * </pre>
   *
   * <code>string promotion_id = 19;</code>
   * @return The promotionId.
   */
  java.lang.String getPromotionId();
  /**
   * <pre>
   * 根据会员等级id(grade_id)获取的Hex促销id, 多个以逗号","隔开。POS后台的会员等级主档单独维护一个扩展字段用来映射grade_id，字段名称在集成过程中定义
   * </pre>
   *
   * <code>string promotion_id = 19;</code>
   * @return The bytes for promotionId.
   */
  com.google.protobuf.ByteString
      getPromotionIdBytes();

  /**
   * <pre>
   * 卡券列表
   * </pre>
   *
   * <code>repeated .coupon.CouponNew coupons = 20;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.member.CouponNew> 
      getCouponsList();
  /**
   * <pre>
   * 卡券列表
   * </pre>
   *
   * <code>repeated .coupon.CouponNew coupons = 20;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.CouponNew getCoupons(int index);
  /**
   * <pre>
   * 卡券列表
   * </pre>
   *
   * <code>repeated .coupon.CouponNew coupons = 20;</code>
   */
  int getCouponsCount();
  /**
   * <pre>
   * 卡券列表
   * </pre>
   *
   * <code>repeated .coupon.CouponNew coupons = 20;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.CouponNewOrBuilder> 
      getCouponsOrBuilderList();
  /**
   * <pre>
   * 卡券列表
   * </pre>
   *
   * <code>repeated .coupon.CouponNew coupons = 20;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.CouponNewOrBuilder getCouponsOrBuilder(
      int index);

  /**
   * <pre>
   * 标签提示语
   * </pre>
   *
   * <code>string greetings = 21;</code>
   * @return The greetings.
   */
  java.lang.String getGreetings();
  /**
   * <pre>
   * 标签提示语
   * </pre>
   *
   * <code>string greetings = 21;</code>
   * @return The bytes for greetings.
   */
  com.google.protobuf.ByteString
      getGreetingsBytes();

  /**
   * <pre>
   * 储值卡列表
   * </pre>
   *
   * <code>repeated .coupon.DepositCard depositCard = 22;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.member.DepositCard> 
      getDepositCardList();
  /**
   * <pre>
   * 储值卡列表
   * </pre>
   *
   * <code>repeated .coupon.DepositCard depositCard = 22;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.DepositCard getDepositCard(int index);
  /**
   * <pre>
   * 储值卡列表
   * </pre>
   *
   * <code>repeated .coupon.DepositCard depositCard = 22;</code>
   */
  int getDepositCardCount();
  /**
   * <pre>
   * 储值卡列表
   * </pre>
   *
   * <code>repeated .coupon.DepositCard depositCard = 22;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.DepositCardOrBuilder> 
      getDepositCardOrBuilderList();
  /**
   * <pre>
   * 储值卡列表
   * </pre>
   *
   * <code>repeated .coupon.DepositCard depositCard = 22;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.DepositCardOrBuilder getDepositCardOrBuilder(
      int index);

  /**
   * <pre>
   * 权益列表
   * </pre>
   *
   * <code>repeated .coupon.Benefit benefit = 23;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.member.Benefit> 
      getBenefitList();
  /**
   * <pre>
   * 权益列表
   * </pre>
   *
   * <code>repeated .coupon.Benefit benefit = 23;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.Benefit getBenefit(int index);
  /**
   * <pre>
   * 权益列表
   * </pre>
   *
   * <code>repeated .coupon.Benefit benefit = 23;</code>
   */
  int getBenefitCount();
  /**
   * <pre>
   * 权益列表
   * </pre>
   *
   * <code>repeated .coupon.Benefit benefit = 23;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.BenefitOrBuilder> 
      getBenefitOrBuilderList();
  /**
   * <pre>
   * 权益列表
   * </pre>
   *
   * <code>repeated .coupon.Benefit benefit = 23;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.BenefitOrBuilder getBenefitOrBuilder(
      int index);

  /**
   * <pre>
   *猜你喜欢
   * </pre>
   *
   * <code>repeated .coupon.PurchasesAnalysis purchases_analysis = 24;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.member.PurchasesAnalysis> 
      getPurchasesAnalysisList();
  /**
   * <pre>
   *猜你喜欢
   * </pre>
   *
   * <code>repeated .coupon.PurchasesAnalysis purchases_analysis = 24;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.PurchasesAnalysis getPurchasesAnalysis(int index);
  /**
   * <pre>
   *猜你喜欢
   * </pre>
   *
   * <code>repeated .coupon.PurchasesAnalysis purchases_analysis = 24;</code>
   */
  int getPurchasesAnalysisCount();
  /**
   * <pre>
   *猜你喜欢
   * </pre>
   *
   * <code>repeated .coupon.PurchasesAnalysis purchases_analysis = 24;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.PurchasesAnalysisOrBuilder> 
      getPurchasesAnalysisOrBuilderList();
  /**
   * <pre>
   *猜你喜欢
   * </pre>
   *
   * <code>repeated .coupon.PurchasesAnalysis purchases_analysis = 24;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.PurchasesAnalysisOrBuilder getPurchasesAnalysisOrBuilder(
      int index);

  /**
   * <pre>
   * 生日
   * </pre>
   *
   * <code>string birthday = 25;</code>
   * @return The birthday.
   */
  java.lang.String getBirthday();
  /**
   * <pre>
   * 生日
   * </pre>
   *
   * <code>string birthday = 25;</code>
   * @return The bytes for birthday.
   */
  com.google.protobuf.ByteString
      getBirthdayBytes();

  /**
   * <pre>
   * 积分抵扣金额
   * </pre>
   *
   * <code>string credit_deduction_amount = 26;</code>
   * @return The creditDeductionAmount.
   */
  java.lang.String getCreditDeductionAmount();
  /**
   * <pre>
   * 积分抵扣金额
   * </pre>
   *
   * <code>string credit_deduction_amount = 26;</code>
   * @return The bytes for creditDeductionAmount.
   */
  com.google.protobuf.ByteString
      getCreditDeductionAmountBytes();
}
