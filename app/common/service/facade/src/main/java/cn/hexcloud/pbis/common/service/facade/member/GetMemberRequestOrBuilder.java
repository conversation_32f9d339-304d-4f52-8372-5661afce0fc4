// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface GetMemberRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.GetMemberRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *  渠道编码，HEYTEAMEMBER(喜茶会员)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  java.lang.String getChannel();
  /**
   * <pre>
   *  渠道编码，HEYTEAMEMBER(喜茶会员)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  com.google.protobuf.ByteString
      getChannelBytes();

  /**
   * <pre>
   *  卡号(card_no、member_code、mobile 三选一)
   * </pre>
   *
   * <code>string card_no = 2;</code>
   * @return The cardNo.
   */
  java.lang.String getCardNo();
  /**
   * <pre>
   *  卡号(card_no、member_code、mobile 三选一)
   * </pre>
   *
   * <code>string card_no = 2;</code>
   * @return The bytes for cardNo.
   */
  com.google.protobuf.ByteString
      getCardNoBytes();

  /**
   * <pre>
   *  会员编码(card_no、member_code、mobile 三选一)
   * </pre>
   *
   * <code>string member_code = 3;</code>
   * @return The memberCode.
   */
  java.lang.String getMemberCode();
  /**
   * <pre>
   *  会员编码(card_no、member_code、mobile 三选一)
   * </pre>
   *
   * <code>string member_code = 3;</code>
   * @return The bytes for memberCode.
   */
  com.google.protobuf.ByteString
      getMemberCodeBytes();

  /**
   * <pre>
   *  手机号(card_no、member_code、mobile 三选一)
   * </pre>
   *
   * <code>string mobile = 4;</code>
   * @return The mobile.
   */
  java.lang.String getMobile();
  /**
   * <pre>
   *  手机号(card_no、member_code、mobile 三选一)
   * </pre>
   *
   * <code>string mobile = 4;</code>
   * @return The bytes for mobile.
   */
  com.google.protobuf.ByteString
      getMobileBytes();

  /**
   * <pre>
   *  敏感信息	密码、辅助码、二磁道信息等,格式- password=123&amp;CVN2=213&amp;expiration=2025/10/13
   * </pre>
   *
   * <code>string secret_content = 5;</code>
   * @return The secretContent.
   */
  java.lang.String getSecretContent();
  /**
   * <pre>
   *  敏感信息	密码、辅助码、二磁道信息等,格式- password=123&amp;CVN2=213&amp;expiration=2025/10/13
   * </pre>
   *
   * <code>string secret_content = 5;</code>
   * @return The bytes for secretContent.
   */
  com.google.protobuf.ByteString
      getSecretContentBytes();

  /**
   * <pre>
   * 门店id
   * </pre>
   *
   * <code>uint64 store_id = 6;</code>
   * @return The storeId.
   */
  long getStoreId();

  /**
   * <pre>
   * 门店partner id
   * </pre>
   *
   * <code>uint64 partner_id = 7;</code>
   * @return The partnerId.
   */
  long getPartnerId();

  /**
   * <pre>
   * 门店scope id，如果没有就传0
   * </pre>
   *
   * <code>uint64 scope_id = 8;</code>
   * @return The scopeId.
   */
  long getScopeId();

  /**
   * <pre>
   * 用户id
   * </pre>
   *
   * <code>uint64 user_id = 9;</code>
   * @return The userId.
   */
  long getUserId();

  /**
   * <pre>
   *  附加扩展信息
   * </pre>
   *
   * <code>string extend = 10;</code>
   * @return The extend.
   */
  java.lang.String getExtend();
  /**
   * <pre>
   *  附加扩展信息
   * </pre>
   *
   * <code>string extend = 10;</code>
   * @return The bytes for extend.
   */
  com.google.protobuf.ByteString
      getExtendBytes();

  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 11;</code>
   * @return Whether the orderContent field is set.
   */
  boolean hasOrderContent();
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 11;</code>
   * @return The orderContent.
   */
  cn.hexcloud.pbis.common.service.facade.member.OrderContent getOrderContent();
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 11;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.OrderContentOrBuilder getOrderContentOrBuilder();

  /**
   * <pre>
   * 门店code
   * </pre>
   *
   * <code>string store_code = 12;</code>
   * @return The storeCode.
   */
  java.lang.String getStoreCode();
  /**
   * <pre>
   * 门店code
   * </pre>
   *
   * <code>string store_code = 12;</code>
   * @return The bytes for storeCode.
   */
  com.google.protobuf.ByteString
      getStoreCodeBytes();

  /**
   * <pre>
   * 券码
   * </pre>
   *
   * <code>string coupon_no = 13;</code>
   * @return The couponNo.
   */
  java.lang.String getCouponNo();
  /**
   * <pre>
   * 券码
   * </pre>
   *
   * <code>string coupon_no = 13;</code>
   * @return The bytes for couponNo.
   */
  com.google.protobuf.ByteString
      getCouponNoBytes();
}
