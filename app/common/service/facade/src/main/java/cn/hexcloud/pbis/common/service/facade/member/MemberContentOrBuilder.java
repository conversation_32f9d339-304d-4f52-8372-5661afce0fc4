// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface MemberContentOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.MemberContent)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 卡号(card_no、member_code、mobile 三选一)
   * </pre>
   *
   * <code>string card_no = 1;</code>
   * @return The cardNo.
   */
  java.lang.String getCardNo();
  /**
   * <pre>
   * 卡号(card_no、member_code、mobile 三选一)
   * </pre>
   *
   * <code>string card_no = 1;</code>
   * @return The bytes for cardNo.
   */
  com.google.protobuf.ByteString
      getCardNoBytes();

  /**
   * <pre>
   * 会员编码(card_no、member_code、mobile 三选一)
   * </pre>
   *
   * <code>string member_code = 2;</code>
   * @return The memberCode.
   */
  java.lang.String getMemberCode();
  /**
   * <pre>
   * 会员编码(card_no、member_code、mobile 三选一)
   * </pre>
   *
   * <code>string member_code = 2;</code>
   * @return The bytes for memberCode.
   */
  com.google.protobuf.ByteString
      getMemberCodeBytes();

  /**
   * <pre>
   * 手机号(card_no、member_code、mobile 三选一)
   * </pre>
   *
   * <code>string mobile = 3;</code>
   * @return The mobile.
   */
  java.lang.String getMobile();
  /**
   * <pre>
   * 手机号(card_no、member_code、mobile 三选一)
   * </pre>
   *
   * <code>string mobile = 3;</code>
   * @return The bytes for mobile.
   */
  com.google.protobuf.ByteString
      getMobileBytes();

  /**
   * <pre>
   * 敏感信息     密码、辅助码、二磁道信息等,格式- password=123&amp;CVN2=213&amp;expiration=2025/10/13
   * </pre>
   *
   * <code>string secret_content = 4;</code>
   * @return The secretContent.
   */
  java.lang.String getSecretContent();
  /**
   * <pre>
   * 敏感信息     密码、辅助码、二磁道信息等,格式- password=123&amp;CVN2=213&amp;expiration=2025/10/13
   * </pre>
   *
   * <code>string secret_content = 4;</code>
   * @return The bytes for secretContent.
   */
  com.google.protobuf.ByteString
      getSecretContentBytes();
}
