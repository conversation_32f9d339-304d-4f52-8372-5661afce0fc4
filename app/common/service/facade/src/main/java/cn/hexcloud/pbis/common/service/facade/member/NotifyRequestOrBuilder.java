// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface NotifyRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.NotifyRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）渠道编码 SeltekTakeout(雪沥外卖)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  java.lang.String getChannel();
  /**
   * <pre>
   * （必传）渠道编码 SeltekTakeout(雪沥外卖)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  com.google.protobuf.ByteString
      getChannelBytes();

  /**
   * <pre>
   * 合阔订单号
   * </pre>
   *
   * <code>string ticketId = 2;</code>
   * @return The ticketId.
   */
  java.lang.String getTicketId();
  /**
   * <pre>
   * 合阔订单号
   * </pre>
   *
   * <code>string ticketId = 2;</code>
   * @return The bytes for ticketId.
   */
  com.google.protobuf.ByteString
      getTicketIdBytes();

  /**
   * <pre>
   * 第三订单号
   * </pre>
   *
   * <code>string tpOrderId = 3;</code>
   * @return The tpOrderId.
   */
  java.lang.String getTpOrderId();
  /**
   * <pre>
   * 第三订单号
   * </pre>
   *
   * <code>string tpOrderId = 3;</code>
   * @return The bytes for tpOrderId.
   */
  com.google.protobuf.ByteString
      getTpOrderIdBytes();

  /**
   * <pre>
   * 2 接单 3 已出餐 4 已完成 10 退款
   * </pre>
   *
   * <code>int32 status = 4;</code>
   * @return The status.
   */
  int getStatus();

  /**
   * <pre>
   * 取餐号
   * </pre>
   *
   * <code>string takeMealNumber = 5;</code>
   * @return The takeMealNumber.
   */
  java.lang.String getTakeMealNumber();
  /**
   * <pre>
   * 取餐号
   * </pre>
   *
   * <code>string takeMealNumber = 5;</code>
   * @return The bytes for takeMealNumber.
   */
  com.google.protobuf.ByteString
      getTakeMealNumberBytes();

  /**
   * <pre>
   * 预计等待时间：单位秒
   * </pre>
   *
   * <code>int32 waitingTime = 6;</code>
   * @return The waitingTime.
   */
  int getWaitingTime();

  /**
   * <pre>
   * 前面等待订单数
   * </pre>
   *
   * <code>int32 waitingOrderCount = 7;</code>
   * @return The waitingOrderCount.
   */
  int getWaitingOrderCount();

  /**
   * <pre>
   * 前面等待商品数
   * </pre>
   *
   * <code>int32 waitingProductCount = 8;</code>
   * @return The waitingProductCount.
   */
  int getWaitingProductCount();

  /**
   * <code>string tpName = 9;</code>
   * @return The tpName.
   */
  java.lang.String getTpName();
  /**
   * <code>string tpName = 9;</code>
   * @return The bytes for tpName.
   */
  com.google.protobuf.ByteString
      getTpNameBytes();

  /**
   * <code>int32 makeTime = 10;</code>
   * @return The makeTime.
   */
  int getMakeTime();

  /**
   * <code>string source = 11;</code>
   * @return The source.
   */
  java.lang.String getSource();
  /**
   * <code>string source = 11;</code>
   * @return The bytes for source.
   */
  com.google.protobuf.ByteString
      getSourceBytes();
}
