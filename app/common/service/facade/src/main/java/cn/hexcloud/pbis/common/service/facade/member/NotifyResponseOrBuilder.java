// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface NotifyResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.NotifyResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int32 statusCode = 1;</code>
   * @return The statusCode.
   */
  int getStatusCode();

  /**
   * <code>string code = 2;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <code>string code = 2;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <code>string description = 3;</code>
   * @return The description.
   */
  java.lang.String getDescription();
  /**
   * <code>string description = 3;</code>
   * @return The bytes for description.
   */
  com.google.protobuf.ByteString
      getDescriptionBytes();
}
