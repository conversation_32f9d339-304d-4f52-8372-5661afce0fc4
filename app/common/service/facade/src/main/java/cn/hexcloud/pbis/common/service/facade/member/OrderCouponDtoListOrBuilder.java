// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface OrderCouponDtoListOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.OrderCouponDtoList)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string couponInstanceCode = 1;</code>
   * @return The couponInstanceCode.
   */
  java.lang.String getCouponInstanceCode();
  /**
   * <code>string couponInstanceCode = 1;</code>
   * @return The bytes for couponInstanceCode.
   */
  com.google.protobuf.ByteString
      getCouponInstanceCodeBytes();

  /**
   * <code>uint64 useTimes = 2;</code>
   * @return The useTimes.
   */
  long getUseTimes();
}
