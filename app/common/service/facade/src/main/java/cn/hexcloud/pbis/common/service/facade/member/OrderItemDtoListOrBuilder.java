// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface OrderItemDtoListOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.OrderItemDtoList)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string orderItemName = 1;</code>
   * @return The orderItemName.
   */
  java.lang.String getOrderItemName();
  /**
   * <code>string orderItemName = 1;</code>
   * @return The bytes for orderItemName.
   */
  com.google.protobuf.ByteString
      getOrderItemNameBytes();

  /**
   * <code>string productCode = 2;</code>
   * @return The productCode.
   */
  java.lang.String getProductCode();
  /**
   * <code>string productCode = 2;</code>
   * @return The bytes for productCode.
   */
  com.google.protobuf.ByteString
      getProductCodeBytes();

  /**
   * <code>int32 quantity = 3;</code>
   * @return The quantity.
   */
  int getQuantity();

  /**
   * <code>double amount = 4;</code>
   * @return The amount.
   */
  double getAmount();
}
