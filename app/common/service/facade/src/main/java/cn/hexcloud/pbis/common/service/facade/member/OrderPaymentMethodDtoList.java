// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

/**
 * Protobuf type {@code coupon.OrderPaymentMethodDtoList}
 */
public final class OrderPaymentMethodDtoList extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:coupon.OrderPaymentMethodDtoList)
    OrderPaymentMethodDtoListOrBuilder {
private static final long serialVersionUID = 0L;
  // Use OrderPaymentMethodDtoList.newBuilder() to construct.
  private OrderPaymentMethodDtoList(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private OrderPaymentMethodDtoList() {
    payName_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new OrderPaymentMethodDtoList();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private OrderPaymentMethodDtoList(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            payName_ = s;
            break;
          }
          case 17: {

            payAmount_ = input.readDouble();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_OrderPaymentMethodDtoList_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_OrderPaymentMethodDtoList_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList.class, cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList.Builder.class);
  }

  public static final int PAYNAME_FIELD_NUMBER = 1;
  private volatile java.lang.Object payName_;
  /**
   * <code>string payName = 1;</code>
   * @return The payName.
   */
  @java.lang.Override
  public java.lang.String getPayName() {
    java.lang.Object ref = payName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      payName_ = s;
      return s;
    }
  }
  /**
   * <code>string payName = 1;</code>
   * @return The bytes for payName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPayNameBytes() {
    java.lang.Object ref = payName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      payName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PAYAMOUNT_FIELD_NUMBER = 2;
  private double payAmount_;
  /**
   * <code>double payAmount = 2;</code>
   * @return The payAmount.
   */
  @java.lang.Override
  public double getPayAmount() {
    return payAmount_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getPayNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, payName_);
    }
    if (payAmount_ != 0D) {
      output.writeDouble(2, payAmount_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getPayNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, payName_);
    }
    if (payAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(2, payAmount_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList other = (cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList) obj;

    if (!getPayName()
        .equals(other.getPayName())) return false;
    if (java.lang.Double.doubleToLongBits(getPayAmount())
        != java.lang.Double.doubleToLongBits(
            other.getPayAmount())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + PAYNAME_FIELD_NUMBER;
    hash = (53 * hash) + getPayName().hashCode();
    hash = (37 * hash) + PAYAMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPayAmount()));
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code coupon.OrderPaymentMethodDtoList}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:coupon.OrderPaymentMethodDtoList)
      cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoListOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_OrderPaymentMethodDtoList_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_OrderPaymentMethodDtoList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList.class, cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      payName_ = "";

      payAmount_ = 0D;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_OrderPaymentMethodDtoList_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList build() {
      cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList buildPartial() {
      cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList result = new cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList(this);
      result.payName_ = payName_;
      result.payAmount_ = payAmount_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList other) {
      if (other == cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList.getDefaultInstance()) return this;
      if (!other.getPayName().isEmpty()) {
        payName_ = other.payName_;
        onChanged();
      }
      if (other.getPayAmount() != 0D) {
        setPayAmount(other.getPayAmount());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object payName_ = "";
    /**
     * <code>string payName = 1;</code>
     * @return The payName.
     */
    public java.lang.String getPayName() {
      java.lang.Object ref = payName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        payName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string payName = 1;</code>
     * @return The bytes for payName.
     */
    public com.google.protobuf.ByteString
        getPayNameBytes() {
      java.lang.Object ref = payName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string payName = 1;</code>
     * @param value The payName to set.
     * @return This builder for chaining.
     */
    public Builder setPayName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      payName_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string payName = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayName() {
      
      payName_ = getDefaultInstance().getPayName();
      onChanged();
      return this;
    }
    /**
     * <code>string payName = 1;</code>
     * @param value The bytes for payName to set.
     * @return This builder for chaining.
     */
    public Builder setPayNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      payName_ = value;
      onChanged();
      return this;
    }

    private double payAmount_ ;
    /**
     * <code>double payAmount = 2;</code>
     * @return The payAmount.
     */
    @java.lang.Override
    public double getPayAmount() {
      return payAmount_;
    }
    /**
     * <code>double payAmount = 2;</code>
     * @param value The payAmount to set.
     * @return This builder for chaining.
     */
    public Builder setPayAmount(double value) {
      
      payAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double payAmount = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayAmount() {
      
      payAmount_ = 0D;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:coupon.OrderPaymentMethodDtoList)
  }

  // @@protoc_insertion_point(class_scope:coupon.OrderPaymentMethodDtoList)
  private static final cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList();
  }

  public static cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<OrderPaymentMethodDtoList>
      PARSER = new com.google.protobuf.AbstractParser<OrderPaymentMethodDtoList>() {
    @java.lang.Override
    public OrderPaymentMethodDtoList parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new OrderPaymentMethodDtoList(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<OrderPaymentMethodDtoList> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<OrderPaymentMethodDtoList> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

