// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface OrderPaymentMethodDtoListOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.OrderPaymentMethodDtoList)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string payName = 1;</code>
   * @return The payName.
   */
  java.lang.String getPayName();
  /**
   * <code>string payName = 1;</code>
   * @return The bytes for payName.
   */
  com.google.protobuf.ByteString
      getPayNameBytes();

  /**
   * <code>double payAmount = 2;</code>
   * @return The payAmount.
   */
  double getPayAmount();
}
