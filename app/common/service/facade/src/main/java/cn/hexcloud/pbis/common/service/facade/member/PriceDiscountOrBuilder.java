// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface PriceDiscountOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.PriceDiscount)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 折扣类型名称
   * </pre>
   *
   * <code>string type = 1;</code>
   * @return The type.
   */
  java.lang.String getType();
  /**
   * <pre>
   * 折扣类型名称
   * </pre>
   *
   * <code>string type = 1;</code>
   * @return The bytes for type.
   */
  com.google.protobuf.ByteString
      getTypeBytes();

  /**
   * <pre>
   * 折扣类型
   * </pre>
   *
   * <code>string discount_type = 2;</code>
   * @return The discountType.
   */
  java.lang.String getDiscountType();
  /**
   * <pre>
   * 折扣类型
   * </pre>
   *
   * <code>string discount_type = 2;</code>
   * @return The bytes for discountType.
   */
  com.google.protobuf.ByteString
      getDiscountTypeBytes();

  /**
   * <pre>
   * 折扣金额
   * </pre>
   *
   * <code>string discount = 3;</code>
   * @return The discount.
   */
  java.lang.String getDiscount();
  /**
   * <pre>
   * 折扣金额
   * </pre>
   *
   * <code>string discount = 3;</code>
   * @return The bytes for discount.
   */
  com.google.protobuf.ByteString
      getDiscountBytes();

  /**
   * <pre>
   * 促销名称
   * </pre>
   *
   * <code>string name = 4;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   * 促销名称
   * </pre>
   *
   * <code>string name = 4;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * 促销id
   * </pre>
   *
   * <code>string promotion_id = 5;</code>
   * @return The promotionId.
   */
  java.lang.String getPromotionId();
  /**
   * <pre>
   * 促销id
   * </pre>
   *
   * <code>string promotion_id = 5;</code>
   * @return The bytes for promotionId.
   */
  com.google.protobuf.ByteString
      getPromotionIdBytes();

  /**
   * <pre>
   * 促销编号
   * </pre>
   *
   * <code>string promotion_code = 6;</code>
   * @return The promotionCode.
   */
  java.lang.String getPromotionCode();
  /**
   * <pre>
   * 促销编号
   * </pre>
   *
   * <code>string promotion_code = 6;</code>
   * @return The bytes for promotionCode.
   */
  com.google.protobuf.ByteString
      getPromotionCodeBytes();

  /**
   * <pre>
   * 促销类型（NORMAL:普通促销，MEMBER:会员促销，COUPON:卡券促销）
   * </pre>
   *
   * <code>string promotion_type = 7;</code>
   * @return The promotionType.
   */
  java.lang.String getPromotionType();
  /**
   * <pre>
   * 促销类型（NORMAL:普通促销，MEMBER:会员促销，COUPON:卡券促销）
   * </pre>
   *
   * <code>string promotion_type = 7;</code>
   * @return The bytes for promotionType.
   */
  com.google.protobuf.ByteString
      getPromotionTypeBytes();

  /**
   * <pre>
   * 是否允许折上折
   * </pre>
   *
   * <code>bool allow_overlap = 8;</code>
   * @return The allowOverlap.
   */
  boolean getAllowOverlap();

  /**
   * <pre>
   * 显示在小票上的促销名称
   * </pre>
   *
   * <code>string ticket_display = 9;</code>
   * @return The ticketDisplay.
   */
  java.lang.String getTicketDisplay();
  /**
   * <pre>
   * 显示在小票上的促销名称
   * </pre>
   *
   * <code>string ticket_display = 9;</code>
   * @return The bytes for ticketDisplay.
   */
  com.google.protobuf.ByteString
      getTicketDisplayBytes();

  /**
   * <pre>
   * 是否允许多次
   * </pre>
   *
   * <code>bool trigger_times_custom = 10;</code>
   * @return The triggerTimesCustom.
   */
  boolean getTriggerTimesCustom();

  /**
   * <pre>
   * 参与折扣的商品
   * </pre>
   *
   * <code>repeated string fired = 11;</code>
   * @return A list containing the fired.
   */
  java.util.List<java.lang.String>
      getFiredList();
  /**
   * <pre>
   * 参与折扣的商品
   * </pre>
   *
   * <code>repeated string fired = 11;</code>
   * @return The count of fired.
   */
  int getFiredCount();
  /**
   * <pre>
   * 参与折扣的商品
   * </pre>
   *
   * <code>repeated string fired = 11;</code>
   * @param index The index of the element to return.
   * @return The fired at the given index.
   */
  java.lang.String getFired(int index);
  /**
   * <pre>
   * 参与折扣的商品
   * </pre>
   *
   * <code>repeated string fired = 11;</code>
   * @param index The index of the value to return.
   * @return The bytes of the fired at the given index.
   */
  com.google.protobuf.ByteString
      getFiredBytes(int index);

  /**
   * <pre>
   * 分摊信息
   * </pre>
   *
   * <code>repeated .coupon.AbsorbedExpenses product = 12;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.member.AbsorbedExpenses> 
      getProductList();
  /**
   * <pre>
   * 分摊信息
   * </pre>
   *
   * <code>repeated .coupon.AbsorbedExpenses product = 12;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.AbsorbedExpenses getProduct(int index);
  /**
   * <pre>
   * 分摊信息
   * </pre>
   *
   * <code>repeated .coupon.AbsorbedExpenses product = 12;</code>
   */
  int getProductCount();
  /**
   * <pre>
   * 分摊信息
   * </pre>
   *
   * <code>repeated .coupon.AbsorbedExpenses product = 12;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.AbsorbedExpensesOrBuilder> 
      getProductOrBuilderList();
  /**
   * <pre>
   * 分摊信息
   * </pre>
   *
   * <code>repeated .coupon.AbsorbedExpenses product = 12;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.AbsorbedExpensesOrBuilder getProductOrBuilder(
      int index);

  /**
   * <pre>
   * 优惠券模版ID
   * </pre>
   *
   * <code>string promotion_template_id = 13;</code>
   * @return The promotionTemplateId.
   */
  java.lang.String getPromotionTemplateId();
  /**
   * <pre>
   * 优惠券模版ID
   * </pre>
   *
   * <code>string promotion_template_id = 13;</code>
   * @return The bytes for promotionTemplateId.
   */
  com.google.protobuf.ByteString
      getPromotionTemplateIdBytes();
}
