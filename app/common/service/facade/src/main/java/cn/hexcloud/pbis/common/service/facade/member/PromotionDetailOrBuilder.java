// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface PromotionDetailOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.PromotionDetail)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 促销id
   * </pre>
   *
   * <code>string promotion_id = 1;</code>
   * @return The promotionId.
   */
  java.lang.String getPromotionId();
  /**
   * <pre>
   * 促销id
   * </pre>
   *
   * <code>string promotion_id = 1;</code>
   * @return The bytes for promotionId.
   */
  com.google.protobuf.ByteString
      getPromotionIdBytes();

  /**
   * <pre>
   * 1 优惠券 2优惠活动
   * </pre>
   *
   * <code>int32 promotion_type = 2;</code>
   * @return The promotionType.
   */
  int getPromotionType();
}
