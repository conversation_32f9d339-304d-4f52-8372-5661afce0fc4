// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface PurchasesAnalysisOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.PurchasesAnalysis)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string code = 1;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <code>string code = 1;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <code>string name = 2;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();
}
