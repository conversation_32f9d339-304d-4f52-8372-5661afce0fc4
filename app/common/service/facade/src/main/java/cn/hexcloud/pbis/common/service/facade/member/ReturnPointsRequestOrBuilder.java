// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface ReturnPointsRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.ReturnPointsRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 渠道编码(必传)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  java.lang.String getChannel();
  /**
   * <pre>
   * 渠道编码(必传)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  com.google.protobuf.ByteString
      getChannelBytes();

  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * <code>.coupon.MemberContent member_content = 2;</code>
   * @return Whether the memberContent field is set.
   */
  boolean hasMemberContent();
  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * <code>.coupon.MemberContent member_content = 2;</code>
   * @return The memberContent.
   */
  cn.hexcloud.pbis.common.service.facade.member.MemberContent getMemberContent();
  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * <code>.coupon.MemberContent member_content = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.MemberContentOrBuilder getMemberContentOrBuilder();

  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 3;</code>
   * @return Whether the orderContent field is set.
   */
  boolean hasOrderContent();
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 3;</code>
   * @return The orderContent.
   */
  cn.hexcloud.pbis.common.service.facade.member.OrderContent getOrderContent();
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * <code>.coupon.OrderContent order_content = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.OrderContentOrBuilder getOrderContentOrBuilder();

  /**
   * <pre>
   * 使用积分(分单位)
   * </pre>
   *
   * <code>int32 point = 4;</code>
   * @return The point.
   */
  int getPoint();

  /**
   * <pre>
   * 积分抵现金额(分单位)
   * </pre>
   *
   * <code>int32 amount = 5;</code>
   * @return The amount.
   */
  int getAmount();

  /**
   * <pre>
   * 操作场景 true：积分抵现（默认）；false：积分更新
   * </pre>
   *
   * <code>bool use_point = 6;</code>
   * @return The usePoint.
   */
  boolean getUsePoint();

  /**
   * <pre>
   * 支付时传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string batch_ticket_id = 7;</code>
   * @return The batchTicketId.
   */
  java.lang.String getBatchTicketId();
  /**
   * <pre>
   * 支付时传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string batch_ticket_id = 7;</code>
   * @return The bytes for batchTicketId.
   */
  com.google.protobuf.ByteString
      getBatchTicketIdBytes();
}
