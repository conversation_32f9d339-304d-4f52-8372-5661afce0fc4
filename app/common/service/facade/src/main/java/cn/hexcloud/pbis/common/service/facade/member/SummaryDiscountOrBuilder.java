// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface SummaryDiscountOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.SummaryDiscount)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *订单总金额
   * </pre>
   *
   * <code>string subTotal = 1;</code>
   * @return The subTotal.
   */
  java.lang.String getSubTotal();
  /**
   * <pre>
   *订单总金额
   * </pre>
   *
   * <code>string subTotal = 1;</code>
   * @return The bytes for subTotal.
   */
  com.google.protobuf.ByteString
      getSubTotalBytes();

  /**
   * <pre>
   *折扣后金额
   * </pre>
   *
   * <code>string grantTotal = 2;</code>
   * @return The grantTotal.
   */
  java.lang.String getGrantTotal();
  /**
   * <pre>
   *折扣后金额
   * </pre>
   *
   * <code>string grantTotal = 2;</code>
   * @return The bytes for grantTotal.
   */
  com.google.protobuf.ByteString
      getGrantTotalBytes();

  /**
   * <pre>
   *折扣总金额
   * </pre>
   *
   * <code>string discount = 3;</code>
   * @return The discount.
   */
  java.lang.String getDiscount();
  /**
   * <pre>
   *折扣总金额
   * </pre>
   *
   * <code>string discount = 3;</code>
   * @return The bytes for discount.
   */
  com.google.protobuf.ByteString
      getDiscountBytes();
}
