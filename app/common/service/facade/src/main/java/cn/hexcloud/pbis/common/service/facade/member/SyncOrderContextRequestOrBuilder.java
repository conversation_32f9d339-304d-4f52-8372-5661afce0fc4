// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface SyncOrderContextRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.SyncOrderContextRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>.coupon.Ticket ticket = 1;</code>
   * @return Whether the ticket field is set.
   */
  boolean hasTicket();
  /**
   * <code>.coupon.Ticket ticket = 1;</code>
   * @return The ticket.
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Ticket getTicket();
  /**
   * <code>.coupon.Ticket ticket = 1;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.TicketOrBuilder getTicketOrBuilder();
}
