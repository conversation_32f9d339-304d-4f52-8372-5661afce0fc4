// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface SyncOrderRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.SyncOrderRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  java.lang.String getChannel();
  /**
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  com.google.protobuf.ByteString
      getChannelBytes();

  /**
   * <code>string memberCode = 2;</code>
   * @return The memberCode.
   */
  java.lang.String getMemberCode();
  /**
   * <code>string memberCode = 2;</code>
   * @return The bytes for memberCode.
   */
  com.google.protobuf.ByteString
      getMemberCodeBytes();

  /**
   * <code>string externalId = 3;</code>
   * @return The externalId.
   */
  java.lang.String getExternalId();
  /**
   * <code>string externalId = 3;</code>
   * @return The bytes for externalId.
   */
  com.google.protobuf.ByteString
      getExternalIdBytes();

  /**
   * <code>int64 channelId = 4;</code>
   * @return The channelId.
   */
  long getChannelId();

  /**
   * <code>string storeCode = 5;</code>
   * @return The storeCode.
   */
  java.lang.String getStoreCode();
  /**
   * <code>string storeCode = 5;</code>
   * @return The bytes for storeCode.
   */
  com.google.protobuf.ByteString
      getStoreCodeBytes();

  /**
   * <code>string orderDate = 6;</code>
   * @return The orderDate.
   */
  java.lang.String getOrderDate();
  /**
   * <code>string orderDate = 6;</code>
   * @return The bytes for orderDate.
   */
  com.google.protobuf.ByteString
      getOrderDateBytes();

  /**
   * <code>string createTime = 7;</code>
   * @return The createTime.
   */
  java.lang.String getCreateTime();
  /**
   * <code>string createTime = 7;</code>
   * @return The bytes for createTime.
   */
  com.google.protobuf.ByteString
      getCreateTimeBytes();

  /**
   * <code>double orderAmount = 8;</code>
   * @return The orderAmount.
   */
  double getOrderAmount();

  /**
   * <code>int32 orderType = 9;</code>
   * @return The orderType.
   */
  int getOrderType();

  /**
   * <code>double payAmount = 10;</code>
   * @return The payAmount.
   */
  double getPayAmount();

  /**
   * <code>double couponAmount = 11;</code>
   * @return The couponAmount.
   */
  double getCouponAmount();

  /**
   * <code>string optType = 12;</code>
   * @return The optType.
   */
  java.lang.String getOptType();
  /**
   * <code>string optType = 12;</code>
   * @return The bytes for optType.
   */
  com.google.protobuf.ByteString
      getOptTypeBytes();

  /**
   * <code>double usedPoints = 13;</code>
   * @return The usedPoints.
   */
  double getUsedPoints();

  /**
   * <code>string extTypeId = 14;</code>
   * @return The extTypeId.
   */
  java.lang.String getExtTypeId();
  /**
   * <code>string extTypeId = 14;</code>
   * @return The bytes for extTypeId.
   */
  com.google.protobuf.ByteString
      getExtTypeIdBytes();

  /**
   * <code>string bindingAccount = 15;</code>
   * @return The bindingAccount.
   */
  java.lang.String getBindingAccount();
  /**
   * <code>string bindingAccount = 15;</code>
   * @return The bytes for bindingAccount.
   */
  com.google.protobuf.ByteString
      getBindingAccountBytes();

  /**
   * <code>repeated .coupon.OrderItemDtoList orderItemDtoList = 16;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.member.OrderItemDtoList> 
      getOrderItemDtoListList();
  /**
   * <code>repeated .coupon.OrderItemDtoList orderItemDtoList = 16;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.OrderItemDtoList getOrderItemDtoList(int index);
  /**
   * <code>repeated .coupon.OrderItemDtoList orderItemDtoList = 16;</code>
   */
  int getOrderItemDtoListCount();
  /**
   * <code>repeated .coupon.OrderItemDtoList orderItemDtoList = 16;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.OrderItemDtoListOrBuilder> 
      getOrderItemDtoListOrBuilderList();
  /**
   * <code>repeated .coupon.OrderItemDtoList orderItemDtoList = 16;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.OrderItemDtoListOrBuilder getOrderItemDtoListOrBuilder(
      int index);

  /**
   * <code>repeated .coupon.OrderPaymentMethodDtoList orderPaymentMethodDtoList = 17;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList> 
      getOrderPaymentMethodDtoListList();
  /**
   * <code>repeated .coupon.OrderPaymentMethodDtoList orderPaymentMethodDtoList = 17;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoList getOrderPaymentMethodDtoList(int index);
  /**
   * <code>repeated .coupon.OrderPaymentMethodDtoList orderPaymentMethodDtoList = 17;</code>
   */
  int getOrderPaymentMethodDtoListCount();
  /**
   * <code>repeated .coupon.OrderPaymentMethodDtoList orderPaymentMethodDtoList = 17;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoListOrBuilder> 
      getOrderPaymentMethodDtoListOrBuilderList();
  /**
   * <code>repeated .coupon.OrderPaymentMethodDtoList orderPaymentMethodDtoList = 17;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.OrderPaymentMethodDtoListOrBuilder getOrderPaymentMethodDtoListOrBuilder(
      int index);

  /**
   * <code>repeated .coupon.OrderCouponDtoList orderCouponDtoList = 18;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.member.OrderCouponDtoList> 
      getOrderCouponDtoListList();
  /**
   * <code>repeated .coupon.OrderCouponDtoList orderCouponDtoList = 18;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.OrderCouponDtoList getOrderCouponDtoList(int index);
  /**
   * <code>repeated .coupon.OrderCouponDtoList orderCouponDtoList = 18;</code>
   */
  int getOrderCouponDtoListCount();
  /**
   * <code>repeated .coupon.OrderCouponDtoList orderCouponDtoList = 18;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.OrderCouponDtoListOrBuilder> 
      getOrderCouponDtoListOrBuilderList();
  /**
   * <code>repeated .coupon.OrderCouponDtoList orderCouponDtoList = 18;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.OrderCouponDtoListOrBuilder getOrderCouponDtoListOrBuilder(
      int index);
}
