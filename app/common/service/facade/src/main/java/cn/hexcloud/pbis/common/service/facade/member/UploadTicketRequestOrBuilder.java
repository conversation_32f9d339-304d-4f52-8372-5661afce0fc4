// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface UploadTicketRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.UploadTicketRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）完整的ticket信息
   * </pre>
   *
   * <code>.coupon.Ticket ticket = 1;</code>
   * @return Whether the ticket field is set.
   */
  boolean hasTicket();
  /**
   * <pre>
   * （必传）完整的ticket信息
   * </pre>
   *
   * <code>.coupon.Ticket ticket = 1;</code>
   * @return The ticket.
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Ticket getTicket();
  /**
   * <pre>
   * （必传）完整的ticket信息
   * </pre>
   *
   * <code>.coupon.Ticket ticket = 1;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.TicketOrBuilder getTicketOrBuilder();

  /**
   * <code>string channel = 2;</code>
   * @return The channel.
   */
  java.lang.String getChannel();
  /**
   * <code>string channel = 2;</code>
   * @return The bytes for channel.
   */
  com.google.protobuf.ByteString
      getChannelBytes();
}
