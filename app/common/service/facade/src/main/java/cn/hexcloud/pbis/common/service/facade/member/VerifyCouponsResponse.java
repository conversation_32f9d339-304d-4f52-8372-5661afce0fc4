// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

/**
 * <pre>
 * 【卡券验证】结果
 * </pre>
 *
 * Protobuf type {@code coupon.VerifyCouponsResponse}
 */
public final class VerifyCouponsResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:coupon.VerifyCouponsResponse)
    VerifyCouponsResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use VerifyCouponsResponse.newBuilder() to construct.
  private VerifyCouponsResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private VerifyCouponsResponse() {
    channel_ = "";
    errorCode_ = "";
    message_ = "";
    responseCode_ = "";
    responseContent_ = "";
    details_ = java.util.Collections.emptyList();
    consumeToken_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new VerifyCouponsResponse();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private VerifyCouponsResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            channel_ = s;
            break;
          }
          case 16: {

            success_ = input.readBool();
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            errorCode_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            message_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            responseCode_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            responseContent_ = s;
            break;
          }
          case 58: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              details_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.member.Coupon>();
              mutable_bitField0_ |= 0x00000001;
            }
            details_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.member.Coupon.parser(), extensionRegistry));
            break;
          }
          case 66: {
            java.lang.String s = input.readStringRequireUtf8();

            consumeToken_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        details_ = java.util.Collections.unmodifiableList(details_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_VerifyCouponsResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_VerifyCouponsResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse.class, cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse.Builder.class);
  }

  public static final int CHANNEL_FIELD_NUMBER = 1;
  private volatile java.lang.Object channel_;
  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  @java.lang.Override
  public java.lang.String getChannel() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channel_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelBytes() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SUCCESS_FIELD_NUMBER = 2;
  private boolean success_;
  /**
   * <pre>
   * 是否验证成功
   * </pre>
   *
   * <code>bool success = 2;</code>
   * @return The success.
   */
  @java.lang.Override
  public boolean getSuccess() {
    return success_;
  }

  public static final int ERROR_CODE_FIELD_NUMBER = 3;
  private volatile java.lang.Object errorCode_;
  /**
   * <pre>
   * 异常编码，查看交易接口异常返回
   * </pre>
   *
   * <code>string error_code = 3;</code>
   * @return The errorCode.
   */
  @java.lang.Override
  public java.lang.String getErrorCode() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 异常编码，查看交易接口异常返回
   * </pre>
   *
   * <code>string error_code = 3;</code>
   * @return The bytes for errorCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorCodeBytes() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MESSAGE_FIELD_NUMBER = 4;
  private volatile java.lang.Object message_;
  /**
   * <pre>
   * 异常信息
   * </pre>
   *
   * <code>string message = 4;</code>
   * @return The message.
   */
  @java.lang.Override
  public java.lang.String getMessage() {
    java.lang.Object ref = message_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      message_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 异常信息
   * </pre>
   *
   * <code>string message = 4;</code>
   * @return The bytes for message.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMessageBytes() {
    java.lang.Object ref = message_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      message_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RESPONSE_CODE_FIELD_NUMBER = 5;
  private volatile java.lang.Object responseCode_;
  /**
   * <pre>
   * 第三方编码
   * </pre>
   *
   * <code>string response_code = 5;</code>
   * @return The responseCode.
   */
  @java.lang.Override
  public java.lang.String getResponseCode() {
    java.lang.Object ref = responseCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      responseCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 第三方编码
   * </pre>
   *
   * <code>string response_code = 5;</code>
   * @return The bytes for responseCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getResponseCodeBytes() {
    java.lang.Object ref = responseCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      responseCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RESPONSE_CONTENT_FIELD_NUMBER = 6;
  private volatile java.lang.Object responseContent_;
  /**
   * <pre>
   * 第三方报文(500字符以内)
   * </pre>
   *
   * <code>string response_content = 6;</code>
   * @return The responseContent.
   */
  @java.lang.Override
  public java.lang.String getResponseContent() {
    java.lang.Object ref = responseContent_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      responseContent_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 第三方报文(500字符以内)
   * </pre>
   *
   * <code>string response_content = 6;</code>
   * @return The bytes for responseContent.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getResponseContentBytes() {
    java.lang.Object ref = responseContent_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      responseContent_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DETAILS_FIELD_NUMBER = 7;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.member.Coupon> details_;
  /**
   * <pre>
   * 卡券信息
   * </pre>
   *
   * <code>repeated .coupon.Coupon details = 7;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.member.Coupon> getDetailsList() {
    return details_;
  }
  /**
   * <pre>
   * 卡券信息
   * </pre>
   *
   * <code>repeated .coupon.Coupon details = 7;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.CouponOrBuilder> 
      getDetailsOrBuilderList() {
    return details_;
  }
  /**
   * <pre>
   * 卡券信息
   * </pre>
   *
   * <code>repeated .coupon.Coupon details = 7;</code>
   */
  @java.lang.Override
  public int getDetailsCount() {
    return details_.size();
  }
  /**
   * <pre>
   * 卡券信息
   * </pre>
   *
   * <code>repeated .coupon.Coupon details = 7;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.Coupon getDetails(int index) {
    return details_.get(index);
  }
  /**
   * <pre>
   * 卡券信息
   * </pre>
   *
   * <code>repeated .coupon.Coupon details = 7;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.CouponOrBuilder getDetailsOrBuilder(
      int index) {
    return details_.get(index);
  }

  public static final int CONSUME_TOKEN_FIELD_NUMBER = 8;
  private volatile java.lang.Object consumeToken_;
  /**
   * <pre>
   * 券核销凭证
   * </pre>
   *
   * <code>string consume_token = 8;</code>
   * @return The consumeToken.
   */
  @java.lang.Override
  public java.lang.String getConsumeToken() {
    java.lang.Object ref = consumeToken_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      consumeToken_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 券核销凭证
   * </pre>
   *
   * <code>string consume_token = 8;</code>
   * @return The bytes for consumeToken.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getConsumeTokenBytes() {
    java.lang.Object ref = consumeToken_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      consumeToken_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getChannelBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channel_);
    }
    if (success_ != false) {
      output.writeBool(2, success_);
    }
    if (!getErrorCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, errorCode_);
    }
    if (!getMessageBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, message_);
    }
    if (!getResponseCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, responseCode_);
    }
    if (!getResponseContentBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, responseContent_);
    }
    for (int i = 0; i < details_.size(); i++) {
      output.writeMessage(7, details_.get(i));
    }
    if (!getConsumeTokenBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, consumeToken_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getChannelBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channel_);
    }
    if (success_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(2, success_);
    }
    if (!getErrorCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, errorCode_);
    }
    if (!getMessageBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, message_);
    }
    if (!getResponseCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, responseCode_);
    }
    if (!getResponseContentBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, responseContent_);
    }
    for (int i = 0; i < details_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, details_.get(i));
    }
    if (!getConsumeTokenBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, consumeToken_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse other = (cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse) obj;

    if (!getChannel()
        .equals(other.getChannel())) return false;
    if (getSuccess()
        != other.getSuccess()) return false;
    if (!getErrorCode()
        .equals(other.getErrorCode())) return false;
    if (!getMessage()
        .equals(other.getMessage())) return false;
    if (!getResponseCode()
        .equals(other.getResponseCode())) return false;
    if (!getResponseContent()
        .equals(other.getResponseContent())) return false;
    if (!getDetailsList()
        .equals(other.getDetailsList())) return false;
    if (!getConsumeToken()
        .equals(other.getConsumeToken())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
    hash = (53 * hash) + getChannel().hashCode();
    hash = (37 * hash) + SUCCESS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getSuccess());
    hash = (37 * hash) + ERROR_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorCode().hashCode();
    hash = (37 * hash) + MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getMessage().hashCode();
    hash = (37 * hash) + RESPONSE_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getResponseCode().hashCode();
    hash = (37 * hash) + RESPONSE_CONTENT_FIELD_NUMBER;
    hash = (53 * hash) + getResponseContent().hashCode();
    if (getDetailsCount() > 0) {
      hash = (37 * hash) + DETAILS_FIELD_NUMBER;
      hash = (53 * hash) + getDetailsList().hashCode();
    }
    hash = (37 * hash) + CONSUME_TOKEN_FIELD_NUMBER;
    hash = (53 * hash) + getConsumeToken().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 【卡券验证】结果
   * </pre>
   *
   * Protobuf type {@code coupon.VerifyCouponsResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:coupon.VerifyCouponsResponse)
      cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_VerifyCouponsResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_VerifyCouponsResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse.class, cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getDetailsFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channel_ = "";

      success_ = false;

      errorCode_ = "";

      message_ = "";

      responseCode_ = "";

      responseContent_ = "";

      if (detailsBuilder_ == null) {
        details_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        detailsBuilder_.clear();
      }
      consumeToken_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.member.CouponOuterClass.internal_static_coupon_VerifyCouponsResponse_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse build() {
      cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse buildPartial() {
      cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse result = new cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse(this);
      int from_bitField0_ = bitField0_;
      result.channel_ = channel_;
      result.success_ = success_;
      result.errorCode_ = errorCode_;
      result.message_ = message_;
      result.responseCode_ = responseCode_;
      result.responseContent_ = responseContent_;
      if (detailsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          details_ = java.util.Collections.unmodifiableList(details_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.details_ = details_;
      } else {
        result.details_ = detailsBuilder_.build();
      }
      result.consumeToken_ = consumeToken_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse other) {
      if (other == cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse.getDefaultInstance()) return this;
      if (!other.getChannel().isEmpty()) {
        channel_ = other.channel_;
        onChanged();
      }
      if (other.getSuccess() != false) {
        setSuccess(other.getSuccess());
      }
      if (!other.getErrorCode().isEmpty()) {
        errorCode_ = other.errorCode_;
        onChanged();
      }
      if (!other.getMessage().isEmpty()) {
        message_ = other.message_;
        onChanged();
      }
      if (!other.getResponseCode().isEmpty()) {
        responseCode_ = other.responseCode_;
        onChanged();
      }
      if (!other.getResponseContent().isEmpty()) {
        responseContent_ = other.responseContent_;
        onChanged();
      }
      if (detailsBuilder_ == null) {
        if (!other.details_.isEmpty()) {
          if (details_.isEmpty()) {
            details_ = other.details_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureDetailsIsMutable();
            details_.addAll(other.details_);
          }
          onChanged();
        }
      } else {
        if (!other.details_.isEmpty()) {
          if (detailsBuilder_.isEmpty()) {
            detailsBuilder_.dispose();
            detailsBuilder_ = null;
            details_ = other.details_;
            bitField0_ = (bitField0_ & ~0x00000001);
            detailsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getDetailsFieldBuilder() : null;
          } else {
            detailsBuilder_.addAllMessages(other.details_);
          }
        }
      }
      if (!other.getConsumeToken().isEmpty()) {
        consumeToken_ = other.consumeToken_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object channel_ = "";
    /**
     * <pre>
     * 渠道编码
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return The channel.
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channel_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 渠道编码
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return The bytes for channel.
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 渠道编码
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @param value The channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannel(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channel_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道编码
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannel() {
      
      channel_ = getDefaultInstance().getChannel();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 渠道编码
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @param value The bytes for channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channel_ = value;
      onChanged();
      return this;
    }

    private boolean success_ ;
    /**
     * <pre>
     * 是否验证成功
     * </pre>
     *
     * <code>bool success = 2;</code>
     * @return The success.
     */
    @java.lang.Override
    public boolean getSuccess() {
      return success_;
    }
    /**
     * <pre>
     * 是否验证成功
     * </pre>
     *
     * <code>bool success = 2;</code>
     * @param value The success to set.
     * @return This builder for chaining.
     */
    public Builder setSuccess(boolean value) {
      
      success_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 是否验证成功
     * </pre>
     *
     * <code>bool success = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSuccess() {
      
      success_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object errorCode_ = "";
    /**
     * <pre>
     * 异常编码，查看交易接口异常返回
     * </pre>
     *
     * <code>string error_code = 3;</code>
     * @return The errorCode.
     */
    public java.lang.String getErrorCode() {
      java.lang.Object ref = errorCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 异常编码，查看交易接口异常返回
     * </pre>
     *
     * <code>string error_code = 3;</code>
     * @return The bytes for errorCode.
     */
    public com.google.protobuf.ByteString
        getErrorCodeBytes() {
      java.lang.Object ref = errorCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 异常编码，查看交易接口异常返回
     * </pre>
     *
     * <code>string error_code = 3;</code>
     * @param value The errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 异常编码，查看交易接口异常返回
     * </pre>
     *
     * <code>string error_code = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorCode() {
      
      errorCode_ = getDefaultInstance().getErrorCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 异常编码，查看交易接口异常返回
     * </pre>
     *
     * <code>string error_code = 3;</code>
     * @param value The bytes for errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object message_ = "";
    /**
     * <pre>
     * 异常信息
     * </pre>
     *
     * <code>string message = 4;</code>
     * @return The message.
     */
    public java.lang.String getMessage() {
      java.lang.Object ref = message_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        message_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 异常信息
     * </pre>
     *
     * <code>string message = 4;</code>
     * @return The bytes for message.
     */
    public com.google.protobuf.ByteString
        getMessageBytes() {
      java.lang.Object ref = message_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        message_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 异常信息
     * </pre>
     *
     * <code>string message = 4;</code>
     * @param value The message to set.
     * @return This builder for chaining.
     */
    public Builder setMessage(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      message_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 异常信息
     * </pre>
     *
     * <code>string message = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearMessage() {
      
      message_ = getDefaultInstance().getMessage();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 异常信息
     * </pre>
     *
     * <code>string message = 4;</code>
     * @param value The bytes for message to set.
     * @return This builder for chaining.
     */
    public Builder setMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      message_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object responseCode_ = "";
    /**
     * <pre>
     * 第三方编码
     * </pre>
     *
     * <code>string response_code = 5;</code>
     * @return The responseCode.
     */
    public java.lang.String getResponseCode() {
      java.lang.Object ref = responseCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        responseCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 第三方编码
     * </pre>
     *
     * <code>string response_code = 5;</code>
     * @return The bytes for responseCode.
     */
    public com.google.protobuf.ByteString
        getResponseCodeBytes() {
      java.lang.Object ref = responseCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        responseCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 第三方编码
     * </pre>
     *
     * <code>string response_code = 5;</code>
     * @param value The responseCode to set.
     * @return This builder for chaining.
     */
    public Builder setResponseCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      responseCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方编码
     * </pre>
     *
     * <code>string response_code = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearResponseCode() {
      
      responseCode_ = getDefaultInstance().getResponseCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方编码
     * </pre>
     *
     * <code>string response_code = 5;</code>
     * @param value The bytes for responseCode to set.
     * @return This builder for chaining.
     */
    public Builder setResponseCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      responseCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object responseContent_ = "";
    /**
     * <pre>
     * 第三方报文(500字符以内)
     * </pre>
     *
     * <code>string response_content = 6;</code>
     * @return The responseContent.
     */
    public java.lang.String getResponseContent() {
      java.lang.Object ref = responseContent_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        responseContent_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 第三方报文(500字符以内)
     * </pre>
     *
     * <code>string response_content = 6;</code>
     * @return The bytes for responseContent.
     */
    public com.google.protobuf.ByteString
        getResponseContentBytes() {
      java.lang.Object ref = responseContent_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        responseContent_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 第三方报文(500字符以内)
     * </pre>
     *
     * <code>string response_content = 6;</code>
     * @param value The responseContent to set.
     * @return This builder for chaining.
     */
    public Builder setResponseContent(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      responseContent_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方报文(500字符以内)
     * </pre>
     *
     * <code>string response_content = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearResponseContent() {
      
      responseContent_ = getDefaultInstance().getResponseContent();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 第三方报文(500字符以内)
     * </pre>
     *
     * <code>string response_content = 6;</code>
     * @param value The bytes for responseContent to set.
     * @return This builder for chaining.
     */
    public Builder setResponseContentBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      responseContent_ = value;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.member.Coupon> details_ =
      java.util.Collections.emptyList();
    private void ensureDetailsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        details_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.member.Coupon>(details_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.Coupon, cn.hexcloud.pbis.common.service.facade.member.Coupon.Builder, cn.hexcloud.pbis.common.service.facade.member.CouponOrBuilder> detailsBuilder_;

    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.member.Coupon> getDetailsList() {
      if (detailsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(details_);
      } else {
        return detailsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public int getDetailsCount() {
      if (detailsBuilder_ == null) {
        return details_.size();
      } else {
        return detailsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.Coupon getDetails(int index) {
      if (detailsBuilder_ == null) {
        return details_.get(index);
      } else {
        return detailsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public Builder setDetails(
        int index, cn.hexcloud.pbis.common.service.facade.member.Coupon value) {
      if (detailsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetailsIsMutable();
        details_.set(index, value);
        onChanged();
      } else {
        detailsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public Builder setDetails(
        int index, cn.hexcloud.pbis.common.service.facade.member.Coupon.Builder builderForValue) {
      if (detailsBuilder_ == null) {
        ensureDetailsIsMutable();
        details_.set(index, builderForValue.build());
        onChanged();
      } else {
        detailsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public Builder addDetails(cn.hexcloud.pbis.common.service.facade.member.Coupon value) {
      if (detailsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetailsIsMutable();
        details_.add(value);
        onChanged();
      } else {
        detailsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public Builder addDetails(
        int index, cn.hexcloud.pbis.common.service.facade.member.Coupon value) {
      if (detailsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetailsIsMutable();
        details_.add(index, value);
        onChanged();
      } else {
        detailsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public Builder addDetails(
        cn.hexcloud.pbis.common.service.facade.member.Coupon.Builder builderForValue) {
      if (detailsBuilder_ == null) {
        ensureDetailsIsMutable();
        details_.add(builderForValue.build());
        onChanged();
      } else {
        detailsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public Builder addDetails(
        int index, cn.hexcloud.pbis.common.service.facade.member.Coupon.Builder builderForValue) {
      if (detailsBuilder_ == null) {
        ensureDetailsIsMutable();
        details_.add(index, builderForValue.build());
        onChanged();
      } else {
        detailsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public Builder addAllDetails(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.member.Coupon> values) {
      if (detailsBuilder_ == null) {
        ensureDetailsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, details_);
        onChanged();
      } else {
        detailsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public Builder clearDetails() {
      if (detailsBuilder_ == null) {
        details_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        detailsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public Builder removeDetails(int index) {
      if (detailsBuilder_ == null) {
        ensureDetailsIsMutable();
        details_.remove(index);
        onChanged();
      } else {
        detailsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.Coupon.Builder getDetailsBuilder(
        int index) {
      return getDetailsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.CouponOrBuilder getDetailsOrBuilder(
        int index) {
      if (detailsBuilder_ == null) {
        return details_.get(index);  } else {
        return detailsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.CouponOrBuilder> 
         getDetailsOrBuilderList() {
      if (detailsBuilder_ != null) {
        return detailsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(details_);
      }
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.Coupon.Builder addDetailsBuilder() {
      return getDetailsFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.member.Coupon.getDefaultInstance());
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.member.Coupon.Builder addDetailsBuilder(
        int index) {
      return getDetailsFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.member.Coupon.getDefaultInstance());
    }
    /**
     * <pre>
     * 卡券信息
     * </pre>
     *
     * <code>repeated .coupon.Coupon details = 7;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.member.Coupon.Builder> 
         getDetailsBuilderList() {
      return getDetailsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.member.Coupon, cn.hexcloud.pbis.common.service.facade.member.Coupon.Builder, cn.hexcloud.pbis.common.service.facade.member.CouponOrBuilder> 
        getDetailsFieldBuilder() {
      if (detailsBuilder_ == null) {
        detailsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.member.Coupon, cn.hexcloud.pbis.common.service.facade.member.Coupon.Builder, cn.hexcloud.pbis.common.service.facade.member.CouponOrBuilder>(
                details_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        details_ = null;
      }
      return detailsBuilder_;
    }

    private java.lang.Object consumeToken_ = "";
    /**
     * <pre>
     * 券核销凭证
     * </pre>
     *
     * <code>string consume_token = 8;</code>
     * @return The consumeToken.
     */
    public java.lang.String getConsumeToken() {
      java.lang.Object ref = consumeToken_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        consumeToken_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 券核销凭证
     * </pre>
     *
     * <code>string consume_token = 8;</code>
     * @return The bytes for consumeToken.
     */
    public com.google.protobuf.ByteString
        getConsumeTokenBytes() {
      java.lang.Object ref = consumeToken_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        consumeToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 券核销凭证
     * </pre>
     *
     * <code>string consume_token = 8;</code>
     * @param value The consumeToken to set.
     * @return This builder for chaining.
     */
    public Builder setConsumeToken(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      consumeToken_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 券核销凭证
     * </pre>
     *
     * <code>string consume_token = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearConsumeToken() {
      
      consumeToken_ = getDefaultInstance().getConsumeToken();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 券核销凭证
     * </pre>
     *
     * <code>string consume_token = 8;</code>
     * @param value The bytes for consumeToken to set.
     * @return This builder for chaining.
     */
    public Builder setConsumeTokenBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      consumeToken_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:coupon.VerifyCouponsResponse)
  }

  // @@protoc_insertion_point(class_scope:coupon.VerifyCouponsResponse)
  private static final cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse();
  }

  public static cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<VerifyCouponsResponse>
      PARSER = new com.google.protobuf.AbstractParser<VerifyCouponsResponse>() {
    @java.lang.Override
    public VerifyCouponsResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new VerifyCouponsResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<VerifyCouponsResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<VerifyCouponsResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.member.VerifyCouponsResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

