// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: coupon.proto

package cn.hexcloud.pbis.common.service.facade.member;

public interface VerifyCouponsResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.VerifyCouponsResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  java.lang.String getChannel();
  /**
   * <pre>
   * 渠道编码
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  com.google.protobuf.ByteString
      getChannelBytes();

  /**
   * <pre>
   * 是否验证成功
   * </pre>
   *
   * <code>bool success = 2;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <pre>
   * 异常编码，查看交易接口异常返回
   * </pre>
   *
   * <code>string error_code = 3;</code>
   * @return The errorCode.
   */
  java.lang.String getErrorCode();
  /**
   * <pre>
   * 异常编码，查看交易接口异常返回
   * </pre>
   *
   * <code>string error_code = 3;</code>
   * @return The bytes for errorCode.
   */
  com.google.protobuf.ByteString
      getErrorCodeBytes();

  /**
   * <pre>
   * 异常信息
   * </pre>
   *
   * <code>string message = 4;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <pre>
   * 异常信息
   * </pre>
   *
   * <code>string message = 4;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <pre>
   * 第三方编码
   * </pre>
   *
   * <code>string response_code = 5;</code>
   * @return The responseCode.
   */
  java.lang.String getResponseCode();
  /**
   * <pre>
   * 第三方编码
   * </pre>
   *
   * <code>string response_code = 5;</code>
   * @return The bytes for responseCode.
   */
  com.google.protobuf.ByteString
      getResponseCodeBytes();

  /**
   * <pre>
   * 第三方报文(500字符以内)
   * </pre>
   *
   * <code>string response_content = 6;</code>
   * @return The responseContent.
   */
  java.lang.String getResponseContent();
  /**
   * <pre>
   * 第三方报文(500字符以内)
   * </pre>
   *
   * <code>string response_content = 6;</code>
   * @return The bytes for responseContent.
   */
  com.google.protobuf.ByteString
      getResponseContentBytes();

  /**
   * <pre>
   * 卡券信息
   * </pre>
   *
   * <code>repeated .coupon.Coupon details = 7;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.member.Coupon> 
      getDetailsList();
  /**
   * <pre>
   * 卡券信息
   * </pre>
   *
   * <code>repeated .coupon.Coupon details = 7;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.Coupon getDetails(int index);
  /**
   * <pre>
   * 卡券信息
   * </pre>
   *
   * <code>repeated .coupon.Coupon details = 7;</code>
   */
  int getDetailsCount();
  /**
   * <pre>
   * 卡券信息
   * </pre>
   *
   * <code>repeated .coupon.Coupon details = 7;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.member.CouponOrBuilder> 
      getDetailsOrBuilderList();
  /**
   * <pre>
   * 卡券信息
   * </pre>
   *
   * <code>repeated .coupon.Coupon details = 7;</code>
   */
  cn.hexcloud.pbis.common.service.facade.member.CouponOrBuilder getDetailsOrBuilder(
      int index);

  /**
   * <pre>
   * 券核销凭证
   * </pre>
   *
   * <code>string consume_token = 8;</code>
   * @return The consumeToken.
   */
  java.lang.String getConsumeToken();
  /**
   * <pre>
   * 券核销凭证
   * </pre>
   *
   * <code>string consume_token = 8;</code>
   * @return The bytes for consumeToken.
   */
  com.google.protobuf.ByteString
      getConsumeTokenBytes();
}
