// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

/**
 * <pre>
 * 撤销接口请求信息
 * </pre>
 *
 * Protobuf type {@code pbis.CancelRequest}
 */
public final class CancelRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:pbis.CancelRequest)
    CancelRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use CancelRequest.newBuilder() to construct.
  private CancelRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private CancelRequest() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new CancelRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private CancelRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            cn.hexcloud.pbis.common.service.facade.payment.CancelSection.Builder subBuilder = null;
            if (cancelSection_ != null) {
              subBuilder = cancelSection_.toBuilder();
            }
            cancelSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.payment.CancelSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(cancelSection_);
              cancelSection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            cn.hexcloud.pbis.common.service.facade.payment.OrderSection.Builder subBuilder = null;
            if (orderSection_ != null) {
              subBuilder = orderSection_.toBuilder();
            }
            orderSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.payment.OrderSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(orderSection_);
              orderSection_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CancelRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CancelRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.payment.CancelRequest.class, cn.hexcloud.pbis.common.service.facade.payment.CancelRequest.Builder.class);
  }

  public static final int CANCEL_SECTION_FIELD_NUMBER = 1;
  private cn.hexcloud.pbis.common.service.facade.payment.CancelSection cancelSection_;
  /**
   * <pre>
   * （必传）撤销交易信息
   * </pre>
   *
   * <code>.pbis.CancelSection cancel_section = 1;</code>
   * @return Whether the cancelSection field is set.
   */
  @java.lang.Override
  public boolean hasCancelSection() {
    return cancelSection_ != null;
  }
  /**
   * <pre>
   * （必传）撤销交易信息
   * </pre>
   *
   * <code>.pbis.CancelSection cancel_section = 1;</code>
   * @return The cancelSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CancelSection getCancelSection() {
    return cancelSection_ == null ? cn.hexcloud.pbis.common.service.facade.payment.CancelSection.getDefaultInstance() : cancelSection_;
  }
  /**
   * <pre>
   * （必传）撤销交易信息
   * </pre>
   *
   * <code>.pbis.CancelSection cancel_section = 1;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CancelSectionOrBuilder getCancelSectionOrBuilder() {
    return getCancelSection();
  }

  public static final int ORDER_SECTION_FIELD_NUMBER = 2;
  private cn.hexcloud.pbis.common.service.facade.payment.OrderSection orderSection_;
  /**
   * <pre>
   *（可选）订单信息
   * </pre>
   *
   * <code>.pbis.OrderSection order_section = 2;</code>
   * @return Whether the orderSection field is set.
   */
  @java.lang.Override
  public boolean hasOrderSection() {
    return orderSection_ != null;
  }
  /**
   * <pre>
   *（可选）订单信息
   * </pre>
   *
   * <code>.pbis.OrderSection order_section = 2;</code>
   * @return The orderSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.OrderSection getOrderSection() {
    return orderSection_ == null ? cn.hexcloud.pbis.common.service.facade.payment.OrderSection.getDefaultInstance() : orderSection_;
  }
  /**
   * <pre>
   *（可选）订单信息
   * </pre>
   *
   * <code>.pbis.OrderSection order_section = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.OrderSectionOrBuilder getOrderSectionOrBuilder() {
    return getOrderSection();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (cancelSection_ != null) {
      output.writeMessage(1, getCancelSection());
    }
    if (orderSection_ != null) {
      output.writeMessage(2, getOrderSection());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (cancelSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getCancelSection());
    }
    if (orderSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getOrderSection());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.CancelRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.payment.CancelRequest other = (cn.hexcloud.pbis.common.service.facade.payment.CancelRequest) obj;

    if (hasCancelSection() != other.hasCancelSection()) return false;
    if (hasCancelSection()) {
      if (!getCancelSection()
          .equals(other.getCancelSection())) return false;
    }
    if (hasOrderSection() != other.hasOrderSection()) return false;
    if (hasOrderSection()) {
      if (!getOrderSection()
          .equals(other.getOrderSection())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasCancelSection()) {
      hash = (37 * hash) + CANCEL_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getCancelSection().hashCode();
    }
    if (hasOrderSection()) {
      hash = (37 * hash) + ORDER_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getOrderSection().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.CancelRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.CancelRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 撤销接口请求信息
   * </pre>
   *
   * Protobuf type {@code pbis.CancelRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:pbis.CancelRequest)
      cn.hexcloud.pbis.common.service.facade.payment.CancelRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CancelRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CancelRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.CancelRequest.class, cn.hexcloud.pbis.common.service.facade.payment.CancelRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.payment.CancelRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (cancelSectionBuilder_ == null) {
        cancelSection_ = null;
      } else {
        cancelSection_ = null;
        cancelSectionBuilder_ = null;
      }
      if (orderSectionBuilder_ == null) {
        orderSection_ = null;
      } else {
        orderSection_ = null;
        orderSectionBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CancelRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CancelRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.CancelRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CancelRequest build() {
      cn.hexcloud.pbis.common.service.facade.payment.CancelRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CancelRequest buildPartial() {
      cn.hexcloud.pbis.common.service.facade.payment.CancelRequest result = new cn.hexcloud.pbis.common.service.facade.payment.CancelRequest(this);
      if (cancelSectionBuilder_ == null) {
        result.cancelSection_ = cancelSection_;
      } else {
        result.cancelSection_ = cancelSectionBuilder_.build();
      }
      if (orderSectionBuilder_ == null) {
        result.orderSection_ = orderSection_;
      } else {
        result.orderSection_ = orderSectionBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.CancelRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.CancelRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.CancelRequest other) {
      if (other == cn.hexcloud.pbis.common.service.facade.payment.CancelRequest.getDefaultInstance()) return this;
      if (other.hasCancelSection()) {
        mergeCancelSection(other.getCancelSection());
      }
      if (other.hasOrderSection()) {
        mergeOrderSection(other.getOrderSection());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.payment.CancelRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.CancelRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.payment.CancelSection cancelSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.CancelSection, cn.hexcloud.pbis.common.service.facade.payment.CancelSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.CancelSectionOrBuilder> cancelSectionBuilder_;
    /**
     * <pre>
     * （必传）撤销交易信息
     * </pre>
     *
     * <code>.pbis.CancelSection cancel_section = 1;</code>
     * @return Whether the cancelSection field is set.
     */
    public boolean hasCancelSection() {
      return cancelSectionBuilder_ != null || cancelSection_ != null;
    }
    /**
     * <pre>
     * （必传）撤销交易信息
     * </pre>
     *
     * <code>.pbis.CancelSection cancel_section = 1;</code>
     * @return The cancelSection.
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CancelSection getCancelSection() {
      if (cancelSectionBuilder_ == null) {
        return cancelSection_ == null ? cn.hexcloud.pbis.common.service.facade.payment.CancelSection.getDefaultInstance() : cancelSection_;
      } else {
        return cancelSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （必传）撤销交易信息
     * </pre>
     *
     * <code>.pbis.CancelSection cancel_section = 1;</code>
     */
    public Builder setCancelSection(cn.hexcloud.pbis.common.service.facade.payment.CancelSection value) {
      if (cancelSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        cancelSection_ = value;
        onChanged();
      } else {
        cancelSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （必传）撤销交易信息
     * </pre>
     *
     * <code>.pbis.CancelSection cancel_section = 1;</code>
     */
    public Builder setCancelSection(
        cn.hexcloud.pbis.common.service.facade.payment.CancelSection.Builder builderForValue) {
      if (cancelSectionBuilder_ == null) {
        cancelSection_ = builderForValue.build();
        onChanged();
      } else {
        cancelSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （必传）撤销交易信息
     * </pre>
     *
     * <code>.pbis.CancelSection cancel_section = 1;</code>
     */
    public Builder mergeCancelSection(cn.hexcloud.pbis.common.service.facade.payment.CancelSection value) {
      if (cancelSectionBuilder_ == null) {
        if (cancelSection_ != null) {
          cancelSection_ =
            cn.hexcloud.pbis.common.service.facade.payment.CancelSection.newBuilder(cancelSection_).mergeFrom(value).buildPartial();
        } else {
          cancelSection_ = value;
        }
        onChanged();
      } else {
        cancelSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （必传）撤销交易信息
     * </pre>
     *
     * <code>.pbis.CancelSection cancel_section = 1;</code>
     */
    public Builder clearCancelSection() {
      if (cancelSectionBuilder_ == null) {
        cancelSection_ = null;
        onChanged();
      } else {
        cancelSection_ = null;
        cancelSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （必传）撤销交易信息
     * </pre>
     *
     * <code>.pbis.CancelSection cancel_section = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CancelSection.Builder getCancelSectionBuilder() {
      
      onChanged();
      return getCancelSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （必传）撤销交易信息
     * </pre>
     *
     * <code>.pbis.CancelSection cancel_section = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CancelSectionOrBuilder getCancelSectionOrBuilder() {
      if (cancelSectionBuilder_ != null) {
        return cancelSectionBuilder_.getMessageOrBuilder();
      } else {
        return cancelSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.payment.CancelSection.getDefaultInstance() : cancelSection_;
      }
    }
    /**
     * <pre>
     * （必传）撤销交易信息
     * </pre>
     *
     * <code>.pbis.CancelSection cancel_section = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.CancelSection, cn.hexcloud.pbis.common.service.facade.payment.CancelSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.CancelSectionOrBuilder> 
        getCancelSectionFieldBuilder() {
      if (cancelSectionBuilder_ == null) {
        cancelSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.payment.CancelSection, cn.hexcloud.pbis.common.service.facade.payment.CancelSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.CancelSectionOrBuilder>(
                getCancelSection(),
                getParentForChildren(),
                isClean());
        cancelSection_ = null;
      }
      return cancelSectionBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.payment.OrderSection orderSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.OrderSection, cn.hexcloud.pbis.common.service.facade.payment.OrderSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.OrderSectionOrBuilder> orderSectionBuilder_;
    /**
     * <pre>
     *（可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     * @return Whether the orderSection field is set.
     */
    public boolean hasOrderSection() {
      return orderSectionBuilder_ != null || orderSection_ != null;
    }
    /**
     * <pre>
     *（可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     * @return The orderSection.
     */
    public cn.hexcloud.pbis.common.service.facade.payment.OrderSection getOrderSection() {
      if (orderSectionBuilder_ == null) {
        return orderSection_ == null ? cn.hexcloud.pbis.common.service.facade.payment.OrderSection.getDefaultInstance() : orderSection_;
      } else {
        return orderSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *（可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     */
    public Builder setOrderSection(cn.hexcloud.pbis.common.service.facade.payment.OrderSection value) {
      if (orderSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        orderSection_ = value;
        onChanged();
      } else {
        orderSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *（可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     */
    public Builder setOrderSection(
        cn.hexcloud.pbis.common.service.facade.payment.OrderSection.Builder builderForValue) {
      if (orderSectionBuilder_ == null) {
        orderSection_ = builderForValue.build();
        onChanged();
      } else {
        orderSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *（可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     */
    public Builder mergeOrderSection(cn.hexcloud.pbis.common.service.facade.payment.OrderSection value) {
      if (orderSectionBuilder_ == null) {
        if (orderSection_ != null) {
          orderSection_ =
            cn.hexcloud.pbis.common.service.facade.payment.OrderSection.newBuilder(orderSection_).mergeFrom(value).buildPartial();
        } else {
          orderSection_ = value;
        }
        onChanged();
      } else {
        orderSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *（可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     */
    public Builder clearOrderSection() {
      if (orderSectionBuilder_ == null) {
        orderSection_ = null;
        onChanged();
      } else {
        orderSection_ = null;
        orderSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *（可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.OrderSection.Builder getOrderSectionBuilder() {
      
      onChanged();
      return getOrderSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *（可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.OrderSectionOrBuilder getOrderSectionOrBuilder() {
      if (orderSectionBuilder_ != null) {
        return orderSectionBuilder_.getMessageOrBuilder();
      } else {
        return orderSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.payment.OrderSection.getDefaultInstance() : orderSection_;
      }
    }
    /**
     * <pre>
     *（可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.OrderSection, cn.hexcloud.pbis.common.service.facade.payment.OrderSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.OrderSectionOrBuilder> 
        getOrderSectionFieldBuilder() {
      if (orderSectionBuilder_ == null) {
        orderSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.payment.OrderSection, cn.hexcloud.pbis.common.service.facade.payment.OrderSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.OrderSectionOrBuilder>(
                getOrderSection(),
                getParentForChildren(),
                isClean());
        orderSection_ = null;
      }
      return orderSectionBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:pbis.CancelRequest)
  }

  // @@protoc_insertion_point(class_scope:pbis.CancelRequest)
  private static final cn.hexcloud.pbis.common.service.facade.payment.CancelRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.CancelRequest();
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.CancelRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CancelRequest>
      PARSER = new com.google.protobuf.AbstractParser<CancelRequest>() {
    @java.lang.Override
    public CancelRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new CancelRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<CancelRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CancelRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CancelRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

