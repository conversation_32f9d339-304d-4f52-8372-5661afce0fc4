// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

public interface CancelRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:pbis.CancelRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）撤销交易信息
   * </pre>
   *
   * <code>.pbis.CancelSection cancel_section = 1;</code>
   * @return Whether the cancelSection field is set.
   */
  boolean hasCancelSection();
  /**
   * <pre>
   * （必传）撤销交易信息
   * </pre>
   *
   * <code>.pbis.CancelSection cancel_section = 1;</code>
   * @return The cancelSection.
   */
  cn.hexcloud.pbis.common.service.facade.payment.CancelSection getCancelSection();
  /**
   * <pre>
   * （必传）撤销交易信息
   * </pre>
   *
   * <code>.pbis.CancelSection cancel_section = 1;</code>
   */
  cn.hexcloud.pbis.common.service.facade.payment.CancelSectionOrBuilder getCancelSectionOrBuilder();

  /**
   * <pre>
   *（可选）订单信息
   * </pre>
   *
   * <code>.pbis.OrderSection order_section = 2;</code>
   * @return Whether the orderSection field is set.
   */
  boolean hasOrderSection();
  /**
   * <pre>
   *（可选）订单信息
   * </pre>
   *
   * <code>.pbis.OrderSection order_section = 2;</code>
   * @return The orderSection.
   */
  cn.hexcloud.pbis.common.service.facade.payment.OrderSection getOrderSection();
  /**
   * <pre>
   *（可选）订单信息
   * </pre>
   *
   * <code>.pbis.OrderSection order_section = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.payment.OrderSectionOrBuilder getOrderSectionOrBuilder();
}
