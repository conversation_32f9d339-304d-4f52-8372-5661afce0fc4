// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

/**
 * <pre>
 * 撤销接口响应信息
 * </pre>
 *
 * Protobuf type {@code pbis.CancelResponse}
 */
public final class CancelResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:pbis.CancelResponse)
    CancelResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use CancelResponse.newBuilder() to construct.
  private CancelResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private CancelResponse() {
    errorCode_ = "";
    errorMessage_ = "";
    warningMessage_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new CancelResponse();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private CancelResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            errorCode_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            errorMessage_ = s;
            break;
          }
          case 24: {

            warning_ = input.readBool();
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            warningMessage_ = s;
            break;
          }
          case 42: {
            cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.Builder subBuilder = null;
            if (transactionResultSection_ != null) {
              subBuilder = transactionResultSection_.toBuilder();
            }
            transactionResultSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(transactionResultSection_);
              transactionResultSection_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CancelResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CancelResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.payment.CancelResponse.class, cn.hexcloud.pbis.common.service.facade.payment.CancelResponse.Builder.class);
  }

  public static final int ERROR_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object errorCode_;
  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  @java.lang.Override
  public java.lang.String getErrorCode() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorCodeBytes() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ERROR_MESSAGE_FIELD_NUMBER = 2;
  private volatile java.lang.Object errorMessage_;
  /**
   * <pre>
   * （可选）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  @java.lang.Override
  public java.lang.String getErrorMessage() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorMessage_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorMessageBytes() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorMessage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int WARNING_FIELD_NUMBER = 3;
  private boolean warning_;
  /**
   * <pre>
   * （必传）是否有警告信息
   * </pre>
   *
   * <code>bool warning = 3;</code>
   * @return The warning.
   */
  @java.lang.Override
  public boolean getWarning() {
    return warning_;
  }

  public static final int WARNING_MESSAGE_FIELD_NUMBER = 4;
  private volatile java.lang.Object warningMessage_;
  /**
   * <pre>
   * （可选）警告信息
   * </pre>
   *
   * <code>string warning_message = 4;</code>
   * @return The warningMessage.
   */
  @java.lang.Override
  public java.lang.String getWarningMessage() {
    java.lang.Object ref = warningMessage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      warningMessage_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）警告信息
   * </pre>
   *
   * <code>string warning_message = 4;</code>
   * @return The bytes for warningMessage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getWarningMessageBytes() {
    java.lang.Object ref = warningMessage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      warningMessage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TRANSACTION_RESULT_SECTION_FIELD_NUMBER = 5;
  private cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection transactionResultSection_;
  /**
   * <pre>
   * （必传）交易结果信息
   * </pre>
   *
   * <code>.pbis.TransactionResultSection transaction_result_section = 5;</code>
   * @return Whether the transactionResultSection field is set.
   */
  @java.lang.Override
  public boolean hasTransactionResultSection() {
    return transactionResultSection_ != null;
  }
  /**
   * <pre>
   * （必传）交易结果信息
   * </pre>
   *
   * <code>.pbis.TransactionResultSection transaction_result_section = 5;</code>
   * @return The transactionResultSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection getTransactionResultSection() {
    return transactionResultSection_ == null ? cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.getDefaultInstance() : transactionResultSection_;
  }
  /**
   * <pre>
   * （必传）交易结果信息
   * </pre>
   *
   * <code>.pbis.TransactionResultSection transaction_result_section = 5;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSectionOrBuilder getTransactionResultSectionOrBuilder() {
    return getTransactionResultSection();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getErrorCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, errorMessage_);
    }
    if (warning_ != false) {
      output.writeBool(3, warning_);
    }
    if (!getWarningMessageBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, warningMessage_);
    }
    if (transactionResultSection_ != null) {
      output.writeMessage(5, getTransactionResultSection());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getErrorCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, errorMessage_);
    }
    if (warning_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(3, warning_);
    }
    if (!getWarningMessageBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, warningMessage_);
    }
    if (transactionResultSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getTransactionResultSection());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.CancelResponse)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.payment.CancelResponse other = (cn.hexcloud.pbis.common.service.facade.payment.CancelResponse) obj;

    if (!getErrorCode()
        .equals(other.getErrorCode())) return false;
    if (!getErrorMessage()
        .equals(other.getErrorMessage())) return false;
    if (getWarning()
        != other.getWarning()) return false;
    if (!getWarningMessage()
        .equals(other.getWarningMessage())) return false;
    if (hasTransactionResultSection() != other.hasTransactionResultSection()) return false;
    if (hasTransactionResultSection()) {
      if (!getTransactionResultSection()
          .equals(other.getTransactionResultSection())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ERROR_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorCode().hashCode();
    hash = (37 * hash) + ERROR_MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorMessage().hashCode();
    hash = (37 * hash) + WARNING_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getWarning());
    hash = (37 * hash) + WARNING_MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getWarningMessage().hashCode();
    if (hasTransactionResultSection()) {
      hash = (37 * hash) + TRANSACTION_RESULT_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getTransactionResultSection().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.CancelResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CancelResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.CancelResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 撤销接口响应信息
   * </pre>
   *
   * Protobuf type {@code pbis.CancelResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:pbis.CancelResponse)
      cn.hexcloud.pbis.common.service.facade.payment.CancelResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CancelResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CancelResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.CancelResponse.class, cn.hexcloud.pbis.common.service.facade.payment.CancelResponse.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.payment.CancelResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      errorCode_ = "";

      errorMessage_ = "";

      warning_ = false;

      warningMessage_ = "";

      if (transactionResultSectionBuilder_ == null) {
        transactionResultSection_ = null;
      } else {
        transactionResultSection_ = null;
        transactionResultSectionBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CancelResponse_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CancelResponse getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.CancelResponse.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CancelResponse build() {
      cn.hexcloud.pbis.common.service.facade.payment.CancelResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CancelResponse buildPartial() {
      cn.hexcloud.pbis.common.service.facade.payment.CancelResponse result = new cn.hexcloud.pbis.common.service.facade.payment.CancelResponse(this);
      result.errorCode_ = errorCode_;
      result.errorMessage_ = errorMessage_;
      result.warning_ = warning_;
      result.warningMessage_ = warningMessage_;
      if (transactionResultSectionBuilder_ == null) {
        result.transactionResultSection_ = transactionResultSection_;
      } else {
        result.transactionResultSection_ = transactionResultSectionBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.CancelResponse) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.CancelResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.CancelResponse other) {
      if (other == cn.hexcloud.pbis.common.service.facade.payment.CancelResponse.getDefaultInstance()) return this;
      if (!other.getErrorCode().isEmpty()) {
        errorCode_ = other.errorCode_;
        onChanged();
      }
      if (!other.getErrorMessage().isEmpty()) {
        errorMessage_ = other.errorMessage_;
        onChanged();
      }
      if (other.getWarning() != false) {
        setWarning(other.getWarning());
      }
      if (!other.getWarningMessage().isEmpty()) {
        warningMessage_ = other.warningMessage_;
        onChanged();
      }
      if (other.hasTransactionResultSection()) {
        mergeTransactionResultSection(other.getTransactionResultSection());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.payment.CancelResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.CancelResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object errorCode_ = "";
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return The errorCode.
     */
    public java.lang.String getErrorCode() {
      java.lang.Object ref = errorCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return The bytes for errorCode.
     */
    public com.google.protobuf.ByteString
        getErrorCodeBytes() {
      java.lang.Object ref = errorCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @param value The errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorCode() {
      
      errorCode_ = getDefaultInstance().getErrorCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @param value The bytes for errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object errorMessage_ = "";
    /**
     * <pre>
     * （可选）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return The errorMessage.
     */
    public java.lang.String getErrorMessage() {
      java.lang.Object ref = errorMessage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorMessage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return The bytes for errorMessage.
     */
    public com.google.protobuf.ByteString
        getErrorMessageBytes() {
      java.lang.Object ref = errorMessage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorMessage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @param value The errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessage(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorMessage_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorMessage() {
      
      errorMessage_ = getDefaultInstance().getErrorMessage();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @param value The bytes for errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorMessage_ = value;
      onChanged();
      return this;
    }

    private boolean warning_ ;
    /**
     * <pre>
     * （必传）是否有警告信息
     * </pre>
     *
     * <code>bool warning = 3;</code>
     * @return The warning.
     */
    @java.lang.Override
    public boolean getWarning() {
      return warning_;
    }
    /**
     * <pre>
     * （必传）是否有警告信息
     * </pre>
     *
     * <code>bool warning = 3;</code>
     * @param value The warning to set.
     * @return This builder for chaining.
     */
    public Builder setWarning(boolean value) {
      
      warning_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）是否有警告信息
     * </pre>
     *
     * <code>bool warning = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearWarning() {
      
      warning_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object warningMessage_ = "";
    /**
     * <pre>
     * （可选）警告信息
     * </pre>
     *
     * <code>string warning_message = 4;</code>
     * @return The warningMessage.
     */
    public java.lang.String getWarningMessage() {
      java.lang.Object ref = warningMessage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        warningMessage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）警告信息
     * </pre>
     *
     * <code>string warning_message = 4;</code>
     * @return The bytes for warningMessage.
     */
    public com.google.protobuf.ByteString
        getWarningMessageBytes() {
      java.lang.Object ref = warningMessage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        warningMessage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）警告信息
     * </pre>
     *
     * <code>string warning_message = 4;</code>
     * @param value The warningMessage to set.
     * @return This builder for chaining.
     */
    public Builder setWarningMessage(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      warningMessage_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）警告信息
     * </pre>
     *
     * <code>string warning_message = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearWarningMessage() {
      
      warningMessage_ = getDefaultInstance().getWarningMessage();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）警告信息
     * </pre>
     *
     * <code>string warning_message = 4;</code>
     * @param value The bytes for warningMessage to set.
     * @return This builder for chaining.
     */
    public Builder setWarningMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      warningMessage_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection transactionResultSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection, cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSectionOrBuilder> transactionResultSectionBuilder_;
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.TransactionResultSection transaction_result_section = 5;</code>
     * @return Whether the transactionResultSection field is set.
     */
    public boolean hasTransactionResultSection() {
      return transactionResultSectionBuilder_ != null || transactionResultSection_ != null;
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.TransactionResultSection transaction_result_section = 5;</code>
     * @return The transactionResultSection.
     */
    public cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection getTransactionResultSection() {
      if (transactionResultSectionBuilder_ == null) {
        return transactionResultSection_ == null ? cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.getDefaultInstance() : transactionResultSection_;
      } else {
        return transactionResultSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.TransactionResultSection transaction_result_section = 5;</code>
     */
    public Builder setTransactionResultSection(cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection value) {
      if (transactionResultSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        transactionResultSection_ = value;
        onChanged();
      } else {
        transactionResultSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.TransactionResultSection transaction_result_section = 5;</code>
     */
    public Builder setTransactionResultSection(
        cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.Builder builderForValue) {
      if (transactionResultSectionBuilder_ == null) {
        transactionResultSection_ = builderForValue.build();
        onChanged();
      } else {
        transactionResultSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.TransactionResultSection transaction_result_section = 5;</code>
     */
    public Builder mergeTransactionResultSection(cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection value) {
      if (transactionResultSectionBuilder_ == null) {
        if (transactionResultSection_ != null) {
          transactionResultSection_ =
            cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.newBuilder(transactionResultSection_).mergeFrom(value).buildPartial();
        } else {
          transactionResultSection_ = value;
        }
        onChanged();
      } else {
        transactionResultSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.TransactionResultSection transaction_result_section = 5;</code>
     */
    public Builder clearTransactionResultSection() {
      if (transactionResultSectionBuilder_ == null) {
        transactionResultSection_ = null;
        onChanged();
      } else {
        transactionResultSection_ = null;
        transactionResultSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.TransactionResultSection transaction_result_section = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.Builder getTransactionResultSectionBuilder() {
      
      onChanged();
      return getTransactionResultSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.TransactionResultSection transaction_result_section = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSectionOrBuilder getTransactionResultSectionOrBuilder() {
      if (transactionResultSectionBuilder_ != null) {
        return transactionResultSectionBuilder_.getMessageOrBuilder();
      } else {
        return transactionResultSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.getDefaultInstance() : transactionResultSection_;
      }
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.TransactionResultSection transaction_result_section = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection, cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSectionOrBuilder> 
        getTransactionResultSectionFieldBuilder() {
      if (transactionResultSectionBuilder_ == null) {
        transactionResultSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection, cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSectionOrBuilder>(
                getTransactionResultSection(),
                getParentForChildren(),
                isClean());
        transactionResultSection_ = null;
      }
      return transactionResultSectionBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:pbis.CancelResponse)
  }

  // @@protoc_insertion_point(class_scope:pbis.CancelResponse)
  private static final cn.hexcloud.pbis.common.service.facade.payment.CancelResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.CancelResponse();
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.CancelResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CancelResponse>
      PARSER = new com.google.protobuf.AbstractParser<CancelResponse>() {
    @java.lang.Override
    public CancelResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new CancelResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<CancelResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CancelResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CancelResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

