// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

public interface CancelResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:pbis.CancelResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  java.lang.String getErrorCode();
  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  com.google.protobuf.ByteString
      getErrorCodeBytes();

  /**
   * <pre>
   * （可选）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  java.lang.String getErrorMessage();
  /**
   * <pre>
   * （可选）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  com.google.protobuf.ByteString
      getErrorMessageBytes();

  /**
   * <pre>
   * （必传）是否有警告信息
   * </pre>
   *
   * <code>bool warning = 3;</code>
   * @return The warning.
   */
  boolean getWarning();

  /**
   * <pre>
   * （可选）警告信息
   * </pre>
   *
   * <code>string warning_message = 4;</code>
   * @return The warningMessage.
   */
  java.lang.String getWarningMessage();
  /**
   * <pre>
   * （可选）警告信息
   * </pre>
   *
   * <code>string warning_message = 4;</code>
   * @return The bytes for warningMessage.
   */
  com.google.protobuf.ByteString
      getWarningMessageBytes();

  /**
   * <pre>
   * （必传）交易结果信息
   * </pre>
   *
   * <code>.pbis.TransactionResultSection transaction_result_section = 5;</code>
   * @return Whether the transactionResultSection field is set.
   */
  boolean hasTransactionResultSection();
  /**
   * <pre>
   * （必传）交易结果信息
   * </pre>
   *
   * <code>.pbis.TransactionResultSection transaction_result_section = 5;</code>
   * @return The transactionResultSection.
   */
  cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection getTransactionResultSection();
  /**
   * <pre>
   * （必传）交易结果信息
   * </pre>
   *
   * <code>.pbis.TransactionResultSection transaction_result_section = 5;</code>
   */
  cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSectionOrBuilder getTransactionResultSectionOrBuilder();
}
