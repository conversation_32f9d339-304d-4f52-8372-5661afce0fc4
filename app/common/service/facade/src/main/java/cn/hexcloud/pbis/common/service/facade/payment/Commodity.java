// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

/**
 * <pre>
 * 商品信息
 * </pre>
 *
 * Protobuf type {@code pbis.Commodity}
 */
public final class Commodity extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:pbis.Commodity)
    CommodityOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Commodity.newBuilder() to construct.
  private Commodity(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Commodity() {
    id_ = "";
    name_ = "";
    code_ = "";
    imageUrl_ = "";
    property_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Commodity();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Commodity(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            id_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            code_ = s;
            break;
          }
          case 33: {

            quantity_ = input.readDouble();
            break;
          }
          case 40: {

            price_ = input.readInt32();
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            imageUrl_ = s;
            break;
          }
          case 58: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              property_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty>();
              mutable_bitField0_ |= 0x00000001;
            }
            property_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        property_ = java.util.Collections.unmodifiableList(property_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_Commodity_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_Commodity_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.payment.Commodity.class, cn.hexcloud.pbis.common.service.facade.payment.Commodity.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  private volatile java.lang.Object id_;
  /**
   * <pre>
   * （可选）商品id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）商品id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 2;
  private volatile java.lang.Object name_;
  /**
   * <pre>
   * （必传）商品名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）商品名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CODE_FIELD_NUMBER = 3;
  private volatile java.lang.Object code_;
  /**
   * <pre>
   * （必传）商品编码
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The code.
   */
  @java.lang.Override
  public java.lang.String getCode() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      code_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）商品编码
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The bytes for code.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCodeBytes() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      code_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int QUANTITY_FIELD_NUMBER = 4;
  private double quantity_;
  /**
   * <pre>
   * （必传）数量
   * </pre>
   *
   * <code>double quantity = 4;</code>
   * @return The quantity.
   */
  @java.lang.Override
  public double getQuantity() {
    return quantity_;
  }

  public static final int PRICE_FIELD_NUMBER = 5;
  private int price_;
  /**
   * <pre>
   * （必传）价格(每个商品的)
   * </pre>
   *
   * <code>int32 price = 5;</code>
   * @return The price.
   */
  @java.lang.Override
  public int getPrice() {
    return price_;
  }

  public static final int IMAGE_URL_FIELD_NUMBER = 6;
  private volatile java.lang.Object imageUrl_;
  /**
   * <pre>
   * （可选）商品图片地址
   * </pre>
   *
   * <code>string image_url = 6;</code>
   * @return The imageUrl.
   */
  @java.lang.Override
  public java.lang.String getImageUrl() {
    java.lang.Object ref = imageUrl_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      imageUrl_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）商品图片地址
   * </pre>
   *
   * <code>string image_url = 6;</code>
   * @return The bytes for imageUrl.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getImageUrlBytes() {
    java.lang.Object ref = imageUrl_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      imageUrl_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PROPERTY_FIELD_NUMBER = 7;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty> property_;
  /**
   * <pre>
   * （可选）商品属性
   * </pre>
   *
   * <code>repeated .pbis.CommodityProperty property = 7;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty> getPropertyList() {
    return property_;
  }
  /**
   * <pre>
   * （可选）商品属性
   * </pre>
   *
   * <code>repeated .pbis.CommodityProperty property = 7;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.payment.CommodityPropertyOrBuilder> 
      getPropertyOrBuilderList() {
    return property_;
  }
  /**
   * <pre>
   * （可选）商品属性
   * </pre>
   *
   * <code>repeated .pbis.CommodityProperty property = 7;</code>
   */
  @java.lang.Override
  public int getPropertyCount() {
    return property_.size();
  }
  /**
   * <pre>
   * （可选）商品属性
   * </pre>
   *
   * <code>repeated .pbis.CommodityProperty property = 7;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty getProperty(int index) {
    return property_.get(index);
  }
  /**
   * <pre>
   * （可选）商品属性
   * </pre>
   *
   * <code>repeated .pbis.CommodityProperty property = 7;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CommodityPropertyOrBuilder getPropertyOrBuilder(
      int index) {
    return property_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!getNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
    }
    if (!getCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, code_);
    }
    if (quantity_ != 0D) {
      output.writeDouble(4, quantity_);
    }
    if (price_ != 0) {
      output.writeInt32(5, price_);
    }
    if (!getImageUrlBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, imageUrl_);
    }
    for (int i = 0; i < property_.size(); i++) {
      output.writeMessage(7, property_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!getNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
    }
    if (!getCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, code_);
    }
    if (quantity_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(4, quantity_);
    }
    if (price_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, price_);
    }
    if (!getImageUrlBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, imageUrl_);
    }
    for (int i = 0; i < property_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, property_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.Commodity)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.payment.Commodity other = (cn.hexcloud.pbis.common.service.facade.payment.Commodity) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (!getCode()
        .equals(other.getCode())) return false;
    if (java.lang.Double.doubleToLongBits(getQuantity())
        != java.lang.Double.doubleToLongBits(
            other.getQuantity())) return false;
    if (getPrice()
        != other.getPrice()) return false;
    if (!getImageUrl()
        .equals(other.getImageUrl())) return false;
    if (!getPropertyList()
        .equals(other.getPropertyList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCode().hashCode();
    hash = (37 * hash) + QUANTITY_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getQuantity()));
    hash = (37 * hash) + PRICE_FIELD_NUMBER;
    hash = (53 * hash) + getPrice();
    hash = (37 * hash) + IMAGE_URL_FIELD_NUMBER;
    hash = (53 * hash) + getImageUrl().hashCode();
    if (getPropertyCount() > 0) {
      hash = (37 * hash) + PROPERTY_FIELD_NUMBER;
      hash = (53 * hash) + getPropertyList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.Commodity parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Commodity parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Commodity parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Commodity parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Commodity parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Commodity parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Commodity parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Commodity parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Commodity parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Commodity parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Commodity parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Commodity parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.Commodity prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 商品信息
   * </pre>
   *
   * Protobuf type {@code pbis.Commodity}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:pbis.Commodity)
      cn.hexcloud.pbis.common.service.facade.payment.CommodityOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_Commodity_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_Commodity_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.Commodity.class, cn.hexcloud.pbis.common.service.facade.payment.Commodity.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.payment.Commodity.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getPropertyFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      id_ = "";

      name_ = "";

      code_ = "";

      quantity_ = 0D;

      price_ = 0;

      imageUrl_ = "";

      if (propertyBuilder_ == null) {
        property_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        propertyBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_Commodity_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.Commodity getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.Commodity.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.Commodity build() {
      cn.hexcloud.pbis.common.service.facade.payment.Commodity result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.Commodity buildPartial() {
      cn.hexcloud.pbis.common.service.facade.payment.Commodity result = new cn.hexcloud.pbis.common.service.facade.payment.Commodity(this);
      int from_bitField0_ = bitField0_;
      result.id_ = id_;
      result.name_ = name_;
      result.code_ = code_;
      result.quantity_ = quantity_;
      result.price_ = price_;
      result.imageUrl_ = imageUrl_;
      if (propertyBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          property_ = java.util.Collections.unmodifiableList(property_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.property_ = property_;
      } else {
        result.property_ = propertyBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.Commodity) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.Commodity)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.Commodity other) {
      if (other == cn.hexcloud.pbis.common.service.facade.payment.Commodity.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (!other.getCode().isEmpty()) {
        code_ = other.code_;
        onChanged();
      }
      if (other.getQuantity() != 0D) {
        setQuantity(other.getQuantity());
      }
      if (other.getPrice() != 0) {
        setPrice(other.getPrice());
      }
      if (!other.getImageUrl().isEmpty()) {
        imageUrl_ = other.imageUrl_;
        onChanged();
      }
      if (propertyBuilder_ == null) {
        if (!other.property_.isEmpty()) {
          if (property_.isEmpty()) {
            property_ = other.property_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePropertyIsMutable();
            property_.addAll(other.property_);
          }
          onChanged();
        }
      } else {
        if (!other.property_.isEmpty()) {
          if (propertyBuilder_.isEmpty()) {
            propertyBuilder_.dispose();
            propertyBuilder_ = null;
            property_ = other.property_;
            bitField0_ = (bitField0_ & ~0x00000001);
            propertyBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getPropertyFieldBuilder() : null;
          } else {
            propertyBuilder_.addAllMessages(other.property_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.payment.Commodity parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.Commodity) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <pre>
     * （可选）商品id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）商品id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）商品id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）商品id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      
      id_ = getDefaultInstance().getId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）商品id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      id_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <pre>
     * （必传）商品名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）商品名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）商品名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）商品名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）商品名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object code_ = "";
    /**
     * <pre>
     * （必传）商品编码
     * </pre>
     *
     * <code>string code = 3;</code>
     * @return The code.
     */
    public java.lang.String getCode() {
      java.lang.Object ref = code_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        code_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）商品编码
     * </pre>
     *
     * <code>string code = 3;</code>
     * @return The bytes for code.
     */
    public com.google.protobuf.ByteString
        getCodeBytes() {
      java.lang.Object ref = code_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        code_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）商品编码
     * </pre>
     *
     * <code>string code = 3;</code>
     * @param value The code to set.
     * @return This builder for chaining.
     */
    public Builder setCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      code_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）商品编码
     * </pre>
     *
     * <code>string code = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearCode() {
      
      code_ = getDefaultInstance().getCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）商品编码
     * </pre>
     *
     * <code>string code = 3;</code>
     * @param value The bytes for code to set.
     * @return This builder for chaining.
     */
    public Builder setCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      code_ = value;
      onChanged();
      return this;
    }

    private double quantity_ ;
    /**
     * <pre>
     * （必传）数量
     * </pre>
     *
     * <code>double quantity = 4;</code>
     * @return The quantity.
     */
    @java.lang.Override
    public double getQuantity() {
      return quantity_;
    }
    /**
     * <pre>
     * （必传）数量
     * </pre>
     *
     * <code>double quantity = 4;</code>
     * @param value The quantity to set.
     * @return This builder for chaining.
     */
    public Builder setQuantity(double value) {
      
      quantity_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）数量
     * </pre>
     *
     * <code>double quantity = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearQuantity() {
      
      quantity_ = 0D;
      onChanged();
      return this;
    }

    private int price_ ;
    /**
     * <pre>
     * （必传）价格(每个商品的)
     * </pre>
     *
     * <code>int32 price = 5;</code>
     * @return The price.
     */
    @java.lang.Override
    public int getPrice() {
      return price_;
    }
    /**
     * <pre>
     * （必传）价格(每个商品的)
     * </pre>
     *
     * <code>int32 price = 5;</code>
     * @param value The price to set.
     * @return This builder for chaining.
     */
    public Builder setPrice(int value) {
      
      price_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）价格(每个商品的)
     * </pre>
     *
     * <code>int32 price = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrice() {
      
      price_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object imageUrl_ = "";
    /**
     * <pre>
     * （可选）商品图片地址
     * </pre>
     *
     * <code>string image_url = 6;</code>
     * @return The imageUrl.
     */
    public java.lang.String getImageUrl() {
      java.lang.Object ref = imageUrl_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        imageUrl_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）商品图片地址
     * </pre>
     *
     * <code>string image_url = 6;</code>
     * @return The bytes for imageUrl.
     */
    public com.google.protobuf.ByteString
        getImageUrlBytes() {
      java.lang.Object ref = imageUrl_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        imageUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）商品图片地址
     * </pre>
     *
     * <code>string image_url = 6;</code>
     * @param value The imageUrl to set.
     * @return This builder for chaining.
     */
    public Builder setImageUrl(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      imageUrl_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）商品图片地址
     * </pre>
     *
     * <code>string image_url = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearImageUrl() {
      
      imageUrl_ = getDefaultInstance().getImageUrl();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）商品图片地址
     * </pre>
     *
     * <code>string image_url = 6;</code>
     * @param value The bytes for imageUrl to set.
     * @return This builder for chaining.
     */
    public Builder setImageUrlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      imageUrl_ = value;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty> property_ =
      java.util.Collections.emptyList();
    private void ensurePropertyIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        property_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty>(property_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.Builder, cn.hexcloud.pbis.common.service.facade.payment.CommodityPropertyOrBuilder> propertyBuilder_;

    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty> getPropertyList() {
      if (propertyBuilder_ == null) {
        return java.util.Collections.unmodifiableList(property_);
      } else {
        return propertyBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public int getPropertyCount() {
      if (propertyBuilder_ == null) {
        return property_.size();
      } else {
        return propertyBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty getProperty(int index) {
      if (propertyBuilder_ == null) {
        return property_.get(index);
      } else {
        return propertyBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public Builder setProperty(
        int index, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty value) {
      if (propertyBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePropertyIsMutable();
        property_.set(index, value);
        onChanged();
      } else {
        propertyBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public Builder setProperty(
        int index, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.Builder builderForValue) {
      if (propertyBuilder_ == null) {
        ensurePropertyIsMutable();
        property_.set(index, builderForValue.build());
        onChanged();
      } else {
        propertyBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public Builder addProperty(cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty value) {
      if (propertyBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePropertyIsMutable();
        property_.add(value);
        onChanged();
      } else {
        propertyBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public Builder addProperty(
        int index, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty value) {
      if (propertyBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePropertyIsMutable();
        property_.add(index, value);
        onChanged();
      } else {
        propertyBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public Builder addProperty(
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.Builder builderForValue) {
      if (propertyBuilder_ == null) {
        ensurePropertyIsMutable();
        property_.add(builderForValue.build());
        onChanged();
      } else {
        propertyBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public Builder addProperty(
        int index, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.Builder builderForValue) {
      if (propertyBuilder_ == null) {
        ensurePropertyIsMutable();
        property_.add(index, builderForValue.build());
        onChanged();
      } else {
        propertyBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public Builder addAllProperty(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty> values) {
      if (propertyBuilder_ == null) {
        ensurePropertyIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, property_);
        onChanged();
      } else {
        propertyBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public Builder clearProperty() {
      if (propertyBuilder_ == null) {
        property_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        propertyBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public Builder removeProperty(int index) {
      if (propertyBuilder_ == null) {
        ensurePropertyIsMutable();
        property_.remove(index);
        onChanged();
      } else {
        propertyBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.Builder getPropertyBuilder(
        int index) {
      return getPropertyFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityPropertyOrBuilder getPropertyOrBuilder(
        int index) {
      if (propertyBuilder_ == null) {
        return property_.get(index);  } else {
        return propertyBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.payment.CommodityPropertyOrBuilder> 
         getPropertyOrBuilderList() {
      if (propertyBuilder_ != null) {
        return propertyBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(property_);
      }
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.Builder addPropertyBuilder() {
      return getPropertyFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.getDefaultInstance());
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.Builder addPropertyBuilder(
        int index) {
      return getPropertyFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.getDefaultInstance());
    }
    /**
     * <pre>
     * （可选）商品属性
     * </pre>
     *
     * <code>repeated .pbis.CommodityProperty property = 7;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.Builder> 
         getPropertyBuilderList() {
      return getPropertyFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.Builder, cn.hexcloud.pbis.common.service.facade.payment.CommodityPropertyOrBuilder> 
        getPropertyFieldBuilder() {
      if (propertyBuilder_ == null) {
        propertyBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.Builder, cn.hexcloud.pbis.common.service.facade.payment.CommodityPropertyOrBuilder>(
                property_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        property_ = null;
      }
      return propertyBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:pbis.Commodity)
  }

  // @@protoc_insertion_point(class_scope:pbis.Commodity)
  private static final cn.hexcloud.pbis.common.service.facade.payment.Commodity DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.Commodity();
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.Commodity getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Commodity>
      PARSER = new com.google.protobuf.AbstractParser<Commodity>() {
    @java.lang.Override
    public Commodity parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Commodity(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Commodity> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Commodity> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.Commodity getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

