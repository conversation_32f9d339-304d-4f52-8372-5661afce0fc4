// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

public interface CommodityOrBuilder extends
    // @@protoc_insertion_point(interface_extends:pbis.Commodity)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （可选）商品id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   * （可选）商品id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   * （必传）商品名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   * （必传）商品名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * （必传）商品编码
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <pre>
   * （必传）商品编码
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <pre>
   * （必传）数量
   * </pre>
   *
   * <code>double quantity = 4;</code>
   * @return The quantity.
   */
  double getQuantity();

  /**
   * <pre>
   * （必传）价格(每个商品的)
   * </pre>
   *
   * <code>int32 price = 5;</code>
   * @return The price.
   */
  int getPrice();

  /**
   * <pre>
   * （可选）商品图片地址
   * </pre>
   *
   * <code>string image_url = 6;</code>
   * @return The imageUrl.
   */
  java.lang.String getImageUrl();
  /**
   * <pre>
   * （可选）商品图片地址
   * </pre>
   *
   * <code>string image_url = 6;</code>
   * @return The bytes for imageUrl.
   */
  com.google.protobuf.ByteString
      getImageUrlBytes();

  /**
   * <pre>
   * （可选）商品属性
   * </pre>
   *
   * <code>repeated .pbis.CommodityProperty property = 7;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty> 
      getPropertyList();
  /**
   * <pre>
   * （可选）商品属性
   * </pre>
   *
   * <code>repeated .pbis.CommodityProperty property = 7;</code>
   */
  cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty getProperty(int index);
  /**
   * <pre>
   * （可选）商品属性
   * </pre>
   *
   * <code>repeated .pbis.CommodityProperty property = 7;</code>
   */
  int getPropertyCount();
  /**
   * <pre>
   * （可选）商品属性
   * </pre>
   *
   * <code>repeated .pbis.CommodityProperty property = 7;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.payment.CommodityPropertyOrBuilder> 
      getPropertyOrBuilderList();
  /**
   * <pre>
   * （可选）商品属性
   * </pre>
   *
   * <code>repeated .pbis.CommodityProperty property = 7;</code>
   */
  cn.hexcloud.pbis.common.service.facade.payment.CommodityPropertyOrBuilder getPropertyOrBuilder(
      int index);
}
