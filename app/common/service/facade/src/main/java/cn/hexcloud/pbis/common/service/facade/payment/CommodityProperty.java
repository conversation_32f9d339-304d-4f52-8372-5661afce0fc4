// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

/**
 * <pre>
 * 商品属性信息
 * </pre>
 *
 * Protobuf type {@code pbis.CommodityProperty}
 */
public final class CommodityProperty extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:pbis.CommodityProperty)
    CommodityPropertyOrBuilder {
private static final long serialVersionUID = 0L;
  // Use CommodityProperty.newBuilder() to construct.
  private CommodityProperty(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private CommodityProperty() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new CommodityProperty();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private CommodityProperty(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.Builder subBuilder = null;
            if (name_ != null) {
              subBuilder = name_.toBuilder();
            }
            name_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(name_);
              name_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.Builder subBuilder = null;
            if (value_ != null) {
              subBuilder = value_.toBuilder();
            }
            value_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(value_);
              value_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CommodityProperty_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CommodityProperty_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.class, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.Builder.class);
  }

  public interface PropertyNameOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pbis.CommodityProperty.PropertyName)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * （必传）属性code
     * </pre>
     *
     * <code>string code = 1;</code>
     * @return The code.
     */
    java.lang.String getCode();
    /**
     * <pre>
     * （必传）属性code
     * </pre>
     *
     * <code>string code = 1;</code>
     * @return The bytes for code.
     */
    com.google.protobuf.ByteString
        getCodeBytes();

    /**
     * <pre>
     *（必传）属性名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <pre>
     *（必传）属性名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();
  }
  /**
   * <pre>
   * 商品属性名称
   * </pre>
   *
   * Protobuf type {@code pbis.CommodityProperty.PropertyName}
   */
  public static final class PropertyName extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pbis.CommodityProperty.PropertyName)
      PropertyNameOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PropertyName.newBuilder() to construct.
    private PropertyName(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PropertyName() {
      code_ = "";
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PropertyName();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PropertyName(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              code_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CommodityProperty_PropertyName_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CommodityProperty_PropertyName_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.class, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private volatile java.lang.Object code_;
    /**
     * <pre>
     * （必传）属性code
     * </pre>
     *
     * <code>string code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public java.lang.String getCode() {
      java.lang.Object ref = code_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        code_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * （必传）属性code
     * </pre>
     *
     * <code>string code = 1;</code>
     * @return The bytes for code.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCodeBytes() {
      java.lang.Object ref = code_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        code_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object name_;
    /**
     * <pre>
     *（必传）属性名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *（必传）属性名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getCodeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, code_);
      }
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getCodeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, code_);
      }
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName)) {
        return super.equals(obj);
      }
      cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName other = (cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName) obj;

      if (!getCode()
          .equals(other.getCode())) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 商品属性名称
     * </pre>
     *
     * Protobuf type {@code pbis.CommodityProperty.PropertyName}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pbis.CommodityProperty.PropertyName)
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyNameOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CommodityProperty_PropertyName_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CommodityProperty_PropertyName_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.class, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.Builder.class);
      }

      // Construct using cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = "";

        name_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CommodityProperty_PropertyName_descriptor;
      }

      @java.lang.Override
      public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName getDefaultInstanceForType() {
        return cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.getDefaultInstance();
      }

      @java.lang.Override
      public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName build() {
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName buildPartial() {
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName result = new cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName(this);
        result.code_ = code_;
        result.name_ = name_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName) {
          return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName other) {
        if (other == cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.getDefaultInstance()) return this;
        if (!other.getCode().isEmpty()) {
          code_ = other.code_;
          onChanged();
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object code_ = "";
      /**
       * <pre>
       * （必传）属性code
       * </pre>
       *
       * <code>string code = 1;</code>
       * @return The code.
       */
      public java.lang.String getCode() {
        java.lang.Object ref = code_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          code_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * （必传）属性code
       * </pre>
       *
       * <code>string code = 1;</code>
       * @return The bytes for code.
       */
      public com.google.protobuf.ByteString
          getCodeBytes() {
        java.lang.Object ref = code_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          code_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * （必传）属性code
       * </pre>
       *
       * <code>string code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * （必传）属性code
       * </pre>
       *
       * <code>string code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = getDefaultInstance().getCode();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * （必传）属性code
       * </pre>
       *
       * <code>string code = 1;</code>
       * @param value The bytes for code to set.
       * @return This builder for chaining.
       */
      public Builder setCodeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        code_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <pre>
       *（必传）属性名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *（必传）属性名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *（必传）属性名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *（必传）属性名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *（必传）属性名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pbis.CommodityProperty.PropertyName)
    }

    // @@protoc_insertion_point(class_scope:pbis.CommodityProperty.PropertyName)
    private static final cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName();
    }

    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PropertyName>
        PARSER = new com.google.protobuf.AbstractParser<PropertyName>() {
      @java.lang.Override
      public PropertyName parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PropertyName(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PropertyName> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PropertyName> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PropertyValueOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pbis.CommodityProperty.PropertyValue)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * （必传）属性值code
     * </pre>
     *
     * <code>string code = 1;</code>
     * @return The code.
     */
    java.lang.String getCode();
    /**
     * <pre>
     * （必传）属性值code
     * </pre>
     *
     * <code>string code = 1;</code>
     * @return The bytes for code.
     */
    com.google.protobuf.ByteString
        getCodeBytes();

    /**
     * <pre>
     * （必传）属性值名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <pre>
     * （必传）属性值名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <pre>
     * （必传）属性值价格
     * </pre>
     *
     * <code>double price = 3;</code>
     * @return The price.
     */
    double getPrice();
  }
  /**
   * <pre>
   * 商品属性值
   * </pre>
   *
   * Protobuf type {@code pbis.CommodityProperty.PropertyValue}
   */
  public static final class PropertyValue extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pbis.CommodityProperty.PropertyValue)
      PropertyValueOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PropertyValue.newBuilder() to construct.
    private PropertyValue(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PropertyValue() {
      code_ = "";
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PropertyValue();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PropertyValue(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              code_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            case 25: {

              price_ = input.readDouble();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CommodityProperty_PropertyValue_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CommodityProperty_PropertyValue_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.class, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private volatile java.lang.Object code_;
    /**
     * <pre>
     * （必传）属性值code
     * </pre>
     *
     * <code>string code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public java.lang.String getCode() {
      java.lang.Object ref = code_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        code_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * （必传）属性值code
     * </pre>
     *
     * <code>string code = 1;</code>
     * @return The bytes for code.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCodeBytes() {
      java.lang.Object ref = code_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        code_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object name_;
    /**
     * <pre>
     * （必传）属性值名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * （必传）属性值名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PRICE_FIELD_NUMBER = 3;
    private double price_;
    /**
     * <pre>
     * （必传）属性值价格
     * </pre>
     *
     * <code>double price = 3;</code>
     * @return The price.
     */
    @java.lang.Override
    public double getPrice() {
      return price_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getCodeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, code_);
      }
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      if (price_ != 0D) {
        output.writeDouble(3, price_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getCodeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, code_);
      }
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      if (price_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(3, price_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue)) {
        return super.equals(obj);
      }
      cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue other = (cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue) obj;

      if (!getCode()
          .equals(other.getCode())) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (java.lang.Double.doubleToLongBits(getPrice())
          != java.lang.Double.doubleToLongBits(
              other.getPrice())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + PRICE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getPrice()));
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 商品属性值
     * </pre>
     *
     * Protobuf type {@code pbis.CommodityProperty.PropertyValue}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pbis.CommodityProperty.PropertyValue)
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValueOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CommodityProperty_PropertyValue_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CommodityProperty_PropertyValue_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.class, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.Builder.class);
      }

      // Construct using cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = "";

        name_ = "";

        price_ = 0D;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CommodityProperty_PropertyValue_descriptor;
      }

      @java.lang.Override
      public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue getDefaultInstanceForType() {
        return cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.getDefaultInstance();
      }

      @java.lang.Override
      public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue build() {
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue buildPartial() {
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue result = new cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue(this);
        result.code_ = code_;
        result.name_ = name_;
        result.price_ = price_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue) {
          return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue other) {
        if (other == cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.getDefaultInstance()) return this;
        if (!other.getCode().isEmpty()) {
          code_ = other.code_;
          onChanged();
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (other.getPrice() != 0D) {
          setPrice(other.getPrice());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object code_ = "";
      /**
       * <pre>
       * （必传）属性值code
       * </pre>
       *
       * <code>string code = 1;</code>
       * @return The code.
       */
      public java.lang.String getCode() {
        java.lang.Object ref = code_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          code_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * （必传）属性值code
       * </pre>
       *
       * <code>string code = 1;</code>
       * @return The bytes for code.
       */
      public com.google.protobuf.ByteString
          getCodeBytes() {
        java.lang.Object ref = code_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          code_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * （必传）属性值code
       * </pre>
       *
       * <code>string code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * （必传）属性值code
       * </pre>
       *
       * <code>string code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = getDefaultInstance().getCode();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * （必传）属性值code
       * </pre>
       *
       * <code>string code = 1;</code>
       * @param value The bytes for code to set.
       * @return This builder for chaining.
       */
      public Builder setCodeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        code_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <pre>
       * （必传）属性值名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * （必传）属性值名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * （必传）属性值名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * （必传）属性值名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * （必传）属性值名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private double price_ ;
      /**
       * <pre>
       * （必传）属性值价格
       * </pre>
       *
       * <code>double price = 3;</code>
       * @return The price.
       */
      @java.lang.Override
      public double getPrice() {
        return price_;
      }
      /**
       * <pre>
       * （必传）属性值价格
       * </pre>
       *
       * <code>double price = 3;</code>
       * @param value The price to set.
       * @return This builder for chaining.
       */
      public Builder setPrice(double value) {
        
        price_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * （必传）属性值价格
       * </pre>
       *
       * <code>double price = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPrice() {
        
        price_ = 0D;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pbis.CommodityProperty.PropertyValue)
    }

    // @@protoc_insertion_point(class_scope:pbis.CommodityProperty.PropertyValue)
    private static final cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue();
    }

    public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PropertyValue>
        PARSER = new com.google.protobuf.AbstractParser<PropertyValue>() {
      @java.lang.Override
      public PropertyValue parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PropertyValue(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PropertyValue> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PropertyValue> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public static final int NAME_FIELD_NUMBER = 1;
  private cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName name_;
  /**
   * <pre>
   * （必传）商品属性名称
   * </pre>
   *
   * <code>.pbis.CommodityProperty.PropertyName name = 1;</code>
   * @return Whether the name field is set.
   */
  @java.lang.Override
  public boolean hasName() {
    return name_ != null;
  }
  /**
   * <pre>
   * （必传）商品属性名称
   * </pre>
   *
   * <code>.pbis.CommodityProperty.PropertyName name = 1;</code>
   * @return The name.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName getName() {
    return name_ == null ? cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.getDefaultInstance() : name_;
  }
  /**
   * <pre>
   * （必传）商品属性名称
   * </pre>
   *
   * <code>.pbis.CommodityProperty.PropertyName name = 1;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyNameOrBuilder getNameOrBuilder() {
    return getName();
  }

  public static final int VALUE_FIELD_NUMBER = 2;
  private cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue value_;
  /**
   * <pre>
   * （必传）商品属性值
   * </pre>
   *
   * <code>.pbis.CommodityProperty.PropertyValue value = 2;</code>
   * @return Whether the value field is set.
   */
  @java.lang.Override
  public boolean hasValue() {
    return value_ != null;
  }
  /**
   * <pre>
   * （必传）商品属性值
   * </pre>
   *
   * <code>.pbis.CommodityProperty.PropertyValue value = 2;</code>
   * @return The value.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue getValue() {
    return value_ == null ? cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.getDefaultInstance() : value_;
  }
  /**
   * <pre>
   * （必传）商品属性值
   * </pre>
   *
   * <code>.pbis.CommodityProperty.PropertyValue value = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValueOrBuilder getValueOrBuilder() {
    return getValue();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (name_ != null) {
      output.writeMessage(1, getName());
    }
    if (value_ != null) {
      output.writeMessage(2, getValue());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (name_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getName());
    }
    if (value_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getValue());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty other = (cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty) obj;

    if (hasName() != other.hasName()) return false;
    if (hasName()) {
      if (!getName()
          .equals(other.getName())) return false;
    }
    if (hasValue() != other.hasValue()) return false;
    if (hasValue()) {
      if (!getValue()
          .equals(other.getValue())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasName()) {
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
    }
    if (hasValue()) {
      hash = (37 * hash) + VALUE_FIELD_NUMBER;
      hash = (53 * hash) + getValue().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 商品属性信息
   * </pre>
   *
   * Protobuf type {@code pbis.CommodityProperty}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:pbis.CommodityProperty)
      cn.hexcloud.pbis.common.service.facade.payment.CommodityPropertyOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CommodityProperty_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CommodityProperty_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.class, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (nameBuilder_ == null) {
        name_ = null;
      } else {
        name_ = null;
        nameBuilder_ = null;
      }
      if (valueBuilder_ == null) {
        value_ = null;
      } else {
        value_ = null;
        valueBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CommodityProperty_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty build() {
      cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty buildPartial() {
      cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty result = new cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty(this);
      if (nameBuilder_ == null) {
        result.name_ = name_;
      } else {
        result.name_ = nameBuilder_.build();
      }
      if (valueBuilder_ == null) {
        result.value_ = value_;
      } else {
        result.value_ = valueBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty other) {
      if (other == cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.getDefaultInstance()) return this;
      if (other.hasName()) {
        mergeName(other.getName());
      }
      if (other.hasValue()) {
        mergeValue(other.getValue());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName name_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.Builder, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyNameOrBuilder> nameBuilder_;
    /**
     * <pre>
     * （必传）商品属性名称
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyName name = 1;</code>
     * @return Whether the name field is set.
     */
    public boolean hasName() {
      return nameBuilder_ != null || name_ != null;
    }
    /**
     * <pre>
     * （必传）商品属性名称
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyName name = 1;</code>
     * @return The name.
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName getName() {
      if (nameBuilder_ == null) {
        return name_ == null ? cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.getDefaultInstance() : name_;
      } else {
        return nameBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （必传）商品属性名称
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyName name = 1;</code>
     */
    public Builder setName(cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName value) {
      if (nameBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        name_ = value;
        onChanged();
      } else {
        nameBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （必传）商品属性名称
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyName name = 1;</code>
     */
    public Builder setName(
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.Builder builderForValue) {
      if (nameBuilder_ == null) {
        name_ = builderForValue.build();
        onChanged();
      } else {
        nameBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （必传）商品属性名称
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyName name = 1;</code>
     */
    public Builder mergeName(cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName value) {
      if (nameBuilder_ == null) {
        if (name_ != null) {
          name_ =
            cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.newBuilder(name_).mergeFrom(value).buildPartial();
        } else {
          name_ = value;
        }
        onChanged();
      } else {
        nameBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （必传）商品属性名称
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyName name = 1;</code>
     */
    public Builder clearName() {
      if (nameBuilder_ == null) {
        name_ = null;
        onChanged();
      } else {
        name_ = null;
        nameBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （必传）商品属性名称
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyName name = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.Builder getNameBuilder() {
      
      onChanged();
      return getNameFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （必传）商品属性名称
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyName name = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyNameOrBuilder getNameOrBuilder() {
      if (nameBuilder_ != null) {
        return nameBuilder_.getMessageOrBuilder();
      } else {
        return name_ == null ?
            cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.getDefaultInstance() : name_;
      }
    }
    /**
     * <pre>
     * （必传）商品属性名称
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyName name = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.Builder, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyNameOrBuilder> 
        getNameFieldBuilder() {
      if (nameBuilder_ == null) {
        nameBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName.Builder, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyNameOrBuilder>(
                getName(),
                getParentForChildren(),
                isClean());
        name_ = null;
      }
      return nameBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue value_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.Builder, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValueOrBuilder> valueBuilder_;
    /**
     * <pre>
     * （必传）商品属性值
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyValue value = 2;</code>
     * @return Whether the value field is set.
     */
    public boolean hasValue() {
      return valueBuilder_ != null || value_ != null;
    }
    /**
     * <pre>
     * （必传）商品属性值
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyValue value = 2;</code>
     * @return The value.
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue getValue() {
      if (valueBuilder_ == null) {
        return value_ == null ? cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.getDefaultInstance() : value_;
      } else {
        return valueBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （必传）商品属性值
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyValue value = 2;</code>
     */
    public Builder setValue(cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue value) {
      if (valueBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        value_ = value;
        onChanged();
      } else {
        valueBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （必传）商品属性值
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyValue value = 2;</code>
     */
    public Builder setValue(
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.Builder builderForValue) {
      if (valueBuilder_ == null) {
        value_ = builderForValue.build();
        onChanged();
      } else {
        valueBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （必传）商品属性值
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyValue value = 2;</code>
     */
    public Builder mergeValue(cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue value) {
      if (valueBuilder_ == null) {
        if (value_ != null) {
          value_ =
            cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.newBuilder(value_).mergeFrom(value).buildPartial();
        } else {
          value_ = value;
        }
        onChanged();
      } else {
        valueBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （必传）商品属性值
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyValue value = 2;</code>
     */
    public Builder clearValue() {
      if (valueBuilder_ == null) {
        value_ = null;
        onChanged();
      } else {
        value_ = null;
        valueBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （必传）商品属性值
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyValue value = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.Builder getValueBuilder() {
      
      onChanged();
      return getValueFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （必传）商品属性值
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyValue value = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValueOrBuilder getValueOrBuilder() {
      if (valueBuilder_ != null) {
        return valueBuilder_.getMessageOrBuilder();
      } else {
        return value_ == null ?
            cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.getDefaultInstance() : value_;
      }
    }
    /**
     * <pre>
     * （必传）商品属性值
     * </pre>
     *
     * <code>.pbis.CommodityProperty.PropertyValue value = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.Builder, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValueOrBuilder> 
        getValueFieldBuilder() {
      if (valueBuilder_ == null) {
        valueBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue.Builder, cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValueOrBuilder>(
                getValue(),
                getParentForChildren(),
                isClean());
        value_ = null;
      }
      return valueBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:pbis.CommodityProperty)
  }

  // @@protoc_insertion_point(class_scope:pbis.CommodityProperty)
  private static final cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty();
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CommodityProperty>
      PARSER = new com.google.protobuf.AbstractParser<CommodityProperty>() {
    @java.lang.Override
    public CommodityProperty parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new CommodityProperty(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<CommodityProperty> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CommodityProperty> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

