// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

public interface CommodityPropertyOrBuilder extends
    // @@protoc_insertion_point(interface_extends:pbis.CommodityProperty)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）商品属性名称
   * </pre>
   *
   * <code>.pbis.CommodityProperty.PropertyName name = 1;</code>
   * @return Whether the name field is set.
   */
  boolean hasName();
  /**
   * <pre>
   * （必传）商品属性名称
   * </pre>
   *
   * <code>.pbis.CommodityProperty.PropertyName name = 1;</code>
   * @return The name.
   */
  cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyName getName();
  /**
   * <pre>
   * （必传）商品属性名称
   * </pre>
   *
   * <code>.pbis.CommodityProperty.PropertyName name = 1;</code>
   */
  cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyNameOrBuilder getNameOrBuilder();

  /**
   * <pre>
   * （必传）商品属性值
   * </pre>
   *
   * <code>.pbis.CommodityProperty.PropertyValue value = 2;</code>
   * @return Whether the value field is set.
   */
  boolean hasValue();
  /**
   * <pre>
   * （必传）商品属性值
   * </pre>
   *
   * <code>.pbis.CommodityProperty.PropertyValue value = 2;</code>
   * @return The value.
   */
  cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValue getValue();
  /**
   * <pre>
   * （必传）商品属性值
   * </pre>
   *
   * <code>.pbis.CommodityProperty.PropertyValue value = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.payment.CommodityProperty.PropertyValueOrBuilder getValueOrBuilder();
}
