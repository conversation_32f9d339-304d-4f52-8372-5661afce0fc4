// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

/**
 * <pre>
 * 下单交易信息
 * </pre>
 *
 * Protobuf type {@code pbis.CreatePaymentSection}
 */
public final class CreatePaymentSection extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:pbis.CreatePaymentSection)
    CreatePaymentSectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use CreatePaymentSection.newBuilder() to construct.
  private CreatePaymentSection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private CreatePaymentSection() {
    channel_ = "";
    transactionId_ = "";
    payer_ = "";
    description_ = "";
    extendedParams_ = "";
    transactionTime_ = "";
    currency_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new CreatePaymentSection();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private CreatePaymentSection(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            channel_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            transactionId_ = s;
            break;
          }
          case 24: {

            amount_ = input.readInt32();
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            payer_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            description_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            extendedParams_ = s;
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            transactionTime_ = s;
            break;
          }
          case 66: {
            java.lang.String s = input.readStringRequireUtf8();

            currency_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CreatePaymentSection_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CreatePaymentSection_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection.class, cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection.Builder.class);
  }

  public static final int CHANNEL_FIELD_NUMBER = 1;
  private volatile java.lang.Object channel_;
  /**
   * <pre>
   * （必传）渠道编码，ALIPAY(支付宝)、WXPAY(微信支付)、UNIONPAY(银联支付)、HEXUNION(合阔聚合支付)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  @java.lang.Override
  public java.lang.String getChannel() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channel_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）渠道编码，ALIPAY(支付宝)、WXPAY(微信支付)、UNIONPAY(银联支付)、HEXUNION(合阔聚合支付)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelBytes() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TRANSACTION_ID_FIELD_NUMBER = 2;
  private volatile java.lang.Object transactionId_;
  /**
   * <pre>
   * （必传）传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string transaction_id = 2;</code>
   * @return The transactionId.
   */
  @java.lang.Override
  public java.lang.String getTransactionId() {
    java.lang.Object ref = transactionId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      transactionId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string transaction_id = 2;</code>
   * @return The bytes for transactionId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTransactionIdBytes() {
    java.lang.Object ref = transactionId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      transactionId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AMOUNT_FIELD_NUMBER = 3;
  private int amount_;
  /**
   * <pre>
   * （必传）下单金额（单位：分）
   * </pre>
   *
   * <code>int32 amount = 3;</code>
   * @return The amount.
   */
  @java.lang.Override
  public int getAmount() {
    return amount_;
  }

  public static final int PAYER_FIELD_NUMBER = 4;
  private volatile java.lang.Object payer_;
  /**
   * <pre>
   * （可选）买家标识
   * </pre>
   *
   * <code>string payer = 4;</code>
   * @return The payer.
   */
  @java.lang.Override
  public java.lang.String getPayer() {
    java.lang.Object ref = payer_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      payer_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）买家标识
   * </pre>
   *
   * <code>string payer = 4;</code>
   * @return The bytes for payer.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPayerBytes() {
    java.lang.Object ref = payer_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      payer_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DESCRIPTION_FIELD_NUMBER = 5;
  private volatile java.lang.Object description_;
  /**
   * <pre>
   * （可选）商品描述，微信小程序支付必传
   * </pre>
   *
   * <code>string description = 5;</code>
   * @return The description.
   */
  @java.lang.Override
  public java.lang.String getDescription() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      description_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）商品描述，微信小程序支付必传
   * </pre>
   *
   * <code>string description = 5;</code>
   * @return The bytes for description.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescriptionBytes() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      description_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int EXTENDED_PARAMS_FIELD_NUMBER = 6;
  private volatile java.lang.Object extendedParams_;
  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 6;</code>
   * @return The extendedParams.
   */
  @java.lang.Override
  public java.lang.String getExtendedParams() {
    java.lang.Object ref = extendedParams_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      extendedParams_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 6;</code>
   * @return The bytes for extendedParams.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getExtendedParamsBytes() {
    java.lang.Object ref = extendedParams_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      extendedParams_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TRANSACTION_TIME_FIELD_NUMBER = 7;
  private volatile java.lang.Object transactionTime_;
  /**
   * <pre>
   * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
   * </pre>
   *
   * <code>string transaction_time = 7;</code>
   * @return The transactionTime.
   */
  @java.lang.Override
  public java.lang.String getTransactionTime() {
    java.lang.Object ref = transactionTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      transactionTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
   * </pre>
   *
   * <code>string transaction_time = 7;</code>
   * @return The bytes for transactionTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTransactionTimeBytes() {
    java.lang.Object ref = transactionTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      transactionTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CURRENCY_FIELD_NUMBER = 8;
  private volatile java.lang.Object currency_;
  /**
   * <pre>
   * （可选）币种
   * </pre>
   *
   * <code>string currency = 8;</code>
   * @return The currency.
   */
  @java.lang.Override
  public java.lang.String getCurrency() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      currency_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）币种
   * </pre>
   *
   * <code>string currency = 8;</code>
   * @return The bytes for currency.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCurrencyBytes() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      currency_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getChannelBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channel_);
    }
    if (!getTransactionIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, transactionId_);
    }
    if (amount_ != 0) {
      output.writeInt32(3, amount_);
    }
    if (!getPayerBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, payer_);
    }
    if (!getDescriptionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, description_);
    }
    if (!getExtendedParamsBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, extendedParams_);
    }
    if (!getTransactionTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, transactionTime_);
    }
    if (!getCurrencyBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, currency_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getChannelBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channel_);
    }
    if (!getTransactionIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, transactionId_);
    }
    if (amount_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, amount_);
    }
    if (!getPayerBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, payer_);
    }
    if (!getDescriptionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, description_);
    }
    if (!getExtendedParamsBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, extendedParams_);
    }
    if (!getTransactionTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, transactionTime_);
    }
    if (!getCurrencyBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, currency_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection other = (cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection) obj;

    if (!getChannel()
        .equals(other.getChannel())) return false;
    if (!getTransactionId()
        .equals(other.getTransactionId())) return false;
    if (getAmount()
        != other.getAmount()) return false;
    if (!getPayer()
        .equals(other.getPayer())) return false;
    if (!getDescription()
        .equals(other.getDescription())) return false;
    if (!getExtendedParams()
        .equals(other.getExtendedParams())) return false;
    if (!getTransactionTime()
        .equals(other.getTransactionTime())) return false;
    if (!getCurrency()
        .equals(other.getCurrency())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
    hash = (53 * hash) + getChannel().hashCode();
    hash = (37 * hash) + TRANSACTION_ID_FIELD_NUMBER;
    hash = (53 * hash) + getTransactionId().hashCode();
    hash = (37 * hash) + AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getAmount();
    hash = (37 * hash) + PAYER_FIELD_NUMBER;
    hash = (53 * hash) + getPayer().hashCode();
    hash = (37 * hash) + DESCRIPTION_FIELD_NUMBER;
    hash = (53 * hash) + getDescription().hashCode();
    hash = (37 * hash) + EXTENDED_PARAMS_FIELD_NUMBER;
    hash = (53 * hash) + getExtendedParams().hashCode();
    hash = (37 * hash) + TRANSACTION_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getTransactionTime().hashCode();
    hash = (37 * hash) + CURRENCY_FIELD_NUMBER;
    hash = (53 * hash) + getCurrency().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 下单交易信息
   * </pre>
   *
   * Protobuf type {@code pbis.CreatePaymentSection}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:pbis.CreatePaymentSection)
      cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CreatePaymentSection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CreatePaymentSection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection.class, cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channel_ = "";

      transactionId_ = "";

      amount_ = 0;

      payer_ = "";

      description_ = "";

      extendedParams_ = "";

      transactionTime_ = "";

      currency_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CreatePaymentSection_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection build() {
      cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection buildPartial() {
      cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection result = new cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection(this);
      result.channel_ = channel_;
      result.transactionId_ = transactionId_;
      result.amount_ = amount_;
      result.payer_ = payer_;
      result.description_ = description_;
      result.extendedParams_ = extendedParams_;
      result.transactionTime_ = transactionTime_;
      result.currency_ = currency_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection other) {
      if (other == cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection.getDefaultInstance()) return this;
      if (!other.getChannel().isEmpty()) {
        channel_ = other.channel_;
        onChanged();
      }
      if (!other.getTransactionId().isEmpty()) {
        transactionId_ = other.transactionId_;
        onChanged();
      }
      if (other.getAmount() != 0) {
        setAmount(other.getAmount());
      }
      if (!other.getPayer().isEmpty()) {
        payer_ = other.payer_;
        onChanged();
      }
      if (!other.getDescription().isEmpty()) {
        description_ = other.description_;
        onChanged();
      }
      if (!other.getExtendedParams().isEmpty()) {
        extendedParams_ = other.extendedParams_;
        onChanged();
      }
      if (!other.getTransactionTime().isEmpty()) {
        transactionTime_ = other.transactionTime_;
        onChanged();
      }
      if (!other.getCurrency().isEmpty()) {
        currency_ = other.currency_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object channel_ = "";
    /**
     * <pre>
     * （必传）渠道编码，ALIPAY(支付宝)、WXPAY(微信支付)、UNIONPAY(银联支付)、HEXUNION(合阔聚合支付)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return The channel.
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channel_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）渠道编码，ALIPAY(支付宝)、WXPAY(微信支付)、UNIONPAY(银联支付)、HEXUNION(合阔聚合支付)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return The bytes for channel.
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）渠道编码，ALIPAY(支付宝)、WXPAY(微信支付)、UNIONPAY(银联支付)、HEXUNION(合阔聚合支付)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @param value The channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannel(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channel_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）渠道编码，ALIPAY(支付宝)、WXPAY(微信支付)、UNIONPAY(银联支付)、HEXUNION(合阔聚合支付)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannel() {
      
      channel_ = getDefaultInstance().getChannel();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）渠道编码，ALIPAY(支付宝)、WXPAY(微信支付)、UNIONPAY(银联支付)、HEXUNION(合阔聚合支付)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @param value The bytes for channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channel_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object transactionId_ = "";
    /**
     * <pre>
     * （必传）传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string transaction_id = 2;</code>
     * @return The transactionId.
     */
    public java.lang.String getTransactionId() {
      java.lang.Object ref = transactionId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        transactionId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string transaction_id = 2;</code>
     * @return The bytes for transactionId.
     */
    public com.google.protobuf.ByteString
        getTransactionIdBytes() {
      java.lang.Object ref = transactionId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string transaction_id = 2;</code>
     * @param value The transactionId to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      transactionId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string transaction_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearTransactionId() {
      
      transactionId_ = getDefaultInstance().getTransactionId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string transaction_id = 2;</code>
     * @param value The bytes for transactionId to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      transactionId_ = value;
      onChanged();
      return this;
    }

    private int amount_ ;
    /**
     * <pre>
     * （必传）下单金额（单位：分）
     * </pre>
     *
     * <code>int32 amount = 3;</code>
     * @return The amount.
     */
    @java.lang.Override
    public int getAmount() {
      return amount_;
    }
    /**
     * <pre>
     * （必传）下单金额（单位：分）
     * </pre>
     *
     * <code>int32 amount = 3;</code>
     * @param value The amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmount(int value) {
      
      amount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）下单金额（单位：分）
     * </pre>
     *
     * <code>int32 amount = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmount() {
      
      amount_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object payer_ = "";
    /**
     * <pre>
     * （可选）买家标识
     * </pre>
     *
     * <code>string payer = 4;</code>
     * @return The payer.
     */
    public java.lang.String getPayer() {
      java.lang.Object ref = payer_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        payer_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）买家标识
     * </pre>
     *
     * <code>string payer = 4;</code>
     * @return The bytes for payer.
     */
    public com.google.protobuf.ByteString
        getPayerBytes() {
      java.lang.Object ref = payer_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payer_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）买家标识
     * </pre>
     *
     * <code>string payer = 4;</code>
     * @param value The payer to set.
     * @return This builder for chaining.
     */
    public Builder setPayer(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      payer_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）买家标识
     * </pre>
     *
     * <code>string payer = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayer() {
      
      payer_ = getDefaultInstance().getPayer();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）买家标识
     * </pre>
     *
     * <code>string payer = 4;</code>
     * @param value The bytes for payer to set.
     * @return This builder for chaining.
     */
    public Builder setPayerBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      payer_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object description_ = "";
    /**
     * <pre>
     * （可选）商品描述，微信小程序支付必传
     * </pre>
     *
     * <code>string description = 5;</code>
     * @return The description.
     */
    public java.lang.String getDescription() {
      java.lang.Object ref = description_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        description_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）商品描述，微信小程序支付必传
     * </pre>
     *
     * <code>string description = 5;</code>
     * @return The bytes for description.
     */
    public com.google.protobuf.ByteString
        getDescriptionBytes() {
      java.lang.Object ref = description_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        description_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）商品描述，微信小程序支付必传
     * </pre>
     *
     * <code>string description = 5;</code>
     * @param value The description to set.
     * @return This builder for chaining.
     */
    public Builder setDescription(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      description_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）商品描述，微信小程序支付必传
     * </pre>
     *
     * <code>string description = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearDescription() {
      
      description_ = getDefaultInstance().getDescription();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）商品描述，微信小程序支付必传
     * </pre>
     *
     * <code>string description = 5;</code>
     * @param value The bytes for description to set.
     * @return This builder for chaining.
     */
    public Builder setDescriptionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      description_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object extendedParams_ = "";
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 6;</code>
     * @return The extendedParams.
     */
    public java.lang.String getExtendedParams() {
      java.lang.Object ref = extendedParams_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        extendedParams_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 6;</code>
     * @return The bytes for extendedParams.
     */
    public com.google.protobuf.ByteString
        getExtendedParamsBytes() {
      java.lang.Object ref = extendedParams_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extendedParams_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 6;</code>
     * @param value The extendedParams to set.
     * @return This builder for chaining.
     */
    public Builder setExtendedParams(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      extendedParams_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearExtendedParams() {
      
      extendedParams_ = getDefaultInstance().getExtendedParams();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 6;</code>
     * @param value The bytes for extendedParams to set.
     * @return This builder for chaining.
     */
    public Builder setExtendedParamsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      extendedParams_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object transactionTime_ = "";
    /**
     * <pre>
     * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
     * </pre>
     *
     * <code>string transaction_time = 7;</code>
     * @return The transactionTime.
     */
    public java.lang.String getTransactionTime() {
      java.lang.Object ref = transactionTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        transactionTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
     * </pre>
     *
     * <code>string transaction_time = 7;</code>
     * @return The bytes for transactionTime.
     */
    public com.google.protobuf.ByteString
        getTransactionTimeBytes() {
      java.lang.Object ref = transactionTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
     * </pre>
     *
     * <code>string transaction_time = 7;</code>
     * @param value The transactionTime to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      transactionTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
     * </pre>
     *
     * <code>string transaction_time = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearTransactionTime() {
      
      transactionTime_ = getDefaultInstance().getTransactionTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
     * </pre>
     *
     * <code>string transaction_time = 7;</code>
     * @param value The bytes for transactionTime to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      transactionTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object currency_ = "";
    /**
     * <pre>
     * （可选）币种
     * </pre>
     *
     * <code>string currency = 8;</code>
     * @return The currency.
     */
    public java.lang.String getCurrency() {
      java.lang.Object ref = currency_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        currency_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）币种
     * </pre>
     *
     * <code>string currency = 8;</code>
     * @return The bytes for currency.
     */
    public com.google.protobuf.ByteString
        getCurrencyBytes() {
      java.lang.Object ref = currency_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        currency_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）币种
     * </pre>
     *
     * <code>string currency = 8;</code>
     * @param value The currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrency(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      currency_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）币种
     * </pre>
     *
     * <code>string currency = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurrency() {
      
      currency_ = getDefaultInstance().getCurrency();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）币种
     * </pre>
     *
     * <code>string currency = 8;</code>
     * @param value The bytes for currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrencyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      currency_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:pbis.CreatePaymentSection)
  }

  // @@protoc_insertion_point(class_scope:pbis.CreatePaymentSection)
  private static final cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection();
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CreatePaymentSection>
      PARSER = new com.google.protobuf.AbstractParser<CreatePaymentSection>() {
    @java.lang.Override
    public CreatePaymentSection parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new CreatePaymentSection(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<CreatePaymentSection> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CreatePaymentSection> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CreatePaymentSection getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

