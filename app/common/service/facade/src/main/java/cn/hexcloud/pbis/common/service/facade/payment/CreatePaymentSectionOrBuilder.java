// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

public interface CreatePaymentSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:pbis.CreatePaymentSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）渠道编码，ALIPAY(支付宝)、WXPAY(微信支付)、UNIONPAY(银联支付)、HEXUNION(合阔聚合支付)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  java.lang.String getChannel();
  /**
   * <pre>
   * （必传）渠道编码，ALIPAY(支付宝)、WXPAY(微信支付)、UNIONPAY(银联支付)、HEXUNION(合阔聚合支付)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  com.google.protobuf.ByteString
      getChannelBytes();

  /**
   * <pre>
   * （必传）传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string transaction_id = 2;</code>
   * @return The transactionId.
   */
  java.lang.String getTransactionId();
  /**
   * <pre>
   * （必传）传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string transaction_id = 2;</code>
   * @return The bytes for transactionId.
   */
  com.google.protobuf.ByteString
      getTransactionIdBytes();

  /**
   * <pre>
   * （必传）下单金额（单位：分）
   * </pre>
   *
   * <code>int32 amount = 3;</code>
   * @return The amount.
   */
  int getAmount();

  /**
   * <pre>
   * （可选）买家标识
   * </pre>
   *
   * <code>string payer = 4;</code>
   * @return The payer.
   */
  java.lang.String getPayer();
  /**
   * <pre>
   * （可选）买家标识
   * </pre>
   *
   * <code>string payer = 4;</code>
   * @return The bytes for payer.
   */
  com.google.protobuf.ByteString
      getPayerBytes();

  /**
   * <pre>
   * （可选）商品描述，微信小程序支付必传
   * </pre>
   *
   * <code>string description = 5;</code>
   * @return The description.
   */
  java.lang.String getDescription();
  /**
   * <pre>
   * （可选）商品描述，微信小程序支付必传
   * </pre>
   *
   * <code>string description = 5;</code>
   * @return The bytes for description.
   */
  com.google.protobuf.ByteString
      getDescriptionBytes();

  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 6;</code>
   * @return The extendedParams.
   */
  java.lang.String getExtendedParams();
  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 6;</code>
   * @return The bytes for extendedParams.
   */
  com.google.protobuf.ByteString
      getExtendedParamsBytes();

  /**
   * <pre>
   * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
   * </pre>
   *
   * <code>string transaction_time = 7;</code>
   * @return The transactionTime.
   */
  java.lang.String getTransactionTime();
  /**
   * <pre>
   * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
   * </pre>
   *
   * <code>string transaction_time = 7;</code>
   * @return The bytes for transactionTime.
   */
  com.google.protobuf.ByteString
      getTransactionTimeBytes();

  /**
   * <pre>
   * （可选）币种
   * </pre>
   *
   * <code>string currency = 8;</code>
   * @return The currency.
   */
  java.lang.String getCurrency();
  /**
   * <pre>
   * （可选）币种
   * </pre>
   *
   * <code>string currency = 8;</code>
   * @return The bytes for currency.
   */
  com.google.protobuf.ByteString
      getCurrencyBytes();
}
