// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

/**
 * <pre>
 * 下单接口响应信息
 * </pre>
 *
 * Protobuf type {@code pbis.CreateResponse}
 */
public final class CreateResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:pbis.CreateResponse)
    CreateResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use CreateResponse.newBuilder() to construct.
  private CreateResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private CreateResponse() {
    errorCode_ = "";
    errorMessage_ = "";
    warningMessage_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new CreateResponse();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private CreateResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            errorCode_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            errorMessage_ = s;
            break;
          }
          case 24: {

            warning_ = input.readBool();
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            warningMessage_ = s;
            break;
          }
          case 42: {
            cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.Builder subBuilder = null;
            if (createResultSection_ != null) {
              subBuilder = createResultSection_.toBuilder();
            }
            createResultSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(createResultSection_);
              createResultSection_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CreateResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CreateResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.payment.CreateResponse.class, cn.hexcloud.pbis.common.service.facade.payment.CreateResponse.Builder.class);
  }

  public static final int ERROR_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object errorCode_;
  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The errorCode.
   */
  @java.lang.Override
  public java.lang.String getErrorCode() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）异常编码
   * </pre>
   *
   * <code>string error_code = 1;</code>
   * @return The bytes for errorCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorCodeBytes() {
    java.lang.Object ref = errorCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ERROR_MESSAGE_FIELD_NUMBER = 2;
  private volatile java.lang.Object errorMessage_;
  /**
   * <pre>
   * （可选）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The errorMessage.
   */
  @java.lang.Override
  public java.lang.String getErrorMessage() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      errorMessage_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）异常信息
   * </pre>
   *
   * <code>string error_message = 2;</code>
   * @return The bytes for errorMessage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getErrorMessageBytes() {
    java.lang.Object ref = errorMessage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      errorMessage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int WARNING_FIELD_NUMBER = 3;
  private boolean warning_;
  /**
   * <pre>
   * （必传）是否有警告信息
   * </pre>
   *
   * <code>bool warning = 3;</code>
   * @return The warning.
   */
  @java.lang.Override
  public boolean getWarning() {
    return warning_;
  }

  public static final int WARNING_MESSAGE_FIELD_NUMBER = 4;
  private volatile java.lang.Object warningMessage_;
  /**
   * <pre>
   * （可选）警告信息
   * </pre>
   *
   * <code>string warning_message = 4;</code>
   * @return The warningMessage.
   */
  @java.lang.Override
  public java.lang.String getWarningMessage() {
    java.lang.Object ref = warningMessage_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      warningMessage_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）警告信息
   * </pre>
   *
   * <code>string warning_message = 4;</code>
   * @return The bytes for warningMessage.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getWarningMessageBytes() {
    java.lang.Object ref = warningMessage_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      warningMessage_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CREATE_RESULT_SECTION_FIELD_NUMBER = 5;
  private cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection createResultSection_;
  /**
   * <pre>
   * （必传）交易结果信息
   * </pre>
   *
   * <code>.pbis.CreateResultSection create_result_section = 5;</code>
   * @return Whether the createResultSection field is set.
   */
  @java.lang.Override
  public boolean hasCreateResultSection() {
    return createResultSection_ != null;
  }
  /**
   * <pre>
   * （必传）交易结果信息
   * </pre>
   *
   * <code>.pbis.CreateResultSection create_result_section = 5;</code>
   * @return The createResultSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection getCreateResultSection() {
    return createResultSection_ == null ? cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.getDefaultInstance() : createResultSection_;
  }
  /**
   * <pre>
   * （必传）交易结果信息
   * </pre>
   *
   * <code>.pbis.CreateResultSection create_result_section = 5;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CreateResultSectionOrBuilder getCreateResultSectionOrBuilder() {
    return getCreateResultSection();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getErrorCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, errorMessage_);
    }
    if (warning_ != false) {
      output.writeBool(3, warning_);
    }
    if (!getWarningMessageBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, warningMessage_);
    }
    if (createResultSection_ != null) {
      output.writeMessage(5, getCreateResultSection());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getErrorCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, errorCode_);
    }
    if (!getErrorMessageBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, errorMessage_);
    }
    if (warning_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(3, warning_);
    }
    if (!getWarningMessageBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, warningMessage_);
    }
    if (createResultSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(5, getCreateResultSection());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.CreateResponse)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.payment.CreateResponse other = (cn.hexcloud.pbis.common.service.facade.payment.CreateResponse) obj;

    if (!getErrorCode()
        .equals(other.getErrorCode())) return false;
    if (!getErrorMessage()
        .equals(other.getErrorMessage())) return false;
    if (getWarning()
        != other.getWarning()) return false;
    if (!getWarningMessage()
        .equals(other.getWarningMessage())) return false;
    if (hasCreateResultSection() != other.hasCreateResultSection()) return false;
    if (hasCreateResultSection()) {
      if (!getCreateResultSection()
          .equals(other.getCreateResultSection())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ERROR_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorCode().hashCode();
    hash = (37 * hash) + ERROR_MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getErrorMessage().hashCode();
    hash = (37 * hash) + WARNING_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getWarning());
    hash = (37 * hash) + WARNING_MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getWarningMessage().hashCode();
    if (hasCreateResultSection()) {
      hash = (37 * hash) + CREATE_RESULT_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getCreateResultSection().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.CreateResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 下单接口响应信息
   * </pre>
   *
   * Protobuf type {@code pbis.CreateResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:pbis.CreateResponse)
      cn.hexcloud.pbis.common.service.facade.payment.CreateResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CreateResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CreateResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.CreateResponse.class, cn.hexcloud.pbis.common.service.facade.payment.CreateResponse.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.payment.CreateResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      errorCode_ = "";

      errorMessage_ = "";

      warning_ = false;

      warningMessage_ = "";

      if (createResultSectionBuilder_ == null) {
        createResultSection_ = null;
      } else {
        createResultSection_ = null;
        createResultSectionBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CreateResponse_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CreateResponse getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.CreateResponse.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CreateResponse build() {
      cn.hexcloud.pbis.common.service.facade.payment.CreateResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CreateResponse buildPartial() {
      cn.hexcloud.pbis.common.service.facade.payment.CreateResponse result = new cn.hexcloud.pbis.common.service.facade.payment.CreateResponse(this);
      result.errorCode_ = errorCode_;
      result.errorMessage_ = errorMessage_;
      result.warning_ = warning_;
      result.warningMessage_ = warningMessage_;
      if (createResultSectionBuilder_ == null) {
        result.createResultSection_ = createResultSection_;
      } else {
        result.createResultSection_ = createResultSectionBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.CreateResponse) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.CreateResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.CreateResponse other) {
      if (other == cn.hexcloud.pbis.common.service.facade.payment.CreateResponse.getDefaultInstance()) return this;
      if (!other.getErrorCode().isEmpty()) {
        errorCode_ = other.errorCode_;
        onChanged();
      }
      if (!other.getErrorMessage().isEmpty()) {
        errorMessage_ = other.errorMessage_;
        onChanged();
      }
      if (other.getWarning() != false) {
        setWarning(other.getWarning());
      }
      if (!other.getWarningMessage().isEmpty()) {
        warningMessage_ = other.warningMessage_;
        onChanged();
      }
      if (other.hasCreateResultSection()) {
        mergeCreateResultSection(other.getCreateResultSection());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.payment.CreateResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.CreateResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object errorCode_ = "";
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return The errorCode.
     */
    public java.lang.String getErrorCode() {
      java.lang.Object ref = errorCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return The bytes for errorCode.
     */
    public com.google.protobuf.ByteString
        getErrorCodeBytes() {
      java.lang.Object ref = errorCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @param value The errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorCode() {
      
      errorCode_ = getDefaultInstance().getErrorCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）异常编码
     * </pre>
     *
     * <code>string error_code = 1;</code>
     * @param value The bytes for errorCode to set.
     * @return This builder for chaining.
     */
    public Builder setErrorCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object errorMessage_ = "";
    /**
     * <pre>
     * （可选）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return The errorMessage.
     */
    public java.lang.String getErrorMessage() {
      java.lang.Object ref = errorMessage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        errorMessage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return The bytes for errorMessage.
     */
    public com.google.protobuf.ByteString
        getErrorMessageBytes() {
      java.lang.Object ref = errorMessage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorMessage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @param value The errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessage(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      errorMessage_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearErrorMessage() {
      
      errorMessage_ = getDefaultInstance().getErrorMessage();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）异常信息
     * </pre>
     *
     * <code>string error_message = 2;</code>
     * @param value The bytes for errorMessage to set.
     * @return This builder for chaining.
     */
    public Builder setErrorMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      errorMessage_ = value;
      onChanged();
      return this;
    }

    private boolean warning_ ;
    /**
     * <pre>
     * （必传）是否有警告信息
     * </pre>
     *
     * <code>bool warning = 3;</code>
     * @return The warning.
     */
    @java.lang.Override
    public boolean getWarning() {
      return warning_;
    }
    /**
     * <pre>
     * （必传）是否有警告信息
     * </pre>
     *
     * <code>bool warning = 3;</code>
     * @param value The warning to set.
     * @return This builder for chaining.
     */
    public Builder setWarning(boolean value) {
      
      warning_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）是否有警告信息
     * </pre>
     *
     * <code>bool warning = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearWarning() {
      
      warning_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object warningMessage_ = "";
    /**
     * <pre>
     * （可选）警告信息
     * </pre>
     *
     * <code>string warning_message = 4;</code>
     * @return The warningMessage.
     */
    public java.lang.String getWarningMessage() {
      java.lang.Object ref = warningMessage_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        warningMessage_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）警告信息
     * </pre>
     *
     * <code>string warning_message = 4;</code>
     * @return The bytes for warningMessage.
     */
    public com.google.protobuf.ByteString
        getWarningMessageBytes() {
      java.lang.Object ref = warningMessage_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        warningMessage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）警告信息
     * </pre>
     *
     * <code>string warning_message = 4;</code>
     * @param value The warningMessage to set.
     * @return This builder for chaining.
     */
    public Builder setWarningMessage(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      warningMessage_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）警告信息
     * </pre>
     *
     * <code>string warning_message = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearWarningMessage() {
      
      warningMessage_ = getDefaultInstance().getWarningMessage();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）警告信息
     * </pre>
     *
     * <code>string warning_message = 4;</code>
     * @param value The bytes for warningMessage to set.
     * @return This builder for chaining.
     */
    public Builder setWarningMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      warningMessage_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection createResultSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection, cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.CreateResultSectionOrBuilder> createResultSectionBuilder_;
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.CreateResultSection create_result_section = 5;</code>
     * @return Whether the createResultSection field is set.
     */
    public boolean hasCreateResultSection() {
      return createResultSectionBuilder_ != null || createResultSection_ != null;
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.CreateResultSection create_result_section = 5;</code>
     * @return The createResultSection.
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection getCreateResultSection() {
      if (createResultSectionBuilder_ == null) {
        return createResultSection_ == null ? cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.getDefaultInstance() : createResultSection_;
      } else {
        return createResultSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.CreateResultSection create_result_section = 5;</code>
     */
    public Builder setCreateResultSection(cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection value) {
      if (createResultSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        createResultSection_ = value;
        onChanged();
      } else {
        createResultSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.CreateResultSection create_result_section = 5;</code>
     */
    public Builder setCreateResultSection(
        cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.Builder builderForValue) {
      if (createResultSectionBuilder_ == null) {
        createResultSection_ = builderForValue.build();
        onChanged();
      } else {
        createResultSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.CreateResultSection create_result_section = 5;</code>
     */
    public Builder mergeCreateResultSection(cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection value) {
      if (createResultSectionBuilder_ == null) {
        if (createResultSection_ != null) {
          createResultSection_ =
            cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.newBuilder(createResultSection_).mergeFrom(value).buildPartial();
        } else {
          createResultSection_ = value;
        }
        onChanged();
      } else {
        createResultSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.CreateResultSection create_result_section = 5;</code>
     */
    public Builder clearCreateResultSection() {
      if (createResultSectionBuilder_ == null) {
        createResultSection_ = null;
        onChanged();
      } else {
        createResultSection_ = null;
        createResultSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.CreateResultSection create_result_section = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.Builder getCreateResultSectionBuilder() {
      
      onChanged();
      return getCreateResultSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.CreateResultSection create_result_section = 5;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CreateResultSectionOrBuilder getCreateResultSectionOrBuilder() {
      if (createResultSectionBuilder_ != null) {
        return createResultSectionBuilder_.getMessageOrBuilder();
      } else {
        return createResultSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.getDefaultInstance() : createResultSection_;
      }
    }
    /**
     * <pre>
     * （必传）交易结果信息
     * </pre>
     *
     * <code>.pbis.CreateResultSection create_result_section = 5;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection, cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.CreateResultSectionOrBuilder> 
        getCreateResultSectionFieldBuilder() {
      if (createResultSectionBuilder_ == null) {
        createResultSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection, cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.CreateResultSectionOrBuilder>(
                getCreateResultSection(),
                getParentForChildren(),
                isClean());
        createResultSection_ = null;
      }
      return createResultSectionBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:pbis.CreateResponse)
  }

  // @@protoc_insertion_point(class_scope:pbis.CreateResponse)
  private static final cn.hexcloud.pbis.common.service.facade.payment.CreateResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.CreateResponse();
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CreateResponse>
      PARSER = new com.google.protobuf.AbstractParser<CreateResponse>() {
    @java.lang.Override
    public CreateResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new CreateResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<CreateResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CreateResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CreateResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

