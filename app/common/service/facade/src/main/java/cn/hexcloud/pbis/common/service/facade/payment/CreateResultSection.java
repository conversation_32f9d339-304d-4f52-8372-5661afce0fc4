// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

/**
 * <pre>
 * 下单交易结果信息
 * </pre>
 *
 * Protobuf type {@code pbis.CreateResultSection}
 */
public final class CreateResultSection extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:pbis.CreateResultSection)
    CreateResultSectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use CreateResultSection.newBuilder() to construct.
  private CreateResultSection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private CreateResultSection() {
    prePayId_ = "";
    payCode_ = "";
    payChannel_ = "";
    payMethod_ = "";
    extendedParams_ = "";
    tpTransactionId_ = "";
    prePaySign_ = "";
    packStr_ = "";
    frontUrl_ = "";
    timeExpire_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new CreateResultSection();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private CreateResultSection(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            prePayId_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            payCode_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            payChannel_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            payMethod_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            extendedParams_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            tpTransactionId_ = s;
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            prePaySign_ = s;
            break;
          }
          case 66: {
            java.lang.String s = input.readStringRequireUtf8();

            packStr_ = s;
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            frontUrl_ = s;
            break;
          }
          case 82: {
            java.lang.String s = input.readStringRequireUtf8();

            timeExpire_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CreateResultSection_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CreateResultSection_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.class, cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.Builder.class);
  }

  public static final int PRE_PAY_ID_FIELD_NUMBER = 1;
  private volatile java.lang.Object prePayId_;
  /**
   * <pre>
   * （必传）预支付交易号
   * </pre>
   *
   * <code>string pre_pay_id = 1;</code>
   * @return The prePayId.
   */
  @java.lang.Override
  public java.lang.String getPrePayId() {
    java.lang.Object ref = prePayId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      prePayId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）预支付交易号
   * </pre>
   *
   * <code>string pre_pay_id = 1;</code>
   * @return The bytes for prePayId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPrePayIdBytes() {
    java.lang.Object ref = prePayId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      prePayId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PAY_CODE_FIELD_NUMBER = 2;
  private volatile java.lang.Object payCode_;
  /**
   * <pre>
   * （可选）付款码
   * </pre>
   *
   * <code>string pay_code = 2;</code>
   * @return The payCode.
   */
  @java.lang.Override
  public java.lang.String getPayCode() {
    java.lang.Object ref = payCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      payCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）付款码
   * </pre>
   *
   * <code>string pay_code = 2;</code>
   * @return The bytes for payCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPayCodeBytes() {
    java.lang.Object ref = payCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      payCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PAY_CHANNEL_FIELD_NUMBER = 3;
  private volatile java.lang.Object payChannel_;
  /**
   * <pre>
   * （必传）支付渠道
   * </pre>
   *
   * <code>string pay_channel = 3;</code>
   * @return The payChannel.
   */
  @java.lang.Override
  public java.lang.String getPayChannel() {
    java.lang.Object ref = payChannel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      payChannel_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）支付渠道
   * </pre>
   *
   * <code>string pay_channel = 3;</code>
   * @return The bytes for payChannel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPayChannelBytes() {
    java.lang.Object ref = payChannel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      payChannel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PAY_METHOD_FIELD_NUMBER = 4;
  private volatile java.lang.Object payMethod_;
  /**
   * <pre>
   * （可选）用户真实支付方式
   * </pre>
   *
   * <code>string pay_method = 4;</code>
   * @return The payMethod.
   */
  @java.lang.Override
  public java.lang.String getPayMethod() {
    java.lang.Object ref = payMethod_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      payMethod_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）用户真实支付方式
   * </pre>
   *
   * <code>string pay_method = 4;</code>
   * @return The bytes for payMethod.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPayMethodBytes() {
    java.lang.Object ref = payMethod_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      payMethod_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int EXTENDED_PARAMS_FIELD_NUMBER = 5;
  private volatile java.lang.Object extendedParams_;
  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 5;</code>
   * @return The extendedParams.
   */
  @java.lang.Override
  public java.lang.String getExtendedParams() {
    java.lang.Object ref = extendedParams_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      extendedParams_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 5;</code>
   * @return The bytes for extendedParams.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getExtendedParamsBytes() {
    java.lang.Object ref = extendedParams_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      extendedParams_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TP_TRANSACTION_ID_FIELD_NUMBER = 6;
  private volatile java.lang.Object tpTransactionId_;
  /**
   * <pre>
   * （可选）第三方流水号
   * </pre>
   *
   * <code>string tp_transaction_id = 6;</code>
   * @return The tpTransactionId.
   */
  @java.lang.Override
  public java.lang.String getTpTransactionId() {
    java.lang.Object ref = tpTransactionId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      tpTransactionId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）第三方流水号
   * </pre>
   *
   * <code>string tp_transaction_id = 6;</code>
   * @return The bytes for tpTransactionId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTpTransactionIdBytes() {
    java.lang.Object ref = tpTransactionId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      tpTransactionId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PRE_PAY_SIGN_FIELD_NUMBER = 7;
  private volatile java.lang.Object prePaySign_;
  /**
   * <pre>
   * （可选）预支付交易客户端唤起支付所需签名
   * </pre>
   *
   * <code>string pre_pay_sign = 7;</code>
   * @return The prePaySign.
   */
  @java.lang.Override
  public java.lang.String getPrePaySign() {
    java.lang.Object ref = prePaySign_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      prePaySign_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）预支付交易客户端唤起支付所需签名
   * </pre>
   *
   * <code>string pre_pay_sign = 7;</code>
   * @return The bytes for prePaySign.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPrePaySignBytes() {
    java.lang.Object ref = prePaySign_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      prePaySign_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PACK_STR_FIELD_NUMBER = 8;
  private volatile java.lang.Object packStr_;
  /**
   * <pre>
   * （可选）预支付交易扩展字段，格式：a=b&amp;c=d
   * </pre>
   *
   * <code>string pack_str = 8;</code>
   * @return The packStr.
   */
  @java.lang.Override
  public java.lang.String getPackStr() {
    java.lang.Object ref = packStr_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      packStr_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）预支付交易扩展字段，格式：a=b&amp;c=d
   * </pre>
   *
   * <code>string pack_str = 8;</code>
   * @return The bytes for packStr.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPackStrBytes() {
    java.lang.Object ref = packStr_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      packStr_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int FRONT_URL_FIELD_NUMBER = 9;
  private volatile java.lang.Object frontUrl_;
  /**
   * <pre>
   * （可选）跳转地址,也可是二维码链接
   * </pre>
   *
   * <code>string front_url = 9;</code>
   * @return The frontUrl.
   */
  @java.lang.Override
  public java.lang.String getFrontUrl() {
    java.lang.Object ref = frontUrl_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      frontUrl_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）跳转地址,也可是二维码链接
   * </pre>
   *
   * <code>string front_url = 9;</code>
   * @return The bytes for frontUrl.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFrontUrlBytes() {
    java.lang.Object ref = frontUrl_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      frontUrl_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TIME_EXPIRE_FIELD_NUMBER = 10;
  private volatile java.lang.Object timeExpire_;
  /**
   * <pre>
   * （可选）过期时间
   * </pre>
   *
   * <code>string time_expire = 10;</code>
   * @return The timeExpire.
   */
  @java.lang.Override
  public java.lang.String getTimeExpire() {
    java.lang.Object ref = timeExpire_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      timeExpire_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）过期时间
   * </pre>
   *
   * <code>string time_expire = 10;</code>
   * @return The bytes for timeExpire.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTimeExpireBytes() {
    java.lang.Object ref = timeExpire_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      timeExpire_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getPrePayIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, prePayId_);
    }
    if (!getPayCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, payCode_);
    }
    if (!getPayChannelBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, payChannel_);
    }
    if (!getPayMethodBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, payMethod_);
    }
    if (!getExtendedParamsBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, extendedParams_);
    }
    if (!getTpTransactionIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, tpTransactionId_);
    }
    if (!getPrePaySignBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, prePaySign_);
    }
    if (!getPackStrBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, packStr_);
    }
    if (!getFrontUrlBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, frontUrl_);
    }
    if (!getTimeExpireBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, timeExpire_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getPrePayIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, prePayId_);
    }
    if (!getPayCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, payCode_);
    }
    if (!getPayChannelBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, payChannel_);
    }
    if (!getPayMethodBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, payMethod_);
    }
    if (!getExtendedParamsBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, extendedParams_);
    }
    if (!getTpTransactionIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, tpTransactionId_);
    }
    if (!getPrePaySignBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, prePaySign_);
    }
    if (!getPackStrBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, packStr_);
    }
    if (!getFrontUrlBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, frontUrl_);
    }
    if (!getTimeExpireBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, timeExpire_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection other = (cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection) obj;

    if (!getPrePayId()
        .equals(other.getPrePayId())) return false;
    if (!getPayCode()
        .equals(other.getPayCode())) return false;
    if (!getPayChannel()
        .equals(other.getPayChannel())) return false;
    if (!getPayMethod()
        .equals(other.getPayMethod())) return false;
    if (!getExtendedParams()
        .equals(other.getExtendedParams())) return false;
    if (!getTpTransactionId()
        .equals(other.getTpTransactionId())) return false;
    if (!getPrePaySign()
        .equals(other.getPrePaySign())) return false;
    if (!getPackStr()
        .equals(other.getPackStr())) return false;
    if (!getFrontUrl()
        .equals(other.getFrontUrl())) return false;
    if (!getTimeExpire()
        .equals(other.getTimeExpire())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + PRE_PAY_ID_FIELD_NUMBER;
    hash = (53 * hash) + getPrePayId().hashCode();
    hash = (37 * hash) + PAY_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getPayCode().hashCode();
    hash = (37 * hash) + PAY_CHANNEL_FIELD_NUMBER;
    hash = (53 * hash) + getPayChannel().hashCode();
    hash = (37 * hash) + PAY_METHOD_FIELD_NUMBER;
    hash = (53 * hash) + getPayMethod().hashCode();
    hash = (37 * hash) + EXTENDED_PARAMS_FIELD_NUMBER;
    hash = (53 * hash) + getExtendedParams().hashCode();
    hash = (37 * hash) + TP_TRANSACTION_ID_FIELD_NUMBER;
    hash = (53 * hash) + getTpTransactionId().hashCode();
    hash = (37 * hash) + PRE_PAY_SIGN_FIELD_NUMBER;
    hash = (53 * hash) + getPrePaySign().hashCode();
    hash = (37 * hash) + PACK_STR_FIELD_NUMBER;
    hash = (53 * hash) + getPackStr().hashCode();
    hash = (37 * hash) + FRONT_URL_FIELD_NUMBER;
    hash = (53 * hash) + getFrontUrl().hashCode();
    hash = (37 * hash) + TIME_EXPIRE_FIELD_NUMBER;
    hash = (53 * hash) + getTimeExpire().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 下单交易结果信息
   * </pre>
   *
   * Protobuf type {@code pbis.CreateResultSection}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:pbis.CreateResultSection)
      cn.hexcloud.pbis.common.service.facade.payment.CreateResultSectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CreateResultSection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CreateResultSection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.class, cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      prePayId_ = "";

      payCode_ = "";

      payChannel_ = "";

      payMethod_ = "";

      extendedParams_ = "";

      tpTransactionId_ = "";

      prePaySign_ = "";

      packStr_ = "";

      frontUrl_ = "";

      timeExpire_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_CreateResultSection_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection build() {
      cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection buildPartial() {
      cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection result = new cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection(this);
      result.prePayId_ = prePayId_;
      result.payCode_ = payCode_;
      result.payChannel_ = payChannel_;
      result.payMethod_ = payMethod_;
      result.extendedParams_ = extendedParams_;
      result.tpTransactionId_ = tpTransactionId_;
      result.prePaySign_ = prePaySign_;
      result.packStr_ = packStr_;
      result.frontUrl_ = frontUrl_;
      result.timeExpire_ = timeExpire_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection other) {
      if (other == cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection.getDefaultInstance()) return this;
      if (!other.getPrePayId().isEmpty()) {
        prePayId_ = other.prePayId_;
        onChanged();
      }
      if (!other.getPayCode().isEmpty()) {
        payCode_ = other.payCode_;
        onChanged();
      }
      if (!other.getPayChannel().isEmpty()) {
        payChannel_ = other.payChannel_;
        onChanged();
      }
      if (!other.getPayMethod().isEmpty()) {
        payMethod_ = other.payMethod_;
        onChanged();
      }
      if (!other.getExtendedParams().isEmpty()) {
        extendedParams_ = other.extendedParams_;
        onChanged();
      }
      if (!other.getTpTransactionId().isEmpty()) {
        tpTransactionId_ = other.tpTransactionId_;
        onChanged();
      }
      if (!other.getPrePaySign().isEmpty()) {
        prePaySign_ = other.prePaySign_;
        onChanged();
      }
      if (!other.getPackStr().isEmpty()) {
        packStr_ = other.packStr_;
        onChanged();
      }
      if (!other.getFrontUrl().isEmpty()) {
        frontUrl_ = other.frontUrl_;
        onChanged();
      }
      if (!other.getTimeExpire().isEmpty()) {
        timeExpire_ = other.timeExpire_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object prePayId_ = "";
    /**
     * <pre>
     * （必传）预支付交易号
     * </pre>
     *
     * <code>string pre_pay_id = 1;</code>
     * @return The prePayId.
     */
    public java.lang.String getPrePayId() {
      java.lang.Object ref = prePayId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        prePayId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）预支付交易号
     * </pre>
     *
     * <code>string pre_pay_id = 1;</code>
     * @return The bytes for prePayId.
     */
    public com.google.protobuf.ByteString
        getPrePayIdBytes() {
      java.lang.Object ref = prePayId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        prePayId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）预支付交易号
     * </pre>
     *
     * <code>string pre_pay_id = 1;</code>
     * @param value The prePayId to set.
     * @return This builder for chaining.
     */
    public Builder setPrePayId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      prePayId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）预支付交易号
     * </pre>
     *
     * <code>string pre_pay_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrePayId() {
      
      prePayId_ = getDefaultInstance().getPrePayId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）预支付交易号
     * </pre>
     *
     * <code>string pre_pay_id = 1;</code>
     * @param value The bytes for prePayId to set.
     * @return This builder for chaining.
     */
    public Builder setPrePayIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      prePayId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object payCode_ = "";
    /**
     * <pre>
     * （可选）付款码
     * </pre>
     *
     * <code>string pay_code = 2;</code>
     * @return The payCode.
     */
    public java.lang.String getPayCode() {
      java.lang.Object ref = payCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        payCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）付款码
     * </pre>
     *
     * <code>string pay_code = 2;</code>
     * @return The bytes for payCode.
     */
    public com.google.protobuf.ByteString
        getPayCodeBytes() {
      java.lang.Object ref = payCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）付款码
     * </pre>
     *
     * <code>string pay_code = 2;</code>
     * @param value The payCode to set.
     * @return This builder for chaining.
     */
    public Builder setPayCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      payCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）付款码
     * </pre>
     *
     * <code>string pay_code = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayCode() {
      
      payCode_ = getDefaultInstance().getPayCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）付款码
     * </pre>
     *
     * <code>string pay_code = 2;</code>
     * @param value The bytes for payCode to set.
     * @return This builder for chaining.
     */
    public Builder setPayCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      payCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object payChannel_ = "";
    /**
     * <pre>
     * （必传）支付渠道
     * </pre>
     *
     * <code>string pay_channel = 3;</code>
     * @return The payChannel.
     */
    public java.lang.String getPayChannel() {
      java.lang.Object ref = payChannel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        payChannel_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）支付渠道
     * </pre>
     *
     * <code>string pay_channel = 3;</code>
     * @return The bytes for payChannel.
     */
    public com.google.protobuf.ByteString
        getPayChannelBytes() {
      java.lang.Object ref = payChannel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payChannel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）支付渠道
     * </pre>
     *
     * <code>string pay_channel = 3;</code>
     * @param value The payChannel to set.
     * @return This builder for chaining.
     */
    public Builder setPayChannel(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      payChannel_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）支付渠道
     * </pre>
     *
     * <code>string pay_channel = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayChannel() {
      
      payChannel_ = getDefaultInstance().getPayChannel();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）支付渠道
     * </pre>
     *
     * <code>string pay_channel = 3;</code>
     * @param value The bytes for payChannel to set.
     * @return This builder for chaining.
     */
    public Builder setPayChannelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      payChannel_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object payMethod_ = "";
    /**
     * <pre>
     * （可选）用户真实支付方式
     * </pre>
     *
     * <code>string pay_method = 4;</code>
     * @return The payMethod.
     */
    public java.lang.String getPayMethod() {
      java.lang.Object ref = payMethod_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        payMethod_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）用户真实支付方式
     * </pre>
     *
     * <code>string pay_method = 4;</code>
     * @return The bytes for payMethod.
     */
    public com.google.protobuf.ByteString
        getPayMethodBytes() {
      java.lang.Object ref = payMethod_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payMethod_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）用户真实支付方式
     * </pre>
     *
     * <code>string pay_method = 4;</code>
     * @param value The payMethod to set.
     * @return This builder for chaining.
     */
    public Builder setPayMethod(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      payMethod_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）用户真实支付方式
     * </pre>
     *
     * <code>string pay_method = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayMethod() {
      
      payMethod_ = getDefaultInstance().getPayMethod();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）用户真实支付方式
     * </pre>
     *
     * <code>string pay_method = 4;</code>
     * @param value The bytes for payMethod to set.
     * @return This builder for chaining.
     */
    public Builder setPayMethodBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      payMethod_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object extendedParams_ = "";
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 5;</code>
     * @return The extendedParams.
     */
    public java.lang.String getExtendedParams() {
      java.lang.Object ref = extendedParams_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        extendedParams_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 5;</code>
     * @return The bytes for extendedParams.
     */
    public com.google.protobuf.ByteString
        getExtendedParamsBytes() {
      java.lang.Object ref = extendedParams_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extendedParams_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 5;</code>
     * @param value The extendedParams to set.
     * @return This builder for chaining.
     */
    public Builder setExtendedParams(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      extendedParams_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearExtendedParams() {
      
      extendedParams_ = getDefaultInstance().getExtendedParams();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 5;</code>
     * @param value The bytes for extendedParams to set.
     * @return This builder for chaining.
     */
    public Builder setExtendedParamsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      extendedParams_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object tpTransactionId_ = "";
    /**
     * <pre>
     * （可选）第三方流水号
     * </pre>
     *
     * <code>string tp_transaction_id = 6;</code>
     * @return The tpTransactionId.
     */
    public java.lang.String getTpTransactionId() {
      java.lang.Object ref = tpTransactionId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        tpTransactionId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）第三方流水号
     * </pre>
     *
     * <code>string tp_transaction_id = 6;</code>
     * @return The bytes for tpTransactionId.
     */
    public com.google.protobuf.ByteString
        getTpTransactionIdBytes() {
      java.lang.Object ref = tpTransactionId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tpTransactionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）第三方流水号
     * </pre>
     *
     * <code>string tp_transaction_id = 6;</code>
     * @param value The tpTransactionId to set.
     * @return This builder for chaining.
     */
    public Builder setTpTransactionId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      tpTransactionId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）第三方流水号
     * </pre>
     *
     * <code>string tp_transaction_id = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearTpTransactionId() {
      
      tpTransactionId_ = getDefaultInstance().getTpTransactionId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）第三方流水号
     * </pre>
     *
     * <code>string tp_transaction_id = 6;</code>
     * @param value The bytes for tpTransactionId to set.
     * @return This builder for chaining.
     */
    public Builder setTpTransactionIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      tpTransactionId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object prePaySign_ = "";
    /**
     * <pre>
     * （可选）预支付交易客户端唤起支付所需签名
     * </pre>
     *
     * <code>string pre_pay_sign = 7;</code>
     * @return The prePaySign.
     */
    public java.lang.String getPrePaySign() {
      java.lang.Object ref = prePaySign_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        prePaySign_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）预支付交易客户端唤起支付所需签名
     * </pre>
     *
     * <code>string pre_pay_sign = 7;</code>
     * @return The bytes for prePaySign.
     */
    public com.google.protobuf.ByteString
        getPrePaySignBytes() {
      java.lang.Object ref = prePaySign_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        prePaySign_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）预支付交易客户端唤起支付所需签名
     * </pre>
     *
     * <code>string pre_pay_sign = 7;</code>
     * @param value The prePaySign to set.
     * @return This builder for chaining.
     */
    public Builder setPrePaySign(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      prePaySign_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）预支付交易客户端唤起支付所需签名
     * </pre>
     *
     * <code>string pre_pay_sign = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrePaySign() {
      
      prePaySign_ = getDefaultInstance().getPrePaySign();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）预支付交易客户端唤起支付所需签名
     * </pre>
     *
     * <code>string pre_pay_sign = 7;</code>
     * @param value The bytes for prePaySign to set.
     * @return This builder for chaining.
     */
    public Builder setPrePaySignBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      prePaySign_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object packStr_ = "";
    /**
     * <pre>
     * （可选）预支付交易扩展字段，格式：a=b&amp;c=d
     * </pre>
     *
     * <code>string pack_str = 8;</code>
     * @return The packStr.
     */
    public java.lang.String getPackStr() {
      java.lang.Object ref = packStr_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        packStr_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）预支付交易扩展字段，格式：a=b&amp;c=d
     * </pre>
     *
     * <code>string pack_str = 8;</code>
     * @return The bytes for packStr.
     */
    public com.google.protobuf.ByteString
        getPackStrBytes() {
      java.lang.Object ref = packStr_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        packStr_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）预支付交易扩展字段，格式：a=b&amp;c=d
     * </pre>
     *
     * <code>string pack_str = 8;</code>
     * @param value The packStr to set.
     * @return This builder for chaining.
     */
    public Builder setPackStr(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      packStr_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）预支付交易扩展字段，格式：a=b&amp;c=d
     * </pre>
     *
     * <code>string pack_str = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearPackStr() {
      
      packStr_ = getDefaultInstance().getPackStr();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）预支付交易扩展字段，格式：a=b&amp;c=d
     * </pre>
     *
     * <code>string pack_str = 8;</code>
     * @param value The bytes for packStr to set.
     * @return This builder for chaining.
     */
    public Builder setPackStrBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      packStr_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object frontUrl_ = "";
    /**
     * <pre>
     * （可选）跳转地址,也可是二维码链接
     * </pre>
     *
     * <code>string front_url = 9;</code>
     * @return The frontUrl.
     */
    public java.lang.String getFrontUrl() {
      java.lang.Object ref = frontUrl_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        frontUrl_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）跳转地址,也可是二维码链接
     * </pre>
     *
     * <code>string front_url = 9;</code>
     * @return The bytes for frontUrl.
     */
    public com.google.protobuf.ByteString
        getFrontUrlBytes() {
      java.lang.Object ref = frontUrl_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        frontUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）跳转地址,也可是二维码链接
     * </pre>
     *
     * <code>string front_url = 9;</code>
     * @param value The frontUrl to set.
     * @return This builder for chaining.
     */
    public Builder setFrontUrl(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      frontUrl_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）跳转地址,也可是二维码链接
     * </pre>
     *
     * <code>string front_url = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearFrontUrl() {
      
      frontUrl_ = getDefaultInstance().getFrontUrl();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）跳转地址,也可是二维码链接
     * </pre>
     *
     * <code>string front_url = 9;</code>
     * @param value The bytes for frontUrl to set.
     * @return This builder for chaining.
     */
    public Builder setFrontUrlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      frontUrl_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object timeExpire_ = "";
    /**
     * <pre>
     * （可选）过期时间
     * </pre>
     *
     * <code>string time_expire = 10;</code>
     * @return The timeExpire.
     */
    public java.lang.String getTimeExpire() {
      java.lang.Object ref = timeExpire_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        timeExpire_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）过期时间
     * </pre>
     *
     * <code>string time_expire = 10;</code>
     * @return The bytes for timeExpire.
     */
    public com.google.protobuf.ByteString
        getTimeExpireBytes() {
      java.lang.Object ref = timeExpire_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        timeExpire_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）过期时间
     * </pre>
     *
     * <code>string time_expire = 10;</code>
     * @param value The timeExpire to set.
     * @return This builder for chaining.
     */
    public Builder setTimeExpire(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      timeExpire_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）过期时间
     * </pre>
     *
     * <code>string time_expire = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearTimeExpire() {
      
      timeExpire_ = getDefaultInstance().getTimeExpire();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）过期时间
     * </pre>
     *
     * <code>string time_expire = 10;</code>
     * @param value The bytes for timeExpire to set.
     * @return This builder for chaining.
     */
    public Builder setTimeExpireBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      timeExpire_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:pbis.CreateResultSection)
  }

  // @@protoc_insertion_point(class_scope:pbis.CreateResultSection)
  private static final cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection();
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CreateResultSection>
      PARSER = new com.google.protobuf.AbstractParser<CreateResultSection>() {
    @java.lang.Override
    public CreateResultSection parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new CreateResultSection(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<CreateResultSection> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CreateResultSection> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CreateResultSection getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

