// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

public interface CreateResultSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:pbis.CreateResultSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）预支付交易号
   * </pre>
   *
   * <code>string pre_pay_id = 1;</code>
   * @return The prePayId.
   */
  java.lang.String getPrePayId();
  /**
   * <pre>
   * （必传）预支付交易号
   * </pre>
   *
   * <code>string pre_pay_id = 1;</code>
   * @return The bytes for prePayId.
   */
  com.google.protobuf.ByteString
      getPrePayIdBytes();

  /**
   * <pre>
   * （可选）付款码
   * </pre>
   *
   * <code>string pay_code = 2;</code>
   * @return The payCode.
   */
  java.lang.String getPayCode();
  /**
   * <pre>
   * （可选）付款码
   * </pre>
   *
   * <code>string pay_code = 2;</code>
   * @return The bytes for payCode.
   */
  com.google.protobuf.ByteString
      getPayCodeBytes();

  /**
   * <pre>
   * （必传）支付渠道
   * </pre>
   *
   * <code>string pay_channel = 3;</code>
   * @return The payChannel.
   */
  java.lang.String getPayChannel();
  /**
   * <pre>
   * （必传）支付渠道
   * </pre>
   *
   * <code>string pay_channel = 3;</code>
   * @return The bytes for payChannel.
   */
  com.google.protobuf.ByteString
      getPayChannelBytes();

  /**
   * <pre>
   * （可选）用户真实支付方式
   * </pre>
   *
   * <code>string pay_method = 4;</code>
   * @return The payMethod.
   */
  java.lang.String getPayMethod();
  /**
   * <pre>
   * （可选）用户真实支付方式
   * </pre>
   *
   * <code>string pay_method = 4;</code>
   * @return The bytes for payMethod.
   */
  com.google.protobuf.ByteString
      getPayMethodBytes();

  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 5;</code>
   * @return The extendedParams.
   */
  java.lang.String getExtendedParams();
  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 5;</code>
   * @return The bytes for extendedParams.
   */
  com.google.protobuf.ByteString
      getExtendedParamsBytes();

  /**
   * <pre>
   * （可选）第三方流水号
   * </pre>
   *
   * <code>string tp_transaction_id = 6;</code>
   * @return The tpTransactionId.
   */
  java.lang.String getTpTransactionId();
  /**
   * <pre>
   * （可选）第三方流水号
   * </pre>
   *
   * <code>string tp_transaction_id = 6;</code>
   * @return The bytes for tpTransactionId.
   */
  com.google.protobuf.ByteString
      getTpTransactionIdBytes();

  /**
   * <pre>
   * （可选）预支付交易客户端唤起支付所需签名
   * </pre>
   *
   * <code>string pre_pay_sign = 7;</code>
   * @return The prePaySign.
   */
  java.lang.String getPrePaySign();
  /**
   * <pre>
   * （可选）预支付交易客户端唤起支付所需签名
   * </pre>
   *
   * <code>string pre_pay_sign = 7;</code>
   * @return The bytes for prePaySign.
   */
  com.google.protobuf.ByteString
      getPrePaySignBytes();

  /**
   * <pre>
   * （可选）预支付交易扩展字段，格式：a=b&amp;c=d
   * </pre>
   *
   * <code>string pack_str = 8;</code>
   * @return The packStr.
   */
  java.lang.String getPackStr();
  /**
   * <pre>
   * （可选）预支付交易扩展字段，格式：a=b&amp;c=d
   * </pre>
   *
   * <code>string pack_str = 8;</code>
   * @return The bytes for packStr.
   */
  com.google.protobuf.ByteString
      getPackStrBytes();

  /**
   * <pre>
   * （可选）跳转地址,也可是二维码链接
   * </pre>
   *
   * <code>string front_url = 9;</code>
   * @return The frontUrl.
   */
  java.lang.String getFrontUrl();
  /**
   * <pre>
   * （可选）跳转地址,也可是二维码链接
   * </pre>
   *
   * <code>string front_url = 9;</code>
   * @return The bytes for frontUrl.
   */
  com.google.protobuf.ByteString
      getFrontUrlBytes();

  /**
   * <pre>
   * （可选）过期时间
   * </pre>
   *
   * <code>string time_expire = 10;</code>
   * @return The timeExpire.
   */
  java.lang.String getTimeExpire();
  /**
   * <pre>
   * （可选）过期时间
   * </pre>
   *
   * <code>string time_expire = 10;</code>
   * @return The bytes for timeExpire.
   */
  com.google.protobuf.ByteString
      getTimeExpireBytes();
}
