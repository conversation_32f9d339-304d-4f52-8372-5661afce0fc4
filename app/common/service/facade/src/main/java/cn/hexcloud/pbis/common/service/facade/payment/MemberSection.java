// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

/**
 * <pre>
 * 会员信息
 * </pre>
 *
 * Protobuf type {@code pbis.MemberSection}
 */
public final class MemberSection extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:pbis.MemberSection)
    MemberSectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use MemberSection.newBuilder() to construct.
  private MemberSection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private MemberSection() {
    cardNo_ = "";
    memberNo_ = "";
    memberId_ = "";
    mobile_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new MemberSection();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private MemberSection(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            cardNo_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            memberNo_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            memberId_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            mobile_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_MemberSection_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_MemberSection_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.payment.MemberSection.class, cn.hexcloud.pbis.common.service.facade.payment.MemberSection.Builder.class);
  }

  public static final int CARD_NO_FIELD_NUMBER = 1;
  private volatile java.lang.Object cardNo_;
  /**
   * <pre>
   * （必传）卡号
   * </pre>
   *
   * <code>string card_no = 1;</code>
   * @return The cardNo.
   */
  @java.lang.Override
  public java.lang.String getCardNo() {
    java.lang.Object ref = cardNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      cardNo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）卡号
   * </pre>
   *
   * <code>string card_no = 1;</code>
   * @return The bytes for cardNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCardNoBytes() {
    java.lang.Object ref = cardNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      cardNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MEMBERNO_FIELD_NUMBER = 2;
  private volatile java.lang.Object memberNo_;
  /**
   * <pre>
   * （必传）会员号码
   * </pre>
   *
   * <code>string memberNo = 2;</code>
   * @return The memberNo.
   */
  @java.lang.Override
  public java.lang.String getMemberNo() {
    java.lang.Object ref = memberNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      memberNo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）会员号码
   * </pre>
   *
   * <code>string memberNo = 2;</code>
   * @return The bytes for memberNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMemberNoBytes() {
    java.lang.Object ref = memberNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      memberNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MEMBERID_FIELD_NUMBER = 3;
  private volatile java.lang.Object memberId_;
  /**
   * <pre>
   * （必传）会员 ID
   * </pre>
   *
   * <code>string memberId = 3;</code>
   * @return The memberId.
   */
  @java.lang.Override
  public java.lang.String getMemberId() {
    java.lang.Object ref = memberId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      memberId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）会员 ID
   * </pre>
   *
   * <code>string memberId = 3;</code>
   * @return The bytes for memberId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMemberIdBytes() {
    java.lang.Object ref = memberId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      memberId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MOBILE_FIELD_NUMBER = 4;
  private volatile java.lang.Object mobile_;
  /**
   * <pre>
   * （必传）会员手机号
   * </pre>
   *
   * <code>string mobile = 4;</code>
   * @return The mobile.
   */
  @java.lang.Override
  public java.lang.String getMobile() {
    java.lang.Object ref = mobile_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      mobile_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）会员手机号
   * </pre>
   *
   * <code>string mobile = 4;</code>
   * @return The bytes for mobile.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMobileBytes() {
    java.lang.Object ref = mobile_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      mobile_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getCardNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, cardNo_);
    }
    if (!getMemberNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, memberNo_);
    }
    if (!getMemberIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, memberId_);
    }
    if (!getMobileBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, mobile_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getCardNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, cardNo_);
    }
    if (!getMemberNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, memberNo_);
    }
    if (!getMemberIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, memberId_);
    }
    if (!getMobileBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, mobile_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.MemberSection)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.payment.MemberSection other = (cn.hexcloud.pbis.common.service.facade.payment.MemberSection) obj;

    if (!getCardNo()
        .equals(other.getCardNo())) return false;
    if (!getMemberNo()
        .equals(other.getMemberNo())) return false;
    if (!getMemberId()
        .equals(other.getMemberId())) return false;
    if (!getMobile()
        .equals(other.getMobile())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CARD_NO_FIELD_NUMBER;
    hash = (53 * hash) + getCardNo().hashCode();
    hash = (37 * hash) + MEMBERNO_FIELD_NUMBER;
    hash = (53 * hash) + getMemberNo().hashCode();
    hash = (37 * hash) + MEMBERID_FIELD_NUMBER;
    hash = (53 * hash) + getMemberId().hashCode();
    hash = (37 * hash) + MOBILE_FIELD_NUMBER;
    hash = (53 * hash) + getMobile().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.MemberSection parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.MemberSection parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.MemberSection parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.MemberSection parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.MemberSection parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.MemberSection parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.MemberSection parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.MemberSection parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.MemberSection parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.MemberSection parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.MemberSection parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.MemberSection parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.MemberSection prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 会员信息
   * </pre>
   *
   * Protobuf type {@code pbis.MemberSection}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:pbis.MemberSection)
      cn.hexcloud.pbis.common.service.facade.payment.MemberSectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_MemberSection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_MemberSection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.MemberSection.class, cn.hexcloud.pbis.common.service.facade.payment.MemberSection.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.payment.MemberSection.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      cardNo_ = "";

      memberNo_ = "";

      memberId_ = "";

      mobile_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_MemberSection_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.MemberSection getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.MemberSection.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.MemberSection build() {
      cn.hexcloud.pbis.common.service.facade.payment.MemberSection result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.MemberSection buildPartial() {
      cn.hexcloud.pbis.common.service.facade.payment.MemberSection result = new cn.hexcloud.pbis.common.service.facade.payment.MemberSection(this);
      result.cardNo_ = cardNo_;
      result.memberNo_ = memberNo_;
      result.memberId_ = memberId_;
      result.mobile_ = mobile_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.MemberSection) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.MemberSection)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.MemberSection other) {
      if (other == cn.hexcloud.pbis.common.service.facade.payment.MemberSection.getDefaultInstance()) return this;
      if (!other.getCardNo().isEmpty()) {
        cardNo_ = other.cardNo_;
        onChanged();
      }
      if (!other.getMemberNo().isEmpty()) {
        memberNo_ = other.memberNo_;
        onChanged();
      }
      if (!other.getMemberId().isEmpty()) {
        memberId_ = other.memberId_;
        onChanged();
      }
      if (!other.getMobile().isEmpty()) {
        mobile_ = other.mobile_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.payment.MemberSection parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.MemberSection) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object cardNo_ = "";
    /**
     * <pre>
     * （必传）卡号
     * </pre>
     *
     * <code>string card_no = 1;</code>
     * @return The cardNo.
     */
    public java.lang.String getCardNo() {
      java.lang.Object ref = cardNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        cardNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）卡号
     * </pre>
     *
     * <code>string card_no = 1;</code>
     * @return The bytes for cardNo.
     */
    public com.google.protobuf.ByteString
        getCardNoBytes() {
      java.lang.Object ref = cardNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cardNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）卡号
     * </pre>
     *
     * <code>string card_no = 1;</code>
     * @param value The cardNo to set.
     * @return This builder for chaining.
     */
    public Builder setCardNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      cardNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）卡号
     * </pre>
     *
     * <code>string card_no = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearCardNo() {
      
      cardNo_ = getDefaultInstance().getCardNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）卡号
     * </pre>
     *
     * <code>string card_no = 1;</code>
     * @param value The bytes for cardNo to set.
     * @return This builder for chaining.
     */
    public Builder setCardNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      cardNo_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object memberNo_ = "";
    /**
     * <pre>
     * （必传）会员号码
     * </pre>
     *
     * <code>string memberNo = 2;</code>
     * @return The memberNo.
     */
    public java.lang.String getMemberNo() {
      java.lang.Object ref = memberNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        memberNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）会员号码
     * </pre>
     *
     * <code>string memberNo = 2;</code>
     * @return The bytes for memberNo.
     */
    public com.google.protobuf.ByteString
        getMemberNoBytes() {
      java.lang.Object ref = memberNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        memberNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）会员号码
     * </pre>
     *
     * <code>string memberNo = 2;</code>
     * @param value The memberNo to set.
     * @return This builder for chaining.
     */
    public Builder setMemberNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      memberNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）会员号码
     * </pre>
     *
     * <code>string memberNo = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMemberNo() {
      
      memberNo_ = getDefaultInstance().getMemberNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）会员号码
     * </pre>
     *
     * <code>string memberNo = 2;</code>
     * @param value The bytes for memberNo to set.
     * @return This builder for chaining.
     */
    public Builder setMemberNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      memberNo_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object memberId_ = "";
    /**
     * <pre>
     * （必传）会员 ID
     * </pre>
     *
     * <code>string memberId = 3;</code>
     * @return The memberId.
     */
    public java.lang.String getMemberId() {
      java.lang.Object ref = memberId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        memberId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）会员 ID
     * </pre>
     *
     * <code>string memberId = 3;</code>
     * @return The bytes for memberId.
     */
    public com.google.protobuf.ByteString
        getMemberIdBytes() {
      java.lang.Object ref = memberId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        memberId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）会员 ID
     * </pre>
     *
     * <code>string memberId = 3;</code>
     * @param value The memberId to set.
     * @return This builder for chaining.
     */
    public Builder setMemberId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      memberId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）会员 ID
     * </pre>
     *
     * <code>string memberId = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearMemberId() {
      
      memberId_ = getDefaultInstance().getMemberId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）会员 ID
     * </pre>
     *
     * <code>string memberId = 3;</code>
     * @param value The bytes for memberId to set.
     * @return This builder for chaining.
     */
    public Builder setMemberIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      memberId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object mobile_ = "";
    /**
     * <pre>
     * （必传）会员手机号
     * </pre>
     *
     * <code>string mobile = 4;</code>
     * @return The mobile.
     */
    public java.lang.String getMobile() {
      java.lang.Object ref = mobile_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        mobile_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）会员手机号
     * </pre>
     *
     * <code>string mobile = 4;</code>
     * @return The bytes for mobile.
     */
    public com.google.protobuf.ByteString
        getMobileBytes() {
      java.lang.Object ref = mobile_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        mobile_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）会员手机号
     * </pre>
     *
     * <code>string mobile = 4;</code>
     * @param value The mobile to set.
     * @return This builder for chaining.
     */
    public Builder setMobile(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      mobile_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）会员手机号
     * </pre>
     *
     * <code>string mobile = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearMobile() {
      
      mobile_ = getDefaultInstance().getMobile();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）会员手机号
     * </pre>
     *
     * <code>string mobile = 4;</code>
     * @param value The bytes for mobile to set.
     * @return This builder for chaining.
     */
    public Builder setMobileBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      mobile_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:pbis.MemberSection)
  }

  // @@protoc_insertion_point(class_scope:pbis.MemberSection)
  private static final cn.hexcloud.pbis.common.service.facade.payment.MemberSection DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.MemberSection();
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.MemberSection getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MemberSection>
      PARSER = new com.google.protobuf.AbstractParser<MemberSection>() {
    @java.lang.Override
    public MemberSection parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new MemberSection(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<MemberSection> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MemberSection> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.MemberSection getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

