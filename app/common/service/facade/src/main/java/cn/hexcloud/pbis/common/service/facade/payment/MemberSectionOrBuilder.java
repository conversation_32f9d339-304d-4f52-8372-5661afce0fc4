// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

public interface MemberSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:pbis.MemberSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）卡号
   * </pre>
   *
   * <code>string card_no = 1;</code>
   * @return The cardNo.
   */
  java.lang.String getCardNo();
  /**
   * <pre>
   * （必传）卡号
   * </pre>
   *
   * <code>string card_no = 1;</code>
   * @return The bytes for cardNo.
   */
  com.google.protobuf.ByteString
      getCardNoBytes();

  /**
   * <pre>
   * （必传）会员号码
   * </pre>
   *
   * <code>string memberNo = 2;</code>
   * @return The memberNo.
   */
  java.lang.String getMemberNo();
  /**
   * <pre>
   * （必传）会员号码
   * </pre>
   *
   * <code>string memberNo = 2;</code>
   * @return The bytes for memberNo.
   */
  com.google.protobuf.ByteString
      getMemberNoBytes();

  /**
   * <pre>
   * （必传）会员 ID
   * </pre>
   *
   * <code>string memberId = 3;</code>
   * @return The memberId.
   */
  java.lang.String getMemberId();
  /**
   * <pre>
   * （必传）会员 ID
   * </pre>
   *
   * <code>string memberId = 3;</code>
   * @return The bytes for memberId.
   */
  com.google.protobuf.ByteString
      getMemberIdBytes();

  /**
   * <pre>
   * （必传）会员手机号
   * </pre>
   *
   * <code>string mobile = 4;</code>
   * @return The mobile.
   */
  java.lang.String getMobile();
  /**
   * <pre>
   * （必传）会员手机号
   * </pre>
   *
   * <code>string mobile = 4;</code>
   * @return The bytes for mobile.
   */
  com.google.protobuf.ByteString
      getMobileBytes();
}
