// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

/**
 * <pre>
 * 订单信息
 * </pre>
 *
 * Protobuf type {@code pbis.OrderSection}
 */
public final class OrderSection extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:pbis.OrderSection)
    OrderSectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use OrderSection.newBuilder() to construct.
  private OrderSection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private OrderSection() {
    orderNo_ = "";
    posId_ = "";
    posCode_ = "";
    tableNo_ = "";
    orderTime_ = "";
    description_ = "";
    commodities_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new OrderSection();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private OrderSection(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            orderNo_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            posId_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            posCode_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            tableNo_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            orderTime_ = s;
            break;
          }
          case 48: {

            orderAmount_ = input.readInt32();
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            description_ = s;
            break;
          }
          case 66: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              commodities_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.payment.Commodity>();
              mutable_bitField0_ |= 0x00000001;
            }
            commodities_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.payment.Commodity.parser(), extensionRegistry));
            break;
          }
          case 72: {

            discountAmount_ = input.readInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        commodities_ = java.util.Collections.unmodifiableList(commodities_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_OrderSection_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_OrderSection_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.payment.OrderSection.class, cn.hexcloud.pbis.common.service.facade.payment.OrderSection.Builder.class);
  }

  public static final int ORDER_NO_FIELD_NUMBER = 1;
  private volatile java.lang.Object orderNo_;
  /**
   * <pre>
   * （可选）交易订单ticket_id
   * </pre>
   *
   * <code>string order_no = 1;</code>
   * @return The orderNo.
   */
  @java.lang.Override
  public java.lang.String getOrderNo() {
    java.lang.Object ref = orderNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      orderNo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）交易订单ticket_id
   * </pre>
   *
   * <code>string order_no = 1;</code>
   * @return The bytes for orderNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOrderNoBytes() {
    java.lang.Object ref = orderNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      orderNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int POS_ID_FIELD_NUMBER = 2;
  private volatile java.lang.Object posId_;
  /**
   * <pre>
   * （可选）pos_id
   * </pre>
   *
   * <code>string pos_id = 2;</code>
   * @return The posId.
   */
  @java.lang.Override
  public java.lang.String getPosId() {
    java.lang.Object ref = posId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      posId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）pos_id
   * </pre>
   *
   * <code>string pos_id = 2;</code>
   * @return The bytes for posId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPosIdBytes() {
    java.lang.Object ref = posId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      posId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int POS_CODE_FIELD_NUMBER = 3;
  private volatile java.lang.Object posCode_;
  /**
   * <pre>
   * （可选）pos编码
   * </pre>
   *
   * <code>string pos_code = 3;</code>
   * @return The posCode.
   */
  @java.lang.Override
  public java.lang.String getPosCode() {
    java.lang.Object ref = posCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      posCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）pos编码
   * </pre>
   *
   * <code>string pos_code = 3;</code>
   * @return The bytes for posCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPosCodeBytes() {
    java.lang.Object ref = posCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      posCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TABLE_NO_FIELD_NUMBER = 4;
  private volatile java.lang.Object tableNo_;
  /**
   * <pre>
   * （可选）桌位号
   * </pre>
   *
   * <code>string table_no = 4;</code>
   * @return The tableNo.
   */
  @java.lang.Override
  public java.lang.String getTableNo() {
    java.lang.Object ref = tableNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      tableNo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）桌位号
   * </pre>
   *
   * <code>string table_no = 4;</code>
   * @return The bytes for tableNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTableNoBytes() {
    java.lang.Object ref = tableNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      tableNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ORDER_TIME_FIELD_NUMBER = 5;
  private volatile java.lang.Object orderTime_;
  /**
   * <pre>
   * （可选）订单生成时间 "yyyy_mm_dd_thh:mm:ss"
   * </pre>
   *
   * <code>string order_time = 5;</code>
   * @return The orderTime.
   */
  @java.lang.Override
  public java.lang.String getOrderTime() {
    java.lang.Object ref = orderTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      orderTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）订单生成时间 "yyyy_mm_dd_thh:mm:ss"
   * </pre>
   *
   * <code>string order_time = 5;</code>
   * @return The bytes for orderTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOrderTimeBytes() {
    java.lang.Object ref = orderTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      orderTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ORDER_AMOUNT_FIELD_NUMBER = 6;
  private int orderAmount_;
  /**
   * <pre>
   * （可选）订单金额（单位：分），退款时必传
   * </pre>
   *
   * <code>int32 order_amount = 6;</code>
   * @return The orderAmount.
   */
  @java.lang.Override
  public int getOrderAmount() {
    return orderAmount_;
  }

  public static final int DESCRIPTION_FIELD_NUMBER = 7;
  private volatile java.lang.Object description_;
  /**
   * <pre>
   * （可选）订单标题/描述（支付宝支付时必传）
   * </pre>
   *
   * <code>string description = 7;</code>
   * @return The description.
   */
  @java.lang.Override
  public java.lang.String getDescription() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      description_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）订单标题/描述（支付宝支付时必传）
   * </pre>
   *
   * <code>string description = 7;</code>
   * @return The bytes for description.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescriptionBytes() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      description_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int COMMODITIES_FIELD_NUMBER = 8;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.payment.Commodity> commodities_;
  /**
   * <pre>
   * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
   * </pre>
   *
   * <code>repeated .pbis.Commodity commodities = 8;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.payment.Commodity> getCommoditiesList() {
    return commodities_;
  }
  /**
   * <pre>
   * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
   * </pre>
   *
   * <code>repeated .pbis.Commodity commodities = 8;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.payment.CommodityOrBuilder> 
      getCommoditiesOrBuilderList() {
    return commodities_;
  }
  /**
   * <pre>
   * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
   * </pre>
   *
   * <code>repeated .pbis.Commodity commodities = 8;</code>
   */
  @java.lang.Override
  public int getCommoditiesCount() {
    return commodities_.size();
  }
  /**
   * <pre>
   * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
   * </pre>
   *
   * <code>repeated .pbis.Commodity commodities = 8;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.Commodity getCommodities(int index) {
    return commodities_.get(index);
  }
  /**
   * <pre>
   * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
   * </pre>
   *
   * <code>repeated .pbis.Commodity commodities = 8;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.CommodityOrBuilder getCommoditiesOrBuilder(
      int index) {
    return commodities_.get(index);
  }

  public static final int DISCOUNT_AMOUNT_FIELD_NUMBER = 9;
  private int discountAmount_;
  /**
   * <pre>
   * （可选）订单优惠金额（单位：分）
   * </pre>
   *
   * <code>int32 discount_amount = 9;</code>
   * @return The discountAmount.
   */
  @java.lang.Override
  public int getDiscountAmount() {
    return discountAmount_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getOrderNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, orderNo_);
    }
    if (!getPosIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, posId_);
    }
    if (!getPosCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, posCode_);
    }
    if (!getTableNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, tableNo_);
    }
    if (!getOrderTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, orderTime_);
    }
    if (orderAmount_ != 0) {
      output.writeInt32(6, orderAmount_);
    }
    if (!getDescriptionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, description_);
    }
    for (int i = 0; i < commodities_.size(); i++) {
      output.writeMessage(8, commodities_.get(i));
    }
    if (discountAmount_ != 0) {
      output.writeInt32(9, discountAmount_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getOrderNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, orderNo_);
    }
    if (!getPosIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, posId_);
    }
    if (!getPosCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, posCode_);
    }
    if (!getTableNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, tableNo_);
    }
    if (!getOrderTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, orderTime_);
    }
    if (orderAmount_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, orderAmount_);
    }
    if (!getDescriptionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, description_);
    }
    for (int i = 0; i < commodities_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, commodities_.get(i));
    }
    if (discountAmount_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(9, discountAmount_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.OrderSection)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.payment.OrderSection other = (cn.hexcloud.pbis.common.service.facade.payment.OrderSection) obj;

    if (!getOrderNo()
        .equals(other.getOrderNo())) return false;
    if (!getPosId()
        .equals(other.getPosId())) return false;
    if (!getPosCode()
        .equals(other.getPosCode())) return false;
    if (!getTableNo()
        .equals(other.getTableNo())) return false;
    if (!getOrderTime()
        .equals(other.getOrderTime())) return false;
    if (getOrderAmount()
        != other.getOrderAmount()) return false;
    if (!getDescription()
        .equals(other.getDescription())) return false;
    if (!getCommoditiesList()
        .equals(other.getCommoditiesList())) return false;
    if (getDiscountAmount()
        != other.getDiscountAmount()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ORDER_NO_FIELD_NUMBER;
    hash = (53 * hash) + getOrderNo().hashCode();
    hash = (37 * hash) + POS_ID_FIELD_NUMBER;
    hash = (53 * hash) + getPosId().hashCode();
    hash = (37 * hash) + POS_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getPosCode().hashCode();
    hash = (37 * hash) + TABLE_NO_FIELD_NUMBER;
    hash = (53 * hash) + getTableNo().hashCode();
    hash = (37 * hash) + ORDER_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getOrderTime().hashCode();
    hash = (37 * hash) + ORDER_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getOrderAmount();
    hash = (37 * hash) + DESCRIPTION_FIELD_NUMBER;
    hash = (53 * hash) + getDescription().hashCode();
    if (getCommoditiesCount() > 0) {
      hash = (37 * hash) + COMMODITIES_FIELD_NUMBER;
      hash = (53 * hash) + getCommoditiesList().hashCode();
    }
    hash = (37 * hash) + DISCOUNT_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getDiscountAmount();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.OrderSection parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.OrderSection parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.OrderSection parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.OrderSection parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.OrderSection parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.OrderSection parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.OrderSection parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.OrderSection parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.OrderSection parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.OrderSection parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.OrderSection parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.OrderSection parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.OrderSection prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 订单信息
   * </pre>
   *
   * Protobuf type {@code pbis.OrderSection}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:pbis.OrderSection)
      cn.hexcloud.pbis.common.service.facade.payment.OrderSectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_OrderSection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_OrderSection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.OrderSection.class, cn.hexcloud.pbis.common.service.facade.payment.OrderSection.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.payment.OrderSection.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getCommoditiesFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      orderNo_ = "";

      posId_ = "";

      posCode_ = "";

      tableNo_ = "";

      orderTime_ = "";

      orderAmount_ = 0;

      description_ = "";

      if (commoditiesBuilder_ == null) {
        commodities_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        commoditiesBuilder_.clear();
      }
      discountAmount_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_OrderSection_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.OrderSection getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.OrderSection.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.OrderSection build() {
      cn.hexcloud.pbis.common.service.facade.payment.OrderSection result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.OrderSection buildPartial() {
      cn.hexcloud.pbis.common.service.facade.payment.OrderSection result = new cn.hexcloud.pbis.common.service.facade.payment.OrderSection(this);
      int from_bitField0_ = bitField0_;
      result.orderNo_ = orderNo_;
      result.posId_ = posId_;
      result.posCode_ = posCode_;
      result.tableNo_ = tableNo_;
      result.orderTime_ = orderTime_;
      result.orderAmount_ = orderAmount_;
      result.description_ = description_;
      if (commoditiesBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          commodities_ = java.util.Collections.unmodifiableList(commodities_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.commodities_ = commodities_;
      } else {
        result.commodities_ = commoditiesBuilder_.build();
      }
      result.discountAmount_ = discountAmount_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.OrderSection) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.OrderSection)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.OrderSection other) {
      if (other == cn.hexcloud.pbis.common.service.facade.payment.OrderSection.getDefaultInstance()) return this;
      if (!other.getOrderNo().isEmpty()) {
        orderNo_ = other.orderNo_;
        onChanged();
      }
      if (!other.getPosId().isEmpty()) {
        posId_ = other.posId_;
        onChanged();
      }
      if (!other.getPosCode().isEmpty()) {
        posCode_ = other.posCode_;
        onChanged();
      }
      if (!other.getTableNo().isEmpty()) {
        tableNo_ = other.tableNo_;
        onChanged();
      }
      if (!other.getOrderTime().isEmpty()) {
        orderTime_ = other.orderTime_;
        onChanged();
      }
      if (other.getOrderAmount() != 0) {
        setOrderAmount(other.getOrderAmount());
      }
      if (!other.getDescription().isEmpty()) {
        description_ = other.description_;
        onChanged();
      }
      if (commoditiesBuilder_ == null) {
        if (!other.commodities_.isEmpty()) {
          if (commodities_.isEmpty()) {
            commodities_ = other.commodities_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureCommoditiesIsMutable();
            commodities_.addAll(other.commodities_);
          }
          onChanged();
        }
      } else {
        if (!other.commodities_.isEmpty()) {
          if (commoditiesBuilder_.isEmpty()) {
            commoditiesBuilder_.dispose();
            commoditiesBuilder_ = null;
            commodities_ = other.commodities_;
            bitField0_ = (bitField0_ & ~0x00000001);
            commoditiesBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getCommoditiesFieldBuilder() : null;
          } else {
            commoditiesBuilder_.addAllMessages(other.commodities_);
          }
        }
      }
      if (other.getDiscountAmount() != 0) {
        setDiscountAmount(other.getDiscountAmount());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.payment.OrderSection parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.OrderSection) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object orderNo_ = "";
    /**
     * <pre>
     * （可选）交易订单ticket_id
     * </pre>
     *
     * <code>string order_no = 1;</code>
     * @return The orderNo.
     */
    public java.lang.String getOrderNo() {
      java.lang.Object ref = orderNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        orderNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）交易订单ticket_id
     * </pre>
     *
     * <code>string order_no = 1;</code>
     * @return The bytes for orderNo.
     */
    public com.google.protobuf.ByteString
        getOrderNoBytes() {
      java.lang.Object ref = orderNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        orderNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）交易订单ticket_id
     * </pre>
     *
     * <code>string order_no = 1;</code>
     * @param value The orderNo to set.
     * @return This builder for chaining.
     */
    public Builder setOrderNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      orderNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）交易订单ticket_id
     * </pre>
     *
     * <code>string order_no = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearOrderNo() {
      
      orderNo_ = getDefaultInstance().getOrderNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）交易订单ticket_id
     * </pre>
     *
     * <code>string order_no = 1;</code>
     * @param value The bytes for orderNo to set.
     * @return This builder for chaining.
     */
    public Builder setOrderNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      orderNo_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object posId_ = "";
    /**
     * <pre>
     * （可选）pos_id
     * </pre>
     *
     * <code>string pos_id = 2;</code>
     * @return The posId.
     */
    public java.lang.String getPosId() {
      java.lang.Object ref = posId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        posId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）pos_id
     * </pre>
     *
     * <code>string pos_id = 2;</code>
     * @return The bytes for posId.
     */
    public com.google.protobuf.ByteString
        getPosIdBytes() {
      java.lang.Object ref = posId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        posId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）pos_id
     * </pre>
     *
     * <code>string pos_id = 2;</code>
     * @param value The posId to set.
     * @return This builder for chaining.
     */
    public Builder setPosId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      posId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）pos_id
     * </pre>
     *
     * <code>string pos_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearPosId() {
      
      posId_ = getDefaultInstance().getPosId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）pos_id
     * </pre>
     *
     * <code>string pos_id = 2;</code>
     * @param value The bytes for posId to set.
     * @return This builder for chaining.
     */
    public Builder setPosIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      posId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object posCode_ = "";
    /**
     * <pre>
     * （可选）pos编码
     * </pre>
     *
     * <code>string pos_code = 3;</code>
     * @return The posCode.
     */
    public java.lang.String getPosCode() {
      java.lang.Object ref = posCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        posCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）pos编码
     * </pre>
     *
     * <code>string pos_code = 3;</code>
     * @return The bytes for posCode.
     */
    public com.google.protobuf.ByteString
        getPosCodeBytes() {
      java.lang.Object ref = posCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        posCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）pos编码
     * </pre>
     *
     * <code>string pos_code = 3;</code>
     * @param value The posCode to set.
     * @return This builder for chaining.
     */
    public Builder setPosCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      posCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）pos编码
     * </pre>
     *
     * <code>string pos_code = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearPosCode() {
      
      posCode_ = getDefaultInstance().getPosCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）pos编码
     * </pre>
     *
     * <code>string pos_code = 3;</code>
     * @param value The bytes for posCode to set.
     * @return This builder for chaining.
     */
    public Builder setPosCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      posCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object tableNo_ = "";
    /**
     * <pre>
     * （可选）桌位号
     * </pre>
     *
     * <code>string table_no = 4;</code>
     * @return The tableNo.
     */
    public java.lang.String getTableNo() {
      java.lang.Object ref = tableNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        tableNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）桌位号
     * </pre>
     *
     * <code>string table_no = 4;</code>
     * @return The bytes for tableNo.
     */
    public com.google.protobuf.ByteString
        getTableNoBytes() {
      java.lang.Object ref = tableNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tableNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）桌位号
     * </pre>
     *
     * <code>string table_no = 4;</code>
     * @param value The tableNo to set.
     * @return This builder for chaining.
     */
    public Builder setTableNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      tableNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）桌位号
     * </pre>
     *
     * <code>string table_no = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearTableNo() {
      
      tableNo_ = getDefaultInstance().getTableNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）桌位号
     * </pre>
     *
     * <code>string table_no = 4;</code>
     * @param value The bytes for tableNo to set.
     * @return This builder for chaining.
     */
    public Builder setTableNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      tableNo_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object orderTime_ = "";
    /**
     * <pre>
     * （可选）订单生成时间 "yyyy_mm_dd_thh:mm:ss"
     * </pre>
     *
     * <code>string order_time = 5;</code>
     * @return The orderTime.
     */
    public java.lang.String getOrderTime() {
      java.lang.Object ref = orderTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        orderTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）订单生成时间 "yyyy_mm_dd_thh:mm:ss"
     * </pre>
     *
     * <code>string order_time = 5;</code>
     * @return The bytes for orderTime.
     */
    public com.google.protobuf.ByteString
        getOrderTimeBytes() {
      java.lang.Object ref = orderTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        orderTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）订单生成时间 "yyyy_mm_dd_thh:mm:ss"
     * </pre>
     *
     * <code>string order_time = 5;</code>
     * @param value The orderTime to set.
     * @return This builder for chaining.
     */
    public Builder setOrderTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      orderTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）订单生成时间 "yyyy_mm_dd_thh:mm:ss"
     * </pre>
     *
     * <code>string order_time = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearOrderTime() {
      
      orderTime_ = getDefaultInstance().getOrderTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）订单生成时间 "yyyy_mm_dd_thh:mm:ss"
     * </pre>
     *
     * <code>string order_time = 5;</code>
     * @param value The bytes for orderTime to set.
     * @return This builder for chaining.
     */
    public Builder setOrderTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      orderTime_ = value;
      onChanged();
      return this;
    }

    private int orderAmount_ ;
    /**
     * <pre>
     * （可选）订单金额（单位：分），退款时必传
     * </pre>
     *
     * <code>int32 order_amount = 6;</code>
     * @return The orderAmount.
     */
    @java.lang.Override
    public int getOrderAmount() {
      return orderAmount_;
    }
    /**
     * <pre>
     * （可选）订单金额（单位：分），退款时必传
     * </pre>
     *
     * <code>int32 order_amount = 6;</code>
     * @param value The orderAmount to set.
     * @return This builder for chaining.
     */
    public Builder setOrderAmount(int value) {
      
      orderAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）订单金额（单位：分），退款时必传
     * </pre>
     *
     * <code>int32 order_amount = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearOrderAmount() {
      
      orderAmount_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object description_ = "";
    /**
     * <pre>
     * （可选）订单标题/描述（支付宝支付时必传）
     * </pre>
     *
     * <code>string description = 7;</code>
     * @return The description.
     */
    public java.lang.String getDescription() {
      java.lang.Object ref = description_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        description_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）订单标题/描述（支付宝支付时必传）
     * </pre>
     *
     * <code>string description = 7;</code>
     * @return The bytes for description.
     */
    public com.google.protobuf.ByteString
        getDescriptionBytes() {
      java.lang.Object ref = description_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        description_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）订单标题/描述（支付宝支付时必传）
     * </pre>
     *
     * <code>string description = 7;</code>
     * @param value The description to set.
     * @return This builder for chaining.
     */
    public Builder setDescription(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      description_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）订单标题/描述（支付宝支付时必传）
     * </pre>
     *
     * <code>string description = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearDescription() {
      
      description_ = getDefaultInstance().getDescription();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）订单标题/描述（支付宝支付时必传）
     * </pre>
     *
     * <code>string description = 7;</code>
     * @param value The bytes for description to set.
     * @return This builder for chaining.
     */
    public Builder setDescriptionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      description_ = value;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.payment.Commodity> commodities_ =
      java.util.Collections.emptyList();
    private void ensureCommoditiesIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        commodities_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.payment.Commodity>(commodities_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.Commodity, cn.hexcloud.pbis.common.service.facade.payment.Commodity.Builder, cn.hexcloud.pbis.common.service.facade.payment.CommodityOrBuilder> commoditiesBuilder_;

    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.payment.Commodity> getCommoditiesList() {
      if (commoditiesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(commodities_);
      } else {
        return commoditiesBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public int getCommoditiesCount() {
      if (commoditiesBuilder_ == null) {
        return commodities_.size();
      } else {
        return commoditiesBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.Commodity getCommodities(int index) {
      if (commoditiesBuilder_ == null) {
        return commodities_.get(index);
      } else {
        return commoditiesBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public Builder setCommodities(
        int index, cn.hexcloud.pbis.common.service.facade.payment.Commodity value) {
      if (commoditiesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCommoditiesIsMutable();
        commodities_.set(index, value);
        onChanged();
      } else {
        commoditiesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public Builder setCommodities(
        int index, cn.hexcloud.pbis.common.service.facade.payment.Commodity.Builder builderForValue) {
      if (commoditiesBuilder_ == null) {
        ensureCommoditiesIsMutable();
        commodities_.set(index, builderForValue.build());
        onChanged();
      } else {
        commoditiesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public Builder addCommodities(cn.hexcloud.pbis.common.service.facade.payment.Commodity value) {
      if (commoditiesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCommoditiesIsMutable();
        commodities_.add(value);
        onChanged();
      } else {
        commoditiesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public Builder addCommodities(
        int index, cn.hexcloud.pbis.common.service.facade.payment.Commodity value) {
      if (commoditiesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCommoditiesIsMutable();
        commodities_.add(index, value);
        onChanged();
      } else {
        commoditiesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public Builder addCommodities(
        cn.hexcloud.pbis.common.service.facade.payment.Commodity.Builder builderForValue) {
      if (commoditiesBuilder_ == null) {
        ensureCommoditiesIsMutable();
        commodities_.add(builderForValue.build());
        onChanged();
      } else {
        commoditiesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public Builder addCommodities(
        int index, cn.hexcloud.pbis.common.service.facade.payment.Commodity.Builder builderForValue) {
      if (commoditiesBuilder_ == null) {
        ensureCommoditiesIsMutable();
        commodities_.add(index, builderForValue.build());
        onChanged();
      } else {
        commoditiesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public Builder addAllCommodities(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.payment.Commodity> values) {
      if (commoditiesBuilder_ == null) {
        ensureCommoditiesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, commodities_);
        onChanged();
      } else {
        commoditiesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public Builder clearCommodities() {
      if (commoditiesBuilder_ == null) {
        commodities_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        commoditiesBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public Builder removeCommodities(int index) {
      if (commoditiesBuilder_ == null) {
        ensureCommoditiesIsMutable();
        commodities_.remove(index);
        onChanged();
      } else {
        commoditiesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.Commodity.Builder getCommoditiesBuilder(
        int index) {
      return getCommoditiesFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CommodityOrBuilder getCommoditiesOrBuilder(
        int index) {
      if (commoditiesBuilder_ == null) {
        return commodities_.get(index);  } else {
        return commoditiesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.payment.CommodityOrBuilder> 
         getCommoditiesOrBuilderList() {
      if (commoditiesBuilder_ != null) {
        return commoditiesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(commodities_);
      }
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.Commodity.Builder addCommoditiesBuilder() {
      return getCommoditiesFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.payment.Commodity.getDefaultInstance());
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.Commodity.Builder addCommoditiesBuilder(
        int index) {
      return getCommoditiesFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.payment.Commodity.getDefaultInstance());
    }
    /**
     * <pre>
     * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
     * </pre>
     *
     * <code>repeated .pbis.Commodity commodities = 8;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.payment.Commodity.Builder> 
         getCommoditiesBuilderList() {
      return getCommoditiesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.Commodity, cn.hexcloud.pbis.common.service.facade.payment.Commodity.Builder, cn.hexcloud.pbis.common.service.facade.payment.CommodityOrBuilder> 
        getCommoditiesFieldBuilder() {
      if (commoditiesBuilder_ == null) {
        commoditiesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.payment.Commodity, cn.hexcloud.pbis.common.service.facade.payment.Commodity.Builder, cn.hexcloud.pbis.common.service.facade.payment.CommodityOrBuilder>(
                commodities_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        commodities_ = null;
      }
      return commoditiesBuilder_;
    }

    private int discountAmount_ ;
    /**
     * <pre>
     * （可选）订单优惠金额（单位：分）
     * </pre>
     *
     * <code>int32 discount_amount = 9;</code>
     * @return The discountAmount.
     */
    @java.lang.Override
    public int getDiscountAmount() {
      return discountAmount_;
    }
    /**
     * <pre>
     * （可选）订单优惠金额（单位：分）
     * </pre>
     *
     * <code>int32 discount_amount = 9;</code>
     * @param value The discountAmount to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountAmount(int value) {
      
      discountAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）订单优惠金额（单位：分）
     * </pre>
     *
     * <code>int32 discount_amount = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountAmount() {
      
      discountAmount_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:pbis.OrderSection)
  }

  // @@protoc_insertion_point(class_scope:pbis.OrderSection)
  private static final cn.hexcloud.pbis.common.service.facade.payment.OrderSection DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.OrderSection();
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.OrderSection getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<OrderSection>
      PARSER = new com.google.protobuf.AbstractParser<OrderSection>() {
    @java.lang.Override
    public OrderSection parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new OrderSection(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<OrderSection> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<OrderSection> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.OrderSection getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

