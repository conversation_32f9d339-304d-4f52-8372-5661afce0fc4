// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

public interface OrderSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:pbis.OrderSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （可选）交易订单ticket_id
   * </pre>
   *
   * <code>string order_no = 1;</code>
   * @return The orderNo.
   */
  java.lang.String getOrderNo();
  /**
   * <pre>
   * （可选）交易订单ticket_id
   * </pre>
   *
   * <code>string order_no = 1;</code>
   * @return The bytes for orderNo.
   */
  com.google.protobuf.ByteString
      getOrderNoBytes();

  /**
   * <pre>
   * （可选）pos_id
   * </pre>
   *
   * <code>string pos_id = 2;</code>
   * @return The posId.
   */
  java.lang.String getPosId();
  /**
   * <pre>
   * （可选）pos_id
   * </pre>
   *
   * <code>string pos_id = 2;</code>
   * @return The bytes for posId.
   */
  com.google.protobuf.ByteString
      getPosIdBytes();

  /**
   * <pre>
   * （可选）pos编码
   * </pre>
   *
   * <code>string pos_code = 3;</code>
   * @return The posCode.
   */
  java.lang.String getPosCode();
  /**
   * <pre>
   * （可选）pos编码
   * </pre>
   *
   * <code>string pos_code = 3;</code>
   * @return The bytes for posCode.
   */
  com.google.protobuf.ByteString
      getPosCodeBytes();

  /**
   * <pre>
   * （可选）桌位号
   * </pre>
   *
   * <code>string table_no = 4;</code>
   * @return The tableNo.
   */
  java.lang.String getTableNo();
  /**
   * <pre>
   * （可选）桌位号
   * </pre>
   *
   * <code>string table_no = 4;</code>
   * @return The bytes for tableNo.
   */
  com.google.protobuf.ByteString
      getTableNoBytes();

  /**
   * <pre>
   * （可选）订单生成时间 "yyyy_mm_dd_thh:mm:ss"
   * </pre>
   *
   * <code>string order_time = 5;</code>
   * @return The orderTime.
   */
  java.lang.String getOrderTime();
  /**
   * <pre>
   * （可选）订单生成时间 "yyyy_mm_dd_thh:mm:ss"
   * </pre>
   *
   * <code>string order_time = 5;</code>
   * @return The bytes for orderTime.
   */
  com.google.protobuf.ByteString
      getOrderTimeBytes();

  /**
   * <pre>
   * （可选）订单金额（单位：分），退款时必传
   * </pre>
   *
   * <code>int32 order_amount = 6;</code>
   * @return The orderAmount.
   */
  int getOrderAmount();

  /**
   * <pre>
   * （可选）订单标题/描述（支付宝支付时必传）
   * </pre>
   *
   * <code>string description = 7;</code>
   * @return The description.
   */
  java.lang.String getDescription();
  /**
   * <pre>
   * （可选）订单标题/描述（支付宝支付时必传）
   * </pre>
   *
   * <code>string description = 7;</code>
   * @return The bytes for description.
   */
  com.google.protobuf.ByteString
      getDescriptionBytes();

  /**
   * <pre>
   * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
   * </pre>
   *
   * <code>repeated .pbis.Commodity commodities = 8;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.payment.Commodity> 
      getCommoditiesList();
  /**
   * <pre>
   * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
   * </pre>
   *
   * <code>repeated .pbis.Commodity commodities = 8;</code>
   */
  cn.hexcloud.pbis.common.service.facade.payment.Commodity getCommodities(int index);
  /**
   * <pre>
   * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
   * </pre>
   *
   * <code>repeated .pbis.Commodity commodities = 8;</code>
   */
  int getCommoditiesCount();
  /**
   * <pre>
   * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
   * </pre>
   *
   * <code>repeated .pbis.Commodity commodities = 8;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.payment.CommodityOrBuilder> 
      getCommoditiesOrBuilderList();
  /**
   * <pre>
   * （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
   * </pre>
   *
   * <code>repeated .pbis.Commodity commodities = 8;</code>
   */
  cn.hexcloud.pbis.common.service.facade.payment.CommodityOrBuilder getCommoditiesOrBuilder(
      int index);

  /**
   * <pre>
   * （可选）订单优惠金额（单位：分）
   * </pre>
   *
   * <code>int32 discount_amount = 9;</code>
   * @return The discountAmount.
   */
  int getDiscountAmount();
}
