// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

/**
 * <pre>
 * 支付接口请求信息
 * </pre>
 *
 * Protobuf type {@code pbis.PayRequest}
 */
public final class PayRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:pbis.PayRequest)
    PayRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PayRequest.newBuilder() to construct.
  private PayRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PayRequest() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PayRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private PayRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            cn.hexcloud.pbis.common.service.facade.payment.PaymentSection.Builder subBuilder = null;
            if (paymentSection_ != null) {
              subBuilder = paymentSection_.toBuilder();
            }
            paymentSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.payment.PaymentSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(paymentSection_);
              paymentSection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            cn.hexcloud.pbis.common.service.facade.payment.OrderSection.Builder subBuilder = null;
            if (orderSection_ != null) {
              subBuilder = orderSection_.toBuilder();
            }
            orderSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.payment.OrderSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(orderSection_);
              orderSection_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            cn.hexcloud.pbis.common.service.facade.payment.MemberSection.Builder subBuilder = null;
            if (memberSection_ != null) {
              subBuilder = memberSection_.toBuilder();
            }
            memberSection_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.payment.MemberSection.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(memberSection_);
              memberSection_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_PayRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_PayRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.payment.PayRequest.class, cn.hexcloud.pbis.common.service.facade.payment.PayRequest.Builder.class);
  }

  public static final int PAYMENT_SECTION_FIELD_NUMBER = 1;
  private cn.hexcloud.pbis.common.service.facade.payment.PaymentSection paymentSection_;
  /**
   * <pre>
   * （必传）支付交易信息
   * </pre>
   *
   * <code>.pbis.PaymentSection payment_section = 1;</code>
   * @return Whether the paymentSection field is set.
   */
  @java.lang.Override
  public boolean hasPaymentSection() {
    return paymentSection_ != null;
  }
  /**
   * <pre>
   * （必传）支付交易信息
   * </pre>
   *
   * <code>.pbis.PaymentSection payment_section = 1;</code>
   * @return The paymentSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.PaymentSection getPaymentSection() {
    return paymentSection_ == null ? cn.hexcloud.pbis.common.service.facade.payment.PaymentSection.getDefaultInstance() : paymentSection_;
  }
  /**
   * <pre>
   * （必传）支付交易信息
   * </pre>
   *
   * <code>.pbis.PaymentSection payment_section = 1;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.PaymentSectionOrBuilder getPaymentSectionOrBuilder() {
    return getPaymentSection();
  }

  public static final int ORDER_SECTION_FIELD_NUMBER = 2;
  private cn.hexcloud.pbis.common.service.facade.payment.OrderSection orderSection_;
  /**
   * <pre>
   * （可选）订单信息
   * </pre>
   *
   * <code>.pbis.OrderSection order_section = 2;</code>
   * @return Whether the orderSection field is set.
   */
  @java.lang.Override
  public boolean hasOrderSection() {
    return orderSection_ != null;
  }
  /**
   * <pre>
   * （可选）订单信息
   * </pre>
   *
   * <code>.pbis.OrderSection order_section = 2;</code>
   * @return The orderSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.OrderSection getOrderSection() {
    return orderSection_ == null ? cn.hexcloud.pbis.common.service.facade.payment.OrderSection.getDefaultInstance() : orderSection_;
  }
  /**
   * <pre>
   * （可选）订单信息
   * </pre>
   *
   * <code>.pbis.OrderSection order_section = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.OrderSectionOrBuilder getOrderSectionOrBuilder() {
    return getOrderSection();
  }

  public static final int MEMBER_SECTION_FIELD_NUMBER = 3;
  private cn.hexcloud.pbis.common.service.facade.payment.MemberSection memberSection_;
  /**
   * <pre>
   * （可选）会员信息
   * </pre>
   *
   * <code>.pbis.MemberSection member_section = 3;</code>
   * @return Whether the memberSection field is set.
   */
  @java.lang.Override
  public boolean hasMemberSection() {
    return memberSection_ != null;
  }
  /**
   * <pre>
   * （可选）会员信息
   * </pre>
   *
   * <code>.pbis.MemberSection member_section = 3;</code>
   * @return The memberSection.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.MemberSection getMemberSection() {
    return memberSection_ == null ? cn.hexcloud.pbis.common.service.facade.payment.MemberSection.getDefaultInstance() : memberSection_;
  }
  /**
   * <pre>
   * （可选）会员信息
   * </pre>
   *
   * <code>.pbis.MemberSection member_section = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.MemberSectionOrBuilder getMemberSectionOrBuilder() {
    return getMemberSection();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (paymentSection_ != null) {
      output.writeMessage(1, getPaymentSection());
    }
    if (orderSection_ != null) {
      output.writeMessage(2, getOrderSection());
    }
    if (memberSection_ != null) {
      output.writeMessage(3, getMemberSection());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (paymentSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getPaymentSection());
    }
    if (orderSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getOrderSection());
    }
    if (memberSection_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getMemberSection());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.PayRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.payment.PayRequest other = (cn.hexcloud.pbis.common.service.facade.payment.PayRequest) obj;

    if (hasPaymentSection() != other.hasPaymentSection()) return false;
    if (hasPaymentSection()) {
      if (!getPaymentSection()
          .equals(other.getPaymentSection())) return false;
    }
    if (hasOrderSection() != other.hasOrderSection()) return false;
    if (hasOrderSection()) {
      if (!getOrderSection()
          .equals(other.getOrderSection())) return false;
    }
    if (hasMemberSection() != other.hasMemberSection()) return false;
    if (hasMemberSection()) {
      if (!getMemberSection()
          .equals(other.getMemberSection())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPaymentSection()) {
      hash = (37 * hash) + PAYMENT_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getPaymentSection().hashCode();
    }
    if (hasOrderSection()) {
      hash = (37 * hash) + ORDER_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getOrderSection().hashCode();
    }
    if (hasMemberSection()) {
      hash = (37 * hash) + MEMBER_SECTION_FIELD_NUMBER;
      hash = (53 * hash) + getMemberSection().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.PayRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.PayRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.PayRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.PayRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.PayRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.PayRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.PayRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.PayRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.PayRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.PayRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.PayRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.PayRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.PayRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 支付接口请求信息
   * </pre>
   *
   * Protobuf type {@code pbis.PayRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:pbis.PayRequest)
      cn.hexcloud.pbis.common.service.facade.payment.PayRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_PayRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_PayRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.PayRequest.class, cn.hexcloud.pbis.common.service.facade.payment.PayRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.payment.PayRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (paymentSectionBuilder_ == null) {
        paymentSection_ = null;
      } else {
        paymentSection_ = null;
        paymentSectionBuilder_ = null;
      }
      if (orderSectionBuilder_ == null) {
        orderSection_ = null;
      } else {
        orderSection_ = null;
        orderSectionBuilder_ = null;
      }
      if (memberSectionBuilder_ == null) {
        memberSection_ = null;
      } else {
        memberSection_ = null;
        memberSectionBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_PayRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.PayRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.PayRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.PayRequest build() {
      cn.hexcloud.pbis.common.service.facade.payment.PayRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.PayRequest buildPartial() {
      cn.hexcloud.pbis.common.service.facade.payment.PayRequest result = new cn.hexcloud.pbis.common.service.facade.payment.PayRequest(this);
      if (paymentSectionBuilder_ == null) {
        result.paymentSection_ = paymentSection_;
      } else {
        result.paymentSection_ = paymentSectionBuilder_.build();
      }
      if (orderSectionBuilder_ == null) {
        result.orderSection_ = orderSection_;
      } else {
        result.orderSection_ = orderSectionBuilder_.build();
      }
      if (memberSectionBuilder_ == null) {
        result.memberSection_ = memberSection_;
      } else {
        result.memberSection_ = memberSectionBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.PayRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.PayRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.PayRequest other) {
      if (other == cn.hexcloud.pbis.common.service.facade.payment.PayRequest.getDefaultInstance()) return this;
      if (other.hasPaymentSection()) {
        mergePaymentSection(other.getPaymentSection());
      }
      if (other.hasOrderSection()) {
        mergeOrderSection(other.getOrderSection());
      }
      if (other.hasMemberSection()) {
        mergeMemberSection(other.getMemberSection());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.payment.PayRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.PayRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.payment.PaymentSection paymentSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.PaymentSection, cn.hexcloud.pbis.common.service.facade.payment.PaymentSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.PaymentSectionOrBuilder> paymentSectionBuilder_;
    /**
     * <pre>
     * （必传）支付交易信息
     * </pre>
     *
     * <code>.pbis.PaymentSection payment_section = 1;</code>
     * @return Whether the paymentSection field is set.
     */
    public boolean hasPaymentSection() {
      return paymentSectionBuilder_ != null || paymentSection_ != null;
    }
    /**
     * <pre>
     * （必传）支付交易信息
     * </pre>
     *
     * <code>.pbis.PaymentSection payment_section = 1;</code>
     * @return The paymentSection.
     */
    public cn.hexcloud.pbis.common.service.facade.payment.PaymentSection getPaymentSection() {
      if (paymentSectionBuilder_ == null) {
        return paymentSection_ == null ? cn.hexcloud.pbis.common.service.facade.payment.PaymentSection.getDefaultInstance() : paymentSection_;
      } else {
        return paymentSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （必传）支付交易信息
     * </pre>
     *
     * <code>.pbis.PaymentSection payment_section = 1;</code>
     */
    public Builder setPaymentSection(cn.hexcloud.pbis.common.service.facade.payment.PaymentSection value) {
      if (paymentSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        paymentSection_ = value;
        onChanged();
      } else {
        paymentSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （必传）支付交易信息
     * </pre>
     *
     * <code>.pbis.PaymentSection payment_section = 1;</code>
     */
    public Builder setPaymentSection(
        cn.hexcloud.pbis.common.service.facade.payment.PaymentSection.Builder builderForValue) {
      if (paymentSectionBuilder_ == null) {
        paymentSection_ = builderForValue.build();
        onChanged();
      } else {
        paymentSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （必传）支付交易信息
     * </pre>
     *
     * <code>.pbis.PaymentSection payment_section = 1;</code>
     */
    public Builder mergePaymentSection(cn.hexcloud.pbis.common.service.facade.payment.PaymentSection value) {
      if (paymentSectionBuilder_ == null) {
        if (paymentSection_ != null) {
          paymentSection_ =
            cn.hexcloud.pbis.common.service.facade.payment.PaymentSection.newBuilder(paymentSection_).mergeFrom(value).buildPartial();
        } else {
          paymentSection_ = value;
        }
        onChanged();
      } else {
        paymentSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （必传）支付交易信息
     * </pre>
     *
     * <code>.pbis.PaymentSection payment_section = 1;</code>
     */
    public Builder clearPaymentSection() {
      if (paymentSectionBuilder_ == null) {
        paymentSection_ = null;
        onChanged();
      } else {
        paymentSection_ = null;
        paymentSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （必传）支付交易信息
     * </pre>
     *
     * <code>.pbis.PaymentSection payment_section = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.PaymentSection.Builder getPaymentSectionBuilder() {
      
      onChanged();
      return getPaymentSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （必传）支付交易信息
     * </pre>
     *
     * <code>.pbis.PaymentSection payment_section = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.PaymentSectionOrBuilder getPaymentSectionOrBuilder() {
      if (paymentSectionBuilder_ != null) {
        return paymentSectionBuilder_.getMessageOrBuilder();
      } else {
        return paymentSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.payment.PaymentSection.getDefaultInstance() : paymentSection_;
      }
    }
    /**
     * <pre>
     * （必传）支付交易信息
     * </pre>
     *
     * <code>.pbis.PaymentSection payment_section = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.PaymentSection, cn.hexcloud.pbis.common.service.facade.payment.PaymentSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.PaymentSectionOrBuilder> 
        getPaymentSectionFieldBuilder() {
      if (paymentSectionBuilder_ == null) {
        paymentSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.payment.PaymentSection, cn.hexcloud.pbis.common.service.facade.payment.PaymentSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.PaymentSectionOrBuilder>(
                getPaymentSection(),
                getParentForChildren(),
                isClean());
        paymentSection_ = null;
      }
      return paymentSectionBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.payment.OrderSection orderSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.OrderSection, cn.hexcloud.pbis.common.service.facade.payment.OrderSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.OrderSectionOrBuilder> orderSectionBuilder_;
    /**
     * <pre>
     * （可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     * @return Whether the orderSection field is set.
     */
    public boolean hasOrderSection() {
      return orderSectionBuilder_ != null || orderSection_ != null;
    }
    /**
     * <pre>
     * （可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     * @return The orderSection.
     */
    public cn.hexcloud.pbis.common.service.facade.payment.OrderSection getOrderSection() {
      if (orderSectionBuilder_ == null) {
        return orderSection_ == null ? cn.hexcloud.pbis.common.service.facade.payment.OrderSection.getDefaultInstance() : orderSection_;
      } else {
        return orderSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     */
    public Builder setOrderSection(cn.hexcloud.pbis.common.service.facade.payment.OrderSection value) {
      if (orderSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        orderSection_ = value;
        onChanged();
      } else {
        orderSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     */
    public Builder setOrderSection(
        cn.hexcloud.pbis.common.service.facade.payment.OrderSection.Builder builderForValue) {
      if (orderSectionBuilder_ == null) {
        orderSection_ = builderForValue.build();
        onChanged();
      } else {
        orderSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     */
    public Builder mergeOrderSection(cn.hexcloud.pbis.common.service.facade.payment.OrderSection value) {
      if (orderSectionBuilder_ == null) {
        if (orderSection_ != null) {
          orderSection_ =
            cn.hexcloud.pbis.common.service.facade.payment.OrderSection.newBuilder(orderSection_).mergeFrom(value).buildPartial();
        } else {
          orderSection_ = value;
        }
        onChanged();
      } else {
        orderSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     */
    public Builder clearOrderSection() {
      if (orderSectionBuilder_ == null) {
        orderSection_ = null;
        onChanged();
      } else {
        orderSection_ = null;
        orderSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.OrderSection.Builder getOrderSectionBuilder() {
      
      onChanged();
      return getOrderSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.OrderSectionOrBuilder getOrderSectionOrBuilder() {
      if (orderSectionBuilder_ != null) {
        return orderSectionBuilder_.getMessageOrBuilder();
      } else {
        return orderSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.payment.OrderSection.getDefaultInstance() : orderSection_;
      }
    }
    /**
     * <pre>
     * （可选）订单信息
     * </pre>
     *
     * <code>.pbis.OrderSection order_section = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.OrderSection, cn.hexcloud.pbis.common.service.facade.payment.OrderSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.OrderSectionOrBuilder> 
        getOrderSectionFieldBuilder() {
      if (orderSectionBuilder_ == null) {
        orderSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.payment.OrderSection, cn.hexcloud.pbis.common.service.facade.payment.OrderSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.OrderSectionOrBuilder>(
                getOrderSection(),
                getParentForChildren(),
                isClean());
        orderSection_ = null;
      }
      return orderSectionBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.payment.MemberSection memberSection_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.MemberSection, cn.hexcloud.pbis.common.service.facade.payment.MemberSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.MemberSectionOrBuilder> memberSectionBuilder_;
    /**
     * <pre>
     * （可选）会员信息
     * </pre>
     *
     * <code>.pbis.MemberSection member_section = 3;</code>
     * @return Whether the memberSection field is set.
     */
    public boolean hasMemberSection() {
      return memberSectionBuilder_ != null || memberSection_ != null;
    }
    /**
     * <pre>
     * （可选）会员信息
     * </pre>
     *
     * <code>.pbis.MemberSection member_section = 3;</code>
     * @return The memberSection.
     */
    public cn.hexcloud.pbis.common.service.facade.payment.MemberSection getMemberSection() {
      if (memberSectionBuilder_ == null) {
        return memberSection_ == null ? cn.hexcloud.pbis.common.service.facade.payment.MemberSection.getDefaultInstance() : memberSection_;
      } else {
        return memberSectionBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （可选）会员信息
     * </pre>
     *
     * <code>.pbis.MemberSection member_section = 3;</code>
     */
    public Builder setMemberSection(cn.hexcloud.pbis.common.service.facade.payment.MemberSection value) {
      if (memberSectionBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        memberSection_ = value;
        onChanged();
      } else {
        memberSectionBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）会员信息
     * </pre>
     *
     * <code>.pbis.MemberSection member_section = 3;</code>
     */
    public Builder setMemberSection(
        cn.hexcloud.pbis.common.service.facade.payment.MemberSection.Builder builderForValue) {
      if (memberSectionBuilder_ == null) {
        memberSection_ = builderForValue.build();
        onChanged();
      } else {
        memberSectionBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （可选）会员信息
     * </pre>
     *
     * <code>.pbis.MemberSection member_section = 3;</code>
     */
    public Builder mergeMemberSection(cn.hexcloud.pbis.common.service.facade.payment.MemberSection value) {
      if (memberSectionBuilder_ == null) {
        if (memberSection_ != null) {
          memberSection_ =
            cn.hexcloud.pbis.common.service.facade.payment.MemberSection.newBuilder(memberSection_).mergeFrom(value).buildPartial();
        } else {
          memberSection_ = value;
        }
        onChanged();
      } else {
        memberSectionBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （可选）会员信息
     * </pre>
     *
     * <code>.pbis.MemberSection member_section = 3;</code>
     */
    public Builder clearMemberSection() {
      if (memberSectionBuilder_ == null) {
        memberSection_ = null;
        onChanged();
      } else {
        memberSection_ = null;
        memberSectionBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （可选）会员信息
     * </pre>
     *
     * <code>.pbis.MemberSection member_section = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.MemberSection.Builder getMemberSectionBuilder() {
      
      onChanged();
      return getMemberSectionFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （可选）会员信息
     * </pre>
     *
     * <code>.pbis.MemberSection member_section = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.MemberSectionOrBuilder getMemberSectionOrBuilder() {
      if (memberSectionBuilder_ != null) {
        return memberSectionBuilder_.getMessageOrBuilder();
      } else {
        return memberSection_ == null ?
            cn.hexcloud.pbis.common.service.facade.payment.MemberSection.getDefaultInstance() : memberSection_;
      }
    }
    /**
     * <pre>
     * （可选）会员信息
     * </pre>
     *
     * <code>.pbis.MemberSection member_section = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.MemberSection, cn.hexcloud.pbis.common.service.facade.payment.MemberSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.MemberSectionOrBuilder> 
        getMemberSectionFieldBuilder() {
      if (memberSectionBuilder_ == null) {
        memberSectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.payment.MemberSection, cn.hexcloud.pbis.common.service.facade.payment.MemberSection.Builder, cn.hexcloud.pbis.common.service.facade.payment.MemberSectionOrBuilder>(
                getMemberSection(),
                getParentForChildren(),
                isClean());
        memberSection_ = null;
      }
      return memberSectionBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:pbis.PayRequest)
  }

  // @@protoc_insertion_point(class_scope:pbis.PayRequest)
  private static final cn.hexcloud.pbis.common.service.facade.payment.PayRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.PayRequest();
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.PayRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PayRequest>
      PARSER = new com.google.protobuf.AbstractParser<PayRequest>() {
    @java.lang.Override
    public PayRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new PayRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<PayRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PayRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.PayRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

