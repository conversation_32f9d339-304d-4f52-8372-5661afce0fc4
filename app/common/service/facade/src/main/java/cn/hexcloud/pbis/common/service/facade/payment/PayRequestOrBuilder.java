// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

public interface PayRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:pbis.PayRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）支付交易信息
   * </pre>
   *
   * <code>.pbis.PaymentSection payment_section = 1;</code>
   * @return Whether the paymentSection field is set.
   */
  boolean hasPaymentSection();
  /**
   * <pre>
   * （必传）支付交易信息
   * </pre>
   *
   * <code>.pbis.PaymentSection payment_section = 1;</code>
   * @return The paymentSection.
   */
  cn.hexcloud.pbis.common.service.facade.payment.PaymentSection getPaymentSection();
  /**
   * <pre>
   * （必传）支付交易信息
   * </pre>
   *
   * <code>.pbis.PaymentSection payment_section = 1;</code>
   */
  cn.hexcloud.pbis.common.service.facade.payment.PaymentSectionOrBuilder getPaymentSectionOrBuilder();

  /**
   * <pre>
   * （可选）订单信息
   * </pre>
   *
   * <code>.pbis.OrderSection order_section = 2;</code>
   * @return Whether the orderSection field is set.
   */
  boolean hasOrderSection();
  /**
   * <pre>
   * （可选）订单信息
   * </pre>
   *
   * <code>.pbis.OrderSection order_section = 2;</code>
   * @return The orderSection.
   */
  cn.hexcloud.pbis.common.service.facade.payment.OrderSection getOrderSection();
  /**
   * <pre>
   * （可选）订单信息
   * </pre>
   *
   * <code>.pbis.OrderSection order_section = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.payment.OrderSectionOrBuilder getOrderSectionOrBuilder();

  /**
   * <pre>
   * （可选）会员信息
   * </pre>
   *
   * <code>.pbis.MemberSection member_section = 3;</code>
   * @return Whether the memberSection field is set.
   */
  boolean hasMemberSection();
  /**
   * <pre>
   * （可选）会员信息
   * </pre>
   *
   * <code>.pbis.MemberSection member_section = 3;</code>
   * @return The memberSection.
   */
  cn.hexcloud.pbis.common.service.facade.payment.MemberSection getMemberSection();
  /**
   * <pre>
   * （可选）会员信息
   * </pre>
   *
   * <code>.pbis.MemberSection member_section = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.payment.MemberSectionOrBuilder getMemberSectionOrBuilder();
}
