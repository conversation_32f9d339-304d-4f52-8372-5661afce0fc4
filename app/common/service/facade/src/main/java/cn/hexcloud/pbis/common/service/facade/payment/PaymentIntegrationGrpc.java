package cn.hexcloud.pbis.common.service.facade.payment;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 * <pre>
 * 支付集成服务
 * </pre>
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.40.1)",
    comments = "Source: PaymentIntegration.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class PaymentIntegrationGrpc {

  private PaymentIntegrationGrpc() {}

  public static final String SERVICE_NAME = "pbis.PaymentIntegration";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.payment.CreateRequest,
      cn.hexcloud.pbis.common.service.facade.payment.CreateResponse> getCreateMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "create",
      requestType = cn.hexcloud.pbis.common.service.facade.payment.CreateRequest.class,
      responseType = cn.hexcloud.pbis.common.service.facade.payment.CreateResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.payment.CreateRequest,
      cn.hexcloud.pbis.common.service.facade.payment.CreateResponse> getCreateMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.payment.CreateRequest, cn.hexcloud.pbis.common.service.facade.payment.CreateResponse> getCreateMethod;
    if ((getCreateMethod = PaymentIntegrationGrpc.getCreateMethod) == null) {
      synchronized (PaymentIntegrationGrpc.class) {
        if ((getCreateMethod = PaymentIntegrationGrpc.getCreateMethod) == null) {
          PaymentIntegrationGrpc.getCreateMethod = getCreateMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.facade.payment.CreateRequest, cn.hexcloud.pbis.common.service.facade.payment.CreateResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "create"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.payment.CreateRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.payment.CreateResponse.getDefaultInstance()))
              .setSchemaDescriptor(new PaymentIntegrationMethodDescriptorSupplier("create"))
              .build();
        }
      }
    }
    return getCreateMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.payment.PayRequest,
      cn.hexcloud.pbis.common.service.facade.payment.PayResponse> getPayMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "pay",
      requestType = cn.hexcloud.pbis.common.service.facade.payment.PayRequest.class,
      responseType = cn.hexcloud.pbis.common.service.facade.payment.PayResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.payment.PayRequest,
      cn.hexcloud.pbis.common.service.facade.payment.PayResponse> getPayMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.payment.PayRequest, cn.hexcloud.pbis.common.service.facade.payment.PayResponse> getPayMethod;
    if ((getPayMethod = PaymentIntegrationGrpc.getPayMethod) == null) {
      synchronized (PaymentIntegrationGrpc.class) {
        if ((getPayMethod = PaymentIntegrationGrpc.getPayMethod) == null) {
          PaymentIntegrationGrpc.getPayMethod = getPayMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.facade.payment.PayRequest, cn.hexcloud.pbis.common.service.facade.payment.PayResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "pay"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.payment.PayRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.payment.PayResponse.getDefaultInstance()))
              .setSchemaDescriptor(new PaymentIntegrationMethodDescriptorSupplier("pay"))
              .build();
        }
      }
    }
    return getPayMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.payment.QueryRequest,
      cn.hexcloud.pbis.common.service.facade.payment.QueryResponse> getQueryMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "query",
      requestType = cn.hexcloud.pbis.common.service.facade.payment.QueryRequest.class,
      responseType = cn.hexcloud.pbis.common.service.facade.payment.QueryResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.payment.QueryRequest,
      cn.hexcloud.pbis.common.service.facade.payment.QueryResponse> getQueryMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.payment.QueryRequest, cn.hexcloud.pbis.common.service.facade.payment.QueryResponse> getQueryMethod;
    if ((getQueryMethod = PaymentIntegrationGrpc.getQueryMethod) == null) {
      synchronized (PaymentIntegrationGrpc.class) {
        if ((getQueryMethod = PaymentIntegrationGrpc.getQueryMethod) == null) {
          PaymentIntegrationGrpc.getQueryMethod = getQueryMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.facade.payment.QueryRequest, cn.hexcloud.pbis.common.service.facade.payment.QueryResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "query"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.payment.QueryRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.payment.QueryResponse.getDefaultInstance()))
              .setSchemaDescriptor(new PaymentIntegrationMethodDescriptorSupplier("query"))
              .build();
        }
      }
    }
    return getQueryMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.payment.CancelRequest,
      cn.hexcloud.pbis.common.service.facade.payment.CancelResponse> getCancelMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "cancel",
      requestType = cn.hexcloud.pbis.common.service.facade.payment.CancelRequest.class,
      responseType = cn.hexcloud.pbis.common.service.facade.payment.CancelResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.payment.CancelRequest,
      cn.hexcloud.pbis.common.service.facade.payment.CancelResponse> getCancelMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.payment.CancelRequest, cn.hexcloud.pbis.common.service.facade.payment.CancelResponse> getCancelMethod;
    if ((getCancelMethod = PaymentIntegrationGrpc.getCancelMethod) == null) {
      synchronized (PaymentIntegrationGrpc.class) {
        if ((getCancelMethod = PaymentIntegrationGrpc.getCancelMethod) == null) {
          PaymentIntegrationGrpc.getCancelMethod = getCancelMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.facade.payment.CancelRequest, cn.hexcloud.pbis.common.service.facade.payment.CancelResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "cancel"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.payment.CancelRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.payment.CancelResponse.getDefaultInstance()))
              .setSchemaDescriptor(new PaymentIntegrationMethodDescriptorSupplier("cancel"))
              .build();
        }
      }
    }
    return getCancelMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.payment.RefundRequest,
      cn.hexcloud.pbis.common.service.facade.payment.RefundResponse> getRefundMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "refund",
      requestType = cn.hexcloud.pbis.common.service.facade.payment.RefundRequest.class,
      responseType = cn.hexcloud.pbis.common.service.facade.payment.RefundResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.payment.RefundRequest,
      cn.hexcloud.pbis.common.service.facade.payment.RefundResponse> getRefundMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.facade.payment.RefundRequest, cn.hexcloud.pbis.common.service.facade.payment.RefundResponse> getRefundMethod;
    if ((getRefundMethod = PaymentIntegrationGrpc.getRefundMethod) == null) {
      synchronized (PaymentIntegrationGrpc.class) {
        if ((getRefundMethod = PaymentIntegrationGrpc.getRefundMethod) == null) {
          PaymentIntegrationGrpc.getRefundMethod = getRefundMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.facade.payment.RefundRequest, cn.hexcloud.pbis.common.service.facade.payment.RefundResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "refund"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.payment.RefundRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.facade.payment.RefundResponse.getDefaultInstance()))
              .setSchemaDescriptor(new PaymentIntegrationMethodDescriptorSupplier("refund"))
              .build();
        }
      }
    }
    return getRefundMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static PaymentIntegrationStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<PaymentIntegrationStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<PaymentIntegrationStub>() {
        @java.lang.Override
        public PaymentIntegrationStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new PaymentIntegrationStub(channel, callOptions);
        }
      };
    return PaymentIntegrationStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static PaymentIntegrationBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<PaymentIntegrationBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<PaymentIntegrationBlockingStub>() {
        @java.lang.Override
        public PaymentIntegrationBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new PaymentIntegrationBlockingStub(channel, callOptions);
        }
      };
    return PaymentIntegrationBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static PaymentIntegrationFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<PaymentIntegrationFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<PaymentIntegrationFutureStub>() {
        @java.lang.Override
        public PaymentIntegrationFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new PaymentIntegrationFutureStub(channel, callOptions);
        }
      };
    return PaymentIntegrationFutureStub.newStub(factory, channel);
  }

  /**
   * <pre>
   * 支付集成服务
   * </pre>
   */
  public static abstract class PaymentIntegrationImplBase implements io.grpc.BindableService {

    /**
     * <pre>
     * 下单接口
     * </pre>
     */
    public void create(cn.hexcloud.pbis.common.service.facade.payment.CreateRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.payment.CreateResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getCreateMethod(), responseObserver);
    }

    /**
     * <pre>
     * 支付接口
     * </pre>
     */
    public void pay(cn.hexcloud.pbis.common.service.facade.payment.PayRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.payment.PayResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getPayMethod(), responseObserver);
    }

    /**
     * <pre>
     * 查询接口
     * </pre>
     */
    public void query(cn.hexcloud.pbis.common.service.facade.payment.QueryRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.payment.QueryResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getQueryMethod(), responseObserver);
    }

    /**
     * <pre>
     * 撤销接口
     * </pre>
     */
    public void cancel(cn.hexcloud.pbis.common.service.facade.payment.CancelRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.payment.CancelResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getCancelMethod(), responseObserver);
    }

    /**
     * <pre>
     * 退款接口
     * </pre>
     */
    public void refund(cn.hexcloud.pbis.common.service.facade.payment.RefundRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.payment.RefundResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getRefundMethod(), responseObserver);
    }

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getCreateMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.facade.payment.CreateRequest,
                cn.hexcloud.pbis.common.service.facade.payment.CreateResponse>(
                  this, METHODID_CREATE)))
          .addMethod(
            getPayMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.facade.payment.PayRequest,
                cn.hexcloud.pbis.common.service.facade.payment.PayResponse>(
                  this, METHODID_PAY)))
          .addMethod(
            getQueryMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.facade.payment.QueryRequest,
                cn.hexcloud.pbis.common.service.facade.payment.QueryResponse>(
                  this, METHODID_QUERY)))
          .addMethod(
            getCancelMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.facade.payment.CancelRequest,
                cn.hexcloud.pbis.common.service.facade.payment.CancelResponse>(
                  this, METHODID_CANCEL)))
          .addMethod(
            getRefundMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.facade.payment.RefundRequest,
                cn.hexcloud.pbis.common.service.facade.payment.RefundResponse>(
                  this, METHODID_REFUND)))
          .build();
    }
  }

  /**
   * <pre>
   * 支付集成服务
   * </pre>
   */
  public static final class PaymentIntegrationStub extends io.grpc.stub.AbstractAsyncStub<PaymentIntegrationStub> {
    private PaymentIntegrationStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected PaymentIntegrationStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new PaymentIntegrationStub(channel, callOptions);
    }

    /**
     * <pre>
     * 下单接口
     * </pre>
     */
    public void create(cn.hexcloud.pbis.common.service.facade.payment.CreateRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.payment.CreateResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getCreateMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 支付接口
     * </pre>
     */
    public void pay(cn.hexcloud.pbis.common.service.facade.payment.PayRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.payment.PayResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getPayMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 查询接口
     * </pre>
     */
    public void query(cn.hexcloud.pbis.common.service.facade.payment.QueryRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.payment.QueryResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getQueryMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 撤销接口
     * </pre>
     */
    public void cancel(cn.hexcloud.pbis.common.service.facade.payment.CancelRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.payment.CancelResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getCancelMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 退款接口
     * </pre>
     */
    public void refund(cn.hexcloud.pbis.common.service.facade.payment.RefundRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.payment.RefundResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getRefundMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * <pre>
   * 支付集成服务
   * </pre>
   */
  public static final class PaymentIntegrationBlockingStub extends io.grpc.stub.AbstractBlockingStub<PaymentIntegrationBlockingStub> {
    private PaymentIntegrationBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected PaymentIntegrationBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new PaymentIntegrationBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * 下单接口
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CreateResponse create(cn.hexcloud.pbis.common.service.facade.payment.CreateRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getCreateMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 支付接口
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.PayResponse pay(cn.hexcloud.pbis.common.service.facade.payment.PayRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getPayMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 查询接口
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.QueryResponse query(cn.hexcloud.pbis.common.service.facade.payment.QueryRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getQueryMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 撤销接口
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.CancelResponse cancel(cn.hexcloud.pbis.common.service.facade.payment.CancelRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getCancelMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 退款接口
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.RefundResponse refund(cn.hexcloud.pbis.common.service.facade.payment.RefundRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getRefundMethod(), getCallOptions(), request);
    }
  }

  /**
   * <pre>
   * 支付集成服务
   * </pre>
   */
  public static final class PaymentIntegrationFutureStub extends io.grpc.stub.AbstractFutureStub<PaymentIntegrationFutureStub> {
    private PaymentIntegrationFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected PaymentIntegrationFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new PaymentIntegrationFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * 下单接口
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.facade.payment.CreateResponse> create(
        cn.hexcloud.pbis.common.service.facade.payment.CreateRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getCreateMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 支付接口
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.facade.payment.PayResponse> pay(
        cn.hexcloud.pbis.common.service.facade.payment.PayRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getPayMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 查询接口
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.facade.payment.QueryResponse> query(
        cn.hexcloud.pbis.common.service.facade.payment.QueryRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getQueryMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 撤销接口
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.facade.payment.CancelResponse> cancel(
        cn.hexcloud.pbis.common.service.facade.payment.CancelRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getCancelMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 退款接口
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.facade.payment.RefundResponse> refund(
        cn.hexcloud.pbis.common.service.facade.payment.RefundRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getRefundMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_CREATE = 0;
  private static final int METHODID_PAY = 1;
  private static final int METHODID_QUERY = 2;
  private static final int METHODID_CANCEL = 3;
  private static final int METHODID_REFUND = 4;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final PaymentIntegrationImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(PaymentIntegrationImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_CREATE:
          serviceImpl.create((cn.hexcloud.pbis.common.service.facade.payment.CreateRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.payment.CreateResponse>) responseObserver);
          break;
        case METHODID_PAY:
          serviceImpl.pay((cn.hexcloud.pbis.common.service.facade.payment.PayRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.payment.PayResponse>) responseObserver);
          break;
        case METHODID_QUERY:
          serviceImpl.query((cn.hexcloud.pbis.common.service.facade.payment.QueryRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.payment.QueryResponse>) responseObserver);
          break;
        case METHODID_CANCEL:
          serviceImpl.cancel((cn.hexcloud.pbis.common.service.facade.payment.CancelRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.payment.CancelResponse>) responseObserver);
          break;
        case METHODID_REFUND:
          serviceImpl.refund((cn.hexcloud.pbis.common.service.facade.payment.RefundRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.facade.payment.RefundResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class PaymentIntegrationBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    PaymentIntegrationBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("PaymentIntegration");
    }
  }

  private static final class PaymentIntegrationFileDescriptorSupplier
      extends PaymentIntegrationBaseDescriptorSupplier {
    PaymentIntegrationFileDescriptorSupplier() {}
  }

  private static final class PaymentIntegrationMethodDescriptorSupplier
      extends PaymentIntegrationBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    PaymentIntegrationMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (PaymentIntegrationGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new PaymentIntegrationFileDescriptorSupplier())
              .addMethod(getCreateMethod())
              .addMethod(getPayMethod())
              .addMethod(getQueryMethod())
              .addMethod(getCancelMethod())
              .addMethod(getRefundMethod())
              .build();
        }
      }
    }
    return result;
  }
}
