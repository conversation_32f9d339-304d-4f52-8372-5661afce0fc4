// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

public final class PaymentIntegrationOuterClass {
  private PaymentIntegrationOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_CreateRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_CreateRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_CreateResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_CreateResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_PayRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_PayRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_PayResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_PayResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_QueryRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_QueryRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_QueryResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_QueryResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_CancelRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_CancelRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_CancelResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_CancelResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_RefundRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_RefundRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_RefundResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_RefundResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_CreatePaymentSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_CreatePaymentSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_PaymentSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_PaymentSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_CancelSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_CancelSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_RefundSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_RefundSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_OrderSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_OrderSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_MemberSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_MemberSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_CreateResultSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_CreateResultSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_TransactionResultSection_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_TransactionResultSection_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_Commodity_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_Commodity_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_CommodityProperty_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_CommodityProperty_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_CommodityProperty_PropertyName_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_CommodityProperty_PropertyName_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_CommodityProperty_PropertyValue_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_CommodityProperty_PropertyValue_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pbis_Promotion_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pbis_Promotion_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\030PaymentIntegration.proto\022\004pbis\"\243\001\n\rCre" +
      "ateRequest\022:\n\026create_payment_section\030\001 \001" +
      "(\0132\032.pbis.CreatePaymentSection\022)\n\rorder_" +
      "section\030\002 \001(\0132\022.pbis.OrderSection\022+\n\016mem" +
      "ber_section\030\003 \001(\0132\023.pbis.MemberSection\"\237" +
      "\001\n\016CreateResponse\022\022\n\nerror_code\030\001 \001(\t\022\025\n" +
      "\rerror_message\030\002 \001(\t\022\017\n\007warning\030\003 \001(\010\022\027\n" +
      "\017warning_message\030\004 \001(\t\0228\n\025create_result_" +
      "section\030\005 \001(\0132\031.pbis.CreateResultSection" +
      "\"\223\001\n\nPayRequest\022-\n\017payment_section\030\001 \001(\013" +
      "2\024.pbis.PaymentSection\022)\n\rorder_section\030" +
      "\002 \001(\0132\022.pbis.OrderSection\022+\n\016member_sect" +
      "ion\030\003 \001(\0132\023.pbis.MemberSection\"\246\001\n\013PayRe" +
      "sponse\022\022\n\nerror_code\030\001 \001(\t\022\025\n\rerror_mess" +
      "age\030\002 \001(\t\022\017\n\007warning\030\003 \001(\010\022\027\n\017warning_me" +
      "ssage\030\004 \001(\t\022B\n\032transaction_result_sectio" +
      "n\030\005 \001(\0132\036.pbis.TransactionResultSection\"" +
      "\251\001\n\014QueryRequest\022\017\n\007channel\030\001 \001(\t\022\026\n\016tra" +
      "nsaction_id\030\002 \001(\t\022\020\n\010pay_code\030\003 \001(\t\022\031\n\021t" +
      "p_transaction_id\030\004 \001(\t\022\030\n\020transaction_ti" +
      "me\030\005 \001(\t\022\020\n\010order_no\030\006 \001(\t\022\027\n\017extended_p" +
      "arams\030\007 \001(\t\"\250\001\n\rQueryResponse\022\022\n\nerror_c" +
      "ode\030\001 \001(\t\022\025\n\rerror_message\030\002 \001(\t\022\017\n\007warn" +
      "ing\030\003 \001(\010\022\027\n\017warning_message\030\004 \001(\t\022B\n\032tr" +
      "ansaction_result_section\030\005 \001(\0132\036.pbis.Tr" +
      "ansactionResultSection\"g\n\rCancelRequest\022" +
      "+\n\016cancel_section\030\001 \001(\0132\023.pbis.CancelSec" +
      "tion\022)\n\rorder_section\030\002 \001(\0132\022.pbis.Order" +
      "Section\"\251\001\n\016CancelResponse\022\022\n\nerror_code" +
      "\030\001 \001(\t\022\025\n\rerror_message\030\002 \001(\t\022\017\n\007warning" +
      "\030\003 \001(\010\022\027\n\017warning_message\030\004 \001(\t\022B\n\032trans" +
      "action_result_section\030\005 \001(\0132\036.pbis.Trans" +
      "actionResultSection\"\224\001\n\rRefundRequest\022+\n" +
      "\016refund_section\030\001 \001(\0132\023.pbis.RefundSecti" +
      "on\022)\n\rorder_section\030\002 \001(\0132\022.pbis.OrderSe" +
      "ction\022+\n\016member_section\030\003 \001(\0132\023.pbis.Mem" +
      "berSection\"\251\001\n\016RefundResponse\022\022\n\nerror_c" +
      "ode\030\001 \001(\t\022\025\n\rerror_message\030\002 \001(\t\022\017\n\007warn" +
      "ing\030\003 \001(\010\022\027\n\017warning_message\030\004 \001(\t\022B\n\032tr" +
      "ansaction_result_section\030\005 \001(\0132\036.pbis.Tr" +
      "ansactionResultSection\"\270\001\n\024CreatePayment" +
      "Section\022\017\n\007channel\030\001 \001(\t\022\026\n\016transaction_" +
      "id\030\002 \001(\t\022\016\n\006amount\030\003 \001(\005\022\r\n\005payer\030\004 \001(\t\022" +
      "\023\n\013description\030\005 \001(\t\022\027\n\017extended_params\030" +
      "\006 \001(\t\022\030\n\020transaction_time\030\007 \001(\t\022\020\n\010curre" +
      "ncy\030\010 \001(\t\"\270\001\n\016PaymentSection\022\017\n\007channel\030" +
      "\001 \001(\t\022\026\n\016transaction_id\030\002 \001(\t\022\020\n\010pay_cod" +
      "e\030\003 \001(\t\022\026\n\016secret_content\030\004 \001(\t\022\016\n\006amoun" +
      "t\030\005 \001(\005\022\027\n\017extended_params\030\006 \001(\t\022\030\n\020tran" +
      "saction_time\030\007 \001(\t\022\020\n\010currency\030\010 \001(\t\"\374\001\n" +
      "\rCancelSection\022\017\n\007channel\030\001 \001(\t\022\026\n\016trans" +
      "action_id\030\002 \001(\t\022\036\n\026related_transaction_i" +
      "d\030\003 \001(\t\022!\n\031related_tp_transaction_id\030\004 \001" +
      "(\t\022\020\n\010pay_code\030\005 \001(\t\022\026\n\016secret_content\030\006" +
      " \001(\t\022\016\n\006amount\030\007 \001(\005\022\027\n\017extended_params\030" +
      "\010 \001(\t\022\030\n\020transaction_time\030\t \001(\t\022\022\n\npay_m" +
      "ethod\030\n \001(\t\"\216\002\n\rRefundSection\022\017\n\007channel" +
      "\030\001 \001(\t\022\026\n\016transaction_id\030\002 \001(\t\022\036\n\026relate" +
      "d_transaction_id\030\003 \001(\t\022!\n\031related_tp_tra" +
      "nsaction_id\030\004 \001(\t\022\020\n\010pay_code\030\005 \001(\t\022\026\n\016s" +
      "ecret_content\030\006 \001(\t\022\016\n\006amount\030\007 \001(\005\022\027\n\017e" +
      "xtended_params\030\010 \001(\t\022\030\n\020transaction_time" +
      "\030\t \001(\t\022\022\n\npay_method\030\n \001(\t\022\020\n\010currency\030\013" +
      " \001(\t\"\322\001\n\014OrderSection\022\020\n\010order_no\030\001 \001(\t\022" +
      "\016\n\006pos_id\030\002 \001(\t\022\020\n\010pos_code\030\003 \001(\t\022\020\n\010tab" +
      "le_no\030\004 \001(\t\022\022\n\norder_time\030\005 \001(\t\022\024\n\014order" +
      "_amount\030\006 \001(\005\022\023\n\013description\030\007 \001(\t\022$\n\013co" +
      "mmodities\030\010 \003(\0132\017.pbis.Commodity\022\027\n\017disc" +
      "ount_amount\030\t \001(\005\"T\n\rMemberSection\022\017\n\007ca" +
      "rd_no\030\001 \001(\t\022\020\n\010memberNo\030\002 \001(\t\022\020\n\010memberI" +
      "d\030\003 \001(\t\022\016\n\006mobile\030\004 \001(\t\"\350\001\n\023CreateResult" +
      "Section\022\022\n\npre_pay_id\030\001 \001(\t\022\020\n\010pay_code\030" +
      "\002 \001(\t\022\023\n\013pay_channel\030\003 \001(\t\022\022\n\npay_method" +
      "\030\004 \001(\t\022\027\n\017extended_params\030\005 \001(\t\022\031\n\021tp_tr" +
      "ansaction_id\030\006 \001(\t\022\024\n\014pre_pay_sign\030\007 \001(\t" +
      "\022\020\n\010pack_str\030\010 \001(\t\022\021\n\tfront_url\030\t \001(\t\022\023\n" +
      "\013time_expire\030\n \001(\t\"\217\002\n\030TransactionResult" +
      "Section\022\031\n\021transaction_state\030\001 \001(\t\022\023\n\013re" +
      "al_amount\030\002 \001(\001\022\031\n\021tp_transaction_id\030\003 \001" +
      "(\t\022\023\n\013pay_channel\030\004 \001(\t\022\022\n\npay_method\030\005 " +
      "\001(\t\022\r\n\005payer\030\006 \001(\t\022\032\n\022transaction_points" +
      "\030\007 \001(\001\022\026\n\016account_points\030\010 \001(\001\022\027\n\017extend" +
      "ed_params\030\t \001(\t\022#\n\npromotions\030\n \003(\0132\017.pb" +
      "is.Promotion\"\222\001\n\tCommodity\022\n\n\002id\030\001 \001(\t\022\014" +
      "\n\004name\030\002 \001(\t\022\014\n\004code\030\003 \001(\t\022\020\n\010quantity\030\004" +
      " \001(\001\022\r\n\005price\030\005 \001(\005\022\021\n\timage_url\030\006 \001(\t\022)" +
      "\n\010property\030\007 \003(\0132\027.pbis.CommodityPropert" +
      "y\"\345\001\n\021CommodityProperty\0222\n\004name\030\001 \001(\0132$." +
      "pbis.CommodityProperty.PropertyName\0224\n\005v" +
      "alue\030\002 \001(\0132%.pbis.CommodityProperty.Prop" +
      "ertyValue\032*\n\014PropertyName\022\014\n\004code\030\001 \001(\t\022" +
      "\014\n\004name\030\002 \001(\t\032:\n\rPropertyValue\022\014\n\004code\030\001" +
      " \001(\t\022\014\n\004name\030\002 \001(\t\022\r\n\005price\030\003 \001(\001\"\213\002\n\tPr" +
      "omotion\022\n\n\002id\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\014\n\004cod" +
      "e\030\003 \001(\t\022\020\n\010discount\030\004 \001(\005\022\034\n\024discount_on" +
      "_merchant\030\005 \001(\005\022\034\n\024discount_on_platform\030" +
      "\006 \001(\005\022\032\n\022discount_on_others\030\007 \001(\005\022\027\n\017use" +
      "r_pay_amount\030\010 \001(\005\022\033\n\023merchant_pay_amoun" +
      "t\030\t \001(\005\022\033\n\023platform_pay_amount\030\n \001(\005\022\031\n\021" +
      "others_pay_amount\030\013 \001(\0052\221\002\n\022PaymentInteg" +
      "ration\0223\n\006create\022\023.pbis.CreateRequest\032\024." +
      "pbis.CreateResponse\022*\n\003pay\022\020.pbis.PayReq" +
      "uest\032\021.pbis.PayResponse\0220\n\005query\022\022.pbis." +
      "QueryRequest\032\023.pbis.QueryResponse\0223\n\006can" +
      "cel\022\023.pbis.CancelRequest\032\024.pbis.CancelRe" +
      "sponse\0223\n\006refund\022\023.pbis.RefundRequest\032\024." +
      "pbis.RefundResponseB2\n.cn.hexcloud.pbis." +
      "common.service.facade.paymentP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_pbis_CreateRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_pbis_CreateRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_CreateRequest_descriptor,
        new java.lang.String[] { "CreatePaymentSection", "OrderSection", "MemberSection", });
    internal_static_pbis_CreateResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_pbis_CreateResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_CreateResponse_descriptor,
        new java.lang.String[] { "ErrorCode", "ErrorMessage", "Warning", "WarningMessage", "CreateResultSection", });
    internal_static_pbis_PayRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_pbis_PayRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_PayRequest_descriptor,
        new java.lang.String[] { "PaymentSection", "OrderSection", "MemberSection", });
    internal_static_pbis_PayResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_pbis_PayResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_PayResponse_descriptor,
        new java.lang.String[] { "ErrorCode", "ErrorMessage", "Warning", "WarningMessage", "TransactionResultSection", });
    internal_static_pbis_QueryRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_pbis_QueryRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_QueryRequest_descriptor,
        new java.lang.String[] { "Channel", "TransactionId", "PayCode", "TpTransactionId", "TransactionTime", "OrderNo", "ExtendedParams", });
    internal_static_pbis_QueryResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_pbis_QueryResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_QueryResponse_descriptor,
        new java.lang.String[] { "ErrorCode", "ErrorMessage", "Warning", "WarningMessage", "TransactionResultSection", });
    internal_static_pbis_CancelRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_pbis_CancelRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_CancelRequest_descriptor,
        new java.lang.String[] { "CancelSection", "OrderSection", });
    internal_static_pbis_CancelResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_pbis_CancelResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_CancelResponse_descriptor,
        new java.lang.String[] { "ErrorCode", "ErrorMessage", "Warning", "WarningMessage", "TransactionResultSection", });
    internal_static_pbis_RefundRequest_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_pbis_RefundRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_RefundRequest_descriptor,
        new java.lang.String[] { "RefundSection", "OrderSection", "MemberSection", });
    internal_static_pbis_RefundResponse_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_pbis_RefundResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_RefundResponse_descriptor,
        new java.lang.String[] { "ErrorCode", "ErrorMessage", "Warning", "WarningMessage", "TransactionResultSection", });
    internal_static_pbis_CreatePaymentSection_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_pbis_CreatePaymentSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_CreatePaymentSection_descriptor,
        new java.lang.String[] { "Channel", "TransactionId", "Amount", "Payer", "Description", "ExtendedParams", "TransactionTime", "Currency", });
    internal_static_pbis_PaymentSection_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_pbis_PaymentSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_PaymentSection_descriptor,
        new java.lang.String[] { "Channel", "TransactionId", "PayCode", "SecretContent", "Amount", "ExtendedParams", "TransactionTime", "Currency", });
    internal_static_pbis_CancelSection_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_pbis_CancelSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_CancelSection_descriptor,
        new java.lang.String[] { "Channel", "TransactionId", "RelatedTransactionId", "RelatedTpTransactionId", "PayCode", "SecretContent", "Amount", "ExtendedParams", "TransactionTime", "PayMethod", });
    internal_static_pbis_RefundSection_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_pbis_RefundSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_RefundSection_descriptor,
        new java.lang.String[] { "Channel", "TransactionId", "RelatedTransactionId", "RelatedTpTransactionId", "PayCode", "SecretContent", "Amount", "ExtendedParams", "TransactionTime", "PayMethod", "Currency", });
    internal_static_pbis_OrderSection_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_pbis_OrderSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_OrderSection_descriptor,
        new java.lang.String[] { "OrderNo", "PosId", "PosCode", "TableNo", "OrderTime", "OrderAmount", "Description", "Commodities", "DiscountAmount", });
    internal_static_pbis_MemberSection_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_pbis_MemberSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_MemberSection_descriptor,
        new java.lang.String[] { "CardNo", "MemberNo", "MemberId", "Mobile", });
    internal_static_pbis_CreateResultSection_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_pbis_CreateResultSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_CreateResultSection_descriptor,
        new java.lang.String[] { "PrePayId", "PayCode", "PayChannel", "PayMethod", "ExtendedParams", "TpTransactionId", "PrePaySign", "PackStr", "FrontUrl", "TimeExpire", });
    internal_static_pbis_TransactionResultSection_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_pbis_TransactionResultSection_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_TransactionResultSection_descriptor,
        new java.lang.String[] { "TransactionState", "RealAmount", "TpTransactionId", "PayChannel", "PayMethod", "Payer", "TransactionPoints", "AccountPoints", "ExtendedParams", "Promotions", });
    internal_static_pbis_Commodity_descriptor =
      getDescriptor().getMessageTypes().get(18);
    internal_static_pbis_Commodity_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_Commodity_descriptor,
        new java.lang.String[] { "Id", "Name", "Code", "Quantity", "Price", "ImageUrl", "Property", });
    internal_static_pbis_CommodityProperty_descriptor =
      getDescriptor().getMessageTypes().get(19);
    internal_static_pbis_CommodityProperty_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_CommodityProperty_descriptor,
        new java.lang.String[] { "Name", "Value", });
    internal_static_pbis_CommodityProperty_PropertyName_descriptor =
      internal_static_pbis_CommodityProperty_descriptor.getNestedTypes().get(0);
    internal_static_pbis_CommodityProperty_PropertyName_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_CommodityProperty_PropertyName_descriptor,
        new java.lang.String[] { "Code", "Name", });
    internal_static_pbis_CommodityProperty_PropertyValue_descriptor =
      internal_static_pbis_CommodityProperty_descriptor.getNestedTypes().get(1);
    internal_static_pbis_CommodityProperty_PropertyValue_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_CommodityProperty_PropertyValue_descriptor,
        new java.lang.String[] { "Code", "Name", "Price", });
    internal_static_pbis_Promotion_descriptor =
      getDescriptor().getMessageTypes().get(20);
    internal_static_pbis_Promotion_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pbis_Promotion_descriptor,
        new java.lang.String[] { "Id", "Name", "Code", "Discount", "DiscountOnMerchant", "DiscountOnPlatform", "DiscountOnOthers", "UserPayAmount", "MerchantPayAmount", "PlatformPayAmount", "OthersPayAmount", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
