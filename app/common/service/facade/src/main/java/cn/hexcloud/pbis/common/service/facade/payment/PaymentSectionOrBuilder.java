// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

public interface PaymentSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:pbis.PaymentSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunion(合阔聚合支付)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  java.lang.String getChannel();
  /**
   * <pre>
   * （必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunion(合阔聚合支付)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  com.google.protobuf.ByteString
      getChannelBytes();

  /**
   * <pre>
   * （必传）传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string transaction_id = 2;</code>
   * @return The transactionId.
   */
  java.lang.String getTransactionId();
  /**
   * <pre>
   * （必传）传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string transaction_id = 2;</code>
   * @return The bytes for transactionId.
   */
  com.google.protobuf.ByteString
      getTransactionIdBytes();

  /**
   * <pre>
   * （必传）付款码、支付卡号
   * </pre>
   *
   * <code>string pay_code = 3;</code>
   * @return The payCode.
   */
  java.lang.String getPayCode();
  /**
   * <pre>
   * （必传）付款码、支付卡号
   * </pre>
   *
   * <code>string pay_code = 3;</code>
   * @return The bytes for payCode.
   */
  com.google.protobuf.ByteString
      getPayCodeBytes();

  /**
   * <pre>
   * （必传）密码、辅助码、二磁道信息等,格式- password=123&amp;cvn_2=213&amp;expiration=2025/10/13（敏感信息）
   * </pre>
   *
   * <code>string secret_content = 4;</code>
   * @return The secretContent.
   */
  java.lang.String getSecretContent();
  /**
   * <pre>
   * （必传）密码、辅助码、二磁道信息等,格式- password=123&amp;cvn_2=213&amp;expiration=2025/10/13（敏感信息）
   * </pre>
   *
   * <code>string secret_content = 4;</code>
   * @return The bytes for secretContent.
   */
  com.google.protobuf.ByteString
      getSecretContentBytes();

  /**
   * <pre>
   * （必传）支付金额（单位：分）
   * </pre>
   *
   * <code>int32 amount = 5;</code>
   * @return The amount.
   */
  int getAmount();

  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 6;</code>
   * @return The extendedParams.
   */
  java.lang.String getExtendedParams();
  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 6;</code>
   * @return The bytes for extendedParams.
   */
  com.google.protobuf.ByteString
      getExtendedParamsBytes();

  /**
   * <pre>
   * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
   * </pre>
   *
   * <code>string transaction_time = 7;</code>
   * @return The transactionTime.
   */
  java.lang.String getTransactionTime();
  /**
   * <pre>
   * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
   * </pre>
   *
   * <code>string transaction_time = 7;</code>
   * @return The bytes for transactionTime.
   */
  com.google.protobuf.ByteString
      getTransactionTimeBytes();

  /**
   * <pre>
   * （可选）货币（如HKD)
   * </pre>
   *
   * <code>string currency = 8;</code>
   * @return The currency.
   */
  java.lang.String getCurrency();
  /**
   * <pre>
   * （可选）货币（如HKD)
   * </pre>
   *
   * <code>string currency = 8;</code>
   * @return The bytes for currency.
   */
  com.google.protobuf.ByteString
      getCurrencyBytes();
}
