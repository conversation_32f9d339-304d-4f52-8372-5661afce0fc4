// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

/**
 * <pre>
 * 支付优惠信息
 * </pre>
 *
 * Protobuf type {@code pbis.Promotion}
 */
public final class Promotion extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:pbis.Promotion)
    PromotionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Promotion.newBuilder() to construct.
  private Promotion(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Promotion() {
    id_ = "";
    name_ = "";
    code_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Promotion();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Promotion(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            id_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            code_ = s;
            break;
          }
          case 32: {

            discount_ = input.readInt32();
            break;
          }
          case 40: {

            discountOnMerchant_ = input.readInt32();
            break;
          }
          case 48: {

            discountOnPlatform_ = input.readInt32();
            break;
          }
          case 56: {

            discountOnOthers_ = input.readInt32();
            break;
          }
          case 64: {

            userPayAmount_ = input.readInt32();
            break;
          }
          case 72: {

            merchantPayAmount_ = input.readInt32();
            break;
          }
          case 80: {

            platformPayAmount_ = input.readInt32();
            break;
          }
          case 88: {

            othersPayAmount_ = input.readInt32();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_Promotion_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_Promotion_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.payment.Promotion.class, cn.hexcloud.pbis.common.service.facade.payment.Promotion.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  private volatile java.lang.Object id_;
  /**
   * <pre>
   * （必传）优惠id，即优惠券id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）优惠id，即优惠券id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 2;
  private volatile java.lang.Object name_;
  /**
   * <pre>
   * （必传）优惠名称，即优惠券名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）优惠名称，即优惠券名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CODE_FIELD_NUMBER = 3;
  private volatile java.lang.Object code_;
  /**
   * <pre>
   * （可选）优惠编码，即优惠券编码
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The code.
   */
  @java.lang.Override
  public java.lang.String getCode() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      code_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）优惠编码，即优惠券编码
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The bytes for code.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCodeBytes() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      code_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DISCOUNT_FIELD_NUMBER = 4;
  private int discount_;
  /**
   * <pre>
   * （必传）优惠金额，即优惠券面值金额（单位：分），例如：100
   * </pre>
   *
   * <code>int32 discount = 4;</code>
   * @return The discount.
   */
  @java.lang.Override
  public int getDiscount() {
    return discount_;
  }

  public static final int DISCOUNT_ON_MERCHANT_FIELD_NUMBER = 5;
  private int discountOnMerchant_;
  /**
   * <pre>
   * （可选）商家承担的优惠金额（单位：分），例如：90
   * </pre>
   *
   * <code>int32 discount_on_merchant = 5;</code>
   * @return The discountOnMerchant.
   */
  @java.lang.Override
  public int getDiscountOnMerchant() {
    return discountOnMerchant_;
  }

  public static final int DISCOUNT_ON_PLATFORM_FIELD_NUMBER = 6;
  private int discountOnPlatform_;
  /**
   * <pre>
   * （可选）平台承担的优惠金额（单位：分），例如：10
   * </pre>
   *
   * <code>int32 discount_on_platform = 6;</code>
   * @return The discountOnPlatform.
   */
  @java.lang.Override
  public int getDiscountOnPlatform() {
    return discountOnPlatform_;
  }

  public static final int DISCOUNT_ON_OTHERS_FIELD_NUMBER = 7;
  private int discountOnOthers_;
  /**
   * <pre>
   * （可选）其他参与方承担的优惠金额（单位：分），例如：0
   * </pre>
   *
   * <code>int32 discount_on_others = 7;</code>
   * @return The discountOnOthers.
   */
  @java.lang.Override
  public int getDiscountOnOthers() {
    return discountOnOthers_;
  }

  public static final int USER_PAY_AMOUNT_FIELD_NUMBER = 8;
  private int userPayAmount_;
  /**
   * <pre>
   * （可选）用户实际买券所花费的金额（单位：分），例如：50
   * </pre>
   *
   * <code>int32 user_pay_amount = 8;</code>
   * @return The userPayAmount.
   */
  @java.lang.Override
  public int getUserPayAmount() {
    return userPayAmount_;
  }

  public static final int MERCHANT_PAY_AMOUNT_FIELD_NUMBER = 9;
  private int merchantPayAmount_;
  /**
   * <pre>
   * （可选）商家承担的券售价折扣金额（单位：分），例如：10
   * </pre>
   *
   * <code>int32 merchant_pay_amount = 9;</code>
   * @return The merchantPayAmount.
   */
  @java.lang.Override
  public int getMerchantPayAmount() {
    return merchantPayAmount_;
  }

  public static final int PLATFORM_PAY_AMOUNT_FIELD_NUMBER = 10;
  private int platformPayAmount_;
  /**
   * <pre>
   * （可选）平台承担的券售价折扣金额（单位：分），例如：10
   * </pre>
   *
   * <code>int32 platform_pay_amount = 10;</code>
   * @return The platformPayAmount.
   */
  @java.lang.Override
  public int getPlatformPayAmount() {
    return platformPayAmount_;
  }

  public static final int OTHERS_PAY_AMOUNT_FIELD_NUMBER = 11;
  private int othersPayAmount_;
  /**
   * <pre>
   * （可选）其他第三方承担的券售价折扣金额（单位：分），例如：0
   * </pre>
   *
   * <code>int32 others_pay_amount = 11;</code>
   * @return The othersPayAmount.
   */
  @java.lang.Override
  public int getOthersPayAmount() {
    return othersPayAmount_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!getNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
    }
    if (!getCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, code_);
    }
    if (discount_ != 0) {
      output.writeInt32(4, discount_);
    }
    if (discountOnMerchant_ != 0) {
      output.writeInt32(5, discountOnMerchant_);
    }
    if (discountOnPlatform_ != 0) {
      output.writeInt32(6, discountOnPlatform_);
    }
    if (discountOnOthers_ != 0) {
      output.writeInt32(7, discountOnOthers_);
    }
    if (userPayAmount_ != 0) {
      output.writeInt32(8, userPayAmount_);
    }
    if (merchantPayAmount_ != 0) {
      output.writeInt32(9, merchantPayAmount_);
    }
    if (platformPayAmount_ != 0) {
      output.writeInt32(10, platformPayAmount_);
    }
    if (othersPayAmount_ != 0) {
      output.writeInt32(11, othersPayAmount_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!getNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
    }
    if (!getCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, code_);
    }
    if (discount_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, discount_);
    }
    if (discountOnMerchant_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, discountOnMerchant_);
    }
    if (discountOnPlatform_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, discountOnPlatform_);
    }
    if (discountOnOthers_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, discountOnOthers_);
    }
    if (userPayAmount_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(8, userPayAmount_);
    }
    if (merchantPayAmount_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(9, merchantPayAmount_);
    }
    if (platformPayAmount_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(10, platformPayAmount_);
    }
    if (othersPayAmount_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(11, othersPayAmount_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.Promotion)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.payment.Promotion other = (cn.hexcloud.pbis.common.service.facade.payment.Promotion) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (!getCode()
        .equals(other.getCode())) return false;
    if (getDiscount()
        != other.getDiscount()) return false;
    if (getDiscountOnMerchant()
        != other.getDiscountOnMerchant()) return false;
    if (getDiscountOnPlatform()
        != other.getDiscountOnPlatform()) return false;
    if (getDiscountOnOthers()
        != other.getDiscountOnOthers()) return false;
    if (getUserPayAmount()
        != other.getUserPayAmount()) return false;
    if (getMerchantPayAmount()
        != other.getMerchantPayAmount()) return false;
    if (getPlatformPayAmount()
        != other.getPlatformPayAmount()) return false;
    if (getOthersPayAmount()
        != other.getOthersPayAmount()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCode().hashCode();
    hash = (37 * hash) + DISCOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getDiscount();
    hash = (37 * hash) + DISCOUNT_ON_MERCHANT_FIELD_NUMBER;
    hash = (53 * hash) + getDiscountOnMerchant();
    hash = (37 * hash) + DISCOUNT_ON_PLATFORM_FIELD_NUMBER;
    hash = (53 * hash) + getDiscountOnPlatform();
    hash = (37 * hash) + DISCOUNT_ON_OTHERS_FIELD_NUMBER;
    hash = (53 * hash) + getDiscountOnOthers();
    hash = (37 * hash) + USER_PAY_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getUserPayAmount();
    hash = (37 * hash) + MERCHANT_PAY_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getMerchantPayAmount();
    hash = (37 * hash) + PLATFORM_PAY_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getPlatformPayAmount();
    hash = (37 * hash) + OTHERS_PAY_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getOthersPayAmount();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.Promotion parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Promotion parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Promotion parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Promotion parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Promotion parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Promotion parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Promotion parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Promotion parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Promotion parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Promotion parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Promotion parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.Promotion parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.Promotion prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 支付优惠信息
   * </pre>
   *
   * Protobuf type {@code pbis.Promotion}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:pbis.Promotion)
      cn.hexcloud.pbis.common.service.facade.payment.PromotionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_Promotion_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_Promotion_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.Promotion.class, cn.hexcloud.pbis.common.service.facade.payment.Promotion.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.payment.Promotion.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      id_ = "";

      name_ = "";

      code_ = "";

      discount_ = 0;

      discountOnMerchant_ = 0;

      discountOnPlatform_ = 0;

      discountOnOthers_ = 0;

      userPayAmount_ = 0;

      merchantPayAmount_ = 0;

      platformPayAmount_ = 0;

      othersPayAmount_ = 0;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_Promotion_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.Promotion getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.Promotion.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.Promotion build() {
      cn.hexcloud.pbis.common.service.facade.payment.Promotion result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.Promotion buildPartial() {
      cn.hexcloud.pbis.common.service.facade.payment.Promotion result = new cn.hexcloud.pbis.common.service.facade.payment.Promotion(this);
      result.id_ = id_;
      result.name_ = name_;
      result.code_ = code_;
      result.discount_ = discount_;
      result.discountOnMerchant_ = discountOnMerchant_;
      result.discountOnPlatform_ = discountOnPlatform_;
      result.discountOnOthers_ = discountOnOthers_;
      result.userPayAmount_ = userPayAmount_;
      result.merchantPayAmount_ = merchantPayAmount_;
      result.platformPayAmount_ = platformPayAmount_;
      result.othersPayAmount_ = othersPayAmount_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.Promotion) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.Promotion)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.Promotion other) {
      if (other == cn.hexcloud.pbis.common.service.facade.payment.Promotion.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (!other.getCode().isEmpty()) {
        code_ = other.code_;
        onChanged();
      }
      if (other.getDiscount() != 0) {
        setDiscount(other.getDiscount());
      }
      if (other.getDiscountOnMerchant() != 0) {
        setDiscountOnMerchant(other.getDiscountOnMerchant());
      }
      if (other.getDiscountOnPlatform() != 0) {
        setDiscountOnPlatform(other.getDiscountOnPlatform());
      }
      if (other.getDiscountOnOthers() != 0) {
        setDiscountOnOthers(other.getDiscountOnOthers());
      }
      if (other.getUserPayAmount() != 0) {
        setUserPayAmount(other.getUserPayAmount());
      }
      if (other.getMerchantPayAmount() != 0) {
        setMerchantPayAmount(other.getMerchantPayAmount());
      }
      if (other.getPlatformPayAmount() != 0) {
        setPlatformPayAmount(other.getPlatformPayAmount());
      }
      if (other.getOthersPayAmount() != 0) {
        setOthersPayAmount(other.getOthersPayAmount());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.payment.Promotion parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.Promotion) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object id_ = "";
    /**
     * <pre>
     * （必传）优惠id，即优惠券id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）优惠id，即优惠券id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）优惠id，即优惠券id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）优惠id，即优惠券id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      
      id_ = getDefaultInstance().getId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）优惠id，即优惠券id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      id_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <pre>
     * （必传）优惠名称，即优惠券名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）优惠名称，即优惠券名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）优惠名称，即优惠券名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）优惠名称，即优惠券名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）优惠名称，即优惠券名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object code_ = "";
    /**
     * <pre>
     * （可选）优惠编码，即优惠券编码
     * </pre>
     *
     * <code>string code = 3;</code>
     * @return The code.
     */
    public java.lang.String getCode() {
      java.lang.Object ref = code_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        code_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）优惠编码，即优惠券编码
     * </pre>
     *
     * <code>string code = 3;</code>
     * @return The bytes for code.
     */
    public com.google.protobuf.ByteString
        getCodeBytes() {
      java.lang.Object ref = code_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        code_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）优惠编码，即优惠券编码
     * </pre>
     *
     * <code>string code = 3;</code>
     * @param value The code to set.
     * @return This builder for chaining.
     */
    public Builder setCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      code_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）优惠编码，即优惠券编码
     * </pre>
     *
     * <code>string code = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearCode() {
      
      code_ = getDefaultInstance().getCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）优惠编码，即优惠券编码
     * </pre>
     *
     * <code>string code = 3;</code>
     * @param value The bytes for code to set.
     * @return This builder for chaining.
     */
    public Builder setCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      code_ = value;
      onChanged();
      return this;
    }

    private int discount_ ;
    /**
     * <pre>
     * （必传）优惠金额，即优惠券面值金额（单位：分），例如：100
     * </pre>
     *
     * <code>int32 discount = 4;</code>
     * @return The discount.
     */
    @java.lang.Override
    public int getDiscount() {
      return discount_;
    }
    /**
     * <pre>
     * （必传）优惠金额，即优惠券面值金额（单位：分），例如：100
     * </pre>
     *
     * <code>int32 discount = 4;</code>
     * @param value The discount to set.
     * @return This builder for chaining.
     */
    public Builder setDiscount(int value) {
      
      discount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）优惠金额，即优惠券面值金额（单位：分），例如：100
     * </pre>
     *
     * <code>int32 discount = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscount() {
      
      discount_ = 0;
      onChanged();
      return this;
    }

    private int discountOnMerchant_ ;
    /**
     * <pre>
     * （可选）商家承担的优惠金额（单位：分），例如：90
     * </pre>
     *
     * <code>int32 discount_on_merchant = 5;</code>
     * @return The discountOnMerchant.
     */
    @java.lang.Override
    public int getDiscountOnMerchant() {
      return discountOnMerchant_;
    }
    /**
     * <pre>
     * （可选）商家承担的优惠金额（单位：分），例如：90
     * </pre>
     *
     * <code>int32 discount_on_merchant = 5;</code>
     * @param value The discountOnMerchant to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountOnMerchant(int value) {
      
      discountOnMerchant_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）商家承担的优惠金额（单位：分），例如：90
     * </pre>
     *
     * <code>int32 discount_on_merchant = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountOnMerchant() {
      
      discountOnMerchant_ = 0;
      onChanged();
      return this;
    }

    private int discountOnPlatform_ ;
    /**
     * <pre>
     * （可选）平台承担的优惠金额（单位：分），例如：10
     * </pre>
     *
     * <code>int32 discount_on_platform = 6;</code>
     * @return The discountOnPlatform.
     */
    @java.lang.Override
    public int getDiscountOnPlatform() {
      return discountOnPlatform_;
    }
    /**
     * <pre>
     * （可选）平台承担的优惠金额（单位：分），例如：10
     * </pre>
     *
     * <code>int32 discount_on_platform = 6;</code>
     * @param value The discountOnPlatform to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountOnPlatform(int value) {
      
      discountOnPlatform_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）平台承担的优惠金额（单位：分），例如：10
     * </pre>
     *
     * <code>int32 discount_on_platform = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountOnPlatform() {
      
      discountOnPlatform_ = 0;
      onChanged();
      return this;
    }

    private int discountOnOthers_ ;
    /**
     * <pre>
     * （可选）其他参与方承担的优惠金额（单位：分），例如：0
     * </pre>
     *
     * <code>int32 discount_on_others = 7;</code>
     * @return The discountOnOthers.
     */
    @java.lang.Override
    public int getDiscountOnOthers() {
      return discountOnOthers_;
    }
    /**
     * <pre>
     * （可选）其他参与方承担的优惠金额（单位：分），例如：0
     * </pre>
     *
     * <code>int32 discount_on_others = 7;</code>
     * @param value The discountOnOthers to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountOnOthers(int value) {
      
      discountOnOthers_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）其他参与方承担的优惠金额（单位：分），例如：0
     * </pre>
     *
     * <code>int32 discount_on_others = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountOnOthers() {
      
      discountOnOthers_ = 0;
      onChanged();
      return this;
    }

    private int userPayAmount_ ;
    /**
     * <pre>
     * （可选）用户实际买券所花费的金额（单位：分），例如：50
     * </pre>
     *
     * <code>int32 user_pay_amount = 8;</code>
     * @return The userPayAmount.
     */
    @java.lang.Override
    public int getUserPayAmount() {
      return userPayAmount_;
    }
    /**
     * <pre>
     * （可选）用户实际买券所花费的金额（单位：分），例如：50
     * </pre>
     *
     * <code>int32 user_pay_amount = 8;</code>
     * @param value The userPayAmount to set.
     * @return This builder for chaining.
     */
    public Builder setUserPayAmount(int value) {
      
      userPayAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）用户实际买券所花费的金额（单位：分），例如：50
     * </pre>
     *
     * <code>int32 user_pay_amount = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearUserPayAmount() {
      
      userPayAmount_ = 0;
      onChanged();
      return this;
    }

    private int merchantPayAmount_ ;
    /**
     * <pre>
     * （可选）商家承担的券售价折扣金额（单位：分），例如：10
     * </pre>
     *
     * <code>int32 merchant_pay_amount = 9;</code>
     * @return The merchantPayAmount.
     */
    @java.lang.Override
    public int getMerchantPayAmount() {
      return merchantPayAmount_;
    }
    /**
     * <pre>
     * （可选）商家承担的券售价折扣金额（单位：分），例如：10
     * </pre>
     *
     * <code>int32 merchant_pay_amount = 9;</code>
     * @param value The merchantPayAmount to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantPayAmount(int value) {
      
      merchantPayAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）商家承担的券售价折扣金额（单位：分），例如：10
     * </pre>
     *
     * <code>int32 merchant_pay_amount = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearMerchantPayAmount() {
      
      merchantPayAmount_ = 0;
      onChanged();
      return this;
    }

    private int platformPayAmount_ ;
    /**
     * <pre>
     * （可选）平台承担的券售价折扣金额（单位：分），例如：10
     * </pre>
     *
     * <code>int32 platform_pay_amount = 10;</code>
     * @return The platformPayAmount.
     */
    @java.lang.Override
    public int getPlatformPayAmount() {
      return platformPayAmount_;
    }
    /**
     * <pre>
     * （可选）平台承担的券售价折扣金额（单位：分），例如：10
     * </pre>
     *
     * <code>int32 platform_pay_amount = 10;</code>
     * @param value The platformPayAmount to set.
     * @return This builder for chaining.
     */
    public Builder setPlatformPayAmount(int value) {
      
      platformPayAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）平台承担的券售价折扣金额（单位：分），例如：10
     * </pre>
     *
     * <code>int32 platform_pay_amount = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlatformPayAmount() {
      
      platformPayAmount_ = 0;
      onChanged();
      return this;
    }

    private int othersPayAmount_ ;
    /**
     * <pre>
     * （可选）其他第三方承担的券售价折扣金额（单位：分），例如：0
     * </pre>
     *
     * <code>int32 others_pay_amount = 11;</code>
     * @return The othersPayAmount.
     */
    @java.lang.Override
    public int getOthersPayAmount() {
      return othersPayAmount_;
    }
    /**
     * <pre>
     * （可选）其他第三方承担的券售价折扣金额（单位：分），例如：0
     * </pre>
     *
     * <code>int32 others_pay_amount = 11;</code>
     * @param value The othersPayAmount to set.
     * @return This builder for chaining.
     */
    public Builder setOthersPayAmount(int value) {
      
      othersPayAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）其他第三方承担的券售价折扣金额（单位：分），例如：0
     * </pre>
     *
     * <code>int32 others_pay_amount = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearOthersPayAmount() {
      
      othersPayAmount_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:pbis.Promotion)
  }

  // @@protoc_insertion_point(class_scope:pbis.Promotion)
  private static final cn.hexcloud.pbis.common.service.facade.payment.Promotion DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.Promotion();
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.Promotion getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Promotion>
      PARSER = new com.google.protobuf.AbstractParser<Promotion>() {
    @java.lang.Override
    public Promotion parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Promotion(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Promotion> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Promotion> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.Promotion getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

