// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

public interface PromotionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:pbis.Promotion)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）优惠id，即优惠券id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   * （必传）优惠id，即优惠券id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   * （必传）优惠名称，即优惠券名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   * （必传）优惠名称，即优惠券名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * （可选）优惠编码，即优惠券编码
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <pre>
   * （可选）优惠编码，即优惠券编码
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <pre>
   * （必传）优惠金额，即优惠券面值金额（单位：分），例如：100
   * </pre>
   *
   * <code>int32 discount = 4;</code>
   * @return The discount.
   */
  int getDiscount();

  /**
   * <pre>
   * （可选）商家承担的优惠金额（单位：分），例如：90
   * </pre>
   *
   * <code>int32 discount_on_merchant = 5;</code>
   * @return The discountOnMerchant.
   */
  int getDiscountOnMerchant();

  /**
   * <pre>
   * （可选）平台承担的优惠金额（单位：分），例如：10
   * </pre>
   *
   * <code>int32 discount_on_platform = 6;</code>
   * @return The discountOnPlatform.
   */
  int getDiscountOnPlatform();

  /**
   * <pre>
   * （可选）其他参与方承担的优惠金额（单位：分），例如：0
   * </pre>
   *
   * <code>int32 discount_on_others = 7;</code>
   * @return The discountOnOthers.
   */
  int getDiscountOnOthers();

  /**
   * <pre>
   * （可选）用户实际买券所花费的金额（单位：分），例如：50
   * </pre>
   *
   * <code>int32 user_pay_amount = 8;</code>
   * @return The userPayAmount.
   */
  int getUserPayAmount();

  /**
   * <pre>
   * （可选）商家承担的券售价折扣金额（单位：分），例如：10
   * </pre>
   *
   * <code>int32 merchant_pay_amount = 9;</code>
   * @return The merchantPayAmount.
   */
  int getMerchantPayAmount();

  /**
   * <pre>
   * （可选）平台承担的券售价折扣金额（单位：分），例如：10
   * </pre>
   *
   * <code>int32 platform_pay_amount = 10;</code>
   * @return The platformPayAmount.
   */
  int getPlatformPayAmount();

  /**
   * <pre>
   * （可选）其他第三方承担的券售价折扣金额（单位：分），例如：0
   * </pre>
   *
   * <code>int32 others_pay_amount = 11;</code>
   * @return The othersPayAmount.
   */
  int getOthersPayAmount();
}
