// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

/**
 * <pre>
 * 交易查询接口请求信息
 * </pre>
 *
 * Protobuf type {@code pbis.QueryRequest}
 */
public final class QueryRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:pbis.QueryRequest)
    QueryRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use QueryRequest.newBuilder() to construct.
  private QueryRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private QueryRequest() {
    channel_ = "";
    transactionId_ = "";
    payCode_ = "";
    tpTransactionId_ = "";
    transactionTime_ = "";
    orderNo_ = "";
    extendedParams_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new QueryRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private QueryRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            channel_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            transactionId_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            payCode_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            tpTransactionId_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            transactionTime_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            orderNo_ = s;
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            extendedParams_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_QueryRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_QueryRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.payment.QueryRequest.class, cn.hexcloud.pbis.common.service.facade.payment.QueryRequest.Builder.class);
  }

  public static final int CHANNEL_FIELD_NUMBER = 1;
  private volatile java.lang.Object channel_;
  /**
   * <pre>
   *（必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunite(合阔聚合支付)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  @java.lang.Override
  public java.lang.String getChannel() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channel_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *（必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunite(合阔聚合支付)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelBytes() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TRANSACTION_ID_FIELD_NUMBER = 2;
  private volatile java.lang.Object transactionId_;
  /**
   * <pre>
   *（必传）支付时传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string transaction_id = 2;</code>
   * @return The transactionId.
   */
  @java.lang.Override
  public java.lang.String getTransactionId() {
    java.lang.Object ref = transactionId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      transactionId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *（必传）支付时传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string transaction_id = 2;</code>
   * @return The bytes for transactionId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTransactionIdBytes() {
    java.lang.Object ref = transactionId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      transactionId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PAY_CODE_FIELD_NUMBER = 3;
  private volatile java.lang.Object payCode_;
  /**
   * <pre>
   *（必传）付款码、支付卡号
   * </pre>
   *
   * <code>string pay_code = 3;</code>
   * @return The payCode.
   */
  @java.lang.Override
  public java.lang.String getPayCode() {
    java.lang.Object ref = payCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      payCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *（必传）付款码、支付卡号
   * </pre>
   *
   * <code>string pay_code = 3;</code>
   * @return The bytes for payCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPayCodeBytes() {
    java.lang.Object ref = payCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      payCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TP_TRANSACTION_ID_FIELD_NUMBER = 4;
  private volatile java.lang.Object tpTransactionId_;
  /**
   * <pre>
   *（可选）第三方流水号
   * </pre>
   *
   * <code>string tp_transaction_id = 4;</code>
   * @return The tpTransactionId.
   */
  @java.lang.Override
  public java.lang.String getTpTransactionId() {
    java.lang.Object ref = tpTransactionId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      tpTransactionId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *（可选）第三方流水号
   * </pre>
   *
   * <code>string tp_transaction_id = 4;</code>
   * @return The bytes for tpTransactionId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTpTransactionIdBytes() {
    java.lang.Object ref = tpTransactionId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      tpTransactionId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TRANSACTION_TIME_FIELD_NUMBER = 5;
  private volatile java.lang.Object transactionTime_;
  /**
   * <pre>
   *（可选）交易时间 "yyyy-mm-ddThh:mm:ss"
   * </pre>
   *
   * <code>string transaction_time = 5;</code>
   * @return The transactionTime.
   */
  @java.lang.Override
  public java.lang.String getTransactionTime() {
    java.lang.Object ref = transactionTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      transactionTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *（可选）交易时间 "yyyy-mm-ddThh:mm:ss"
   * </pre>
   *
   * <code>string transaction_time = 5;</code>
   * @return The bytes for transactionTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTransactionTimeBytes() {
    java.lang.Object ref = transactionTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      transactionTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ORDER_NO_FIELD_NUMBER = 6;
  private volatile java.lang.Object orderNo_;
  /**
   * <pre>
   * order_ticket_id
   * </pre>
   *
   * <code>string order_no = 6;</code>
   * @return The orderNo.
   */
  @java.lang.Override
  public java.lang.String getOrderNo() {
    java.lang.Object ref = orderNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      orderNo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * order_ticket_id
   * </pre>
   *
   * <code>string order_no = 6;</code>
   * @return The bytes for orderNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOrderNoBytes() {
    java.lang.Object ref = orderNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      orderNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int EXTENDED_PARAMS_FIELD_NUMBER = 7;
  private volatile java.lang.Object extendedParams_;
  /**
   * <pre>
   * json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 7;</code>
   * @return The extendedParams.
   */
  @java.lang.Override
  public java.lang.String getExtendedParams() {
    java.lang.Object ref = extendedParams_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      extendedParams_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 7;</code>
   * @return The bytes for extendedParams.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getExtendedParamsBytes() {
    java.lang.Object ref = extendedParams_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      extendedParams_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getChannelBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channel_);
    }
    if (!getTransactionIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, transactionId_);
    }
    if (!getPayCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, payCode_);
    }
    if (!getTpTransactionIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, tpTransactionId_);
    }
    if (!getTransactionTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, transactionTime_);
    }
    if (!getOrderNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, orderNo_);
    }
    if (!getExtendedParamsBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, extendedParams_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getChannelBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channel_);
    }
    if (!getTransactionIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, transactionId_);
    }
    if (!getPayCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, payCode_);
    }
    if (!getTpTransactionIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, tpTransactionId_);
    }
    if (!getTransactionTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, transactionTime_);
    }
    if (!getOrderNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, orderNo_);
    }
    if (!getExtendedParamsBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, extendedParams_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.QueryRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.payment.QueryRequest other = (cn.hexcloud.pbis.common.service.facade.payment.QueryRequest) obj;

    if (!getChannel()
        .equals(other.getChannel())) return false;
    if (!getTransactionId()
        .equals(other.getTransactionId())) return false;
    if (!getPayCode()
        .equals(other.getPayCode())) return false;
    if (!getTpTransactionId()
        .equals(other.getTpTransactionId())) return false;
    if (!getTransactionTime()
        .equals(other.getTransactionTime())) return false;
    if (!getOrderNo()
        .equals(other.getOrderNo())) return false;
    if (!getExtendedParams()
        .equals(other.getExtendedParams())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
    hash = (53 * hash) + getChannel().hashCode();
    hash = (37 * hash) + TRANSACTION_ID_FIELD_NUMBER;
    hash = (53 * hash) + getTransactionId().hashCode();
    hash = (37 * hash) + PAY_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getPayCode().hashCode();
    hash = (37 * hash) + TP_TRANSACTION_ID_FIELD_NUMBER;
    hash = (53 * hash) + getTpTransactionId().hashCode();
    hash = (37 * hash) + TRANSACTION_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getTransactionTime().hashCode();
    hash = (37 * hash) + ORDER_NO_FIELD_NUMBER;
    hash = (53 * hash) + getOrderNo().hashCode();
    hash = (37 * hash) + EXTENDED_PARAMS_FIELD_NUMBER;
    hash = (53 * hash) + getExtendedParams().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.QueryRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.QueryRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.QueryRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.QueryRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.QueryRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.QueryRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.QueryRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.QueryRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.QueryRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.QueryRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.QueryRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.QueryRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.QueryRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 交易查询接口请求信息
   * </pre>
   *
   * Protobuf type {@code pbis.QueryRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:pbis.QueryRequest)
      cn.hexcloud.pbis.common.service.facade.payment.QueryRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_QueryRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_QueryRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.QueryRequest.class, cn.hexcloud.pbis.common.service.facade.payment.QueryRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.payment.QueryRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channel_ = "";

      transactionId_ = "";

      payCode_ = "";

      tpTransactionId_ = "";

      transactionTime_ = "";

      orderNo_ = "";

      extendedParams_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_QueryRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.QueryRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.QueryRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.QueryRequest build() {
      cn.hexcloud.pbis.common.service.facade.payment.QueryRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.QueryRequest buildPartial() {
      cn.hexcloud.pbis.common.service.facade.payment.QueryRequest result = new cn.hexcloud.pbis.common.service.facade.payment.QueryRequest(this);
      result.channel_ = channel_;
      result.transactionId_ = transactionId_;
      result.payCode_ = payCode_;
      result.tpTransactionId_ = tpTransactionId_;
      result.transactionTime_ = transactionTime_;
      result.orderNo_ = orderNo_;
      result.extendedParams_ = extendedParams_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.QueryRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.QueryRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.QueryRequest other) {
      if (other == cn.hexcloud.pbis.common.service.facade.payment.QueryRequest.getDefaultInstance()) return this;
      if (!other.getChannel().isEmpty()) {
        channel_ = other.channel_;
        onChanged();
      }
      if (!other.getTransactionId().isEmpty()) {
        transactionId_ = other.transactionId_;
        onChanged();
      }
      if (!other.getPayCode().isEmpty()) {
        payCode_ = other.payCode_;
        onChanged();
      }
      if (!other.getTpTransactionId().isEmpty()) {
        tpTransactionId_ = other.tpTransactionId_;
        onChanged();
      }
      if (!other.getTransactionTime().isEmpty()) {
        transactionTime_ = other.transactionTime_;
        onChanged();
      }
      if (!other.getOrderNo().isEmpty()) {
        orderNo_ = other.orderNo_;
        onChanged();
      }
      if (!other.getExtendedParams().isEmpty()) {
        extendedParams_ = other.extendedParams_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.payment.QueryRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.QueryRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object channel_ = "";
    /**
     * <pre>
     *（必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunite(合阔聚合支付)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return The channel.
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channel_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *（必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunite(合阔聚合支付)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return The bytes for channel.
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *（必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunite(合阔聚合支付)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @param value The channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannel(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channel_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunite(合阔聚合支付)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannel() {
      
      channel_ = getDefaultInstance().getChannel();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunite(合阔聚合支付)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @param value The bytes for channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channel_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object transactionId_ = "";
    /**
     * <pre>
     *（必传）支付时传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string transaction_id = 2;</code>
     * @return The transactionId.
     */
    public java.lang.String getTransactionId() {
      java.lang.Object ref = transactionId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        transactionId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *（必传）支付时传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string transaction_id = 2;</code>
     * @return The bytes for transactionId.
     */
    public com.google.protobuf.ByteString
        getTransactionIdBytes() {
      java.lang.Object ref = transactionId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *（必传）支付时传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string transaction_id = 2;</code>
     * @param value The transactionId to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      transactionId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（必传）支付时传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string transaction_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearTransactionId() {
      
      transactionId_ = getDefaultInstance().getTransactionId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（必传）支付时传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string transaction_id = 2;</code>
     * @param value The bytes for transactionId to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      transactionId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object payCode_ = "";
    /**
     * <pre>
     *（必传）付款码、支付卡号
     * </pre>
     *
     * <code>string pay_code = 3;</code>
     * @return The payCode.
     */
    public java.lang.String getPayCode() {
      java.lang.Object ref = payCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        payCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *（必传）付款码、支付卡号
     * </pre>
     *
     * <code>string pay_code = 3;</code>
     * @return The bytes for payCode.
     */
    public com.google.protobuf.ByteString
        getPayCodeBytes() {
      java.lang.Object ref = payCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *（必传）付款码、支付卡号
     * </pre>
     *
     * <code>string pay_code = 3;</code>
     * @param value The payCode to set.
     * @return This builder for chaining.
     */
    public Builder setPayCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      payCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（必传）付款码、支付卡号
     * </pre>
     *
     * <code>string pay_code = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayCode() {
      
      payCode_ = getDefaultInstance().getPayCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（必传）付款码、支付卡号
     * </pre>
     *
     * <code>string pay_code = 3;</code>
     * @param value The bytes for payCode to set.
     * @return This builder for chaining.
     */
    public Builder setPayCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      payCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object tpTransactionId_ = "";
    /**
     * <pre>
     *（可选）第三方流水号
     * </pre>
     *
     * <code>string tp_transaction_id = 4;</code>
     * @return The tpTransactionId.
     */
    public java.lang.String getTpTransactionId() {
      java.lang.Object ref = tpTransactionId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        tpTransactionId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *（可选）第三方流水号
     * </pre>
     *
     * <code>string tp_transaction_id = 4;</code>
     * @return The bytes for tpTransactionId.
     */
    public com.google.protobuf.ByteString
        getTpTransactionIdBytes() {
      java.lang.Object ref = tpTransactionId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tpTransactionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *（可选）第三方流水号
     * </pre>
     *
     * <code>string tp_transaction_id = 4;</code>
     * @param value The tpTransactionId to set.
     * @return This builder for chaining.
     */
    public Builder setTpTransactionId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      tpTransactionId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（可选）第三方流水号
     * </pre>
     *
     * <code>string tp_transaction_id = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearTpTransactionId() {
      
      tpTransactionId_ = getDefaultInstance().getTpTransactionId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（可选）第三方流水号
     * </pre>
     *
     * <code>string tp_transaction_id = 4;</code>
     * @param value The bytes for tpTransactionId to set.
     * @return This builder for chaining.
     */
    public Builder setTpTransactionIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      tpTransactionId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object transactionTime_ = "";
    /**
     * <pre>
     *（可选）交易时间 "yyyy-mm-ddThh:mm:ss"
     * </pre>
     *
     * <code>string transaction_time = 5;</code>
     * @return The transactionTime.
     */
    public java.lang.String getTransactionTime() {
      java.lang.Object ref = transactionTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        transactionTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *（可选）交易时间 "yyyy-mm-ddThh:mm:ss"
     * </pre>
     *
     * <code>string transaction_time = 5;</code>
     * @return The bytes for transactionTime.
     */
    public com.google.protobuf.ByteString
        getTransactionTimeBytes() {
      java.lang.Object ref = transactionTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *（可选）交易时间 "yyyy-mm-ddThh:mm:ss"
     * </pre>
     *
     * <code>string transaction_time = 5;</code>
     * @param value The transactionTime to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      transactionTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（可选）交易时间 "yyyy-mm-ddThh:mm:ss"
     * </pre>
     *
     * <code>string transaction_time = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearTransactionTime() {
      
      transactionTime_ = getDefaultInstance().getTransactionTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *（可选）交易时间 "yyyy-mm-ddThh:mm:ss"
     * </pre>
     *
     * <code>string transaction_time = 5;</code>
     * @param value The bytes for transactionTime to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      transactionTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object orderNo_ = "";
    /**
     * <pre>
     * order_ticket_id
     * </pre>
     *
     * <code>string order_no = 6;</code>
     * @return The orderNo.
     */
    public java.lang.String getOrderNo() {
      java.lang.Object ref = orderNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        orderNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * order_ticket_id
     * </pre>
     *
     * <code>string order_no = 6;</code>
     * @return The bytes for orderNo.
     */
    public com.google.protobuf.ByteString
        getOrderNoBytes() {
      java.lang.Object ref = orderNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        orderNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * order_ticket_id
     * </pre>
     *
     * <code>string order_no = 6;</code>
     * @param value The orderNo to set.
     * @return This builder for chaining.
     */
    public Builder setOrderNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      orderNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * order_ticket_id
     * </pre>
     *
     * <code>string order_no = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearOrderNo() {
      
      orderNo_ = getDefaultInstance().getOrderNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * order_ticket_id
     * </pre>
     *
     * <code>string order_no = 6;</code>
     * @param value The bytes for orderNo to set.
     * @return This builder for chaining.
     */
    public Builder setOrderNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      orderNo_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object extendedParams_ = "";
    /**
     * <pre>
     * json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 7;</code>
     * @return The extendedParams.
     */
    public java.lang.String getExtendedParams() {
      java.lang.Object ref = extendedParams_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        extendedParams_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 7;</code>
     * @return The bytes for extendedParams.
     */
    public com.google.protobuf.ByteString
        getExtendedParamsBytes() {
      java.lang.Object ref = extendedParams_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extendedParams_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 7;</code>
     * @param value The extendedParams to set.
     * @return This builder for chaining.
     */
    public Builder setExtendedParams(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      extendedParams_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearExtendedParams() {
      
      extendedParams_ = getDefaultInstance().getExtendedParams();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 7;</code>
     * @param value The bytes for extendedParams to set.
     * @return This builder for chaining.
     */
    public Builder setExtendedParamsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      extendedParams_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:pbis.QueryRequest)
  }

  // @@protoc_insertion_point(class_scope:pbis.QueryRequest)
  private static final cn.hexcloud.pbis.common.service.facade.payment.QueryRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.QueryRequest();
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.QueryRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<QueryRequest>
      PARSER = new com.google.protobuf.AbstractParser<QueryRequest>() {
    @java.lang.Override
    public QueryRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new QueryRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<QueryRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<QueryRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.QueryRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

