// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

public interface QueryRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:pbis.QueryRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *（必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunite(合阔聚合支付)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  java.lang.String getChannel();
  /**
   * <pre>
   *（必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunite(合阔聚合支付)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  com.google.protobuf.ByteString
      getChannelBytes();

  /**
   * <pre>
   *（必传）支付时传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string transaction_id = 2;</code>
   * @return The transactionId.
   */
  java.lang.String getTransactionId();
  /**
   * <pre>
   *（必传）支付时传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string transaction_id = 2;</code>
   * @return The bytes for transactionId.
   */
  com.google.protobuf.ByteString
      getTransactionIdBytes();

  /**
   * <pre>
   *（必传）付款码、支付卡号
   * </pre>
   *
   * <code>string pay_code = 3;</code>
   * @return The payCode.
   */
  java.lang.String getPayCode();
  /**
   * <pre>
   *（必传）付款码、支付卡号
   * </pre>
   *
   * <code>string pay_code = 3;</code>
   * @return The bytes for payCode.
   */
  com.google.protobuf.ByteString
      getPayCodeBytes();

  /**
   * <pre>
   *（可选）第三方流水号
   * </pre>
   *
   * <code>string tp_transaction_id = 4;</code>
   * @return The tpTransactionId.
   */
  java.lang.String getTpTransactionId();
  /**
   * <pre>
   *（可选）第三方流水号
   * </pre>
   *
   * <code>string tp_transaction_id = 4;</code>
   * @return The bytes for tpTransactionId.
   */
  com.google.protobuf.ByteString
      getTpTransactionIdBytes();

  /**
   * <pre>
   *（可选）交易时间 "yyyy-mm-ddThh:mm:ss"
   * </pre>
   *
   * <code>string transaction_time = 5;</code>
   * @return The transactionTime.
   */
  java.lang.String getTransactionTime();
  /**
   * <pre>
   *（可选）交易时间 "yyyy-mm-ddThh:mm:ss"
   * </pre>
   *
   * <code>string transaction_time = 5;</code>
   * @return The bytes for transactionTime.
   */
  com.google.protobuf.ByteString
      getTransactionTimeBytes();

  /**
   * <pre>
   * order_ticket_id
   * </pre>
   *
   * <code>string order_no = 6;</code>
   * @return The orderNo.
   */
  java.lang.String getOrderNo();
  /**
   * <pre>
   * order_ticket_id
   * </pre>
   *
   * <code>string order_no = 6;</code>
   * @return The bytes for orderNo.
   */
  com.google.protobuf.ByteString
      getOrderNoBytes();

  /**
   * <pre>
   * json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 7;</code>
   * @return The extendedParams.
   */
  java.lang.String getExtendedParams();
  /**
   * <pre>
   * json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 7;</code>
   * @return The bytes for extendedParams.
   */
  com.google.protobuf.ByteString
      getExtendedParamsBytes();
}
