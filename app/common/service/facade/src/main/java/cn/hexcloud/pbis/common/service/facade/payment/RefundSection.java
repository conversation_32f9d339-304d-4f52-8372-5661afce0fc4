// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

/**
 * <pre>
 * 退款交易信息
 * </pre>
 *
 * Protobuf type {@code pbis.RefundSection}
 */
public final class RefundSection extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:pbis.RefundSection)
    RefundSectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RefundSection.newBuilder() to construct.
  private RefundSection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RefundSection() {
    channel_ = "";
    transactionId_ = "";
    relatedTransactionId_ = "";
    relatedTpTransactionId_ = "";
    payCode_ = "";
    secretContent_ = "";
    extendedParams_ = "";
    transactionTime_ = "";
    payMethod_ = "";
    currency_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new RefundSection();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private RefundSection(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            channel_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            transactionId_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            relatedTransactionId_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            relatedTpTransactionId_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            payCode_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            secretContent_ = s;
            break;
          }
          case 56: {

            amount_ = input.readInt32();
            break;
          }
          case 66: {
            java.lang.String s = input.readStringRequireUtf8();

            extendedParams_ = s;
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            transactionTime_ = s;
            break;
          }
          case 82: {
            java.lang.String s = input.readStringRequireUtf8();

            payMethod_ = s;
            break;
          }
          case 90: {
            java.lang.String s = input.readStringRequireUtf8();

            currency_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_RefundSection_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_RefundSection_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.payment.RefundSection.class, cn.hexcloud.pbis.common.service.facade.payment.RefundSection.Builder.class);
  }

  public static final int CHANNEL_FIELD_NUMBER = 1;
  private volatile java.lang.Object channel_;
  /**
   * <pre>
   * （必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunion(合阔聚合支付)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The channel.
   */
  @java.lang.Override
  public java.lang.String getChannel() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      channel_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunion(合阔聚合支付)
   * </pre>
   *
   * <code>string channel = 1;</code>
   * @return The bytes for channel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getChannelBytes() {
    java.lang.Object ref = channel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      channel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TRANSACTION_ID_FIELD_NUMBER = 2;
  private volatile java.lang.Object transactionId_;
  /**
   * <pre>
   * （必传）传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string transaction_id = 2;</code>
   * @return The transactionId.
   */
  @java.lang.Override
  public java.lang.String getTransactionId() {
    java.lang.Object ref = transactionId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      transactionId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）传给第三方接口的唯一标识id
   * </pre>
   *
   * <code>string transaction_id = 2;</code>
   * @return The bytes for transactionId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTransactionIdBytes() {
    java.lang.Object ref = transactionId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      transactionId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RELATED_TRANSACTION_ID_FIELD_NUMBER = 3;
  private volatile java.lang.Object relatedTransactionId_;
  /**
   * <pre>
   * （必传）需要退款的交易的唯一标识id
   * </pre>
   *
   * <code>string related_transaction_id = 3;</code>
   * @return The relatedTransactionId.
   */
  @java.lang.Override
  public java.lang.String getRelatedTransactionId() {
    java.lang.Object ref = relatedTransactionId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      relatedTransactionId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）需要退款的交易的唯一标识id
   * </pre>
   *
   * <code>string related_transaction_id = 3;</code>
   * @return The bytes for relatedTransactionId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRelatedTransactionIdBytes() {
    java.lang.Object ref = relatedTransactionId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      relatedTransactionId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RELATED_TP_TRANSACTION_ID_FIELD_NUMBER = 4;
  private volatile java.lang.Object relatedTpTransactionId_;
  /**
   * <pre>
   * （必传）需要退款的第三方交易的唯一标识id
   * </pre>
   *
   * <code>string related_tp_transaction_id = 4;</code>
   * @return The relatedTpTransactionId.
   */
  @java.lang.Override
  public java.lang.String getRelatedTpTransactionId() {
    java.lang.Object ref = relatedTpTransactionId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      relatedTpTransactionId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）需要退款的第三方交易的唯一标识id
   * </pre>
   *
   * <code>string related_tp_transaction_id = 4;</code>
   * @return The bytes for relatedTpTransactionId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRelatedTpTransactionIdBytes() {
    java.lang.Object ref = relatedTpTransactionId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      relatedTpTransactionId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PAY_CODE_FIELD_NUMBER = 5;
  private volatile java.lang.Object payCode_;
  /**
   * <pre>
   * （必传）付款码、支付卡号
   * </pre>
   *
   * <code>string pay_code = 5;</code>
   * @return The payCode.
   */
  @java.lang.Override
  public java.lang.String getPayCode() {
    java.lang.Object ref = payCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      payCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）付款码、支付卡号
   * </pre>
   *
   * <code>string pay_code = 5;</code>
   * @return The bytes for payCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPayCodeBytes() {
    java.lang.Object ref = payCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      payCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SECRET_CONTENT_FIELD_NUMBER = 6;
  private volatile java.lang.Object secretContent_;
  /**
   * <pre>
   * （必传）密码、辅助码、二磁道信息等,格式- password=123&amp;cvn_2=213&amp;expiration=2025/10/13（敏感信息）
   * </pre>
   *
   * <code>string secret_content = 6;</code>
   * @return The secretContent.
   */
  @java.lang.Override
  public java.lang.String getSecretContent() {
    java.lang.Object ref = secretContent_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      secretContent_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）密码、辅助码、二磁道信息等,格式- password=123&amp;cvn_2=213&amp;expiration=2025/10/13（敏感信息）
   * </pre>
   *
   * <code>string secret_content = 6;</code>
   * @return The bytes for secretContent.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSecretContentBytes() {
    java.lang.Object ref = secretContent_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      secretContent_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AMOUNT_FIELD_NUMBER = 7;
  private int amount_;
  /**
   * <pre>
   * （必传）退款金额（单位：分）
   * </pre>
   *
   * <code>int32 amount = 7;</code>
   * @return The amount.
   */
  @java.lang.Override
  public int getAmount() {
    return amount_;
  }

  public static final int EXTENDED_PARAMS_FIELD_NUMBER = 8;
  private volatile java.lang.Object extendedParams_;
  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 8;</code>
   * @return The extendedParams.
   */
  @java.lang.Override
  public java.lang.String getExtendedParams() {
    java.lang.Object ref = extendedParams_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      extendedParams_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 8;</code>
   * @return The bytes for extendedParams.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getExtendedParamsBytes() {
    java.lang.Object ref = extendedParams_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      extendedParams_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TRANSACTION_TIME_FIELD_NUMBER = 9;
  private volatile java.lang.Object transactionTime_;
  /**
   * <pre>
   * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
   * </pre>
   *
   * <code>string transaction_time = 9;</code>
   * @return The transactionTime.
   */
  @java.lang.Override
  public java.lang.String getTransactionTime() {
    java.lang.Object ref = transactionTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      transactionTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
   * </pre>
   *
   * <code>string transaction_time = 9;</code>
   * @return The bytes for transactionTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTransactionTimeBytes() {
    java.lang.Object ref = transactionTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      transactionTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PAY_METHOD_FIELD_NUMBER = 10;
  private volatile java.lang.Object payMethod_;
  /**
   * <pre>
   * （可选）用户真实支付方式，QPay渠道必传
   * </pre>
   *
   * <code>string pay_method = 10;</code>
   * @return The payMethod.
   */
  @java.lang.Override
  public java.lang.String getPayMethod() {
    java.lang.Object ref = payMethod_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      payMethod_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）用户真实支付方式，QPay渠道必传
   * </pre>
   *
   * <code>string pay_method = 10;</code>
   * @return The bytes for payMethod.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPayMethodBytes() {
    java.lang.Object ref = payMethod_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      payMethod_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CURRENCY_FIELD_NUMBER = 11;
  private volatile java.lang.Object currency_;
  /**
   * <pre>
   * （可选）货币（如HKD)
   * </pre>
   *
   * <code>string currency = 11;</code>
   * @return The currency.
   */
  @java.lang.Override
  public java.lang.String getCurrency() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      currency_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）货币（如HKD)
   * </pre>
   *
   * <code>string currency = 11;</code>
   * @return The bytes for currency.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCurrencyBytes() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      currency_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getChannelBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, channel_);
    }
    if (!getTransactionIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, transactionId_);
    }
    if (!getRelatedTransactionIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, relatedTransactionId_);
    }
    if (!getRelatedTpTransactionIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, relatedTpTransactionId_);
    }
    if (!getPayCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, payCode_);
    }
    if (!getSecretContentBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, secretContent_);
    }
    if (amount_ != 0) {
      output.writeInt32(7, amount_);
    }
    if (!getExtendedParamsBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, extendedParams_);
    }
    if (!getTransactionTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, transactionTime_);
    }
    if (!getPayMethodBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, payMethod_);
    }
    if (!getCurrencyBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, currency_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getChannelBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, channel_);
    }
    if (!getTransactionIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, transactionId_);
    }
    if (!getRelatedTransactionIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, relatedTransactionId_);
    }
    if (!getRelatedTpTransactionIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, relatedTpTransactionId_);
    }
    if (!getPayCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, payCode_);
    }
    if (!getSecretContentBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, secretContent_);
    }
    if (amount_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, amount_);
    }
    if (!getExtendedParamsBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, extendedParams_);
    }
    if (!getTransactionTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, transactionTime_);
    }
    if (!getPayMethodBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, payMethod_);
    }
    if (!getCurrencyBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, currency_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.RefundSection)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.payment.RefundSection other = (cn.hexcloud.pbis.common.service.facade.payment.RefundSection) obj;

    if (!getChannel()
        .equals(other.getChannel())) return false;
    if (!getTransactionId()
        .equals(other.getTransactionId())) return false;
    if (!getRelatedTransactionId()
        .equals(other.getRelatedTransactionId())) return false;
    if (!getRelatedTpTransactionId()
        .equals(other.getRelatedTpTransactionId())) return false;
    if (!getPayCode()
        .equals(other.getPayCode())) return false;
    if (!getSecretContent()
        .equals(other.getSecretContent())) return false;
    if (getAmount()
        != other.getAmount()) return false;
    if (!getExtendedParams()
        .equals(other.getExtendedParams())) return false;
    if (!getTransactionTime()
        .equals(other.getTransactionTime())) return false;
    if (!getPayMethod()
        .equals(other.getPayMethod())) return false;
    if (!getCurrency()
        .equals(other.getCurrency())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
    hash = (53 * hash) + getChannel().hashCode();
    hash = (37 * hash) + TRANSACTION_ID_FIELD_NUMBER;
    hash = (53 * hash) + getTransactionId().hashCode();
    hash = (37 * hash) + RELATED_TRANSACTION_ID_FIELD_NUMBER;
    hash = (53 * hash) + getRelatedTransactionId().hashCode();
    hash = (37 * hash) + RELATED_TP_TRANSACTION_ID_FIELD_NUMBER;
    hash = (53 * hash) + getRelatedTpTransactionId().hashCode();
    hash = (37 * hash) + PAY_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getPayCode().hashCode();
    hash = (37 * hash) + SECRET_CONTENT_FIELD_NUMBER;
    hash = (53 * hash) + getSecretContent().hashCode();
    hash = (37 * hash) + AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getAmount();
    hash = (37 * hash) + EXTENDED_PARAMS_FIELD_NUMBER;
    hash = (53 * hash) + getExtendedParams().hashCode();
    hash = (37 * hash) + TRANSACTION_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getTransactionTime().hashCode();
    hash = (37 * hash) + PAY_METHOD_FIELD_NUMBER;
    hash = (53 * hash) + getPayMethod().hashCode();
    hash = (37 * hash) + CURRENCY_FIELD_NUMBER;
    hash = (53 * hash) + getCurrency().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.RefundSection parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.RefundSection parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.RefundSection parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.RefundSection parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.RefundSection parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.RefundSection parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.RefundSection parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.RefundSection parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.RefundSection parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.RefundSection parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.RefundSection parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.RefundSection parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.RefundSection prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 退款交易信息
   * </pre>
   *
   * Protobuf type {@code pbis.RefundSection}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:pbis.RefundSection)
      cn.hexcloud.pbis.common.service.facade.payment.RefundSectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_RefundSection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_RefundSection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.RefundSection.class, cn.hexcloud.pbis.common.service.facade.payment.RefundSection.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.payment.RefundSection.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      channel_ = "";

      transactionId_ = "";

      relatedTransactionId_ = "";

      relatedTpTransactionId_ = "";

      payCode_ = "";

      secretContent_ = "";

      amount_ = 0;

      extendedParams_ = "";

      transactionTime_ = "";

      payMethod_ = "";

      currency_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_RefundSection_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.RefundSection getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.RefundSection.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.RefundSection build() {
      cn.hexcloud.pbis.common.service.facade.payment.RefundSection result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.RefundSection buildPartial() {
      cn.hexcloud.pbis.common.service.facade.payment.RefundSection result = new cn.hexcloud.pbis.common.service.facade.payment.RefundSection(this);
      result.channel_ = channel_;
      result.transactionId_ = transactionId_;
      result.relatedTransactionId_ = relatedTransactionId_;
      result.relatedTpTransactionId_ = relatedTpTransactionId_;
      result.payCode_ = payCode_;
      result.secretContent_ = secretContent_;
      result.amount_ = amount_;
      result.extendedParams_ = extendedParams_;
      result.transactionTime_ = transactionTime_;
      result.payMethod_ = payMethod_;
      result.currency_ = currency_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.RefundSection) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.RefundSection)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.RefundSection other) {
      if (other == cn.hexcloud.pbis.common.service.facade.payment.RefundSection.getDefaultInstance()) return this;
      if (!other.getChannel().isEmpty()) {
        channel_ = other.channel_;
        onChanged();
      }
      if (!other.getTransactionId().isEmpty()) {
        transactionId_ = other.transactionId_;
        onChanged();
      }
      if (!other.getRelatedTransactionId().isEmpty()) {
        relatedTransactionId_ = other.relatedTransactionId_;
        onChanged();
      }
      if (!other.getRelatedTpTransactionId().isEmpty()) {
        relatedTpTransactionId_ = other.relatedTpTransactionId_;
        onChanged();
      }
      if (!other.getPayCode().isEmpty()) {
        payCode_ = other.payCode_;
        onChanged();
      }
      if (!other.getSecretContent().isEmpty()) {
        secretContent_ = other.secretContent_;
        onChanged();
      }
      if (other.getAmount() != 0) {
        setAmount(other.getAmount());
      }
      if (!other.getExtendedParams().isEmpty()) {
        extendedParams_ = other.extendedParams_;
        onChanged();
      }
      if (!other.getTransactionTime().isEmpty()) {
        transactionTime_ = other.transactionTime_;
        onChanged();
      }
      if (!other.getPayMethod().isEmpty()) {
        payMethod_ = other.payMethod_;
        onChanged();
      }
      if (!other.getCurrency().isEmpty()) {
        currency_ = other.currency_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.payment.RefundSection parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.RefundSection) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object channel_ = "";
    /**
     * <pre>
     * （必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunion(合阔聚合支付)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return The channel.
     */
    public java.lang.String getChannel() {
      java.lang.Object ref = channel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        channel_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunion(合阔聚合支付)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return The bytes for channel.
     */
    public com.google.protobuf.ByteString
        getChannelBytes() {
      java.lang.Object ref = channel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunion(合阔聚合支付)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @param value The channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannel(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      channel_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunion(合阔聚合支付)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChannel() {
      
      channel_ = getDefaultInstance().getChannel();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunion(合阔聚合支付)
     * </pre>
     *
     * <code>string channel = 1;</code>
     * @param value The bytes for channel to set.
     * @return This builder for chaining.
     */
    public Builder setChannelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      channel_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object transactionId_ = "";
    /**
     * <pre>
     * （必传）传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string transaction_id = 2;</code>
     * @return The transactionId.
     */
    public java.lang.String getTransactionId() {
      java.lang.Object ref = transactionId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        transactionId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string transaction_id = 2;</code>
     * @return The bytes for transactionId.
     */
    public com.google.protobuf.ByteString
        getTransactionIdBytes() {
      java.lang.Object ref = transactionId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string transaction_id = 2;</code>
     * @param value The transactionId to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      transactionId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string transaction_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearTransactionId() {
      
      transactionId_ = getDefaultInstance().getTransactionId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）传给第三方接口的唯一标识id
     * </pre>
     *
     * <code>string transaction_id = 2;</code>
     * @param value The bytes for transactionId to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      transactionId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object relatedTransactionId_ = "";
    /**
     * <pre>
     * （必传）需要退款的交易的唯一标识id
     * </pre>
     *
     * <code>string related_transaction_id = 3;</code>
     * @return The relatedTransactionId.
     */
    public java.lang.String getRelatedTransactionId() {
      java.lang.Object ref = relatedTransactionId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        relatedTransactionId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）需要退款的交易的唯一标识id
     * </pre>
     *
     * <code>string related_transaction_id = 3;</code>
     * @return The bytes for relatedTransactionId.
     */
    public com.google.protobuf.ByteString
        getRelatedTransactionIdBytes() {
      java.lang.Object ref = relatedTransactionId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        relatedTransactionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）需要退款的交易的唯一标识id
     * </pre>
     *
     * <code>string related_transaction_id = 3;</code>
     * @param value The relatedTransactionId to set.
     * @return This builder for chaining.
     */
    public Builder setRelatedTransactionId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      relatedTransactionId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）需要退款的交易的唯一标识id
     * </pre>
     *
     * <code>string related_transaction_id = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearRelatedTransactionId() {
      
      relatedTransactionId_ = getDefaultInstance().getRelatedTransactionId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）需要退款的交易的唯一标识id
     * </pre>
     *
     * <code>string related_transaction_id = 3;</code>
     * @param value The bytes for relatedTransactionId to set.
     * @return This builder for chaining.
     */
    public Builder setRelatedTransactionIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      relatedTransactionId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object relatedTpTransactionId_ = "";
    /**
     * <pre>
     * （必传）需要退款的第三方交易的唯一标识id
     * </pre>
     *
     * <code>string related_tp_transaction_id = 4;</code>
     * @return The relatedTpTransactionId.
     */
    public java.lang.String getRelatedTpTransactionId() {
      java.lang.Object ref = relatedTpTransactionId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        relatedTpTransactionId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）需要退款的第三方交易的唯一标识id
     * </pre>
     *
     * <code>string related_tp_transaction_id = 4;</code>
     * @return The bytes for relatedTpTransactionId.
     */
    public com.google.protobuf.ByteString
        getRelatedTpTransactionIdBytes() {
      java.lang.Object ref = relatedTpTransactionId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        relatedTpTransactionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）需要退款的第三方交易的唯一标识id
     * </pre>
     *
     * <code>string related_tp_transaction_id = 4;</code>
     * @param value The relatedTpTransactionId to set.
     * @return This builder for chaining.
     */
    public Builder setRelatedTpTransactionId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      relatedTpTransactionId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）需要退款的第三方交易的唯一标识id
     * </pre>
     *
     * <code>string related_tp_transaction_id = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRelatedTpTransactionId() {
      
      relatedTpTransactionId_ = getDefaultInstance().getRelatedTpTransactionId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）需要退款的第三方交易的唯一标识id
     * </pre>
     *
     * <code>string related_tp_transaction_id = 4;</code>
     * @param value The bytes for relatedTpTransactionId to set.
     * @return This builder for chaining.
     */
    public Builder setRelatedTpTransactionIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      relatedTpTransactionId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object payCode_ = "";
    /**
     * <pre>
     * （必传）付款码、支付卡号
     * </pre>
     *
     * <code>string pay_code = 5;</code>
     * @return The payCode.
     */
    public java.lang.String getPayCode() {
      java.lang.Object ref = payCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        payCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）付款码、支付卡号
     * </pre>
     *
     * <code>string pay_code = 5;</code>
     * @return The bytes for payCode.
     */
    public com.google.protobuf.ByteString
        getPayCodeBytes() {
      java.lang.Object ref = payCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）付款码、支付卡号
     * </pre>
     *
     * <code>string pay_code = 5;</code>
     * @param value The payCode to set.
     * @return This builder for chaining.
     */
    public Builder setPayCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      payCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）付款码、支付卡号
     * </pre>
     *
     * <code>string pay_code = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayCode() {
      
      payCode_ = getDefaultInstance().getPayCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）付款码、支付卡号
     * </pre>
     *
     * <code>string pay_code = 5;</code>
     * @param value The bytes for payCode to set.
     * @return This builder for chaining.
     */
    public Builder setPayCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      payCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object secretContent_ = "";
    /**
     * <pre>
     * （必传）密码、辅助码、二磁道信息等,格式- password=123&amp;cvn_2=213&amp;expiration=2025/10/13（敏感信息）
     * </pre>
     *
     * <code>string secret_content = 6;</code>
     * @return The secretContent.
     */
    public java.lang.String getSecretContent() {
      java.lang.Object ref = secretContent_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        secretContent_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）密码、辅助码、二磁道信息等,格式- password=123&amp;cvn_2=213&amp;expiration=2025/10/13（敏感信息）
     * </pre>
     *
     * <code>string secret_content = 6;</code>
     * @return The bytes for secretContent.
     */
    public com.google.protobuf.ByteString
        getSecretContentBytes() {
      java.lang.Object ref = secretContent_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        secretContent_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）密码、辅助码、二磁道信息等,格式- password=123&amp;cvn_2=213&amp;expiration=2025/10/13（敏感信息）
     * </pre>
     *
     * <code>string secret_content = 6;</code>
     * @param value The secretContent to set.
     * @return This builder for chaining.
     */
    public Builder setSecretContent(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      secretContent_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）密码、辅助码、二磁道信息等,格式- password=123&amp;cvn_2=213&amp;expiration=2025/10/13（敏感信息）
     * </pre>
     *
     * <code>string secret_content = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearSecretContent() {
      
      secretContent_ = getDefaultInstance().getSecretContent();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）密码、辅助码、二磁道信息等,格式- password=123&amp;cvn_2=213&amp;expiration=2025/10/13（敏感信息）
     * </pre>
     *
     * <code>string secret_content = 6;</code>
     * @param value The bytes for secretContent to set.
     * @return This builder for chaining.
     */
    public Builder setSecretContentBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      secretContent_ = value;
      onChanged();
      return this;
    }

    private int amount_ ;
    /**
     * <pre>
     * （必传）退款金额（单位：分）
     * </pre>
     *
     * <code>int32 amount = 7;</code>
     * @return The amount.
     */
    @java.lang.Override
    public int getAmount() {
      return amount_;
    }
    /**
     * <pre>
     * （必传）退款金额（单位：分）
     * </pre>
     *
     * <code>int32 amount = 7;</code>
     * @param value The amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmount(int value) {
      
      amount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）退款金额（单位：分）
     * </pre>
     *
     * <code>int32 amount = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmount() {
      
      amount_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object extendedParams_ = "";
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 8;</code>
     * @return The extendedParams.
     */
    public java.lang.String getExtendedParams() {
      java.lang.Object ref = extendedParams_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        extendedParams_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 8;</code>
     * @return The bytes for extendedParams.
     */
    public com.google.protobuf.ByteString
        getExtendedParamsBytes() {
      java.lang.Object ref = extendedParams_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extendedParams_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 8;</code>
     * @param value The extendedParams to set.
     * @return This builder for chaining.
     */
    public Builder setExtendedParams(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      extendedParams_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearExtendedParams() {
      
      extendedParams_ = getDefaultInstance().getExtendedParams();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 8;</code>
     * @param value The bytes for extendedParams to set.
     * @return This builder for chaining.
     */
    public Builder setExtendedParamsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      extendedParams_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object transactionTime_ = "";
    /**
     * <pre>
     * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
     * </pre>
     *
     * <code>string transaction_time = 9;</code>
     * @return The transactionTime.
     */
    public java.lang.String getTransactionTime() {
      java.lang.Object ref = transactionTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        transactionTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
     * </pre>
     *
     * <code>string transaction_time = 9;</code>
     * @return The bytes for transactionTime.
     */
    public com.google.protobuf.ByteString
        getTransactionTimeBytes() {
      java.lang.Object ref = transactionTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
     * </pre>
     *
     * <code>string transaction_time = 9;</code>
     * @param value The transactionTime to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      transactionTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
     * </pre>
     *
     * <code>string transaction_time = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearTransactionTime() {
      
      transactionTime_ = getDefaultInstance().getTransactionTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
     * </pre>
     *
     * <code>string transaction_time = 9;</code>
     * @param value The bytes for transactionTime to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      transactionTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object payMethod_ = "";
    /**
     * <pre>
     * （可选）用户真实支付方式，QPay渠道必传
     * </pre>
     *
     * <code>string pay_method = 10;</code>
     * @return The payMethod.
     */
    public java.lang.String getPayMethod() {
      java.lang.Object ref = payMethod_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        payMethod_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）用户真实支付方式，QPay渠道必传
     * </pre>
     *
     * <code>string pay_method = 10;</code>
     * @return The bytes for payMethod.
     */
    public com.google.protobuf.ByteString
        getPayMethodBytes() {
      java.lang.Object ref = payMethod_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payMethod_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）用户真实支付方式，QPay渠道必传
     * </pre>
     *
     * <code>string pay_method = 10;</code>
     * @param value The payMethod to set.
     * @return This builder for chaining.
     */
    public Builder setPayMethod(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      payMethod_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）用户真实支付方式，QPay渠道必传
     * </pre>
     *
     * <code>string pay_method = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayMethod() {
      
      payMethod_ = getDefaultInstance().getPayMethod();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）用户真实支付方式，QPay渠道必传
     * </pre>
     *
     * <code>string pay_method = 10;</code>
     * @param value The bytes for payMethod to set.
     * @return This builder for chaining.
     */
    public Builder setPayMethodBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      payMethod_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object currency_ = "";
    /**
     * <pre>
     * （可选）货币（如HKD)
     * </pre>
     *
     * <code>string currency = 11;</code>
     * @return The currency.
     */
    public java.lang.String getCurrency() {
      java.lang.Object ref = currency_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        currency_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）货币（如HKD)
     * </pre>
     *
     * <code>string currency = 11;</code>
     * @return The bytes for currency.
     */
    public com.google.protobuf.ByteString
        getCurrencyBytes() {
      java.lang.Object ref = currency_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        currency_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）货币（如HKD)
     * </pre>
     *
     * <code>string currency = 11;</code>
     * @param value The currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrency(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      currency_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）货币（如HKD)
     * </pre>
     *
     * <code>string currency = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurrency() {
      
      currency_ = getDefaultInstance().getCurrency();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）货币（如HKD)
     * </pre>
     *
     * <code>string currency = 11;</code>
     * @param value The bytes for currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrencyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      currency_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:pbis.RefundSection)
  }

  // @@protoc_insertion_point(class_scope:pbis.RefundSection)
  private static final cn.hexcloud.pbis.common.service.facade.payment.RefundSection DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.RefundSection();
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.RefundSection getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RefundSection>
      PARSER = new com.google.protobuf.AbstractParser<RefundSection>() {
    @java.lang.Override
    public RefundSection parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new RefundSection(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<RefundSection> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RefundSection> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.RefundSection getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

