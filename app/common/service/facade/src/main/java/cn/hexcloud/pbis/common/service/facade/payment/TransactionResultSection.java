// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

/**
 * <pre>
 * 支付交易结果信息
 * </pre>
 *
 * Protobuf type {@code pbis.TransactionResultSection}
 */
public final class TransactionResultSection extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:pbis.TransactionResultSection)
    TransactionResultSectionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use TransactionResultSection.newBuilder() to construct.
  private TransactionResultSection(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private TransactionResultSection() {
    transactionState_ = "";
    tpTransactionId_ = "";
    payChannel_ = "";
    payMethod_ = "";
    payer_ = "";
    extendedParams_ = "";
    promotions_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new TransactionResultSection();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private TransactionResultSection(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            transactionState_ = s;
            break;
          }
          case 17: {

            realAmount_ = input.readDouble();
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            tpTransactionId_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            payChannel_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            payMethod_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            payer_ = s;
            break;
          }
          case 57: {

            transactionPoints_ = input.readDouble();
            break;
          }
          case 65: {

            accountPoints_ = input.readDouble();
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            extendedParams_ = s;
            break;
          }
          case 82: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              promotions_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.payment.Promotion>();
              mutable_bitField0_ |= 0x00000001;
            }
            promotions_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.payment.Promotion.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        promotions_ = java.util.Collections.unmodifiableList(promotions_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_TransactionResultSection_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_TransactionResultSection_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.class, cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.Builder.class);
  }

  public static final int TRANSACTION_STATE_FIELD_NUMBER = 1;
  private volatile java.lang.Object transactionState_;
  /**
   * <pre>
   * （必传）交易状态
   * </pre>
   *
   * <code>string transaction_state = 1;</code>
   * @return The transactionState.
   */
  @java.lang.Override
  public java.lang.String getTransactionState() {
    java.lang.Object ref = transactionState_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      transactionState_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）交易状态
   * </pre>
   *
   * <code>string transaction_state = 1;</code>
   * @return The bytes for transactionState.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTransactionStateBytes() {
    java.lang.Object ref = transactionState_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      transactionState_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REAL_AMOUNT_FIELD_NUMBER = 2;
  private double realAmount_;
  /**
   * <pre>
   * （必传）实际发生金额
   * </pre>
   *
   * <code>double real_amount = 2;</code>
   * @return The realAmount.
   */
  @java.lang.Override
  public double getRealAmount() {
    return realAmount_;
  }

  public static final int TP_TRANSACTION_ID_FIELD_NUMBER = 3;
  private volatile java.lang.Object tpTransactionId_;
  /**
   * <pre>
   * （必传）第三方流水号
   * </pre>
   *
   * <code>string tp_transaction_id = 3;</code>
   * @return The tpTransactionId.
   */
  @java.lang.Override
  public java.lang.String getTpTransactionId() {
    java.lang.Object ref = tpTransactionId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      tpTransactionId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）第三方流水号
   * </pre>
   *
   * <code>string tp_transaction_id = 3;</code>
   * @return The bytes for tpTransactionId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTpTransactionIdBytes() {
    java.lang.Object ref = tpTransactionId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      tpTransactionId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PAY_CHANNEL_FIELD_NUMBER = 4;
  private volatile java.lang.Object payChannel_;
  /**
   * <pre>
   * （必传）支付渠道
   * </pre>
   *
   * <code>string pay_channel = 4;</code>
   * @return The payChannel.
   */
  @java.lang.Override
  public java.lang.String getPayChannel() {
    java.lang.Object ref = payChannel_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      payChannel_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （必传）支付渠道
   * </pre>
   *
   * <code>string pay_channel = 4;</code>
   * @return The bytes for payChannel.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPayChannelBytes() {
    java.lang.Object ref = payChannel_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      payChannel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PAY_METHOD_FIELD_NUMBER = 5;
  private volatile java.lang.Object payMethod_;
  /**
   * <pre>
   * （可选）用户真实支付方式
   * </pre>
   *
   * <code>string pay_method = 5;</code>
   * @return The payMethod.
   */
  @java.lang.Override
  public java.lang.String getPayMethod() {
    java.lang.Object ref = payMethod_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      payMethod_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）用户真实支付方式
   * </pre>
   *
   * <code>string pay_method = 5;</code>
   * @return The bytes for payMethod.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPayMethodBytes() {
    java.lang.Object ref = payMethod_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      payMethod_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PAYER_FIELD_NUMBER = 6;
  private volatile java.lang.Object payer_;
  /**
   * <pre>
   * （可选）买家标识
   * </pre>
   *
   * <code>string payer = 6;</code>
   * @return The payer.
   */
  @java.lang.Override
  public java.lang.String getPayer() {
    java.lang.Object ref = payer_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      payer_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）买家标识
   * </pre>
   *
   * <code>string payer = 6;</code>
   * @return The bytes for payer.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPayerBytes() {
    java.lang.Object ref = payer_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      payer_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TRANSACTION_POINTS_FIELD_NUMBER = 7;
  private double transactionPoints_;
  /**
   * <pre>
   * （可选）交易积分
   * </pre>
   *
   * <code>double transaction_points = 7;</code>
   * @return The transactionPoints.
   */
  @java.lang.Override
  public double getTransactionPoints() {
    return transactionPoints_;
  }

  public static final int ACCOUNT_POINTS_FIELD_NUMBER = 8;
  private double accountPoints_;
  /**
   * <pre>
   * （可选）帐户积分
   * </pre>
   *
   * <code>double account_points = 8;</code>
   * @return The accountPoints.
   */
  @java.lang.Override
  public double getAccountPoints() {
    return accountPoints_;
  }

  public static final int EXTENDED_PARAMS_FIELD_NUMBER = 9;
  private volatile java.lang.Object extendedParams_;
  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 9;</code>
   * @return The extendedParams.
   */
  @java.lang.Override
  public java.lang.String getExtendedParams() {
    java.lang.Object ref = extendedParams_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      extendedParams_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 9;</code>
   * @return The bytes for extendedParams.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getExtendedParamsBytes() {
    java.lang.Object ref = extendedParams_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      extendedParams_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PROMOTIONS_FIELD_NUMBER = 10;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.payment.Promotion> promotions_;
  /**
   * <pre>
   * （可选）促销信息(商家折扣、平台折扣)
   * </pre>
   *
   * <code>repeated .pbis.Promotion promotions = 10;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.payment.Promotion> getPromotionsList() {
    return promotions_;
  }
  /**
   * <pre>
   * （可选）促销信息(商家折扣、平台折扣)
   * </pre>
   *
   * <code>repeated .pbis.Promotion promotions = 10;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.payment.PromotionOrBuilder> 
      getPromotionsOrBuilderList() {
    return promotions_;
  }
  /**
   * <pre>
   * （可选）促销信息(商家折扣、平台折扣)
   * </pre>
   *
   * <code>repeated .pbis.Promotion promotions = 10;</code>
   */
  @java.lang.Override
  public int getPromotionsCount() {
    return promotions_.size();
  }
  /**
   * <pre>
   * （可选）促销信息(商家折扣、平台折扣)
   * </pre>
   *
   * <code>repeated .pbis.Promotion promotions = 10;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.Promotion getPromotions(int index) {
    return promotions_.get(index);
  }
  /**
   * <pre>
   * （可选）促销信息(商家折扣、平台折扣)
   * </pre>
   *
   * <code>repeated .pbis.Promotion promotions = 10;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.PromotionOrBuilder getPromotionsOrBuilder(
      int index) {
    return promotions_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getTransactionStateBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, transactionState_);
    }
    if (realAmount_ != 0D) {
      output.writeDouble(2, realAmount_);
    }
    if (!getTpTransactionIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, tpTransactionId_);
    }
    if (!getPayChannelBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, payChannel_);
    }
    if (!getPayMethodBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, payMethod_);
    }
    if (!getPayerBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, payer_);
    }
    if (transactionPoints_ != 0D) {
      output.writeDouble(7, transactionPoints_);
    }
    if (accountPoints_ != 0D) {
      output.writeDouble(8, accountPoints_);
    }
    if (!getExtendedParamsBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, extendedParams_);
    }
    for (int i = 0; i < promotions_.size(); i++) {
      output.writeMessage(10, promotions_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getTransactionStateBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, transactionState_);
    }
    if (realAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(2, realAmount_);
    }
    if (!getTpTransactionIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, tpTransactionId_);
    }
    if (!getPayChannelBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, payChannel_);
    }
    if (!getPayMethodBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, payMethod_);
    }
    if (!getPayerBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, payer_);
    }
    if (transactionPoints_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(7, transactionPoints_);
    }
    if (accountPoints_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(8, accountPoints_);
    }
    if (!getExtendedParamsBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, extendedParams_);
    }
    for (int i = 0; i < promotions_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(10, promotions_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection other = (cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection) obj;

    if (!getTransactionState()
        .equals(other.getTransactionState())) return false;
    if (java.lang.Double.doubleToLongBits(getRealAmount())
        != java.lang.Double.doubleToLongBits(
            other.getRealAmount())) return false;
    if (!getTpTransactionId()
        .equals(other.getTpTransactionId())) return false;
    if (!getPayChannel()
        .equals(other.getPayChannel())) return false;
    if (!getPayMethod()
        .equals(other.getPayMethod())) return false;
    if (!getPayer()
        .equals(other.getPayer())) return false;
    if (java.lang.Double.doubleToLongBits(getTransactionPoints())
        != java.lang.Double.doubleToLongBits(
            other.getTransactionPoints())) return false;
    if (java.lang.Double.doubleToLongBits(getAccountPoints())
        != java.lang.Double.doubleToLongBits(
            other.getAccountPoints())) return false;
    if (!getExtendedParams()
        .equals(other.getExtendedParams())) return false;
    if (!getPromotionsList()
        .equals(other.getPromotionsList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TRANSACTION_STATE_FIELD_NUMBER;
    hash = (53 * hash) + getTransactionState().hashCode();
    hash = (37 * hash) + REAL_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getRealAmount()));
    hash = (37 * hash) + TP_TRANSACTION_ID_FIELD_NUMBER;
    hash = (53 * hash) + getTpTransactionId().hashCode();
    hash = (37 * hash) + PAY_CHANNEL_FIELD_NUMBER;
    hash = (53 * hash) + getPayChannel().hashCode();
    hash = (37 * hash) + PAY_METHOD_FIELD_NUMBER;
    hash = (53 * hash) + getPayMethod().hashCode();
    hash = (37 * hash) + PAYER_FIELD_NUMBER;
    hash = (53 * hash) + getPayer().hashCode();
    hash = (37 * hash) + TRANSACTION_POINTS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getTransactionPoints()));
    hash = (37 * hash) + ACCOUNT_POINTS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getAccountPoints()));
    hash = (37 * hash) + EXTENDED_PARAMS_FIELD_NUMBER;
    hash = (53 * hash) + getExtendedParams().hashCode();
    if (getPromotionsCount() > 0) {
      hash = (37 * hash) + PROMOTIONS_FIELD_NUMBER;
      hash = (53 * hash) + getPromotionsList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 支付交易结果信息
   * </pre>
   *
   * Protobuf type {@code pbis.TransactionResultSection}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:pbis.TransactionResultSection)
      cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSectionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_TransactionResultSection_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_TransactionResultSection_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.class, cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getPromotionsFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      transactionState_ = "";

      realAmount_ = 0D;

      tpTransactionId_ = "";

      payChannel_ = "";

      payMethod_ = "";

      payer_ = "";

      transactionPoints_ = 0D;

      accountPoints_ = 0D;

      extendedParams_ = "";

      if (promotionsBuilder_ == null) {
        promotions_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        promotionsBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.PaymentIntegrationOuterClass.internal_static_pbis_TransactionResultSection_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection build() {
      cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection buildPartial() {
      cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection result = new cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection(this);
      int from_bitField0_ = bitField0_;
      result.transactionState_ = transactionState_;
      result.realAmount_ = realAmount_;
      result.tpTransactionId_ = tpTransactionId_;
      result.payChannel_ = payChannel_;
      result.payMethod_ = payMethod_;
      result.payer_ = payer_;
      result.transactionPoints_ = transactionPoints_;
      result.accountPoints_ = accountPoints_;
      result.extendedParams_ = extendedParams_;
      if (promotionsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          promotions_ = java.util.Collections.unmodifiableList(promotions_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.promotions_ = promotions_;
      } else {
        result.promotions_ = promotionsBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection other) {
      if (other == cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection.getDefaultInstance()) return this;
      if (!other.getTransactionState().isEmpty()) {
        transactionState_ = other.transactionState_;
        onChanged();
      }
      if (other.getRealAmount() != 0D) {
        setRealAmount(other.getRealAmount());
      }
      if (!other.getTpTransactionId().isEmpty()) {
        tpTransactionId_ = other.tpTransactionId_;
        onChanged();
      }
      if (!other.getPayChannel().isEmpty()) {
        payChannel_ = other.payChannel_;
        onChanged();
      }
      if (!other.getPayMethod().isEmpty()) {
        payMethod_ = other.payMethod_;
        onChanged();
      }
      if (!other.getPayer().isEmpty()) {
        payer_ = other.payer_;
        onChanged();
      }
      if (other.getTransactionPoints() != 0D) {
        setTransactionPoints(other.getTransactionPoints());
      }
      if (other.getAccountPoints() != 0D) {
        setAccountPoints(other.getAccountPoints());
      }
      if (!other.getExtendedParams().isEmpty()) {
        extendedParams_ = other.extendedParams_;
        onChanged();
      }
      if (promotionsBuilder_ == null) {
        if (!other.promotions_.isEmpty()) {
          if (promotions_.isEmpty()) {
            promotions_ = other.promotions_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePromotionsIsMutable();
            promotions_.addAll(other.promotions_);
          }
          onChanged();
        }
      } else {
        if (!other.promotions_.isEmpty()) {
          if (promotionsBuilder_.isEmpty()) {
            promotionsBuilder_.dispose();
            promotionsBuilder_ = null;
            promotions_ = other.promotions_;
            bitField0_ = (bitField0_ & ~0x00000001);
            promotionsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getPromotionsFieldBuilder() : null;
          } else {
            promotionsBuilder_.addAllMessages(other.promotions_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object transactionState_ = "";
    /**
     * <pre>
     * （必传）交易状态
     * </pre>
     *
     * <code>string transaction_state = 1;</code>
     * @return The transactionState.
     */
    public java.lang.String getTransactionState() {
      java.lang.Object ref = transactionState_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        transactionState_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）交易状态
     * </pre>
     *
     * <code>string transaction_state = 1;</code>
     * @return The bytes for transactionState.
     */
    public com.google.protobuf.ByteString
        getTransactionStateBytes() {
      java.lang.Object ref = transactionState_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionState_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）交易状态
     * </pre>
     *
     * <code>string transaction_state = 1;</code>
     * @param value The transactionState to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionState(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      transactionState_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）交易状态
     * </pre>
     *
     * <code>string transaction_state = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearTransactionState() {
      
      transactionState_ = getDefaultInstance().getTransactionState();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）交易状态
     * </pre>
     *
     * <code>string transaction_state = 1;</code>
     * @param value The bytes for transactionState to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionStateBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      transactionState_ = value;
      onChanged();
      return this;
    }

    private double realAmount_ ;
    /**
     * <pre>
     * （必传）实际发生金额
     * </pre>
     *
     * <code>double real_amount = 2;</code>
     * @return The realAmount.
     */
    @java.lang.Override
    public double getRealAmount() {
      return realAmount_;
    }
    /**
     * <pre>
     * （必传）实际发生金额
     * </pre>
     *
     * <code>double real_amount = 2;</code>
     * @param value The realAmount to set.
     * @return This builder for chaining.
     */
    public Builder setRealAmount(double value) {
      
      realAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）实际发生金额
     * </pre>
     *
     * <code>double real_amount = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearRealAmount() {
      
      realAmount_ = 0D;
      onChanged();
      return this;
    }

    private java.lang.Object tpTransactionId_ = "";
    /**
     * <pre>
     * （必传）第三方流水号
     * </pre>
     *
     * <code>string tp_transaction_id = 3;</code>
     * @return The tpTransactionId.
     */
    public java.lang.String getTpTransactionId() {
      java.lang.Object ref = tpTransactionId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        tpTransactionId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）第三方流水号
     * </pre>
     *
     * <code>string tp_transaction_id = 3;</code>
     * @return The bytes for tpTransactionId.
     */
    public com.google.protobuf.ByteString
        getTpTransactionIdBytes() {
      java.lang.Object ref = tpTransactionId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tpTransactionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）第三方流水号
     * </pre>
     *
     * <code>string tp_transaction_id = 3;</code>
     * @param value The tpTransactionId to set.
     * @return This builder for chaining.
     */
    public Builder setTpTransactionId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      tpTransactionId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）第三方流水号
     * </pre>
     *
     * <code>string tp_transaction_id = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearTpTransactionId() {
      
      tpTransactionId_ = getDefaultInstance().getTpTransactionId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）第三方流水号
     * </pre>
     *
     * <code>string tp_transaction_id = 3;</code>
     * @param value The bytes for tpTransactionId to set.
     * @return This builder for chaining.
     */
    public Builder setTpTransactionIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      tpTransactionId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object payChannel_ = "";
    /**
     * <pre>
     * （必传）支付渠道
     * </pre>
     *
     * <code>string pay_channel = 4;</code>
     * @return The payChannel.
     */
    public java.lang.String getPayChannel() {
      java.lang.Object ref = payChannel_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        payChannel_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （必传）支付渠道
     * </pre>
     *
     * <code>string pay_channel = 4;</code>
     * @return The bytes for payChannel.
     */
    public com.google.protobuf.ByteString
        getPayChannelBytes() {
      java.lang.Object ref = payChannel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payChannel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （必传）支付渠道
     * </pre>
     *
     * <code>string pay_channel = 4;</code>
     * @param value The payChannel to set.
     * @return This builder for chaining.
     */
    public Builder setPayChannel(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      payChannel_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）支付渠道
     * </pre>
     *
     * <code>string pay_channel = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayChannel() {
      
      payChannel_ = getDefaultInstance().getPayChannel();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （必传）支付渠道
     * </pre>
     *
     * <code>string pay_channel = 4;</code>
     * @param value The bytes for payChannel to set.
     * @return This builder for chaining.
     */
    public Builder setPayChannelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      payChannel_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object payMethod_ = "";
    /**
     * <pre>
     * （可选）用户真实支付方式
     * </pre>
     *
     * <code>string pay_method = 5;</code>
     * @return The payMethod.
     */
    public java.lang.String getPayMethod() {
      java.lang.Object ref = payMethod_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        payMethod_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）用户真实支付方式
     * </pre>
     *
     * <code>string pay_method = 5;</code>
     * @return The bytes for payMethod.
     */
    public com.google.protobuf.ByteString
        getPayMethodBytes() {
      java.lang.Object ref = payMethod_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payMethod_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）用户真实支付方式
     * </pre>
     *
     * <code>string pay_method = 5;</code>
     * @param value The payMethod to set.
     * @return This builder for chaining.
     */
    public Builder setPayMethod(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      payMethod_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）用户真实支付方式
     * </pre>
     *
     * <code>string pay_method = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayMethod() {
      
      payMethod_ = getDefaultInstance().getPayMethod();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）用户真实支付方式
     * </pre>
     *
     * <code>string pay_method = 5;</code>
     * @param value The bytes for payMethod to set.
     * @return This builder for chaining.
     */
    public Builder setPayMethodBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      payMethod_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object payer_ = "";
    /**
     * <pre>
     * （可选）买家标识
     * </pre>
     *
     * <code>string payer = 6;</code>
     * @return The payer.
     */
    public java.lang.String getPayer() {
      java.lang.Object ref = payer_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        payer_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）买家标识
     * </pre>
     *
     * <code>string payer = 6;</code>
     * @return The bytes for payer.
     */
    public com.google.protobuf.ByteString
        getPayerBytes() {
      java.lang.Object ref = payer_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payer_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）买家标识
     * </pre>
     *
     * <code>string payer = 6;</code>
     * @param value The payer to set.
     * @return This builder for chaining.
     */
    public Builder setPayer(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      payer_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）买家标识
     * </pre>
     *
     * <code>string payer = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayer() {
      
      payer_ = getDefaultInstance().getPayer();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）买家标识
     * </pre>
     *
     * <code>string payer = 6;</code>
     * @param value The bytes for payer to set.
     * @return This builder for chaining.
     */
    public Builder setPayerBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      payer_ = value;
      onChanged();
      return this;
    }

    private double transactionPoints_ ;
    /**
     * <pre>
     * （可选）交易积分
     * </pre>
     *
     * <code>double transaction_points = 7;</code>
     * @return The transactionPoints.
     */
    @java.lang.Override
    public double getTransactionPoints() {
      return transactionPoints_;
    }
    /**
     * <pre>
     * （可选）交易积分
     * </pre>
     *
     * <code>double transaction_points = 7;</code>
     * @param value The transactionPoints to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionPoints(double value) {
      
      transactionPoints_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）交易积分
     * </pre>
     *
     * <code>double transaction_points = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearTransactionPoints() {
      
      transactionPoints_ = 0D;
      onChanged();
      return this;
    }

    private double accountPoints_ ;
    /**
     * <pre>
     * （可选）帐户积分
     * </pre>
     *
     * <code>double account_points = 8;</code>
     * @return The accountPoints.
     */
    @java.lang.Override
    public double getAccountPoints() {
      return accountPoints_;
    }
    /**
     * <pre>
     * （可选）帐户积分
     * </pre>
     *
     * <code>double account_points = 8;</code>
     * @param value The accountPoints to set.
     * @return This builder for chaining.
     */
    public Builder setAccountPoints(double value) {
      
      accountPoints_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）帐户积分
     * </pre>
     *
     * <code>double account_points = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearAccountPoints() {
      
      accountPoints_ = 0D;
      onChanged();
      return this;
    }

    private java.lang.Object extendedParams_ = "";
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 9;</code>
     * @return The extendedParams.
     */
    public java.lang.String getExtendedParams() {
      java.lang.Object ref = extendedParams_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        extendedParams_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 9;</code>
     * @return The bytes for extendedParams.
     */
    public com.google.protobuf.ByteString
        getExtendedParamsBytes() {
      java.lang.Object ref = extendedParams_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extendedParams_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 9;</code>
     * @param value The extendedParams to set.
     * @return This builder for chaining.
     */
    public Builder setExtendedParams(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      extendedParams_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearExtendedParams() {
      
      extendedParams_ = getDefaultInstance().getExtendedParams();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * （可选）json格式的附加扩展信息
     * </pre>
     *
     * <code>string extended_params = 9;</code>
     * @param value The bytes for extendedParams to set.
     * @return This builder for chaining.
     */
    public Builder setExtendedParamsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      extendedParams_ = value;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.payment.Promotion> promotions_ =
      java.util.Collections.emptyList();
    private void ensurePromotionsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        promotions_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.payment.Promotion>(promotions_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.Promotion, cn.hexcloud.pbis.common.service.facade.payment.Promotion.Builder, cn.hexcloud.pbis.common.service.facade.payment.PromotionOrBuilder> promotionsBuilder_;

    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.payment.Promotion> getPromotionsList() {
      if (promotionsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(promotions_);
      } else {
        return promotionsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public int getPromotionsCount() {
      if (promotionsBuilder_ == null) {
        return promotions_.size();
      } else {
        return promotionsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.Promotion getPromotions(int index) {
      if (promotionsBuilder_ == null) {
        return promotions_.get(index);
      } else {
        return promotionsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public Builder setPromotions(
        int index, cn.hexcloud.pbis.common.service.facade.payment.Promotion value) {
      if (promotionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePromotionsIsMutable();
        promotions_.set(index, value);
        onChanged();
      } else {
        promotionsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public Builder setPromotions(
        int index, cn.hexcloud.pbis.common.service.facade.payment.Promotion.Builder builderForValue) {
      if (promotionsBuilder_ == null) {
        ensurePromotionsIsMutable();
        promotions_.set(index, builderForValue.build());
        onChanged();
      } else {
        promotionsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public Builder addPromotions(cn.hexcloud.pbis.common.service.facade.payment.Promotion value) {
      if (promotionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePromotionsIsMutable();
        promotions_.add(value);
        onChanged();
      } else {
        promotionsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public Builder addPromotions(
        int index, cn.hexcloud.pbis.common.service.facade.payment.Promotion value) {
      if (promotionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePromotionsIsMutable();
        promotions_.add(index, value);
        onChanged();
      } else {
        promotionsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public Builder addPromotions(
        cn.hexcloud.pbis.common.service.facade.payment.Promotion.Builder builderForValue) {
      if (promotionsBuilder_ == null) {
        ensurePromotionsIsMutable();
        promotions_.add(builderForValue.build());
        onChanged();
      } else {
        promotionsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public Builder addPromotions(
        int index, cn.hexcloud.pbis.common.service.facade.payment.Promotion.Builder builderForValue) {
      if (promotionsBuilder_ == null) {
        ensurePromotionsIsMutable();
        promotions_.add(index, builderForValue.build());
        onChanged();
      } else {
        promotionsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public Builder addAllPromotions(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.payment.Promotion> values) {
      if (promotionsBuilder_ == null) {
        ensurePromotionsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, promotions_);
        onChanged();
      } else {
        promotionsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public Builder clearPromotions() {
      if (promotionsBuilder_ == null) {
        promotions_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        promotionsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public Builder removePromotions(int index) {
      if (promotionsBuilder_ == null) {
        ensurePromotionsIsMutable();
        promotions_.remove(index);
        onChanged();
      } else {
        promotionsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.Promotion.Builder getPromotionsBuilder(
        int index) {
      return getPromotionsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.PromotionOrBuilder getPromotionsOrBuilder(
        int index) {
      if (promotionsBuilder_ == null) {
        return promotions_.get(index);  } else {
        return promotionsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.payment.PromotionOrBuilder> 
         getPromotionsOrBuilderList() {
      if (promotionsBuilder_ != null) {
        return promotionsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(promotions_);
      }
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.Promotion.Builder addPromotionsBuilder() {
      return getPromotionsFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.payment.Promotion.getDefaultInstance());
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.payment.Promotion.Builder addPromotionsBuilder(
        int index) {
      return getPromotionsFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.payment.Promotion.getDefaultInstance());
    }
    /**
     * <pre>
     * （可选）促销信息(商家折扣、平台折扣)
     * </pre>
     *
     * <code>repeated .pbis.Promotion promotions = 10;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.payment.Promotion.Builder> 
         getPromotionsBuilderList() {
      return getPromotionsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.payment.Promotion, cn.hexcloud.pbis.common.service.facade.payment.Promotion.Builder, cn.hexcloud.pbis.common.service.facade.payment.PromotionOrBuilder> 
        getPromotionsFieldBuilder() {
      if (promotionsBuilder_ == null) {
        promotionsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.payment.Promotion, cn.hexcloud.pbis.common.service.facade.payment.Promotion.Builder, cn.hexcloud.pbis.common.service.facade.payment.PromotionOrBuilder>(
                promotions_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        promotions_ = null;
      }
      return promotionsBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:pbis.TransactionResultSection)
  }

  // @@protoc_insertion_point(class_scope:pbis.TransactionResultSection)
  private static final cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection();
  }

  public static cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TransactionResultSection>
      PARSER = new com.google.protobuf.AbstractParser<TransactionResultSection>() {
    @java.lang.Override
    public TransactionResultSection parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new TransactionResultSection(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<TransactionResultSection> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TransactionResultSection> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.payment.TransactionResultSection getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

