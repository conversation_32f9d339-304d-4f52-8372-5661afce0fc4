// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentIntegration.proto

package cn.hexcloud.pbis.common.service.facade.payment;

public interface TransactionResultSectionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:pbis.TransactionResultSection)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）交易状态
   * </pre>
   *
   * <code>string transaction_state = 1;</code>
   * @return The transactionState.
   */
  java.lang.String getTransactionState();
  /**
   * <pre>
   * （必传）交易状态
   * </pre>
   *
   * <code>string transaction_state = 1;</code>
   * @return The bytes for transactionState.
   */
  com.google.protobuf.ByteString
      getTransactionStateBytes();

  /**
   * <pre>
   * （必传）实际发生金额
   * </pre>
   *
   * <code>double real_amount = 2;</code>
   * @return The realAmount.
   */
  double getRealAmount();

  /**
   * <pre>
   * （必传）第三方流水号
   * </pre>
   *
   * <code>string tp_transaction_id = 3;</code>
   * @return The tpTransactionId.
   */
  java.lang.String getTpTransactionId();
  /**
   * <pre>
   * （必传）第三方流水号
   * </pre>
   *
   * <code>string tp_transaction_id = 3;</code>
   * @return The bytes for tpTransactionId.
   */
  com.google.protobuf.ByteString
      getTpTransactionIdBytes();

  /**
   * <pre>
   * （必传）支付渠道
   * </pre>
   *
   * <code>string pay_channel = 4;</code>
   * @return The payChannel.
   */
  java.lang.String getPayChannel();
  /**
   * <pre>
   * （必传）支付渠道
   * </pre>
   *
   * <code>string pay_channel = 4;</code>
   * @return The bytes for payChannel.
   */
  com.google.protobuf.ByteString
      getPayChannelBytes();

  /**
   * <pre>
   * （可选）用户真实支付方式
   * </pre>
   *
   * <code>string pay_method = 5;</code>
   * @return The payMethod.
   */
  java.lang.String getPayMethod();
  /**
   * <pre>
   * （可选）用户真实支付方式
   * </pre>
   *
   * <code>string pay_method = 5;</code>
   * @return The bytes for payMethod.
   */
  com.google.protobuf.ByteString
      getPayMethodBytes();

  /**
   * <pre>
   * （可选）买家标识
   * </pre>
   *
   * <code>string payer = 6;</code>
   * @return The payer.
   */
  java.lang.String getPayer();
  /**
   * <pre>
   * （可选）买家标识
   * </pre>
   *
   * <code>string payer = 6;</code>
   * @return The bytes for payer.
   */
  com.google.protobuf.ByteString
      getPayerBytes();

  /**
   * <pre>
   * （可选）交易积分
   * </pre>
   *
   * <code>double transaction_points = 7;</code>
   * @return The transactionPoints.
   */
  double getTransactionPoints();

  /**
   * <pre>
   * （可选）帐户积分
   * </pre>
   *
   * <code>double account_points = 8;</code>
   * @return The accountPoints.
   */
  double getAccountPoints();

  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 9;</code>
   * @return The extendedParams.
   */
  java.lang.String getExtendedParams();
  /**
   * <pre>
   * （可选）json格式的附加扩展信息
   * </pre>
   *
   * <code>string extended_params = 9;</code>
   * @return The bytes for extendedParams.
   */
  com.google.protobuf.ByteString
      getExtendedParamsBytes();

  /**
   * <pre>
   * （可选）促销信息(商家折扣、平台折扣)
   * </pre>
   *
   * <code>repeated .pbis.Promotion promotions = 10;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.payment.Promotion> 
      getPromotionsList();
  /**
   * <pre>
   * （可选）促销信息(商家折扣、平台折扣)
   * </pre>
   *
   * <code>repeated .pbis.Promotion promotions = 10;</code>
   */
  cn.hexcloud.pbis.common.service.facade.payment.Promotion getPromotions(int index);
  /**
   * <pre>
   * （可选）促销信息(商家折扣、平台折扣)
   * </pre>
   *
   * <code>repeated .pbis.Promotion promotions = 10;</code>
   */
  int getPromotionsCount();
  /**
   * <pre>
   * （可选）促销信息(商家折扣、平台折扣)
   * </pre>
   *
   * <code>repeated .pbis.Promotion promotions = 10;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.payment.PromotionOrBuilder> 
      getPromotionsOrBuilderList();
  /**
   * <pre>
   * （可选）促销信息(商家折扣、平台折扣)
   * </pre>
   *
   * <code>repeated .pbis.Promotion promotions = 10;</code>
   */
  cn.hexcloud.pbis.common.service.facade.payment.PromotionOrBuilder getPromotionsOrBuilder(
      int index);
}
