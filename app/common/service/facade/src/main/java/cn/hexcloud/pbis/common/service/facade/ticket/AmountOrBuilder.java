// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface AmountOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.Amount)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>double taxAmount = 1;</code>
   * @return The taxAmount.
   */
  double getTaxAmount();

  /**
   * <code>double gross_amount = 2;</code>
   * @return The grossAmount.
   */
  double getGrossAmount();

  /**
   * <code>double net_amount = 3;</code>
   * @return The netAmount.
   */
  double getNetAmount();

  /**
   * <code>double pay_amount = 4;</code>
   * @return The payAmount.
   */
  double getPayAmount();

  /**
   * <code>double discount_amount = 5;</code>
   * @return The discountAmount.
   */
  double getDiscountAmount();

  /**
   * <code>double removezero_amount = 6;</code>
   * @return The removezeroAmount.
   */
  double getRemovezeroAmount();

  /**
   * <code>double rounding = 7;</code>
   * @return The rounding.
   */
  double getRounding();

  /**
   * <code>double overflow_amount = 8;</code>
   * @return The overflowAmount.
   */
  double getOverflowAmount();

  /**
   * <code>double changeAmount = 9;</code>
   * @return The changeAmount.
   */
  double getChangeAmount();

  /**
   * <code>double serviceFee = 10;</code>
   * @return The serviceFee.
   */
  double getServiceFee();

  /**
   * <code>double tip = 11;</code>
   * @return The tip.
   */
  double getTip();

  /**
   * <code>double commission = 12;</code>
   * @return The commission.
   */
  double getCommission();

  /**
   * <code>double amount_0 = 13;</code>
   * @return The amount0.
   */
  double getAmount0();

  /**
   * <code>double amount_1 = 14;</code>
   * @return The amount1.
   */
  double getAmount1();

  /**
   * <code>double amount_2 = 15;</code>
   * @return The amount2.
   */
  double getAmount2();

  /**
   * <code>double amount_3 = 16;</code>
   * @return The amount3.
   */
  double getAmount3();

  /**
   * <code>double amount_4 = 17;</code>
   * @return The amount4.
   */
  double getAmount4();

  /**
   * <pre>
   *价税合一
   * </pre>
   *
   * <code>bool taxIncluded = 18;</code>
   * @return The taxIncluded.
   */
  boolean getTaxIncluded();

  /**
   * <pre>
   *其他费用
   * </pre>
   *
   * <code>float otherFee = 19;</code>
   * @return The otherFee.
   */
  float getOtherFee();

  /**
   * <pre>
   *商家优惠承担
   * </pre>
   *
   * <code>float merchant_discount_amount = 20;</code>
   * @return The merchantDiscountAmount.
   */
  float getMerchantDiscountAmount();

  /**
   * <pre>
   *活动平台优惠承担
   * </pre>
   *
   * <code>float platform_discount_amount = 21;</code>
   * @return The platformDiscountAmount.
   */
  float getPlatformDiscountAmount();

  /**
   * <pre>
   *预计收入
   * </pre>
   *
   * <code>float projected_income = 22;</code>
   * @return The projectedIncome.
   */
  float getProjectedIncome();

  /**
   * <pre>
   *应付金额/应收金额
   * </pre>
   *
   * <code>double receivable = 23;</code>
   * @return The receivable.
   */
  double getReceivable();

  /**
   * <code>double real_amount = 24;</code>
   * @return The realAmount.
   */
  double getRealAmount();

  /**
   * <code>double business_amount = 25;</code>
   * @return The businessAmount.
   */
  double getBusinessAmount();

  /**
   * <code>double expend_amount = 26;</code>
   * @return The expendAmount.
   */
  double getExpendAmount();

  /**
   * <code>double payment_transfer_amount = 27;</code>
   * @return The paymentTransferAmount.
   */
  double getPaymentTransferAmount();

  /**
   * <code>double discount_transfer_amount = 28;</code>
   * @return The discountTransferAmount.
   */
  double getDiscountTransferAmount();

  /**
   * <code>double store_discount_amount = 29;</code>
   * @return The storeDiscountAmount.
   */
  double getStoreDiscountAmount();

  /**
   * <code>double discount_merchant_contribute = 30;</code>
   * @return The discountMerchantContribute.
   */
  double getDiscountMerchantContribute();

  /**
   * <code>double discount_platform_contribute = 31;</code>
   * @return The discountPlatformContribute.
   */
  double getDiscountPlatformContribute();

  /**
   * <code>double discount_buyer_contribute = 32;</code>
   * @return The discountBuyerContribute.
   */
  double getDiscountBuyerContribute();

  /**
   * <code>double discount_other_contribute = 33;</code>
   * @return The discountOtherContribute.
   */
  double getDiscountOtherContribute();

  /**
   * <code>double pay_merchant_contribute = 34;</code>
   * @return The payMerchantContribute.
   */
  double getPayMerchantContribute();

  /**
   * <code>double pay_platform_contribute = 35;</code>
   * @return The payPlatformContribute.
   */
  double getPayPlatformContribute();

  /**
   * <code>double pay_buyer_contribute = 36;</code>
   * @return The payBuyerContribute.
   */
  double getPayBuyerContribute();

  /**
   * <code>double pay_other_contribute = 37;</code>
   * @return The payOtherContribute.
   */
  double getPayOtherContribute();

  /**
   * <code>double delivery_fee = 38;</code>
   * @return The deliveryFee.
   */
  double getDeliveryFee();

  /**
   * <code>double delivery_fee_for_platform = 39;</code>
   * @return The deliveryFeeForPlatform.
   */
  double getDeliveryFeeForPlatform();

  /**
   * <code>double delivery_fee_for_merchant = 40;</code>
   * @return The deliveryFeeForMerchant.
   */
  double getDeliveryFeeForMerchant();
}
