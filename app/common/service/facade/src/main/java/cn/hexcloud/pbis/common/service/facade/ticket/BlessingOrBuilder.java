// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface BlessingOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.Blessing)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string templateId = 1;</code>
   * @return The templateId.
   */
  java.lang.String getTemplateId();
  /**
   * <code>string templateId = 1;</code>
   * @return The bytes for templateId.
   */
  com.google.protobuf.ByteString
      getTemplateIdBytes();

  /**
   * <code>string to = 2;</code>
   * @return The to.
   */
  java.lang.String getTo();
  /**
   * <code>string to = 2;</code>
   * @return The bytes for to.
   */
  com.google.protobuf.ByteString
      getToBytes();

  /**
   * <code>string content = 3;</code>
   * @return The content.
   */
  java.lang.String getContent();
  /**
   * <code>string content = 3;</code>
   * @return The bytes for content.
   */
  com.google.protobuf.ByteString
      getContentBytes();

  /**
   * <code>string from = 4;</code>
   * @return The from.
   */
  java.lang.String getFrom();
  /**
   * <code>string from = 4;</code>
   * @return The bytes for from.
   */
  com.google.protobuf.ByteString
      getFromBytes();
}
