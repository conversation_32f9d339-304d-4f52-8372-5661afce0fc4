// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface CurrencyOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.Currency)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *币种名称
   * </pre>
   *
   * <code>string name = 1;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   *币种名称
   * </pre>
   *
   * <code>string name = 1;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   *币种符号
   * </pre>
   *
   * <code>string symbol = 2;</code>
   * @return The symbol.
   */
  java.lang.String getSymbol();
  /**
   * <pre>
   *币种符号
   * </pre>
   *
   * <code>string symbol = 2;</code>
   * @return The bytes for symbol.
   */
  com.google.protobuf.ByteString
      getSymbolBytes();
}
