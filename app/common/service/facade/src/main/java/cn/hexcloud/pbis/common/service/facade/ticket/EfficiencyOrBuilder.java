// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface EfficiencyOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.Efficiency)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *订单确认时间
   * </pre>
   *
   * <code>string confirmed_time = 1;</code>
   * @return The confirmedTime.
   */
  java.lang.String getConfirmedTime();
  /**
   * <pre>
   *订单确认时间
   * </pre>
   *
   * <code>string confirmed_time = 1;</code>
   * @return The bytes for confirmedTime.
   */
  com.google.protobuf.ByteString
      getConfirmedTimeBytes();

  /**
   * <pre>
   *制作完成时间
   * </pre>
   *
   * <code>string made_time = 2;</code>
   * @return The madeTime.
   */
  java.lang.String getMadeTime();
  /**
   * <pre>
   *制作完成时间
   * </pre>
   *
   * <code>string made_time = 2;</code>
   * @return The bytes for madeTime.
   */
  com.google.protobuf.ByteString
      getMadeTimeBytes();

  /**
   * <pre>
   *物流接单时间
   * </pre>
   *
   * <code>string assigned_time = 3;</code>
   * @return The assignedTime.
   */
  java.lang.String getAssignedTime();
  /**
   * <pre>
   *物流接单时间
   * </pre>
   *
   * <code>string assigned_time = 3;</code>
   * @return The bytes for assignedTime.
   */
  com.google.protobuf.ByteString
      getAssignedTimeBytes();

  /**
   * <pre>
   *骑手到店时间
   * </pre>
   *
   * <code>string arrived_time = 4;</code>
   * @return The arrivedTime.
   */
  java.lang.String getArrivedTime();
  /**
   * <pre>
   *骑手到店时间
   * </pre>
   *
   * <code>string arrived_time = 4;</code>
   * @return The bytes for arrivedTime.
   */
  com.google.protobuf.ByteString
      getArrivedTimeBytes();

  /**
   * <pre>
   *骑手取餐时间
   * </pre>
   *
   * <code>string fetched_time = 5;</code>
   * @return The fetchedTime.
   */
  java.lang.String getFetchedTime();
  /**
   * <pre>
   *骑手取餐时间
   * </pre>
   *
   * <code>string fetched_time = 5;</code>
   * @return The bytes for fetchedTime.
   */
  com.google.protobuf.ByteString
      getFetchedTimeBytes();

  /**
   * <pre>
   *骑手送达时间
   * </pre>
   *
   * <code>string delivered_time = 6;</code>
   * @return The deliveredTime.
   */
  java.lang.String getDeliveredTime();
  /**
   * <pre>
   *骑手送达时间
   * </pre>
   *
   * <code>string delivered_time = 6;</code>
   * @return The bytes for deliveredTime.
   */
  com.google.protobuf.ByteString
      getDeliveredTimeBytes();

  /**
   * <pre>
   *制作时长
   * </pre>
   *
   * <code>float make_span = 7;</code>
   * @return The makeSpan.
   */
  float getMakeSpan();

  /**
   * <pre>
   *平均每杯制作时长
   * </pre>
   *
   * <code>float avg_make_span = 8;</code>
   * @return The avgMakeSpan.
   */
  float getAvgMakeSpan();

  /**
   * <pre>
   *取餐时长
   * </pre>
   *
   * <code>float arrive_span = 9;</code>
   * @return The arriveSpan.
   */
  float getArriveSpan();

  /**
   * <pre>
   *配送时长
   * </pre>
   *
   * <code>float deliver_span = 10;</code>
   * @return The deliverSpan.
   */
  float getDeliverSpan();
}
