// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

/**
 * Protobuf type {@code coupon.Fee}
 */
public final class Fee extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:coupon.Fee)
    FeeOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Fee.newBuilder() to construct.
  private Fee(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Fee() {
    name_ = "";
    type_ = "";
    detailFees_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Fee();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Fee(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 17: {

            price_ = input.readDouble();
            break;
          }
          case 24: {

            qty_ = input.readInt32();
            break;
          }
          case 33: {

            amount_ = input.readDouble();
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            type_ = s;
            break;
          }
          case 53: {

            discountRate_ = input.readFloat();
            break;
          }
          case 58: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              detailFees_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.Fee>();
              mutable_bitField0_ |= 0x00000001;
            }
            detailFees_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.Fee.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        detailFees_ = java.util.Collections.unmodifiableList(detailFees_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Fee_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Fee_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.ticket.Fee.class, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder.class);
  }

  public static final int NAME_FIELD_NUMBER = 1;
  private volatile java.lang.Object name_;
  /**
   * <code>string name = 1;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <code>string name = 1;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PRICE_FIELD_NUMBER = 2;
  private double price_;
  /**
   * <code>double price = 2;</code>
   * @return The price.
   */
  @java.lang.Override
  public double getPrice() {
    return price_;
  }

  public static final int QTY_FIELD_NUMBER = 3;
  private int qty_;
  /**
   * <code>int32 qty = 3;</code>
   * @return The qty.
   */
  @java.lang.Override
  public int getQty() {
    return qty_;
  }

  public static final int AMOUNT_FIELD_NUMBER = 4;
  private double amount_;
  /**
   * <code>double amount = 4;</code>
   * @return The amount.
   */
  @java.lang.Override
  public double getAmount() {
    return amount_;
  }

  public static final int TYPE_FIELD_NUMBER = 5;
  private volatile java.lang.Object type_;
  /**
   * <code>string type = 5;</code>
   * @return The type.
   */
  @java.lang.Override
  public java.lang.String getType() {
    java.lang.Object ref = type_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      type_ = s;
      return s;
    }
  }
  /**
   * <code>string type = 5;</code>
   * @return The bytes for type.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTypeBytes() {
    java.lang.Object ref = type_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      type_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DISCOUNT_RATE_FIELD_NUMBER = 6;
  private float discountRate_;
  /**
   * <code>float discount_rate = 6;</code>
   * @return The discountRate.
   */
  @java.lang.Override
  public float getDiscountRate() {
    return discountRate_;
  }

  public static final int DETAIL_FEES_FIELD_NUMBER = 7;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee> detailFees_;
  /**
   * <code>repeated .coupon.Fee detail_fees = 7;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee> getDetailFeesList() {
    return detailFees_;
  }
  /**
   * <code>repeated .coupon.Fee detail_fees = 7;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder> 
      getDetailFeesOrBuilderList() {
    return detailFees_;
  }
  /**
   * <code>repeated .coupon.Fee detail_fees = 7;</code>
   */
  @java.lang.Override
  public int getDetailFeesCount() {
    return detailFees_.size();
  }
  /**
   * <code>repeated .coupon.Fee detail_fees = 7;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Fee getDetailFees(int index) {
    return detailFees_.get(index);
  }
  /**
   * <code>repeated .coupon.Fee detail_fees = 7;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder getDetailFeesOrBuilder(
      int index) {
    return detailFees_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
    }
    if (price_ != 0D) {
      output.writeDouble(2, price_);
    }
    if (qty_ != 0) {
      output.writeInt32(3, qty_);
    }
    if (amount_ != 0D) {
      output.writeDouble(4, amount_);
    }
    if (!getTypeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, type_);
    }
    if (discountRate_ != 0F) {
      output.writeFloat(6, discountRate_);
    }
    for (int i = 0; i < detailFees_.size(); i++) {
      output.writeMessage(7, detailFees_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
    }
    if (price_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(2, price_);
    }
    if (qty_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, qty_);
    }
    if (amount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(4, amount_);
    }
    if (!getTypeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, type_);
    }
    if (discountRate_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(6, discountRate_);
    }
    for (int i = 0; i < detailFees_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, detailFees_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.ticket.Fee)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.ticket.Fee other = (cn.hexcloud.pbis.common.service.facade.ticket.Fee) obj;

    if (!getName()
        .equals(other.getName())) return false;
    if (java.lang.Double.doubleToLongBits(getPrice())
        != java.lang.Double.doubleToLongBits(
            other.getPrice())) return false;
    if (getQty()
        != other.getQty()) return false;
    if (java.lang.Double.doubleToLongBits(getAmount())
        != java.lang.Double.doubleToLongBits(
            other.getAmount())) return false;
    if (!getType()
        .equals(other.getType())) return false;
    if (java.lang.Float.floatToIntBits(getDiscountRate())
        != java.lang.Float.floatToIntBits(
            other.getDiscountRate())) return false;
    if (!getDetailFeesList()
        .equals(other.getDetailFeesList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + PRICE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPrice()));
    hash = (37 * hash) + QTY_FIELD_NUMBER;
    hash = (53 * hash) + getQty();
    hash = (37 * hash) + AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getAmount()));
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getType().hashCode();
    hash = (37 * hash) + DISCOUNT_RATE_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getDiscountRate());
    if (getDetailFeesCount() > 0) {
      hash = (37 * hash) + DETAIL_FEES_FIELD_NUMBER;
      hash = (53 * hash) + getDetailFeesList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.ticket.Fee parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Fee parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Fee parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Fee parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Fee parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Fee parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Fee parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Fee parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Fee parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Fee parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Fee parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Fee parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.ticket.Fee prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code coupon.Fee}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:coupon.Fee)
      cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Fee_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Fee_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.ticket.Fee.class, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.ticket.Fee.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getDetailFeesFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      name_ = "";

      price_ = 0D;

      qty_ = 0;

      amount_ = 0D;

      type_ = "";

      discountRate_ = 0F;

      if (detailFeesBuilder_ == null) {
        detailFees_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        detailFeesBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Fee_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.Fee getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.ticket.Fee.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.Fee build() {
      cn.hexcloud.pbis.common.service.facade.ticket.Fee result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.Fee buildPartial() {
      cn.hexcloud.pbis.common.service.facade.ticket.Fee result = new cn.hexcloud.pbis.common.service.facade.ticket.Fee(this);
      int from_bitField0_ = bitField0_;
      result.name_ = name_;
      result.price_ = price_;
      result.qty_ = qty_;
      result.amount_ = amount_;
      result.type_ = type_;
      result.discountRate_ = discountRate_;
      if (detailFeesBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          detailFees_ = java.util.Collections.unmodifiableList(detailFees_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.detailFees_ = detailFees_;
      } else {
        result.detailFees_ = detailFeesBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.ticket.Fee) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.ticket.Fee)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.ticket.Fee other) {
      if (other == cn.hexcloud.pbis.common.service.facade.ticket.Fee.getDefaultInstance()) return this;
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (other.getPrice() != 0D) {
        setPrice(other.getPrice());
      }
      if (other.getQty() != 0) {
        setQty(other.getQty());
      }
      if (other.getAmount() != 0D) {
        setAmount(other.getAmount());
      }
      if (!other.getType().isEmpty()) {
        type_ = other.type_;
        onChanged();
      }
      if (other.getDiscountRate() != 0F) {
        setDiscountRate(other.getDiscountRate());
      }
      if (detailFeesBuilder_ == null) {
        if (!other.detailFees_.isEmpty()) {
          if (detailFees_.isEmpty()) {
            detailFees_ = other.detailFees_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureDetailFeesIsMutable();
            detailFees_.addAll(other.detailFees_);
          }
          onChanged();
        }
      } else {
        if (!other.detailFees_.isEmpty()) {
          if (detailFeesBuilder_.isEmpty()) {
            detailFeesBuilder_.dispose();
            detailFeesBuilder_ = null;
            detailFees_ = other.detailFees_;
            bitField0_ = (bitField0_ & ~0x00000001);
            detailFeesBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getDetailFeesFieldBuilder() : null;
          } else {
            detailFeesBuilder_.addAllMessages(other.detailFees_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.ticket.Fee parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.ticket.Fee) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object name_ = "";
    /**
     * <code>string name = 1;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string name = 1;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string name = 1;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string name = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <code>string name = 1;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private double price_ ;
    /**
     * <code>double price = 2;</code>
     * @return The price.
     */
    @java.lang.Override
    public double getPrice() {
      return price_;
    }
    /**
     * <code>double price = 2;</code>
     * @param value The price to set.
     * @return This builder for chaining.
     */
    public Builder setPrice(double value) {
      
      price_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double price = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrice() {
      
      price_ = 0D;
      onChanged();
      return this;
    }

    private int qty_ ;
    /**
     * <code>int32 qty = 3;</code>
     * @return The qty.
     */
    @java.lang.Override
    public int getQty() {
      return qty_;
    }
    /**
     * <code>int32 qty = 3;</code>
     * @param value The qty to set.
     * @return This builder for chaining.
     */
    public Builder setQty(int value) {
      
      qty_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>int32 qty = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearQty() {
      
      qty_ = 0;
      onChanged();
      return this;
    }

    private double amount_ ;
    /**
     * <code>double amount = 4;</code>
     * @return The amount.
     */
    @java.lang.Override
    public double getAmount() {
      return amount_;
    }
    /**
     * <code>double amount = 4;</code>
     * @param value The amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmount(double value) {
      
      amount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double amount = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmount() {
      
      amount_ = 0D;
      onChanged();
      return this;
    }

    private java.lang.Object type_ = "";
    /**
     * <code>string type = 5;</code>
     * @return The type.
     */
    public java.lang.String getType() {
      java.lang.Object ref = type_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        type_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string type = 5;</code>
     * @return The bytes for type.
     */
    public com.google.protobuf.ByteString
        getTypeBytes() {
      java.lang.Object ref = type_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        type_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string type = 5;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      type_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string type = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      
      type_ = getDefaultInstance().getType();
      onChanged();
      return this;
    }
    /**
     * <code>string type = 5;</code>
     * @param value The bytes for type to set.
     * @return This builder for chaining.
     */
    public Builder setTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      type_ = value;
      onChanged();
      return this;
    }

    private float discountRate_ ;
    /**
     * <code>float discount_rate = 6;</code>
     * @return The discountRate.
     */
    @java.lang.Override
    public float getDiscountRate() {
      return discountRate_;
    }
    /**
     * <code>float discount_rate = 6;</code>
     * @param value The discountRate to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountRate(float value) {
      
      discountRate_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>float discount_rate = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountRate() {
      
      discountRate_ = 0F;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee> detailFees_ =
      java.util.Collections.emptyList();
    private void ensureDetailFeesIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        detailFees_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.Fee>(detailFees_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Fee, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder, cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder> detailFeesBuilder_;

    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee> getDetailFeesList() {
      if (detailFeesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(detailFees_);
      } else {
        return detailFeesBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public int getDetailFeesCount() {
      if (detailFeesBuilder_ == null) {
        return detailFees_.size();
      } else {
        return detailFeesBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Fee getDetailFees(int index) {
      if (detailFeesBuilder_ == null) {
        return detailFees_.get(index);
      } else {
        return detailFeesBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public Builder setDetailFees(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Fee value) {
      if (detailFeesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetailFeesIsMutable();
        detailFees_.set(index, value);
        onChanged();
      } else {
        detailFeesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public Builder setDetailFees(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder builderForValue) {
      if (detailFeesBuilder_ == null) {
        ensureDetailFeesIsMutable();
        detailFees_.set(index, builderForValue.build());
        onChanged();
      } else {
        detailFeesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public Builder addDetailFees(cn.hexcloud.pbis.common.service.facade.ticket.Fee value) {
      if (detailFeesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetailFeesIsMutable();
        detailFees_.add(value);
        onChanged();
      } else {
        detailFeesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public Builder addDetailFees(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Fee value) {
      if (detailFeesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetailFeesIsMutable();
        detailFees_.add(index, value);
        onChanged();
      } else {
        detailFeesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public Builder addDetailFees(
        cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder builderForValue) {
      if (detailFeesBuilder_ == null) {
        ensureDetailFeesIsMutable();
        detailFees_.add(builderForValue.build());
        onChanged();
      } else {
        detailFeesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public Builder addDetailFees(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder builderForValue) {
      if (detailFeesBuilder_ == null) {
        ensureDetailFeesIsMutable();
        detailFees_.add(index, builderForValue.build());
        onChanged();
      } else {
        detailFeesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public Builder addAllDetailFees(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.ticket.Fee> values) {
      if (detailFeesBuilder_ == null) {
        ensureDetailFeesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, detailFees_);
        onChanged();
      } else {
        detailFeesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public Builder clearDetailFees() {
      if (detailFeesBuilder_ == null) {
        detailFees_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        detailFeesBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public Builder removeDetailFees(int index) {
      if (detailFeesBuilder_ == null) {
        ensureDetailFeesIsMutable();
        detailFees_.remove(index);
        onChanged();
      } else {
        detailFeesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder getDetailFeesBuilder(
        int index) {
      return getDetailFeesFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder getDetailFeesOrBuilder(
        int index) {
      if (detailFeesBuilder_ == null) {
        return detailFees_.get(index);  } else {
        return detailFeesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder> 
         getDetailFeesOrBuilderList() {
      if (detailFeesBuilder_ != null) {
        return detailFeesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(detailFees_);
      }
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder addDetailFeesBuilder() {
      return getDetailFeesFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.ticket.Fee.getDefaultInstance());
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder addDetailFeesBuilder(
        int index) {
      return getDetailFeesFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.ticket.Fee.getDefaultInstance());
    }
    /**
     * <code>repeated .coupon.Fee detail_fees = 7;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder> 
         getDetailFeesBuilderList() {
      return getDetailFeesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Fee, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder, cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder> 
        getDetailFeesFieldBuilder() {
      if (detailFeesBuilder_ == null) {
        detailFeesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.Fee, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder, cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder>(
                detailFees_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        detailFees_ = null;
      }
      return detailFeesBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:coupon.Fee)
  }

  // @@protoc_insertion_point(class_scope:coupon.Fee)
  private static final cn.hexcloud.pbis.common.service.facade.ticket.Fee DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.ticket.Fee();
  }

  public static cn.hexcloud.pbis.common.service.facade.ticket.Fee getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Fee>
      PARSER = new com.google.protobuf.AbstractParser<Fee>() {
    @java.lang.Override
    public Fee parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Fee(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Fee> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Fee> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Fee getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

