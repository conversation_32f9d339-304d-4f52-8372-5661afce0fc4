// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface InvoiceInfoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.InvoiceInfo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *是否开具发票
   * </pre>
   *
   * <code>bool issue = 1;</code>
   * @return The issue.
   */
  boolean getIssue();

  /**
   * <pre>
   *发票类型
   * </pre>
   *
   * <code>string type = 2;</code>
   * @return The type.
   */
  java.lang.String getType();
  /**
   * <pre>
   *发票类型
   * </pre>
   *
   * <code>string type = 2;</code>
   * @return The bytes for type.
   */
  com.google.protobuf.ByteString
      getTypeBytes();

  /**
   * <pre>
   *发票抬头
   * </pre>
   *
   * <code>string title = 3;</code>
   * @return The title.
   */
  java.lang.String getTitle();
  /**
   * <pre>
   *发票抬头
   * </pre>
   *
   * <code>string title = 3;</code>
   * @return The bytes for title.
   */
  com.google.protobuf.ByteString
      getTitleBytes();

  /**
   * <pre>
   *纳税人识别号
   * </pre>
   *
   * <code>string taxPayerId = 4;</code>
   * @return The taxPayerId.
   */
  java.lang.String getTaxPayerId();
  /**
   * <pre>
   *纳税人识别号
   * </pre>
   *
   * <code>string taxPayerId = 4;</code>
   * @return The bytes for taxPayerId.
   */
  com.google.protobuf.ByteString
      getTaxPayerIdBytes();
}
