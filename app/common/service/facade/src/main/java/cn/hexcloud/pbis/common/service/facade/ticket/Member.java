// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

/**
 * Protobuf type {@code coupon.Member}
 */
public final class Member extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:coupon.Member)
    MemberOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Member.newBuilder() to construct.
  private Member(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Member() {
    memberCode_ = "";
    mobile_ = "";
    name_ = "";
    greetings_ = "";
    gradeCode_ = "";
    gradeName_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Member();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Member(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            memberCode_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            mobile_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            greetings_ = s;
            break;
          }
          case 40: {

            balancePoints_ = input.readInt64();
            break;
          }
          case 48: {

            totalPoints_ = input.readInt64();
            break;
          }
          case 56: {

            orderPoints_ = input.readInt64();
            break;
          }
          case 66: {
            java.lang.String s = input.readStringRequireUtf8();

            gradeCode_ = s;
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            gradeName_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Member_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Member_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.ticket.Member.class, cn.hexcloud.pbis.common.service.facade.ticket.Member.Builder.class);
  }

  public static final int MEMBER_CODE_FIELD_NUMBER = 1;
  private volatile java.lang.Object memberCode_;
  /**
   * <code>string member_code = 1;</code>
   * @return The memberCode.
   */
  @java.lang.Override
  public java.lang.String getMemberCode() {
    java.lang.Object ref = memberCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      memberCode_ = s;
      return s;
    }
  }
  /**
   * <code>string member_code = 1;</code>
   * @return The bytes for memberCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMemberCodeBytes() {
    java.lang.Object ref = memberCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      memberCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MOBILE_FIELD_NUMBER = 2;
  private volatile java.lang.Object mobile_;
  /**
   * <code>string mobile = 2;</code>
   * @return The mobile.
   */
  @java.lang.Override
  public java.lang.String getMobile() {
    java.lang.Object ref = mobile_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      mobile_ = s;
      return s;
    }
  }
  /**
   * <code>string mobile = 2;</code>
   * @return The bytes for mobile.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMobileBytes() {
    java.lang.Object ref = mobile_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      mobile_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 3;
  private volatile java.lang.Object name_;
  /**
   * <code>string name = 3;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <code>string name = 3;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int GREETINGS_FIELD_NUMBER = 4;
  private volatile java.lang.Object greetings_;
  /**
   * <code>string greetings = 4;</code>
   * @return The greetings.
   */
  @java.lang.Override
  public java.lang.String getGreetings() {
    java.lang.Object ref = greetings_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      greetings_ = s;
      return s;
    }
  }
  /**
   * <code>string greetings = 4;</code>
   * @return The bytes for greetings.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getGreetingsBytes() {
    java.lang.Object ref = greetings_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      greetings_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BALANCE_POINTS_FIELD_NUMBER = 5;
  private long balancePoints_;
  /**
   * <code>int64 balance_points = 5;</code>
   * @return The balancePoints.
   */
  @java.lang.Override
  public long getBalancePoints() {
    return balancePoints_;
  }

  public static final int TOTAL_POINTS_FIELD_NUMBER = 6;
  private long totalPoints_;
  /**
   * <code>int64 total_points = 6;</code>
   * @return The totalPoints.
   */
  @java.lang.Override
  public long getTotalPoints() {
    return totalPoints_;
  }

  public static final int ORDER_POINTS_FIELD_NUMBER = 7;
  private long orderPoints_;
  /**
   * <code>int64 order_points = 7;</code>
   * @return The orderPoints.
   */
  @java.lang.Override
  public long getOrderPoints() {
    return orderPoints_;
  }

  public static final int GRADE_CODE_FIELD_NUMBER = 8;
  private volatile java.lang.Object gradeCode_;
  /**
   * <pre>
   *会员等级编码
   * </pre>
   *
   * <code>string grade_code = 8;</code>
   * @return The gradeCode.
   */
  @java.lang.Override
  public java.lang.String getGradeCode() {
    java.lang.Object ref = gradeCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      gradeCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *会员等级编码
   * </pre>
   *
   * <code>string grade_code = 8;</code>
   * @return The bytes for gradeCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getGradeCodeBytes() {
    java.lang.Object ref = gradeCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      gradeCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int GRADE_NAME_FIELD_NUMBER = 9;
  private volatile java.lang.Object gradeName_;
  /**
   * <pre>
   *会员等级名称
   * </pre>
   *
   * <code>string grade_name = 9;</code>
   * @return The gradeName.
   */
  @java.lang.Override
  public java.lang.String getGradeName() {
    java.lang.Object ref = gradeName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      gradeName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *会员等级名称
   * </pre>
   *
   * <code>string grade_name = 9;</code>
   * @return The bytes for gradeName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getGradeNameBytes() {
    java.lang.Object ref = gradeName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      gradeName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getMemberCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, memberCode_);
    }
    if (!getMobileBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, mobile_);
    }
    if (!getNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, name_);
    }
    if (!getGreetingsBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, greetings_);
    }
    if (balancePoints_ != 0L) {
      output.writeInt64(5, balancePoints_);
    }
    if (totalPoints_ != 0L) {
      output.writeInt64(6, totalPoints_);
    }
    if (orderPoints_ != 0L) {
      output.writeInt64(7, orderPoints_);
    }
    if (!getGradeCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, gradeCode_);
    }
    if (!getGradeNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, gradeName_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getMemberCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, memberCode_);
    }
    if (!getMobileBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, mobile_);
    }
    if (!getNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, name_);
    }
    if (!getGreetingsBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, greetings_);
    }
    if (balancePoints_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, balancePoints_);
    }
    if (totalPoints_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(6, totalPoints_);
    }
    if (orderPoints_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(7, orderPoints_);
    }
    if (!getGradeCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, gradeCode_);
    }
    if (!getGradeNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, gradeName_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.ticket.Member)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.ticket.Member other = (cn.hexcloud.pbis.common.service.facade.ticket.Member) obj;

    if (!getMemberCode()
        .equals(other.getMemberCode())) return false;
    if (!getMobile()
        .equals(other.getMobile())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (!getGreetings()
        .equals(other.getGreetings())) return false;
    if (getBalancePoints()
        != other.getBalancePoints()) return false;
    if (getTotalPoints()
        != other.getTotalPoints()) return false;
    if (getOrderPoints()
        != other.getOrderPoints()) return false;
    if (!getGradeCode()
        .equals(other.getGradeCode())) return false;
    if (!getGradeName()
        .equals(other.getGradeName())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + MEMBER_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getMemberCode().hashCode();
    hash = (37 * hash) + MOBILE_FIELD_NUMBER;
    hash = (53 * hash) + getMobile().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + GREETINGS_FIELD_NUMBER;
    hash = (53 * hash) + getGreetings().hashCode();
    hash = (37 * hash) + BALANCE_POINTS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getBalancePoints());
    hash = (37 * hash) + TOTAL_POINTS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTotalPoints());
    hash = (37 * hash) + ORDER_POINTS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getOrderPoints());
    hash = (37 * hash) + GRADE_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getGradeCode().hashCode();
    hash = (37 * hash) + GRADE_NAME_FIELD_NUMBER;
    hash = (53 * hash) + getGradeName().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.ticket.Member parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Member parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Member parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Member parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Member parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Member parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Member parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Member parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Member parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Member parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Member parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Member parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.ticket.Member prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code coupon.Member}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:coupon.Member)
      cn.hexcloud.pbis.common.service.facade.ticket.MemberOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Member_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Member_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.ticket.Member.class, cn.hexcloud.pbis.common.service.facade.ticket.Member.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.ticket.Member.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      memberCode_ = "";

      mobile_ = "";

      name_ = "";

      greetings_ = "";

      balancePoints_ = 0L;

      totalPoints_ = 0L;

      orderPoints_ = 0L;

      gradeCode_ = "";

      gradeName_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Member_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.Member getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.ticket.Member.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.Member build() {
      cn.hexcloud.pbis.common.service.facade.ticket.Member result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.Member buildPartial() {
      cn.hexcloud.pbis.common.service.facade.ticket.Member result = new cn.hexcloud.pbis.common.service.facade.ticket.Member(this);
      result.memberCode_ = memberCode_;
      result.mobile_ = mobile_;
      result.name_ = name_;
      result.greetings_ = greetings_;
      result.balancePoints_ = balancePoints_;
      result.totalPoints_ = totalPoints_;
      result.orderPoints_ = orderPoints_;
      result.gradeCode_ = gradeCode_;
      result.gradeName_ = gradeName_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.ticket.Member) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.ticket.Member)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.ticket.Member other) {
      if (other == cn.hexcloud.pbis.common.service.facade.ticket.Member.getDefaultInstance()) return this;
      if (!other.getMemberCode().isEmpty()) {
        memberCode_ = other.memberCode_;
        onChanged();
      }
      if (!other.getMobile().isEmpty()) {
        mobile_ = other.mobile_;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (!other.getGreetings().isEmpty()) {
        greetings_ = other.greetings_;
        onChanged();
      }
      if (other.getBalancePoints() != 0L) {
        setBalancePoints(other.getBalancePoints());
      }
      if (other.getTotalPoints() != 0L) {
        setTotalPoints(other.getTotalPoints());
      }
      if (other.getOrderPoints() != 0L) {
        setOrderPoints(other.getOrderPoints());
      }
      if (!other.getGradeCode().isEmpty()) {
        gradeCode_ = other.gradeCode_;
        onChanged();
      }
      if (!other.getGradeName().isEmpty()) {
        gradeName_ = other.gradeName_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.ticket.Member parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.ticket.Member) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object memberCode_ = "";
    /**
     * <code>string member_code = 1;</code>
     * @return The memberCode.
     */
    public java.lang.String getMemberCode() {
      java.lang.Object ref = memberCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        memberCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string member_code = 1;</code>
     * @return The bytes for memberCode.
     */
    public com.google.protobuf.ByteString
        getMemberCodeBytes() {
      java.lang.Object ref = memberCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        memberCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string member_code = 1;</code>
     * @param value The memberCode to set.
     * @return This builder for chaining.
     */
    public Builder setMemberCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      memberCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string member_code = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearMemberCode() {
      
      memberCode_ = getDefaultInstance().getMemberCode();
      onChanged();
      return this;
    }
    /**
     * <code>string member_code = 1;</code>
     * @param value The bytes for memberCode to set.
     * @return This builder for chaining.
     */
    public Builder setMemberCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      memberCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object mobile_ = "";
    /**
     * <code>string mobile = 2;</code>
     * @return The mobile.
     */
    public java.lang.String getMobile() {
      java.lang.Object ref = mobile_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        mobile_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string mobile = 2;</code>
     * @return The bytes for mobile.
     */
    public com.google.protobuf.ByteString
        getMobileBytes() {
      java.lang.Object ref = mobile_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        mobile_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string mobile = 2;</code>
     * @param value The mobile to set.
     * @return This builder for chaining.
     */
    public Builder setMobile(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      mobile_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string mobile = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMobile() {
      
      mobile_ = getDefaultInstance().getMobile();
      onChanged();
      return this;
    }
    /**
     * <code>string mobile = 2;</code>
     * @param value The bytes for mobile to set.
     * @return This builder for chaining.
     */
    public Builder setMobileBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      mobile_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <code>string name = 3;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string name = 3;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string name = 3;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string name = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <code>string name = 3;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object greetings_ = "";
    /**
     * <code>string greetings = 4;</code>
     * @return The greetings.
     */
    public java.lang.String getGreetings() {
      java.lang.Object ref = greetings_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        greetings_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string greetings = 4;</code>
     * @return The bytes for greetings.
     */
    public com.google.protobuf.ByteString
        getGreetingsBytes() {
      java.lang.Object ref = greetings_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        greetings_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string greetings = 4;</code>
     * @param value The greetings to set.
     * @return This builder for chaining.
     */
    public Builder setGreetings(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      greetings_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string greetings = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearGreetings() {
      
      greetings_ = getDefaultInstance().getGreetings();
      onChanged();
      return this;
    }
    /**
     * <code>string greetings = 4;</code>
     * @param value The bytes for greetings to set.
     * @return This builder for chaining.
     */
    public Builder setGreetingsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      greetings_ = value;
      onChanged();
      return this;
    }

    private long balancePoints_ ;
    /**
     * <code>int64 balance_points = 5;</code>
     * @return The balancePoints.
     */
    @java.lang.Override
    public long getBalancePoints() {
      return balancePoints_;
    }
    /**
     * <code>int64 balance_points = 5;</code>
     * @param value The balancePoints to set.
     * @return This builder for chaining.
     */
    public Builder setBalancePoints(long value) {
      
      balancePoints_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>int64 balance_points = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearBalancePoints() {
      
      balancePoints_ = 0L;
      onChanged();
      return this;
    }

    private long totalPoints_ ;
    /**
     * <code>int64 total_points = 6;</code>
     * @return The totalPoints.
     */
    @java.lang.Override
    public long getTotalPoints() {
      return totalPoints_;
    }
    /**
     * <code>int64 total_points = 6;</code>
     * @param value The totalPoints to set.
     * @return This builder for chaining.
     */
    public Builder setTotalPoints(long value) {
      
      totalPoints_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>int64 total_points = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearTotalPoints() {
      
      totalPoints_ = 0L;
      onChanged();
      return this;
    }

    private long orderPoints_ ;
    /**
     * <code>int64 order_points = 7;</code>
     * @return The orderPoints.
     */
    @java.lang.Override
    public long getOrderPoints() {
      return orderPoints_;
    }
    /**
     * <code>int64 order_points = 7;</code>
     * @param value The orderPoints to set.
     * @return This builder for chaining.
     */
    public Builder setOrderPoints(long value) {
      
      orderPoints_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>int64 order_points = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearOrderPoints() {
      
      orderPoints_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object gradeCode_ = "";
    /**
     * <pre>
     *会员等级编码
     * </pre>
     *
     * <code>string grade_code = 8;</code>
     * @return The gradeCode.
     */
    public java.lang.String getGradeCode() {
      java.lang.Object ref = gradeCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        gradeCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *会员等级编码
     * </pre>
     *
     * <code>string grade_code = 8;</code>
     * @return The bytes for gradeCode.
     */
    public com.google.protobuf.ByteString
        getGradeCodeBytes() {
      java.lang.Object ref = gradeCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gradeCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *会员等级编码
     * </pre>
     *
     * <code>string grade_code = 8;</code>
     * @param value The gradeCode to set.
     * @return This builder for chaining.
     */
    public Builder setGradeCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      gradeCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *会员等级编码
     * </pre>
     *
     * <code>string grade_code = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearGradeCode() {
      
      gradeCode_ = getDefaultInstance().getGradeCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *会员等级编码
     * </pre>
     *
     * <code>string grade_code = 8;</code>
     * @param value The bytes for gradeCode to set.
     * @return This builder for chaining.
     */
    public Builder setGradeCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      gradeCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object gradeName_ = "";
    /**
     * <pre>
     *会员等级名称
     * </pre>
     *
     * <code>string grade_name = 9;</code>
     * @return The gradeName.
     */
    public java.lang.String getGradeName() {
      java.lang.Object ref = gradeName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        gradeName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *会员等级名称
     * </pre>
     *
     * <code>string grade_name = 9;</code>
     * @return The bytes for gradeName.
     */
    public com.google.protobuf.ByteString
        getGradeNameBytes() {
      java.lang.Object ref = gradeName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gradeName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *会员等级名称
     * </pre>
     *
     * <code>string grade_name = 9;</code>
     * @param value The gradeName to set.
     * @return This builder for chaining.
     */
    public Builder setGradeName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      gradeName_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *会员等级名称
     * </pre>
     *
     * <code>string grade_name = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearGradeName() {
      
      gradeName_ = getDefaultInstance().getGradeName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *会员等级名称
     * </pre>
     *
     * <code>string grade_name = 9;</code>
     * @param value The bytes for gradeName to set.
     * @return This builder for chaining.
     */
    public Builder setGradeNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      gradeName_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:coupon.Member)
  }

  // @@protoc_insertion_point(class_scope:coupon.Member)
  private static final cn.hexcloud.pbis.common.service.facade.ticket.Member DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.ticket.Member();
  }

  public static cn.hexcloud.pbis.common.service.facade.ticket.Member getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Member>
      PARSER = new com.google.protobuf.AbstractParser<Member>() {
    @java.lang.Override
    public Member parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Member(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Member> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Member> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Member getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

