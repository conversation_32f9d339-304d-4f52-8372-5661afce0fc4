// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface MemberOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.Member)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string member_code = 1;</code>
   * @return The memberCode.
   */
  java.lang.String getMemberCode();
  /**
   * <code>string member_code = 1;</code>
   * @return The bytes for memberCode.
   */
  com.google.protobuf.ByteString
      getMemberCodeBytes();

  /**
   * <code>string mobile = 2;</code>
   * @return The mobile.
   */
  java.lang.String getMobile();
  /**
   * <code>string mobile = 2;</code>
   * @return The bytes for mobile.
   */
  com.google.protobuf.ByteString
      getMobileBytes();

  /**
   * <code>string name = 3;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <code>string name = 3;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <code>string greetings = 4;</code>
   * @return The greetings.
   */
  java.lang.String getGreetings();
  /**
   * <code>string greetings = 4;</code>
   * @return The bytes for greetings.
   */
  com.google.protobuf.ByteString
      getGreetingsBytes();

  /**
   * <code>int64 balance_points = 5;</code>
   * @return The balancePoints.
   */
  long getBalancePoints();

  /**
   * <code>int64 total_points = 6;</code>
   * @return The totalPoints.
   */
  long getTotalPoints();

  /**
   * <code>int64 order_points = 7;</code>
   * @return The orderPoints.
   */
  long getOrderPoints();

  /**
   * <pre>
   *会员等级编码
   * </pre>
   *
   * <code>string grade_code = 8;</code>
   * @return The gradeCode.
   */
  java.lang.String getGradeCode();
  /**
   * <pre>
   *会员等级编码
   * </pre>
   *
   * <code>string grade_code = 8;</code>
   * @return The bytes for gradeCode.
   */
  com.google.protobuf.ByteString
      getGradeCodeBytes();

  /**
   * <pre>
   *会员等级名称
   * </pre>
   *
   * <code>string grade_name = 9;</code>
   * @return The gradeName.
   */
  java.lang.String getGradeName();
  /**
   * <pre>
   *会员等级名称
   * </pre>
   *
   * <code>string grade_name = 9;</code>
   * @return The bytes for gradeName.
   */
  com.google.protobuf.ByteString
      getGradeNameBytes();
}
