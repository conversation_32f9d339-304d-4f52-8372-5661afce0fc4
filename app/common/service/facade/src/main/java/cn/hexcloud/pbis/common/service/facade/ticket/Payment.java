// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

/**
 * <pre>
 *支付信息
 * </pre>
 *
 * Protobuf type {@code coupon.Payment}
 */
public final class Payment extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:coupon.Payment)
    PaymentOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Payment.newBuilder() to construct.
  private Payment(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Payment() {
    id_ = "";
    seqId_ = "";
    payTime_ = "";
    transCode_ = "";
    name_ = "";
    tpTransactionNo_ = "";
    transName_ = "";
    payerNo_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Payment();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Payment(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            id_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            seqId_ = s;
            break;
          }
          case 25: {

            payAmount_ = input.readDouble();
            break;
          }
          case 33: {

            realPayAmount_ = input.readDouble();
            break;
          }
          case 41: {

            change_ = input.readDouble();
            break;
          }
          case 49: {

            overflow_ = input.readDouble();
            break;
          }
          case 57: {

            rounding_ = input.readDouble();
            break;
          }
          case 66: {
            java.lang.String s = input.readStringRequireUtf8();

            payTime_ = s;
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            transCode_ = s;
            break;
          }
          case 82: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 89: {

            receivable_ = input.readDouble();
            break;
          }
          case 98: {
            java.lang.String s = input.readStringRequireUtf8();

            tpTransactionNo_ = s;
            break;
          }
          case 105: {

            tpAllowance_ = input.readDouble();
            break;
          }
          case 113: {

            merchantAllowance_ = input.readDouble();
            break;
          }
          case 122: {
            java.lang.String s = input.readStringRequireUtf8();

            transName_ = s;
            break;
          }
          case 129: {

            price_ = input.readDouble();
            break;
          }
          case 137: {

            cost_ = input.readDouble();
            break;
          }
          case 145: {

            realAmount_ = input.readDouble();
            break;
          }
          case 152: {

            hasInvoiced_ = input.readBool();
            break;
          }
          case 161: {

            transferAmount_ = input.readDouble();
            break;
          }
          case 169: {

            platformAllowance_ = input.readDouble();
            break;
          }
          case 178: {
            java.lang.String s = input.readStringRequireUtf8();

            payerNo_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Payment_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Payment_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.ticket.Payment.class, cn.hexcloud.pbis.common.service.facade.ticket.Payment.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  private volatile java.lang.Object id_;
  /**
   * <pre>
   *支付id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *支付id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SEQ_ID_FIELD_NUMBER = 2;
  private volatile java.lang.Object seqId_;
  /**
   * <pre>
   *支付顺序号
   * </pre>
   *
   * <code>string seq_id = 2;</code>
   * @return The seqId.
   */
  @java.lang.Override
  public java.lang.String getSeqId() {
    java.lang.Object ref = seqId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      seqId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *支付顺序号
   * </pre>
   *
   * <code>string seq_id = 2;</code>
   * @return The bytes for seqId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSeqIdBytes() {
    java.lang.Object ref = seqId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      seqId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PAY_AMOUNT_FIELD_NUMBER = 3;
  private double payAmount_;
  /**
   * <pre>
   *支付金额
   * </pre>
   *
   * <code>double pay_amount = 3;</code>
   * @return The payAmount.
   */
  @java.lang.Override
  public double getPayAmount() {
    return payAmount_;
  }

  public static final int REALPAYAMOUNT_FIELD_NUMBER = 4;
  private double realPayAmount_;
  /**
   * <pre>
   *字段废弃
   * </pre>
   *
   * <code>double realPayAmount = 4;</code>
   * @return The realPayAmount.
   */
  @java.lang.Override
  public double getRealPayAmount() {
    return realPayAmount_;
  }

  public static final int CHANGE_FIELD_NUMBER = 5;
  private double change_;
  /**
   * <pre>
   *找零
   * </pre>
   *
   * <code>double change = 5;</code>
   * @return The change.
   */
  @java.lang.Override
  public double getChange() {
    return change_;
  }

  public static final int OVERFLOW_FIELD_NUMBER = 6;
  private double overflow_;
  /**
   * <pre>
   *溢收
   * </pre>
   *
   * <code>double overflow = 6;</code>
   * @return The overflow.
   */
  @java.lang.Override
  public double getOverflow() {
    return overflow_;
  }

  public static final int ROUNDING_FIELD_NUMBER = 7;
  private double rounding_;
  /**
   * <pre>
   *rounding
   * </pre>
   *
   * <code>double rounding = 7;</code>
   * @return The rounding.
   */
  @java.lang.Override
  public double getRounding() {
    return rounding_;
  }

  public static final int PAY_TIME_FIELD_NUMBER = 8;
  private volatile java.lang.Object payTime_;
  /**
   * <pre>
   *支付时间，YYYY-MM-DD HH:MM:SS
   * </pre>
   *
   * <code>string pay_time = 8;</code>
   * @return The payTime.
   */
  @java.lang.Override
  public java.lang.String getPayTime() {
    java.lang.Object ref = payTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      payTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *支付时间，YYYY-MM-DD HH:MM:SS
   * </pre>
   *
   * <code>string pay_time = 8;</code>
   * @return The bytes for payTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPayTimeBytes() {
    java.lang.Object ref = payTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      payTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TRANS_CODE_FIELD_NUMBER = 9;
  private volatile java.lang.Object transCode_;
  /**
   * <pre>
   *支付方式编码
   * </pre>
   *
   * <code>string trans_code = 9;</code>
   * @return The transCode.
   */
  @java.lang.Override
  public java.lang.String getTransCode() {
    java.lang.Object ref = transCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      transCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *支付方式编码
   * </pre>
   *
   * <code>string trans_code = 9;</code>
   * @return The bytes for transCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTransCodeBytes() {
    java.lang.Object ref = transCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      transCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 10;
  private volatile java.lang.Object name_;
  /**
   * <pre>
   *支付方式名称
   * </pre>
   *
   * <code>string name = 10;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *支付方式名称
   * </pre>
   *
   * <code>string name = 10;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RECEIVABLE_FIELD_NUMBER = 11;
  private double receivable_;
  /**
   * <pre>
   *实收金额
   * </pre>
   *
   * <code>double receivable = 11;</code>
   * @return The receivable.
   */
  @java.lang.Override
  public double getReceivable() {
    return receivable_;
  }

  public static final int TPTRANSACTIONNO_FIELD_NUMBER = 12;
  private volatile java.lang.Object tpTransactionNo_;
  /**
   * <code>string tpTransactionNo = 12;</code>
   * @return The tpTransactionNo.
   */
  @java.lang.Override
  public java.lang.String getTpTransactionNo() {
    java.lang.Object ref = tpTransactionNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      tpTransactionNo_ = s;
      return s;
    }
  }
  /**
   * <code>string tpTransactionNo = 12;</code>
   * @return The bytes for tpTransactionNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTpTransactionNoBytes() {
    java.lang.Object ref = tpTransactionNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      tpTransactionNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TP_ALLOWANCE_FIELD_NUMBER = 13;
  private double tpAllowance_;
  /**
   * <pre>
   *第三方补贴金额
   * </pre>
   *
   * <code>double tp_allowance = 13;</code>
   * @return The tpAllowance.
   */
  @java.lang.Override
  public double getTpAllowance() {
    return tpAllowance_;
  }

  public static final int MERCHANT_ALLOWANCE_FIELD_NUMBER = 14;
  private double merchantAllowance_;
  /**
   * <pre>
   *商家补贴金额
   * </pre>
   *
   * <code>double merchant_allowance = 14;</code>
   * @return The merchantAllowance.
   */
  @java.lang.Override
  public double getMerchantAllowance() {
    return merchantAllowance_;
  }

  public static final int TRANS_NAME_FIELD_NUMBER = 15;
  private volatile java.lang.Object transName_;
  /**
   * <pre>
   *卡券支付，存储券名称
   * </pre>
   *
   * <code>string trans_name = 15;</code>
   * @return The transName.
   */
  @java.lang.Override
  public java.lang.String getTransName() {
    java.lang.Object ref = transName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      transName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *卡券支付，存储券名称
   * </pre>
   *
   * <code>string trans_name = 15;</code>
   * @return The bytes for transName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTransNameBytes() {
    java.lang.Object ref = transName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      transName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PRICE_FIELD_NUMBER = 16;
  private double price_;
  /**
   * <pre>
   *售价
   * </pre>
   *
   * <code>double price = 16;</code>
   * @return The price.
   */
  @java.lang.Override
  public double getPrice() {
    return price_;
  }

  public static final int COST_FIELD_NUMBER = 17;
  private double cost_;
  /**
   * <pre>
   *用户实际购买金额
   * </pre>
   *
   * <code>double cost = 17;</code>
   * @return The cost.
   */
  @java.lang.Override
  public double getCost() {
    return cost_;
  }

  public static final int REAL_AMOUNT_FIELD_NUMBER = 18;
  private double realAmount_;
  /**
   * <pre>
   *商家实收
   * </pre>
   *
   * <code>double real_amount = 18;</code>
   * @return The realAmount.
   */
  @java.lang.Override
  public double getRealAmount() {
    return realAmount_;
  }

  public static final int HAS_INVOICED_FIELD_NUMBER = 19;
  private boolean hasInvoiced_;
  /**
   * <pre>
   *是否已开发票
   * </pre>
   *
   * <code>bool has_invoiced = 19;</code>
   * @return The hasInvoiced.
   */
  @java.lang.Override
  public boolean getHasInvoiced() {
    return hasInvoiced_;
  }

  public static final int TRANSFER_AMOUNT_FIELD_NUMBER = 20;
  private double transferAmount_;
  /**
   * <pre>
   *支付转折扣金额
   * </pre>
   *
   * <code>double transfer_amount = 20;</code>
   * @return The transferAmount.
   */
  @java.lang.Override
  public double getTransferAmount() {
    return transferAmount_;
  }

  public static final int PLATFORM_ALLOWANCE_FIELD_NUMBER = 21;
  private double platformAllowance_;
  /**
   * <code>double platform_allowance = 21;</code>
   * @return The platformAllowance.
   */
  @java.lang.Override
  public double getPlatformAllowance() {
    return platformAllowance_;
  }

  public static final int PAYERNO_FIELD_NUMBER = 22;
  private volatile java.lang.Object payerNo_;
  /**
   * <pre>
   * 买家ID
   * </pre>
   *
   * <code>string payerNo = 22;</code>
   * @return The payerNo.
   */
  @java.lang.Override
  public java.lang.String getPayerNo() {
    java.lang.Object ref = payerNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      payerNo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 买家ID
   * </pre>
   *
   * <code>string payerNo = 22;</code>
   * @return The bytes for payerNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPayerNoBytes() {
    java.lang.Object ref = payerNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      payerNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!getSeqIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, seqId_);
    }
    if (payAmount_ != 0D) {
      output.writeDouble(3, payAmount_);
    }
    if (realPayAmount_ != 0D) {
      output.writeDouble(4, realPayAmount_);
    }
    if (change_ != 0D) {
      output.writeDouble(5, change_);
    }
    if (overflow_ != 0D) {
      output.writeDouble(6, overflow_);
    }
    if (rounding_ != 0D) {
      output.writeDouble(7, rounding_);
    }
    if (!getPayTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, payTime_);
    }
    if (!getTransCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, transCode_);
    }
    if (!getNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, name_);
    }
    if (receivable_ != 0D) {
      output.writeDouble(11, receivable_);
    }
    if (!getTpTransactionNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 12, tpTransactionNo_);
    }
    if (tpAllowance_ != 0D) {
      output.writeDouble(13, tpAllowance_);
    }
    if (merchantAllowance_ != 0D) {
      output.writeDouble(14, merchantAllowance_);
    }
    if (!getTransNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 15, transName_);
    }
    if (price_ != 0D) {
      output.writeDouble(16, price_);
    }
    if (cost_ != 0D) {
      output.writeDouble(17, cost_);
    }
    if (realAmount_ != 0D) {
      output.writeDouble(18, realAmount_);
    }
    if (hasInvoiced_ != false) {
      output.writeBool(19, hasInvoiced_);
    }
    if (transferAmount_ != 0D) {
      output.writeDouble(20, transferAmount_);
    }
    if (platformAllowance_ != 0D) {
      output.writeDouble(21, platformAllowance_);
    }
    if (!getPayerNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 22, payerNo_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!getSeqIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, seqId_);
    }
    if (payAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(3, payAmount_);
    }
    if (realPayAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(4, realPayAmount_);
    }
    if (change_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(5, change_);
    }
    if (overflow_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(6, overflow_);
    }
    if (rounding_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(7, rounding_);
    }
    if (!getPayTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, payTime_);
    }
    if (!getTransCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, transCode_);
    }
    if (!getNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, name_);
    }
    if (receivable_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(11, receivable_);
    }
    if (!getTpTransactionNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, tpTransactionNo_);
    }
    if (tpAllowance_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(13, tpAllowance_);
    }
    if (merchantAllowance_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(14, merchantAllowance_);
    }
    if (!getTransNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, transName_);
    }
    if (price_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(16, price_);
    }
    if (cost_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(17, cost_);
    }
    if (realAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(18, realAmount_);
    }
    if (hasInvoiced_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(19, hasInvoiced_);
    }
    if (transferAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(20, transferAmount_);
    }
    if (platformAllowance_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(21, platformAllowance_);
    }
    if (!getPayerNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(22, payerNo_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.ticket.Payment)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.ticket.Payment other = (cn.hexcloud.pbis.common.service.facade.ticket.Payment) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getSeqId()
        .equals(other.getSeqId())) return false;
    if (java.lang.Double.doubleToLongBits(getPayAmount())
        != java.lang.Double.doubleToLongBits(
            other.getPayAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getRealPayAmount())
        != java.lang.Double.doubleToLongBits(
            other.getRealPayAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getChange())
        != java.lang.Double.doubleToLongBits(
            other.getChange())) return false;
    if (java.lang.Double.doubleToLongBits(getOverflow())
        != java.lang.Double.doubleToLongBits(
            other.getOverflow())) return false;
    if (java.lang.Double.doubleToLongBits(getRounding())
        != java.lang.Double.doubleToLongBits(
            other.getRounding())) return false;
    if (!getPayTime()
        .equals(other.getPayTime())) return false;
    if (!getTransCode()
        .equals(other.getTransCode())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (java.lang.Double.doubleToLongBits(getReceivable())
        != java.lang.Double.doubleToLongBits(
            other.getReceivable())) return false;
    if (!getTpTransactionNo()
        .equals(other.getTpTransactionNo())) return false;
    if (java.lang.Double.doubleToLongBits(getTpAllowance())
        != java.lang.Double.doubleToLongBits(
            other.getTpAllowance())) return false;
    if (java.lang.Double.doubleToLongBits(getMerchantAllowance())
        != java.lang.Double.doubleToLongBits(
            other.getMerchantAllowance())) return false;
    if (!getTransName()
        .equals(other.getTransName())) return false;
    if (java.lang.Double.doubleToLongBits(getPrice())
        != java.lang.Double.doubleToLongBits(
            other.getPrice())) return false;
    if (java.lang.Double.doubleToLongBits(getCost())
        != java.lang.Double.doubleToLongBits(
            other.getCost())) return false;
    if (java.lang.Double.doubleToLongBits(getRealAmount())
        != java.lang.Double.doubleToLongBits(
            other.getRealAmount())) return false;
    if (getHasInvoiced()
        != other.getHasInvoiced()) return false;
    if (java.lang.Double.doubleToLongBits(getTransferAmount())
        != java.lang.Double.doubleToLongBits(
            other.getTransferAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getPlatformAllowance())
        != java.lang.Double.doubleToLongBits(
            other.getPlatformAllowance())) return false;
    if (!getPayerNo()
        .equals(other.getPayerNo())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + SEQ_ID_FIELD_NUMBER;
    hash = (53 * hash) + getSeqId().hashCode();
    hash = (37 * hash) + PAY_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPayAmount()));
    hash = (37 * hash) + REALPAYAMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getRealPayAmount()));
    hash = (37 * hash) + CHANGE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getChange()));
    hash = (37 * hash) + OVERFLOW_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getOverflow()));
    hash = (37 * hash) + ROUNDING_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getRounding()));
    hash = (37 * hash) + PAY_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getPayTime().hashCode();
    hash = (37 * hash) + TRANS_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getTransCode().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + RECEIVABLE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getReceivable()));
    hash = (37 * hash) + TPTRANSACTIONNO_FIELD_NUMBER;
    hash = (53 * hash) + getTpTransactionNo().hashCode();
    hash = (37 * hash) + TP_ALLOWANCE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getTpAllowance()));
    hash = (37 * hash) + MERCHANT_ALLOWANCE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getMerchantAllowance()));
    hash = (37 * hash) + TRANS_NAME_FIELD_NUMBER;
    hash = (53 * hash) + getTransName().hashCode();
    hash = (37 * hash) + PRICE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPrice()));
    hash = (37 * hash) + COST_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getCost()));
    hash = (37 * hash) + REAL_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getRealAmount()));
    hash = (37 * hash) + HAS_INVOICED_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getHasInvoiced());
    hash = (37 * hash) + TRANSFER_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getTransferAmount()));
    hash = (37 * hash) + PLATFORM_ALLOWANCE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPlatformAllowance()));
    hash = (37 * hash) + PAYERNO_FIELD_NUMBER;
    hash = (53 * hash) + getPayerNo().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.ticket.Payment parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Payment parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Payment parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Payment parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Payment parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Payment parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Payment parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Payment parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Payment parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Payment parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Payment parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Payment parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.ticket.Payment prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *支付信息
   * </pre>
   *
   * Protobuf type {@code coupon.Payment}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:coupon.Payment)
      cn.hexcloud.pbis.common.service.facade.ticket.PaymentOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Payment_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Payment_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.ticket.Payment.class, cn.hexcloud.pbis.common.service.facade.ticket.Payment.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.ticket.Payment.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      id_ = "";

      seqId_ = "";

      payAmount_ = 0D;

      realPayAmount_ = 0D;

      change_ = 0D;

      overflow_ = 0D;

      rounding_ = 0D;

      payTime_ = "";

      transCode_ = "";

      name_ = "";

      receivable_ = 0D;

      tpTransactionNo_ = "";

      tpAllowance_ = 0D;

      merchantAllowance_ = 0D;

      transName_ = "";

      price_ = 0D;

      cost_ = 0D;

      realAmount_ = 0D;

      hasInvoiced_ = false;

      transferAmount_ = 0D;

      platformAllowance_ = 0D;

      payerNo_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Payment_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.Payment getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.ticket.Payment.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.Payment build() {
      cn.hexcloud.pbis.common.service.facade.ticket.Payment result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.Payment buildPartial() {
      cn.hexcloud.pbis.common.service.facade.ticket.Payment result = new cn.hexcloud.pbis.common.service.facade.ticket.Payment(this);
      result.id_ = id_;
      result.seqId_ = seqId_;
      result.payAmount_ = payAmount_;
      result.realPayAmount_ = realPayAmount_;
      result.change_ = change_;
      result.overflow_ = overflow_;
      result.rounding_ = rounding_;
      result.payTime_ = payTime_;
      result.transCode_ = transCode_;
      result.name_ = name_;
      result.receivable_ = receivable_;
      result.tpTransactionNo_ = tpTransactionNo_;
      result.tpAllowance_ = tpAllowance_;
      result.merchantAllowance_ = merchantAllowance_;
      result.transName_ = transName_;
      result.price_ = price_;
      result.cost_ = cost_;
      result.realAmount_ = realAmount_;
      result.hasInvoiced_ = hasInvoiced_;
      result.transferAmount_ = transferAmount_;
      result.platformAllowance_ = platformAllowance_;
      result.payerNo_ = payerNo_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.ticket.Payment) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.ticket.Payment)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.ticket.Payment other) {
      if (other == cn.hexcloud.pbis.common.service.facade.ticket.Payment.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        onChanged();
      }
      if (!other.getSeqId().isEmpty()) {
        seqId_ = other.seqId_;
        onChanged();
      }
      if (other.getPayAmount() != 0D) {
        setPayAmount(other.getPayAmount());
      }
      if (other.getRealPayAmount() != 0D) {
        setRealPayAmount(other.getRealPayAmount());
      }
      if (other.getChange() != 0D) {
        setChange(other.getChange());
      }
      if (other.getOverflow() != 0D) {
        setOverflow(other.getOverflow());
      }
      if (other.getRounding() != 0D) {
        setRounding(other.getRounding());
      }
      if (!other.getPayTime().isEmpty()) {
        payTime_ = other.payTime_;
        onChanged();
      }
      if (!other.getTransCode().isEmpty()) {
        transCode_ = other.transCode_;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (other.getReceivable() != 0D) {
        setReceivable(other.getReceivable());
      }
      if (!other.getTpTransactionNo().isEmpty()) {
        tpTransactionNo_ = other.tpTransactionNo_;
        onChanged();
      }
      if (other.getTpAllowance() != 0D) {
        setTpAllowance(other.getTpAllowance());
      }
      if (other.getMerchantAllowance() != 0D) {
        setMerchantAllowance(other.getMerchantAllowance());
      }
      if (!other.getTransName().isEmpty()) {
        transName_ = other.transName_;
        onChanged();
      }
      if (other.getPrice() != 0D) {
        setPrice(other.getPrice());
      }
      if (other.getCost() != 0D) {
        setCost(other.getCost());
      }
      if (other.getRealAmount() != 0D) {
        setRealAmount(other.getRealAmount());
      }
      if (other.getHasInvoiced() != false) {
        setHasInvoiced(other.getHasInvoiced());
      }
      if (other.getTransferAmount() != 0D) {
        setTransferAmount(other.getTransferAmount());
      }
      if (other.getPlatformAllowance() != 0D) {
        setPlatformAllowance(other.getPlatformAllowance());
      }
      if (!other.getPayerNo().isEmpty()) {
        payerNo_ = other.payerNo_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.ticket.Payment parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.ticket.Payment) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object id_ = "";
    /**
     * <pre>
     *支付id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *支付id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *支付id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *支付id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      
      id_ = getDefaultInstance().getId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *支付id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      id_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object seqId_ = "";
    /**
     * <pre>
     *支付顺序号
     * </pre>
     *
     * <code>string seq_id = 2;</code>
     * @return The seqId.
     */
    public java.lang.String getSeqId() {
      java.lang.Object ref = seqId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        seqId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *支付顺序号
     * </pre>
     *
     * <code>string seq_id = 2;</code>
     * @return The bytes for seqId.
     */
    public com.google.protobuf.ByteString
        getSeqIdBytes() {
      java.lang.Object ref = seqId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        seqId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *支付顺序号
     * </pre>
     *
     * <code>string seq_id = 2;</code>
     * @param value The seqId to set.
     * @return This builder for chaining.
     */
    public Builder setSeqId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      seqId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *支付顺序号
     * </pre>
     *
     * <code>string seq_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSeqId() {
      
      seqId_ = getDefaultInstance().getSeqId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *支付顺序号
     * </pre>
     *
     * <code>string seq_id = 2;</code>
     * @param value The bytes for seqId to set.
     * @return This builder for chaining.
     */
    public Builder setSeqIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      seqId_ = value;
      onChanged();
      return this;
    }

    private double payAmount_ ;
    /**
     * <pre>
     *支付金额
     * </pre>
     *
     * <code>double pay_amount = 3;</code>
     * @return The payAmount.
     */
    @java.lang.Override
    public double getPayAmount() {
      return payAmount_;
    }
    /**
     * <pre>
     *支付金额
     * </pre>
     *
     * <code>double pay_amount = 3;</code>
     * @param value The payAmount to set.
     * @return This builder for chaining.
     */
    public Builder setPayAmount(double value) {
      
      payAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *支付金额
     * </pre>
     *
     * <code>double pay_amount = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayAmount() {
      
      payAmount_ = 0D;
      onChanged();
      return this;
    }

    private double realPayAmount_ ;
    /**
     * <pre>
     *字段废弃
     * </pre>
     *
     * <code>double realPayAmount = 4;</code>
     * @return The realPayAmount.
     */
    @java.lang.Override
    public double getRealPayAmount() {
      return realPayAmount_;
    }
    /**
     * <pre>
     *字段废弃
     * </pre>
     *
     * <code>double realPayAmount = 4;</code>
     * @param value The realPayAmount to set.
     * @return This builder for chaining.
     */
    public Builder setRealPayAmount(double value) {
      
      realPayAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *字段废弃
     * </pre>
     *
     * <code>double realPayAmount = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRealPayAmount() {
      
      realPayAmount_ = 0D;
      onChanged();
      return this;
    }

    private double change_ ;
    /**
     * <pre>
     *找零
     * </pre>
     *
     * <code>double change = 5;</code>
     * @return The change.
     */
    @java.lang.Override
    public double getChange() {
      return change_;
    }
    /**
     * <pre>
     *找零
     * </pre>
     *
     * <code>double change = 5;</code>
     * @param value The change to set.
     * @return This builder for chaining.
     */
    public Builder setChange(double value) {
      
      change_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *找零
     * </pre>
     *
     * <code>double change = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearChange() {
      
      change_ = 0D;
      onChanged();
      return this;
    }

    private double overflow_ ;
    /**
     * <pre>
     *溢收
     * </pre>
     *
     * <code>double overflow = 6;</code>
     * @return The overflow.
     */
    @java.lang.Override
    public double getOverflow() {
      return overflow_;
    }
    /**
     * <pre>
     *溢收
     * </pre>
     *
     * <code>double overflow = 6;</code>
     * @param value The overflow to set.
     * @return This builder for chaining.
     */
    public Builder setOverflow(double value) {
      
      overflow_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *溢收
     * </pre>
     *
     * <code>double overflow = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearOverflow() {
      
      overflow_ = 0D;
      onChanged();
      return this;
    }

    private double rounding_ ;
    /**
     * <pre>
     *rounding
     * </pre>
     *
     * <code>double rounding = 7;</code>
     * @return The rounding.
     */
    @java.lang.Override
    public double getRounding() {
      return rounding_;
    }
    /**
     * <pre>
     *rounding
     * </pre>
     *
     * <code>double rounding = 7;</code>
     * @param value The rounding to set.
     * @return This builder for chaining.
     */
    public Builder setRounding(double value) {
      
      rounding_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *rounding
     * </pre>
     *
     * <code>double rounding = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearRounding() {
      
      rounding_ = 0D;
      onChanged();
      return this;
    }

    private java.lang.Object payTime_ = "";
    /**
     * <pre>
     *支付时间，YYYY-MM-DD HH:MM:SS
     * </pre>
     *
     * <code>string pay_time = 8;</code>
     * @return The payTime.
     */
    public java.lang.String getPayTime() {
      java.lang.Object ref = payTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        payTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *支付时间，YYYY-MM-DD HH:MM:SS
     * </pre>
     *
     * <code>string pay_time = 8;</code>
     * @return The bytes for payTime.
     */
    public com.google.protobuf.ByteString
        getPayTimeBytes() {
      java.lang.Object ref = payTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *支付时间，YYYY-MM-DD HH:MM:SS
     * </pre>
     *
     * <code>string pay_time = 8;</code>
     * @param value The payTime to set.
     * @return This builder for chaining.
     */
    public Builder setPayTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      payTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *支付时间，YYYY-MM-DD HH:MM:SS
     * </pre>
     *
     * <code>string pay_time = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayTime() {
      
      payTime_ = getDefaultInstance().getPayTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *支付时间，YYYY-MM-DD HH:MM:SS
     * </pre>
     *
     * <code>string pay_time = 8;</code>
     * @param value The bytes for payTime to set.
     * @return This builder for chaining.
     */
    public Builder setPayTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      payTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object transCode_ = "";
    /**
     * <pre>
     *支付方式编码
     * </pre>
     *
     * <code>string trans_code = 9;</code>
     * @return The transCode.
     */
    public java.lang.String getTransCode() {
      java.lang.Object ref = transCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        transCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *支付方式编码
     * </pre>
     *
     * <code>string trans_code = 9;</code>
     * @return The bytes for transCode.
     */
    public com.google.protobuf.ByteString
        getTransCodeBytes() {
      java.lang.Object ref = transCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *支付方式编码
     * </pre>
     *
     * <code>string trans_code = 9;</code>
     * @param value The transCode to set.
     * @return This builder for chaining.
     */
    public Builder setTransCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      transCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *支付方式编码
     * </pre>
     *
     * <code>string trans_code = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearTransCode() {
      
      transCode_ = getDefaultInstance().getTransCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *支付方式编码
     * </pre>
     *
     * <code>string trans_code = 9;</code>
     * @param value The bytes for transCode to set.
     * @return This builder for chaining.
     */
    public Builder setTransCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      transCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <pre>
     *支付方式名称
     * </pre>
     *
     * <code>string name = 10;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *支付方式名称
     * </pre>
     *
     * <code>string name = 10;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *支付方式名称
     * </pre>
     *
     * <code>string name = 10;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *支付方式名称
     * </pre>
     *
     * <code>string name = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *支付方式名称
     * </pre>
     *
     * <code>string name = 10;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private double receivable_ ;
    /**
     * <pre>
     *实收金额
     * </pre>
     *
     * <code>double receivable = 11;</code>
     * @return The receivable.
     */
    @java.lang.Override
    public double getReceivable() {
      return receivable_;
    }
    /**
     * <pre>
     *实收金额
     * </pre>
     *
     * <code>double receivable = 11;</code>
     * @param value The receivable to set.
     * @return This builder for chaining.
     */
    public Builder setReceivable(double value) {
      
      receivable_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *实收金额
     * </pre>
     *
     * <code>double receivable = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearReceivable() {
      
      receivable_ = 0D;
      onChanged();
      return this;
    }

    private java.lang.Object tpTransactionNo_ = "";
    /**
     * <code>string tpTransactionNo = 12;</code>
     * @return The tpTransactionNo.
     */
    public java.lang.String getTpTransactionNo() {
      java.lang.Object ref = tpTransactionNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        tpTransactionNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string tpTransactionNo = 12;</code>
     * @return The bytes for tpTransactionNo.
     */
    public com.google.protobuf.ByteString
        getTpTransactionNoBytes() {
      java.lang.Object ref = tpTransactionNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tpTransactionNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string tpTransactionNo = 12;</code>
     * @param value The tpTransactionNo to set.
     * @return This builder for chaining.
     */
    public Builder setTpTransactionNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      tpTransactionNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string tpTransactionNo = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearTpTransactionNo() {
      
      tpTransactionNo_ = getDefaultInstance().getTpTransactionNo();
      onChanged();
      return this;
    }
    /**
     * <code>string tpTransactionNo = 12;</code>
     * @param value The bytes for tpTransactionNo to set.
     * @return This builder for chaining.
     */
    public Builder setTpTransactionNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      tpTransactionNo_ = value;
      onChanged();
      return this;
    }

    private double tpAllowance_ ;
    /**
     * <pre>
     *第三方补贴金额
     * </pre>
     *
     * <code>double tp_allowance = 13;</code>
     * @return The tpAllowance.
     */
    @java.lang.Override
    public double getTpAllowance() {
      return tpAllowance_;
    }
    /**
     * <pre>
     *第三方补贴金额
     * </pre>
     *
     * <code>double tp_allowance = 13;</code>
     * @param value The tpAllowance to set.
     * @return This builder for chaining.
     */
    public Builder setTpAllowance(double value) {
      
      tpAllowance_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *第三方补贴金额
     * </pre>
     *
     * <code>double tp_allowance = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearTpAllowance() {
      
      tpAllowance_ = 0D;
      onChanged();
      return this;
    }

    private double merchantAllowance_ ;
    /**
     * <pre>
     *商家补贴金额
     * </pre>
     *
     * <code>double merchant_allowance = 14;</code>
     * @return The merchantAllowance.
     */
    @java.lang.Override
    public double getMerchantAllowance() {
      return merchantAllowance_;
    }
    /**
     * <pre>
     *商家补贴金额
     * </pre>
     *
     * <code>double merchant_allowance = 14;</code>
     * @param value The merchantAllowance to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantAllowance(double value) {
      
      merchantAllowance_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商家补贴金额
     * </pre>
     *
     * <code>double merchant_allowance = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearMerchantAllowance() {
      
      merchantAllowance_ = 0D;
      onChanged();
      return this;
    }

    private java.lang.Object transName_ = "";
    /**
     * <pre>
     *卡券支付，存储券名称
     * </pre>
     *
     * <code>string trans_name = 15;</code>
     * @return The transName.
     */
    public java.lang.String getTransName() {
      java.lang.Object ref = transName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        transName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *卡券支付，存储券名称
     * </pre>
     *
     * <code>string trans_name = 15;</code>
     * @return The bytes for transName.
     */
    public com.google.protobuf.ByteString
        getTransNameBytes() {
      java.lang.Object ref = transName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *卡券支付，存储券名称
     * </pre>
     *
     * <code>string trans_name = 15;</code>
     * @param value The transName to set.
     * @return This builder for chaining.
     */
    public Builder setTransName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      transName_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *卡券支付，存储券名称
     * </pre>
     *
     * <code>string trans_name = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearTransName() {
      
      transName_ = getDefaultInstance().getTransName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *卡券支付，存储券名称
     * </pre>
     *
     * <code>string trans_name = 15;</code>
     * @param value The bytes for transName to set.
     * @return This builder for chaining.
     */
    public Builder setTransNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      transName_ = value;
      onChanged();
      return this;
    }

    private double price_ ;
    /**
     * <pre>
     *售价
     * </pre>
     *
     * <code>double price = 16;</code>
     * @return The price.
     */
    @java.lang.Override
    public double getPrice() {
      return price_;
    }
    /**
     * <pre>
     *售价
     * </pre>
     *
     * <code>double price = 16;</code>
     * @param value The price to set.
     * @return This builder for chaining.
     */
    public Builder setPrice(double value) {
      
      price_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *售价
     * </pre>
     *
     * <code>double price = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrice() {
      
      price_ = 0D;
      onChanged();
      return this;
    }

    private double cost_ ;
    /**
     * <pre>
     *用户实际购买金额
     * </pre>
     *
     * <code>double cost = 17;</code>
     * @return The cost.
     */
    @java.lang.Override
    public double getCost() {
      return cost_;
    }
    /**
     * <pre>
     *用户实际购买金额
     * </pre>
     *
     * <code>double cost = 17;</code>
     * @param value The cost to set.
     * @return This builder for chaining.
     */
    public Builder setCost(double value) {
      
      cost_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *用户实际购买金额
     * </pre>
     *
     * <code>double cost = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearCost() {
      
      cost_ = 0D;
      onChanged();
      return this;
    }

    private double realAmount_ ;
    /**
     * <pre>
     *商家实收
     * </pre>
     *
     * <code>double real_amount = 18;</code>
     * @return The realAmount.
     */
    @java.lang.Override
    public double getRealAmount() {
      return realAmount_;
    }
    /**
     * <pre>
     *商家实收
     * </pre>
     *
     * <code>double real_amount = 18;</code>
     * @param value The realAmount to set.
     * @return This builder for chaining.
     */
    public Builder setRealAmount(double value) {
      
      realAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商家实收
     * </pre>
     *
     * <code>double real_amount = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearRealAmount() {
      
      realAmount_ = 0D;
      onChanged();
      return this;
    }

    private boolean hasInvoiced_ ;
    /**
     * <pre>
     *是否已开发票
     * </pre>
     *
     * <code>bool has_invoiced = 19;</code>
     * @return The hasInvoiced.
     */
    @java.lang.Override
    public boolean getHasInvoiced() {
      return hasInvoiced_;
    }
    /**
     * <pre>
     *是否已开发票
     * </pre>
     *
     * <code>bool has_invoiced = 19;</code>
     * @param value The hasInvoiced to set.
     * @return This builder for chaining.
     */
    public Builder setHasInvoiced(boolean value) {
      
      hasInvoiced_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *是否已开发票
     * </pre>
     *
     * <code>bool has_invoiced = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearHasInvoiced() {
      
      hasInvoiced_ = false;
      onChanged();
      return this;
    }

    private double transferAmount_ ;
    /**
     * <pre>
     *支付转折扣金额
     * </pre>
     *
     * <code>double transfer_amount = 20;</code>
     * @return The transferAmount.
     */
    @java.lang.Override
    public double getTransferAmount() {
      return transferAmount_;
    }
    /**
     * <pre>
     *支付转折扣金额
     * </pre>
     *
     * <code>double transfer_amount = 20;</code>
     * @param value The transferAmount to set.
     * @return This builder for chaining.
     */
    public Builder setTransferAmount(double value) {
      
      transferAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *支付转折扣金额
     * </pre>
     *
     * <code>double transfer_amount = 20;</code>
     * @return This builder for chaining.
     */
    public Builder clearTransferAmount() {
      
      transferAmount_ = 0D;
      onChanged();
      return this;
    }

    private double platformAllowance_ ;
    /**
     * <code>double platform_allowance = 21;</code>
     * @return The platformAllowance.
     */
    @java.lang.Override
    public double getPlatformAllowance() {
      return platformAllowance_;
    }
    /**
     * <code>double platform_allowance = 21;</code>
     * @param value The platformAllowance to set.
     * @return This builder for chaining.
     */
    public Builder setPlatformAllowance(double value) {
      
      platformAllowance_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double platform_allowance = 21;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlatformAllowance() {
      
      platformAllowance_ = 0D;
      onChanged();
      return this;
    }

    private java.lang.Object payerNo_ = "";
    /**
     * <pre>
     * 买家ID
     * </pre>
     *
     * <code>string payerNo = 22;</code>
     * @return The payerNo.
     */
    public java.lang.String getPayerNo() {
      java.lang.Object ref = payerNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        payerNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 买家ID
     * </pre>
     *
     * <code>string payerNo = 22;</code>
     * @return The bytes for payerNo.
     */
    public com.google.protobuf.ByteString
        getPayerNoBytes() {
      java.lang.Object ref = payerNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payerNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 买家ID
     * </pre>
     *
     * <code>string payerNo = 22;</code>
     * @param value The payerNo to set.
     * @return This builder for chaining.
     */
    public Builder setPayerNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      payerNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 买家ID
     * </pre>
     *
     * <code>string payerNo = 22;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayerNo() {
      
      payerNo_ = getDefaultInstance().getPayerNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 买家ID
     * </pre>
     *
     * <code>string payerNo = 22;</code>
     * @param value The bytes for payerNo to set.
     * @return This builder for chaining.
     */
    public Builder setPayerNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      payerNo_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:coupon.Payment)
  }

  // @@protoc_insertion_point(class_scope:coupon.Payment)
  private static final cn.hexcloud.pbis.common.service.facade.ticket.Payment DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.ticket.Payment();
  }

  public static cn.hexcloud.pbis.common.service.facade.ticket.Payment getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Payment>
      PARSER = new com.google.protobuf.AbstractParser<Payment>() {
    @java.lang.Override
    public Payment parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Payment(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Payment> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Payment> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Payment getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

