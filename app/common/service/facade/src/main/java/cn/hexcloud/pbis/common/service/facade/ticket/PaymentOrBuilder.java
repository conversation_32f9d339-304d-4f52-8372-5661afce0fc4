// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface PaymentOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.Payment)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *支付id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   *支付id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   *支付顺序号
   * </pre>
   *
   * <code>string seq_id = 2;</code>
   * @return The seqId.
   */
  java.lang.String getSeqId();
  /**
   * <pre>
   *支付顺序号
   * </pre>
   *
   * <code>string seq_id = 2;</code>
   * @return The bytes for seqId.
   */
  com.google.protobuf.ByteString
      getSeqIdBytes();

  /**
   * <pre>
   *支付金额
   * </pre>
   *
   * <code>double pay_amount = 3;</code>
   * @return The payAmount.
   */
  double getPayAmount();

  /**
   * <pre>
   *字段废弃
   * </pre>
   *
   * <code>double realPayAmount = 4;</code>
   * @return The realPayAmount.
   */
  double getRealPayAmount();

  /**
   * <pre>
   *找零
   * </pre>
   *
   * <code>double change = 5;</code>
   * @return The change.
   */
  double getChange();

  /**
   * <pre>
   *溢收
   * </pre>
   *
   * <code>double overflow = 6;</code>
   * @return The overflow.
   */
  double getOverflow();

  /**
   * <pre>
   *rounding
   * </pre>
   *
   * <code>double rounding = 7;</code>
   * @return The rounding.
   */
  double getRounding();

  /**
   * <pre>
   *支付时间，YYYY-MM-DD HH:MM:SS
   * </pre>
   *
   * <code>string pay_time = 8;</code>
   * @return The payTime.
   */
  java.lang.String getPayTime();
  /**
   * <pre>
   *支付时间，YYYY-MM-DD HH:MM:SS
   * </pre>
   *
   * <code>string pay_time = 8;</code>
   * @return The bytes for payTime.
   */
  com.google.protobuf.ByteString
      getPayTimeBytes();

  /**
   * <pre>
   *支付方式编码
   * </pre>
   *
   * <code>string trans_code = 9;</code>
   * @return The transCode.
   */
  java.lang.String getTransCode();
  /**
   * <pre>
   *支付方式编码
   * </pre>
   *
   * <code>string trans_code = 9;</code>
   * @return The bytes for transCode.
   */
  com.google.protobuf.ByteString
      getTransCodeBytes();

  /**
   * <pre>
   *支付方式名称
   * </pre>
   *
   * <code>string name = 10;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   *支付方式名称
   * </pre>
   *
   * <code>string name = 10;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   *实收金额
   * </pre>
   *
   * <code>double receivable = 11;</code>
   * @return The receivable.
   */
  double getReceivable();

  /**
   * <code>string tpTransactionNo = 12;</code>
   * @return The tpTransactionNo.
   */
  java.lang.String getTpTransactionNo();
  /**
   * <code>string tpTransactionNo = 12;</code>
   * @return The bytes for tpTransactionNo.
   */
  com.google.protobuf.ByteString
      getTpTransactionNoBytes();

  /**
   * <pre>
   *第三方补贴金额
   * </pre>
   *
   * <code>double tp_allowance = 13;</code>
   * @return The tpAllowance.
   */
  double getTpAllowance();

  /**
   * <pre>
   *商家补贴金额
   * </pre>
   *
   * <code>double merchant_allowance = 14;</code>
   * @return The merchantAllowance.
   */
  double getMerchantAllowance();

  /**
   * <pre>
   *卡券支付，存储券名称
   * </pre>
   *
   * <code>string trans_name = 15;</code>
   * @return The transName.
   */
  java.lang.String getTransName();
  /**
   * <pre>
   *卡券支付，存储券名称
   * </pre>
   *
   * <code>string trans_name = 15;</code>
   * @return The bytes for transName.
   */
  com.google.protobuf.ByteString
      getTransNameBytes();

  /**
   * <pre>
   *售价
   * </pre>
   *
   * <code>double price = 16;</code>
   * @return The price.
   */
  double getPrice();

  /**
   * <pre>
   *用户实际购买金额
   * </pre>
   *
   * <code>double cost = 17;</code>
   * @return The cost.
   */
  double getCost();

  /**
   * <pre>
   *商家实收
   * </pre>
   *
   * <code>double real_amount = 18;</code>
   * @return The realAmount.
   */
  double getRealAmount();

  /**
   * <pre>
   *是否已开发票
   * </pre>
   *
   * <code>bool has_invoiced = 19;</code>
   * @return The hasInvoiced.
   */
  boolean getHasInvoiced();

  /**
   * <pre>
   *支付转折扣金额
   * </pre>
   *
   * <code>double transfer_amount = 20;</code>
   * @return The transferAmount.
   */
  double getTransferAmount();

  /**
   * <code>double platform_allowance = 21;</code>
   * @return The platformAllowance.
   */
  double getPlatformAllowance();

  /**
   * <pre>
   * 买家ID
   * </pre>
   *
   * <code>string payerNo = 22;</code>
   * @return The payerNo.
   */
  java.lang.String getPayerNo();
  /**
   * <pre>
   * 买家ID
   * </pre>
   *
   * <code>string payerNo = 22;</code>
   * @return The bytes for payerNo.
   */
  com.google.protobuf.ByteString
      getPayerNoBytes();
}
