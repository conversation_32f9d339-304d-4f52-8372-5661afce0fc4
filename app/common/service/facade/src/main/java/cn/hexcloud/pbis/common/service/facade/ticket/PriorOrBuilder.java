// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface PriorOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.Prior)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string code = 1;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <code>string code = 1;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <code>string reason = 2;</code>
   * @return The reason.
   */
  java.lang.String getReason();
  /**
   * <code>string reason = 2;</code>
   * @return The bytes for reason.
   */
  com.google.protobuf.ByteString
      getReasonBytes();

  /**
   * <code>bool isPrior = 3;</code>
   * @return The isPrior.
   */
  boolean getIsPrior();
}
