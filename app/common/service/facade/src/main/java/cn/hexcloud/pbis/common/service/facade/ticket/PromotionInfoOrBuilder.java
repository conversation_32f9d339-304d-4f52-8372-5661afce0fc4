// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface PromotionInfoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.PromotionInfo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string type = 1;</code>
   * @return The type.
   */
  java.lang.String getType();
  /**
   * <code>string type = 1;</code>
   * @return The bytes for type.
   */
  com.google.protobuf.ByteString
      getTypeBytes();

  /**
   * <code>string discount_type = 2;</code>
   * @return The discountType.
   */
  java.lang.String getDiscountType();
  /**
   * <code>string discount_type = 2;</code>
   * @return The bytes for discountType.
   */
  com.google.protobuf.ByteString
      getDiscountTypeBytes();

  /**
   * <code>string name = 3;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <code>string name = 3;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <code>string promotion_id = 4;</code>
   * @return The promotionId.
   */
  java.lang.String getPromotionId();
  /**
   * <code>string promotion_id = 4;</code>
   * @return The bytes for promotionId.
   */
  com.google.protobuf.ByteString
      getPromotionIdBytes();

  /**
   * <code>string promotion_code = 5;</code>
   * @return The promotionCode.
   */
  java.lang.String getPromotionCode();
  /**
   * <code>string promotion_code = 5;</code>
   * @return The bytes for promotionCode.
   */
  com.google.protobuf.ByteString
      getPromotionCodeBytes();

  /**
   * <code>string promotion_type = 6;</code>
   * @return The promotionType.
   */
  java.lang.String getPromotionType();
  /**
   * <code>string promotion_type = 6;</code>
   * @return The bytes for promotionType.
   */
  com.google.protobuf.ByteString
      getPromotionTypeBytes();

  /**
   * <code>bool allow_overlap = 7;</code>
   * @return The allowOverlap.
   */
  boolean getAllowOverlap();

  /**
   * <code>bool trigger_times_custom = 8;</code>
   * @return The triggerTimesCustom.
   */
  boolean getTriggerTimesCustom();

  /**
   * <code>string ticket_display = 9;</code>
   * @return The ticketDisplay.
   */
  java.lang.String getTicketDisplay();
  /**
   * <code>string ticket_display = 9;</code>
   * @return The bytes for ticketDisplay.
   */
  com.google.protobuf.ByteString
      getTicketDisplayBytes();

  /**
   * <code>double max_discount = 10;</code>
   * @return The maxDiscount.
   */
  double getMaxDiscount();
}
