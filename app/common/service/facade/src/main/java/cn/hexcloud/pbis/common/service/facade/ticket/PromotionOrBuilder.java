// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface PromotionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.Promotion)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>.coupon.PromotionInfo promotionInfo = 1;</code>
   * @return Whether the promotionInfo field is set.
   */
  boolean hasPromotionInfo();
  /**
   * <code>.coupon.PromotionInfo promotionInfo = 1;</code>
   * @return The promotionInfo.
   */
  cn.hexcloud.pbis.common.service.facade.ticket.PromotionInfo getPromotionInfo();
  /**
   * <code>.coupon.PromotionInfo promotionInfo = 1;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.PromotionInfoOrBuilder getPromotionInfoOrBuilder();

  /**
   * <code>.coupon.PromotionSource source = 2;</code>
   * @return Whether the source field is set.
   */
  boolean hasSource();
  /**
   * <code>.coupon.PromotionSource source = 2;</code>
   * @return The source.
   */
  cn.hexcloud.pbis.common.service.facade.ticket.PromotionSource getSource();
  /**
   * <code>.coupon.PromotionSource source = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.PromotionSourceOrBuilder getSourceOrBuilder();

  /**
   * <code>repeated .coupon.PromotionProduct products = 3;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.PromotionProduct> 
      getProductsList();
  /**
   * <code>repeated .coupon.PromotionProduct products = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.PromotionProduct getProducts(int index);
  /**
   * <code>repeated .coupon.PromotionProduct products = 3;</code>
   */
  int getProductsCount();
  /**
   * <code>repeated .coupon.PromotionProduct products = 3;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.PromotionProductOrBuilder> 
      getProductsOrBuilderList();
  /**
   * <code>repeated .coupon.PromotionProduct products = 3;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.PromotionProductOrBuilder getProductsOrBuilder(
      int index);
}
