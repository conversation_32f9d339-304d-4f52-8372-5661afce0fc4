// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface PromotionProductOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.PromotionProduct)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>double price = 1;</code>
   * @return The price.
   */
  double getPrice();

  /**
   * <code>double amt = 2;</code>
   * @return The amt.
   */
  double getAmt();

  /**
   * <code>double accAmt = 3;</code>
   * @return The accAmt.
   */
  double getAccAmt();

  /**
   * <code>int32 qty = 4;</code>
   * @return The qty.
   */
  int getQty();

  /**
   * <code>string key_id = 5;</code>
   * @return The keyId.
   */
  java.lang.String getKeyId();
  /**
   * <code>string key_id = 5;</code>
   * @return The bytes for keyId.
   */
  com.google.protobuf.ByteString
      getKeyIdBytes();

  /**
   * <code>repeated string accies = 6;</code>
   * @return A list containing the accies.
   */
  java.util.List<java.lang.String>
      getAcciesList();
  /**
   * <code>repeated string accies = 6;</code>
   * @return The count of accies.
   */
  int getAcciesCount();
  /**
   * <code>repeated string accies = 6;</code>
   * @param index The index of the element to return.
   * @return The accies at the given index.
   */
  java.lang.String getAccies(int index);
  /**
   * <code>repeated string accies = 6;</code>
   * @param index The index of the value to return.
   * @return The bytes of the accies at the given index.
   */
  com.google.protobuf.ByteString
      getAcciesBytes(int index);

  /**
   * <code>string type = 7;</code>
   * @return The type.
   */
  java.lang.String getType();
  /**
   * <code>string type = 7;</code>
   * @return The bytes for type.
   */
  com.google.protobuf.ByteString
      getTypeBytes();

  /**
   * <code>double discount = 8;</code>
   * @return The discount.
   */
  double getDiscount();

  /**
   * <code>double free_amt = 9;</code>
   * @return The freeAmt.
   */
  double getFreeAmt();

  /**
   * <code>string method = 10;</code>
   * @return The method.
   */
  java.lang.String getMethod();
  /**
   * <code>string method = 10;</code>
   * @return The bytes for method.
   */
  com.google.protobuf.ByteString
      getMethodBytes();

  /**
   * <code>repeated .coupon.PromotionProduct accessories = 11;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.PromotionProduct> 
      getAccessoriesList();
  /**
   * <code>repeated .coupon.PromotionProduct accessories = 11;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.PromotionProduct getAccessories(int index);
  /**
   * <code>repeated .coupon.PromotionProduct accessories = 11;</code>
   */
  int getAccessoriesCount();
  /**
   * <code>repeated .coupon.PromotionProduct accessories = 11;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.PromotionProductOrBuilder> 
      getAccessoriesOrBuilderList();
  /**
   * <code>repeated .coupon.PromotionProduct accessories = 11;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.PromotionProductOrBuilder getAccessoriesOrBuilder(
      int index);

  /**
   * <pre>
   *重量(kg)
   * </pre>
   *
   * <code>float weight = 12;</code>
   * @return The weight.
   */
  float getWeight();

  /**
   * <pre>
   *是否称重商品
   * </pre>
   *
   * <code>bool has_weight = 13;</code>
   * @return The hasWeight.
   */
  boolean getHasWeight();
}
