// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface PromotionSourceOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.PromotionSource)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>int32 trigger = 1;</code>
   * @return The trigger.
   */
  int getTrigger();

  /**
   * <code>double discount = 2;</code>
   * @return The discount.
   */
  double getDiscount();

  /**
   * <code>repeated string fired = 3;</code>
   * @return A list containing the fired.
   */
  java.util.List<java.lang.String>
      getFiredList();
  /**
   * <code>repeated string fired = 3;</code>
   * @return The count of fired.
   */
  int getFiredCount();
  /**
   * <code>repeated string fired = 3;</code>
   * @param index The index of the element to return.
   * @return The fired at the given index.
   */
  java.lang.String getFired(int index);
  /**
   * <code>repeated string fired = 3;</code>
   * @param index The index of the value to return.
   * @return The bytes of the fired at the given index.
   */
  com.google.protobuf.ByteString
      getFiredBytes(int index);

  /**
   * <code>double merchant_discount = 4;</code>
   * @return The merchantDiscount.
   */
  double getMerchantDiscount();

  /**
   * <code>double platform_discount = 5;</code>
   * @return The platformDiscount.
   */
  double getPlatformDiscount();

  /**
   * <code>double store_discount = 6;</code>
   * @return The storeDiscount.
   */
  double getStoreDiscount();

  /**
   * <code>double cost = 7;</code>
   * @return The cost.
   */
  double getCost();

  /**
   * <code>double tp_allowance = 8;</code>
   * @return The tpAllowance.
   */
  double getTpAllowance();

  /**
   * <code>double merchant_allowance = 9;</code>
   * @return The merchantAllowance.
   */
  double getMerchantAllowance();

  /**
   * <code>double platform_allowance = 10;</code>
   * @return The platformAllowance.
   */
  double getPlatformAllowance();

  /**
   * <code>double real_amount = 11;</code>
   * @return The realAmount.
   */
  double getRealAmount();

  /**
   * <code>double transfer_amount = 12;</code>
   * @return The transferAmount.
   */
  double getTransferAmount();
}
