// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

/**
 * Protobuf type {@code coupon.RefundInfo}
 */
public final class RefundInfo extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:coupon.RefundInfo)
    RefundInfoOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RefundInfo.newBuilder() to construct.
  private RefundInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RefundInfo() {
    refundId_ = "";
    refTicketId_ = "";
    refTicketNo_ = "";
    refundReason_ = "";
    refundNo_ = "";
    refundSide_ = "";
    refundCode_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new RefundInfo();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private RefundInfo(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            refundId_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            refTicketId_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            refTicketNo_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            refundReason_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            refundNo_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            refundSide_ = s;
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            refundCode_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_RefundInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_RefundInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.class, cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.Builder.class);
  }

  public static final int REFUND_ID_FIELD_NUMBER = 1;
  private volatile java.lang.Object refundId_;
  /**
   * <pre>
   * 退单负单的ticketId，
   * </pre>
   *
   * <code>string refund_id = 1;</code>
   * @return The refundId.
   */
  @java.lang.Override
  public java.lang.String getRefundId() {
    java.lang.Object ref = refundId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      refundId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 退单负单的ticketId，
   * </pre>
   *
   * <code>string refund_id = 1;</code>
   * @return The bytes for refundId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRefundIdBytes() {
    java.lang.Object ref = refundId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      refundId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REF_TICKET_ID_FIELD_NUMBER = 2;
  private volatile java.lang.Object refTicketId_;
  /**
   * <pre>
   *退单正单的ticketId
   * </pre>
   *
   * <code>string ref_ticket_id = 2;</code>
   * @return The refTicketId.
   */
  @java.lang.Override
  public java.lang.String getRefTicketId() {
    java.lang.Object ref = refTicketId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      refTicketId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *退单正单的ticketId
   * </pre>
   *
   * <code>string ref_ticket_id = 2;</code>
   * @return The bytes for refTicketId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRefTicketIdBytes() {
    java.lang.Object ref = refTicketId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      refTicketId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REF_TICKET_NO_FIELD_NUMBER = 3;
  private volatile java.lang.Object refTicketNo_;
  /**
   * <pre>
   *退单正单的ticketNo
   * </pre>
   *
   * <code>string ref_ticket_no = 3;</code>
   * @return The refTicketNo.
   */
  @java.lang.Override
  public java.lang.String getRefTicketNo() {
    java.lang.Object ref = refTicketNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      refTicketNo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *退单正单的ticketNo
   * </pre>
   *
   * <code>string ref_ticket_no = 3;</code>
   * @return The bytes for refTicketNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRefTicketNoBytes() {
    java.lang.Object ref = refTicketNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      refTicketNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REFUND_REASON_FIELD_NUMBER = 4;
  private volatile java.lang.Object refundReason_;
  /**
   * <pre>
   *退单原因
   * </pre>
   *
   * <code>string refund_reason = 4;</code>
   * @return The refundReason.
   */
  @java.lang.Override
  public java.lang.String getRefundReason() {
    java.lang.Object ref = refundReason_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      refundReason_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *退单原因
   * </pre>
   *
   * <code>string refund_reason = 4;</code>
   * @return The bytes for refundReason.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRefundReasonBytes() {
    java.lang.Object ref = refundReason_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      refundReason_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REFUND_NO_FIELD_NUMBER = 5;
  private volatile java.lang.Object refundNo_;
  /**
   * <pre>
   * 退单负单的orderId，
   * </pre>
   *
   * <code>string refund_no = 5;</code>
   * @return The refundNo.
   */
  @java.lang.Override
  public java.lang.String getRefundNo() {
    java.lang.Object ref = refundNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      refundNo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 退单负单的orderId，
   * </pre>
   *
   * <code>string refund_no = 5;</code>
   * @return The bytes for refundNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRefundNoBytes() {
    java.lang.Object ref = refundNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      refundNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REFUND_SIDE_FIELD_NUMBER = 6;
  private volatile java.lang.Object refundSide_;
  /**
   * <pre>
   *退单方
   * </pre>
   *
   * <code>string refund_side = 6;</code>
   * @return The refundSide.
   */
  @java.lang.Override
  public java.lang.String getRefundSide() {
    java.lang.Object ref = refundSide_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      refundSide_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *退单方
   * </pre>
   *
   * <code>string refund_side = 6;</code>
   * @return The bytes for refundSide.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRefundSideBytes() {
    java.lang.Object ref = refundSide_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      refundSide_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REFUND_CODE_FIELD_NUMBER = 7;
  private volatile java.lang.Object refundCode_;
  /**
   * <code>string refund_code = 7;</code>
   * @return The refundCode.
   */
  @java.lang.Override
  public java.lang.String getRefundCode() {
    java.lang.Object ref = refundCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      refundCode_ = s;
      return s;
    }
  }
  /**
   * <code>string refund_code = 7;</code>
   * @return The bytes for refundCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRefundCodeBytes() {
    java.lang.Object ref = refundCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      refundCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getRefundIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, refundId_);
    }
    if (!getRefTicketIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, refTicketId_);
    }
    if (!getRefTicketNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, refTicketNo_);
    }
    if (!getRefundReasonBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, refundReason_);
    }
    if (!getRefundNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, refundNo_);
    }
    if (!getRefundSideBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, refundSide_);
    }
    if (!getRefundCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, refundCode_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getRefundIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, refundId_);
    }
    if (!getRefTicketIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, refTicketId_);
    }
    if (!getRefTicketNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, refTicketNo_);
    }
    if (!getRefundReasonBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, refundReason_);
    }
    if (!getRefundNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, refundNo_);
    }
    if (!getRefundSideBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, refundSide_);
    }
    if (!getRefundCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, refundCode_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo other = (cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo) obj;

    if (!getRefundId()
        .equals(other.getRefundId())) return false;
    if (!getRefTicketId()
        .equals(other.getRefTicketId())) return false;
    if (!getRefTicketNo()
        .equals(other.getRefTicketNo())) return false;
    if (!getRefundReason()
        .equals(other.getRefundReason())) return false;
    if (!getRefundNo()
        .equals(other.getRefundNo())) return false;
    if (!getRefundSide()
        .equals(other.getRefundSide())) return false;
    if (!getRefundCode()
        .equals(other.getRefundCode())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + REFUND_ID_FIELD_NUMBER;
    hash = (53 * hash) + getRefundId().hashCode();
    hash = (37 * hash) + REF_TICKET_ID_FIELD_NUMBER;
    hash = (53 * hash) + getRefTicketId().hashCode();
    hash = (37 * hash) + REF_TICKET_NO_FIELD_NUMBER;
    hash = (53 * hash) + getRefTicketNo().hashCode();
    hash = (37 * hash) + REFUND_REASON_FIELD_NUMBER;
    hash = (53 * hash) + getRefundReason().hashCode();
    hash = (37 * hash) + REFUND_NO_FIELD_NUMBER;
    hash = (53 * hash) + getRefundNo().hashCode();
    hash = (37 * hash) + REFUND_SIDE_FIELD_NUMBER;
    hash = (53 * hash) + getRefundSide().hashCode();
    hash = (37 * hash) + REFUND_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getRefundCode().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code coupon.RefundInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:coupon.RefundInfo)
      cn.hexcloud.pbis.common.service.facade.ticket.RefundInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_RefundInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_RefundInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.class, cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      refundId_ = "";

      refTicketId_ = "";

      refTicketNo_ = "";

      refundReason_ = "";

      refundNo_ = "";

      refundSide_ = "";

      refundCode_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_RefundInfo_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo build() {
      cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo buildPartial() {
      cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo result = new cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo(this);
      result.refundId_ = refundId_;
      result.refTicketId_ = refTicketId_;
      result.refTicketNo_ = refTicketNo_;
      result.refundReason_ = refundReason_;
      result.refundNo_ = refundNo_;
      result.refundSide_ = refundSide_;
      result.refundCode_ = refundCode_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo other) {
      if (other == cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.getDefaultInstance()) return this;
      if (!other.getRefundId().isEmpty()) {
        refundId_ = other.refundId_;
        onChanged();
      }
      if (!other.getRefTicketId().isEmpty()) {
        refTicketId_ = other.refTicketId_;
        onChanged();
      }
      if (!other.getRefTicketNo().isEmpty()) {
        refTicketNo_ = other.refTicketNo_;
        onChanged();
      }
      if (!other.getRefundReason().isEmpty()) {
        refundReason_ = other.refundReason_;
        onChanged();
      }
      if (!other.getRefundNo().isEmpty()) {
        refundNo_ = other.refundNo_;
        onChanged();
      }
      if (!other.getRefundSide().isEmpty()) {
        refundSide_ = other.refundSide_;
        onChanged();
      }
      if (!other.getRefundCode().isEmpty()) {
        refundCode_ = other.refundCode_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object refundId_ = "";
    /**
     * <pre>
     * 退单负单的ticketId，
     * </pre>
     *
     * <code>string refund_id = 1;</code>
     * @return The refundId.
     */
    public java.lang.String getRefundId() {
      java.lang.Object ref = refundId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        refundId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 退单负单的ticketId，
     * </pre>
     *
     * <code>string refund_id = 1;</code>
     * @return The bytes for refundId.
     */
    public com.google.protobuf.ByteString
        getRefundIdBytes() {
      java.lang.Object ref = refundId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        refundId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 退单负单的ticketId，
     * </pre>
     *
     * <code>string refund_id = 1;</code>
     * @param value The refundId to set.
     * @return This builder for chaining.
     */
    public Builder setRefundId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      refundId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 退单负单的ticketId，
     * </pre>
     *
     * <code>string refund_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRefundId() {
      
      refundId_ = getDefaultInstance().getRefundId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 退单负单的ticketId，
     * </pre>
     *
     * <code>string refund_id = 1;</code>
     * @param value The bytes for refundId to set.
     * @return This builder for chaining.
     */
    public Builder setRefundIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      refundId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object refTicketId_ = "";
    /**
     * <pre>
     *退单正单的ticketId
     * </pre>
     *
     * <code>string ref_ticket_id = 2;</code>
     * @return The refTicketId.
     */
    public java.lang.String getRefTicketId() {
      java.lang.Object ref = refTicketId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        refTicketId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *退单正单的ticketId
     * </pre>
     *
     * <code>string ref_ticket_id = 2;</code>
     * @return The bytes for refTicketId.
     */
    public com.google.protobuf.ByteString
        getRefTicketIdBytes() {
      java.lang.Object ref = refTicketId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        refTicketId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *退单正单的ticketId
     * </pre>
     *
     * <code>string ref_ticket_id = 2;</code>
     * @param value The refTicketId to set.
     * @return This builder for chaining.
     */
    public Builder setRefTicketId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      refTicketId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *退单正单的ticketId
     * </pre>
     *
     * <code>string ref_ticket_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearRefTicketId() {
      
      refTicketId_ = getDefaultInstance().getRefTicketId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *退单正单的ticketId
     * </pre>
     *
     * <code>string ref_ticket_id = 2;</code>
     * @param value The bytes for refTicketId to set.
     * @return This builder for chaining.
     */
    public Builder setRefTicketIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      refTicketId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object refTicketNo_ = "";
    /**
     * <pre>
     *退单正单的ticketNo
     * </pre>
     *
     * <code>string ref_ticket_no = 3;</code>
     * @return The refTicketNo.
     */
    public java.lang.String getRefTicketNo() {
      java.lang.Object ref = refTicketNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        refTicketNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *退单正单的ticketNo
     * </pre>
     *
     * <code>string ref_ticket_no = 3;</code>
     * @return The bytes for refTicketNo.
     */
    public com.google.protobuf.ByteString
        getRefTicketNoBytes() {
      java.lang.Object ref = refTicketNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        refTicketNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *退单正单的ticketNo
     * </pre>
     *
     * <code>string ref_ticket_no = 3;</code>
     * @param value The refTicketNo to set.
     * @return This builder for chaining.
     */
    public Builder setRefTicketNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      refTicketNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *退单正单的ticketNo
     * </pre>
     *
     * <code>string ref_ticket_no = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearRefTicketNo() {
      
      refTicketNo_ = getDefaultInstance().getRefTicketNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *退单正单的ticketNo
     * </pre>
     *
     * <code>string ref_ticket_no = 3;</code>
     * @param value The bytes for refTicketNo to set.
     * @return This builder for chaining.
     */
    public Builder setRefTicketNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      refTicketNo_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object refundReason_ = "";
    /**
     * <pre>
     *退单原因
     * </pre>
     *
     * <code>string refund_reason = 4;</code>
     * @return The refundReason.
     */
    public java.lang.String getRefundReason() {
      java.lang.Object ref = refundReason_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        refundReason_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *退单原因
     * </pre>
     *
     * <code>string refund_reason = 4;</code>
     * @return The bytes for refundReason.
     */
    public com.google.protobuf.ByteString
        getRefundReasonBytes() {
      java.lang.Object ref = refundReason_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        refundReason_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *退单原因
     * </pre>
     *
     * <code>string refund_reason = 4;</code>
     * @param value The refundReason to set.
     * @return This builder for chaining.
     */
    public Builder setRefundReason(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      refundReason_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *退单原因
     * </pre>
     *
     * <code>string refund_reason = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearRefundReason() {
      
      refundReason_ = getDefaultInstance().getRefundReason();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *退单原因
     * </pre>
     *
     * <code>string refund_reason = 4;</code>
     * @param value The bytes for refundReason to set.
     * @return This builder for chaining.
     */
    public Builder setRefundReasonBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      refundReason_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object refundNo_ = "";
    /**
     * <pre>
     * 退单负单的orderId，
     * </pre>
     *
     * <code>string refund_no = 5;</code>
     * @return The refundNo.
     */
    public java.lang.String getRefundNo() {
      java.lang.Object ref = refundNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        refundNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 退单负单的orderId，
     * </pre>
     *
     * <code>string refund_no = 5;</code>
     * @return The bytes for refundNo.
     */
    public com.google.protobuf.ByteString
        getRefundNoBytes() {
      java.lang.Object ref = refundNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        refundNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 退单负单的orderId，
     * </pre>
     *
     * <code>string refund_no = 5;</code>
     * @param value The refundNo to set.
     * @return This builder for chaining.
     */
    public Builder setRefundNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      refundNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 退单负单的orderId，
     * </pre>
     *
     * <code>string refund_no = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearRefundNo() {
      
      refundNo_ = getDefaultInstance().getRefundNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 退单负单的orderId，
     * </pre>
     *
     * <code>string refund_no = 5;</code>
     * @param value The bytes for refundNo to set.
     * @return This builder for chaining.
     */
    public Builder setRefundNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      refundNo_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object refundSide_ = "";
    /**
     * <pre>
     *退单方
     * </pre>
     *
     * <code>string refund_side = 6;</code>
     * @return The refundSide.
     */
    public java.lang.String getRefundSide() {
      java.lang.Object ref = refundSide_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        refundSide_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *退单方
     * </pre>
     *
     * <code>string refund_side = 6;</code>
     * @return The bytes for refundSide.
     */
    public com.google.protobuf.ByteString
        getRefundSideBytes() {
      java.lang.Object ref = refundSide_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        refundSide_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *退单方
     * </pre>
     *
     * <code>string refund_side = 6;</code>
     * @param value The refundSide to set.
     * @return This builder for chaining.
     */
    public Builder setRefundSide(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      refundSide_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *退单方
     * </pre>
     *
     * <code>string refund_side = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearRefundSide() {
      
      refundSide_ = getDefaultInstance().getRefundSide();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *退单方
     * </pre>
     *
     * <code>string refund_side = 6;</code>
     * @param value The bytes for refundSide to set.
     * @return This builder for chaining.
     */
    public Builder setRefundSideBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      refundSide_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object refundCode_ = "";
    /**
     * <code>string refund_code = 7;</code>
     * @return The refundCode.
     */
    public java.lang.String getRefundCode() {
      java.lang.Object ref = refundCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        refundCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string refund_code = 7;</code>
     * @return The bytes for refundCode.
     */
    public com.google.protobuf.ByteString
        getRefundCodeBytes() {
      java.lang.Object ref = refundCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        refundCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string refund_code = 7;</code>
     * @param value The refundCode to set.
     * @return This builder for chaining.
     */
    public Builder setRefundCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      refundCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string refund_code = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearRefundCode() {
      
      refundCode_ = getDefaultInstance().getRefundCode();
      onChanged();
      return this;
    }
    /**
     * <code>string refund_code = 7;</code>
     * @param value The bytes for refundCode to set.
     * @return This builder for chaining.
     */
    public Builder setRefundCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      refundCode_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:coupon.RefundInfo)
  }

  // @@protoc_insertion_point(class_scope:coupon.RefundInfo)
  private static final cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo();
  }

  public static cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RefundInfo>
      PARSER = new com.google.protobuf.AbstractParser<RefundInfo>() {
    @java.lang.Override
    public RefundInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new RefundInfo(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<RefundInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RefundInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

