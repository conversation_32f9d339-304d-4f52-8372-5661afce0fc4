// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface SkuRemarkOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.SkuRemark)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>.coupon.SkuRemark.skuName name = 1;</code>
   * @return Whether the name field is set.
   */
  boolean hasName();
  /**
   * <code>.coupon.SkuRemark.skuName name = 1;</code>
   * @return The name.
   */
  cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.skuName getName();
  /**
   * <code>.coupon.SkuRemark.skuName name = 1;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.skuNameOrBuilder getNameOrBuilder();

  /**
   * <code>.coupon.SkuRemark.skuValue values = 2;</code>
   * @return Whether the values field is set.
   */
  boolean hasValues();
  /**
   * <code>.coupon.SkuRemark.skuValue values = 2;</code>
   * @return The values.
   */
  cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.skuValue getValues();
  /**
   * <code>.coupon.SkuRemark.skuValue values = 2;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.skuValueOrBuilder getValuesOrBuilder();
}
