// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface StoreOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.Store)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string code = 2;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <code>string code = 2;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <pre>
   *门店三方编码
   * </pre>
   *
   * <code>string secondCode = 3;</code>
   * @return The secondCode.
   */
  java.lang.String getSecondCode();
  /**
   * <pre>
   *门店三方编码
   * </pre>
   *
   * <code>string secondCode = 3;</code>
   * @return The bytes for secondCode.
   */
  com.google.protobuf.ByteString
      getSecondCodeBytes();

  /**
   * <pre>
   *门店三方id
   * </pre>
   *
   * <code>string companyId = 4;</code>
   * @return The companyId.
   */
  java.lang.String getCompanyId();
  /**
   * <pre>
   *门店三方id
   * </pre>
   *
   * <code>string companyId = 4;</code>
   * @return The bytes for companyId.
   */
  com.google.protobuf.ByteString
      getCompanyIdBytes();

  /**
   * <pre>
   *租户id
   * </pre>
   *
   * <code>string partnerId = 5;</code>
   * @return The partnerId.
   */
  java.lang.String getPartnerId();
  /**
   * <pre>
   *租户id
   * </pre>
   *
   * <code>string partnerId = 5;</code>
   * @return The bytes for partnerId.
   */
  com.google.protobuf.ByteString
      getPartnerIdBytes();

  /**
   * <code>string scopeId = 6;</code>
   * @return The scopeId.
   */
  java.lang.String getScopeId();
  /**
   * <code>string scopeId = 6;</code>
   * @return The bytes for scopeId.
   */
  com.google.protobuf.ByteString
      getScopeIdBytes();

  /**
   * <code>string branchId = 7;</code>
   * @return The branchId.
   */
  java.lang.String getBranchId();
  /**
   * <code>string branchId = 7;</code>
   * @return The bytes for branchId.
   */
  com.google.protobuf.ByteString
      getBranchIdBytes();
}
