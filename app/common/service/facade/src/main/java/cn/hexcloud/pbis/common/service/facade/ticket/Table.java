// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

/**
 * <pre>
 *桌位信息
 * </pre>
 *
 * Protobuf type {@code coupon.Table}
 */
public final class Table extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:coupon.Table)
    TableOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Table.newBuilder() to construct.
  private Table(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Table() {
    id_ = "";
    zoneId_ = "";
    no_ = "";
    zoneNo_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Table();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Table(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            id_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            zoneId_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            no_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            zoneNo_ = s;
            break;
          }
          case 40: {

            people_ = input.readInt32();
            break;
          }
          case 48: {

            temporary_ = input.readBool();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Table_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Table_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.ticket.Table.class, cn.hexcloud.pbis.common.service.facade.ticket.Table.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  private volatile java.lang.Object id_;
  /**
   * <pre>
   *桌位id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *桌位id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ZONE_ID_FIELD_NUMBER = 2;
  private volatile java.lang.Object zoneId_;
  /**
   * <pre>
   *桌位分区id
   * </pre>
   *
   * <code>string zone_id = 2;</code>
   * @return The zoneId.
   */
  @java.lang.Override
  public java.lang.String getZoneId() {
    java.lang.Object ref = zoneId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      zoneId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *桌位分区id
   * </pre>
   *
   * <code>string zone_id = 2;</code>
   * @return The bytes for zoneId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getZoneIdBytes() {
    java.lang.Object ref = zoneId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      zoneId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NO_FIELD_NUMBER = 3;
  private volatile java.lang.Object no_;
  /**
   * <pre>
   *桌位号
   * </pre>
   *
   * <code>string no = 3;</code>
   * @return The no.
   */
  @java.lang.Override
  public java.lang.String getNo() {
    java.lang.Object ref = no_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      no_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *桌位号
   * </pre>
   *
   * <code>string no = 3;</code>
   * @return The bytes for no.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNoBytes() {
    java.lang.Object ref = no_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      no_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ZONENO_FIELD_NUMBER = 4;
  private volatile java.lang.Object zoneNo_;
  /**
   * <pre>
   *桌位分区号
   * </pre>
   *
   * <code>string zoneNo = 4;</code>
   * @return The zoneNo.
   */
  @java.lang.Override
  public java.lang.String getZoneNo() {
    java.lang.Object ref = zoneNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      zoneNo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *桌位分区号
   * </pre>
   *
   * <code>string zoneNo = 4;</code>
   * @return The bytes for zoneNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getZoneNoBytes() {
    java.lang.Object ref = zoneNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      zoneNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PEOPLE_FIELD_NUMBER = 5;
  private int people_;
  /**
   * <pre>
   *桌位人数
   * </pre>
   *
   * <code>int32 people = 5;</code>
   * @return The people.
   */
  @java.lang.Override
  public int getPeople() {
    return people_;
  }

  public static final int TEMPORARY_FIELD_NUMBER = 6;
  private boolean temporary_;
  /**
   * <pre>
   *是否是临时桌位
   * </pre>
   *
   * <code>bool temporary = 6;</code>
   * @return The temporary.
   */
  @java.lang.Override
  public boolean getTemporary() {
    return temporary_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!getZoneIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, zoneId_);
    }
    if (!getNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, no_);
    }
    if (!getZoneNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, zoneNo_);
    }
    if (people_ != 0) {
      output.writeInt32(5, people_);
    }
    if (temporary_ != false) {
      output.writeBool(6, temporary_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!getZoneIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, zoneId_);
    }
    if (!getNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, no_);
    }
    if (!getZoneNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, zoneNo_);
    }
    if (people_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, people_);
    }
    if (temporary_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(6, temporary_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.ticket.Table)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.ticket.Table other = (cn.hexcloud.pbis.common.service.facade.ticket.Table) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getZoneId()
        .equals(other.getZoneId())) return false;
    if (!getNo()
        .equals(other.getNo())) return false;
    if (!getZoneNo()
        .equals(other.getZoneNo())) return false;
    if (getPeople()
        != other.getPeople()) return false;
    if (getTemporary()
        != other.getTemporary()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + ZONE_ID_FIELD_NUMBER;
    hash = (53 * hash) + getZoneId().hashCode();
    hash = (37 * hash) + NO_FIELD_NUMBER;
    hash = (53 * hash) + getNo().hashCode();
    hash = (37 * hash) + ZONENO_FIELD_NUMBER;
    hash = (53 * hash) + getZoneNo().hashCode();
    hash = (37 * hash) + PEOPLE_FIELD_NUMBER;
    hash = (53 * hash) + getPeople();
    hash = (37 * hash) + TEMPORARY_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getTemporary());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.ticket.Table parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Table parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Table parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Table parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Table parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Table parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Table parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Table parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Table parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Table parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Table parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Table parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.ticket.Table prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *桌位信息
   * </pre>
   *
   * Protobuf type {@code coupon.Table}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:coupon.Table)
      cn.hexcloud.pbis.common.service.facade.ticket.TableOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Table_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Table_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.ticket.Table.class, cn.hexcloud.pbis.common.service.facade.ticket.Table.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.ticket.Table.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      id_ = "";

      zoneId_ = "";

      no_ = "";

      zoneNo_ = "";

      people_ = 0;

      temporary_ = false;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Table_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.Table getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.ticket.Table.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.Table build() {
      cn.hexcloud.pbis.common.service.facade.ticket.Table result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.Table buildPartial() {
      cn.hexcloud.pbis.common.service.facade.ticket.Table result = new cn.hexcloud.pbis.common.service.facade.ticket.Table(this);
      result.id_ = id_;
      result.zoneId_ = zoneId_;
      result.no_ = no_;
      result.zoneNo_ = zoneNo_;
      result.people_ = people_;
      result.temporary_ = temporary_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.ticket.Table) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.ticket.Table)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.ticket.Table other) {
      if (other == cn.hexcloud.pbis.common.service.facade.ticket.Table.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        onChanged();
      }
      if (!other.getZoneId().isEmpty()) {
        zoneId_ = other.zoneId_;
        onChanged();
      }
      if (!other.getNo().isEmpty()) {
        no_ = other.no_;
        onChanged();
      }
      if (!other.getZoneNo().isEmpty()) {
        zoneNo_ = other.zoneNo_;
        onChanged();
      }
      if (other.getPeople() != 0) {
        setPeople(other.getPeople());
      }
      if (other.getTemporary() != false) {
        setTemporary(other.getTemporary());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.ticket.Table parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.ticket.Table) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object id_ = "";
    /**
     * <pre>
     *桌位id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *桌位id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *桌位id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *桌位id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      
      id_ = getDefaultInstance().getId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *桌位id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      id_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object zoneId_ = "";
    /**
     * <pre>
     *桌位分区id
     * </pre>
     *
     * <code>string zone_id = 2;</code>
     * @return The zoneId.
     */
    public java.lang.String getZoneId() {
      java.lang.Object ref = zoneId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        zoneId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *桌位分区id
     * </pre>
     *
     * <code>string zone_id = 2;</code>
     * @return The bytes for zoneId.
     */
    public com.google.protobuf.ByteString
        getZoneIdBytes() {
      java.lang.Object ref = zoneId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        zoneId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *桌位分区id
     * </pre>
     *
     * <code>string zone_id = 2;</code>
     * @param value The zoneId to set.
     * @return This builder for chaining.
     */
    public Builder setZoneId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      zoneId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *桌位分区id
     * </pre>
     *
     * <code>string zone_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearZoneId() {
      
      zoneId_ = getDefaultInstance().getZoneId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *桌位分区id
     * </pre>
     *
     * <code>string zone_id = 2;</code>
     * @param value The bytes for zoneId to set.
     * @return This builder for chaining.
     */
    public Builder setZoneIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      zoneId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object no_ = "";
    /**
     * <pre>
     *桌位号
     * </pre>
     *
     * <code>string no = 3;</code>
     * @return The no.
     */
    public java.lang.String getNo() {
      java.lang.Object ref = no_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        no_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *桌位号
     * </pre>
     *
     * <code>string no = 3;</code>
     * @return The bytes for no.
     */
    public com.google.protobuf.ByteString
        getNoBytes() {
      java.lang.Object ref = no_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        no_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *桌位号
     * </pre>
     *
     * <code>string no = 3;</code>
     * @param value The no to set.
     * @return This builder for chaining.
     */
    public Builder setNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      no_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *桌位号
     * </pre>
     *
     * <code>string no = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearNo() {
      
      no_ = getDefaultInstance().getNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *桌位号
     * </pre>
     *
     * <code>string no = 3;</code>
     * @param value The bytes for no to set.
     * @return This builder for chaining.
     */
    public Builder setNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      no_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object zoneNo_ = "";
    /**
     * <pre>
     *桌位分区号
     * </pre>
     *
     * <code>string zoneNo = 4;</code>
     * @return The zoneNo.
     */
    public java.lang.String getZoneNo() {
      java.lang.Object ref = zoneNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        zoneNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *桌位分区号
     * </pre>
     *
     * <code>string zoneNo = 4;</code>
     * @return The bytes for zoneNo.
     */
    public com.google.protobuf.ByteString
        getZoneNoBytes() {
      java.lang.Object ref = zoneNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        zoneNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *桌位分区号
     * </pre>
     *
     * <code>string zoneNo = 4;</code>
     * @param value The zoneNo to set.
     * @return This builder for chaining.
     */
    public Builder setZoneNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      zoneNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *桌位分区号
     * </pre>
     *
     * <code>string zoneNo = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearZoneNo() {
      
      zoneNo_ = getDefaultInstance().getZoneNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *桌位分区号
     * </pre>
     *
     * <code>string zoneNo = 4;</code>
     * @param value The bytes for zoneNo to set.
     * @return This builder for chaining.
     */
    public Builder setZoneNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      zoneNo_ = value;
      onChanged();
      return this;
    }

    private int people_ ;
    /**
     * <pre>
     *桌位人数
     * </pre>
     *
     * <code>int32 people = 5;</code>
     * @return The people.
     */
    @java.lang.Override
    public int getPeople() {
      return people_;
    }
    /**
     * <pre>
     *桌位人数
     * </pre>
     *
     * <code>int32 people = 5;</code>
     * @param value The people to set.
     * @return This builder for chaining.
     */
    public Builder setPeople(int value) {
      
      people_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *桌位人数
     * </pre>
     *
     * <code>int32 people = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearPeople() {
      
      people_ = 0;
      onChanged();
      return this;
    }

    private boolean temporary_ ;
    /**
     * <pre>
     *是否是临时桌位
     * </pre>
     *
     * <code>bool temporary = 6;</code>
     * @return The temporary.
     */
    @java.lang.Override
    public boolean getTemporary() {
      return temporary_;
    }
    /**
     * <pre>
     *是否是临时桌位
     * </pre>
     *
     * <code>bool temporary = 6;</code>
     * @param value The temporary to set.
     * @return This builder for chaining.
     */
    public Builder setTemporary(boolean value) {
      
      temporary_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *是否是临时桌位
     * </pre>
     *
     * <code>bool temporary = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearTemporary() {
      
      temporary_ = false;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:coupon.Table)
  }

  // @@protoc_insertion_point(class_scope:coupon.Table)
  private static final cn.hexcloud.pbis.common.service.facade.ticket.Table DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.ticket.Table();
  }

  public static cn.hexcloud.pbis.common.service.facade.ticket.Table getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Table>
      PARSER = new com.google.protobuf.AbstractParser<Table>() {
    @java.lang.Override
    public Table parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Table(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Table> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Table> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Table getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

