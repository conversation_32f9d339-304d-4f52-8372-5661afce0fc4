// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

/**
 * Protobuf type {@code coupon.Ticket}
 */
public final class Ticket extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:coupon.Ticket)
    TicketOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Ticket.newBuilder() to construct.
  private Ticket(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Ticket() {
    ticketId_ = "";
    ticketNo_ = "";
    startTime_ = "";
    endTime_ = "";
    busDate_ = "";
    takemealNumber_ = "";
    status_ = "";
    products_ = java.util.Collections.emptyList();
    payments_ = java.util.Collections.emptyList();
    promotions_ = java.util.Collections.emptyList();
    members_ = java.util.Collections.emptyList();
    roomNo_ = "";
    remark_ = "";
    orderTimeType_ = "";
    shiftNumber_ = "";
    taxList_ = java.util.Collections.emptyList();
    ticketUno_ = "";
    coupons_ = java.util.Collections.emptyList();
    fees_ = java.util.Collections.emptyList();
    timeZone_ = "";
    uploadTime_ = "";
    transactionNo_ = "";
    feesNoAccount_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Ticket();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Ticket(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            ticketId_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            ticketNo_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            startTime_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            endTime_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            busDate_ = s;
            break;
          }
          case 50: {
            cn.hexcloud.pbis.common.service.facade.ticket.Pos.Builder subBuilder = null;
            if (pos_ != null) {
              subBuilder = pos_.toBuilder();
            }
            pos_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.Pos.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(pos_);
              pos_ = subBuilder.buildPartial();
            }

            break;
          }
          case 58: {
            cn.hexcloud.pbis.common.service.facade.ticket.Operator.Builder subBuilder = null;
            if (operator_ != null) {
              subBuilder = operator_.toBuilder();
            }
            operator_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.Operator.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(operator_);
              operator_ = subBuilder.buildPartial();
            }

            break;
          }
          case 66: {
            cn.hexcloud.pbis.common.service.facade.ticket.Amount.Builder subBuilder = null;
            if (amounts_ != null) {
              subBuilder = amounts_.toBuilder();
            }
            amounts_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.Amount.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(amounts_);
              amounts_ = subBuilder.buildPartial();
            }

            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            takemealNumber_ = s;
            break;
          }
          case 80: {

            qty_ = input.readInt32();
            break;
          }
          case 90: {
            java.lang.String s = input.readStringRequireUtf8();

            status_ = s;
            break;
          }
          case 98: {
            cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.Builder subBuilder = null;
            if (refundInfo_ != null) {
              subBuilder = refundInfo_.toBuilder();
            }
            refundInfo_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(refundInfo_);
              refundInfo_ = subBuilder.buildPartial();
            }

            break;
          }
          case 106: {
            cn.hexcloud.pbis.common.service.facade.ticket.Channel.Builder subBuilder = null;
            if (channel_ != null) {
              subBuilder = channel_.toBuilder();
            }
            channel_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.Channel.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(channel_);
              channel_ = subBuilder.buildPartial();
            }

            break;
          }
          case 114: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              products_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct>();
              mutable_bitField0_ |= 0x00000001;
            }
            products_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.parser(), extensionRegistry));
            break;
          }
          case 122: {
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              payments_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.Payment>();
              mutable_bitField0_ |= 0x00000002;
            }
            payments_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.Payment.parser(), extensionRegistry));
            break;
          }
          case 130: {
            if (!((mutable_bitField0_ & 0x00000004) != 0)) {
              promotions_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.Promotion>();
              mutable_bitField0_ |= 0x00000004;
            }
            promotions_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.Promotion.parser(), extensionRegistry));
            break;
          }
          case 138: {
            if (!((mutable_bitField0_ & 0x00000008) != 0)) {
              members_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.Member>();
              mutable_bitField0_ |= 0x00000008;
            }
            members_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.Member.parser(), extensionRegistry));
            break;
          }
          case 146: {
            cn.hexcloud.pbis.common.service.facade.ticket.Table.Builder subBuilder = null;
            if (table_ != null) {
              subBuilder = table_.toBuilder();
            }
            table_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.Table.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(table_);
              table_ = subBuilder.buildPartial();
            }

            break;
          }
          case 152: {

            people_ = input.readInt32();
            break;
          }
          case 162: {
            java.lang.String s = input.readStringRequireUtf8();

            roomNo_ = s;
            break;
          }
          case 170: {
            java.lang.String s = input.readStringRequireUtf8();

            remark_ = s;
            break;
          }
          case 176: {

            houseAc_ = input.readBool();
            break;
          }
          case 186: {
            java.lang.String s = input.readStringRequireUtf8();

            orderTimeType_ = s;
            break;
          }
          case 194: {
            java.lang.String s = input.readStringRequireUtf8();

            shiftNumber_ = s;
            break;
          }
          case 202: {
            if (!((mutable_bitField0_ & 0x00000010) != 0)) {
              taxList_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.Tax>();
              mutable_bitField0_ |= 0x00000010;
            }
            taxList_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.Tax.parser(), extensionRegistry));
            break;
          }
          case 210: {
            cn.hexcloud.pbis.common.service.facade.ticket.Store.Builder subBuilder = null;
            if (store_ != null) {
              subBuilder = store_.toBuilder();
            }
            store_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.Store.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(store_);
              store_ = subBuilder.buildPartial();
            }

            break;
          }
          case 274: {
            cn.hexcloud.pbis.common.service.facade.ticket.Takeaway.Builder subBuilder = null;
            if (takeawayInfo_ != null) {
              subBuilder = takeawayInfo_.toBuilder();
            }
            takeawayInfo_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.Takeaway.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(takeawayInfo_);
              takeawayInfo_ = subBuilder.buildPartial();
            }

            break;
          }
          case 282: {
            java.lang.String s = input.readStringRequireUtf8();

            ticketUno_ = s;
            break;
          }
          case 290: {
            if (!((mutable_bitField0_ & 0x00000020) != 0)) {
              coupons_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon>();
              mutable_bitField0_ |= 0x00000020;
            }
            coupons_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon.parser(), extensionRegistry));
            break;
          }
          case 298: {
            if (!((mutable_bitField0_ & 0x00000040) != 0)) {
              fees_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.Fee>();
              mutable_bitField0_ |= 0x00000040;
            }
            fees_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.Fee.parser(), extensionRegistry));
            break;
          }
          case 306: {
            java.lang.String s = input.readStringRequireUtf8();

            timeZone_ = s;
            break;
          }
          case 314: {
            java.lang.String s = input.readStringRequireUtf8();

            uploadTime_ = s;
            break;
          }
          case 320: {

            discountProportioned_ = input.readBool();
            break;
          }
          case 330: {
            java.lang.String s = input.readStringRequireUtf8();

            transactionNo_ = s;
            break;
          }
          case 338: {
            if (!((mutable_bitField0_ & 0x00000080) != 0)) {
              feesNoAccount_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.Fee>();
              mutable_bitField0_ |= 0x00000080;
            }
            feesNoAccount_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.Fee.parser(), extensionRegistry));
            break;
          }
          case 346: {
            cn.hexcloud.pbis.common.service.facade.ticket.Efficiency.Builder subBuilder = null;
            if (efficiency_ != null) {
              subBuilder = efficiency_.toBuilder();
            }
            efficiency_ = input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.Efficiency.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(efficiency_);
              efficiency_ = subBuilder.buildPartial();
            }

            break;
          }
          case 352: {

            partnerId_ = input.readUInt64();
            break;
          }
          case 365: {

            weight_ = input.readFloat();
            break;
          }
          case 368: {

            pendingSyncMember_ = input.readBool();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        products_ = java.util.Collections.unmodifiableList(products_);
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        payments_ = java.util.Collections.unmodifiableList(payments_);
      }
      if (((mutable_bitField0_ & 0x00000004) != 0)) {
        promotions_ = java.util.Collections.unmodifiableList(promotions_);
      }
      if (((mutable_bitField0_ & 0x00000008) != 0)) {
        members_ = java.util.Collections.unmodifiableList(members_);
      }
      if (((mutable_bitField0_ & 0x00000010) != 0)) {
        taxList_ = java.util.Collections.unmodifiableList(taxList_);
      }
      if (((mutable_bitField0_ & 0x00000020) != 0)) {
        coupons_ = java.util.Collections.unmodifiableList(coupons_);
      }
      if (((mutable_bitField0_ & 0x00000040) != 0)) {
        fees_ = java.util.Collections.unmodifiableList(fees_);
      }
      if (((mutable_bitField0_ & 0x00000080) != 0)) {
        feesNoAccount_ = java.util.Collections.unmodifiableList(feesNoAccount_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Ticket_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Ticket_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.ticket.Ticket.class, cn.hexcloud.pbis.common.service.facade.ticket.Ticket.Builder.class);
  }

  public static final int TICKET_ID_FIELD_NUMBER = 1;
  private volatile java.lang.Object ticketId_;
  /**
   * <pre>
   *订单uuid，全市场范围内唯一
   * </pre>
   *
   * <code>string ticket_id = 1;</code>
   * @return The ticketId.
   */
  @java.lang.Override
  public java.lang.String getTicketId() {
    java.lang.Object ref = ticketId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      ticketId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *订单uuid，全市场范围内唯一
   * </pre>
   *
   * <code>string ticket_id = 1;</code>
   * @return The bytes for ticketId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTicketIdBytes() {
    java.lang.Object ref = ticketId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      ticketId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TICKET_NO_FIELD_NUMBER = 2;
  private volatile java.lang.Object ticketNo_;
  /**
   * <pre>
   *订单号，有特殊的业务规则
   * </pre>
   *
   * <code>string ticket_no = 2;</code>
   * @return The ticketNo.
   */
  @java.lang.Override
  public java.lang.String getTicketNo() {
    java.lang.Object ref = ticketNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      ticketNo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *订单号，有特殊的业务规则
   * </pre>
   *
   * <code>string ticket_no = 2;</code>
   * @return The bytes for ticketNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTicketNoBytes() {
    java.lang.Object ref = ticketNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      ticketNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int START_TIME_FIELD_NUMBER = 3;
  private volatile java.lang.Object startTime_;
  /**
   * <pre>
   *YYYY-MM-dd HH:MM:SS，订单开始时间
   * </pre>
   *
   * <code>string start_time = 3;</code>
   * @return The startTime.
   */
  @java.lang.Override
  public java.lang.String getStartTime() {
    java.lang.Object ref = startTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      startTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *YYYY-MM-dd HH:MM:SS，订单开始时间
   * </pre>
   *
   * <code>string start_time = 3;</code>
   * @return The bytes for startTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStartTimeBytes() {
    java.lang.Object ref = startTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      startTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int END_TIME_FIELD_NUMBER = 4;
  private volatile java.lang.Object endTime_;
  /**
   * <pre>
   *YYYY-MM-dd HH:MM:SS，订单结束时间
   * </pre>
   *
   * <code>string end_time = 4;</code>
   * @return The endTime.
   */
  @java.lang.Override
  public java.lang.String getEndTime() {
    java.lang.Object ref = endTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      endTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *YYYY-MM-dd HH:MM:SS，订单结束时间
   * </pre>
   *
   * <code>string end_time = 4;</code>
   * @return The bytes for endTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getEndTimeBytes() {
    java.lang.Object ref = endTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      endTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BUS_DATE_FIELD_NUMBER = 5;
  private volatile java.lang.Object busDate_;
  /**
   * <pre>
   *YYYY-MM-dd，订单营业日期
   * </pre>
   *
   * <code>string bus_date = 5;</code>
   * @return The busDate.
   */
  @java.lang.Override
  public java.lang.String getBusDate() {
    java.lang.Object ref = busDate_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      busDate_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *YYYY-MM-dd，订单营业日期
   * </pre>
   *
   * <code>string bus_date = 5;</code>
   * @return The bytes for busDate.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBusDateBytes() {
    java.lang.Object ref = busDate_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      busDate_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int POS_FIELD_NUMBER = 6;
  private cn.hexcloud.pbis.common.service.facade.ticket.Pos pos_;
  /**
   * <pre>
   *pos信息
   * </pre>
   *
   * <code>.coupon.Pos pos = 6;</code>
   * @return Whether the pos field is set.
   */
  @java.lang.Override
  public boolean hasPos() {
    return pos_ != null;
  }
  /**
   * <pre>
   *pos信息
   * </pre>
   *
   * <code>.coupon.Pos pos = 6;</code>
   * @return The pos.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Pos getPos() {
    return pos_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Pos.getDefaultInstance() : pos_;
  }
  /**
   * <pre>
   *pos信息
   * </pre>
   *
   * <code>.coupon.Pos pos = 6;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.PosOrBuilder getPosOrBuilder() {
    return getPos();
  }

  public static final int OPERATOR_FIELD_NUMBER = 7;
  private cn.hexcloud.pbis.common.service.facade.ticket.Operator operator_;
  /**
   * <pre>
   *收银员信息
   * </pre>
   *
   * <code>.coupon.Operator operator = 7;</code>
   * @return Whether the operator field is set.
   */
  @java.lang.Override
  public boolean hasOperator() {
    return operator_ != null;
  }
  /**
   * <pre>
   *收银员信息
   * </pre>
   *
   * <code>.coupon.Operator operator = 7;</code>
   * @return The operator.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Operator getOperator() {
    return operator_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Operator.getDefaultInstance() : operator_;
  }
  /**
   * <pre>
   *收银员信息
   * </pre>
   *
   * <code>.coupon.Operator operator = 7;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.OperatorOrBuilder getOperatorOrBuilder() {
    return getOperator();
  }

  public static final int AMOUNTS_FIELD_NUMBER = 8;
  private cn.hexcloud.pbis.common.service.facade.ticket.Amount amounts_;
  /**
   * <pre>
   *金额信息
   * </pre>
   *
   * <code>.coupon.Amount amounts = 8;</code>
   * @return Whether the amounts field is set.
   */
  @java.lang.Override
  public boolean hasAmounts() {
    return amounts_ != null;
  }
  /**
   * <pre>
   *金额信息
   * </pre>
   *
   * <code>.coupon.Amount amounts = 8;</code>
   * @return The amounts.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Amount getAmounts() {
    return amounts_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Amount.getDefaultInstance() : amounts_;
  }
  /**
   * <pre>
   *金额信息
   * </pre>
   *
   * <code>.coupon.Amount amounts = 8;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.AmountOrBuilder getAmountsOrBuilder() {
    return getAmounts();
  }

  public static final int TAKEMEALNUMBER_FIELD_NUMBER = 9;
  private volatile java.lang.Object takemealNumber_;
  /**
   * <pre>
   *取餐号
   * </pre>
   *
   * <code>string takemealNumber = 9;</code>
   * @return The takemealNumber.
   */
  @java.lang.Override
  public java.lang.String getTakemealNumber() {
    java.lang.Object ref = takemealNumber_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      takemealNumber_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *取餐号
   * </pre>
   *
   * <code>string takemealNumber = 9;</code>
   * @return The bytes for takemealNumber.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTakemealNumberBytes() {
    java.lang.Object ref = takemealNumber_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      takemealNumber_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int QTY_FIELD_NUMBER = 10;
  private int qty_;
  /**
   * <pre>
   *订单商品总数
   * </pre>
   *
   * <code>int32 qty = 10;</code>
   * @return The qty.
   */
  @java.lang.Override
  public int getQty() {
    return qty_;
  }

  public static final int STATUS_FIELD_NUMBER = 11;
  private volatile java.lang.Object status_;
  /**
   * <code>string status = 11;</code>
   * @return The status.
   */
  @java.lang.Override
  public java.lang.String getStatus() {
    java.lang.Object ref = status_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      status_ = s;
      return s;
    }
  }
  /**
   * <code>string status = 11;</code>
   * @return The bytes for status.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStatusBytes() {
    java.lang.Object ref = status_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      status_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REFUNDINFO_FIELD_NUMBER = 12;
  private cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo refundInfo_;
  /**
   * <pre>
   *订单的退款相关信息
   * </pre>
   *
   * <code>.coupon.RefundInfo refundInfo = 12;</code>
   * @return Whether the refundInfo field is set.
   */
  @java.lang.Override
  public boolean hasRefundInfo() {
    return refundInfo_ != null;
  }
  /**
   * <pre>
   *订单的退款相关信息
   * </pre>
   *
   * <code>.coupon.RefundInfo refundInfo = 12;</code>
   * @return The refundInfo.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo getRefundInfo() {
    return refundInfo_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.getDefaultInstance() : refundInfo_;
  }
  /**
   * <pre>
   *订单的退款相关信息
   * </pre>
   *
   * <code>.coupon.RefundInfo refundInfo = 12;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.RefundInfoOrBuilder getRefundInfoOrBuilder() {
    return getRefundInfo();
  }

  public static final int CHANNEL_FIELD_NUMBER = 13;
  private cn.hexcloud.pbis.common.service.facade.ticket.Channel channel_;
  /**
   * <pre>
   *订单的渠道信息
   * </pre>
   *
   * <code>.coupon.Channel channel = 13;</code>
   * @return Whether the channel field is set.
   */
  @java.lang.Override
  public boolean hasChannel() {
    return channel_ != null;
  }
  /**
   * <pre>
   *订单的渠道信息
   * </pre>
   *
   * <code>.coupon.Channel channel = 13;</code>
   * @return The channel.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Channel getChannel() {
    return channel_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Channel.getDefaultInstance() : channel_;
  }
  /**
   * <pre>
   *订单的渠道信息
   * </pre>
   *
   * <code>.coupon.Channel channel = 13;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.ChannelOrBuilder getChannelOrBuilder() {
    return getChannel();
  }

  public static final int PRODUCTS_FIELD_NUMBER = 14;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> products_;
  /**
   * <pre>
   *订单商品信息
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct products = 14;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> getProductsList() {
    return products_;
  }
  /**
   * <pre>
   *订单商品信息
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct products = 14;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder> 
      getProductsOrBuilderList() {
    return products_;
  }
  /**
   * <pre>
   *订单商品信息
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct products = 14;</code>
   */
  @java.lang.Override
  public int getProductsCount() {
    return products_.size();
  }
  /**
   * <pre>
   *订单商品信息
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct products = 14;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct getProducts(int index) {
    return products_.get(index);
  }
  /**
   * <pre>
   *订单商品信息
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct products = 14;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder getProductsOrBuilder(
      int index) {
    return products_.get(index);
  }

  public static final int PAYMENTS_FIELD_NUMBER = 15;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Payment> payments_;
  /**
   * <pre>
   *订单的支付信息
   * </pre>
   *
   * <code>repeated .coupon.Payment payments = 15;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Payment> getPaymentsList() {
    return payments_;
  }
  /**
   * <pre>
   *订单的支付信息
   * </pre>
   *
   * <code>repeated .coupon.Payment payments = 15;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.PaymentOrBuilder> 
      getPaymentsOrBuilderList() {
    return payments_;
  }
  /**
   * <pre>
   *订单的支付信息
   * </pre>
   *
   * <code>repeated .coupon.Payment payments = 15;</code>
   */
  @java.lang.Override
  public int getPaymentsCount() {
    return payments_.size();
  }
  /**
   * <pre>
   *订单的支付信息
   * </pre>
   *
   * <code>repeated .coupon.Payment payments = 15;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Payment getPayments(int index) {
    return payments_.get(index);
  }
  /**
   * <pre>
   *订单的支付信息
   * </pre>
   *
   * <code>repeated .coupon.Payment payments = 15;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.PaymentOrBuilder getPaymentsOrBuilder(
      int index) {
    return payments_.get(index);
  }

  public static final int PROMOTIONS_FIELD_NUMBER = 16;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Promotion> promotions_;
  /**
   * <pre>
   *订单的促销信息
   * </pre>
   *
   * <code>repeated .coupon.Promotion promotions = 16;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Promotion> getPromotionsList() {
    return promotions_;
  }
  /**
   * <pre>
   *订单的促销信息
   * </pre>
   *
   * <code>repeated .coupon.Promotion promotions = 16;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.PromotionOrBuilder> 
      getPromotionsOrBuilderList() {
    return promotions_;
  }
  /**
   * <pre>
   *订单的促销信息
   * </pre>
   *
   * <code>repeated .coupon.Promotion promotions = 16;</code>
   */
  @java.lang.Override
  public int getPromotionsCount() {
    return promotions_.size();
  }
  /**
   * <pre>
   *订单的促销信息
   * </pre>
   *
   * <code>repeated .coupon.Promotion promotions = 16;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Promotion getPromotions(int index) {
    return promotions_.get(index);
  }
  /**
   * <pre>
   *订单的促销信息
   * </pre>
   *
   * <code>repeated .coupon.Promotion promotions = 16;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.PromotionOrBuilder getPromotionsOrBuilder(
      int index) {
    return promotions_.get(index);
  }

  public static final int MEMBERS_FIELD_NUMBER = 17;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Member> members_;
  /**
   * <pre>
   *订单的会员信息
   * </pre>
   *
   * <code>repeated .coupon.Member members = 17;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Member> getMembersList() {
    return members_;
  }
  /**
   * <pre>
   *订单的会员信息
   * </pre>
   *
   * <code>repeated .coupon.Member members = 17;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.MemberOrBuilder> 
      getMembersOrBuilderList() {
    return members_;
  }
  /**
   * <pre>
   *订单的会员信息
   * </pre>
   *
   * <code>repeated .coupon.Member members = 17;</code>
   */
  @java.lang.Override
  public int getMembersCount() {
    return members_.size();
  }
  /**
   * <pre>
   *订单的会员信息
   * </pre>
   *
   * <code>repeated .coupon.Member members = 17;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Member getMembers(int index) {
    return members_.get(index);
  }
  /**
   * <pre>
   *订单的会员信息
   * </pre>
   *
   * <code>repeated .coupon.Member members = 17;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.MemberOrBuilder getMembersOrBuilder(
      int index) {
    return members_.get(index);
  }

  public static final int TABLE_FIELD_NUMBER = 18;
  private cn.hexcloud.pbis.common.service.facade.ticket.Table table_;
  /**
   * <pre>
   *桌位信息
   * </pre>
   *
   * <code>.coupon.Table table = 18;</code>
   * @return Whether the table field is set.
   */
  @java.lang.Override
  public boolean hasTable() {
    return table_ != null;
  }
  /**
   * <pre>
   *桌位信息
   * </pre>
   *
   * <code>.coupon.Table table = 18;</code>
   * @return The table.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Table getTable() {
    return table_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Table.getDefaultInstance() : table_;
  }
  /**
   * <pre>
   *桌位信息
   * </pre>
   *
   * <code>.coupon.Table table = 18;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.TableOrBuilder getTableOrBuilder() {
    return getTable();
  }

  public static final int PEOPLE_FIELD_NUMBER = 19;
  private int people_;
  /**
   * <pre>
   *订单人数
   * </pre>
   *
   * <code>int32 people = 19;</code>
   * @return The people.
   */
  @java.lang.Override
  public int getPeople() {
    return people_;
  }

  public static final int ROOM_NO_FIELD_NUMBER = 20;
  private volatile java.lang.Object roomNo_;
  /**
   * <pre>
   *房间号
   * </pre>
   *
   * <code>string room_no = 20;</code>
   * @return The roomNo.
   */
  @java.lang.Override
  public java.lang.String getRoomNo() {
    java.lang.Object ref = roomNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      roomNo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *房间号
   * </pre>
   *
   * <code>string room_no = 20;</code>
   * @return The bytes for roomNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRoomNoBytes() {
    java.lang.Object ref = roomNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      roomNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REMARK_FIELD_NUMBER = 21;
  private volatile java.lang.Object remark_;
  /**
   * <pre>
   *订单备注
   * </pre>
   *
   * <code>string remark = 21;</code>
   * @return The remark.
   */
  @java.lang.Override
  public java.lang.String getRemark() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      remark_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *订单备注
   * </pre>
   *
   * <code>string remark = 21;</code>
   * @return The bytes for remark.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRemarkBytes() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      remark_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int HOUSE_AC_FIELD_NUMBER = 22;
  private boolean houseAc_;
  /**
   * <pre>
   *如家场景，意义待明确
   * </pre>
   *
   * <code>bool house_ac = 22;</code>
   * @return The houseAc.
   */
  @java.lang.Override
  public boolean getHouseAc() {
    return houseAc_;
  }

  public static final int ORDER_TIME_TYPE_FIELD_NUMBER = 23;
  private volatile java.lang.Object orderTimeType_;
  /**
   * <pre>
   *早中晚餐标志，枚举值
   * </pre>
   *
   * <code>string order_time_type = 23;</code>
   * @return The orderTimeType.
   */
  @java.lang.Override
  public java.lang.String getOrderTimeType() {
    java.lang.Object ref = orderTimeType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      orderTimeType_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *早中晚餐标志，枚举值
   * </pre>
   *
   * <code>string order_time_type = 23;</code>
   * @return The bytes for orderTimeType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOrderTimeTypeBytes() {
    java.lang.Object ref = orderTimeType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      orderTimeType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SHIFTNUMBER_FIELD_NUMBER = 24;
  private volatile java.lang.Object shiftNumber_;
  /**
   * <pre>
   *班次号
   * </pre>
   *
   * <code>string shiftNumber = 24;</code>
   * @return The shiftNumber.
   */
  @java.lang.Override
  public java.lang.String getShiftNumber() {
    java.lang.Object ref = shiftNumber_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      shiftNumber_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *班次号
   * </pre>
   *
   * <code>string shiftNumber = 24;</code>
   * @return The bytes for shiftNumber.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getShiftNumberBytes() {
    java.lang.Object ref = shiftNumber_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      shiftNumber_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TAXLIST_FIELD_NUMBER = 25;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Tax> taxList_;
  /**
   * <pre>
   *税项
   * </pre>
   *
   * <code>repeated .coupon.Tax taxList = 25;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Tax> getTaxListList() {
    return taxList_;
  }
  /**
   * <pre>
   *税项
   * </pre>
   *
   * <code>repeated .coupon.Tax taxList = 25;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.TaxOrBuilder> 
      getTaxListOrBuilderList() {
    return taxList_;
  }
  /**
   * <pre>
   *税项
   * </pre>
   *
   * <code>repeated .coupon.Tax taxList = 25;</code>
   */
  @java.lang.Override
  public int getTaxListCount() {
    return taxList_.size();
  }
  /**
   * <pre>
   *税项
   * </pre>
   *
   * <code>repeated .coupon.Tax taxList = 25;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Tax getTaxList(int index) {
    return taxList_.get(index);
  }
  /**
   * <pre>
   *税项
   * </pre>
   *
   * <code>repeated .coupon.Tax taxList = 25;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.TaxOrBuilder getTaxListOrBuilder(
      int index) {
    return taxList_.get(index);
  }

  public static final int STORE_FIELD_NUMBER = 26;
  private cn.hexcloud.pbis.common.service.facade.ticket.Store store_;
  /**
   * <pre>
   *门店信息
   * </pre>
   *
   * <code>.coupon.Store store = 26;</code>
   * @return Whether the store field is set.
   */
  @java.lang.Override
  public boolean hasStore() {
    return store_ != null;
  }
  /**
   * <pre>
   *门店信息
   * </pre>
   *
   * <code>.coupon.Store store = 26;</code>
   * @return The store.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Store getStore() {
    return store_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Store.getDefaultInstance() : store_;
  }
  /**
   * <pre>
   *门店信息
   * </pre>
   *
   * <code>.coupon.Store store = 26;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.StoreOrBuilder getStoreOrBuilder() {
    return getStore();
  }

  public static final int TAKEAWAY_INFO_FIELD_NUMBER = 34;
  private cn.hexcloud.pbis.common.service.facade.ticket.Takeaway takeawayInfo_;
  /**
   * <pre>
   *外卖信息
   * </pre>
   *
   * <code>.coupon.Takeaway takeaway_info = 34;</code>
   * @return Whether the takeawayInfo field is set.
   */
  @java.lang.Override
  public boolean hasTakeawayInfo() {
    return takeawayInfo_ != null;
  }
  /**
   * <pre>
   *外卖信息
   * </pre>
   *
   * <code>.coupon.Takeaway takeaway_info = 34;</code>
   * @return The takeawayInfo.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Takeaway getTakeawayInfo() {
    return takeawayInfo_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Takeaway.getDefaultInstance() : takeawayInfo_;
  }
  /**
   * <pre>
   *外卖信息
   * </pre>
   *
   * <code>.coupon.Takeaway takeaway_info = 34;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.TakeawayOrBuilder getTakeawayInfoOrBuilder() {
    return getTakeawayInfo();
  }

  public static final int TICKETUNO_FIELD_NUMBER = 35;
  private volatile java.lang.Object ticketUno_;
  /**
   * <pre>
   *订单唯一流水号
   * </pre>
   *
   * <code>string ticketUno = 35;</code>
   * @return The ticketUno.
   */
  @java.lang.Override
  public java.lang.String getTicketUno() {
    java.lang.Object ref = ticketUno_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      ticketUno_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *订单唯一流水号
   * </pre>
   *
   * <code>string ticketUno = 35;</code>
   * @return The bytes for ticketUno.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTicketUnoBytes() {
    java.lang.Object ref = ticketUno_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      ticketUno_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int COUPONS_FIELD_NUMBER = 36;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon> coupons_;
  /**
   * <pre>
   *卡券信息
   * </pre>
   *
   * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon> getCouponsList() {
    return coupons_;
  }
  /**
   * <pre>
   *卡券信息
   * </pre>
   *
   * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketCouponOrBuilder> 
      getCouponsOrBuilderList() {
    return coupons_;
  }
  /**
   * <pre>
   *卡券信息
   * </pre>
   *
   * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
   */
  @java.lang.Override
  public int getCouponsCount() {
    return coupons_.size();
  }
  /**
   * <pre>
   *卡券信息
   * </pre>
   *
   * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon getCoupons(int index) {
    return coupons_.get(index);
  }
  /**
   * <pre>
   *卡券信息
   * </pre>
   *
   * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.TicketCouponOrBuilder getCouponsOrBuilder(
      int index) {
    return coupons_.get(index);
  }

  public static final int FEES_FIELD_NUMBER = 37;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee> fees_;
  /**
   * <pre>
   *费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees = 37;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee> getFeesList() {
    return fees_;
  }
  /**
   * <pre>
   *费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees = 37;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder> 
      getFeesOrBuilderList() {
    return fees_;
  }
  /**
   * <pre>
   *费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees = 37;</code>
   */
  @java.lang.Override
  public int getFeesCount() {
    return fees_.size();
  }
  /**
   * <pre>
   *费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees = 37;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Fee getFees(int index) {
    return fees_.get(index);
  }
  /**
   * <pre>
   *费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees = 37;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder getFeesOrBuilder(
      int index) {
    return fees_.get(index);
  }

  public static final int TIMEZONE_FIELD_NUMBER = 38;
  private volatile java.lang.Object timeZone_;
  /**
   * <pre>
   *时区信息
   * </pre>
   *
   * <code>string timeZone = 38;</code>
   * @return The timeZone.
   */
  @java.lang.Override
  public java.lang.String getTimeZone() {
    java.lang.Object ref = timeZone_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      timeZone_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *时区信息
   * </pre>
   *
   * <code>string timeZone = 38;</code>
   * @return The bytes for timeZone.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTimeZoneBytes() {
    java.lang.Object ref = timeZone_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      timeZone_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int UPLOAD_TIME_FIELD_NUMBER = 39;
  private volatile java.lang.Object uploadTime_;
  /**
   * <pre>
   *上传时间
   * </pre>
   *
   * <code>string upload_time = 39;</code>
   * @return The uploadTime.
   */
  @java.lang.Override
  public java.lang.String getUploadTime() {
    java.lang.Object ref = uploadTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      uploadTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *上传时间
   * </pre>
   *
   * <code>string upload_time = 39;</code>
   * @return The bytes for uploadTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUploadTimeBytes() {
    java.lang.Object ref = uploadTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      uploadTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DISCOUNT_PROPORTIONED_FIELD_NUMBER = 40;
  private boolean discountProportioned_;
  /**
   * <pre>
   * 是否折扣分摊
   * </pre>
   *
   * <code>bool discount_proportioned = 40;</code>
   * @return The discountProportioned.
   */
  @java.lang.Override
  public boolean getDiscountProportioned() {
    return discountProportioned_;
  }

  public static final int TRANSACTION_NO_FIELD_NUMBER = 41;
  private volatile java.lang.Object transactionNo_;
  /**
   * <code>string transaction_no = 41;</code>
   * @return The transactionNo.
   */
  @java.lang.Override
  public java.lang.String getTransactionNo() {
    java.lang.Object ref = transactionNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      transactionNo_ = s;
      return s;
    }
  }
  /**
   * <code>string transaction_no = 41;</code>
   * @return The bytes for transactionNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTransactionNoBytes() {
    java.lang.Object ref = transactionNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      transactionNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int FEES_NO_ACCOUNT_FIELD_NUMBER = 42;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee> feesNoAccount_;
  /**
   * <pre>
   * 无需用户额外支付的费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees_no_account = 42;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee> getFeesNoAccountList() {
    return feesNoAccount_;
  }
  /**
   * <pre>
   * 无需用户额外支付的费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees_no_account = 42;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder> 
      getFeesNoAccountOrBuilderList() {
    return feesNoAccount_;
  }
  /**
   * <pre>
   * 无需用户额外支付的费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees_no_account = 42;</code>
   */
  @java.lang.Override
  public int getFeesNoAccountCount() {
    return feesNoAccount_.size();
  }
  /**
   * <pre>
   * 无需用户额外支付的费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees_no_account = 42;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Fee getFeesNoAccount(int index) {
    return feesNoAccount_.get(index);
  }
  /**
   * <pre>
   * 无需用户额外支付的费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees_no_account = 42;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder getFeesNoAccountOrBuilder(
      int index) {
    return feesNoAccount_.get(index);
  }

  public static final int EFFICIENCY_FIELD_NUMBER = 43;
  private cn.hexcloud.pbis.common.service.facade.ticket.Efficiency efficiency_;
  /**
   * <code>.coupon.Efficiency efficiency = 43;</code>
   * @return Whether the efficiency field is set.
   */
  @java.lang.Override
  public boolean hasEfficiency() {
    return efficiency_ != null;
  }
  /**
   * <code>.coupon.Efficiency efficiency = 43;</code>
   * @return The efficiency.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Efficiency getEfficiency() {
    return efficiency_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Efficiency.getDefaultInstance() : efficiency_;
  }
  /**
   * <code>.coupon.Efficiency efficiency = 43;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.EfficiencyOrBuilder getEfficiencyOrBuilder() {
    return getEfficiency();
  }

  public static final int WEIGHT_FIELD_NUMBER = 45;
  private float weight_;
  /**
   * <pre>
   * 称重商品总重量(kg)
   * </pre>
   *
   * <code>float weight = 45;</code>
   * @return The weight.
   */
  @java.lang.Override
  public float getWeight() {
    return weight_;
  }

  public static final int PARTNER_ID_FIELD_NUMBER = 44;
  private long partnerId_;
  /**
   * <pre>
   * 门店partner id
   * </pre>
   *
   * <code>uint64 partner_id = 44;</code>
   * @return The partnerId.
   */
  @java.lang.Override
  public long getPartnerId() {
    return partnerId_;
  }

  public static final int PENDING_SYNC_MEMBER_FIELD_NUMBER = 46;
  private boolean pendingSyncMember_;
  /**
   * <pre>
   * 是否待同步会员单
   * </pre>
   *
   * <code>bool pending_sync_member = 46;</code>
   * @return The pendingSyncMember.
   */
  @java.lang.Override
  public boolean getPendingSyncMember() {
    return pendingSyncMember_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getTicketIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, ticketId_);
    }
    if (!getTicketNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, ticketNo_);
    }
    if (!getStartTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, startTime_);
    }
    if (!getEndTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, endTime_);
    }
    if (!getBusDateBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, busDate_);
    }
    if (pos_ != null) {
      output.writeMessage(6, getPos());
    }
    if (operator_ != null) {
      output.writeMessage(7, getOperator());
    }
    if (amounts_ != null) {
      output.writeMessage(8, getAmounts());
    }
    if (!getTakemealNumberBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, takemealNumber_);
    }
    if (qty_ != 0) {
      output.writeInt32(10, qty_);
    }
    if (!getStatusBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, status_);
    }
    if (refundInfo_ != null) {
      output.writeMessage(12, getRefundInfo());
    }
    if (channel_ != null) {
      output.writeMessage(13, getChannel());
    }
    for (int i = 0; i < products_.size(); i++) {
      output.writeMessage(14, products_.get(i));
    }
    for (int i = 0; i < payments_.size(); i++) {
      output.writeMessage(15, payments_.get(i));
    }
    for (int i = 0; i < promotions_.size(); i++) {
      output.writeMessage(16, promotions_.get(i));
    }
    for (int i = 0; i < members_.size(); i++) {
      output.writeMessage(17, members_.get(i));
    }
    if (table_ != null) {
      output.writeMessage(18, getTable());
    }
    if (people_ != 0) {
      output.writeInt32(19, people_);
    }
    if (!getRoomNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 20, roomNo_);
    }
    if (!getRemarkBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 21, remark_);
    }
    if (houseAc_ != false) {
      output.writeBool(22, houseAc_);
    }
    if (!getOrderTimeTypeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 23, orderTimeType_);
    }
    if (!getShiftNumberBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 24, shiftNumber_);
    }
    for (int i = 0; i < taxList_.size(); i++) {
      output.writeMessage(25, taxList_.get(i));
    }
    if (store_ != null) {
      output.writeMessage(26, getStore());
    }
    if (takeawayInfo_ != null) {
      output.writeMessage(34, getTakeawayInfo());
    }
    if (!getTicketUnoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 35, ticketUno_);
    }
    for (int i = 0; i < coupons_.size(); i++) {
      output.writeMessage(36, coupons_.get(i));
    }
    for (int i = 0; i < fees_.size(); i++) {
      output.writeMessage(37, fees_.get(i));
    }
    if (!getTimeZoneBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 38, timeZone_);
    }
    if (!getUploadTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 39, uploadTime_);
    }
    if (discountProportioned_ != false) {
      output.writeBool(40, discountProportioned_);
    }
    if (!getTransactionNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 41, transactionNo_);
    }
    for (int i = 0; i < feesNoAccount_.size(); i++) {
      output.writeMessage(42, feesNoAccount_.get(i));
    }
    if (efficiency_ != null) {
      output.writeMessage(43, getEfficiency());
    }
    if (partnerId_ != 0L) {
      output.writeUInt64(44, partnerId_);
    }
    if (weight_ != 0F) {
      output.writeFloat(45, weight_);
    }
    if (pendingSyncMember_ != false) {
      output.writeBool(46, pendingSyncMember_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getTicketIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, ticketId_);
    }
    if (!getTicketNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, ticketNo_);
    }
    if (!getStartTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, startTime_);
    }
    if (!getEndTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, endTime_);
    }
    if (!getBusDateBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, busDate_);
    }
    if (pos_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(6, getPos());
    }
    if (operator_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, getOperator());
    }
    if (amounts_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, getAmounts());
    }
    if (!getTakemealNumberBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, takemealNumber_);
    }
    if (qty_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(10, qty_);
    }
    if (!getStatusBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, status_);
    }
    if (refundInfo_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(12, getRefundInfo());
    }
    if (channel_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(13, getChannel());
    }
    for (int i = 0; i < products_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(14, products_.get(i));
    }
    for (int i = 0; i < payments_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(15, payments_.get(i));
    }
    for (int i = 0; i < promotions_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(16, promotions_.get(i));
    }
    for (int i = 0; i < members_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(17, members_.get(i));
    }
    if (table_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(18, getTable());
    }
    if (people_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(19, people_);
    }
    if (!getRoomNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(20, roomNo_);
    }
    if (!getRemarkBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(21, remark_);
    }
    if (houseAc_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(22, houseAc_);
    }
    if (!getOrderTimeTypeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(23, orderTimeType_);
    }
    if (!getShiftNumberBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(24, shiftNumber_);
    }
    for (int i = 0; i < taxList_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(25, taxList_.get(i));
    }
    if (store_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(26, getStore());
    }
    if (takeawayInfo_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(34, getTakeawayInfo());
    }
    if (!getTicketUnoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(35, ticketUno_);
    }
    for (int i = 0; i < coupons_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(36, coupons_.get(i));
    }
    for (int i = 0; i < fees_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(37, fees_.get(i));
    }
    if (!getTimeZoneBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(38, timeZone_);
    }
    if (!getUploadTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(39, uploadTime_);
    }
    if (discountProportioned_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(40, discountProportioned_);
    }
    if (!getTransactionNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(41, transactionNo_);
    }
    for (int i = 0; i < feesNoAccount_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(42, feesNoAccount_.get(i));
    }
    if (efficiency_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(43, getEfficiency());
    }
    if (partnerId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(44, partnerId_);
    }
    if (weight_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(45, weight_);
    }
    if (pendingSyncMember_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(46, pendingSyncMember_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.ticket.Ticket)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.ticket.Ticket other = (cn.hexcloud.pbis.common.service.facade.ticket.Ticket) obj;

    if (!getTicketId()
        .equals(other.getTicketId())) return false;
    if (!getTicketNo()
        .equals(other.getTicketNo())) return false;
    if (!getStartTime()
        .equals(other.getStartTime())) return false;
    if (!getEndTime()
        .equals(other.getEndTime())) return false;
    if (!getBusDate()
        .equals(other.getBusDate())) return false;
    if (hasPos() != other.hasPos()) return false;
    if (hasPos()) {
      if (!getPos()
          .equals(other.getPos())) return false;
    }
    if (hasOperator() != other.hasOperator()) return false;
    if (hasOperator()) {
      if (!getOperator()
          .equals(other.getOperator())) return false;
    }
    if (hasAmounts() != other.hasAmounts()) return false;
    if (hasAmounts()) {
      if (!getAmounts()
          .equals(other.getAmounts())) return false;
    }
    if (!getTakemealNumber()
        .equals(other.getTakemealNumber())) return false;
    if (getQty()
        != other.getQty()) return false;
    if (!getStatus()
        .equals(other.getStatus())) return false;
    if (hasRefundInfo() != other.hasRefundInfo()) return false;
    if (hasRefundInfo()) {
      if (!getRefundInfo()
          .equals(other.getRefundInfo())) return false;
    }
    if (hasChannel() != other.hasChannel()) return false;
    if (hasChannel()) {
      if (!getChannel()
          .equals(other.getChannel())) return false;
    }
    if (!getProductsList()
        .equals(other.getProductsList())) return false;
    if (!getPaymentsList()
        .equals(other.getPaymentsList())) return false;
    if (!getPromotionsList()
        .equals(other.getPromotionsList())) return false;
    if (!getMembersList()
        .equals(other.getMembersList())) return false;
    if (hasTable() != other.hasTable()) return false;
    if (hasTable()) {
      if (!getTable()
          .equals(other.getTable())) return false;
    }
    if (getPeople()
        != other.getPeople()) return false;
    if (!getRoomNo()
        .equals(other.getRoomNo())) return false;
    if (!getRemark()
        .equals(other.getRemark())) return false;
    if (getHouseAc()
        != other.getHouseAc()) return false;
    if (!getOrderTimeType()
        .equals(other.getOrderTimeType())) return false;
    if (!getShiftNumber()
        .equals(other.getShiftNumber())) return false;
    if (!getTaxListList()
        .equals(other.getTaxListList())) return false;
    if (hasStore() != other.hasStore()) return false;
    if (hasStore()) {
      if (!getStore()
          .equals(other.getStore())) return false;
    }
    if (hasTakeawayInfo() != other.hasTakeawayInfo()) return false;
    if (hasTakeawayInfo()) {
      if (!getTakeawayInfo()
          .equals(other.getTakeawayInfo())) return false;
    }
    if (!getTicketUno()
        .equals(other.getTicketUno())) return false;
    if (!getCouponsList()
        .equals(other.getCouponsList())) return false;
    if (!getFeesList()
        .equals(other.getFeesList())) return false;
    if (!getTimeZone()
        .equals(other.getTimeZone())) return false;
    if (!getUploadTime()
        .equals(other.getUploadTime())) return false;
    if (getDiscountProportioned()
        != other.getDiscountProportioned()) return false;
    if (!getTransactionNo()
        .equals(other.getTransactionNo())) return false;
    if (!getFeesNoAccountList()
        .equals(other.getFeesNoAccountList())) return false;
    if (hasEfficiency() != other.hasEfficiency()) return false;
    if (hasEfficiency()) {
      if (!getEfficiency()
          .equals(other.getEfficiency())) return false;
    }
    if (java.lang.Float.floatToIntBits(getWeight())
        != java.lang.Float.floatToIntBits(
            other.getWeight())) return false;
    if (getPartnerId()
        != other.getPartnerId()) return false;
    if (getPendingSyncMember()
        != other.getPendingSyncMember()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TICKET_ID_FIELD_NUMBER;
    hash = (53 * hash) + getTicketId().hashCode();
    hash = (37 * hash) + TICKET_NO_FIELD_NUMBER;
    hash = (53 * hash) + getTicketNo().hashCode();
    hash = (37 * hash) + START_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getStartTime().hashCode();
    hash = (37 * hash) + END_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getEndTime().hashCode();
    hash = (37 * hash) + BUS_DATE_FIELD_NUMBER;
    hash = (53 * hash) + getBusDate().hashCode();
    if (hasPos()) {
      hash = (37 * hash) + POS_FIELD_NUMBER;
      hash = (53 * hash) + getPos().hashCode();
    }
    if (hasOperator()) {
      hash = (37 * hash) + OPERATOR_FIELD_NUMBER;
      hash = (53 * hash) + getOperator().hashCode();
    }
    if (hasAmounts()) {
      hash = (37 * hash) + AMOUNTS_FIELD_NUMBER;
      hash = (53 * hash) + getAmounts().hashCode();
    }
    hash = (37 * hash) + TAKEMEALNUMBER_FIELD_NUMBER;
    hash = (53 * hash) + getTakemealNumber().hashCode();
    hash = (37 * hash) + QTY_FIELD_NUMBER;
    hash = (53 * hash) + getQty();
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + getStatus().hashCode();
    if (hasRefundInfo()) {
      hash = (37 * hash) + REFUNDINFO_FIELD_NUMBER;
      hash = (53 * hash) + getRefundInfo().hashCode();
    }
    if (hasChannel()) {
      hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
      hash = (53 * hash) + getChannel().hashCode();
    }
    if (getProductsCount() > 0) {
      hash = (37 * hash) + PRODUCTS_FIELD_NUMBER;
      hash = (53 * hash) + getProductsList().hashCode();
    }
    if (getPaymentsCount() > 0) {
      hash = (37 * hash) + PAYMENTS_FIELD_NUMBER;
      hash = (53 * hash) + getPaymentsList().hashCode();
    }
    if (getPromotionsCount() > 0) {
      hash = (37 * hash) + PROMOTIONS_FIELD_NUMBER;
      hash = (53 * hash) + getPromotionsList().hashCode();
    }
    if (getMembersCount() > 0) {
      hash = (37 * hash) + MEMBERS_FIELD_NUMBER;
      hash = (53 * hash) + getMembersList().hashCode();
    }
    if (hasTable()) {
      hash = (37 * hash) + TABLE_FIELD_NUMBER;
      hash = (53 * hash) + getTable().hashCode();
    }
    hash = (37 * hash) + PEOPLE_FIELD_NUMBER;
    hash = (53 * hash) + getPeople();
    hash = (37 * hash) + ROOM_NO_FIELD_NUMBER;
    hash = (53 * hash) + getRoomNo().hashCode();
    hash = (37 * hash) + REMARK_FIELD_NUMBER;
    hash = (53 * hash) + getRemark().hashCode();
    hash = (37 * hash) + HOUSE_AC_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getHouseAc());
    hash = (37 * hash) + ORDER_TIME_TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getOrderTimeType().hashCode();
    hash = (37 * hash) + SHIFTNUMBER_FIELD_NUMBER;
    hash = (53 * hash) + getShiftNumber().hashCode();
    if (getTaxListCount() > 0) {
      hash = (37 * hash) + TAXLIST_FIELD_NUMBER;
      hash = (53 * hash) + getTaxListList().hashCode();
    }
    if (hasStore()) {
      hash = (37 * hash) + STORE_FIELD_NUMBER;
      hash = (53 * hash) + getStore().hashCode();
    }
    if (hasTakeawayInfo()) {
      hash = (37 * hash) + TAKEAWAY_INFO_FIELD_NUMBER;
      hash = (53 * hash) + getTakeawayInfo().hashCode();
    }
    hash = (37 * hash) + TICKETUNO_FIELD_NUMBER;
    hash = (53 * hash) + getTicketUno().hashCode();
    if (getCouponsCount() > 0) {
      hash = (37 * hash) + COUPONS_FIELD_NUMBER;
      hash = (53 * hash) + getCouponsList().hashCode();
    }
    if (getFeesCount() > 0) {
      hash = (37 * hash) + FEES_FIELD_NUMBER;
      hash = (53 * hash) + getFeesList().hashCode();
    }
    hash = (37 * hash) + TIMEZONE_FIELD_NUMBER;
    hash = (53 * hash) + getTimeZone().hashCode();
    hash = (37 * hash) + UPLOAD_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getUploadTime().hashCode();
    hash = (37 * hash) + DISCOUNT_PROPORTIONED_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getDiscountProportioned());
    hash = (37 * hash) + TRANSACTION_NO_FIELD_NUMBER;
    hash = (53 * hash) + getTransactionNo().hashCode();
    if (getFeesNoAccountCount() > 0) {
      hash = (37 * hash) + FEES_NO_ACCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getFeesNoAccountList().hashCode();
    }
    if (hasEfficiency()) {
      hash = (37 * hash) + EFFICIENCY_FIELD_NUMBER;
      hash = (53 * hash) + getEfficiency().hashCode();
    }
    hash = (37 * hash) + WEIGHT_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getWeight());
    hash = (37 * hash) + PARTNER_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getPartnerId());
    hash = (37 * hash) + PENDING_SYNC_MEMBER_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getPendingSyncMember());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.ticket.Ticket parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Ticket parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Ticket parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Ticket parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Ticket parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Ticket parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Ticket parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Ticket parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Ticket parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Ticket parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Ticket parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.Ticket parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.ticket.Ticket prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code coupon.Ticket}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:coupon.Ticket)
      cn.hexcloud.pbis.common.service.facade.ticket.TicketOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Ticket_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Ticket_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.ticket.Ticket.class, cn.hexcloud.pbis.common.service.facade.ticket.Ticket.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.ticket.Ticket.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getProductsFieldBuilder();
        getPaymentsFieldBuilder();
        getPromotionsFieldBuilder();
        getMembersFieldBuilder();
        getTaxListFieldBuilder();
        getCouponsFieldBuilder();
        getFeesFieldBuilder();
        getFeesNoAccountFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      ticketId_ = "";

      ticketNo_ = "";

      startTime_ = "";

      endTime_ = "";

      busDate_ = "";

      if (posBuilder_ == null) {
        pos_ = null;
      } else {
        pos_ = null;
        posBuilder_ = null;
      }
      if (operatorBuilder_ == null) {
        operator_ = null;
      } else {
        operator_ = null;
        operatorBuilder_ = null;
      }
      if (amountsBuilder_ == null) {
        amounts_ = null;
      } else {
        amounts_ = null;
        amountsBuilder_ = null;
      }
      takemealNumber_ = "";

      qty_ = 0;

      status_ = "";

      if (refundInfoBuilder_ == null) {
        refundInfo_ = null;
      } else {
        refundInfo_ = null;
        refundInfoBuilder_ = null;
      }
      if (channelBuilder_ == null) {
        channel_ = null;
      } else {
        channel_ = null;
        channelBuilder_ = null;
      }
      if (productsBuilder_ == null) {
        products_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        productsBuilder_.clear();
      }
      if (paymentsBuilder_ == null) {
        payments_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
      } else {
        paymentsBuilder_.clear();
      }
      if (promotionsBuilder_ == null) {
        promotions_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
      } else {
        promotionsBuilder_.clear();
      }
      if (membersBuilder_ == null) {
        members_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
      } else {
        membersBuilder_.clear();
      }
      if (tableBuilder_ == null) {
        table_ = null;
      } else {
        table_ = null;
        tableBuilder_ = null;
      }
      people_ = 0;

      roomNo_ = "";

      remark_ = "";

      houseAc_ = false;

      orderTimeType_ = "";

      shiftNumber_ = "";

      if (taxListBuilder_ == null) {
        taxList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
      } else {
        taxListBuilder_.clear();
      }
      if (storeBuilder_ == null) {
        store_ = null;
      } else {
        store_ = null;
        storeBuilder_ = null;
      }
      if (takeawayInfoBuilder_ == null) {
        takeawayInfo_ = null;
      } else {
        takeawayInfo_ = null;
        takeawayInfoBuilder_ = null;
      }
      ticketUno_ = "";

      if (couponsBuilder_ == null) {
        coupons_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
      } else {
        couponsBuilder_.clear();
      }
      if (feesBuilder_ == null) {
        fees_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
      } else {
        feesBuilder_.clear();
      }
      timeZone_ = "";

      uploadTime_ = "";

      discountProportioned_ = false;

      transactionNo_ = "";

      if (feesNoAccountBuilder_ == null) {
        feesNoAccount_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
      } else {
        feesNoAccountBuilder_.clear();
      }
      if (efficiencyBuilder_ == null) {
        efficiency_ = null;
      } else {
        efficiency_ = null;
        efficiencyBuilder_ = null;
      }
      weight_ = 0F;

      partnerId_ = 0L;

      pendingSyncMember_ = false;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_Ticket_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.Ticket getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.ticket.Ticket.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.Ticket build() {
      cn.hexcloud.pbis.common.service.facade.ticket.Ticket result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.Ticket buildPartial() {
      cn.hexcloud.pbis.common.service.facade.ticket.Ticket result = new cn.hexcloud.pbis.common.service.facade.ticket.Ticket(this);
      int from_bitField0_ = bitField0_;
      result.ticketId_ = ticketId_;
      result.ticketNo_ = ticketNo_;
      result.startTime_ = startTime_;
      result.endTime_ = endTime_;
      result.busDate_ = busDate_;
      if (posBuilder_ == null) {
        result.pos_ = pos_;
      } else {
        result.pos_ = posBuilder_.build();
      }
      if (operatorBuilder_ == null) {
        result.operator_ = operator_;
      } else {
        result.operator_ = operatorBuilder_.build();
      }
      if (amountsBuilder_ == null) {
        result.amounts_ = amounts_;
      } else {
        result.amounts_ = amountsBuilder_.build();
      }
      result.takemealNumber_ = takemealNumber_;
      result.qty_ = qty_;
      result.status_ = status_;
      if (refundInfoBuilder_ == null) {
        result.refundInfo_ = refundInfo_;
      } else {
        result.refundInfo_ = refundInfoBuilder_.build();
      }
      if (channelBuilder_ == null) {
        result.channel_ = channel_;
      } else {
        result.channel_ = channelBuilder_.build();
      }
      if (productsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          products_ = java.util.Collections.unmodifiableList(products_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.products_ = products_;
      } else {
        result.products_ = productsBuilder_.build();
      }
      if (paymentsBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          payments_ = java.util.Collections.unmodifiableList(payments_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.payments_ = payments_;
      } else {
        result.payments_ = paymentsBuilder_.build();
      }
      if (promotionsBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          promotions_ = java.util.Collections.unmodifiableList(promotions_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.promotions_ = promotions_;
      } else {
        result.promotions_ = promotionsBuilder_.build();
      }
      if (membersBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0)) {
          members_ = java.util.Collections.unmodifiableList(members_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.members_ = members_;
      } else {
        result.members_ = membersBuilder_.build();
      }
      if (tableBuilder_ == null) {
        result.table_ = table_;
      } else {
        result.table_ = tableBuilder_.build();
      }
      result.people_ = people_;
      result.roomNo_ = roomNo_;
      result.remark_ = remark_;
      result.houseAc_ = houseAc_;
      result.orderTimeType_ = orderTimeType_;
      result.shiftNumber_ = shiftNumber_;
      if (taxListBuilder_ == null) {
        if (((bitField0_ & 0x00000010) != 0)) {
          taxList_ = java.util.Collections.unmodifiableList(taxList_);
          bitField0_ = (bitField0_ & ~0x00000010);
        }
        result.taxList_ = taxList_;
      } else {
        result.taxList_ = taxListBuilder_.build();
      }
      if (storeBuilder_ == null) {
        result.store_ = store_;
      } else {
        result.store_ = storeBuilder_.build();
      }
      if (takeawayInfoBuilder_ == null) {
        result.takeawayInfo_ = takeawayInfo_;
      } else {
        result.takeawayInfo_ = takeawayInfoBuilder_.build();
      }
      result.ticketUno_ = ticketUno_;
      if (couponsBuilder_ == null) {
        if (((bitField0_ & 0x00000020) != 0)) {
          coupons_ = java.util.Collections.unmodifiableList(coupons_);
          bitField0_ = (bitField0_ & ~0x00000020);
        }
        result.coupons_ = coupons_;
      } else {
        result.coupons_ = couponsBuilder_.build();
      }
      if (feesBuilder_ == null) {
        if (((bitField0_ & 0x00000040) != 0)) {
          fees_ = java.util.Collections.unmodifiableList(fees_);
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.fees_ = fees_;
      } else {
        result.fees_ = feesBuilder_.build();
      }
      result.timeZone_ = timeZone_;
      result.uploadTime_ = uploadTime_;
      result.discountProportioned_ = discountProportioned_;
      result.transactionNo_ = transactionNo_;
      if (feesNoAccountBuilder_ == null) {
        if (((bitField0_ & 0x00000080) != 0)) {
          feesNoAccount_ = java.util.Collections.unmodifiableList(feesNoAccount_);
          bitField0_ = (bitField0_ & ~0x00000080);
        }
        result.feesNoAccount_ = feesNoAccount_;
      } else {
        result.feesNoAccount_ = feesNoAccountBuilder_.build();
      }
      if (efficiencyBuilder_ == null) {
        result.efficiency_ = efficiency_;
      } else {
        result.efficiency_ = efficiencyBuilder_.build();
      }
      result.weight_ = weight_;
      result.partnerId_ = partnerId_;
      result.pendingSyncMember_ = pendingSyncMember_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.ticket.Ticket) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.ticket.Ticket)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.ticket.Ticket other) {
      if (other == cn.hexcloud.pbis.common.service.facade.ticket.Ticket.getDefaultInstance()) return this;
      if (!other.getTicketId().isEmpty()) {
        ticketId_ = other.ticketId_;
        onChanged();
      }
      if (!other.getTicketNo().isEmpty()) {
        ticketNo_ = other.ticketNo_;
        onChanged();
      }
      if (!other.getStartTime().isEmpty()) {
        startTime_ = other.startTime_;
        onChanged();
      }
      if (!other.getEndTime().isEmpty()) {
        endTime_ = other.endTime_;
        onChanged();
      }
      if (!other.getBusDate().isEmpty()) {
        busDate_ = other.busDate_;
        onChanged();
      }
      if (other.hasPos()) {
        mergePos(other.getPos());
      }
      if (other.hasOperator()) {
        mergeOperator(other.getOperator());
      }
      if (other.hasAmounts()) {
        mergeAmounts(other.getAmounts());
      }
      if (!other.getTakemealNumber().isEmpty()) {
        takemealNumber_ = other.takemealNumber_;
        onChanged();
      }
      if (other.getQty() != 0) {
        setQty(other.getQty());
      }
      if (!other.getStatus().isEmpty()) {
        status_ = other.status_;
        onChanged();
      }
      if (other.hasRefundInfo()) {
        mergeRefundInfo(other.getRefundInfo());
      }
      if (other.hasChannel()) {
        mergeChannel(other.getChannel());
      }
      if (productsBuilder_ == null) {
        if (!other.products_.isEmpty()) {
          if (products_.isEmpty()) {
            products_ = other.products_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureProductsIsMutable();
            products_.addAll(other.products_);
          }
          onChanged();
        }
      } else {
        if (!other.products_.isEmpty()) {
          if (productsBuilder_.isEmpty()) {
            productsBuilder_.dispose();
            productsBuilder_ = null;
            products_ = other.products_;
            bitField0_ = (bitField0_ & ~0x00000001);
            productsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getProductsFieldBuilder() : null;
          } else {
            productsBuilder_.addAllMessages(other.products_);
          }
        }
      }
      if (paymentsBuilder_ == null) {
        if (!other.payments_.isEmpty()) {
          if (payments_.isEmpty()) {
            payments_ = other.payments_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensurePaymentsIsMutable();
            payments_.addAll(other.payments_);
          }
          onChanged();
        }
      } else {
        if (!other.payments_.isEmpty()) {
          if (paymentsBuilder_.isEmpty()) {
            paymentsBuilder_.dispose();
            paymentsBuilder_ = null;
            payments_ = other.payments_;
            bitField0_ = (bitField0_ & ~0x00000002);
            paymentsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getPaymentsFieldBuilder() : null;
          } else {
            paymentsBuilder_.addAllMessages(other.payments_);
          }
        }
      }
      if (promotionsBuilder_ == null) {
        if (!other.promotions_.isEmpty()) {
          if (promotions_.isEmpty()) {
            promotions_ = other.promotions_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensurePromotionsIsMutable();
            promotions_.addAll(other.promotions_);
          }
          onChanged();
        }
      } else {
        if (!other.promotions_.isEmpty()) {
          if (promotionsBuilder_.isEmpty()) {
            promotionsBuilder_.dispose();
            promotionsBuilder_ = null;
            promotions_ = other.promotions_;
            bitField0_ = (bitField0_ & ~0x00000004);
            promotionsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getPromotionsFieldBuilder() : null;
          } else {
            promotionsBuilder_.addAllMessages(other.promotions_);
          }
        }
      }
      if (membersBuilder_ == null) {
        if (!other.members_.isEmpty()) {
          if (members_.isEmpty()) {
            members_ = other.members_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureMembersIsMutable();
            members_.addAll(other.members_);
          }
          onChanged();
        }
      } else {
        if (!other.members_.isEmpty()) {
          if (membersBuilder_.isEmpty()) {
            membersBuilder_.dispose();
            membersBuilder_ = null;
            members_ = other.members_;
            bitField0_ = (bitField0_ & ~0x00000008);
            membersBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getMembersFieldBuilder() : null;
          } else {
            membersBuilder_.addAllMessages(other.members_);
          }
        }
      }
      if (other.hasTable()) {
        mergeTable(other.getTable());
      }
      if (other.getPeople() != 0) {
        setPeople(other.getPeople());
      }
      if (!other.getRoomNo().isEmpty()) {
        roomNo_ = other.roomNo_;
        onChanged();
      }
      if (!other.getRemark().isEmpty()) {
        remark_ = other.remark_;
        onChanged();
      }
      if (other.getHouseAc() != false) {
        setHouseAc(other.getHouseAc());
      }
      if (!other.getOrderTimeType().isEmpty()) {
        orderTimeType_ = other.orderTimeType_;
        onChanged();
      }
      if (!other.getShiftNumber().isEmpty()) {
        shiftNumber_ = other.shiftNumber_;
        onChanged();
      }
      if (taxListBuilder_ == null) {
        if (!other.taxList_.isEmpty()) {
          if (taxList_.isEmpty()) {
            taxList_ = other.taxList_;
            bitField0_ = (bitField0_ & ~0x00000010);
          } else {
            ensureTaxListIsMutable();
            taxList_.addAll(other.taxList_);
          }
          onChanged();
        }
      } else {
        if (!other.taxList_.isEmpty()) {
          if (taxListBuilder_.isEmpty()) {
            taxListBuilder_.dispose();
            taxListBuilder_ = null;
            taxList_ = other.taxList_;
            bitField0_ = (bitField0_ & ~0x00000010);
            taxListBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getTaxListFieldBuilder() : null;
          } else {
            taxListBuilder_.addAllMessages(other.taxList_);
          }
        }
      }
      if (other.hasStore()) {
        mergeStore(other.getStore());
      }
      if (other.hasTakeawayInfo()) {
        mergeTakeawayInfo(other.getTakeawayInfo());
      }
      if (!other.getTicketUno().isEmpty()) {
        ticketUno_ = other.ticketUno_;
        onChanged();
      }
      if (couponsBuilder_ == null) {
        if (!other.coupons_.isEmpty()) {
          if (coupons_.isEmpty()) {
            coupons_ = other.coupons_;
            bitField0_ = (bitField0_ & ~0x00000020);
          } else {
            ensureCouponsIsMutable();
            coupons_.addAll(other.coupons_);
          }
          onChanged();
        }
      } else {
        if (!other.coupons_.isEmpty()) {
          if (couponsBuilder_.isEmpty()) {
            couponsBuilder_.dispose();
            couponsBuilder_ = null;
            coupons_ = other.coupons_;
            bitField0_ = (bitField0_ & ~0x00000020);
            couponsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getCouponsFieldBuilder() : null;
          } else {
            couponsBuilder_.addAllMessages(other.coupons_);
          }
        }
      }
      if (feesBuilder_ == null) {
        if (!other.fees_.isEmpty()) {
          if (fees_.isEmpty()) {
            fees_ = other.fees_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureFeesIsMutable();
            fees_.addAll(other.fees_);
          }
          onChanged();
        }
      } else {
        if (!other.fees_.isEmpty()) {
          if (feesBuilder_.isEmpty()) {
            feesBuilder_.dispose();
            feesBuilder_ = null;
            fees_ = other.fees_;
            bitField0_ = (bitField0_ & ~0x00000040);
            feesBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getFeesFieldBuilder() : null;
          } else {
            feesBuilder_.addAllMessages(other.fees_);
          }
        }
      }
      if (!other.getTimeZone().isEmpty()) {
        timeZone_ = other.timeZone_;
        onChanged();
      }
      if (!other.getUploadTime().isEmpty()) {
        uploadTime_ = other.uploadTime_;
        onChanged();
      }
      if (other.getDiscountProportioned() != false) {
        setDiscountProportioned(other.getDiscountProportioned());
      }
      if (!other.getTransactionNo().isEmpty()) {
        transactionNo_ = other.transactionNo_;
        onChanged();
      }
      if (feesNoAccountBuilder_ == null) {
        if (!other.feesNoAccount_.isEmpty()) {
          if (feesNoAccount_.isEmpty()) {
            feesNoAccount_ = other.feesNoAccount_;
            bitField0_ = (bitField0_ & ~0x00000080);
          } else {
            ensureFeesNoAccountIsMutable();
            feesNoAccount_.addAll(other.feesNoAccount_);
          }
          onChanged();
        }
      } else {
        if (!other.feesNoAccount_.isEmpty()) {
          if (feesNoAccountBuilder_.isEmpty()) {
            feesNoAccountBuilder_.dispose();
            feesNoAccountBuilder_ = null;
            feesNoAccount_ = other.feesNoAccount_;
            bitField0_ = (bitField0_ & ~0x00000080);
            feesNoAccountBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getFeesNoAccountFieldBuilder() : null;
          } else {
            feesNoAccountBuilder_.addAllMessages(other.feesNoAccount_);
          }
        }
      }
      if (other.hasEfficiency()) {
        mergeEfficiency(other.getEfficiency());
      }
      if (other.getWeight() != 0F) {
        setWeight(other.getWeight());
      }
      if (other.getPartnerId() != 0L) {
        setPartnerId(other.getPartnerId());
      }
      if (other.getPendingSyncMember() != false) {
        setPendingSyncMember(other.getPendingSyncMember());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.ticket.Ticket parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.ticket.Ticket) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object ticketId_ = "";
    /**
     * <pre>
     *订单uuid，全市场范围内唯一
     * </pre>
     *
     * <code>string ticket_id = 1;</code>
     * @return The ticketId.
     */
    public java.lang.String getTicketId() {
      java.lang.Object ref = ticketId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ticketId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *订单uuid，全市场范围内唯一
     * </pre>
     *
     * <code>string ticket_id = 1;</code>
     * @return The bytes for ticketId.
     */
    public com.google.protobuf.ByteString
        getTicketIdBytes() {
      java.lang.Object ref = ticketId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ticketId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *订单uuid，全市场范围内唯一
     * </pre>
     *
     * <code>string ticket_id = 1;</code>
     * @param value The ticketId to set.
     * @return This builder for chaining.
     */
    public Builder setTicketId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      ticketId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单uuid，全市场范围内唯一
     * </pre>
     *
     * <code>string ticket_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearTicketId() {
      
      ticketId_ = getDefaultInstance().getTicketId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单uuid，全市场范围内唯一
     * </pre>
     *
     * <code>string ticket_id = 1;</code>
     * @param value The bytes for ticketId to set.
     * @return This builder for chaining.
     */
    public Builder setTicketIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      ticketId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object ticketNo_ = "";
    /**
     * <pre>
     *订单号，有特殊的业务规则
     * </pre>
     *
     * <code>string ticket_no = 2;</code>
     * @return The ticketNo.
     */
    public java.lang.String getTicketNo() {
      java.lang.Object ref = ticketNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ticketNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *订单号，有特殊的业务规则
     * </pre>
     *
     * <code>string ticket_no = 2;</code>
     * @return The bytes for ticketNo.
     */
    public com.google.protobuf.ByteString
        getTicketNoBytes() {
      java.lang.Object ref = ticketNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ticketNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *订单号，有特殊的业务规则
     * </pre>
     *
     * <code>string ticket_no = 2;</code>
     * @param value The ticketNo to set.
     * @return This builder for chaining.
     */
    public Builder setTicketNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      ticketNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单号，有特殊的业务规则
     * </pre>
     *
     * <code>string ticket_no = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearTicketNo() {
      
      ticketNo_ = getDefaultInstance().getTicketNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单号，有特殊的业务规则
     * </pre>
     *
     * <code>string ticket_no = 2;</code>
     * @param value The bytes for ticketNo to set.
     * @return This builder for chaining.
     */
    public Builder setTicketNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      ticketNo_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object startTime_ = "";
    /**
     * <pre>
     *YYYY-MM-dd HH:MM:SS，订单开始时间
     * </pre>
     *
     * <code>string start_time = 3;</code>
     * @return The startTime.
     */
    public java.lang.String getStartTime() {
      java.lang.Object ref = startTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        startTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *YYYY-MM-dd HH:MM:SS，订单开始时间
     * </pre>
     *
     * <code>string start_time = 3;</code>
     * @return The bytes for startTime.
     */
    public com.google.protobuf.ByteString
        getStartTimeBytes() {
      java.lang.Object ref = startTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        startTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *YYYY-MM-dd HH:MM:SS，订单开始时间
     * </pre>
     *
     * <code>string start_time = 3;</code>
     * @param value The startTime to set.
     * @return This builder for chaining.
     */
    public Builder setStartTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      startTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *YYYY-MM-dd HH:MM:SS，订单开始时间
     * </pre>
     *
     * <code>string start_time = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearStartTime() {
      
      startTime_ = getDefaultInstance().getStartTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *YYYY-MM-dd HH:MM:SS，订单开始时间
     * </pre>
     *
     * <code>string start_time = 3;</code>
     * @param value The bytes for startTime to set.
     * @return This builder for chaining.
     */
    public Builder setStartTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      startTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object endTime_ = "";
    /**
     * <pre>
     *YYYY-MM-dd HH:MM:SS，订单结束时间
     * </pre>
     *
     * <code>string end_time = 4;</code>
     * @return The endTime.
     */
    public java.lang.String getEndTime() {
      java.lang.Object ref = endTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        endTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *YYYY-MM-dd HH:MM:SS，订单结束时间
     * </pre>
     *
     * <code>string end_time = 4;</code>
     * @return The bytes for endTime.
     */
    public com.google.protobuf.ByteString
        getEndTimeBytes() {
      java.lang.Object ref = endTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        endTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *YYYY-MM-dd HH:MM:SS，订单结束时间
     * </pre>
     *
     * <code>string end_time = 4;</code>
     * @param value The endTime to set.
     * @return This builder for chaining.
     */
    public Builder setEndTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      endTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *YYYY-MM-dd HH:MM:SS，订单结束时间
     * </pre>
     *
     * <code>string end_time = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearEndTime() {
      
      endTime_ = getDefaultInstance().getEndTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *YYYY-MM-dd HH:MM:SS，订单结束时间
     * </pre>
     *
     * <code>string end_time = 4;</code>
     * @param value The bytes for endTime to set.
     * @return This builder for chaining.
     */
    public Builder setEndTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      endTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object busDate_ = "";
    /**
     * <pre>
     *YYYY-MM-dd，订单营业日期
     * </pre>
     *
     * <code>string bus_date = 5;</code>
     * @return The busDate.
     */
    public java.lang.String getBusDate() {
      java.lang.Object ref = busDate_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        busDate_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *YYYY-MM-dd，订单营业日期
     * </pre>
     *
     * <code>string bus_date = 5;</code>
     * @return The bytes for busDate.
     */
    public com.google.protobuf.ByteString
        getBusDateBytes() {
      java.lang.Object ref = busDate_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        busDate_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *YYYY-MM-dd，订单营业日期
     * </pre>
     *
     * <code>string bus_date = 5;</code>
     * @param value The busDate to set.
     * @return This builder for chaining.
     */
    public Builder setBusDate(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      busDate_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *YYYY-MM-dd，订单营业日期
     * </pre>
     *
     * <code>string bus_date = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearBusDate() {
      
      busDate_ = getDefaultInstance().getBusDate();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *YYYY-MM-dd，订单营业日期
     * </pre>
     *
     * <code>string bus_date = 5;</code>
     * @param value The bytes for busDate to set.
     * @return This builder for chaining.
     */
    public Builder setBusDateBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      busDate_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.ticket.Pos pos_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Pos, cn.hexcloud.pbis.common.service.facade.ticket.Pos.Builder, cn.hexcloud.pbis.common.service.facade.ticket.PosOrBuilder> posBuilder_;
    /**
     * <pre>
     *pos信息
     * </pre>
     *
     * <code>.coupon.Pos pos = 6;</code>
     * @return Whether the pos field is set.
     */
    public boolean hasPos() {
      return posBuilder_ != null || pos_ != null;
    }
    /**
     * <pre>
     *pos信息
     * </pre>
     *
     * <code>.coupon.Pos pos = 6;</code>
     * @return The pos.
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Pos getPos() {
      if (posBuilder_ == null) {
        return pos_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Pos.getDefaultInstance() : pos_;
      } else {
        return posBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *pos信息
     * </pre>
     *
     * <code>.coupon.Pos pos = 6;</code>
     */
    public Builder setPos(cn.hexcloud.pbis.common.service.facade.ticket.Pos value) {
      if (posBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        pos_ = value;
        onChanged();
      } else {
        posBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *pos信息
     * </pre>
     *
     * <code>.coupon.Pos pos = 6;</code>
     */
    public Builder setPos(
        cn.hexcloud.pbis.common.service.facade.ticket.Pos.Builder builderForValue) {
      if (posBuilder_ == null) {
        pos_ = builderForValue.build();
        onChanged();
      } else {
        posBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *pos信息
     * </pre>
     *
     * <code>.coupon.Pos pos = 6;</code>
     */
    public Builder mergePos(cn.hexcloud.pbis.common.service.facade.ticket.Pos value) {
      if (posBuilder_ == null) {
        if (pos_ != null) {
          pos_ =
            cn.hexcloud.pbis.common.service.facade.ticket.Pos.newBuilder(pos_).mergeFrom(value).buildPartial();
        } else {
          pos_ = value;
        }
        onChanged();
      } else {
        posBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *pos信息
     * </pre>
     *
     * <code>.coupon.Pos pos = 6;</code>
     */
    public Builder clearPos() {
      if (posBuilder_ == null) {
        pos_ = null;
        onChanged();
      } else {
        pos_ = null;
        posBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *pos信息
     * </pre>
     *
     * <code>.coupon.Pos pos = 6;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Pos.Builder getPosBuilder() {
      
      onChanged();
      return getPosFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *pos信息
     * </pre>
     *
     * <code>.coupon.Pos pos = 6;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.PosOrBuilder getPosOrBuilder() {
      if (posBuilder_ != null) {
        return posBuilder_.getMessageOrBuilder();
      } else {
        return pos_ == null ?
            cn.hexcloud.pbis.common.service.facade.ticket.Pos.getDefaultInstance() : pos_;
      }
    }
    /**
     * <pre>
     *pos信息
     * </pre>
     *
     * <code>.coupon.Pos pos = 6;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Pos, cn.hexcloud.pbis.common.service.facade.ticket.Pos.Builder, cn.hexcloud.pbis.common.service.facade.ticket.PosOrBuilder> 
        getPosFieldBuilder() {
      if (posBuilder_ == null) {
        posBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.Pos, cn.hexcloud.pbis.common.service.facade.ticket.Pos.Builder, cn.hexcloud.pbis.common.service.facade.ticket.PosOrBuilder>(
                getPos(),
                getParentForChildren(),
                isClean());
        pos_ = null;
      }
      return posBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.ticket.Operator operator_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Operator, cn.hexcloud.pbis.common.service.facade.ticket.Operator.Builder, cn.hexcloud.pbis.common.service.facade.ticket.OperatorOrBuilder> operatorBuilder_;
    /**
     * <pre>
     *收银员信息
     * </pre>
     *
     * <code>.coupon.Operator operator = 7;</code>
     * @return Whether the operator field is set.
     */
    public boolean hasOperator() {
      return operatorBuilder_ != null || operator_ != null;
    }
    /**
     * <pre>
     *收银员信息
     * </pre>
     *
     * <code>.coupon.Operator operator = 7;</code>
     * @return The operator.
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Operator getOperator() {
      if (operatorBuilder_ == null) {
        return operator_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Operator.getDefaultInstance() : operator_;
      } else {
        return operatorBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *收银员信息
     * </pre>
     *
     * <code>.coupon.Operator operator = 7;</code>
     */
    public Builder setOperator(cn.hexcloud.pbis.common.service.facade.ticket.Operator value) {
      if (operatorBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        operator_ = value;
        onChanged();
      } else {
        operatorBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *收银员信息
     * </pre>
     *
     * <code>.coupon.Operator operator = 7;</code>
     */
    public Builder setOperator(
        cn.hexcloud.pbis.common.service.facade.ticket.Operator.Builder builderForValue) {
      if (operatorBuilder_ == null) {
        operator_ = builderForValue.build();
        onChanged();
      } else {
        operatorBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *收银员信息
     * </pre>
     *
     * <code>.coupon.Operator operator = 7;</code>
     */
    public Builder mergeOperator(cn.hexcloud.pbis.common.service.facade.ticket.Operator value) {
      if (operatorBuilder_ == null) {
        if (operator_ != null) {
          operator_ =
            cn.hexcloud.pbis.common.service.facade.ticket.Operator.newBuilder(operator_).mergeFrom(value).buildPartial();
        } else {
          operator_ = value;
        }
        onChanged();
      } else {
        operatorBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *收银员信息
     * </pre>
     *
     * <code>.coupon.Operator operator = 7;</code>
     */
    public Builder clearOperator() {
      if (operatorBuilder_ == null) {
        operator_ = null;
        onChanged();
      } else {
        operator_ = null;
        operatorBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *收银员信息
     * </pre>
     *
     * <code>.coupon.Operator operator = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Operator.Builder getOperatorBuilder() {
      
      onChanged();
      return getOperatorFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *收银员信息
     * </pre>
     *
     * <code>.coupon.Operator operator = 7;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.OperatorOrBuilder getOperatorOrBuilder() {
      if (operatorBuilder_ != null) {
        return operatorBuilder_.getMessageOrBuilder();
      } else {
        return operator_ == null ?
            cn.hexcloud.pbis.common.service.facade.ticket.Operator.getDefaultInstance() : operator_;
      }
    }
    /**
     * <pre>
     *收银员信息
     * </pre>
     *
     * <code>.coupon.Operator operator = 7;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Operator, cn.hexcloud.pbis.common.service.facade.ticket.Operator.Builder, cn.hexcloud.pbis.common.service.facade.ticket.OperatorOrBuilder> 
        getOperatorFieldBuilder() {
      if (operatorBuilder_ == null) {
        operatorBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.Operator, cn.hexcloud.pbis.common.service.facade.ticket.Operator.Builder, cn.hexcloud.pbis.common.service.facade.ticket.OperatorOrBuilder>(
                getOperator(),
                getParentForChildren(),
                isClean());
        operator_ = null;
      }
      return operatorBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.ticket.Amount amounts_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Amount, cn.hexcloud.pbis.common.service.facade.ticket.Amount.Builder, cn.hexcloud.pbis.common.service.facade.ticket.AmountOrBuilder> amountsBuilder_;
    /**
     * <pre>
     *金额信息
     * </pre>
     *
     * <code>.coupon.Amount amounts = 8;</code>
     * @return Whether the amounts field is set.
     */
    public boolean hasAmounts() {
      return amountsBuilder_ != null || amounts_ != null;
    }
    /**
     * <pre>
     *金额信息
     * </pre>
     *
     * <code>.coupon.Amount amounts = 8;</code>
     * @return The amounts.
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Amount getAmounts() {
      if (amountsBuilder_ == null) {
        return amounts_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Amount.getDefaultInstance() : amounts_;
      } else {
        return amountsBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *金额信息
     * </pre>
     *
     * <code>.coupon.Amount amounts = 8;</code>
     */
    public Builder setAmounts(cn.hexcloud.pbis.common.service.facade.ticket.Amount value) {
      if (amountsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        amounts_ = value;
        onChanged();
      } else {
        amountsBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *金额信息
     * </pre>
     *
     * <code>.coupon.Amount amounts = 8;</code>
     */
    public Builder setAmounts(
        cn.hexcloud.pbis.common.service.facade.ticket.Amount.Builder builderForValue) {
      if (amountsBuilder_ == null) {
        amounts_ = builderForValue.build();
        onChanged();
      } else {
        amountsBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *金额信息
     * </pre>
     *
     * <code>.coupon.Amount amounts = 8;</code>
     */
    public Builder mergeAmounts(cn.hexcloud.pbis.common.service.facade.ticket.Amount value) {
      if (amountsBuilder_ == null) {
        if (amounts_ != null) {
          amounts_ =
            cn.hexcloud.pbis.common.service.facade.ticket.Amount.newBuilder(amounts_).mergeFrom(value).buildPartial();
        } else {
          amounts_ = value;
        }
        onChanged();
      } else {
        amountsBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *金额信息
     * </pre>
     *
     * <code>.coupon.Amount amounts = 8;</code>
     */
    public Builder clearAmounts() {
      if (amountsBuilder_ == null) {
        amounts_ = null;
        onChanged();
      } else {
        amounts_ = null;
        amountsBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *金额信息
     * </pre>
     *
     * <code>.coupon.Amount amounts = 8;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Amount.Builder getAmountsBuilder() {
      
      onChanged();
      return getAmountsFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *金额信息
     * </pre>
     *
     * <code>.coupon.Amount amounts = 8;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.AmountOrBuilder getAmountsOrBuilder() {
      if (amountsBuilder_ != null) {
        return amountsBuilder_.getMessageOrBuilder();
      } else {
        return amounts_ == null ?
            cn.hexcloud.pbis.common.service.facade.ticket.Amount.getDefaultInstance() : amounts_;
      }
    }
    /**
     * <pre>
     *金额信息
     * </pre>
     *
     * <code>.coupon.Amount amounts = 8;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Amount, cn.hexcloud.pbis.common.service.facade.ticket.Amount.Builder, cn.hexcloud.pbis.common.service.facade.ticket.AmountOrBuilder> 
        getAmountsFieldBuilder() {
      if (amountsBuilder_ == null) {
        amountsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.Amount, cn.hexcloud.pbis.common.service.facade.ticket.Amount.Builder, cn.hexcloud.pbis.common.service.facade.ticket.AmountOrBuilder>(
                getAmounts(),
                getParentForChildren(),
                isClean());
        amounts_ = null;
      }
      return amountsBuilder_;
    }

    private java.lang.Object takemealNumber_ = "";
    /**
     * <pre>
     *取餐号
     * </pre>
     *
     * <code>string takemealNumber = 9;</code>
     * @return The takemealNumber.
     */
    public java.lang.String getTakemealNumber() {
      java.lang.Object ref = takemealNumber_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        takemealNumber_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *取餐号
     * </pre>
     *
     * <code>string takemealNumber = 9;</code>
     * @return The bytes for takemealNumber.
     */
    public com.google.protobuf.ByteString
        getTakemealNumberBytes() {
      java.lang.Object ref = takemealNumber_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        takemealNumber_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *取餐号
     * </pre>
     *
     * <code>string takemealNumber = 9;</code>
     * @param value The takemealNumber to set.
     * @return This builder for chaining.
     */
    public Builder setTakemealNumber(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      takemealNumber_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *取餐号
     * </pre>
     *
     * <code>string takemealNumber = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearTakemealNumber() {
      
      takemealNumber_ = getDefaultInstance().getTakemealNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *取餐号
     * </pre>
     *
     * <code>string takemealNumber = 9;</code>
     * @param value The bytes for takemealNumber to set.
     * @return This builder for chaining.
     */
    public Builder setTakemealNumberBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      takemealNumber_ = value;
      onChanged();
      return this;
    }

    private int qty_ ;
    /**
     * <pre>
     *订单商品总数
     * </pre>
     *
     * <code>int32 qty = 10;</code>
     * @return The qty.
     */
    @java.lang.Override
    public int getQty() {
      return qty_;
    }
    /**
     * <pre>
     *订单商品总数
     * </pre>
     *
     * <code>int32 qty = 10;</code>
     * @param value The qty to set.
     * @return This builder for chaining.
     */
    public Builder setQty(int value) {
      
      qty_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单商品总数
     * </pre>
     *
     * <code>int32 qty = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearQty() {
      
      qty_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object status_ = "";
    /**
     * <code>string status = 11;</code>
     * @return The status.
     */
    public java.lang.String getStatus() {
      java.lang.Object ref = status_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        status_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string status = 11;</code>
     * @return The bytes for status.
     */
    public com.google.protobuf.ByteString
        getStatusBytes() {
      java.lang.Object ref = status_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        status_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string status = 11;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      status_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string status = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      
      status_ = getDefaultInstance().getStatus();
      onChanged();
      return this;
    }
    /**
     * <code>string status = 11;</code>
     * @param value The bytes for status to set.
     * @return This builder for chaining.
     */
    public Builder setStatusBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      status_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo refundInfo_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo, cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.Builder, cn.hexcloud.pbis.common.service.facade.ticket.RefundInfoOrBuilder> refundInfoBuilder_;
    /**
     * <pre>
     *订单的退款相关信息
     * </pre>
     *
     * <code>.coupon.RefundInfo refundInfo = 12;</code>
     * @return Whether the refundInfo field is set.
     */
    public boolean hasRefundInfo() {
      return refundInfoBuilder_ != null || refundInfo_ != null;
    }
    /**
     * <pre>
     *订单的退款相关信息
     * </pre>
     *
     * <code>.coupon.RefundInfo refundInfo = 12;</code>
     * @return The refundInfo.
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo getRefundInfo() {
      if (refundInfoBuilder_ == null) {
        return refundInfo_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.getDefaultInstance() : refundInfo_;
      } else {
        return refundInfoBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *订单的退款相关信息
     * </pre>
     *
     * <code>.coupon.RefundInfo refundInfo = 12;</code>
     */
    public Builder setRefundInfo(cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo value) {
      if (refundInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        refundInfo_ = value;
        onChanged();
      } else {
        refundInfoBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *订单的退款相关信息
     * </pre>
     *
     * <code>.coupon.RefundInfo refundInfo = 12;</code>
     */
    public Builder setRefundInfo(
        cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.Builder builderForValue) {
      if (refundInfoBuilder_ == null) {
        refundInfo_ = builderForValue.build();
        onChanged();
      } else {
        refundInfoBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *订单的退款相关信息
     * </pre>
     *
     * <code>.coupon.RefundInfo refundInfo = 12;</code>
     */
    public Builder mergeRefundInfo(cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo value) {
      if (refundInfoBuilder_ == null) {
        if (refundInfo_ != null) {
          refundInfo_ =
            cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.newBuilder(refundInfo_).mergeFrom(value).buildPartial();
        } else {
          refundInfo_ = value;
        }
        onChanged();
      } else {
        refundInfoBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *订单的退款相关信息
     * </pre>
     *
     * <code>.coupon.RefundInfo refundInfo = 12;</code>
     */
    public Builder clearRefundInfo() {
      if (refundInfoBuilder_ == null) {
        refundInfo_ = null;
        onChanged();
      } else {
        refundInfo_ = null;
        refundInfoBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *订单的退款相关信息
     * </pre>
     *
     * <code>.coupon.RefundInfo refundInfo = 12;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.Builder getRefundInfoBuilder() {
      
      onChanged();
      return getRefundInfoFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *订单的退款相关信息
     * </pre>
     *
     * <code>.coupon.RefundInfo refundInfo = 12;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.RefundInfoOrBuilder getRefundInfoOrBuilder() {
      if (refundInfoBuilder_ != null) {
        return refundInfoBuilder_.getMessageOrBuilder();
      } else {
        return refundInfo_ == null ?
            cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.getDefaultInstance() : refundInfo_;
      }
    }
    /**
     * <pre>
     *订单的退款相关信息
     * </pre>
     *
     * <code>.coupon.RefundInfo refundInfo = 12;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo, cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.Builder, cn.hexcloud.pbis.common.service.facade.ticket.RefundInfoOrBuilder> 
        getRefundInfoFieldBuilder() {
      if (refundInfoBuilder_ == null) {
        refundInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo, cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo.Builder, cn.hexcloud.pbis.common.service.facade.ticket.RefundInfoOrBuilder>(
                getRefundInfo(),
                getParentForChildren(),
                isClean());
        refundInfo_ = null;
      }
      return refundInfoBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.ticket.Channel channel_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Channel, cn.hexcloud.pbis.common.service.facade.ticket.Channel.Builder, cn.hexcloud.pbis.common.service.facade.ticket.ChannelOrBuilder> channelBuilder_;
    /**
     * <pre>
     *订单的渠道信息
     * </pre>
     *
     * <code>.coupon.Channel channel = 13;</code>
     * @return Whether the channel field is set.
     */
    public boolean hasChannel() {
      return channelBuilder_ != null || channel_ != null;
    }
    /**
     * <pre>
     *订单的渠道信息
     * </pre>
     *
     * <code>.coupon.Channel channel = 13;</code>
     * @return The channel.
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Channel getChannel() {
      if (channelBuilder_ == null) {
        return channel_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Channel.getDefaultInstance() : channel_;
      } else {
        return channelBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *订单的渠道信息
     * </pre>
     *
     * <code>.coupon.Channel channel = 13;</code>
     */
    public Builder setChannel(cn.hexcloud.pbis.common.service.facade.ticket.Channel value) {
      if (channelBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        channel_ = value;
        onChanged();
      } else {
        channelBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *订单的渠道信息
     * </pre>
     *
     * <code>.coupon.Channel channel = 13;</code>
     */
    public Builder setChannel(
        cn.hexcloud.pbis.common.service.facade.ticket.Channel.Builder builderForValue) {
      if (channelBuilder_ == null) {
        channel_ = builderForValue.build();
        onChanged();
      } else {
        channelBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *订单的渠道信息
     * </pre>
     *
     * <code>.coupon.Channel channel = 13;</code>
     */
    public Builder mergeChannel(cn.hexcloud.pbis.common.service.facade.ticket.Channel value) {
      if (channelBuilder_ == null) {
        if (channel_ != null) {
          channel_ =
            cn.hexcloud.pbis.common.service.facade.ticket.Channel.newBuilder(channel_).mergeFrom(value).buildPartial();
        } else {
          channel_ = value;
        }
        onChanged();
      } else {
        channelBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *订单的渠道信息
     * </pre>
     *
     * <code>.coupon.Channel channel = 13;</code>
     */
    public Builder clearChannel() {
      if (channelBuilder_ == null) {
        channel_ = null;
        onChanged();
      } else {
        channel_ = null;
        channelBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *订单的渠道信息
     * </pre>
     *
     * <code>.coupon.Channel channel = 13;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Channel.Builder getChannelBuilder() {
      
      onChanged();
      return getChannelFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *订单的渠道信息
     * </pre>
     *
     * <code>.coupon.Channel channel = 13;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.ChannelOrBuilder getChannelOrBuilder() {
      if (channelBuilder_ != null) {
        return channelBuilder_.getMessageOrBuilder();
      } else {
        return channel_ == null ?
            cn.hexcloud.pbis.common.service.facade.ticket.Channel.getDefaultInstance() : channel_;
      }
    }
    /**
     * <pre>
     *订单的渠道信息
     * </pre>
     *
     * <code>.coupon.Channel channel = 13;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Channel, cn.hexcloud.pbis.common.service.facade.ticket.Channel.Builder, cn.hexcloud.pbis.common.service.facade.ticket.ChannelOrBuilder> 
        getChannelFieldBuilder() {
      if (channelBuilder_ == null) {
        channelBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.Channel, cn.hexcloud.pbis.common.service.facade.ticket.Channel.Builder, cn.hexcloud.pbis.common.service.facade.ticket.ChannelOrBuilder>(
                getChannel(),
                getParentForChildren(),
                isClean());
        channel_ = null;
      }
      return channelBuilder_;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> products_ =
      java.util.Collections.emptyList();
    private void ensureProductsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        products_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct>(products_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder> productsBuilder_;

    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> getProductsList() {
      if (productsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(products_);
      } else {
        return productsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public int getProductsCount() {
      if (productsBuilder_ == null) {
        return products_.size();
      } else {
        return productsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct getProducts(int index) {
      if (productsBuilder_ == null) {
        return products_.get(index);
      } else {
        return productsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public Builder setProducts(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct value) {
      if (productsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureProductsIsMutable();
        products_.set(index, value);
        onChanged();
      } else {
        productsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public Builder setProducts(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder builderForValue) {
      if (productsBuilder_ == null) {
        ensureProductsIsMutable();
        products_.set(index, builderForValue.build());
        onChanged();
      } else {
        productsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public Builder addProducts(cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct value) {
      if (productsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureProductsIsMutable();
        products_.add(value);
        onChanged();
      } else {
        productsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public Builder addProducts(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct value) {
      if (productsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureProductsIsMutable();
        products_.add(index, value);
        onChanged();
      } else {
        productsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public Builder addProducts(
        cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder builderForValue) {
      if (productsBuilder_ == null) {
        ensureProductsIsMutable();
        products_.add(builderForValue.build());
        onChanged();
      } else {
        productsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public Builder addProducts(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder builderForValue) {
      if (productsBuilder_ == null) {
        ensureProductsIsMutable();
        products_.add(index, builderForValue.build());
        onChanged();
      } else {
        productsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public Builder addAllProducts(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> values) {
      if (productsBuilder_ == null) {
        ensureProductsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, products_);
        onChanged();
      } else {
        productsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public Builder clearProducts() {
      if (productsBuilder_ == null) {
        products_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        productsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public Builder removeProducts(int index) {
      if (productsBuilder_ == null) {
        ensureProductsIsMutable();
        products_.remove(index);
        onChanged();
      } else {
        productsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder getProductsBuilder(
        int index) {
      return getProductsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder getProductsOrBuilder(
        int index) {
      if (productsBuilder_ == null) {
        return products_.get(index);  } else {
        return productsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder> 
         getProductsOrBuilderList() {
      if (productsBuilder_ != null) {
        return productsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(products_);
      }
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder addProductsBuilder() {
      return getProductsFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.getDefaultInstance());
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder addProductsBuilder(
        int index) {
      return getProductsFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.getDefaultInstance());
    }
    /**
     * <pre>
     *订单商品信息
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct products = 14;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder> 
         getProductsBuilderList() {
      return getProductsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder> 
        getProductsFieldBuilder() {
      if (productsBuilder_ == null) {
        productsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder>(
                products_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        products_ = null;
      }
      return productsBuilder_;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Payment> payments_ =
      java.util.Collections.emptyList();
    private void ensurePaymentsIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        payments_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.Payment>(payments_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Payment, cn.hexcloud.pbis.common.service.facade.ticket.Payment.Builder, cn.hexcloud.pbis.common.service.facade.ticket.PaymentOrBuilder> paymentsBuilder_;

    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Payment> getPaymentsList() {
      if (paymentsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(payments_);
      } else {
        return paymentsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public int getPaymentsCount() {
      if (paymentsBuilder_ == null) {
        return payments_.size();
      } else {
        return paymentsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Payment getPayments(int index) {
      if (paymentsBuilder_ == null) {
        return payments_.get(index);
      } else {
        return paymentsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public Builder setPayments(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Payment value) {
      if (paymentsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePaymentsIsMutable();
        payments_.set(index, value);
        onChanged();
      } else {
        paymentsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public Builder setPayments(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Payment.Builder builderForValue) {
      if (paymentsBuilder_ == null) {
        ensurePaymentsIsMutable();
        payments_.set(index, builderForValue.build());
        onChanged();
      } else {
        paymentsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public Builder addPayments(cn.hexcloud.pbis.common.service.facade.ticket.Payment value) {
      if (paymentsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePaymentsIsMutable();
        payments_.add(value);
        onChanged();
      } else {
        paymentsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public Builder addPayments(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Payment value) {
      if (paymentsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePaymentsIsMutable();
        payments_.add(index, value);
        onChanged();
      } else {
        paymentsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public Builder addPayments(
        cn.hexcloud.pbis.common.service.facade.ticket.Payment.Builder builderForValue) {
      if (paymentsBuilder_ == null) {
        ensurePaymentsIsMutable();
        payments_.add(builderForValue.build());
        onChanged();
      } else {
        paymentsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public Builder addPayments(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Payment.Builder builderForValue) {
      if (paymentsBuilder_ == null) {
        ensurePaymentsIsMutable();
        payments_.add(index, builderForValue.build());
        onChanged();
      } else {
        paymentsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public Builder addAllPayments(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.ticket.Payment> values) {
      if (paymentsBuilder_ == null) {
        ensurePaymentsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, payments_);
        onChanged();
      } else {
        paymentsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public Builder clearPayments() {
      if (paymentsBuilder_ == null) {
        payments_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        paymentsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public Builder removePayments(int index) {
      if (paymentsBuilder_ == null) {
        ensurePaymentsIsMutable();
        payments_.remove(index);
        onChanged();
      } else {
        paymentsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Payment.Builder getPaymentsBuilder(
        int index) {
      return getPaymentsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.PaymentOrBuilder getPaymentsOrBuilder(
        int index) {
      if (paymentsBuilder_ == null) {
        return payments_.get(index);  } else {
        return paymentsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.PaymentOrBuilder> 
         getPaymentsOrBuilderList() {
      if (paymentsBuilder_ != null) {
        return paymentsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(payments_);
      }
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Payment.Builder addPaymentsBuilder() {
      return getPaymentsFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.ticket.Payment.getDefaultInstance());
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Payment.Builder addPaymentsBuilder(
        int index) {
      return getPaymentsFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.ticket.Payment.getDefaultInstance());
    }
    /**
     * <pre>
     *订单的支付信息
     * </pre>
     *
     * <code>repeated .coupon.Payment payments = 15;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Payment.Builder> 
         getPaymentsBuilderList() {
      return getPaymentsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Payment, cn.hexcloud.pbis.common.service.facade.ticket.Payment.Builder, cn.hexcloud.pbis.common.service.facade.ticket.PaymentOrBuilder> 
        getPaymentsFieldBuilder() {
      if (paymentsBuilder_ == null) {
        paymentsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.Payment, cn.hexcloud.pbis.common.service.facade.ticket.Payment.Builder, cn.hexcloud.pbis.common.service.facade.ticket.PaymentOrBuilder>(
                payments_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        payments_ = null;
      }
      return paymentsBuilder_;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Promotion> promotions_ =
      java.util.Collections.emptyList();
    private void ensurePromotionsIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        promotions_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.Promotion>(promotions_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Promotion, cn.hexcloud.pbis.common.service.facade.ticket.Promotion.Builder, cn.hexcloud.pbis.common.service.facade.ticket.PromotionOrBuilder> promotionsBuilder_;

    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Promotion> getPromotionsList() {
      if (promotionsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(promotions_);
      } else {
        return promotionsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public int getPromotionsCount() {
      if (promotionsBuilder_ == null) {
        return promotions_.size();
      } else {
        return promotionsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Promotion getPromotions(int index) {
      if (promotionsBuilder_ == null) {
        return promotions_.get(index);
      } else {
        return promotionsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public Builder setPromotions(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Promotion value) {
      if (promotionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePromotionsIsMutable();
        promotions_.set(index, value);
        onChanged();
      } else {
        promotionsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public Builder setPromotions(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Promotion.Builder builderForValue) {
      if (promotionsBuilder_ == null) {
        ensurePromotionsIsMutable();
        promotions_.set(index, builderForValue.build());
        onChanged();
      } else {
        promotionsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public Builder addPromotions(cn.hexcloud.pbis.common.service.facade.ticket.Promotion value) {
      if (promotionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePromotionsIsMutable();
        promotions_.add(value);
        onChanged();
      } else {
        promotionsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public Builder addPromotions(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Promotion value) {
      if (promotionsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePromotionsIsMutable();
        promotions_.add(index, value);
        onChanged();
      } else {
        promotionsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public Builder addPromotions(
        cn.hexcloud.pbis.common.service.facade.ticket.Promotion.Builder builderForValue) {
      if (promotionsBuilder_ == null) {
        ensurePromotionsIsMutable();
        promotions_.add(builderForValue.build());
        onChanged();
      } else {
        promotionsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public Builder addPromotions(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Promotion.Builder builderForValue) {
      if (promotionsBuilder_ == null) {
        ensurePromotionsIsMutable();
        promotions_.add(index, builderForValue.build());
        onChanged();
      } else {
        promotionsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public Builder addAllPromotions(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.ticket.Promotion> values) {
      if (promotionsBuilder_ == null) {
        ensurePromotionsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, promotions_);
        onChanged();
      } else {
        promotionsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public Builder clearPromotions() {
      if (promotionsBuilder_ == null) {
        promotions_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        promotionsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public Builder removePromotions(int index) {
      if (promotionsBuilder_ == null) {
        ensurePromotionsIsMutable();
        promotions_.remove(index);
        onChanged();
      } else {
        promotionsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Promotion.Builder getPromotionsBuilder(
        int index) {
      return getPromotionsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.PromotionOrBuilder getPromotionsOrBuilder(
        int index) {
      if (promotionsBuilder_ == null) {
        return promotions_.get(index);  } else {
        return promotionsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.PromotionOrBuilder> 
         getPromotionsOrBuilderList() {
      if (promotionsBuilder_ != null) {
        return promotionsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(promotions_);
      }
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Promotion.Builder addPromotionsBuilder() {
      return getPromotionsFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.ticket.Promotion.getDefaultInstance());
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Promotion.Builder addPromotionsBuilder(
        int index) {
      return getPromotionsFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.ticket.Promotion.getDefaultInstance());
    }
    /**
     * <pre>
     *订单的促销信息
     * </pre>
     *
     * <code>repeated .coupon.Promotion promotions = 16;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Promotion.Builder> 
         getPromotionsBuilderList() {
      return getPromotionsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Promotion, cn.hexcloud.pbis.common.service.facade.ticket.Promotion.Builder, cn.hexcloud.pbis.common.service.facade.ticket.PromotionOrBuilder> 
        getPromotionsFieldBuilder() {
      if (promotionsBuilder_ == null) {
        promotionsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.Promotion, cn.hexcloud.pbis.common.service.facade.ticket.Promotion.Builder, cn.hexcloud.pbis.common.service.facade.ticket.PromotionOrBuilder>(
                promotions_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        promotions_ = null;
      }
      return promotionsBuilder_;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Member> members_ =
      java.util.Collections.emptyList();
    private void ensureMembersIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        members_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.Member>(members_);
        bitField0_ |= 0x00000008;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Member, cn.hexcloud.pbis.common.service.facade.ticket.Member.Builder, cn.hexcloud.pbis.common.service.facade.ticket.MemberOrBuilder> membersBuilder_;

    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Member> getMembersList() {
      if (membersBuilder_ == null) {
        return java.util.Collections.unmodifiableList(members_);
      } else {
        return membersBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public int getMembersCount() {
      if (membersBuilder_ == null) {
        return members_.size();
      } else {
        return membersBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Member getMembers(int index) {
      if (membersBuilder_ == null) {
        return members_.get(index);
      } else {
        return membersBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public Builder setMembers(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Member value) {
      if (membersBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMembersIsMutable();
        members_.set(index, value);
        onChanged();
      } else {
        membersBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public Builder setMembers(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Member.Builder builderForValue) {
      if (membersBuilder_ == null) {
        ensureMembersIsMutable();
        members_.set(index, builderForValue.build());
        onChanged();
      } else {
        membersBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public Builder addMembers(cn.hexcloud.pbis.common.service.facade.ticket.Member value) {
      if (membersBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMembersIsMutable();
        members_.add(value);
        onChanged();
      } else {
        membersBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public Builder addMembers(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Member value) {
      if (membersBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureMembersIsMutable();
        members_.add(index, value);
        onChanged();
      } else {
        membersBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public Builder addMembers(
        cn.hexcloud.pbis.common.service.facade.ticket.Member.Builder builderForValue) {
      if (membersBuilder_ == null) {
        ensureMembersIsMutable();
        members_.add(builderForValue.build());
        onChanged();
      } else {
        membersBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public Builder addMembers(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Member.Builder builderForValue) {
      if (membersBuilder_ == null) {
        ensureMembersIsMutable();
        members_.add(index, builderForValue.build());
        onChanged();
      } else {
        membersBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public Builder addAllMembers(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.ticket.Member> values) {
      if (membersBuilder_ == null) {
        ensureMembersIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, members_);
        onChanged();
      } else {
        membersBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public Builder clearMembers() {
      if (membersBuilder_ == null) {
        members_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
      } else {
        membersBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public Builder removeMembers(int index) {
      if (membersBuilder_ == null) {
        ensureMembersIsMutable();
        members_.remove(index);
        onChanged();
      } else {
        membersBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Member.Builder getMembersBuilder(
        int index) {
      return getMembersFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.MemberOrBuilder getMembersOrBuilder(
        int index) {
      if (membersBuilder_ == null) {
        return members_.get(index);  } else {
        return membersBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.MemberOrBuilder> 
         getMembersOrBuilderList() {
      if (membersBuilder_ != null) {
        return membersBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(members_);
      }
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Member.Builder addMembersBuilder() {
      return getMembersFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.ticket.Member.getDefaultInstance());
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Member.Builder addMembersBuilder(
        int index) {
      return getMembersFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.ticket.Member.getDefaultInstance());
    }
    /**
     * <pre>
     *订单的会员信息
     * </pre>
     *
     * <code>repeated .coupon.Member members = 17;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Member.Builder> 
         getMembersBuilderList() {
      return getMembersFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Member, cn.hexcloud.pbis.common.service.facade.ticket.Member.Builder, cn.hexcloud.pbis.common.service.facade.ticket.MemberOrBuilder> 
        getMembersFieldBuilder() {
      if (membersBuilder_ == null) {
        membersBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.Member, cn.hexcloud.pbis.common.service.facade.ticket.Member.Builder, cn.hexcloud.pbis.common.service.facade.ticket.MemberOrBuilder>(
                members_,
                ((bitField0_ & 0x00000008) != 0),
                getParentForChildren(),
                isClean());
        members_ = null;
      }
      return membersBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.ticket.Table table_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Table, cn.hexcloud.pbis.common.service.facade.ticket.Table.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TableOrBuilder> tableBuilder_;
    /**
     * <pre>
     *桌位信息
     * </pre>
     *
     * <code>.coupon.Table table = 18;</code>
     * @return Whether the table field is set.
     */
    public boolean hasTable() {
      return tableBuilder_ != null || table_ != null;
    }
    /**
     * <pre>
     *桌位信息
     * </pre>
     *
     * <code>.coupon.Table table = 18;</code>
     * @return The table.
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Table getTable() {
      if (tableBuilder_ == null) {
        return table_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Table.getDefaultInstance() : table_;
      } else {
        return tableBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *桌位信息
     * </pre>
     *
     * <code>.coupon.Table table = 18;</code>
     */
    public Builder setTable(cn.hexcloud.pbis.common.service.facade.ticket.Table value) {
      if (tableBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        table_ = value;
        onChanged();
      } else {
        tableBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *桌位信息
     * </pre>
     *
     * <code>.coupon.Table table = 18;</code>
     */
    public Builder setTable(
        cn.hexcloud.pbis.common.service.facade.ticket.Table.Builder builderForValue) {
      if (tableBuilder_ == null) {
        table_ = builderForValue.build();
        onChanged();
      } else {
        tableBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *桌位信息
     * </pre>
     *
     * <code>.coupon.Table table = 18;</code>
     */
    public Builder mergeTable(cn.hexcloud.pbis.common.service.facade.ticket.Table value) {
      if (tableBuilder_ == null) {
        if (table_ != null) {
          table_ =
            cn.hexcloud.pbis.common.service.facade.ticket.Table.newBuilder(table_).mergeFrom(value).buildPartial();
        } else {
          table_ = value;
        }
        onChanged();
      } else {
        tableBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *桌位信息
     * </pre>
     *
     * <code>.coupon.Table table = 18;</code>
     */
    public Builder clearTable() {
      if (tableBuilder_ == null) {
        table_ = null;
        onChanged();
      } else {
        table_ = null;
        tableBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *桌位信息
     * </pre>
     *
     * <code>.coupon.Table table = 18;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Table.Builder getTableBuilder() {
      
      onChanged();
      return getTableFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *桌位信息
     * </pre>
     *
     * <code>.coupon.Table table = 18;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TableOrBuilder getTableOrBuilder() {
      if (tableBuilder_ != null) {
        return tableBuilder_.getMessageOrBuilder();
      } else {
        return table_ == null ?
            cn.hexcloud.pbis.common.service.facade.ticket.Table.getDefaultInstance() : table_;
      }
    }
    /**
     * <pre>
     *桌位信息
     * </pre>
     *
     * <code>.coupon.Table table = 18;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Table, cn.hexcloud.pbis.common.service.facade.ticket.Table.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TableOrBuilder> 
        getTableFieldBuilder() {
      if (tableBuilder_ == null) {
        tableBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.Table, cn.hexcloud.pbis.common.service.facade.ticket.Table.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TableOrBuilder>(
                getTable(),
                getParentForChildren(),
                isClean());
        table_ = null;
      }
      return tableBuilder_;
    }

    private int people_ ;
    /**
     * <pre>
     *订单人数
     * </pre>
     *
     * <code>int32 people = 19;</code>
     * @return The people.
     */
    @java.lang.Override
    public int getPeople() {
      return people_;
    }
    /**
     * <pre>
     *订单人数
     * </pre>
     *
     * <code>int32 people = 19;</code>
     * @param value The people to set.
     * @return This builder for chaining.
     */
    public Builder setPeople(int value) {
      
      people_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单人数
     * </pre>
     *
     * <code>int32 people = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearPeople() {
      
      people_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object roomNo_ = "";
    /**
     * <pre>
     *房间号
     * </pre>
     *
     * <code>string room_no = 20;</code>
     * @return The roomNo.
     */
    public java.lang.String getRoomNo() {
      java.lang.Object ref = roomNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        roomNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *房间号
     * </pre>
     *
     * <code>string room_no = 20;</code>
     * @return The bytes for roomNo.
     */
    public com.google.protobuf.ByteString
        getRoomNoBytes() {
      java.lang.Object ref = roomNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        roomNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *房间号
     * </pre>
     *
     * <code>string room_no = 20;</code>
     * @param value The roomNo to set.
     * @return This builder for chaining.
     */
    public Builder setRoomNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      roomNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *房间号
     * </pre>
     *
     * <code>string room_no = 20;</code>
     * @return This builder for chaining.
     */
    public Builder clearRoomNo() {
      
      roomNo_ = getDefaultInstance().getRoomNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *房间号
     * </pre>
     *
     * <code>string room_no = 20;</code>
     * @param value The bytes for roomNo to set.
     * @return This builder for chaining.
     */
    public Builder setRoomNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      roomNo_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object remark_ = "";
    /**
     * <pre>
     *订单备注
     * </pre>
     *
     * <code>string remark = 21;</code>
     * @return The remark.
     */
    public java.lang.String getRemark() {
      java.lang.Object ref = remark_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        remark_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *订单备注
     * </pre>
     *
     * <code>string remark = 21;</code>
     * @return The bytes for remark.
     */
    public com.google.protobuf.ByteString
        getRemarkBytes() {
      java.lang.Object ref = remark_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        remark_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *订单备注
     * </pre>
     *
     * <code>string remark = 21;</code>
     * @param value The remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemark(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      remark_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单备注
     * </pre>
     *
     * <code>string remark = 21;</code>
     * @return This builder for chaining.
     */
    public Builder clearRemark() {
      
      remark_ = getDefaultInstance().getRemark();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单备注
     * </pre>
     *
     * <code>string remark = 21;</code>
     * @param value The bytes for remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemarkBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      remark_ = value;
      onChanged();
      return this;
    }

    private boolean houseAc_ ;
    /**
     * <pre>
     *如家场景，意义待明确
     * </pre>
     *
     * <code>bool house_ac = 22;</code>
     * @return The houseAc.
     */
    @java.lang.Override
    public boolean getHouseAc() {
      return houseAc_;
    }
    /**
     * <pre>
     *如家场景，意义待明确
     * </pre>
     *
     * <code>bool house_ac = 22;</code>
     * @param value The houseAc to set.
     * @return This builder for chaining.
     */
    public Builder setHouseAc(boolean value) {
      
      houseAc_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *如家场景，意义待明确
     * </pre>
     *
     * <code>bool house_ac = 22;</code>
     * @return This builder for chaining.
     */
    public Builder clearHouseAc() {
      
      houseAc_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object orderTimeType_ = "";
    /**
     * <pre>
     *早中晚餐标志，枚举值
     * </pre>
     *
     * <code>string order_time_type = 23;</code>
     * @return The orderTimeType.
     */
    public java.lang.String getOrderTimeType() {
      java.lang.Object ref = orderTimeType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        orderTimeType_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *早中晚餐标志，枚举值
     * </pre>
     *
     * <code>string order_time_type = 23;</code>
     * @return The bytes for orderTimeType.
     */
    public com.google.protobuf.ByteString
        getOrderTimeTypeBytes() {
      java.lang.Object ref = orderTimeType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        orderTimeType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *早中晚餐标志，枚举值
     * </pre>
     *
     * <code>string order_time_type = 23;</code>
     * @param value The orderTimeType to set.
     * @return This builder for chaining.
     */
    public Builder setOrderTimeType(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      orderTimeType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *早中晚餐标志，枚举值
     * </pre>
     *
     * <code>string order_time_type = 23;</code>
     * @return This builder for chaining.
     */
    public Builder clearOrderTimeType() {
      
      orderTimeType_ = getDefaultInstance().getOrderTimeType();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *早中晚餐标志，枚举值
     * </pre>
     *
     * <code>string order_time_type = 23;</code>
     * @param value The bytes for orderTimeType to set.
     * @return This builder for chaining.
     */
    public Builder setOrderTimeTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      orderTimeType_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object shiftNumber_ = "";
    /**
     * <pre>
     *班次号
     * </pre>
     *
     * <code>string shiftNumber = 24;</code>
     * @return The shiftNumber.
     */
    public java.lang.String getShiftNumber() {
      java.lang.Object ref = shiftNumber_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        shiftNumber_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *班次号
     * </pre>
     *
     * <code>string shiftNumber = 24;</code>
     * @return The bytes for shiftNumber.
     */
    public com.google.protobuf.ByteString
        getShiftNumberBytes() {
      java.lang.Object ref = shiftNumber_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        shiftNumber_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *班次号
     * </pre>
     *
     * <code>string shiftNumber = 24;</code>
     * @param value The shiftNumber to set.
     * @return This builder for chaining.
     */
    public Builder setShiftNumber(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      shiftNumber_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *班次号
     * </pre>
     *
     * <code>string shiftNumber = 24;</code>
     * @return This builder for chaining.
     */
    public Builder clearShiftNumber() {
      
      shiftNumber_ = getDefaultInstance().getShiftNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *班次号
     * </pre>
     *
     * <code>string shiftNumber = 24;</code>
     * @param value The bytes for shiftNumber to set.
     * @return This builder for chaining.
     */
    public Builder setShiftNumberBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      shiftNumber_ = value;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Tax> taxList_ =
      java.util.Collections.emptyList();
    private void ensureTaxListIsMutable() {
      if (!((bitField0_ & 0x00000010) != 0)) {
        taxList_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.Tax>(taxList_);
        bitField0_ |= 0x00000010;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Tax, cn.hexcloud.pbis.common.service.facade.ticket.Tax.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TaxOrBuilder> taxListBuilder_;

    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Tax> getTaxListList() {
      if (taxListBuilder_ == null) {
        return java.util.Collections.unmodifiableList(taxList_);
      } else {
        return taxListBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public int getTaxListCount() {
      if (taxListBuilder_ == null) {
        return taxList_.size();
      } else {
        return taxListBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Tax getTaxList(int index) {
      if (taxListBuilder_ == null) {
        return taxList_.get(index);
      } else {
        return taxListBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public Builder setTaxList(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Tax value) {
      if (taxListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTaxListIsMutable();
        taxList_.set(index, value);
        onChanged();
      } else {
        taxListBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public Builder setTaxList(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Tax.Builder builderForValue) {
      if (taxListBuilder_ == null) {
        ensureTaxListIsMutable();
        taxList_.set(index, builderForValue.build());
        onChanged();
      } else {
        taxListBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public Builder addTaxList(cn.hexcloud.pbis.common.service.facade.ticket.Tax value) {
      if (taxListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTaxListIsMutable();
        taxList_.add(value);
        onChanged();
      } else {
        taxListBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public Builder addTaxList(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Tax value) {
      if (taxListBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureTaxListIsMutable();
        taxList_.add(index, value);
        onChanged();
      } else {
        taxListBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public Builder addTaxList(
        cn.hexcloud.pbis.common.service.facade.ticket.Tax.Builder builderForValue) {
      if (taxListBuilder_ == null) {
        ensureTaxListIsMutable();
        taxList_.add(builderForValue.build());
        onChanged();
      } else {
        taxListBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public Builder addTaxList(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Tax.Builder builderForValue) {
      if (taxListBuilder_ == null) {
        ensureTaxListIsMutable();
        taxList_.add(index, builderForValue.build());
        onChanged();
      } else {
        taxListBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public Builder addAllTaxList(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.ticket.Tax> values) {
      if (taxListBuilder_ == null) {
        ensureTaxListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, taxList_);
        onChanged();
      } else {
        taxListBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public Builder clearTaxList() {
      if (taxListBuilder_ == null) {
        taxList_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
      } else {
        taxListBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public Builder removeTaxList(int index) {
      if (taxListBuilder_ == null) {
        ensureTaxListIsMutable();
        taxList_.remove(index);
        onChanged();
      } else {
        taxListBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Tax.Builder getTaxListBuilder(
        int index) {
      return getTaxListFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TaxOrBuilder getTaxListOrBuilder(
        int index) {
      if (taxListBuilder_ == null) {
        return taxList_.get(index);  } else {
        return taxListBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.TaxOrBuilder> 
         getTaxListOrBuilderList() {
      if (taxListBuilder_ != null) {
        return taxListBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(taxList_);
      }
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Tax.Builder addTaxListBuilder() {
      return getTaxListFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.ticket.Tax.getDefaultInstance());
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Tax.Builder addTaxListBuilder(
        int index) {
      return getTaxListFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.ticket.Tax.getDefaultInstance());
    }
    /**
     * <pre>
     *税项
     * </pre>
     *
     * <code>repeated .coupon.Tax taxList = 25;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Tax.Builder> 
         getTaxListBuilderList() {
      return getTaxListFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Tax, cn.hexcloud.pbis.common.service.facade.ticket.Tax.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TaxOrBuilder> 
        getTaxListFieldBuilder() {
      if (taxListBuilder_ == null) {
        taxListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.Tax, cn.hexcloud.pbis.common.service.facade.ticket.Tax.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TaxOrBuilder>(
                taxList_,
                ((bitField0_ & 0x00000010) != 0),
                getParentForChildren(),
                isClean());
        taxList_ = null;
      }
      return taxListBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.ticket.Store store_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Store, cn.hexcloud.pbis.common.service.facade.ticket.Store.Builder, cn.hexcloud.pbis.common.service.facade.ticket.StoreOrBuilder> storeBuilder_;
    /**
     * <pre>
     *门店信息
     * </pre>
     *
     * <code>.coupon.Store store = 26;</code>
     * @return Whether the store field is set.
     */
    public boolean hasStore() {
      return storeBuilder_ != null || store_ != null;
    }
    /**
     * <pre>
     *门店信息
     * </pre>
     *
     * <code>.coupon.Store store = 26;</code>
     * @return The store.
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Store getStore() {
      if (storeBuilder_ == null) {
        return store_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Store.getDefaultInstance() : store_;
      } else {
        return storeBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *门店信息
     * </pre>
     *
     * <code>.coupon.Store store = 26;</code>
     */
    public Builder setStore(cn.hexcloud.pbis.common.service.facade.ticket.Store value) {
      if (storeBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        store_ = value;
        onChanged();
      } else {
        storeBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *门店信息
     * </pre>
     *
     * <code>.coupon.Store store = 26;</code>
     */
    public Builder setStore(
        cn.hexcloud.pbis.common.service.facade.ticket.Store.Builder builderForValue) {
      if (storeBuilder_ == null) {
        store_ = builderForValue.build();
        onChanged();
      } else {
        storeBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *门店信息
     * </pre>
     *
     * <code>.coupon.Store store = 26;</code>
     */
    public Builder mergeStore(cn.hexcloud.pbis.common.service.facade.ticket.Store value) {
      if (storeBuilder_ == null) {
        if (store_ != null) {
          store_ =
            cn.hexcloud.pbis.common.service.facade.ticket.Store.newBuilder(store_).mergeFrom(value).buildPartial();
        } else {
          store_ = value;
        }
        onChanged();
      } else {
        storeBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *门店信息
     * </pre>
     *
     * <code>.coupon.Store store = 26;</code>
     */
    public Builder clearStore() {
      if (storeBuilder_ == null) {
        store_ = null;
        onChanged();
      } else {
        store_ = null;
        storeBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *门店信息
     * </pre>
     *
     * <code>.coupon.Store store = 26;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Store.Builder getStoreBuilder() {
      
      onChanged();
      return getStoreFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *门店信息
     * </pre>
     *
     * <code>.coupon.Store store = 26;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.StoreOrBuilder getStoreOrBuilder() {
      if (storeBuilder_ != null) {
        return storeBuilder_.getMessageOrBuilder();
      } else {
        return store_ == null ?
            cn.hexcloud.pbis.common.service.facade.ticket.Store.getDefaultInstance() : store_;
      }
    }
    /**
     * <pre>
     *门店信息
     * </pre>
     *
     * <code>.coupon.Store store = 26;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Store, cn.hexcloud.pbis.common.service.facade.ticket.Store.Builder, cn.hexcloud.pbis.common.service.facade.ticket.StoreOrBuilder> 
        getStoreFieldBuilder() {
      if (storeBuilder_ == null) {
        storeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.Store, cn.hexcloud.pbis.common.service.facade.ticket.Store.Builder, cn.hexcloud.pbis.common.service.facade.ticket.StoreOrBuilder>(
                getStore(),
                getParentForChildren(),
                isClean());
        store_ = null;
      }
      return storeBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.ticket.Takeaway takeawayInfo_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Takeaway, cn.hexcloud.pbis.common.service.facade.ticket.Takeaway.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TakeawayOrBuilder> takeawayInfoBuilder_;
    /**
     * <pre>
     *外卖信息
     * </pre>
     *
     * <code>.coupon.Takeaway takeaway_info = 34;</code>
     * @return Whether the takeawayInfo field is set.
     */
    public boolean hasTakeawayInfo() {
      return takeawayInfoBuilder_ != null || takeawayInfo_ != null;
    }
    /**
     * <pre>
     *外卖信息
     * </pre>
     *
     * <code>.coupon.Takeaway takeaway_info = 34;</code>
     * @return The takeawayInfo.
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Takeaway getTakeawayInfo() {
      if (takeawayInfoBuilder_ == null) {
        return takeawayInfo_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Takeaway.getDefaultInstance() : takeawayInfo_;
      } else {
        return takeawayInfoBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *外卖信息
     * </pre>
     *
     * <code>.coupon.Takeaway takeaway_info = 34;</code>
     */
    public Builder setTakeawayInfo(cn.hexcloud.pbis.common.service.facade.ticket.Takeaway value) {
      if (takeawayInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        takeawayInfo_ = value;
        onChanged();
      } else {
        takeawayInfoBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *外卖信息
     * </pre>
     *
     * <code>.coupon.Takeaway takeaway_info = 34;</code>
     */
    public Builder setTakeawayInfo(
        cn.hexcloud.pbis.common.service.facade.ticket.Takeaway.Builder builderForValue) {
      if (takeawayInfoBuilder_ == null) {
        takeawayInfo_ = builderForValue.build();
        onChanged();
      } else {
        takeawayInfoBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *外卖信息
     * </pre>
     *
     * <code>.coupon.Takeaway takeaway_info = 34;</code>
     */
    public Builder mergeTakeawayInfo(cn.hexcloud.pbis.common.service.facade.ticket.Takeaway value) {
      if (takeawayInfoBuilder_ == null) {
        if (takeawayInfo_ != null) {
          takeawayInfo_ =
            cn.hexcloud.pbis.common.service.facade.ticket.Takeaway.newBuilder(takeawayInfo_).mergeFrom(value).buildPartial();
        } else {
          takeawayInfo_ = value;
        }
        onChanged();
      } else {
        takeawayInfoBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *外卖信息
     * </pre>
     *
     * <code>.coupon.Takeaway takeaway_info = 34;</code>
     */
    public Builder clearTakeawayInfo() {
      if (takeawayInfoBuilder_ == null) {
        takeawayInfo_ = null;
        onChanged();
      } else {
        takeawayInfo_ = null;
        takeawayInfoBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *外卖信息
     * </pre>
     *
     * <code>.coupon.Takeaway takeaway_info = 34;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Takeaway.Builder getTakeawayInfoBuilder() {
      
      onChanged();
      return getTakeawayInfoFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *外卖信息
     * </pre>
     *
     * <code>.coupon.Takeaway takeaway_info = 34;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TakeawayOrBuilder getTakeawayInfoOrBuilder() {
      if (takeawayInfoBuilder_ != null) {
        return takeawayInfoBuilder_.getMessageOrBuilder();
      } else {
        return takeawayInfo_ == null ?
            cn.hexcloud.pbis.common.service.facade.ticket.Takeaway.getDefaultInstance() : takeawayInfo_;
      }
    }
    /**
     * <pre>
     *外卖信息
     * </pre>
     *
     * <code>.coupon.Takeaway takeaway_info = 34;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Takeaway, cn.hexcloud.pbis.common.service.facade.ticket.Takeaway.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TakeawayOrBuilder> 
        getTakeawayInfoFieldBuilder() {
      if (takeawayInfoBuilder_ == null) {
        takeawayInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.Takeaway, cn.hexcloud.pbis.common.service.facade.ticket.Takeaway.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TakeawayOrBuilder>(
                getTakeawayInfo(),
                getParentForChildren(),
                isClean());
        takeawayInfo_ = null;
      }
      return takeawayInfoBuilder_;
    }

    private java.lang.Object ticketUno_ = "";
    /**
     * <pre>
     *订单唯一流水号
     * </pre>
     *
     * <code>string ticketUno = 35;</code>
     * @return The ticketUno.
     */
    public java.lang.String getTicketUno() {
      java.lang.Object ref = ticketUno_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ticketUno_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *订单唯一流水号
     * </pre>
     *
     * <code>string ticketUno = 35;</code>
     * @return The bytes for ticketUno.
     */
    public com.google.protobuf.ByteString
        getTicketUnoBytes() {
      java.lang.Object ref = ticketUno_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ticketUno_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *订单唯一流水号
     * </pre>
     *
     * <code>string ticketUno = 35;</code>
     * @param value The ticketUno to set.
     * @return This builder for chaining.
     */
    public Builder setTicketUno(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      ticketUno_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单唯一流水号
     * </pre>
     *
     * <code>string ticketUno = 35;</code>
     * @return This builder for chaining.
     */
    public Builder clearTicketUno() {
      
      ticketUno_ = getDefaultInstance().getTicketUno();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单唯一流水号
     * </pre>
     *
     * <code>string ticketUno = 35;</code>
     * @param value The bytes for ticketUno to set.
     * @return This builder for chaining.
     */
    public Builder setTicketUnoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      ticketUno_ = value;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon> coupons_ =
      java.util.Collections.emptyList();
    private void ensureCouponsIsMutable() {
      if (!((bitField0_ & 0x00000020) != 0)) {
        coupons_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon>(coupons_);
        bitField0_ |= 0x00000020;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon, cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TicketCouponOrBuilder> couponsBuilder_;

    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon> getCouponsList() {
      if (couponsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(coupons_);
      } else {
        return couponsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public int getCouponsCount() {
      if (couponsBuilder_ == null) {
        return coupons_.size();
      } else {
        return couponsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon getCoupons(int index) {
      if (couponsBuilder_ == null) {
        return coupons_.get(index);
      } else {
        return couponsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public Builder setCoupons(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon value) {
      if (couponsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCouponsIsMutable();
        coupons_.set(index, value);
        onChanged();
      } else {
        couponsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public Builder setCoupons(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon.Builder builderForValue) {
      if (couponsBuilder_ == null) {
        ensureCouponsIsMutable();
        coupons_.set(index, builderForValue.build());
        onChanged();
      } else {
        couponsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public Builder addCoupons(cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon value) {
      if (couponsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCouponsIsMutable();
        coupons_.add(value);
        onChanged();
      } else {
        couponsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public Builder addCoupons(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon value) {
      if (couponsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureCouponsIsMutable();
        coupons_.add(index, value);
        onChanged();
      } else {
        couponsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public Builder addCoupons(
        cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon.Builder builderForValue) {
      if (couponsBuilder_ == null) {
        ensureCouponsIsMutable();
        coupons_.add(builderForValue.build());
        onChanged();
      } else {
        couponsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public Builder addCoupons(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon.Builder builderForValue) {
      if (couponsBuilder_ == null) {
        ensureCouponsIsMutable();
        coupons_.add(index, builderForValue.build());
        onChanged();
      } else {
        couponsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public Builder addAllCoupons(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon> values) {
      if (couponsBuilder_ == null) {
        ensureCouponsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, coupons_);
        onChanged();
      } else {
        couponsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public Builder clearCoupons() {
      if (couponsBuilder_ == null) {
        coupons_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
      } else {
        couponsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public Builder removeCoupons(int index) {
      if (couponsBuilder_ == null) {
        ensureCouponsIsMutable();
        coupons_.remove(index);
        onChanged();
      } else {
        couponsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon.Builder getCouponsBuilder(
        int index) {
      return getCouponsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketCouponOrBuilder getCouponsOrBuilder(
        int index) {
      if (couponsBuilder_ == null) {
        return coupons_.get(index);  } else {
        return couponsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketCouponOrBuilder> 
         getCouponsOrBuilderList() {
      if (couponsBuilder_ != null) {
        return couponsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(coupons_);
      }
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon.Builder addCouponsBuilder() {
      return getCouponsFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon.getDefaultInstance());
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon.Builder addCouponsBuilder(
        int index) {
      return getCouponsFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon.getDefaultInstance());
    }
    /**
     * <pre>
     *卡券信息
     * </pre>
     *
     * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon.Builder> 
         getCouponsBuilderList() {
      return getCouponsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon, cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TicketCouponOrBuilder> 
        getCouponsFieldBuilder() {
      if (couponsBuilder_ == null) {
        couponsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon, cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TicketCouponOrBuilder>(
                coupons_,
                ((bitField0_ & 0x00000020) != 0),
                getParentForChildren(),
                isClean());
        coupons_ = null;
      }
      return couponsBuilder_;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee> fees_ =
      java.util.Collections.emptyList();
    private void ensureFeesIsMutable() {
      if (!((bitField0_ & 0x00000040) != 0)) {
        fees_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.Fee>(fees_);
        bitField0_ |= 0x00000040;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Fee, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder, cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder> feesBuilder_;

    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee> getFeesList() {
      if (feesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(fees_);
      } else {
        return feesBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public int getFeesCount() {
      if (feesBuilder_ == null) {
        return fees_.size();
      } else {
        return feesBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Fee getFees(int index) {
      if (feesBuilder_ == null) {
        return fees_.get(index);
      } else {
        return feesBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public Builder setFees(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Fee value) {
      if (feesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureFeesIsMutable();
        fees_.set(index, value);
        onChanged();
      } else {
        feesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public Builder setFees(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder builderForValue) {
      if (feesBuilder_ == null) {
        ensureFeesIsMutable();
        fees_.set(index, builderForValue.build());
        onChanged();
      } else {
        feesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public Builder addFees(cn.hexcloud.pbis.common.service.facade.ticket.Fee value) {
      if (feesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureFeesIsMutable();
        fees_.add(value);
        onChanged();
      } else {
        feesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public Builder addFees(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Fee value) {
      if (feesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureFeesIsMutable();
        fees_.add(index, value);
        onChanged();
      } else {
        feesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public Builder addFees(
        cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder builderForValue) {
      if (feesBuilder_ == null) {
        ensureFeesIsMutable();
        fees_.add(builderForValue.build());
        onChanged();
      } else {
        feesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public Builder addFees(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder builderForValue) {
      if (feesBuilder_ == null) {
        ensureFeesIsMutable();
        fees_.add(index, builderForValue.build());
        onChanged();
      } else {
        feesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public Builder addAllFees(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.ticket.Fee> values) {
      if (feesBuilder_ == null) {
        ensureFeesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, fees_);
        onChanged();
      } else {
        feesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public Builder clearFees() {
      if (feesBuilder_ == null) {
        fees_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
      } else {
        feesBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public Builder removeFees(int index) {
      if (feesBuilder_ == null) {
        ensureFeesIsMutable();
        fees_.remove(index);
        onChanged();
      } else {
        feesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder getFeesBuilder(
        int index) {
      return getFeesFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder getFeesOrBuilder(
        int index) {
      if (feesBuilder_ == null) {
        return fees_.get(index);  } else {
        return feesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder> 
         getFeesOrBuilderList() {
      if (feesBuilder_ != null) {
        return feesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(fees_);
      }
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder addFeesBuilder() {
      return getFeesFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.ticket.Fee.getDefaultInstance());
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder addFeesBuilder(
        int index) {
      return getFeesFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.ticket.Fee.getDefaultInstance());
    }
    /**
     * <pre>
     *费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees = 37;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder> 
         getFeesBuilderList() {
      return getFeesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Fee, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder, cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder> 
        getFeesFieldBuilder() {
      if (feesBuilder_ == null) {
        feesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.Fee, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder, cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder>(
                fees_,
                ((bitField0_ & 0x00000040) != 0),
                getParentForChildren(),
                isClean());
        fees_ = null;
      }
      return feesBuilder_;
    }

    private java.lang.Object timeZone_ = "";
    /**
     * <pre>
     *时区信息
     * </pre>
     *
     * <code>string timeZone = 38;</code>
     * @return The timeZone.
     */
    public java.lang.String getTimeZone() {
      java.lang.Object ref = timeZone_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        timeZone_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *时区信息
     * </pre>
     *
     * <code>string timeZone = 38;</code>
     * @return The bytes for timeZone.
     */
    public com.google.protobuf.ByteString
        getTimeZoneBytes() {
      java.lang.Object ref = timeZone_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        timeZone_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *时区信息
     * </pre>
     *
     * <code>string timeZone = 38;</code>
     * @param value The timeZone to set.
     * @return This builder for chaining.
     */
    public Builder setTimeZone(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      timeZone_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *时区信息
     * </pre>
     *
     * <code>string timeZone = 38;</code>
     * @return This builder for chaining.
     */
    public Builder clearTimeZone() {
      
      timeZone_ = getDefaultInstance().getTimeZone();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *时区信息
     * </pre>
     *
     * <code>string timeZone = 38;</code>
     * @param value The bytes for timeZone to set.
     * @return This builder for chaining.
     */
    public Builder setTimeZoneBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      timeZone_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object uploadTime_ = "";
    /**
     * <pre>
     *上传时间
     * </pre>
     *
     * <code>string upload_time = 39;</code>
     * @return The uploadTime.
     */
    public java.lang.String getUploadTime() {
      java.lang.Object ref = uploadTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        uploadTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *上传时间
     * </pre>
     *
     * <code>string upload_time = 39;</code>
     * @return The bytes for uploadTime.
     */
    public com.google.protobuf.ByteString
        getUploadTimeBytes() {
      java.lang.Object ref = uploadTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        uploadTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *上传时间
     * </pre>
     *
     * <code>string upload_time = 39;</code>
     * @param value The uploadTime to set.
     * @return This builder for chaining.
     */
    public Builder setUploadTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      uploadTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *上传时间
     * </pre>
     *
     * <code>string upload_time = 39;</code>
     * @return This builder for chaining.
     */
    public Builder clearUploadTime() {
      
      uploadTime_ = getDefaultInstance().getUploadTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *上传时间
     * </pre>
     *
     * <code>string upload_time = 39;</code>
     * @param value The bytes for uploadTime to set.
     * @return This builder for chaining.
     */
    public Builder setUploadTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      uploadTime_ = value;
      onChanged();
      return this;
    }

    private boolean discountProportioned_ ;
    /**
     * <pre>
     * 是否折扣分摊
     * </pre>
     *
     * <code>bool discount_proportioned = 40;</code>
     * @return The discountProportioned.
     */
    @java.lang.Override
    public boolean getDiscountProportioned() {
      return discountProportioned_;
    }
    /**
     * <pre>
     * 是否折扣分摊
     * </pre>
     *
     * <code>bool discount_proportioned = 40;</code>
     * @param value The discountProportioned to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountProportioned(boolean value) {
      
      discountProportioned_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 是否折扣分摊
     * </pre>
     *
     * <code>bool discount_proportioned = 40;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountProportioned() {
      
      discountProportioned_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object transactionNo_ = "";
    /**
     * <code>string transaction_no = 41;</code>
     * @return The transactionNo.
     */
    public java.lang.String getTransactionNo() {
      java.lang.Object ref = transactionNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        transactionNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string transaction_no = 41;</code>
     * @return The bytes for transactionNo.
     */
    public com.google.protobuf.ByteString
        getTransactionNoBytes() {
      java.lang.Object ref = transactionNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string transaction_no = 41;</code>
     * @param value The transactionNo to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      transactionNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string transaction_no = 41;</code>
     * @return This builder for chaining.
     */
    public Builder clearTransactionNo() {
      
      transactionNo_ = getDefaultInstance().getTransactionNo();
      onChanged();
      return this;
    }
    /**
     * <code>string transaction_no = 41;</code>
     * @param value The bytes for transactionNo to set.
     * @return This builder for chaining.
     */
    public Builder setTransactionNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      transactionNo_ = value;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee> feesNoAccount_ =
      java.util.Collections.emptyList();
    private void ensureFeesNoAccountIsMutable() {
      if (!((bitField0_ & 0x00000080) != 0)) {
        feesNoAccount_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.Fee>(feesNoAccount_);
        bitField0_ |= 0x00000080;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Fee, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder, cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder> feesNoAccountBuilder_;

    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee> getFeesNoAccountList() {
      if (feesNoAccountBuilder_ == null) {
        return java.util.Collections.unmodifiableList(feesNoAccount_);
      } else {
        return feesNoAccountBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public int getFeesNoAccountCount() {
      if (feesNoAccountBuilder_ == null) {
        return feesNoAccount_.size();
      } else {
        return feesNoAccountBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Fee getFeesNoAccount(int index) {
      if (feesNoAccountBuilder_ == null) {
        return feesNoAccount_.get(index);
      } else {
        return feesNoAccountBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public Builder setFeesNoAccount(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Fee value) {
      if (feesNoAccountBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureFeesNoAccountIsMutable();
        feesNoAccount_.set(index, value);
        onChanged();
      } else {
        feesNoAccountBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public Builder setFeesNoAccount(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder builderForValue) {
      if (feesNoAccountBuilder_ == null) {
        ensureFeesNoAccountIsMutable();
        feesNoAccount_.set(index, builderForValue.build());
        onChanged();
      } else {
        feesNoAccountBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public Builder addFeesNoAccount(cn.hexcloud.pbis.common.service.facade.ticket.Fee value) {
      if (feesNoAccountBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureFeesNoAccountIsMutable();
        feesNoAccount_.add(value);
        onChanged();
      } else {
        feesNoAccountBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public Builder addFeesNoAccount(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Fee value) {
      if (feesNoAccountBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureFeesNoAccountIsMutable();
        feesNoAccount_.add(index, value);
        onChanged();
      } else {
        feesNoAccountBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public Builder addFeesNoAccount(
        cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder builderForValue) {
      if (feesNoAccountBuilder_ == null) {
        ensureFeesNoAccountIsMutable();
        feesNoAccount_.add(builderForValue.build());
        onChanged();
      } else {
        feesNoAccountBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public Builder addFeesNoAccount(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder builderForValue) {
      if (feesNoAccountBuilder_ == null) {
        ensureFeesNoAccountIsMutable();
        feesNoAccount_.add(index, builderForValue.build());
        onChanged();
      } else {
        feesNoAccountBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public Builder addAllFeesNoAccount(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.ticket.Fee> values) {
      if (feesNoAccountBuilder_ == null) {
        ensureFeesNoAccountIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, feesNoAccount_);
        onChanged();
      } else {
        feesNoAccountBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public Builder clearFeesNoAccount() {
      if (feesNoAccountBuilder_ == null) {
        feesNoAccount_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
      } else {
        feesNoAccountBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public Builder removeFeesNoAccount(int index) {
      if (feesNoAccountBuilder_ == null) {
        ensureFeesNoAccountIsMutable();
        feesNoAccount_.remove(index);
        onChanged();
      } else {
        feesNoAccountBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder getFeesNoAccountBuilder(
        int index) {
      return getFeesNoAccountFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder getFeesNoAccountOrBuilder(
        int index) {
      if (feesNoAccountBuilder_ == null) {
        return feesNoAccount_.get(index);  } else {
        return feesNoAccountBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder> 
         getFeesNoAccountOrBuilderList() {
      if (feesNoAccountBuilder_ != null) {
        return feesNoAccountBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(feesNoAccount_);
      }
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder addFeesNoAccountBuilder() {
      return getFeesNoAccountFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.ticket.Fee.getDefaultInstance());
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder addFeesNoAccountBuilder(
        int index) {
      return getFeesNoAccountFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.ticket.Fee.getDefaultInstance());
    }
    /**
     * <pre>
     * 无需用户额外支付的费用信息
     * </pre>
     *
     * <code>repeated .coupon.Fee fees_no_account = 42;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder> 
         getFeesNoAccountBuilderList() {
      return getFeesNoAccountFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Fee, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder, cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder> 
        getFeesNoAccountFieldBuilder() {
      if (feesNoAccountBuilder_ == null) {
        feesNoAccountBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.Fee, cn.hexcloud.pbis.common.service.facade.ticket.Fee.Builder, cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder>(
                feesNoAccount_,
                ((bitField0_ & 0x00000080) != 0),
                getParentForChildren(),
                isClean());
        feesNoAccount_ = null;
      }
      return feesNoAccountBuilder_;
    }

    private cn.hexcloud.pbis.common.service.facade.ticket.Efficiency efficiency_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Efficiency, cn.hexcloud.pbis.common.service.facade.ticket.Efficiency.Builder, cn.hexcloud.pbis.common.service.facade.ticket.EfficiencyOrBuilder> efficiencyBuilder_;
    /**
     * <code>.coupon.Efficiency efficiency = 43;</code>
     * @return Whether the efficiency field is set.
     */
    public boolean hasEfficiency() {
      return efficiencyBuilder_ != null || efficiency_ != null;
    }
    /**
     * <code>.coupon.Efficiency efficiency = 43;</code>
     * @return The efficiency.
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Efficiency getEfficiency() {
      if (efficiencyBuilder_ == null) {
        return efficiency_ == null ? cn.hexcloud.pbis.common.service.facade.ticket.Efficiency.getDefaultInstance() : efficiency_;
      } else {
        return efficiencyBuilder_.getMessage();
      }
    }
    /**
     * <code>.coupon.Efficiency efficiency = 43;</code>
     */
    public Builder setEfficiency(cn.hexcloud.pbis.common.service.facade.ticket.Efficiency value) {
      if (efficiencyBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        efficiency_ = value;
        onChanged();
      } else {
        efficiencyBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <code>.coupon.Efficiency efficiency = 43;</code>
     */
    public Builder setEfficiency(
        cn.hexcloud.pbis.common.service.facade.ticket.Efficiency.Builder builderForValue) {
      if (efficiencyBuilder_ == null) {
        efficiency_ = builderForValue.build();
        onChanged();
      } else {
        efficiencyBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <code>.coupon.Efficiency efficiency = 43;</code>
     */
    public Builder mergeEfficiency(cn.hexcloud.pbis.common.service.facade.ticket.Efficiency value) {
      if (efficiencyBuilder_ == null) {
        if (efficiency_ != null) {
          efficiency_ =
            cn.hexcloud.pbis.common.service.facade.ticket.Efficiency.newBuilder(efficiency_).mergeFrom(value).buildPartial();
        } else {
          efficiency_ = value;
        }
        onChanged();
      } else {
        efficiencyBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <code>.coupon.Efficiency efficiency = 43;</code>
     */
    public Builder clearEfficiency() {
      if (efficiencyBuilder_ == null) {
        efficiency_ = null;
        onChanged();
      } else {
        efficiency_ = null;
        efficiencyBuilder_ = null;
      }

      return this;
    }
    /**
     * <code>.coupon.Efficiency efficiency = 43;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.Efficiency.Builder getEfficiencyBuilder() {
      
      onChanged();
      return getEfficiencyFieldBuilder().getBuilder();
    }
    /**
     * <code>.coupon.Efficiency efficiency = 43;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.EfficiencyOrBuilder getEfficiencyOrBuilder() {
      if (efficiencyBuilder_ != null) {
        return efficiencyBuilder_.getMessageOrBuilder();
      } else {
        return efficiency_ == null ?
            cn.hexcloud.pbis.common.service.facade.ticket.Efficiency.getDefaultInstance() : efficiency_;
      }
    }
    /**
     * <code>.coupon.Efficiency efficiency = 43;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.Efficiency, cn.hexcloud.pbis.common.service.facade.ticket.Efficiency.Builder, cn.hexcloud.pbis.common.service.facade.ticket.EfficiencyOrBuilder> 
        getEfficiencyFieldBuilder() {
      if (efficiencyBuilder_ == null) {
        efficiencyBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.Efficiency, cn.hexcloud.pbis.common.service.facade.ticket.Efficiency.Builder, cn.hexcloud.pbis.common.service.facade.ticket.EfficiencyOrBuilder>(
                getEfficiency(),
                getParentForChildren(),
                isClean());
        efficiency_ = null;
      }
      return efficiencyBuilder_;
    }

    private float weight_ ;
    /**
     * <pre>
     * 称重商品总重量(kg)
     * </pre>
     *
     * <code>float weight = 45;</code>
     * @return The weight.
     */
    @java.lang.Override
    public float getWeight() {
      return weight_;
    }
    /**
     * <pre>
     * 称重商品总重量(kg)
     * </pre>
     *
     * <code>float weight = 45;</code>
     * @param value The weight to set.
     * @return This builder for chaining.
     */
    public Builder setWeight(float value) {
      
      weight_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 称重商品总重量(kg)
     * </pre>
     *
     * <code>float weight = 45;</code>
     * @return This builder for chaining.
     */
    public Builder clearWeight() {
      
      weight_ = 0F;
      onChanged();
      return this;
    }

    private long partnerId_ ;
    /**
     * <pre>
     * 门店partner id
     * </pre>
     *
     * <code>uint64 partner_id = 44;</code>
     * @return The partnerId.
     */
    @java.lang.Override
    public long getPartnerId() {
      return partnerId_;
    }
    /**
     * <pre>
     * 门店partner id
     * </pre>
     *
     * <code>uint64 partner_id = 44;</code>
     * @param value The partnerId to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerId(long value) {
      
      partnerId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 门店partner id
     * </pre>
     *
     * <code>uint64 partner_id = 44;</code>
     * @return This builder for chaining.
     */
    public Builder clearPartnerId() {
      
      partnerId_ = 0L;
      onChanged();
      return this;
    }

    private boolean pendingSyncMember_ ;
    /**
     * <pre>
     * 是否待同步会员单
     * </pre>
     *
     * <code>bool pending_sync_member = 46;</code>
     * @return The pendingSyncMember.
     */
    @java.lang.Override
    public boolean getPendingSyncMember() {
      return pendingSyncMember_;
    }
    /**
     * <pre>
     * 是否待同步会员单
     * </pre>
     *
     * <code>bool pending_sync_member = 46;</code>
     * @param value The pendingSyncMember to set.
     * @return This builder for chaining.
     */
    public Builder setPendingSyncMember(boolean value) {
      
      pendingSyncMember_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 是否待同步会员单
     * </pre>
     *
     * <code>bool pending_sync_member = 46;</code>
     * @return This builder for chaining.
     */
    public Builder clearPendingSyncMember() {
      
      pendingSyncMember_ = false;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:coupon.Ticket)
  }

  // @@protoc_insertion_point(class_scope:coupon.Ticket)
  private static final cn.hexcloud.pbis.common.service.facade.ticket.Ticket DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.ticket.Ticket();
  }

  public static cn.hexcloud.pbis.common.service.facade.ticket.Ticket getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Ticket>
      PARSER = new com.google.protobuf.AbstractParser<Ticket>() {
    @java.lang.Override
    public Ticket parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Ticket(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Ticket> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Ticket> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.Ticket getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

