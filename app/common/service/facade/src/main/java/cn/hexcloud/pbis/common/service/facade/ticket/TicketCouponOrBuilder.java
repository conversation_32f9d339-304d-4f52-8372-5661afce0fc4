// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface TicketCouponOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.TicketCoupon)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool is_online = 1;</code>
   * @return The isOnline.
   */
  boolean getIsOnline();

  /**
   * <code>string id = 2;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 2;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string name = 3;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <code>string name = 3;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <code>string code = 4;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <code>string code = 4;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <code>int64 type = 5;</code>
   * @return The type.
   */
  long getType();

  /**
   * <code>double par_value = 6;</code>
   * @return The parValue.
   */
  double getParValue();

  /**
   * <code>string sequence_id = 7;</code>
   * @return The sequenceId.
   */
  java.lang.String getSequenceId();
  /**
   * <code>string sequence_id = 7;</code>
   * @return The bytes for sequenceId.
   */
  com.google.protobuf.ByteString
      getSequenceIdBytes();

  /**
   * <code>double price = 8;</code>
   * @return The price.
   */
  double getPrice();

  /**
   * <pre>
   *（实付金额）（=price-tp_allowance-merchant_allowance？）
   * </pre>
   *
   * <code>double cost = 9;</code>
   * @return The cost.
   */
  double getCost();

  /**
   * <pre>
   * (购买卡券时的优惠金额，平台部分)
   * </pre>
   *
   * <code>double tp_allowance = 10;</code>
   * @return The tpAllowance.
   */
  double getTpAllowance();

  /**
   * <pre>
   * (购买卡券时的优惠金额，商家部分)
   * </pre>
   *
   * <code>double merchant_allowance = 11;</code>
   * @return The merchantAllowance.
   */
  double getMerchantAllowance();

  /**
   * <pre>
   *商家实收
   * </pre>
   *
   * <code>double real_amount = 12;</code>
   * @return The realAmount.
   */
  double getRealAmount();

  /**
   * <pre>
   *折扣转支付金额
   * </pre>
   *
   * <code>double transfer_amount = 13;</code>
   * @return The transferAmount.
   */
  double getTransferAmount();

  /**
   * <code>double platform_allowance = 14;</code>
   * @return The platformAllowance.
   */
  double getPlatformAllowance();
}
