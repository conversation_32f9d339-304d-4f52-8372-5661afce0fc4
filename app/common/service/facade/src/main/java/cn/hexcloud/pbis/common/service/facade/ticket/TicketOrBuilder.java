// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface TicketOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.Ticket)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *订单uuid，全市场范围内唯一
   * </pre>
   *
   * <code>string ticket_id = 1;</code>
   * @return The ticketId.
   */
  java.lang.String getTicketId();
  /**
   * <pre>
   *订单uuid，全市场范围内唯一
   * </pre>
   *
   * <code>string ticket_id = 1;</code>
   * @return The bytes for ticketId.
   */
  com.google.protobuf.ByteString
      getTicketIdBytes();

  /**
   * <pre>
   *订单号，有特殊的业务规则
   * </pre>
   *
   * <code>string ticket_no = 2;</code>
   * @return The ticketNo.
   */
  java.lang.String getTicketNo();
  /**
   * <pre>
   *订单号，有特殊的业务规则
   * </pre>
   *
   * <code>string ticket_no = 2;</code>
   * @return The bytes for ticketNo.
   */
  com.google.protobuf.ByteString
      getTicketNoBytes();

  /**
   * <pre>
   *YYYY-MM-dd HH:MM:SS，订单开始时间
   * </pre>
   *
   * <code>string start_time = 3;</code>
   * @return The startTime.
   */
  java.lang.String getStartTime();
  /**
   * <pre>
   *YYYY-MM-dd HH:MM:SS，订单开始时间
   * </pre>
   *
   * <code>string start_time = 3;</code>
   * @return The bytes for startTime.
   */
  com.google.protobuf.ByteString
      getStartTimeBytes();

  /**
   * <pre>
   *YYYY-MM-dd HH:MM:SS，订单结束时间
   * </pre>
   *
   * <code>string end_time = 4;</code>
   * @return The endTime.
   */
  java.lang.String getEndTime();
  /**
   * <pre>
   *YYYY-MM-dd HH:MM:SS，订单结束时间
   * </pre>
   *
   * <code>string end_time = 4;</code>
   * @return The bytes for endTime.
   */
  com.google.protobuf.ByteString
      getEndTimeBytes();

  /**
   * <pre>
   *YYYY-MM-dd，订单营业日期
   * </pre>
   *
   * <code>string bus_date = 5;</code>
   * @return The busDate.
   */
  java.lang.String getBusDate();
  /**
   * <pre>
   *YYYY-MM-dd，订单营业日期
   * </pre>
   *
   * <code>string bus_date = 5;</code>
   * @return The bytes for busDate.
   */
  com.google.protobuf.ByteString
      getBusDateBytes();

  /**
   * <pre>
   *pos信息
   * </pre>
   *
   * <code>.coupon.Pos pos = 6;</code>
   * @return Whether the pos field is set.
   */
  boolean hasPos();
  /**
   * <pre>
   *pos信息
   * </pre>
   *
   * <code>.coupon.Pos pos = 6;</code>
   * @return The pos.
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Pos getPos();
  /**
   * <pre>
   *pos信息
   * </pre>
   *
   * <code>.coupon.Pos pos = 6;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.PosOrBuilder getPosOrBuilder();

  /**
   * <pre>
   *收银员信息
   * </pre>
   *
   * <code>.coupon.Operator operator = 7;</code>
   * @return Whether the operator field is set.
   */
  boolean hasOperator();
  /**
   * <pre>
   *收银员信息
   * </pre>
   *
   * <code>.coupon.Operator operator = 7;</code>
   * @return The operator.
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Operator getOperator();
  /**
   * <pre>
   *收银员信息
   * </pre>
   *
   * <code>.coupon.Operator operator = 7;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.OperatorOrBuilder getOperatorOrBuilder();

  /**
   * <pre>
   *金额信息
   * </pre>
   *
   * <code>.coupon.Amount amounts = 8;</code>
   * @return Whether the amounts field is set.
   */
  boolean hasAmounts();
  /**
   * <pre>
   *金额信息
   * </pre>
   *
   * <code>.coupon.Amount amounts = 8;</code>
   * @return The amounts.
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Amount getAmounts();
  /**
   * <pre>
   *金额信息
   * </pre>
   *
   * <code>.coupon.Amount amounts = 8;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.AmountOrBuilder getAmountsOrBuilder();

  /**
   * <pre>
   *取餐号
   * </pre>
   *
   * <code>string takemealNumber = 9;</code>
   * @return The takemealNumber.
   */
  java.lang.String getTakemealNumber();
  /**
   * <pre>
   *取餐号
   * </pre>
   *
   * <code>string takemealNumber = 9;</code>
   * @return The bytes for takemealNumber.
   */
  com.google.protobuf.ByteString
      getTakemealNumberBytes();

  /**
   * <pre>
   *订单商品总数
   * </pre>
   *
   * <code>int32 qty = 10;</code>
   * @return The qty.
   */
  int getQty();

  /**
   * <code>string status = 11;</code>
   * @return The status.
   */
  java.lang.String getStatus();
  /**
   * <code>string status = 11;</code>
   * @return The bytes for status.
   */
  com.google.protobuf.ByteString
      getStatusBytes();

  /**
   * <pre>
   *订单的退款相关信息
   * </pre>
   *
   * <code>.coupon.RefundInfo refundInfo = 12;</code>
   * @return Whether the refundInfo field is set.
   */
  boolean hasRefundInfo();
  /**
   * <pre>
   *订单的退款相关信息
   * </pre>
   *
   * <code>.coupon.RefundInfo refundInfo = 12;</code>
   * @return The refundInfo.
   */
  cn.hexcloud.pbis.common.service.facade.ticket.RefundInfo getRefundInfo();
  /**
   * <pre>
   *订单的退款相关信息
   * </pre>
   *
   * <code>.coupon.RefundInfo refundInfo = 12;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.RefundInfoOrBuilder getRefundInfoOrBuilder();

  /**
   * <pre>
   *订单的渠道信息
   * </pre>
   *
   * <code>.coupon.Channel channel = 13;</code>
   * @return Whether the channel field is set.
   */
  boolean hasChannel();
  /**
   * <pre>
   *订单的渠道信息
   * </pre>
   *
   * <code>.coupon.Channel channel = 13;</code>
   * @return The channel.
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Channel getChannel();
  /**
   * <pre>
   *订单的渠道信息
   * </pre>
   *
   * <code>.coupon.Channel channel = 13;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.ChannelOrBuilder getChannelOrBuilder();

  /**
   * <pre>
   *订单商品信息
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct products = 14;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> 
      getProductsList();
  /**
   * <pre>
   *订单商品信息
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct products = 14;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct getProducts(int index);
  /**
   * <pre>
   *订单商品信息
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct products = 14;</code>
   */
  int getProductsCount();
  /**
   * <pre>
   *订单商品信息
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct products = 14;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder> 
      getProductsOrBuilderList();
  /**
   * <pre>
   *订单商品信息
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct products = 14;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder getProductsOrBuilder(
      int index);

  /**
   * <pre>
   *订单的支付信息
   * </pre>
   *
   * <code>repeated .coupon.Payment payments = 15;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Payment> 
      getPaymentsList();
  /**
   * <pre>
   *订单的支付信息
   * </pre>
   *
   * <code>repeated .coupon.Payment payments = 15;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Payment getPayments(int index);
  /**
   * <pre>
   *订单的支付信息
   * </pre>
   *
   * <code>repeated .coupon.Payment payments = 15;</code>
   */
  int getPaymentsCount();
  /**
   * <pre>
   *订单的支付信息
   * </pre>
   *
   * <code>repeated .coupon.Payment payments = 15;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.PaymentOrBuilder> 
      getPaymentsOrBuilderList();
  /**
   * <pre>
   *订单的支付信息
   * </pre>
   *
   * <code>repeated .coupon.Payment payments = 15;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.PaymentOrBuilder getPaymentsOrBuilder(
      int index);

  /**
   * <pre>
   *订单的促销信息
   * </pre>
   *
   * <code>repeated .coupon.Promotion promotions = 16;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Promotion> 
      getPromotionsList();
  /**
   * <pre>
   *订单的促销信息
   * </pre>
   *
   * <code>repeated .coupon.Promotion promotions = 16;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Promotion getPromotions(int index);
  /**
   * <pre>
   *订单的促销信息
   * </pre>
   *
   * <code>repeated .coupon.Promotion promotions = 16;</code>
   */
  int getPromotionsCount();
  /**
   * <pre>
   *订单的促销信息
   * </pre>
   *
   * <code>repeated .coupon.Promotion promotions = 16;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.PromotionOrBuilder> 
      getPromotionsOrBuilderList();
  /**
   * <pre>
   *订单的促销信息
   * </pre>
   *
   * <code>repeated .coupon.Promotion promotions = 16;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.PromotionOrBuilder getPromotionsOrBuilder(
      int index);

  /**
   * <pre>
   *订单的会员信息
   * </pre>
   *
   * <code>repeated .coupon.Member members = 17;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Member> 
      getMembersList();
  /**
   * <pre>
   *订单的会员信息
   * </pre>
   *
   * <code>repeated .coupon.Member members = 17;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Member getMembers(int index);
  /**
   * <pre>
   *订单的会员信息
   * </pre>
   *
   * <code>repeated .coupon.Member members = 17;</code>
   */
  int getMembersCount();
  /**
   * <pre>
   *订单的会员信息
   * </pre>
   *
   * <code>repeated .coupon.Member members = 17;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.MemberOrBuilder> 
      getMembersOrBuilderList();
  /**
   * <pre>
   *订单的会员信息
   * </pre>
   *
   * <code>repeated .coupon.Member members = 17;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.MemberOrBuilder getMembersOrBuilder(
      int index);

  /**
   * <pre>
   *桌位信息
   * </pre>
   *
   * <code>.coupon.Table table = 18;</code>
   * @return Whether the table field is set.
   */
  boolean hasTable();
  /**
   * <pre>
   *桌位信息
   * </pre>
   *
   * <code>.coupon.Table table = 18;</code>
   * @return The table.
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Table getTable();
  /**
   * <pre>
   *桌位信息
   * </pre>
   *
   * <code>.coupon.Table table = 18;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.TableOrBuilder getTableOrBuilder();

  /**
   * <pre>
   *订单人数
   * </pre>
   *
   * <code>int32 people = 19;</code>
   * @return The people.
   */
  int getPeople();

  /**
   * <pre>
   *房间号
   * </pre>
   *
   * <code>string room_no = 20;</code>
   * @return The roomNo.
   */
  java.lang.String getRoomNo();
  /**
   * <pre>
   *房间号
   * </pre>
   *
   * <code>string room_no = 20;</code>
   * @return The bytes for roomNo.
   */
  com.google.protobuf.ByteString
      getRoomNoBytes();

  /**
   * <pre>
   *订单备注
   * </pre>
   *
   * <code>string remark = 21;</code>
   * @return The remark.
   */
  java.lang.String getRemark();
  /**
   * <pre>
   *订单备注
   * </pre>
   *
   * <code>string remark = 21;</code>
   * @return The bytes for remark.
   */
  com.google.protobuf.ByteString
      getRemarkBytes();

  /**
   * <pre>
   *如家场景，意义待明确
   * </pre>
   *
   * <code>bool house_ac = 22;</code>
   * @return The houseAc.
   */
  boolean getHouseAc();

  /**
   * <pre>
   *早中晚餐标志，枚举值
   * </pre>
   *
   * <code>string order_time_type = 23;</code>
   * @return The orderTimeType.
   */
  java.lang.String getOrderTimeType();
  /**
   * <pre>
   *早中晚餐标志，枚举值
   * </pre>
   *
   * <code>string order_time_type = 23;</code>
   * @return The bytes for orderTimeType.
   */
  com.google.protobuf.ByteString
      getOrderTimeTypeBytes();

  /**
   * <pre>
   *班次号
   * </pre>
   *
   * <code>string shiftNumber = 24;</code>
   * @return The shiftNumber.
   */
  java.lang.String getShiftNumber();
  /**
   * <pre>
   *班次号
   * </pre>
   *
   * <code>string shiftNumber = 24;</code>
   * @return The bytes for shiftNumber.
   */
  com.google.protobuf.ByteString
      getShiftNumberBytes();

  /**
   * <pre>
   *税项
   * </pre>
   *
   * <code>repeated .coupon.Tax taxList = 25;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Tax> 
      getTaxListList();
  /**
   * <pre>
   *税项
   * </pre>
   *
   * <code>repeated .coupon.Tax taxList = 25;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Tax getTaxList(int index);
  /**
   * <pre>
   *税项
   * </pre>
   *
   * <code>repeated .coupon.Tax taxList = 25;</code>
   */
  int getTaxListCount();
  /**
   * <pre>
   *税项
   * </pre>
   *
   * <code>repeated .coupon.Tax taxList = 25;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.TaxOrBuilder> 
      getTaxListOrBuilderList();
  /**
   * <pre>
   *税项
   * </pre>
   *
   * <code>repeated .coupon.Tax taxList = 25;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.TaxOrBuilder getTaxListOrBuilder(
      int index);

  /**
   * <pre>
   *门店信息
   * </pre>
   *
   * <code>.coupon.Store store = 26;</code>
   * @return Whether the store field is set.
   */
  boolean hasStore();
  /**
   * <pre>
   *门店信息
   * </pre>
   *
   * <code>.coupon.Store store = 26;</code>
   * @return The store.
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Store getStore();
  /**
   * <pre>
   *门店信息
   * </pre>
   *
   * <code>.coupon.Store store = 26;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.StoreOrBuilder getStoreOrBuilder();

  /**
   * <pre>
   *外卖信息
   * </pre>
   *
   * <code>.coupon.Takeaway takeaway_info = 34;</code>
   * @return Whether the takeawayInfo field is set.
   */
  boolean hasTakeawayInfo();
  /**
   * <pre>
   *外卖信息
   * </pre>
   *
   * <code>.coupon.Takeaway takeaway_info = 34;</code>
   * @return The takeawayInfo.
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Takeaway getTakeawayInfo();
  /**
   * <pre>
   *外卖信息
   * </pre>
   *
   * <code>.coupon.Takeaway takeaway_info = 34;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.TakeawayOrBuilder getTakeawayInfoOrBuilder();

  /**
   * <pre>
   *订单唯一流水号
   * </pre>
   *
   * <code>string ticketUno = 35;</code>
   * @return The ticketUno.
   */
  java.lang.String getTicketUno();
  /**
   * <pre>
   *订单唯一流水号
   * </pre>
   *
   * <code>string ticketUno = 35;</code>
   * @return The bytes for ticketUno.
   */
  com.google.protobuf.ByteString
      getTicketUnoBytes();

  /**
   * <pre>
   *卡券信息
   * </pre>
   *
   * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon> 
      getCouponsList();
  /**
   * <pre>
   *卡券信息
   * </pre>
   *
   * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.TicketCoupon getCoupons(int index);
  /**
   * <pre>
   *卡券信息
   * </pre>
   *
   * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
   */
  int getCouponsCount();
  /**
   * <pre>
   *卡券信息
   * </pre>
   *
   * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketCouponOrBuilder> 
      getCouponsOrBuilderList();
  /**
   * <pre>
   *卡券信息
   * </pre>
   *
   * <code>repeated .coupon.TicketCoupon coupons = 36;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.TicketCouponOrBuilder getCouponsOrBuilder(
      int index);

  /**
   * <pre>
   *费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees = 37;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee> 
      getFeesList();
  /**
   * <pre>
   *费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees = 37;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Fee getFees(int index);
  /**
   * <pre>
   *费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees = 37;</code>
   */
  int getFeesCount();
  /**
   * <pre>
   *费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees = 37;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder> 
      getFeesOrBuilderList();
  /**
   * <pre>
   *费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees = 37;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder getFeesOrBuilder(
      int index);

  /**
   * <pre>
   *时区信息
   * </pre>
   *
   * <code>string timeZone = 38;</code>
   * @return The timeZone.
   */
  java.lang.String getTimeZone();
  /**
   * <pre>
   *时区信息
   * </pre>
   *
   * <code>string timeZone = 38;</code>
   * @return The bytes for timeZone.
   */
  com.google.protobuf.ByteString
      getTimeZoneBytes();

  /**
   * <pre>
   *上传时间
   * </pre>
   *
   * <code>string upload_time = 39;</code>
   * @return The uploadTime.
   */
  java.lang.String getUploadTime();
  /**
   * <pre>
   *上传时间
   * </pre>
   *
   * <code>string upload_time = 39;</code>
   * @return The bytes for uploadTime.
   */
  com.google.protobuf.ByteString
      getUploadTimeBytes();

  /**
   * <pre>
   * 是否折扣分摊
   * </pre>
   *
   * <code>bool discount_proportioned = 40;</code>
   * @return The discountProportioned.
   */
  boolean getDiscountProportioned();

  /**
   * <code>string transaction_no = 41;</code>
   * @return The transactionNo.
   */
  java.lang.String getTransactionNo();
  /**
   * <code>string transaction_no = 41;</code>
   * @return The bytes for transactionNo.
   */
  com.google.protobuf.ByteString
      getTransactionNoBytes();

  /**
   * <pre>
   * 无需用户额外支付的费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees_no_account = 42;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.Fee> 
      getFeesNoAccountList();
  /**
   * <pre>
   * 无需用户额外支付的费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees_no_account = 42;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Fee getFeesNoAccount(int index);
  /**
   * <pre>
   * 无需用户额外支付的费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees_no_account = 42;</code>
   */
  int getFeesNoAccountCount();
  /**
   * <pre>
   * 无需用户额外支付的费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees_no_account = 42;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder> 
      getFeesNoAccountOrBuilderList();
  /**
   * <pre>
   * 无需用户额外支付的费用信息
   * </pre>
   *
   * <code>repeated .coupon.Fee fees_no_account = 42;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.FeeOrBuilder getFeesNoAccountOrBuilder(
      int index);

  /**
   * <code>.coupon.Efficiency efficiency = 43;</code>
   * @return Whether the efficiency field is set.
   */
  boolean hasEfficiency();
  /**
   * <code>.coupon.Efficiency efficiency = 43;</code>
   * @return The efficiency.
   */
  cn.hexcloud.pbis.common.service.facade.ticket.Efficiency getEfficiency();
  /**
   * <code>.coupon.Efficiency efficiency = 43;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.EfficiencyOrBuilder getEfficiencyOrBuilder();

  /**
   * <pre>
   * 称重商品总重量(kg)
   * </pre>
   *
   * <code>float weight = 45;</code>
   * @return The weight.
   */
  float getWeight();

  /**
   * <pre>
   * 门店partner id
   * </pre>
   *
   * <code>uint64 partner_id = 44;</code>
   * @return The partnerId.
   */
  long getPartnerId();

  /**
   * <pre>
   * 是否待同步会员单
   * </pre>
   *
   * <code>bool pending_sync_member = 46;</code>
   * @return The pendingSyncMember.
   */
  boolean getPendingSyncMember();
}
