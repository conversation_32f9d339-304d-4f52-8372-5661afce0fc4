// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

/**
 * <pre>
 *订单商品
 * </pre>
 *
 * Protobuf type {@code coupon.TicketProduct}
 */
public final class TicketProduct extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:coupon.TicketProduct)
    TicketProductOrBuilder {
private static final long serialVersionUID = 0L;
  // Use TicketProduct.newBuilder() to construct.
  private TicketProduct(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private TicketProduct() {
    id_ = "";
    name_ = "";
    code_ = "";
    type_ = "";
    accessories_ = java.util.Collections.emptyList();
    comboItems_ = java.util.Collections.emptyList();
    operationRecords_ = "";
    skuRemark_ = java.util.Collections.emptyList();
    remark_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new TicketProduct();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private TicketProduct(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            id_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            code_ = s;
            break;
          }
          case 32: {

            seqId_ = input.readInt64();
            break;
          }
          case 41: {

            price_ = input.readDouble();
            break;
          }
          case 49: {

            amount_ = input.readDouble();
            break;
          }
          case 56: {

            qty_ = input.readInt32();
            break;
          }
          case 65: {

            discountAmount_ = input.readDouble();
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            type_ = s;
            break;
          }
          case 82: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              accessories_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct>();
              mutable_bitField0_ |= 0x00000001;
            }
            accessories_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.parser(), extensionRegistry));
            break;
          }
          case 90: {
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              comboItems_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct>();
              mutable_bitField0_ |= 0x00000002;
            }
            comboItems_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.parser(), extensionRegistry));
            break;
          }
          case 98: {
            java.lang.String s = input.readStringRequireUtf8();

            operationRecords_ = s;
            break;
          }
          case 106: {
            if (!((mutable_bitField0_ & 0x00000004) != 0)) {
              skuRemark_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark>();
              mutable_bitField0_ |= 0x00000004;
            }
            skuRemark_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.parser(), extensionRegistry));
            break;
          }
          case 114: {
            java.lang.String s = input.readStringRequireUtf8();

            remark_ = s;
            break;
          }
          case 121: {

            taxAmount_ = input.readDouble();
            break;
          }
          case 129: {

            netAmount_ = input.readDouble();
            break;
          }
          case 141: {

            avgMakeSpan_ = input.readFloat();
            break;
          }
          case 149: {

            weight_ = input.readFloat();
            break;
          }
          case 152: {

            hasMakeSpan_ = input.readBool();
            break;
          }
          case 161: {

            sumDiscountAmount_ = input.readDouble();
            break;
          }
          case 169: {

            sumNetAmount_ = input.readDouble();
            break;
          }
          case 177: {

            sumAmount_ = input.readDouble();
            break;
          }
          case 184: {

            hasWeight_ = input.readBool();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        accessories_ = java.util.Collections.unmodifiableList(accessories_);
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        comboItems_ = java.util.Collections.unmodifiableList(comboItems_);
      }
      if (((mutable_bitField0_ & 0x00000004) != 0)) {
        skuRemark_ = java.util.Collections.unmodifiableList(skuRemark_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_TicketProduct_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_TicketProduct_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.class, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  private volatile java.lang.Object id_;
  /**
   * <pre>
   *商品id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *商品id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 2;
  private volatile java.lang.Object name_;
  /**
   * <pre>
   *商品名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *商品名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CODE_FIELD_NUMBER = 3;
  private volatile java.lang.Object code_;
  /**
   * <pre>
   *商品编码
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The code.
   */
  @java.lang.Override
  public java.lang.String getCode() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      code_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *商品编码
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The bytes for code.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCodeBytes() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      code_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SEQ_ID_FIELD_NUMBER = 4;
  private long seqId_;
  /**
   * <pre>
   *商品的顺序号，代表下单的顺序
   * </pre>
   *
   * <code>int64 seq_id = 4;</code>
   * @return The seqId.
   */
  @java.lang.Override
  public long getSeqId() {
    return seqId_;
  }

  public static final int PRICE_FIELD_NUMBER = 5;
  private double price_;
  /**
   * <pre>
   *商品单价
   * </pre>
   *
   * <code>double price = 5;</code>
   * @return The price.
   */
  @java.lang.Override
  public double getPrice() {
    return price_;
  }

  public static final int AMOUNT_FIELD_NUMBER = 6;
  private double amount_;
  /**
   * <pre>
   *商品总价
   * </pre>
   *
   * <code>double amount = 6;</code>
   * @return The amount.
   */
  @java.lang.Override
  public double getAmount() {
    return amount_;
  }

  public static final int QTY_FIELD_NUMBER = 7;
  private int qty_;
  /**
   * <pre>
   *商品数量
   * </pre>
   *
   * <code>int32 qty = 7;</code>
   * @return The qty.
   */
  @java.lang.Override
  public int getQty() {
    return qty_;
  }

  public static final int DISCOUNT_AMOUNT_FIELD_NUMBER = 8;
  private double discountAmount_;
  /**
   * <pre>
   *商品的折扣金额
   * </pre>
   *
   * <code>double discount_amount = 8;</code>
   * @return The discountAmount.
   */
  @java.lang.Override
  public double getDiscountAmount() {
    return discountAmount_;
  }

  public static final int TYPE_FIELD_NUMBER = 9;
  private volatile java.lang.Object type_;
  /**
   * <pre>
   *商品类型
   * </pre>
   *
   * <code>string type = 9;</code>
   * @return The type.
   */
  @java.lang.Override
  public java.lang.String getType() {
    java.lang.Object ref = type_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      type_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *商品类型
   * </pre>
   *
   * <code>string type = 9;</code>
   * @return The bytes for type.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTypeBytes() {
    java.lang.Object ref = type_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      type_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACCESSORIES_FIELD_NUMBER = 10;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> accessories_;
  /**
   * <pre>
   *加料
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct accessories = 10;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> getAccessoriesList() {
    return accessories_;
  }
  /**
   * <pre>
   *加料
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct accessories = 10;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder> 
      getAccessoriesOrBuilderList() {
    return accessories_;
  }
  /**
   * <pre>
   *加料
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct accessories = 10;</code>
   */
  @java.lang.Override
  public int getAccessoriesCount() {
    return accessories_.size();
  }
  /**
   * <pre>
   *加料
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct accessories = 10;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct getAccessories(int index) {
    return accessories_.get(index);
  }
  /**
   * <pre>
   *加料
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct accessories = 10;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder getAccessoriesOrBuilder(
      int index) {
    return accessories_.get(index);
  }

  public static final int COMBO_ITEMS_FIELD_NUMBER = 11;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> comboItems_;
  /**
   * <pre>
   *套餐子项
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> getComboItemsList() {
    return comboItems_;
  }
  /**
   * <pre>
   *套餐子项
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder> 
      getComboItemsOrBuilderList() {
    return comboItems_;
  }
  /**
   * <pre>
   *套餐子项
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
   */
  @java.lang.Override
  public int getComboItemsCount() {
    return comboItems_.size();
  }
  /**
   * <pre>
   *套餐子项
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct getComboItems(int index) {
    return comboItems_.get(index);
  }
  /**
   * <pre>
   *套餐子项
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder getComboItemsOrBuilder(
      int index) {
    return comboItems_.get(index);
  }

  public static final int OPERATION_RECORDS_FIELD_NUMBER = 12;
  private volatile java.lang.Object operationRecords_;
  /**
   * <pre>
   *操作记录
   * </pre>
   *
   * <code>string operation_records = 12;</code>
   * @return The operationRecords.
   */
  @java.lang.Override
  public java.lang.String getOperationRecords() {
    java.lang.Object ref = operationRecords_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      operationRecords_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *操作记录
   * </pre>
   *
   * <code>string operation_records = 12;</code>
   * @return The bytes for operationRecords.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOperationRecordsBytes() {
    java.lang.Object ref = operationRecords_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      operationRecords_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SKUREMARK_FIELD_NUMBER = 13;
  private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark> skuRemark_;
  /**
   * <pre>
   *sku属性
   * </pre>
   *
   * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark> getSkuRemarkList() {
    return skuRemark_;
  }
  /**
   * <pre>
   *sku属性
   * </pre>
   *
   * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.SkuRemarkOrBuilder> 
      getSkuRemarkOrBuilderList() {
    return skuRemark_;
  }
  /**
   * <pre>
   *sku属性
   * </pre>
   *
   * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
   */
  @java.lang.Override
  public int getSkuRemarkCount() {
    return skuRemark_.size();
  }
  /**
   * <pre>
   *sku属性
   * </pre>
   *
   * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark getSkuRemark(int index) {
    return skuRemark_.get(index);
  }
  /**
   * <pre>
   *sku属性
   * </pre>
   *
   * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.SkuRemarkOrBuilder getSkuRemarkOrBuilder(
      int index) {
    return skuRemark_.get(index);
  }

  public static final int REMARK_FIELD_NUMBER = 14;
  private volatile java.lang.Object remark_;
  /**
   * <pre>
   *商品备注
   * </pre>
   *
   * <code>string remark = 14;</code>
   * @return The remark.
   */
  @java.lang.Override
  public java.lang.String getRemark() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      remark_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *商品备注
   * </pre>
   *
   * <code>string remark = 14;</code>
   * @return The bytes for remark.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRemarkBytes() {
    java.lang.Object ref = remark_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      remark_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TAXAMOUNT_FIELD_NUMBER = 15;
  private double taxAmount_;
  /**
   * <pre>
   *税额
   * </pre>
   *
   * <code>double taxAmount = 15;</code>
   * @return The taxAmount.
   */
  @java.lang.Override
  public double getTaxAmount() {
    return taxAmount_;
  }

  public static final int NET_AMOUNT_FIELD_NUMBER = 16;
  private double netAmount_;
  /**
   * <pre>
   *净额
   * </pre>
   *
   * <code>double net_amount = 16;</code>
   * @return The netAmount.
   */
  @java.lang.Override
  public double getNetAmount() {
    return netAmount_;
  }

  public static final int AVG_MAKE_SPAN_FIELD_NUMBER = 17;
  private float avgMakeSpan_;
  /**
   * <pre>
   *商品组平均制作时长
   * </pre>
   *
   * <code>float avg_make_span = 17;</code>
   * @return The avgMakeSpan.
   */
  @java.lang.Override
  public float getAvgMakeSpan() {
    return avgMakeSpan_;
  }

  public static final int WEIGHT_FIELD_NUMBER = 18;
  private float weight_;
  /**
   * <pre>
   *重量(kg)
   * </pre>
   *
   * <code>float weight = 18;</code>
   * @return The weight.
   */
  @java.lang.Override
  public float getWeight() {
    return weight_;
  }

  public static final int HAS_MAKE_SPAN_FIELD_NUMBER = 19;
  private boolean hasMakeSpan_;
  /**
   * <pre>
   *是否有制作时长信息
   * </pre>
   *
   * <code>bool has_make_span = 19;</code>
   * @return The hasMakeSpan.
   */
  @java.lang.Override
  public boolean getHasMakeSpan() {
    return hasMakeSpan_;
  }

  public static final int SUM_DISCOUNT_AMOUNT_FIELD_NUMBER = 20;
  private double sumDiscountAmount_;
  /**
   * <pre>
   *商品折扣
   * </pre>
   *
   * <code>double sum_discount_amount = 20;</code>
   * @return The sumDiscountAmount.
   */
  @java.lang.Override
  public double getSumDiscountAmount() {
    return sumDiscountAmount_;
  }

  public static final int SUM_NET_AMOUNT_FIELD_NUMBER = 21;
  private double sumNetAmount_;
  /**
   * <pre>
   *商品实收
   * </pre>
   *
   * <code>double sum_net_amount = 21;</code>
   * @return The sumNetAmount.
   */
  @java.lang.Override
  public double getSumNetAmount() {
    return sumNetAmount_;
  }

  public static final int SUM_AMOUNT_FIELD_NUMBER = 22;
  private double sumAmount_;
  /**
   * <pre>
   *商品原价
   * </pre>
   *
   * <code>double sum_amount = 22;</code>
   * @return The sumAmount.
   */
  @java.lang.Override
  public double getSumAmount() {
    return sumAmount_;
  }

  public static final int HAS_WEIGHT_FIELD_NUMBER = 23;
  private boolean hasWeight_;
  /**
   * <pre>
   *是否称重商品
   * </pre>
   *
   * <code>bool has_weight = 23;</code>
   * @return The hasWeight.
   */
  @java.lang.Override
  public boolean getHasWeight() {
    return hasWeight_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!getNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
    }
    if (!getCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, code_);
    }
    if (seqId_ != 0L) {
      output.writeInt64(4, seqId_);
    }
    if (price_ != 0D) {
      output.writeDouble(5, price_);
    }
    if (amount_ != 0D) {
      output.writeDouble(6, amount_);
    }
    if (qty_ != 0) {
      output.writeInt32(7, qty_);
    }
    if (discountAmount_ != 0D) {
      output.writeDouble(8, discountAmount_);
    }
    if (!getTypeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, type_);
    }
    for (int i = 0; i < accessories_.size(); i++) {
      output.writeMessage(10, accessories_.get(i));
    }
    for (int i = 0; i < comboItems_.size(); i++) {
      output.writeMessage(11, comboItems_.get(i));
    }
    if (!getOperationRecordsBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 12, operationRecords_);
    }
    for (int i = 0; i < skuRemark_.size(); i++) {
      output.writeMessage(13, skuRemark_.get(i));
    }
    if (!getRemarkBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 14, remark_);
    }
    if (taxAmount_ != 0D) {
      output.writeDouble(15, taxAmount_);
    }
    if (netAmount_ != 0D) {
      output.writeDouble(16, netAmount_);
    }
    if (avgMakeSpan_ != 0F) {
      output.writeFloat(17, avgMakeSpan_);
    }
    if (weight_ != 0F) {
      output.writeFloat(18, weight_);
    }
    if (hasMakeSpan_ != false) {
      output.writeBool(19, hasMakeSpan_);
    }
    if (sumDiscountAmount_ != 0D) {
      output.writeDouble(20, sumDiscountAmount_);
    }
    if (sumNetAmount_ != 0D) {
      output.writeDouble(21, sumNetAmount_);
    }
    if (sumAmount_ != 0D) {
      output.writeDouble(22, sumAmount_);
    }
    if (hasWeight_ != false) {
      output.writeBool(23, hasWeight_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!getNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
    }
    if (!getCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, code_);
    }
    if (seqId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, seqId_);
    }
    if (price_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(5, price_);
    }
    if (amount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(6, amount_);
    }
    if (qty_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, qty_);
    }
    if (discountAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(8, discountAmount_);
    }
    if (!getTypeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, type_);
    }
    for (int i = 0; i < accessories_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(10, accessories_.get(i));
    }
    for (int i = 0; i < comboItems_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(11, comboItems_.get(i));
    }
    if (!getOperationRecordsBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, operationRecords_);
    }
    for (int i = 0; i < skuRemark_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(13, skuRemark_.get(i));
    }
    if (!getRemarkBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, remark_);
    }
    if (taxAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(15, taxAmount_);
    }
    if (netAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(16, netAmount_);
    }
    if (avgMakeSpan_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(17, avgMakeSpan_);
    }
    if (weight_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(18, weight_);
    }
    if (hasMakeSpan_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(19, hasMakeSpan_);
    }
    if (sumDiscountAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(20, sumDiscountAmount_);
    }
    if (sumNetAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(21, sumNetAmount_);
    }
    if (sumAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(22, sumAmount_);
    }
    if (hasWeight_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(23, hasWeight_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct other = (cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (!getCode()
        .equals(other.getCode())) return false;
    if (getSeqId()
        != other.getSeqId()) return false;
    if (java.lang.Double.doubleToLongBits(getPrice())
        != java.lang.Double.doubleToLongBits(
            other.getPrice())) return false;
    if (java.lang.Double.doubleToLongBits(getAmount())
        != java.lang.Double.doubleToLongBits(
            other.getAmount())) return false;
    if (getQty()
        != other.getQty()) return false;
    if (java.lang.Double.doubleToLongBits(getDiscountAmount())
        != java.lang.Double.doubleToLongBits(
            other.getDiscountAmount())) return false;
    if (!getType()
        .equals(other.getType())) return false;
    if (!getAccessoriesList()
        .equals(other.getAccessoriesList())) return false;
    if (!getComboItemsList()
        .equals(other.getComboItemsList())) return false;
    if (!getOperationRecords()
        .equals(other.getOperationRecords())) return false;
    if (!getSkuRemarkList()
        .equals(other.getSkuRemarkList())) return false;
    if (!getRemark()
        .equals(other.getRemark())) return false;
    if (java.lang.Double.doubleToLongBits(getTaxAmount())
        != java.lang.Double.doubleToLongBits(
            other.getTaxAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getNetAmount())
        != java.lang.Double.doubleToLongBits(
            other.getNetAmount())) return false;
    if (java.lang.Float.floatToIntBits(getAvgMakeSpan())
        != java.lang.Float.floatToIntBits(
            other.getAvgMakeSpan())) return false;
    if (java.lang.Float.floatToIntBits(getWeight())
        != java.lang.Float.floatToIntBits(
            other.getWeight())) return false;
    if (getHasMakeSpan()
        != other.getHasMakeSpan()) return false;
    if (java.lang.Double.doubleToLongBits(getSumDiscountAmount())
        != java.lang.Double.doubleToLongBits(
            other.getSumDiscountAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getSumNetAmount())
        != java.lang.Double.doubleToLongBits(
            other.getSumNetAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getSumAmount())
        != java.lang.Double.doubleToLongBits(
            other.getSumAmount())) return false;
    if (getHasWeight()
        != other.getHasWeight()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCode().hashCode();
    hash = (37 * hash) + SEQ_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getSeqId());
    hash = (37 * hash) + PRICE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPrice()));
    hash = (37 * hash) + AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getAmount()));
    hash = (37 * hash) + QTY_FIELD_NUMBER;
    hash = (53 * hash) + getQty();
    hash = (37 * hash) + DISCOUNT_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getDiscountAmount()));
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getType().hashCode();
    if (getAccessoriesCount() > 0) {
      hash = (37 * hash) + ACCESSORIES_FIELD_NUMBER;
      hash = (53 * hash) + getAccessoriesList().hashCode();
    }
    if (getComboItemsCount() > 0) {
      hash = (37 * hash) + COMBO_ITEMS_FIELD_NUMBER;
      hash = (53 * hash) + getComboItemsList().hashCode();
    }
    hash = (37 * hash) + OPERATION_RECORDS_FIELD_NUMBER;
    hash = (53 * hash) + getOperationRecords().hashCode();
    if (getSkuRemarkCount() > 0) {
      hash = (37 * hash) + SKUREMARK_FIELD_NUMBER;
      hash = (53 * hash) + getSkuRemarkList().hashCode();
    }
    hash = (37 * hash) + REMARK_FIELD_NUMBER;
    hash = (53 * hash) + getRemark().hashCode();
    hash = (37 * hash) + TAXAMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getTaxAmount()));
    hash = (37 * hash) + NET_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getNetAmount()));
    hash = (37 * hash) + AVG_MAKE_SPAN_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getAvgMakeSpan());
    hash = (37 * hash) + WEIGHT_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getWeight());
    hash = (37 * hash) + HAS_MAKE_SPAN_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getHasMakeSpan());
    hash = (37 * hash) + SUM_DISCOUNT_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getSumDiscountAmount()));
    hash = (37 * hash) + SUM_NET_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getSumNetAmount()));
    hash = (37 * hash) + SUM_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getSumAmount()));
    hash = (37 * hash) + HAS_WEIGHT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getHasWeight());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *订单商品
   * </pre>
   *
   * Protobuf type {@code coupon.TicketProduct}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:coupon.TicketProduct)
      cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_TicketProduct_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_TicketProduct_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.class, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getAccessoriesFieldBuilder();
        getComboItemsFieldBuilder();
        getSkuRemarkFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      id_ = "";

      name_ = "";

      code_ = "";

      seqId_ = 0L;

      price_ = 0D;

      amount_ = 0D;

      qty_ = 0;

      discountAmount_ = 0D;

      type_ = "";

      if (accessoriesBuilder_ == null) {
        accessories_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        accessoriesBuilder_.clear();
      }
      if (comboItemsBuilder_ == null) {
        comboItems_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
      } else {
        comboItemsBuilder_.clear();
      }
      operationRecords_ = "";

      if (skuRemarkBuilder_ == null) {
        skuRemark_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
      } else {
        skuRemarkBuilder_.clear();
      }
      remark_ = "";

      taxAmount_ = 0D;

      netAmount_ = 0D;

      avgMakeSpan_ = 0F;

      weight_ = 0F;

      hasMakeSpan_ = false;

      sumDiscountAmount_ = 0D;

      sumNetAmount_ = 0D;

      sumAmount_ = 0D;

      hasWeight_ = false;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketOuterClass.internal_static_coupon_TicketProduct_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct build() {
      cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct buildPartial() {
      cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct result = new cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct(this);
      int from_bitField0_ = bitField0_;
      result.id_ = id_;
      result.name_ = name_;
      result.code_ = code_;
      result.seqId_ = seqId_;
      result.price_ = price_;
      result.amount_ = amount_;
      result.qty_ = qty_;
      result.discountAmount_ = discountAmount_;
      result.type_ = type_;
      if (accessoriesBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          accessories_ = java.util.Collections.unmodifiableList(accessories_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.accessories_ = accessories_;
      } else {
        result.accessories_ = accessoriesBuilder_.build();
      }
      if (comboItemsBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          comboItems_ = java.util.Collections.unmodifiableList(comboItems_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.comboItems_ = comboItems_;
      } else {
        result.comboItems_ = comboItemsBuilder_.build();
      }
      result.operationRecords_ = operationRecords_;
      if (skuRemarkBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          skuRemark_ = java.util.Collections.unmodifiableList(skuRemark_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.skuRemark_ = skuRemark_;
      } else {
        result.skuRemark_ = skuRemarkBuilder_.build();
      }
      result.remark_ = remark_;
      result.taxAmount_ = taxAmount_;
      result.netAmount_ = netAmount_;
      result.avgMakeSpan_ = avgMakeSpan_;
      result.weight_ = weight_;
      result.hasMakeSpan_ = hasMakeSpan_;
      result.sumDiscountAmount_ = sumDiscountAmount_;
      result.sumNetAmount_ = sumNetAmount_;
      result.sumAmount_ = sumAmount_;
      result.hasWeight_ = hasWeight_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct) {
        return mergeFrom((cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct other) {
      if (other == cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (!other.getCode().isEmpty()) {
        code_ = other.code_;
        onChanged();
      }
      if (other.getSeqId() != 0L) {
        setSeqId(other.getSeqId());
      }
      if (other.getPrice() != 0D) {
        setPrice(other.getPrice());
      }
      if (other.getAmount() != 0D) {
        setAmount(other.getAmount());
      }
      if (other.getQty() != 0) {
        setQty(other.getQty());
      }
      if (other.getDiscountAmount() != 0D) {
        setDiscountAmount(other.getDiscountAmount());
      }
      if (!other.getType().isEmpty()) {
        type_ = other.type_;
        onChanged();
      }
      if (accessoriesBuilder_ == null) {
        if (!other.accessories_.isEmpty()) {
          if (accessories_.isEmpty()) {
            accessories_ = other.accessories_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureAccessoriesIsMutable();
            accessories_.addAll(other.accessories_);
          }
          onChanged();
        }
      } else {
        if (!other.accessories_.isEmpty()) {
          if (accessoriesBuilder_.isEmpty()) {
            accessoriesBuilder_.dispose();
            accessoriesBuilder_ = null;
            accessories_ = other.accessories_;
            bitField0_ = (bitField0_ & ~0x00000001);
            accessoriesBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getAccessoriesFieldBuilder() : null;
          } else {
            accessoriesBuilder_.addAllMessages(other.accessories_);
          }
        }
      }
      if (comboItemsBuilder_ == null) {
        if (!other.comboItems_.isEmpty()) {
          if (comboItems_.isEmpty()) {
            comboItems_ = other.comboItems_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureComboItemsIsMutable();
            comboItems_.addAll(other.comboItems_);
          }
          onChanged();
        }
      } else {
        if (!other.comboItems_.isEmpty()) {
          if (comboItemsBuilder_.isEmpty()) {
            comboItemsBuilder_.dispose();
            comboItemsBuilder_ = null;
            comboItems_ = other.comboItems_;
            bitField0_ = (bitField0_ & ~0x00000002);
            comboItemsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getComboItemsFieldBuilder() : null;
          } else {
            comboItemsBuilder_.addAllMessages(other.comboItems_);
          }
        }
      }
      if (!other.getOperationRecords().isEmpty()) {
        operationRecords_ = other.operationRecords_;
        onChanged();
      }
      if (skuRemarkBuilder_ == null) {
        if (!other.skuRemark_.isEmpty()) {
          if (skuRemark_.isEmpty()) {
            skuRemark_ = other.skuRemark_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureSkuRemarkIsMutable();
            skuRemark_.addAll(other.skuRemark_);
          }
          onChanged();
        }
      } else {
        if (!other.skuRemark_.isEmpty()) {
          if (skuRemarkBuilder_.isEmpty()) {
            skuRemarkBuilder_.dispose();
            skuRemarkBuilder_ = null;
            skuRemark_ = other.skuRemark_;
            bitField0_ = (bitField0_ & ~0x00000004);
            skuRemarkBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getSkuRemarkFieldBuilder() : null;
          } else {
            skuRemarkBuilder_.addAllMessages(other.skuRemark_);
          }
        }
      }
      if (!other.getRemark().isEmpty()) {
        remark_ = other.remark_;
        onChanged();
      }
      if (other.getTaxAmount() != 0D) {
        setTaxAmount(other.getTaxAmount());
      }
      if (other.getNetAmount() != 0D) {
        setNetAmount(other.getNetAmount());
      }
      if (other.getAvgMakeSpan() != 0F) {
        setAvgMakeSpan(other.getAvgMakeSpan());
      }
      if (other.getWeight() != 0F) {
        setWeight(other.getWeight());
      }
      if (other.getHasMakeSpan() != false) {
        setHasMakeSpan(other.getHasMakeSpan());
      }
      if (other.getSumDiscountAmount() != 0D) {
        setSumDiscountAmount(other.getSumDiscountAmount());
      }
      if (other.getSumNetAmount() != 0D) {
        setSumNetAmount(other.getSumNetAmount());
      }
      if (other.getSumAmount() != 0D) {
        setSumAmount(other.getSumAmount());
      }
      if (other.getHasWeight() != false) {
        setHasWeight(other.getHasWeight());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object id_ = "";
    /**
     * <pre>
     *商品id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *商品id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *商品id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      
      id_ = getDefaultInstance().getId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品id
     * </pre>
     *
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      id_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <pre>
     *商品名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *商品名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *商品名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品名称
     * </pre>
     *
     * <code>string name = 2;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object code_ = "";
    /**
     * <pre>
     *商品编码
     * </pre>
     *
     * <code>string code = 3;</code>
     * @return The code.
     */
    public java.lang.String getCode() {
      java.lang.Object ref = code_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        code_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *商品编码
     * </pre>
     *
     * <code>string code = 3;</code>
     * @return The bytes for code.
     */
    public com.google.protobuf.ByteString
        getCodeBytes() {
      java.lang.Object ref = code_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        code_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *商品编码
     * </pre>
     *
     * <code>string code = 3;</code>
     * @param value The code to set.
     * @return This builder for chaining.
     */
    public Builder setCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      code_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品编码
     * </pre>
     *
     * <code>string code = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearCode() {
      
      code_ = getDefaultInstance().getCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品编码
     * </pre>
     *
     * <code>string code = 3;</code>
     * @param value The bytes for code to set.
     * @return This builder for chaining.
     */
    public Builder setCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      code_ = value;
      onChanged();
      return this;
    }

    private long seqId_ ;
    /**
     * <pre>
     *商品的顺序号，代表下单的顺序
     * </pre>
     *
     * <code>int64 seq_id = 4;</code>
     * @return The seqId.
     */
    @java.lang.Override
    public long getSeqId() {
      return seqId_;
    }
    /**
     * <pre>
     *商品的顺序号，代表下单的顺序
     * </pre>
     *
     * <code>int64 seq_id = 4;</code>
     * @param value The seqId to set.
     * @return This builder for chaining.
     */
    public Builder setSeqId(long value) {
      
      seqId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品的顺序号，代表下单的顺序
     * </pre>
     *
     * <code>int64 seq_id = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearSeqId() {
      
      seqId_ = 0L;
      onChanged();
      return this;
    }

    private double price_ ;
    /**
     * <pre>
     *商品单价
     * </pre>
     *
     * <code>double price = 5;</code>
     * @return The price.
     */
    @java.lang.Override
    public double getPrice() {
      return price_;
    }
    /**
     * <pre>
     *商品单价
     * </pre>
     *
     * <code>double price = 5;</code>
     * @param value The price to set.
     * @return This builder for chaining.
     */
    public Builder setPrice(double value) {
      
      price_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品单价
     * </pre>
     *
     * <code>double price = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrice() {
      
      price_ = 0D;
      onChanged();
      return this;
    }

    private double amount_ ;
    /**
     * <pre>
     *商品总价
     * </pre>
     *
     * <code>double amount = 6;</code>
     * @return The amount.
     */
    @java.lang.Override
    public double getAmount() {
      return amount_;
    }
    /**
     * <pre>
     *商品总价
     * </pre>
     *
     * <code>double amount = 6;</code>
     * @param value The amount to set.
     * @return This builder for chaining.
     */
    public Builder setAmount(double value) {
      
      amount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品总价
     * </pre>
     *
     * <code>double amount = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmount() {
      
      amount_ = 0D;
      onChanged();
      return this;
    }

    private int qty_ ;
    /**
     * <pre>
     *商品数量
     * </pre>
     *
     * <code>int32 qty = 7;</code>
     * @return The qty.
     */
    @java.lang.Override
    public int getQty() {
      return qty_;
    }
    /**
     * <pre>
     *商品数量
     * </pre>
     *
     * <code>int32 qty = 7;</code>
     * @param value The qty to set.
     * @return This builder for chaining.
     */
    public Builder setQty(int value) {
      
      qty_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品数量
     * </pre>
     *
     * <code>int32 qty = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearQty() {
      
      qty_ = 0;
      onChanged();
      return this;
    }

    private double discountAmount_ ;
    /**
     * <pre>
     *商品的折扣金额
     * </pre>
     *
     * <code>double discount_amount = 8;</code>
     * @return The discountAmount.
     */
    @java.lang.Override
    public double getDiscountAmount() {
      return discountAmount_;
    }
    /**
     * <pre>
     *商品的折扣金额
     * </pre>
     *
     * <code>double discount_amount = 8;</code>
     * @param value The discountAmount to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountAmount(double value) {
      
      discountAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品的折扣金额
     * </pre>
     *
     * <code>double discount_amount = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountAmount() {
      
      discountAmount_ = 0D;
      onChanged();
      return this;
    }

    private java.lang.Object type_ = "";
    /**
     * <pre>
     *商品类型
     * </pre>
     *
     * <code>string type = 9;</code>
     * @return The type.
     */
    public java.lang.String getType() {
      java.lang.Object ref = type_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        type_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *商品类型
     * </pre>
     *
     * <code>string type = 9;</code>
     * @return The bytes for type.
     */
    public com.google.protobuf.ByteString
        getTypeBytes() {
      java.lang.Object ref = type_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        type_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *商品类型
     * </pre>
     *
     * <code>string type = 9;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      type_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品类型
     * </pre>
     *
     * <code>string type = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      
      type_ = getDefaultInstance().getType();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品类型
     * </pre>
     *
     * <code>string type = 9;</code>
     * @param value The bytes for type to set.
     * @return This builder for chaining.
     */
    public Builder setTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      type_ = value;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> accessories_ =
      java.util.Collections.emptyList();
    private void ensureAccessoriesIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        accessories_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct>(accessories_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder> accessoriesBuilder_;

    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> getAccessoriesList() {
      if (accessoriesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(accessories_);
      } else {
        return accessoriesBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public int getAccessoriesCount() {
      if (accessoriesBuilder_ == null) {
        return accessories_.size();
      } else {
        return accessoriesBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct getAccessories(int index) {
      if (accessoriesBuilder_ == null) {
        return accessories_.get(index);
      } else {
        return accessoriesBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public Builder setAccessories(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct value) {
      if (accessoriesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAccessoriesIsMutable();
        accessories_.set(index, value);
        onChanged();
      } else {
        accessoriesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public Builder setAccessories(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder builderForValue) {
      if (accessoriesBuilder_ == null) {
        ensureAccessoriesIsMutable();
        accessories_.set(index, builderForValue.build());
        onChanged();
      } else {
        accessoriesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public Builder addAccessories(cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct value) {
      if (accessoriesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAccessoriesIsMutable();
        accessories_.add(value);
        onChanged();
      } else {
        accessoriesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public Builder addAccessories(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct value) {
      if (accessoriesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAccessoriesIsMutable();
        accessories_.add(index, value);
        onChanged();
      } else {
        accessoriesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public Builder addAccessories(
        cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder builderForValue) {
      if (accessoriesBuilder_ == null) {
        ensureAccessoriesIsMutable();
        accessories_.add(builderForValue.build());
        onChanged();
      } else {
        accessoriesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public Builder addAccessories(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder builderForValue) {
      if (accessoriesBuilder_ == null) {
        ensureAccessoriesIsMutable();
        accessories_.add(index, builderForValue.build());
        onChanged();
      } else {
        accessoriesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public Builder addAllAccessories(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> values) {
      if (accessoriesBuilder_ == null) {
        ensureAccessoriesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, accessories_);
        onChanged();
      } else {
        accessoriesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public Builder clearAccessories() {
      if (accessoriesBuilder_ == null) {
        accessories_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        accessoriesBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public Builder removeAccessories(int index) {
      if (accessoriesBuilder_ == null) {
        ensureAccessoriesIsMutable();
        accessories_.remove(index);
        onChanged();
      } else {
        accessoriesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder getAccessoriesBuilder(
        int index) {
      return getAccessoriesFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder getAccessoriesOrBuilder(
        int index) {
      if (accessoriesBuilder_ == null) {
        return accessories_.get(index);  } else {
        return accessoriesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder> 
         getAccessoriesOrBuilderList() {
      if (accessoriesBuilder_ != null) {
        return accessoriesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(accessories_);
      }
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder addAccessoriesBuilder() {
      return getAccessoriesFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.getDefaultInstance());
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder addAccessoriesBuilder(
        int index) {
      return getAccessoriesFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.getDefaultInstance());
    }
    /**
     * <pre>
     *加料
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct accessories = 10;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder> 
         getAccessoriesBuilderList() {
      return getAccessoriesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder> 
        getAccessoriesFieldBuilder() {
      if (accessoriesBuilder_ == null) {
        accessoriesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder>(
                accessories_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        accessories_ = null;
      }
      return accessoriesBuilder_;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> comboItems_ =
      java.util.Collections.emptyList();
    private void ensureComboItemsIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        comboItems_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct>(comboItems_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder> comboItemsBuilder_;

    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> getComboItemsList() {
      if (comboItemsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(comboItems_);
      } else {
        return comboItemsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public int getComboItemsCount() {
      if (comboItemsBuilder_ == null) {
        return comboItems_.size();
      } else {
        return comboItemsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct getComboItems(int index) {
      if (comboItemsBuilder_ == null) {
        return comboItems_.get(index);
      } else {
        return comboItemsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public Builder setComboItems(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct value) {
      if (comboItemsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureComboItemsIsMutable();
        comboItems_.set(index, value);
        onChanged();
      } else {
        comboItemsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public Builder setComboItems(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder builderForValue) {
      if (comboItemsBuilder_ == null) {
        ensureComboItemsIsMutable();
        comboItems_.set(index, builderForValue.build());
        onChanged();
      } else {
        comboItemsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public Builder addComboItems(cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct value) {
      if (comboItemsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureComboItemsIsMutable();
        comboItems_.add(value);
        onChanged();
      } else {
        comboItemsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public Builder addComboItems(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct value) {
      if (comboItemsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureComboItemsIsMutable();
        comboItems_.add(index, value);
        onChanged();
      } else {
        comboItemsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public Builder addComboItems(
        cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder builderForValue) {
      if (comboItemsBuilder_ == null) {
        ensureComboItemsIsMutable();
        comboItems_.add(builderForValue.build());
        onChanged();
      } else {
        comboItemsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public Builder addComboItems(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder builderForValue) {
      if (comboItemsBuilder_ == null) {
        ensureComboItemsIsMutable();
        comboItems_.add(index, builderForValue.build());
        onChanged();
      } else {
        comboItemsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public Builder addAllComboItems(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> values) {
      if (comboItemsBuilder_ == null) {
        ensureComboItemsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, comboItems_);
        onChanged();
      } else {
        comboItemsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public Builder clearComboItems() {
      if (comboItemsBuilder_ == null) {
        comboItems_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        comboItemsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public Builder removeComboItems(int index) {
      if (comboItemsBuilder_ == null) {
        ensureComboItemsIsMutable();
        comboItems_.remove(index);
        onChanged();
      } else {
        comboItemsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder getComboItemsBuilder(
        int index) {
      return getComboItemsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder getComboItemsOrBuilder(
        int index) {
      if (comboItemsBuilder_ == null) {
        return comboItems_.get(index);  } else {
        return comboItemsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder> 
         getComboItemsOrBuilderList() {
      if (comboItemsBuilder_ != null) {
        return comboItemsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(comboItems_);
      }
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder addComboItemsBuilder() {
      return getComboItemsFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.getDefaultInstance());
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder addComboItemsBuilder(
        int index) {
      return getComboItemsFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.getDefaultInstance());
    }
    /**
     * <pre>
     *套餐子项
     * </pre>
     *
     * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder> 
         getComboItemsBuilderList() {
      return getComboItemsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder> 
        getComboItemsFieldBuilder() {
      if (comboItemsBuilder_ == null) {
        comboItemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct, cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct.Builder, cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder>(
                comboItems_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        comboItems_ = null;
      }
      return comboItemsBuilder_;
    }

    private java.lang.Object operationRecords_ = "";
    /**
     * <pre>
     *操作记录
     * </pre>
     *
     * <code>string operation_records = 12;</code>
     * @return The operationRecords.
     */
    public java.lang.String getOperationRecords() {
      java.lang.Object ref = operationRecords_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        operationRecords_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *操作记录
     * </pre>
     *
     * <code>string operation_records = 12;</code>
     * @return The bytes for operationRecords.
     */
    public com.google.protobuf.ByteString
        getOperationRecordsBytes() {
      java.lang.Object ref = operationRecords_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        operationRecords_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *操作记录
     * </pre>
     *
     * <code>string operation_records = 12;</code>
     * @param value The operationRecords to set.
     * @return This builder for chaining.
     */
    public Builder setOperationRecords(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      operationRecords_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *操作记录
     * </pre>
     *
     * <code>string operation_records = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearOperationRecords() {
      
      operationRecords_ = getDefaultInstance().getOperationRecords();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *操作记录
     * </pre>
     *
     * <code>string operation_records = 12;</code>
     * @param value The bytes for operationRecords to set.
     * @return This builder for chaining.
     */
    public Builder setOperationRecordsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      operationRecords_ = value;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark> skuRemark_ =
      java.util.Collections.emptyList();
    private void ensureSkuRemarkIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        skuRemark_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark>(skuRemark_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark, cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.Builder, cn.hexcloud.pbis.common.service.facade.ticket.SkuRemarkOrBuilder> skuRemarkBuilder_;

    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark> getSkuRemarkList() {
      if (skuRemarkBuilder_ == null) {
        return java.util.Collections.unmodifiableList(skuRemark_);
      } else {
        return skuRemarkBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public int getSkuRemarkCount() {
      if (skuRemarkBuilder_ == null) {
        return skuRemark_.size();
      } else {
        return skuRemarkBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark getSkuRemark(int index) {
      if (skuRemarkBuilder_ == null) {
        return skuRemark_.get(index);
      } else {
        return skuRemarkBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public Builder setSkuRemark(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark value) {
      if (skuRemarkBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSkuRemarkIsMutable();
        skuRemark_.set(index, value);
        onChanged();
      } else {
        skuRemarkBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public Builder setSkuRemark(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.Builder builderForValue) {
      if (skuRemarkBuilder_ == null) {
        ensureSkuRemarkIsMutable();
        skuRemark_.set(index, builderForValue.build());
        onChanged();
      } else {
        skuRemarkBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public Builder addSkuRemark(cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark value) {
      if (skuRemarkBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSkuRemarkIsMutable();
        skuRemark_.add(value);
        onChanged();
      } else {
        skuRemarkBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public Builder addSkuRemark(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark value) {
      if (skuRemarkBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureSkuRemarkIsMutable();
        skuRemark_.add(index, value);
        onChanged();
      } else {
        skuRemarkBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public Builder addSkuRemark(
        cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.Builder builderForValue) {
      if (skuRemarkBuilder_ == null) {
        ensureSkuRemarkIsMutable();
        skuRemark_.add(builderForValue.build());
        onChanged();
      } else {
        skuRemarkBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public Builder addSkuRemark(
        int index, cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.Builder builderForValue) {
      if (skuRemarkBuilder_ == null) {
        ensureSkuRemarkIsMutable();
        skuRemark_.add(index, builderForValue.build());
        onChanged();
      } else {
        skuRemarkBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public Builder addAllSkuRemark(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark> values) {
      if (skuRemarkBuilder_ == null) {
        ensureSkuRemarkIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, skuRemark_);
        onChanged();
      } else {
        skuRemarkBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public Builder clearSkuRemark() {
      if (skuRemarkBuilder_ == null) {
        skuRemark_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        skuRemarkBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public Builder removeSkuRemark(int index) {
      if (skuRemarkBuilder_ == null) {
        ensureSkuRemarkIsMutable();
        skuRemark_.remove(index);
        onChanged();
      } else {
        skuRemarkBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.Builder getSkuRemarkBuilder(
        int index) {
      return getSkuRemarkFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.SkuRemarkOrBuilder getSkuRemarkOrBuilder(
        int index) {
      if (skuRemarkBuilder_ == null) {
        return skuRemark_.get(index);  } else {
        return skuRemarkBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.SkuRemarkOrBuilder> 
         getSkuRemarkOrBuilderList() {
      if (skuRemarkBuilder_ != null) {
        return skuRemarkBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(skuRemark_);
      }
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.Builder addSkuRemarkBuilder() {
      return getSkuRemarkFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.getDefaultInstance());
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.Builder addSkuRemarkBuilder(
        int index) {
      return getSkuRemarkFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.getDefaultInstance());
    }
    /**
     * <pre>
     *sku属性
     * </pre>
     *
     * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.Builder> 
         getSkuRemarkBuilderList() {
      return getSkuRemarkFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark, cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.Builder, cn.hexcloud.pbis.common.service.facade.ticket.SkuRemarkOrBuilder> 
        getSkuRemarkFieldBuilder() {
      if (skuRemarkBuilder_ == null) {
        skuRemarkBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark, cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark.Builder, cn.hexcloud.pbis.common.service.facade.ticket.SkuRemarkOrBuilder>(
                skuRemark_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        skuRemark_ = null;
      }
      return skuRemarkBuilder_;
    }

    private java.lang.Object remark_ = "";
    /**
     * <pre>
     *商品备注
     * </pre>
     *
     * <code>string remark = 14;</code>
     * @return The remark.
     */
    public java.lang.String getRemark() {
      java.lang.Object ref = remark_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        remark_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *商品备注
     * </pre>
     *
     * <code>string remark = 14;</code>
     * @return The bytes for remark.
     */
    public com.google.protobuf.ByteString
        getRemarkBytes() {
      java.lang.Object ref = remark_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        remark_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *商品备注
     * </pre>
     *
     * <code>string remark = 14;</code>
     * @param value The remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemark(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      remark_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品备注
     * </pre>
     *
     * <code>string remark = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearRemark() {
      
      remark_ = getDefaultInstance().getRemark();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品备注
     * </pre>
     *
     * <code>string remark = 14;</code>
     * @param value The bytes for remark to set.
     * @return This builder for chaining.
     */
    public Builder setRemarkBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      remark_ = value;
      onChanged();
      return this;
    }

    private double taxAmount_ ;
    /**
     * <pre>
     *税额
     * </pre>
     *
     * <code>double taxAmount = 15;</code>
     * @return The taxAmount.
     */
    @java.lang.Override
    public double getTaxAmount() {
      return taxAmount_;
    }
    /**
     * <pre>
     *税额
     * </pre>
     *
     * <code>double taxAmount = 15;</code>
     * @param value The taxAmount to set.
     * @return This builder for chaining.
     */
    public Builder setTaxAmount(double value) {
      
      taxAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *税额
     * </pre>
     *
     * <code>double taxAmount = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearTaxAmount() {
      
      taxAmount_ = 0D;
      onChanged();
      return this;
    }

    private double netAmount_ ;
    /**
     * <pre>
     *净额
     * </pre>
     *
     * <code>double net_amount = 16;</code>
     * @return The netAmount.
     */
    @java.lang.Override
    public double getNetAmount() {
      return netAmount_;
    }
    /**
     * <pre>
     *净额
     * </pre>
     *
     * <code>double net_amount = 16;</code>
     * @param value The netAmount to set.
     * @return This builder for chaining.
     */
    public Builder setNetAmount(double value) {
      
      netAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *净额
     * </pre>
     *
     * <code>double net_amount = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearNetAmount() {
      
      netAmount_ = 0D;
      onChanged();
      return this;
    }

    private float avgMakeSpan_ ;
    /**
     * <pre>
     *商品组平均制作时长
     * </pre>
     *
     * <code>float avg_make_span = 17;</code>
     * @return The avgMakeSpan.
     */
    @java.lang.Override
    public float getAvgMakeSpan() {
      return avgMakeSpan_;
    }
    /**
     * <pre>
     *商品组平均制作时长
     * </pre>
     *
     * <code>float avg_make_span = 17;</code>
     * @param value The avgMakeSpan to set.
     * @return This builder for chaining.
     */
    public Builder setAvgMakeSpan(float value) {
      
      avgMakeSpan_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品组平均制作时长
     * </pre>
     *
     * <code>float avg_make_span = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearAvgMakeSpan() {
      
      avgMakeSpan_ = 0F;
      onChanged();
      return this;
    }

    private float weight_ ;
    /**
     * <pre>
     *重量(kg)
     * </pre>
     *
     * <code>float weight = 18;</code>
     * @return The weight.
     */
    @java.lang.Override
    public float getWeight() {
      return weight_;
    }
    /**
     * <pre>
     *重量(kg)
     * </pre>
     *
     * <code>float weight = 18;</code>
     * @param value The weight to set.
     * @return This builder for chaining.
     */
    public Builder setWeight(float value) {
      
      weight_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *重量(kg)
     * </pre>
     *
     * <code>float weight = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearWeight() {
      
      weight_ = 0F;
      onChanged();
      return this;
    }

    private boolean hasMakeSpan_ ;
    /**
     * <pre>
     *是否有制作时长信息
     * </pre>
     *
     * <code>bool has_make_span = 19;</code>
     * @return The hasMakeSpan.
     */
    @java.lang.Override
    public boolean getHasMakeSpan() {
      return hasMakeSpan_;
    }
    /**
     * <pre>
     *是否有制作时长信息
     * </pre>
     *
     * <code>bool has_make_span = 19;</code>
     * @param value The hasMakeSpan to set.
     * @return This builder for chaining.
     */
    public Builder setHasMakeSpan(boolean value) {
      
      hasMakeSpan_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *是否有制作时长信息
     * </pre>
     *
     * <code>bool has_make_span = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearHasMakeSpan() {
      
      hasMakeSpan_ = false;
      onChanged();
      return this;
    }

    private double sumDiscountAmount_ ;
    /**
     * <pre>
     *商品折扣
     * </pre>
     *
     * <code>double sum_discount_amount = 20;</code>
     * @return The sumDiscountAmount.
     */
    @java.lang.Override
    public double getSumDiscountAmount() {
      return sumDiscountAmount_;
    }
    /**
     * <pre>
     *商品折扣
     * </pre>
     *
     * <code>double sum_discount_amount = 20;</code>
     * @param value The sumDiscountAmount to set.
     * @return This builder for chaining.
     */
    public Builder setSumDiscountAmount(double value) {
      
      sumDiscountAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品折扣
     * </pre>
     *
     * <code>double sum_discount_amount = 20;</code>
     * @return This builder for chaining.
     */
    public Builder clearSumDiscountAmount() {
      
      sumDiscountAmount_ = 0D;
      onChanged();
      return this;
    }

    private double sumNetAmount_ ;
    /**
     * <pre>
     *商品实收
     * </pre>
     *
     * <code>double sum_net_amount = 21;</code>
     * @return The sumNetAmount.
     */
    @java.lang.Override
    public double getSumNetAmount() {
      return sumNetAmount_;
    }
    /**
     * <pre>
     *商品实收
     * </pre>
     *
     * <code>double sum_net_amount = 21;</code>
     * @param value The sumNetAmount to set.
     * @return This builder for chaining.
     */
    public Builder setSumNetAmount(double value) {
      
      sumNetAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品实收
     * </pre>
     *
     * <code>double sum_net_amount = 21;</code>
     * @return This builder for chaining.
     */
    public Builder clearSumNetAmount() {
      
      sumNetAmount_ = 0D;
      onChanged();
      return this;
    }

    private double sumAmount_ ;
    /**
     * <pre>
     *商品原价
     * </pre>
     *
     * <code>double sum_amount = 22;</code>
     * @return The sumAmount.
     */
    @java.lang.Override
    public double getSumAmount() {
      return sumAmount_;
    }
    /**
     * <pre>
     *商品原价
     * </pre>
     *
     * <code>double sum_amount = 22;</code>
     * @param value The sumAmount to set.
     * @return This builder for chaining.
     */
    public Builder setSumAmount(double value) {
      
      sumAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商品原价
     * </pre>
     *
     * <code>double sum_amount = 22;</code>
     * @return This builder for chaining.
     */
    public Builder clearSumAmount() {
      
      sumAmount_ = 0D;
      onChanged();
      return this;
    }

    private boolean hasWeight_ ;
    /**
     * <pre>
     *是否称重商品
     * </pre>
     *
     * <code>bool has_weight = 23;</code>
     * @return The hasWeight.
     */
    @java.lang.Override
    public boolean getHasWeight() {
      return hasWeight_;
    }
    /**
     * <pre>
     *是否称重商品
     * </pre>
     *
     * <code>bool has_weight = 23;</code>
     * @param value The hasWeight to set.
     * @return This builder for chaining.
     */
    public Builder setHasWeight(boolean value) {
      
      hasWeight_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *是否称重商品
     * </pre>
     *
     * <code>bool has_weight = 23;</code>
     * @return This builder for chaining.
     */
    public Builder clearHasWeight() {
      
      hasWeight_ = false;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:coupon.TicketProduct)
  }

  // @@protoc_insertion_point(class_scope:coupon.TicketProduct)
  private static final cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct();
  }

  public static cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TicketProduct>
      PARSER = new com.google.protobuf.AbstractParser<TicketProduct>() {
    @java.lang.Override
    public TicketProduct parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new TicketProduct(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<TicketProduct> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TicketProduct> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

