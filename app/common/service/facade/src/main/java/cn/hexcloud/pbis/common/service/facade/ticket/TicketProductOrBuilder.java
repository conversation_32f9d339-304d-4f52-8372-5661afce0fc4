// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.facade.ticket;

public interface TicketProductOrBuilder extends
    // @@protoc_insertion_point(interface_extends:coupon.TicketProduct)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *商品id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   *商品id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   *商品名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   *商品名称
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   *商品编码
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <pre>
   *商品编码
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <pre>
   *商品的顺序号，代表下单的顺序
   * </pre>
   *
   * <code>int64 seq_id = 4;</code>
   * @return The seqId.
   */
  long getSeqId();

  /**
   * <pre>
   *商品单价
   * </pre>
   *
   * <code>double price = 5;</code>
   * @return The price.
   */
  double getPrice();

  /**
   * <pre>
   *商品总价
   * </pre>
   *
   * <code>double amount = 6;</code>
   * @return The amount.
   */
  double getAmount();

  /**
   * <pre>
   *商品数量
   * </pre>
   *
   * <code>int32 qty = 7;</code>
   * @return The qty.
   */
  int getQty();

  /**
   * <pre>
   *商品的折扣金额
   * </pre>
   *
   * <code>double discount_amount = 8;</code>
   * @return The discountAmount.
   */
  double getDiscountAmount();

  /**
   * <pre>
   *商品类型
   * </pre>
   *
   * <code>string type = 9;</code>
   * @return The type.
   */
  java.lang.String getType();
  /**
   * <pre>
   *商品类型
   * </pre>
   *
   * <code>string type = 9;</code>
   * @return The bytes for type.
   */
  com.google.protobuf.ByteString
      getTypeBytes();

  /**
   * <pre>
   *加料
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct accessories = 10;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> 
      getAccessoriesList();
  /**
   * <pre>
   *加料
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct accessories = 10;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct getAccessories(int index);
  /**
   * <pre>
   *加料
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct accessories = 10;</code>
   */
  int getAccessoriesCount();
  /**
   * <pre>
   *加料
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct accessories = 10;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder> 
      getAccessoriesOrBuilderList();
  /**
   * <pre>
   *加料
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct accessories = 10;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder getAccessoriesOrBuilder(
      int index);

  /**
   * <pre>
   *套餐子项
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct> 
      getComboItemsList();
  /**
   * <pre>
   *套餐子项
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.TicketProduct getComboItems(int index);
  /**
   * <pre>
   *套餐子项
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
   */
  int getComboItemsCount();
  /**
   * <pre>
   *套餐子项
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder> 
      getComboItemsOrBuilderList();
  /**
   * <pre>
   *套餐子项
   * </pre>
   *
   * <code>repeated .coupon.TicketProduct combo_items = 11;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.TicketProductOrBuilder getComboItemsOrBuilder(
      int index);

  /**
   * <pre>
   *操作记录
   * </pre>
   *
   * <code>string operation_records = 12;</code>
   * @return The operationRecords.
   */
  java.lang.String getOperationRecords();
  /**
   * <pre>
   *操作记录
   * </pre>
   *
   * <code>string operation_records = 12;</code>
   * @return The bytes for operationRecords.
   */
  com.google.protobuf.ByteString
      getOperationRecordsBytes();

  /**
   * <pre>
   *sku属性
   * </pre>
   *
   * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark> 
      getSkuRemarkList();
  /**
   * <pre>
   *sku属性
   * </pre>
   *
   * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.SkuRemark getSkuRemark(int index);
  /**
   * <pre>
   *sku属性
   * </pre>
   *
   * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
   */
  int getSkuRemarkCount();
  /**
   * <pre>
   *sku属性
   * </pre>
   *
   * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.facade.ticket.SkuRemarkOrBuilder> 
      getSkuRemarkOrBuilderList();
  /**
   * <pre>
   *sku属性
   * </pre>
   *
   * <code>repeated .coupon.SkuRemark skuRemark = 13;</code>
   */
  cn.hexcloud.pbis.common.service.facade.ticket.SkuRemarkOrBuilder getSkuRemarkOrBuilder(
      int index);

  /**
   * <pre>
   *商品备注
   * </pre>
   *
   * <code>string remark = 14;</code>
   * @return The remark.
   */
  java.lang.String getRemark();
  /**
   * <pre>
   *商品备注
   * </pre>
   *
   * <code>string remark = 14;</code>
   * @return The bytes for remark.
   */
  com.google.protobuf.ByteString
      getRemarkBytes();

  /**
   * <pre>
   *税额
   * </pre>
   *
   * <code>double taxAmount = 15;</code>
   * @return The taxAmount.
   */
  double getTaxAmount();

  /**
   * <pre>
   *净额
   * </pre>
   *
   * <code>double net_amount = 16;</code>
   * @return The netAmount.
   */
  double getNetAmount();

  /**
   * <pre>
   *商品组平均制作时长
   * </pre>
   *
   * <code>float avg_make_span = 17;</code>
   * @return The avgMakeSpan.
   */
  float getAvgMakeSpan();

  /**
   * <pre>
   *重量(kg)
   * </pre>
   *
   * <code>float weight = 18;</code>
   * @return The weight.
   */
  float getWeight();

  /**
   * <pre>
   *是否有制作时长信息
   * </pre>
   *
   * <code>bool has_make_span = 19;</code>
   * @return The hasMakeSpan.
   */
  boolean getHasMakeSpan();

  /**
   * <pre>
   *商品折扣
   * </pre>
   *
   * <code>double sum_discount_amount = 20;</code>
   * @return The sumDiscountAmount.
   */
  double getSumDiscountAmount();

  /**
   * <pre>
   *商品实收
   * </pre>
   *
   * <code>double sum_net_amount = 21;</code>
   * @return The sumNetAmount.
   */
  double getSumNetAmount();

  /**
   * <pre>
   *商品原价
   * </pre>
   *
   * <code>double sum_amount = 22;</code>
   * @return The sumAmount.
   */
  double getSumAmount();

  /**
   * <pre>
   *是否称重商品
   * </pre>
   *
   * <code>bool has_weight = 23;</code>
   * @return The hasWeight.
   */
  boolean getHasWeight();
}
