syntax = "proto3";
option java_package = "cn.hexcloud.pbis.common.service.facade.payment";
option java_multiple_files = true;

package pbis;

//import "google/api/annotations.proto";

// 支付集成服务
service PaymentIntegration {

  // 下单接口
  rpc create(CreateRequest) returns (CreateResponse);

  // 支付接口
  rpc pay(PayRequest) returns (PayResponse);

  // 查询接口
  rpc query(QueryRequest) returns (QueryResponse);

  // 撤销接口
  rpc cancel(CancelRequest) returns (CancelResponse);

  // 退款接口
  rpc refund(RefundRequest) returns (RefundResponse);
}

// 下单接口请求信息
message CreateRequest {
  // （必传）支付交易信息
  CreatePaymentSection create_payment_section = 1;
  // （可选）订单信息
  OrderSection order_section = 2;
  // （可选）会员信息
  MemberSection member_section = 3;
}

// 下单接口响应信息
message CreateResponse {
  // （必传）异常编码
  string error_code = 1;
  // （可选）异常信息
  string error_message = 2;
  // （必传）是否有警告信息
  bool warning = 3;
  // （可选）警告信息
  string warning_message = 4;
  // （必传）交易结果信息
  CreateResultSection create_result_section = 5;
}

// 支付接口请求信息
message PayRequest {
  // （必传）支付交易信息
  PaymentSection payment_section = 1;
  // （可选）订单信息
  OrderSection order_section = 2;
  // （可选）会员信息
  MemberSection member_section = 3;
}

// 支付接口响应信息
message PayResponse {
  // （必传）异常编码
  string error_code = 1;
  // （可选）异常信息
  string error_message = 2;
  // （必传）是否有警告信息
  bool warning = 3;
  // （可选）警告信息
  string warning_message = 4;
  // （必传）交易结果信息
  TransactionResultSection transaction_result_section = 5;
}

// 交易查询接口请求信息
message QueryRequest {
  //（必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunite(合阔聚合支付)
  string channel = 1;
  //（必传）支付时传给第三方接口的唯一标识id
  string transaction_id = 2;
  //（必传）付款码、支付卡号
  string pay_code = 3;
  //（可选）第三方流水号
  string tp_transaction_id = 4;
  //（可选）交易时间 "yyyy-mm-ddThh:mm:ss"
  string transaction_time = 5;
  // order_ticket_id
  string order_no = 6;
  // json格式的附加扩展信息
  string extended_params = 7;
}

// 交易查询接口响应信息
message QueryResponse {
  // （必传）异常编码
  string error_code = 1;
  // （可选）异常信息
  string error_message = 2;
  // （必传）是否有警告信息
  bool warning = 3;
  // （可选）警告信息
  string warning_message = 4;
  // （必传）交易结果信息
  TransactionResultSection transaction_result_section = 5;
}

// 撤销接口请求信息
message CancelRequest {
  // （必传）撤销交易信息
  CancelSection cancel_section = 1;
  //（可选）订单信息
  OrderSection order_section = 2;
}

// 撤销接口响应信息
message CancelResponse {
  // （必传）异常编码
  string error_code = 1;
  // （可选）异常信息
  string error_message = 2;
  // （必传）是否有警告信息
  bool warning = 3;
  // （可选）警告信息
  string warning_message = 4;
  // （必传）交易结果信息
  TransactionResultSection transaction_result_section = 5;
}

// 退款接口请求信息
message RefundRequest {
  // （必传）退款交易信息
  RefundSection refund_section = 1;
  //（可选）订单信息
  OrderSection order_section = 2;
  // （可选）会员信息
  MemberSection member_section = 3;
}

// 退款接口响应信息
message RefundResponse {
  // （必传）异常编码
  string error_code = 1;
  // （可选）异常信息
  string error_message = 2;
  // （必传）是否有警告信息
  bool warning = 3;
  // （可选）警告信息
  string warning_message = 4;
  // （必传）交易结果信息
  TransactionResultSection transaction_result_section = 5;
}

// 下单交易信息
message CreatePaymentSection {
  // （必传）渠道编码，ALIPAY(支付宝)、WXPAY(微信支付)、UNIONPAY(银联支付)、HEXUNION(合阔聚合支付)
  string channel = 1;
  // （必传）传给第三方接口的唯一标识id
  string transaction_id = 2;
  // （必传）下单金额（单位：分）
  int32 amount = 3;
  // （可选）买家标识
  string payer = 4;
  // （可选）商品描述，微信小程序支付必传
  string description = 5;
  // （可选）json格式的附加扩展信息
  string extended_params = 6;
  // （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
  string transaction_time = 7;
  // （可选）币种
  string currency = 8;
}

// 支付交易信息
message PaymentSection {
  // （必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunion(合阔聚合支付)
  string channel = 1;
  // （必传）传给第三方接口的唯一标识id
  string transaction_id = 2;
  // （必传）付款码、支付卡号
  string pay_code = 3;
  // （必传）密码、辅助码、二磁道信息等,格式- password=123&cvn_2=213&expiration=2025/10/13（敏感信息）
  string secret_content = 4;
  // （必传）支付金额（单位：分）
  int32 amount = 5;
  // （可选）json格式的附加扩展信息
  string extended_params = 6;
  // （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
  string transaction_time = 7;
  // （可选）货币（如HKD)
  string currency = 8;
}

// 撤销交易信息
message CancelSection {
  // （必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunion(合阔聚合支付)
  string channel = 1;
  // （必传）传给第三方接口的唯一标识id
  string transaction_id = 2;
  // （必传）需要撤销的交易的唯一标识id
  string related_transaction_id = 3;
  // （必传）需要撤销的第三方交易的唯一标识id
  string related_tp_transaction_id = 4;
  // （必传）付款码、支付卡号
  string pay_code = 5;
  // （必传）密码、辅助码、二磁道信息等,格式- password=123&cvn_2=213&expiration=2025/10/13（敏感信息）
  string secret_content = 6;
  // （必传）支付金额（单位：分）
  int32 amount = 7;
  // （可选）json格式的附加扩展信息
  string extended_params = 8;
  // （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
  string transaction_time = 9;
  // （可选）用户真实支付方式，QPay渠道必传
  string pay_method = 10;
}

// 退款交易信息
message RefundSection {
  // （必传）渠道编码，alipay(支付宝)、wxpay(微信支付)、unionpay(银联支付)、hexunion(合阔聚合支付)
  string channel = 1;
  // （必传）传给第三方接口的唯一标识id
  string transaction_id = 2;
  // （必传）需要退款的交易的唯一标识id
  string related_transaction_id = 3;
  // （必传）需要退款的第三方交易的唯一标识id
  string related_tp_transaction_id = 4;
  // （必传）付款码、支付卡号
  string pay_code = 5;
  // （必传）密码、辅助码、二磁道信息等,格式- password=123&cvn_2=213&expiration=2025/10/13（敏感信息）
  string secret_content = 6;
  // （必传）退款金额（单位：分）
  int32 amount = 7;
  // （可选）json格式的附加扩展信息
  string extended_params = 8;
  // （可选）交易时间 "yyyy-mm-ddThh:mm:ss"
  string transaction_time = 9;
  // （可选）用户真实支付方式，QPay渠道必传
  string pay_method = 10;
  // （可选）货币（如HKD)
  string currency = 11;
}

// 订单信息
message OrderSection {
  // （可选）交易订单ticket_id
  string order_no = 1;
  // （可选）pos_id
  string pos_id = 2;
  // （可选）pos编码
  string pos_code = 3;
  // （可选）桌位号
  string table_no = 4;
  // （可选）订单生成时间 "yyyy_mm_dd_thh:mm:ss"
  string order_time = 5;
  // （可选）订单金额（单位：分），退款时必传
  int32 order_amount = 6;
  // （可选）订单标题/描述（支付宝支付时必传）
  string description = 7;
  // （可选）商品列表，部分第三方支付需要根据商品列表进行促销计算
  repeated Commodity commodities = 8;
  // （可选）订单优惠金额（单位：分）
  int32 discount_amount = 9;
}

// 会员信息
message MemberSection {
  // （必传）卡号
  string card_no = 1;
  // （必传）会员号码
  string memberNo = 2;
  // （必传）会员 ID
  string memberId = 3;
  // （必传）会员手机号
  string mobile = 4;
}

// 下单交易结果信息
message CreateResultSection {
  // （必传）预支付交易号
  string pre_pay_id = 1;
  // （可选）付款码
  string pay_code = 2;
  // （必传）支付渠道
  string pay_channel = 3;
  // （可选）用户真实支付方式
  string pay_method = 4;
  // （可选）json格式的附加扩展信息
  string extended_params = 5;
  // （可选）第三方流水号
  string tp_transaction_id = 6;
  // （可选）预支付交易客户端唤起支付所需签名
  string pre_pay_sign = 7;
  // （可选）预支付交易扩展字段，格式：a=b&c=d
  string pack_str = 8;
  // （可选）跳转地址,也可是二维码链接
  string front_url = 9;
  // （可选）过期时间
  string time_expire = 10;
}

// 支付交易结果信息
message TransactionResultSection {
  // （必传）交易状态
  string transaction_state = 1;
  // （必传）实际发生金额
  double real_amount = 2;
  // （必传）第三方流水号
  string tp_transaction_id = 3;
  // （必传）支付渠道
  string pay_channel = 4;
  // （可选）用户真实支付方式
  string pay_method = 5;
  // （可选）买家标识
  string payer = 6;
  // （可选）交易积分
  double transaction_points = 7;
  // （可选）帐户积分
  double account_points = 8;
  // （可选）json格式的附加扩展信息
  string extended_params = 9;
  // （可选）促销信息(商家折扣、平台折扣)
  repeated Promotion promotions = 10;
}

// 商品信息
message Commodity {
  // （可选）商品id
  string id = 1;
  // （必传）商品名称
  string name = 2;
  // （必传）商品编码
  string code = 3;
  // （必传）数量
  double quantity = 4;
  // （必传）价格(每个商品的)
  int32 price = 5;
  // （可选）商品图片地址
  string image_url = 6;
  // （可选）商品属性
  repeated CommodityProperty property = 7;
}

// 商品属性信息
message CommodityProperty {
  // 商品属性名称
  message PropertyName {
    // （必传）属性code
    string code = 1;
    //（必传）属性名称
    string name = 2;
  }

  // 商品属性值
  message PropertyValue {
    // （必传）属性值code
    string code = 1;
    // （必传）属性值名称
    string name = 2;
    // （必传）属性值价格
    double price = 3;
  }

  // （必传）商品属性名称
  PropertyName name = 1;
  // （必传）商品属性值
  PropertyValue value = 2;
}

// 支付优惠信息
message Promotion {
  // （必传）优惠id，即优惠券id
  string id = 1;
  // （必传）优惠名称，即优惠券名称
  string name = 2;
  // （可选）优惠编码，即优惠券编码
  string code = 3;
  // （必传）优惠金额，即优惠券面值金额（单位：分），例如：100
  int32 discount = 4;
  // （可选）商家承担的优惠金额（单位：分），例如：90
  int32 discount_on_merchant = 5;
  // （可选）平台承担的优惠金额（单位：分），例如：10
  int32 discount_on_platform = 6;
  // （可选）其他参与方承担的优惠金额（单位：分），例如：0
  int32 discount_on_others = 7;
  // （可选）用户实际买券所花费的金额（单位：分），例如：50
  int32 user_pay_amount = 8;
  // （可选）商家承担的券售价折扣金额（单位：分），例如：10
  int32 merchant_pay_amount = 9;
  // （可选）平台承担的券售价折扣金额（单位：分），例如：10
  int32 platform_pay_amount = 10;
  // （可选）其他第三方承担的券售价折扣金额（单位：分），例如：0
  int32 others_pay_amount = 11;
}
