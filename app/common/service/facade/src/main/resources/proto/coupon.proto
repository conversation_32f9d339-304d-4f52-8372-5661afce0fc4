syntax = "proto3";
option java_package = "cn.hexcloud.pbis.common.service.facade.member";
option java_multiple_files = true;

package coupon;

import "ticket.proto";
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";

// 会员卡券集成服务
service CouponService {

    // 示例方法
    rpc DemoMethod (DemoRequest) returns (DemoResponse) {}

    // 根据卡号/会员号/手机号码获取会员信息，以及相关的会员促销和会员卡券信息
    rpc GetMember (GetMemberRequest) returns (GetMemberResponse) {}

    // 根据卡号/会员号/手机号码获取会员信息，以及相关的会员促销和会员卡券信息
    rpc GetMemberNew (GetMemberRequest) returns (GetMemberNewResponse) {
        option (google.api.http) = {
      post: "/api/v3/pbis/getMember"
      body: "*"
    };
    }

    // 用于计算第三方的促销
    rpc CalculatePromotion (CalculatePromotionRequest) returns (CalculatePromotionResponse) {
        option (google.api.http) = {
      post: "/api/v2/pbis/calculatePromotion"
      body: "*"
    };
    }

    // 用于验证卡券是否可以使用，包括会员卡券和非会员卡券
    rpc VerifyCoupons (VerifyCouponsRequest) returns (VerifyCouponsResponse) {
        option (google.api.http) = {
      post: "/api/v3/pbis/verifyCoupons"
      body: "*"
    };
    }

    // 用于核销卡券
    rpc ConsumeCoupons (ConsumeCouponsRequest) returns (ConsumeCouponsResponse) {}

    // 用于反核销卡券
    rpc CancelCoupons (CancelCouponsRequest) returns (CancelCouponsResponse) {}

    // 消费积分
    rpc ConsumePoints (ConsumePointsRequest) returns (ConsumePointsResponse) {}

    // 回退积分
    rpc ReturnPoints (ReturnPointsRequest) returns (ReturnPointsResponse) {}

    // 获取可用积分
    rpc GetAvailablePoints (AvailablePointsRequest) returns (AvailablePointsResponse) {}

}

message ConsumePointsRequest {
    // 渠道编码(必传)
    string channel = 1;
    // 会员信息
    MemberContent member_content = 2;
    // 订单信息
    OrderContent order_content = 3;
    // 使用积分(分单位)
    int32 point = 4;
    // 积分抵现金额(分单位)
    int32 amount = 5;
    // 操作场景 true：积分抵现（默认）；false：积分更新
    bool use_point = 6;
    //
    string sessid = 10;
    // 支付时传给第三方接口的唯一标识id
    string batch_ticket_id = 11;
}

message AvailablePointsRequest {
    // 渠道编码(必传)
    string channel = 1;
    // 会员信息
    MemberContent member_content = 2;
    // 订单信息
    OrderContent order_content = 3;
}

message ConsumePointsResponse {
    // （必传）异常编码
    string error_code = 1;
    // （可选）异常信息
    string error_message = 2;
}

message AvailablePointsResponse {
    // 按商品分类设置的折扣比例与商品金额换算的积分上限和
    int32 maxPoint = 1;
    // 商户维度的积分使用下限
    int32 minPoint = 2;
    // 积分抵扣比例,100积分=1元
    int32 ratio = 3;
    string sessId = 4;
}

message ReturnPointsRequest {
    // 渠道编码(必传)
    string channel = 1;
    // 会员信息
    MemberContent member_content = 2;
    // 订单信息
    OrderContent order_content = 3;
    // 使用积分(分单位)
    int32 point = 4;
    // 积分抵现金额(分单位)
    int32 amount = 5;
    // 操作场景 true：积分抵现（默认）；false：积分更新
    bool use_point = 6;
    // 支付时传给第三方接口的唯一标识id
    string batch_ticket_id = 7;
}

message ReturnPointsResponse {
    // （必传）异常编码
    string error_code = 1;
    // （可选）异常信息
    string error_message = 2;
}

message SyncOrderContextRequest {
    Ticket ticket = 1;
}

message NotifyRequest {
    // （必传）渠道编码 SeltekTakeout(雪沥外卖)
    string channel = 1;
    // 合阔订单号
    string ticketId = 2;
    // 第三订单号
    string tpOrderId = 3;
    // 2 接单 3 已出餐 4 已完成 10 退款
    int32 status = 4;
    // 取餐号
    string takeMealNumber = 5;
    // 预计等待时间：单位秒
    int32 waitingTime = 6;
    // 前面等待订单数
    int32 waitingOrderCount = 7;
    // 前面等待商品数
    int32 waitingProductCount = 8;

    string  tpName = 9;

    int32 makeTime = 10;

    string source = 11;

}

message NotifyResponse {

    int32 statusCode = 1;
    string code = 2;
    string description = 3;

}

message UploadTicketRequest {
    // （必传）完整的ticket信息
    Ticket ticket = 1;
    string channel = 2;
}

message UploadTicketResponse {
    // （必传）是否上传成功
    bool success = 1;
    string ticket_id = 2;
}


message Entity{

}

message DemoRequest {
    string data = 1;
}

message DemoResponse {
    string json = 1;
}

message GetMemberRequest {
    //  渠道编码，HEYTEAMEMBER(喜茶会员)
    string channel = 1;
    //  卡号(card_no、member_code、mobile 三选一)
    string card_no = 2;
    //  会员编码(card_no、member_code、mobile 三选一)
    string member_code = 3;
    //  手机号(card_no、member_code、mobile 三选一)
    string mobile = 4;
    //  敏感信息	密码、辅助码、二磁道信息等,格式- password=123&CVN2=213&expiration=2025/10/13
    string secret_content = 5;
    // 门店id
    uint64 store_id = 6;
    // 门店partner id
    uint64 partner_id = 7;
    // 门店scope id，如果没有就传0
    uint64 scope_id = 8;
    // 用户id
    uint64 user_id = 9;
    //  附加扩展信息
    string extend = 10;
    // 订单信息
    OrderContent order_content = 11;
    // 门店code
    string store_code = 12;
    // 券码
    string coupon_no = 13;
}

message GetMemberResponse {
    // 渠道编码
    string channel = 1;
    // 账户余额
    double account_balance = 2;
    // 历史总积分
    int32 credit_total = 3;
    // 积分余额
    int32 credit_balance = 4;
    // 会员卡号
    string card_no = 5;
    // 会员ID
    string member_id = 6;
    // 会员编号
    string member_code = 7;
    // 会员名字
    string name = 8;
    // 性别：MALE/FEMALE/NA(未知)
    string gender = 9;
    // 邮箱
    string email = 10;
    // 手机号码
    string mobile = 11;
    // 会员状态(TODO枚举类型待定义)
    int32 status = 12;
    // 会员等级id
    string grade_id = 13;
    // 会员等级名称
    string grade_name = 14;
    // 会员授予时间
    string grant_date = 15;
    // 是否员工
    bool is_employee = 16;
    // 头像
    string avatar = 17;
    // 可以用于支付的编码，根据不用厂商，可以是card_no或member_code
    string paycode = 18;
    // 根据会员等级id(grade_id)获取的Hex促销id, 多个以逗号","隔开。POS后台的会员等级主档单独维护一个扩展字段用来映射grade_id，字段名称在集成过程中定义
    string promotion_id = 19;
    // 卡券列表
    repeated Coupon coupons = 20;
    // 标签提示语
    string greetings = 21;
    //储值卡列表
    repeated DepositCard depositCard = 22;
    //猜你喜欢
    repeated PurchasesAnalysis purchases_analysis = 23;
    // 生日
    string birthday = 24;
    // 抵扣金额
    string credit_deduction_amount = 25;
}

// 猜你喜欢
message PurchasesAnalysis {
    string code = 1;
    string name = 2;
}

message DepositCard {
    int32   amount = 1;  //实充余额，单位分
    string  applyId = 2;  // 卡规格ID
    string  cardCode = 3;  // 卡号
    string  cardName = 4;  // 卡名称
    int32   vamount = 5;  // 赠送余额，单位分
    bool    hasExpireTIme = 6;  // 是否有过期时间
    int64   validStartTime = 7;  // 生效时间
    int64   expireTime = 8;  // 过期时间
}

// 用户权益
message Benefit {
    // 权益id
    int32  benefit_id = 1;
}

message GetMemberNewResponse {
    // 渠道编码
    string channel = 1;
    // 账户余额
    double account_balance = 2;
    // 历史总积分
    int32 credit_total = 3;
    // 积分余额
    int32 credit_balance = 4;
    // 会员卡号
    string card_no = 5;
    // 会员ID
    string member_id = 6;
    // 会员编号
    string member_code = 7;
    // 会员名字
    string name = 8;
    // 性别：MALE/FEMALE/NA(未知)
    string gender = 9;
    // 邮箱
    string email = 10;
    // 手机号码
    string mobile = 11;
    // 会员状态(TODO枚举类型待定义)
    int32 status = 12;
    // 会员等级id
    string grade_id = 13;
    // 会员等级名称
    string grade_name = 14;
    // 会员授予时间
    string grant_date = 15;
    // 是否员工
    bool is_employee = 16;
    // 头像
    string avatar = 17;
    // 可以用于支付的编码，根据不用厂商，可以是card_no或member_code
    string paycode = 18;
    // 根据会员等级id(grade_id)获取的Hex促销id, 多个以逗号","隔开。POS后台的会员等级主档单独维护一个扩展字段用来映射grade_id，字段名称在集成过程中定义
    string promotion_id = 19;
    // 卡券列表
    repeated CouponNew coupons = 20;
    // 标签提示语
    string greetings = 21;
    // 储值卡列表
    repeated DepositCard depositCard = 22;
    // 权益列表
    repeated Benefit benefit = 23;
    //猜你喜欢
    repeated PurchasesAnalysis purchases_analysis = 24;
    // 生日
    string birthday = 25;
    // 积分抵扣金额
    string credit_deduction_amount = 26;
}

// 卡券信息
message CouponNew {
    //hex 内部卡券id
    string id = 1;
    //外部卡券唯一编码 用于卡券核销
    string code = 2;
    //卡券名称
    string name = 3;
    //卡券类别 hex 内部类别
    string type = 4;
    //卡券类别 外部类别
    string typeCode = 5;
    // 会员券(MEMBERSHIP)或非会员券(NORMAL)
    string isMember = 6;
    // 有效期起始时间: "2020-10-01 00:00:00"
    string startDate = 7;
    // 有效期结束时间 "2020-10-07 00:00:00"(seltek:expireDate)
    string expiredDate = 8;
    // 券状态: 0(可用), 1(不可用), 2(锁定)
    int32 status = 9;
    // 多次券(MANY)/单次券(ONCE)
    string useType = 10;
    //优惠券图片
    string couponImgUrl = 11;
    //是否是码商券 0:是
    int32 isMerchant = 12;
    //优惠券来源渠道 0: 未知 1-后台 2-兑换码兑换 3-券架领取 4-积分商城 5-礼品卡小程序 6-CRM活动 7-签到奖励 8-抽奖 9-marketing活动平台 10-客服补偿 11-咨库 12-分销小程序 21-企微 22-天猫码商
    int64 couponChannelType = 13;
    // 根据第三方券类别编码获取Hex券类别中的促销id, 多个以逗号","隔开。POS后台的卡券管理主档单独维护一个扩展字段用来映射券类别编码，字段名称在集成过程中定义
    string promotion_id = 14;
    //卡券财务计算
    Merchant merchant = 15;
    //次数限制
    Limited limited = 16;
    //优惠券规则
    RuleNew rule = 17;
    //扩展字段
    google.protobuf.Struct extend = 18;
}

message RuleNew {
    //整单满金额（单位：分）
    int32 amount = 1;
    //优惠金额（单位：分）
    int32 couponAmount = 2;
    //折扣（75折，值75）
    int32 discount = 3;
    //数量
    int32 quantity = 4;
    //赠品
    string productId = 5;
    int32 plusAmount = 6;
    int32 buyQuantity = 7;
    string buyProductId = 8;
    int32 giveQuantity = 9;
    string giveProductId = 10;
    //关联sku
    repeated string skus = 11;
}


// 卡券信息
message Coupon {
    string code_no = 1; // 券号(用于核销)(seltek:couponId)
    string id = 2; // HEX券类别(coupon_type)id，根据
    string name = 3; // 券名称 (seltek:couponName)
    string code = 4; // HEX券类别(coupon_type)编码
    double prepay = 5; // 是否提前核销(HEX券类型配置)
    string type_code = 6; // 外表卡券的类别编码，用于和HEX券类别映射. POS后台的卡券管理主档单独维护一个扩展字段用来映射type_code，字段名称在集成过程中定义
    double par_value = 7; // 面值(或可以抵用的金额)
    string type = 8; // 会员券(MEMBERSHIP)或非会员券(NORMAL)(seltek:MEMBERSHIP)
    string start_date = 9; // 有效期起始时间: "2020-10-01 00:00:00"
    string expired_date = 10; // 有效期结束时间 "2020-10-07 00:00:00"(seltek:expireDate)
    int32 status = 11; // 券状态: 1(可用), 0(不可用), 2(锁定)
    int32 available_count = 12; // 剩余次数
    string use_type = 13; // 多次券(MANY)/单次券(ONCE)
    string promotion_id = 14; // 根据第三方券类别编码获取Hex券类别中的促销id, 多个以逗号","隔开。POS后台的卡券管理主档单独维护一个扩展字段用来映射券类别编码，字段名称在集成过程中定义
    string start_timestamp = 15; // 有效期起始时间戳
    string expired_timestamp = 16; // 有效期结束时间戳

    //雪沥增加字段
    string couponInstanceCode = 17; //优惠券码
    //couponName =  name
    //couponId = code_no
    int64 couponTypeId = 18; //优惠券类型ID
    string couponImg2 = 19; //优惠券图片2
    string couponImg3 = 20; //优惠券图片3
    string couponTypeCode = 21; //优惠券类型code
    //expireDate = expired_date
    int32 couponFlag = 22; //优惠券标识符（1.普通 2. 即时券）
    string refTypeCode = 23; //POS扩展属性
    string refTypeId = 24; //POS扩展属性
    string refLevelId = 25; //POS扩展属性
    string refAmount = 26; //POS扩展属性
    string refProductId = 27; //POS扩展属性
    string refDepartmentId = 28; //POS扩展属性
    Rule rule = 29; //优惠券规则
    int32 isMerchant = 30; //是否是码商券
    Merchant merchant = 31; //码商专有属性【若isMerchant=1，返回该字段信息】
    Limited limited = 32; //次数限制
    int64 couponChannelType = 33; //优惠券来源渠道
    SkuDetail skuDetail = 34; //商品券附带信息
    string consume_transaction_id = 35; // 券核销流水号
    CouponAmount coupon_amount = 36; // 券金额信息
}

// 卡券金额信息
message CouponAmount {
    // 券面值，例如：50元券，单位：分
    int32 par_value = 1;
    // 券售价，例如：10元，单位：分
    int32 price = 2;
    // 用户实际买券所花费的金额，例如：6元，单位：分
    int32 pay_amount = 3;
    // 商家承担的券售价折扣金额，例如：3元，单位：分
    int32 discount_on_merchant = 4;
    // 平台（第三方券商）承担的券售价折扣金额，例如：1元，单位：分
    int32 discount_on_platform = 5;
    // 其他第三方承担的券售价折扣金额，单位：分
    int32 discount_on_others = 6;
}

message SkuDetail{
    int64 count = 1;
    repeated  string skus = 2;
}

message Rule {
    int32 amount = 1;
    int32 couponAmount = 2;
    int32 discount = 3;
    int32 quantity = 4;
    int64 productId = 5;
    int32 plusAmount = 6;
    int32 buyQuantity = 7;
    int64 buyProductId = 8;
    int32 giveQuantity = 9;
    int64 giveProductId = 10;
}
message Merchant{
    //用户为这个卡券花费了多少钱（单位分）
    int32 payPrice = 1;
    //这个卡券售价是多少（单位分）
    int32 sellPrice = 2;
    //原价是多少（单位分）
    int32 originPrice = 3;
}
message Limited{
    int32 todayLimit = 1; //当天剩余可用次数
    int32 sumLimit = 2; //总可剩余次数
    int32 usedQty = 3; //已使用次数
}
// 卡券请求信息
message CouponReq {
    // 券号(用于核销)
    string code_no = 1;
    // 券密码
    string password_code = 2;
    // 核销次数，对应多次券，验证是否可以核销的次数
    int32 qty = 3;
    // seltek卡券类型id 默认是0,999666为空券，10000为代金券，其他为优惠券
    string typeId = 4;
    //扩展字段，面值
    string extend = 5;
    //商品列表
    repeated ProductItem products = 6;
    // 券id
    string id = 7;
    // 券核销流水号
    string consume_transaction_id = 8;
    // 卡券优惠金额
    double discount = 9;

}

message ProductItem {
    //商品id
    string id = 1;
    //商品名称
    string name = 2;
    //商品编码
    string code = 3;
    //数量
    int64 quantity = 4;
    // 价格(每个商品的)
    int64 price = 5;
}

// 订单信息
message OrderContent {
    // 交易订单ticket id
    string order_ticket_id = 1;
    // pos id
    string pos_id = 2;
    // pos编码
    string pos_code = 3;
    // 桌位号
    string table_no = 4;
    // 交易时间 "2020-01-08 01:12:40"
    string sales_time = 5;
    // 营业日期 "2020-01-07"
    string sales_date = 6;
    // 毛额
    double gross_amount = 7;
    // 净额
    double net_amount = 8;
    // 商品列表，部分第三方支付需要根据商品列表进行促销计算
    repeated Product products = 9;
}

// 会员信息
message MemberContent {
    // 卡号(card_no、member_code、mobile 三选一)
    string card_no = 1;
    // 会员编码(card_no、member_code、mobile 三选一)
    string member_code = 2;
    // 手机号(card_no、member_code、mobile 三选一)
    string mobile = 3;
    // 敏感信息     密码、辅助码、二磁道信息等,格式- password=123&CVN2=213&expiration=2025/10/13
    string secret_content = 4;
}

// 商品
message Product {
    // 商品id
    string id = 1;
    // 商品名称
    string name = 2;
    // 商品编码
    string code = 3;
    // 数量
    double qty = 4;
    // 价格(每个商品的)
    double price = 5;
    // 加料
    repeated Product accessories = 6;
    // 属性
    repeated Attribute attributes = 7;
}

// 属性
message Attribute {
    // 属性名称
    string name = 1;
    // 属性编码
    string name_code = 2;
    // 属性值
    string value = 3;
    // 属性值编码
    string value_code = 4;
    // 属性价格
    double price = 5;
}

// 【卡券验证】请求
message VerifyCouponsRequest {
    // 渠道编码，HEYTEAMEMBER(喜茶会员)
    string channel = 1;
    // 会员信息
    MemberContent member_content = 2;
    // 门店id
    uint64 store_id = 3;
    // 门店partner id
    uint64 partner_id = 4;
    // 门店scope id，如果没有就传0
    uint64 scope_id = 5;
    // 用户id
    uint64 user_id = 6;
    // 卡券列表
    repeated CouponReq coupons = 7;
    // 是否返回对应促销/Hex券id等主档，默认false
    bool include_metadata = 8;
    // 门店code
    string store_code = 9;
}

// 【卡券验证】结果
message VerifyCouponsResponse {
    // 渠道编码
    string channel = 1;
    // 是否验证成功
    bool success = 2;
    // 异常编码，查看交易接口异常返回
    string error_code = 3;
    // 异常信息
    string message = 4;
    // 第三方编码
    string response_code = 5;
    // 第三方报文(500字符以内)
    string response_content = 6;
    // 卡券信息
    repeated Coupon details = 7;
    // 券核销凭证
    string consume_token = 8;
}

// 商品信息
message GoodsInfo {
    // 商品单价
    string price = 1;
    // 商品总价（不含加料）
    string amt = 2;
    string accAmt = 3;//加料总价
    int32  qty = 4; //商品数量
    string  key_id = 5;//商品id
    repeated string categories = 6;//商品分类id数组
    repeated ChargeGoods charge_goods = 7;//加料商品
    //商品信息扩展字段
    string shop_id = 8;
}

// 加料商品信息
message ChargeGoods {
    string price = 1; //商品单价
    string amt = 2; //加料商品总价
    int32  qty = 3; //商品数量
    string key_id = 4;//商品id
    //扩展字段，无业务相关
    string shop_id = 5;
}

// 【第三方促销计算】请求
message CalculatePromotionRequest {
    //门店code
    string storeCode = 1;
    //订单总价
    string subTotal = 2;
    //商品列表
    repeated GoodsInfo lines = 3;
    //适用场景
    int32 scene = 4;
    //适用渠道
    string channel = 5;
    //用户id
    int64 userId = 6;
    //打包费
    string packageFee = 7;
    //配送费
    string deliveryFee = 8;
    //优惠信息
    repeated PromotionDetail discs = 9;
    // 门店id
    uint64 store_id = 10;
    // 门店partner id
    uint64 partner_id = 11;
    // 门店scope id，如果没有就传0
    uint64 scope_id = 12;
}

// 优惠信息
message PromotionDetail {
    // 促销id
    string promotion_id = 1;
    // 1 优惠券 2优惠活动
    int32 promotion_type = 2;
}

// 【第三方促销计算】结果
message CalculatePromotionResponse {
    // 商品折扣信息
    repeated PriceDiscount discount = 2;
    // 折扣信息汇总
    SummaryDiscount summary = 3;
    // 渠道编码
    string channel = 4;
    // 是否验证成功
    bool success = 5;
    // 异常编码，查看交易接口的error_code
    string error_code = 6;
    // 异常信息
    string message = 7;
    // 第三方编码
    string response_code = 8;
    // 第三方报文(500字符以内)
    string response_content = 9;
    // 打包费信息
    PackageFee packageFee = 10;
    // 配送费信息
    DeliveryFee deliveryFee = 11;
}


message DeliveryFee {
    string deliveryTotal = 1;//配送费总金额
    string deliveryDiscount = 2;//配送费折扣金额
}

message PackageFee {
    string packageTotal = 1;//打包费总金额
    string packageDiscount = 2;//打包费折扣金额
}

// 折扣汇总信息
message SummaryDiscount {
    string subTotal = 1;//订单总金额
    string grantTotal = 2;//折扣后金额
    string discount = 3;//折扣总金额
}

// 折扣信息
message PriceDiscount {
    // 折扣类型名称
    string type = 1;
    // 折扣类型
    string discount_type = 2;
    // 折扣金额
    string discount = 3;
    // 促销名称
    string name = 4;
    // 促销id
    string promotion_id = 5;
    // 促销编号
    string promotion_code = 6;
    // 促销类型（NORMAL:普通促销，MEMBER:会员促销，COUPON:卡券促销）
    string promotion_type = 7;
    // 是否允许折上折
    bool allow_overlap = 8;
    // 显示在小票上的促销名称
    string ticket_display = 9;
    // 是否允许多次
    bool trigger_times_custom = 10;
    // 参与折扣的商品
    repeated string fired = 11;
    // 分摊信息
    repeated AbsorbedExpenses product = 12;
    // 优惠券模版ID
    string promotion_template_id = 13;
}

message AbsorbedExpenses {
    // 商品id
    string key_id = 1;
    // 换购商品优惠方式（AMOUNT:金额折扣，PERCENT:百分比折扣，PRICE:固定价格）
    string method = 2;
    // 商品价格
    string price = 3;
    // 商品数量
    int32  qty = 4;
    // 折扣后总价
    string amt = 5;
    // 换购商品折扣（根据优惠方式，数值代表的意义不同，AMOUNT:折扣金额，PERCENT:折扣百分比，PRICE:固定价格)
    string discount = 6;
    // 商品折扣金额
    string price_discount = 7;
    //分摊信息
    repeated ChargeInfo charge_info = 8;
    //商品信息扩展字段-回传
    string shop_id = 9;
}

//加料分摊信息
message ChargeInfo {
    string key_id = 1;//商品id
    string price = 3;//商品价格
    int32  qty = 4; //商品数量
    string amt = 5; //折扣后总价
    string price_discount = 7;//商品折扣金额
    int32  discount_goods_num = 8;//参与优惠的数量
    string shop_id = 9; //回传字段无业务逻辑
}

// 【卡券核销】请求
message ConsumeCouponsRequest {
    // 渠道编码，HEYTEAMEMBER(喜茶会员)
    string channel = 1;
    // 支付时传给第三方接口的唯一标识id
    string batch_ticket_id = 2;
    // 门店id
    uint64 store_id = 3;
    // 门店partner id
    uint64 partner_id = 4;
    // 门店scope id，如果没有就传0
    uint64 scope_id = 5;
    // 用户id
    uint64 user_id = 6;
    // 会员信息
    MemberContent member_content = 7;
    // 订单信息
    OrderContent order_content = 8;
    // 订单信息
    repeated CouponReq coupons = 9;
    // seltek storecode
    string storeCode = 10;
    // 使用积分（没有使用积分填0）
    double usedPoints = 11;
    // 券核销凭证
    string consume_token = 12;
}

// 【卡券核销】结果
message ConsumeCouponsResponse {
    // 渠道编码
    string channel = 1;
    // 是否验证成功
    bool success = 2;
    // 异常编码，查看交易接口的error_code
    string error_code = 3;
    // 异常信息
    string message = 4;
    // 第三方编码
    string response_code = 5;
    // 第三方报文(500字符以内)
    string response_content = 6;
    // 卡券信息
    repeated Coupon details = 7;
    // 打包费信息
    PackageFee packageFee = 8;
    // 配送费信息
    DeliveryFee deliveryFee = 9;
}

// 【卡券反核销】请求
message CancelCouponsRequest {
    // 渠道编码，HEYTEAMEMBER(喜茶会员)
    string channel = 1;
    // 支付时传给第三方接口的唯一标识id
    string batch_ticket_id = 2;
    // 门店id
    uint64 store_id = 3;
    // 门店partner id
    uint64 partner_id = 4;
    // 门店scope id，如果没有就传0
    uint64 scope_id = 5;
    // 用户id
    uint64 user_id = 6;
    // 会员信息
    MemberContent member_content = 7;
    // 订单信息
    OrderContent order_content = 8;
    // 订单信息
    repeated CouponReq coupons = 9;
    // seltek门店code
    string storeCode = 10;

}

// 【卡券反核销】结果
message CancelCouponsResponse {
    // 渠道编码
    string channel = 1;
    // 是否验证成功
    bool success = 2;
    // 异常编码，查看交易接口的error_code
    string error_code = 3;
    // 异常信息
    string message = 4;
    // 第三方编码
    string response_code = 5;
    // 第三方报文(500字符以内)
    string response_content = 6;
}

message SyncOrderRequest{
    string channel = 1;
    string memberCode = 2;
    string externalId = 3;
    int64 channelId = 4;
    string storeCode = 5;
    string orderDate = 6;
    string createTime = 7;
    double orderAmount = 8;
    int32 orderType = 9;
    double payAmount = 10;
    double couponAmount = 11;
    string optType = 12;
    double usedPoints = 13;
    string extTypeId = 14;
    string bindingAccount = 15;
    repeated OrderItemDtoList orderItemDtoList = 16;
    repeated OrderPaymentMethodDtoList orderPaymentMethodDtoList = 17;
    repeated OrderCouponDtoList orderCouponDtoList = 18;
}
message OrderItemDtoList{
    string orderItemName = 1;
    string productCode = 2;
    int32 quantity = 3;
    double amount = 4;
}
message OrderPaymentMethodDtoList{
    string payName = 1;
    double payAmount = 2;
}
message  OrderCouponDtoList{
    string couponInstanceCode = 1;
    uint64 useTimes = 2;
}
message SyncOrderResponse{
    // 渠道编码
    string channel = 1;
    // 是否验证成功
    bool success = 2;
    // 异常编码，查看交易接口的error_code
    string error_code = 3;
    // 异常信息
    string message = 4;
    // 第三方编码
    string response_code = 5;
    // 第三方报文(500字符以内)
    string response_content = 6;
}