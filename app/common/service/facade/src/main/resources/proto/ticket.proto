syntax = "proto3";
option java_multiple_files = true;
option java_package = "cn.hexcloud.pbis.common.service.facade.ticket";

package coupon;

message Ticket {
  string ticket_id = 1; //订单uuid，全市场范围内唯一
  string ticket_no = 2; //订单号，有特殊的业务规则
  string start_time = 3; //YYYY-MM-dd HH:MM:SS，订单开始时间
  string end_time = 4; //YYYY-MM-dd HH:MM:SS，订单结束时间
  string bus_date = 5; //YYYY-MM-dd，订单营业日期
  Pos pos = 6; //pos信息
  Operator operator = 7; //收银员信息
  Amount amounts = 8; //金额信息
  string takemealNumber = 9; //取餐号
  int32 qty = 10; //订单商品总数

  string status = 11;
  RefundInfo refundInfo = 12; //订单的退款相关信息
  Channel  channel = 13; //订单的渠道信息

  repeated TicketProduct products = 14; //订单商品信息

  repeated Payment payments = 15; //订单的支付信息

  repeated Promotion promotions = 16; //订单的促销信息

  repeated Member members = 17; //订单的会员信息

  Table table = 18; //桌位信息

  int32 people = 19; //订单人数

  string room_no = 20; //房间号

  string remark = 21; //订单备注

  bool house_ac = 22; //如家场景，意义待明确

  string order_time_type = 23; //早中晚餐标志，枚举值

  string shiftNumber = 24; //班次号

  repeated Tax taxList = 25; //税项

  Store store = 26; //门店信息

  Takeaway takeaway_info = 34; //外卖信息

  string ticketUno = 35; //订单唯一流水号

  repeated TicketCoupon coupons = 36; //卡券信息

  //费用信息
  repeated Fee fees = 37;
  //时区信息
  string timeZone = 38;

  //上传时间
  string upload_time = 39;
  bool discount_proportioned = 40; // 是否折扣分摊
  string transaction_no = 41;
  // 无需用户额外支付的费用信息
  repeated Fee fees_no_account = 42;
  Efficiency efficiency = 43;
  float weight = 45;// 称重商品总重量(kg)
  // 门店partner id
  uint64 partner_id = 44;
  bool pending_sync_member = 46; // 是否待同步会员单
}

message Efficiency {
  string confirmed_time = 1; //订单确认时间
  string made_time = 2; //制作完成时间
  string assigned_time = 3; //物流接单时间
  string arrived_time = 4; //骑手到店时间
  string fetched_time = 5; //骑手取餐时间
  string delivered_time = 6; //骑手送达时间
  float make_span = 7; //制作时长
  float avg_make_span = 8; //平均每杯制作时长
  float arrive_span = 9; //取餐时长
  float deliver_span = 10; //配送时长
}

message InvoiceInfo{
  bool issue = 1; //是否开具发票
  string type = 2; //发票类型
  string title = 3; //发票抬头
  string taxPayerId = 4; //纳税人识别号
}

message Prior {// 插单
  string code = 1;
  string reason = 2;
  bool isPrior = 3;
}

message Store {//门店信息
  string id = 1;
  string code = 2;
  string secondCode = 3; //门店三方编码
  string companyId = 4; //门店三方id
  string partnerId = 5; //租户id
  string scopeId = 6;
  string branchId = 7;
  }

message Pos {//pos相关的信息
  string id = 1; //pos id
  string code = 2; // pos 编码
  string pos_name = 3; //pos名称
  string device_id = 4; // pos的设备id
  string device_code = 5; //pos设备编码
}

message Amount {
  double taxAmount = 1;
  double gross_amount = 2;
  double net_amount = 3;
  double pay_amount = 4;
  double discount_amount = 5;
  double removezero_amount = 6;
  double rounding = 7;
  double overflow_amount = 8;
  double changeAmount = 9;
  double serviceFee = 10;
  double tip = 11;
  double commission = 12;
  double amount_0 = 13;
  double amount_1 = 14;
  double amount_2 = 15;
  double amount_3 = 16;
  double amount_4 = 17;
  bool taxIncluded = 18; //价税合一
  float otherFee = 19; //其他费用
  float merchant_discount_amount = 20; //商家优惠承担
  float platform_discount_amount = 21; //活动平台优惠承担
  float projected_income = 22; //预计收入
  double receivable = 23; //应付金额/应收金额
  double real_amount = 24;
  double business_amount = 25;
  double expend_amount = 26;
  double payment_transfer_amount = 27;
  double discount_transfer_amount = 28;
  double store_discount_amount = 29;
  double discount_merchant_contribute = 30;
  double discount_platform_contribute = 31;
  double discount_buyer_contribute = 32;
  double discount_other_contribute = 33;
  double pay_merchant_contribute = 34;
  double pay_platform_contribute = 35;
  double pay_buyer_contribute = 36;
  double pay_other_contribute = 37;
  double delivery_fee = 38;
  double delivery_fee_for_platform = 39;
  double delivery_fee_for_merchant = 40;
}

message Operator {//订单收银员相关的信息
  string id = 1; // 收银员id
  string name = 2; //收银员姓名
  string code = 3; //收银员code
  string login_time = 4; //登陆时间
  string loginId = 5; //登陆id
}

message Table {//桌位信息
  string id = 1; //桌位id
  string zone_id = 2; //桌位分区id
  string no = 3; //桌位号
  string zoneNo = 4; //桌位分区号
  int32 people = 5; //桌位人数
  bool temporary = 6; //是否是临时桌位
}

message Channel {
  string source = 1;
  string deviceType = 2;
  string orderType = 3;
  string deliveryType = 4;
  string tpName = 5;
  //渠道编码
  string code = 6;
  //渠道id
  string id = 7;
  //第三方编码，内部有标准定义
  string mapping_code = 8;
}


message RefundInfo {
  string refund_id = 1;// 退单负单的ticketId，
  string ref_ticket_id = 2; //退单正单的ticketId
  string ref_ticket_no = 3; //退单正单的ticketNo
  string refund_reason = 4; //退单原因
  string refund_no = 5;// 退单负单的orderId，
  string refund_side = 6; //退单方
  string refund_code = 7;
}

message TicketProduct {//订单商品
  string id = 1; //商品id
  string name = 2; //商品名称
  string code = 3; //商品编码
  int64 seq_id = 4; //商品的顺序号，代表下单的顺序
  double price = 5; //商品单价
  double amount = 6; //商品总价
  int32 qty = 7; //商品数量
  double discount_amount = 8; //商品的折扣金额
  string type = 9; //商品类型
  repeated TicketProduct accessories = 10; //加料
  repeated TicketProduct combo_items = 11; //套餐子项
  string operation_records = 12; //操作记录
  repeated SkuRemark skuRemark = 13; //sku属性
  string remark = 14; //商品备注
  double taxAmount = 15; //税额
  double net_amount = 16; //净额
  float avg_make_span = 17; //商品组平均制作时长
  float weight = 18;//重量(kg)
  bool has_make_span = 19; //是否有制作时长信息
  double sum_discount_amount = 20; //商品折扣
  double sum_net_amount = 21; //商品实收
  double sum_amount = 22; //商品原价
  bool has_weight = 23;//是否称重商品
}

message SpellUser{
  string name = 1;
  int32 qty = 2;
}

message Currency {
  string name = 1; //币种名称
  string symbol = 2; //币种符号
}

message SkuRemark {
  message skuName {
    string id = 1; //sku属性id
    string code = 2; //sku属性code
    string name = 3; //sku属性名称
  }
  message skuValue {
    string code = 1; //sku属性值code
    string name = 2; //sku属性值名称
  }
  skuName name = 1;
  skuValue values = 2;
}

message Tax {
  double amount = 1; //税金额
  double subTotal = 2; //纳税商品总额
  string code = 3; //税种编码
  string name = 4; //税种名称
  double rate = 5; //税率
}

message Payment {//支付信息
  string id = 1; //支付id
  string seq_id = 2; //支付顺序号
  double pay_amount = 3; //支付金额
  double realPayAmount = 4; //字段废弃
  double change = 5; //找零
  double overflow = 6; //溢收
  double rounding = 7; //rounding
  string pay_time = 8; //支付时间，YYYY-MM-DD HH:MM:SS
  string trans_code = 9; //支付方式编码
  string name = 10; //支付方式名称
  //实收金额
  double receivable = 11;
  string tpTransactionNo = 12;
  double tp_allowance = 13; //第三方补贴金额
  double merchant_allowance = 14;//商家补贴金额
  string trans_name = 15; //卡券支付，存储券名称
  double price = 16; //售价
  double cost = 17; //用户实际购买金额
  double real_amount = 18; //商家实收
  bool has_invoiced = 19;//是否已开发票
  double transfer_amount = 20; //支付转折扣金额
  double platform_allowance = 21;
  // 买家ID
  string payerNo = 22;
}

message Promotion {//促销
  PromotionInfo promotionInfo = 1;
  PromotionSource source = 2;
  repeated PromotionProduct products = 3;
}

message PromotionInfo {
  string type = 1;
  string discount_type = 2;
  string name = 3;
  string promotion_id = 4;
  string promotion_code = 5;
  string promotion_type = 6;
  bool allow_overlap = 7;
  bool trigger_times_custom = 8;
  string ticket_display = 9;
  double max_discount = 10;
}

message PromotionSource {
  int32 trigger = 1;
  double discount = 2;
  repeated string fired = 3;
  double merchant_discount = 4;
  double platform_discount = 5;
  double store_discount = 6;
  double cost = 7;
  double tp_allowance = 8;
  double merchant_allowance = 9;
  double platform_allowance = 10;
  double real_amount = 11;
  double transfer_amount = 12;

}

message PromotionProduct {
  double price = 1;
  double amt = 2;
  double accAmt = 3;
  int32 qty = 4;
  string key_id = 5;
  repeated string accies = 6;
  string type = 7;
  double discount = 8;
  double free_amt = 9;
  string method = 10;
  repeated PromotionProduct accessories = 11;
  float weight = 12; //重量(kg)
  bool has_weight = 13; //是否称重商品
}

message Member {
  string member_code = 1;
  string mobile = 2;
  string name = 3;
  string greetings = 4;
  int64  balance_points = 5;
  int64  total_points = 6;
  int64  order_points = 7;
  string grade_code = 8; //会员等级编码
  string grade_name = 9; //会员等级名称
}

message TicketCoupon {
  bool is_online = 1;
  string id = 2;
  string name = 3;
  string code = 4;
  int64 type = 5;
  double par_value = 6;
  string sequence_id = 7;
  double price = 8;
  double cost = 9; //（实付金额）（=price-tp_allowance-merchant_allowance？）
  double tp_allowance = 10; // (购买卡券时的优惠金额，平台部分)
  double merchant_allowance = 11; // (购买卡券时的优惠金额，商家部分)
  double real_amount = 12; //商家实收
  double transfer_amount = 13;//折扣转支付金额
  double platform_allowance = 14;
}

message Fee {
  string name = 1;
  double price = 2;
  int32 qty = 3;
  double amount = 4;
  string type = 5;
  float discount_rate = 6;
  repeated Fee detail_fees = 7;
}

message Takeaway {
  string order_method = 1;
  bool is_paid = 3;
  string tp_order_id = 4;
  string order_time = 5;
  string deliver_time = 6;
  string description = 7;
  string consignee = 8;
  string delivery_poi_address = 9;
  repeated string phone_list = 10;
  string tp = 11;
  string source = 12;
  string source_order_id = 13;
  string day_seq = 14;
  int32 delivery_type = 15;
  string delivery_name = 16;
  string invoice_title = 17;
  string waiting_time = 18;
  int32 tableware_num = 19;
  double send_fee = 20;
  double package_fee = 21;
  string delivery_time = 22;
  string take_meal_sn = 23;
  int32 partnerPlatformId = 24;
  string partnerPlatformName = 25;
  string wxName = 26;
  bool isHighPriority = 27;
  //外卖订单类型
  string takeoutType = 28;
  //部分退款时的外卖原单号
  string originalOrderNo = 29;

  float merchant_send_fee = 30; //商家替用户承担配送费
  bool selfDelivery = 31; //是否自配送
  string delivery_phone = 32; //配送员电话
  string delivery_platform = 33; //配送平台
  string invoice_type = 34; //发票类型
  string invoice_tax_payer_id = 35; //纳税人识别号
  string invoice_email = 36; //用户取发票邮箱
  float platform_send_fee = 37; //平台承担配送费
  float send_fee_for_platform = 38;
  float send_fee_for_merchant = 39;
  string invoice_provider = 40;   // 开票供应商
  string invoice_amount = 41;     // 开票金额
  string invoice_url = 42;        // 开票链接
}

message Blessing{
  string templateId = 1;
  string to = 2;
  string content = 3;
  string from = 4;
}