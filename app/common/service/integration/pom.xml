<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>cn.hexcloud</groupId>
    <artifactId>pbis</artifactId>
    <version>1.2.4-SNAPSHOT</version>
    <relativePath>../../../../pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>pbis-common-service-integration</artifactId>

  <properties>
    <alipay.sdk.version>4.39.19.ALL</alipay.sdk.version>
    <ccb.sdk.version>*******</ccb.sdk.version>
    <stripe.sdk.version>22.3.0</stripe.sdk.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>cn.hexcloud</groupId>
      <artifactId>pbis-common-util</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.retry</groupId>
      <artifactId>spring-retry</artifactId>
      <version>1.3.1</version>
    </dependency>
    <dependency>
      <groupId>cn.hexcloud</groupId>
      <artifactId>pbis-common-dal</artifactId>
    </dependency>
    <!--grpc & protobuf start-->
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-all</artifactId>
    </dependency>
    <dependency>
      <groupId>net.devh</groupId>
      <artifactId>grpc-client-spring-boot-starter</artifactId>
    </dependency>
    <!--grpc & protobuf end-->
    <dependency>
      <groupId>com.alipay.sdk</groupId>
      <artifactId>alipay-sdk-java</artifactId>
      <version>${alipay.sdk.version}</version>
    </dependency>

    <!-- https://mvnrepository.com/artifact/com.stripe/stripe-java -->
    <dependency>
      <groupId>com.stripe</groupId>
      <artifactId>stripe-java</artifactId>
      <version>${stripe.sdk.version}</version>
    </dependency>

    <dependency>
      <groupId>cn.hexcloud.fiserv</groupId>
      <artifactId>first-data-gateway</artifactId>
      <version>1.0.7</version>
    </dependency>

    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.10.1</version>
    </dependency>

    <dependency>
      <groupId>com.ccb</groupId>
      <artifactId>mis-sdk</artifactId>
      <version>${ccb.sdk.version}</version>
    </dependency>
  </dependencies>

</project>
