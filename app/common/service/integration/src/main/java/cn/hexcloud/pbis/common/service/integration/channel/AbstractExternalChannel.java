package cn.hexcloud.pbis.common.service.integration.channel;

import cn.hexcloud.commons.trace.util.TraceUtil;
import cn.hexcloud.commons.utils.SpringContextUtil;
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig;
import cn.hexcloud.pbis.common.service.integration.script.ScriptManager;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

/**
 * @ClassName AbstractExternalChannel.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2022/01/10 20:38:48
 */
public abstract class AbstractExternalChannel implements ExternalChannel {

  @Autowired
  protected ChannelAccessSupportService channelAccessSupportService;

  protected ChannelAccessConfig channelAccessConfig;

  @Override
  public Object doRequest(String channelModule, String method, Object... input) {
    String scriptKey = getScriptKey(channelModule);
    ScriptManager scriptManager = SpringContextUtil.bean(ScriptManager.class);
    String scriptText = scriptManager.getScript(this.getChannelAccessConfig().getChannelCode(), scriptKey);
    Map<String, String> eventAttrs = CollectionUtils.newHashMap(1);
    eventAttrs.put("script_key", scriptKey);
    TraceUtil.currSpan().addEvent("loadScriptEnd", eventAttrs);
    return scriptManager.invoke(scriptText, method, new Object[]{this}, input);
  }

  private String getScriptKey(String channelModule) {
    return this.getChannelAccessConfig().getChannelCode() + "_" + channelModule;
  }

}
