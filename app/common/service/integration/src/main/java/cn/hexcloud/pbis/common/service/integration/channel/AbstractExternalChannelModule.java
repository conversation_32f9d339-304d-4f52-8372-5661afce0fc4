package cn.hexcloud.pbis.common.service.integration.channel;

import java.util.Map;

/**
 * @ClassName AbstractExternalChannelModule.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2022/01/09 15:09:56
 */
public abstract class AbstractExternalChannelModule implements ExternalChannelModule {

  protected ExternalChannel channel;

  public AbstractExternalChannelModule(ExternalChannel channel) {
    this.channel = channel;
  }

  protected String getSignModuleName() {
    return "Sign";
  }

  @Override
  public String getSignature(Map<String, String> rawMessage) {
    Object result = channel.doRequest(getSignModuleName(), "getSignature", rawMessage);
    return null == result ? null : result.toString();
  }

  @Override
  public boolean isValidSignature(Map<String, String> unverifiedMessage) {
    return (boolean) channel.doRequest(getSignModuleName(), "isValidSignature", unverifiedMessage);
  }

  protected String getFullMethodName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method;
  }

}
