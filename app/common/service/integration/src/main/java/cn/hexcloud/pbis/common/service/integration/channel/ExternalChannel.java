package cn.hexcloud.pbis.common.service.integration.channel;

import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig;

/**
 * @ClassName ExternalChannel.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/05 17:21:53
 */
public interface ExternalChannel extends Channel {

  void init(ChannelAccessConfig channelAccessConfig);

  ChannelAccessConfig getChannelAccessConfig();

  ChannelAccessSupportService getChannelAccessSupportService();

  Object doRequest(String channelModule, String method, Object... input);

}
