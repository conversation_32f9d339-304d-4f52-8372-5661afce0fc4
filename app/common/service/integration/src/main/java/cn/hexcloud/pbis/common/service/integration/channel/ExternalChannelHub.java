package cn.hexcloud.pbis.common.service.integration.channel;

import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig;

/**
 * @ClassName ExternalChannelHub.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/24 17:19:53
 */
public interface ExternalChannelHub<T extends ExternalChannel> extends ChannelHub<T> {

  T on(String channelCode, ChannelAccessConfig channelAccessConfig);

}
