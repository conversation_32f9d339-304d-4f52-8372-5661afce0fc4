package cn.hexcloud.pbis.common.service.integration.channel;

import java.util.Map;

/**
 * @ClassName ExternalChannelModule.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2022/01/10 20:50:20
 */
public interface ExternalChannelModule extends ChannelModule {

  String getSignature(Map<String, String> rawMessage);

  boolean isValidSignature(Map<String, String> unverifiedMessage);

}
