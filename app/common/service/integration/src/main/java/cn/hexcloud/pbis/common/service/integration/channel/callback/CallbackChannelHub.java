package cn.hexcloud.pbis.common.service.integration.channel.callback;

import cn.hexcloud.commons.exception.CommonException;
import cn.hexcloud.pbis.common.service.integration.channel.ChannelHub;
import cn.hexcloud.pbis.common.util.exception.ServiceError;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName CallbackChannelHub.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2022/01/09 17:29:25
 */
@Component
public class CallbackChannelHub implements ChannelHub<CallbackChannel> {

  @Autowired
  private Map<String, CallbackChannel> callbackChannelMap;

  @Override
  public CallbackChannel on(String channelCode) {
    CallbackChannel callbackChannel = callbackChannelMap.get(channelCode + "Callback");
    if (null == callbackChannel) {
      throw new CommonException(ServiceError.CHANNEL_NOT_FOUND, channelCode);
    }

    return callbackChannel;
  }

}
