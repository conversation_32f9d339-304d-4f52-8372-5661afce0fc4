package cn.hexcloud.pbis.common.service.integration.channel.callback;

import cn.hexcloud.commons.exception.CommonException;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelSyncOrderRequest;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelSyncOrderResponse;
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.util.ProtoBeanUtil;
import cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest;
import cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest.Builder;
import cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketResponse;
import cn.hexcloud.pbis.common.service.integration.eticket.client.EticketClient;
import cn.hexcloud.pbis.common.util.exception.ServiceError;
import com.alibaba.fastjson.JSON;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname EticketCallback
 * @Description:
 * @Date 2022/2/113:38 下午
 * <AUTHOR>
 */
@Service
@Slf4j
public class EticketCallback implements CallbackChannel {

  @Autowired
  private EticketClient eticketClient;

  @Override
  public void callback(Object payload) {

    ChannelSyncOrderRequest channelSyncOrder = (ChannelSyncOrderRequest) payload;
    UploadTicketRequest request = buildRequest(channelSyncOrder);
    log.info("开始同步eticket,oderId:{}", channelSyncOrder.getTicket().getTicketId());
    UploadTicketResponse uploadTicketResponse = eticketClient.uploadTicket(request);
    ChannelSyncOrderResponse channelSyncOrderResponse = new ChannelSyncOrderResponse();
    channelSyncOrderResponse.setSuccess(uploadTicketResponse.getSuccess());
    log.info("同步eticket成功,oderId:{}", channelSyncOrder.getTicket().getTicketId());

  }

  private UploadTicketRequest buildRequest(ChannelSyncOrderRequest channelSyncOrder) {
    Builder builder = UploadTicketRequest.newBuilder();
    try {
      ProtoBeanUtil.toProtoBean(builder, JSON.toJSONString(channelSyncOrder));
    } catch (IOException e) {
      throw new CommonException(ServiceError.PARAM_CONSTRAINTS_VIOLATION);
    }
    return builder.build();
  }


  @Override
  public String getChannelCode() {
    return "Eticket";
  }

  @Override
  public String getChannelName() {
    return "Eticket";
  }
}
