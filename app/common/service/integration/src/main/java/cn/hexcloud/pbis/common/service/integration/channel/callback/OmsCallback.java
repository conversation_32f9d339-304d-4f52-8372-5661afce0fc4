package cn.hexcloud.pbis.common.service.integration.channel.callback;

import cn.hexcloud.commons.exception.CommonException;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelNotifyRequest;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelSyncOrderRequest;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelNotifyResponse;
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.util.ProtoBeanUtil;
import cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest;
import cn.hexcloud.pbis.common.service.integration.eticket.client.EticketClient;
import cn.hexcloud.pbis.common.service.integration.oms.AcceptKosOrderStatusRequest;
import cn.hexcloud.pbis.common.service.integration.oms.AcceptKosOrderStatusRequest.Builder;
import cn.hexcloud.pbis.common.service.integration.oms.AcceptKosOrderStatusResponse;
import cn.hexcloud.pbis.common.service.integration.oms.client.OmsClient;
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant;
import cn.hexcloud.pbis.common.util.context.ServiceContext;
import cn.hexcloud.pbis.common.util.exception.ServiceError;
import com.alibaba.fastjson.JSON;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname OmsCallback
 * @Description:
 * @Date 2022/2/113:40 下午
 * <AUTHOR>
 */
@Service
@Slf4j
public class OmsCallback implements CallbackChannel {


  @Autowired
  private OmsClient omsClient;


  @Override
  public void callback(Object payload) {

    String storeId = (String) ServiceContext.get(ContextKeyConstant.STORE_ID);
    ChannelNotifyRequest channelNotifyRequest = (ChannelNotifyRequest) payload;
    log.info("hub请求头信息,storeId:{}", storeId);

    Builder builder = AcceptKosOrderStatusRequest.newBuilder()
        .setOrderNo(channelNotifyRequest.getTicketId())
        .setWaitOrderCount(channelNotifyRequest.getWaitingOrderCount())
        .setWaitProductCount(channelNotifyRequest.getWaitingProductCount())
        .setWaitTime(channelNotifyRequest.getWaitingTime())
        .setStatus(channelNotifyRequest.getStatus())
        .setStoreId(storeId)
        .setTpName(channelNotifyRequest.getTpName())
        .setPickUpNum(channelNotifyRequest.getTakeMealNumber());
    log.info("开始调用oms:" + channelNotifyRequest.getTicketId());
    AcceptKosOrderStatusResponse response = omsClient.acceptKosOrderStatus(builder.build());
    log.info("调用oms信息:" + response);
    ChannelNotifyResponse channelNotifyResponse = new ChannelNotifyResponse();
    channelNotifyResponse.setSuccess(true);
    channelNotifyResponse.setStatusCode(0);
    channelNotifyResponse.setDescription(response.getMessage());
    log.info("调用oms成功：" + channelNotifyRequest.getTicketId());

  }


  @Override
  public String getChannelCode() {
    return "Oms";
  }

  @Override
  public String getChannelName() {
    return "Oms";
  }
}
