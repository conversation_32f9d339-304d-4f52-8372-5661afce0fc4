package cn.hexcloud.pbis.common.service.integration.channel.callback;

import cn.hexcloud.commons.utils.DateUtil;
import cn.hexcloud.pbis.common.service.integration.channel.callback.dto.PaymentNotificationRequest;
import cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq;
import cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.ActionType;
import cn.hexcloud.pbis.common.service.integration.payflow.PayFlowClient;
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant;
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext;
import com.google.protobuf.util.JsonFormat;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName PayFlowCallback.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2022/01/26 17:08:43
 */
@Service
public class PayFlowCallback implements CallbackChannel {

  @Autowired
  private PayFlowClient payFlowClient;

  @Override
  public String getChannelCode() {
    return "PayFlow";
  }

  @Override
  public String getChannelName() {
    return "PayFlow";
  }

  @SneakyThrows
  @Override
  public void callback(Object payload) {
    // 通知PayFlow支付结果
    PaymentNotificationRequest request = (PaymentNotificationRequest) payload;
    ActionType actionType = "PAY".endsWith(request.getNotificationType()) ? ActionType.PAY : ActionType.REFUND;
    NotifyReq.Builder notifyPayReqBuilder = NotifyReq.newBuilder()
        .setStatus(request.getTransactionState())
        .setPaymentTicketId(request.getTransactionId())
        .setTpTransactionNo(request.getTpTransactionId())
        .setActionType(actionType);

    // PayMethod
    if(request.getPayMethod() != null) {
        notifyPayReqBuilder.setPayMethod(request.getPayMethod().getCode());
    }

    // extendedParams
    if(request.getExtendedParams() != null) {
      notifyPayReqBuilder.setExtendedParams(request.getExtendedParams());
    }

    NotifyReq notifyPayReq = notifyPayReqBuilder.build();
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA,
        JsonFormat.printer().includingDefaultValueFields().omittingInsignificantWhitespace().print((notifyPayReq).toBuilder()));
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, DateUtil.getNowTimeStamp());
    payFlowClient.payNotify(notifyPayReq);
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, DateUtil.getNowTimeStamp());
  }
}
