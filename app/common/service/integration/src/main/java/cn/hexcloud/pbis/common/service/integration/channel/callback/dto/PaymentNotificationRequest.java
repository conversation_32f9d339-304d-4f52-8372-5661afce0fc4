package cn.hexcloud.pbis.common.service.integration.channel.callback.dto;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Promotion;
import java.math.BigDecimal;
import java.nio.file.OpenOption;
import java.util.List;

import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName PaymentNotificationRequest.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/14 10:40:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class PaymentNotificationRequest extends ChannelRequest {

  private String transactionState;
  private String notificationType;
  private BigDecimal realAmount;
  private String transactionId;
  private String tpTransactionId;
  private Double transactionPoints;
  private Double accountPoints;
  private PayMethod payMethod;
  private String extendedParams;
  private List<Promotion> promotions;
}
