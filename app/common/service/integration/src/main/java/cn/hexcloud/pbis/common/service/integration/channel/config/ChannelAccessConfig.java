package cn.hexcloud.pbis.common.service.integration.channel.config;

import java.security.cert.X509Certificate;
import java.util.Map;
import lombok.Data;
import lombok.ToString;

/**
 * @ClassName ChannelAccessConfig.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/15 13:50:11
 */
@Data
@ToString
public class ChannelAccessConfig {

  private String channelCode;
  private String merchantId;
  private String subMerchantId;
  private String appId;
  private String appKey;
  private String accessKey;
  private String certText;
  private String privateKey;
  private String thirdPartyPublicKey;
  private String terminalId;
  private String gatewayUrl;
  private String apiVersion;
  private X509Certificate cert;
  private String authToken;
  private Map<String, String> properties;

  public String getProperty(String key) {
    return null == properties ? null : properties.get(key);
  }

}
