package cn.hexcloud.pbis.common.service.integration.channel.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @author: miao
 * @create: 2021-12-27 14:35
 * */
@ConfigurationProperties(prefix = "pbis.channel.partner")
@Component
public class ChannelPartnerRelationConfig {

  @Setter
  private Map<String, String> relation;

  public String getChannelCode(String partnerId) {
    String channel = "";
    if (CollUtil.isEmpty(relation)) {
      return channel;
    }
    for (Entry<String, String> entry : relation.entrySet()) {
      // 租户ID List
      List<String> partnerIdList = StrUtil.split(entry.getValue(), ",");
      if (CollUtil.isNotEmpty(partnerIdList) && partnerIdList.contains(partnerId)) {
        channel = entry.getKey();
        break;
      }
    }
    return channel;
  }
}
