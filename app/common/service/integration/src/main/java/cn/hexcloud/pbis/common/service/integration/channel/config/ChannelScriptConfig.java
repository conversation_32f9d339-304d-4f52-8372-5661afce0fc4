package cn.hexcloud.pbis.common.service.integration.channel.config;

import java.util.Map;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @ClassName ChannelScriptConfig.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2022/01/07 16:01:41
 */
@ConfigurationProperties(prefix = "pbis.channel.script")
@Component
public class ChannelScriptConfig {

  @Setter
  private Map<String, String> versions;

  public String getScriptVersion(String scriptKey) {
    String scriptVersion = CollectionUtils.isEmpty(versions) ? null : versions.get(scriptKey);
    return StringUtils.isBlank(scriptVersion) ? "LATEST" : scriptVersion;
  }

}
