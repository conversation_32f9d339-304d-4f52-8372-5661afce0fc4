package cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName ConfirmSignupRequest.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/23 11:36:45
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ConfirmSignupRequest extends ChannelRequest {

  private String signupBatchNo;

}
