package cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName CreateSignupRequest.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/23 11:36:07
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class CreateSignupRequest extends ChannelRequest {

  private String merchantId;
  private String contactName;
  private String contactEmail;
  private String contactMobile;

}
