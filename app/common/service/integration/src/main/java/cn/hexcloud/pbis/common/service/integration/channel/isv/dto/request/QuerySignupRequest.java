package cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName QuerySignupRequest.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/23 11:37:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class QuerySignupRequest extends ChannelRequest {

  private String signupBatchNo;

}
