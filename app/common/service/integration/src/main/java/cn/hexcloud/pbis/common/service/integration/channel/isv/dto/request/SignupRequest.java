package cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelRequest;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName SignupRequest.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/23 11:30:01
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class SignupRequest extends ChannelRequest {

  private String signupBatchNo;
  private String partyName;
  private String businessCategory;
  private Double serviceRate;
  private String countryCode;
  private String provinceCode;
  private String cityCode;
  private String districtCode;
  private String address;
  private Map<String, SignupAttachment> attachments;

}
