package cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request;

/**
 * @ClassName SignupState.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/25 19:57:07
 */
public enum SignupState {

  WAITING("WAITING", "待签约"),
  UNDER_REVIEW("UNDER_REVIEW", "签约审核中"),
  UNCONFIRMED("UNCONFIRMED", "待商家确认"),
  COMPLETE("COMPLETE", "已签约"),
  FAILURE("FAILURE", "签约失败"),
  UNKNOWN("UNKNOWN", "未知状态"),
  ;

  private final String code;
  private final String name;

  SignupState(String code, String name) {
    this.code = code;
    this.name = name;
  }

  public static SignupState byCode(String code) {
    for (SignupState value : SignupState.values()) {
      if (value.code.equals(code)) {
        return value;
      }
    }

    return null;
  }

  public String getCode() {
    return code;
  }

  public String getName() {
    return name;
  }

}
