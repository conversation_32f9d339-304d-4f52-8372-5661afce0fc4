package cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelRequest;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Singular;
import lombok.ToString;

/**
 * @ClassName SimpleSignupRequest.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/24 20:28:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class SimpleSignupRequest extends ChannelRequest {

  private String merchantId;
  private Double serviceRate;
  private String contactName;
  private String contactEmail;
  private String contactMobile;
  private String partyName;
  private String businessCategory;
  private String countryCode;
  private String provinceCode;
  private String cityCode;
  private String districtCode;
  private String address;
  @Singular
  private Map<String, SignupAttachment> attachments;

}
