package cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName ConfirmSignupResponse.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/23 11:36:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ConfirmSignupResponse extends ChannelResponse {

  private String signupNo;
  private String merchantId;
  private String appId;

}
