package cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName GrantCallbackResponse.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/23 11:40:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class GrantCallbackResponse extends ChannelResponse {

  private String merchantId;
  private String grantTargetId;
  private String authToken;
  private String refreshToken;
  private String response2ThirdParty;

}
