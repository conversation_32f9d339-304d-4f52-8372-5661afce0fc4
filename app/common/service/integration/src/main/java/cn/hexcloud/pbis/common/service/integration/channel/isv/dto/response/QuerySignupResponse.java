package cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName QuerySignupResponse.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/23 11:37:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class QuerySignupResponse extends ChannelResponse {

  private String signupState;
  private String signupResult;

}
