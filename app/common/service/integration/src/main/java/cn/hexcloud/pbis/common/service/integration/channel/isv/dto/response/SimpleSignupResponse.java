package cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName SimpleSignupResponse.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/24 20:29:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class SimpleSignupResponse extends ChannelResponse {

  private String merchantId;
  private String appId;
  private String signupBatchNo;
  private String signupNo;
  private Boolean isInstantResult;

}
