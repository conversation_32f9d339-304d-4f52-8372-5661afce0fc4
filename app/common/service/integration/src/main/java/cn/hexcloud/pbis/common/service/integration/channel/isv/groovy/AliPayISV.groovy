package cn.hexcloud.pbis.common.service.integration.channel.isv.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.FileUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.isv.provider.ISVAuthModule
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSONObject
import com.alipay.api.AlipayClient
import com.alipay.api.DefaultAlipayClient
import com.alipay.api.FileItem
import com.alipay.api.domain.SignAddressInfo
import com.alipay.api.internal.util.AlipaySignature
import com.alipay.api.request.AlipayOpenAgentConfirmRequest
import com.alipay.api.request.AlipayOpenAgentCreateRequest
import com.alipay.api.request.AlipayOpenAgentFacetofaceSignRequest
import com.alipay.api.request.AlipayOpenAgentOrderQueryRequest
import com.alipay.api.response.AlipayOpenAgentConfirmResponse
import com.alipay.api.response.AlipayOpenAgentCreateResponse
import com.alipay.api.response.AlipayOpenAgentFacetofaceSignResponse
import com.alipay.api.response.AlipayOpenAgentOrderQueryResponse
import org.apache.commons.lang3.StringUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets

class AliPayISV extends AbstractExternalChannelModule implements ISVAuthModule {

  private static final String FORMAT = "JSON"
  private static final String SIGN_TYPE = "RSA2"

  AliPayISV(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  @Override
  CreateSignupResponse createSignup(CreateSignupRequest request) {
    // 请求参数
    JSONObject body = new JSONObject()
    body.put("account", request.getMerchantId())
    JSONObject contactBody = new JSONObject()
    contactBody.put("contact_name", request.getContactName())
    contactBody.put("contact_mobile", request.getContactMobile())
    contactBody.put("contact_email", request.getContactEmail())
    body.put("contact_info", contactBody)
    String bodyJSON = body.toJSONString()

    // 发起请求
    String methodFullName = getMethodFullName("createSignup")
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
    AlipayClient alipayClient = getAlipayClient()
    AlipayOpenAgentCreateRequest alipayRequest = new AlipayOpenAgentCreateRequest()
    alipayRequest.setBizContent(bodyJSON)
    AlipayOpenAgentCreateResponse alipayResponse = alipayClient.execute(alipayRequest)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, JSONObject.toJSONString(alipayResponse))

    // 解析并返回结果
    CreateSignupResponse response = new CreateSignupResponse()
    if (alipayResponse.isSuccess()) {
      response.setSignupBatchNo(alipayResponse.getBatchNo())
    } else {
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null,
          methodFullName, alipayResponse.getSubCode(), alipayResponse.getSubMsg())
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, alipayResponse.getSubCode() + "-" + alipayResponse.getSubMsg())
    }

    return response
  }

  @Override
  SignupResponse signup(SignupRequest request) {
    // 请求参数
    AlipayOpenAgentFacetofaceSignRequest alipayRequest = new AlipayOpenAgentFacetofaceSignRequest()
    alipayRequest.setBatchNo(request.getSignupBatchNo())
    alipayRequest.setMccCode(request.getBusinessCategory()) // 经营类目
    alipayRequest.setSignAndAuth(true) // 签约且授权
    alipayRequest.setRate(null == request.getServiceRate() || request.getServiceRate() <= 0 ? "0.6" : request.getServiceRate() as String)
    if (null != request.getAttachments() && request.getAttachments().size() > 0) {
      String businessLicensePicName = "BusinessLicensePic"
      String shopSignBoardPicName = "ShopSignBoardPic"
      String shopScenePicName = "ShopScenePic"
      if (null != request.getAttachments().get(businessLicensePicName)) {
        // 营业执照照片
        alipayRequest.setBusinessLicensePic(getAlipayFileItem(request.getAttachments().get(businessLicensePicName)))
      }
      if (null != request.getAttachments().get(shopSignBoardPicName)) {
        // 店铺门头照片
        alipayRequest.setShopSignBoardPic(getAlipayFileItem(request.getAttachments().get(shopSignBoardPicName)))
      }
      if (null != request.getAttachments().get(shopScenePicName)) {
        // 店铺内景照片
        alipayRequest.setShopScenePic(getAlipayFileItem(request.getAttachments().get(shopScenePicName)))
      }
    }
    SignAddressInfo signAddressInfo = new SignAddressInfo() // 签约主体所在地信息
    signAddressInfo.setCountryCode(request.getCountryCode())
    signAddressInfo.setProvinceCode(request.getProvinceCode())
    signAddressInfo.setCityCode(request.getCityCode())
    signAddressInfo.setDistrictCode(request.getDistrictCode())
    signAddressInfo.setDetailAddress(request.getAddress())
    alipayRequest.setShopAddress(signAddressInfo)
    String bodyJSON = JSONObject.toJSONString(alipayRequest)

    // 发起请求
    String methodFullName = getMethodFullName("signup")
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
    AlipayClient alipayClient = getAlipayClient()
    AlipayOpenAgentFacetofaceSignResponse alipayResponse = alipayClient.execute(alipayRequest)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, JSONObject.toJSONString(alipayResponse))

    // 解析并返回结果
    SignupResponse response = new SignupResponse()
    if (!alipayResponse.isSuccess()) {
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null,
          methodFullName, alipayResponse.getSubCode(), alipayResponse.getSubMsg())
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, alipayResponse.getSubCode() + "-" + alipayResponse.getSubMsg())
    }

    return response
  }

  @Override
  ConfirmSignupResponse confirmSignup(ConfirmSignupRequest request) {
    // 请求参数
    JSONObject body = new JSONObject()
    body.put("batch_no", request.getSignupBatchNo())
    String bodyJSON = body.toJSONString()

    // 发起请求
    String methodFullName = getMethodFullName("confirmSignup")
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
    AlipayClient alipayClient = getAlipayClient()
    AlipayOpenAgentConfirmRequest alipayRequest = new AlipayOpenAgentConfirmRequest()
    alipayRequest.setBizContent(bodyJSON)
    AlipayOpenAgentConfirmResponse alipayResponse = alipayClient.execute(alipayRequest)
    String alipayResponseJSON = JSONObject.toJSONString(alipayResponse)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, alipayResponseJSON)

    // 解析并返回结果
    ConfirmSignupResponse response = new ConfirmSignupResponse()
    if (alipayResponse.isSuccess()) {
      response.setSignupNo(JSONObject.parseObject(alipayResponseJSON).getJSONObject("body")
          .getJSONObject("alipay_open_agent_confirm_response").getString("order_no"))
    } else {
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null,
          methodFullName, alipayResponse.getSubCode(), alipayResponse.getSubMsg())
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, alipayResponse.getSubCode() + "-" + alipayResponse.getSubMsg())
    }

    return response
  }

  @Override
  SimpleSignupResponse simpleSignup(SimpleSignupRequest request) {
    // createSignup
    CreateSignupRequest createSignupRequest = new CreateSignupRequest()
    createSignupRequest.setMerchantId(request.getMerchantId())
    createSignupRequest.setContactName(request.getContactName())
    createSignupRequest.setContactMobile(request.getContactMobile())
    createSignupRequest.setContactEmail(request.getContactEmail())
    CreateSignupResponse createSignupResponse = createSignup(createSignupRequest)

    // signup
    SignupRequest signupRequest = new SignupRequest()
    signupRequest.setSignupBatchNo(createSignupResponse.getSignupBatchNo())
    signupRequest.setServiceRate(request.getServiceRate())
    signupRequest.setPartyName(request.getPartyName())
    signupRequest.setBusinessCategory(request.getBusinessCategory())
    signupRequest.setCountryCode(request.getCountryCode())
    signupRequest.setProvinceCode(request.getProvinceCode())
    signupRequest.setCityCode(request.getCityCode())
    signupRequest.setDistrictCode(request.getDistrictCode())
    signupRequest.setAddress(request.getAddress())
    signupRequest.setAttachments(request.getAttachments())
    signup(signupRequest)

    // confirmSignup
    ConfirmSignupRequest confirmSignupRequest = new ConfirmSignupRequest()
    confirmSignupRequest.setSignupBatchNo(createSignupResponse.getSignupBatchNo())
    ConfirmSignupResponse confirmSignupResponse = confirmSignup(confirmSignupRequest)

    // 解析并返回结果
    SimpleSignupResponse response = new SimpleSignupResponse()
    response.setSignupBatchNo(createSignupResponse.getSignupBatchNo())
    response.setSignupNo(confirmSignupResponse.getSignupNo())
    response.setMerchantId(confirmSignupResponse.getMerchantId())
    response.setAppId(confirmSignupResponse.getAppId())
    response.setIsInstantResult(false) // 不能取得实时签约结果，需后续通过查询接口更新签约状态

    return response
  }

  @Override
  QuerySignupResponse querySignup(QuerySignupRequest request) {
    // 请求参数
    JSONObject body = new JSONObject()
    body.put("batch_no", request.getSignupBatchNo())
    String bodyJSON = body.toJSONString()

    // 发起请求
    String methodFullName = getMethodFullName("querySignup")
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
    AlipayClient alipayClient = getAlipayClient()
    AlipayOpenAgentOrderQueryRequest alipayRequest = new AlipayOpenAgentOrderQueryRequest()
    alipayRequest.setBizContent(bodyJSON)
    AlipayOpenAgentOrderQueryResponse alipayResponse = alipayClient.execute(alipayRequest)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, JSONObject.toJSONString(alipayResponse))

    // 解析并返回结果
    QuerySignupResponse response = new QuerySignupResponse()
    if (alipayResponse.isSuccess()) {
      response.setSignupState(mapSignupState(alipayResponse.getOrderStatus()).getCode())
      response.setSignupResult(alipayResponse.getRejectReason())
    } else {
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null,
          methodFullName, alipayResponse.getSubCode(), alipayResponse.getSubMsg())
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, alipayResponse.getSubCode() + "-" + alipayResponse.getSubMsg())
    }

    return response
  }

  @Override
  GrantCallbackResponse grantNotify(HttpServletRequest request) {
    // 解析参数并对请求进行验签
    String callbackStr = HttpUtil.readBody2String(request)
    LoggerUtil.info("{0} received message: {1}.", getMethodFullName("grantNotify"), callbackStr)
    Map<String, String> unverifiedMessage = getUrlParamsMap(callbackStr)
    if (!verifySignature(unverifiedMessage)) {
      // 验签失败
      throw new CommonException(ServiceError.INVALID_SIGNATURE)
    }

    JSONObject bizContentJSON = JSONObject.parseObject(unverifiedMessage.get("biz_content"))

    // 返回结果
    GrantCallbackResponse response = new GrantCallbackResponse()
    JSONObject bizContentDetailNode = bizContentJSON.getJSONObject("detail")
    response.setMerchantId(bizContentDetailNode.getString("user_id"))
    response.setGrantTargetId(bizContentDetailNode.getString("auth_app_id"))
    response.setAuthToken(bizContentDetailNode.getString("app_auth_token"))
    response.setRefreshToken(bizContentDetailNode.getString("app_refresh_token"))
    response.setResponse2ThirdParty("")

    return response
  }

  private static SignupState mapSignupState(String alipaySignupState) {
    SignupState signupState
    switch (alipaySignupState) {
      case "MERCHANT_INFO_HOLD":
        signupState = SignupState.WAITING
        break
      case "MERCHANT_AUDITING":
        signupState = SignupState.UNDER_REVIEW
        break
      case "MERCHANT_CONFIRM":
        signupState = SignupState.UNCONFIRMED
        break
      case "MERCHANT_CONFIRM_SUCCESS":
        signupState = SignupState.COMPLETE
        break
      case "MERCHANT_CONFIRM_TIME_OUT":
      case "MERCHANT_APPLY_ORDER_CANCELED":
        signupState = SignupState.FAILURE
        break
      default:
        signupState = SignupState.UNKNOWN
    }
    return signupState
  }

  private AlipayClient getAlipayClient() {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    return new DefaultAlipayClient(channelAccessConfig.getProperty("gateway_url"),
        channelAccessConfig.getAppId(), channelAccessConfig.getPrivateKey(), FORMAT,
        StandardCharsets.UTF_8.toString(), channelAccessConfig.getThirdPartyPublicKey(), SIGN_TYPE)
  }

  private static FileItem getAlipayFileItem(SignupAttachment attachment) {
    FileItem fileItem = null
    if (null != attachment.getContent() && attachment.getContent().size() > 0) {
      fileItem = new FileItem(attachment.getFileName(), attachment.getContent())
    } else {
      if (StringUtils.isNotBlank(attachment.getUrl())) {
        // 下载文件
        byte[] content = FileUtil.readWebFile2Bytes(attachment.getUrl())
        String fileName
        if (StringUtils.isBlank(attachment.getFileName())) {
          URL fileUrl = new URL(attachment.getUrl())
          String[] pathItems = fileUrl.getPath().split("/")
          fileName = pathItems[pathItems.length - 1]
        } else {
          fileName = attachment.getFileName()
        }
        fileItem = new FileItem(fileName, content)
      }
    }
    return fileItem
  }


  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }

  private boolean verifySignature(Map<String, String> unverifiedMessage) {
    // 解析请求，获取待验签所需的数据
    String charset = unverifiedMessage.get("charset")
    String signType = unverifiedMessage.get("sign_type")
    if (StringUtils.isAnyBlank(charset, signType)) {
      return false
    }

    return AlipaySignature.verifyV1(unverifiedMessage, channel.getChannelAccessConfig().getThirdPartyPublicKey(), charset, signType)
  }

  private static Map<String, String> getUrlParamsMap(String urlParams) {
    Map<String, String> paramsMap = new HashMap<>()
    if (StringUtils.isEmpty(urlParams)) {
      return paramsMap
    }

    String[] urlParamArr = urlParams.split("&")
    for (String urlParam : urlParamArr) {
      String[] paramPair = urlParam.split("=")
      paramsMap.put(paramPair[0], URLDecoder.decode(paramPair[1], StandardCharsets.UTF_8.toString()))
    }

    return paramsMap
  }

}
