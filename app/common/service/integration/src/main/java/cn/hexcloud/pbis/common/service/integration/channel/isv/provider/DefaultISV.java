package cn.hexcloud.pbis.common.service.integration.channel.isv.provider;

import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannel;
import cn.hexcloud.pbis.common.service.integration.channel.ChannelAccessSupportService;
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

/**
 * @ClassName DefaultISV.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/25 18:14:31
 */
@Service
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
public class DefaultISV extends AbstractExternalChannel implements ISVChannel {

  @Override
  public String getChannelCode() {
    return channelAccessConfig.getChannelCode();
  }

  @Override
  public String getChannelName() {
    return channelAccessConfig.getChannelCode();
  }

  @Override
  public void init(ChannelAccessConfig channelAccessConfig) {
    this.channelAccessConfig = channelAccessConfig;
  }

  @Override
  public ChannelAccessConfig getChannelAccessConfig() {
    return channelAccessConfig;
  }

  @Override
  public ChannelAccessSupportService getChannelAccessSupportService() {
    return channelAccessSupportService;
  }

  @Override
  public ISVAuthModule getAuthorizationModule() {
    return new DefaultISVAuthModule(this);
  }
}
