package cn.hexcloud.pbis.common.service.integration.channel.isv.provider;

import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule;
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request.ConfirmSignupRequest;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request.CreateSignupRequest;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request.QuerySignupRequest;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request.SignupRequest;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request.SimpleSignupRequest;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response.ConfirmSignupResponse;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response.CreateSignupResponse;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response.GrantCallbackResponse;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response.QuerySignupResponse;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response.SignupResponse;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response.SimpleSignupResponse;
import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName DefaultISVAuthModule.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2022/01/15 12:35:15
 */
public class DefaultISVAuthModule extends AbstractExternalChannelModule implements ISVAuthModule {

  public DefaultISVAuthModule(ExternalChannel channel) {
    super(channel);
  }

  @Override
  protected String getSignModuleName() {
    return channel.getChannelName();
  }

  @Override
  public CreateSignupResponse createSignup(CreateSignupRequest request) {
    return (CreateSignupResponse) channel.doRequest(this.getModuleName(), "createSignup", request);
  }

  @Override
  public SignupResponse signup(SignupRequest request) {
    return (SignupResponse) channel.doRequest(this.getModuleName(), "signup", request);
  }

  @Override
  public ConfirmSignupResponse confirmSignup(ConfirmSignupRequest request) {
    return (ConfirmSignupResponse) channel.doRequest(this.getModuleName(), "confirmSignup", request);
  }

  @Override
  public SimpleSignupResponse simpleSignup(SimpleSignupRequest request) {
    return (SimpleSignupResponse) channel.doRequest(this.getModuleName(), "simpleSignup", request);
  }

  @Override
  public QuerySignupResponse querySignup(QuerySignupRequest request) {
    return (QuerySignupResponse) channel.doRequest(this.getModuleName(), "querySignup", request);
  }

  @Override
  public GrantCallbackResponse grantNotify(HttpServletRequest request) {
    return (GrantCallbackResponse) channel.doRequest(this.getModuleName(), "grantNotify", request);
  }
}
