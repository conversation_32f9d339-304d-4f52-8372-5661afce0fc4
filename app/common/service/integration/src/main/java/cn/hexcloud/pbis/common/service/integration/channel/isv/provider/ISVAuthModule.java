package cn.hexcloud.pbis.common.service.integration.channel.isv.provider;

import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannelModule;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request.ConfirmSignupRequest;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request.CreateSignupRequest;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request.QuerySignupRequest;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request.SignupRequest;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.request.SimpleSignupRequest;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response.ConfirmSignupResponse;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response.CreateSignupResponse;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response.GrantCallbackResponse;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response.QuerySignupResponse;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response.SignupResponse;
import cn.hexcloud.pbis.common.service.integration.channel.isv.dto.response.SimpleSignupResponse;
import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName ISVAuthModule.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2022/01/09 15:35:37
 */
public interface ISVAuthModule extends ExternalChannelModule {

  CreateSignupResponse createSignup(CreateSignupRequest request);

  SignupResponse signup(SignupRequest request);

  ConfirmSignupResponse confirmSignup(ConfirmSignupRequest request);

  SimpleSignupResponse simpleSignup(SimpleSignupRequest request);

  QuerySignupResponse querySignup(QuerySignupRequest request);

  GrantCallbackResponse grantNotify(HttpServletRequest request);

  @Override
  default String getModuleName() {
    return "Auth";
  }

}
