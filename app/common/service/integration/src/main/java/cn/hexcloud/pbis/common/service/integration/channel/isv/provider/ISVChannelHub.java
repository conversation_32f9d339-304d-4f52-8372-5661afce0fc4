package cn.hexcloud.pbis.common.service.integration.channel.isv.provider;

import cn.hexcloud.commons.utils.SpringContextUtil;
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannelHub;
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName ISVChannelHub.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/24 17:12:05
 */
@Component
public class ISVChannelHub implements ExternalChannelHub<ISVChannel> {

  @Autowired
  private Map<String, ISVChannel> isvChannelMap;

  @Override
  public ISVChannel on(String channelCode) {
    ISVChannel isvChannel = isvChannelMap.get(channelCode);
    if (null == isvChannel) {
      isvChannel = SpringContextUtil.bean(DefaultISV.class);
    }

    return isvChannel;
  }

  @Override
  public ISVChannel on(String channelCode, ChannelAccessConfig channelAccessConfig) {
    ISVChannel isvChannel = this.on(channelCode);
    isvChannel.init(channelAccessConfig);
    return isvChannel;
  }

}
