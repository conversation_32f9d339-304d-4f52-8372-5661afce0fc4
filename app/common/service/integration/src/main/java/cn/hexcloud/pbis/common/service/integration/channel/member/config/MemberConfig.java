package cn.hexcloud.pbis.common.service.integration.channel.member.config;

import java.security.cert.X509Certificate;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * @Classname MemberConfig
 * @Description:
 * @Date 2021/11/52:10 下午
 * <AUTHOR>
 */
@Component
@Data
public class MemberConfig {

  private String requestUrl;
  private String channel;
  private String merchantId;
  private String appId;
  private String appKey;
  private String accessKey;
  private String certText;
  private String privateKey;
  private long partnerId;
  private String thirdPartyPublicKey;
  private X509Certificate cert;
}
