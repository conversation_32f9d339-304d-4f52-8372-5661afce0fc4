package cn.hexcloud.pbis.common.service.integration.channel.member.config;

import java.util.HashMap;
import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @Classname MemberConfiguration
 * @Description:
 * @Date 2021/11/52:06 下午
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "pbis.channel")
@Component
@Data
public class MemberConfiguration {


  private HashMap<String, List<MemberConfig>> member;

}
