package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import lombok.Data;

import java.util.List;

/**
 * @program: pbis
 * @author: miao
 * @create: 2022-01-15 12:25
 **/
@Data
public class AbsorbedExpenses {

  // 商品id
  private String key_id;
  // 换购商品优惠方式（AMOUNT:金额折扣，PERCENT:百分比折扣，PRICE:固定价格）
  private String method;
  // 商品价格
  private String price;
  // 商品数量
  private Integer qty;
  // 折扣后总价
  private String amt;
  // 换购商品折扣（根据优惠方式，数值代表的意义不同，AMOUNT:折扣金额，PERCENT:折扣百分比，PRICE:固定价格)
  private String discount;
  // 商品折扣金额
  private String priceDiscount;
  // 分摊信息
  private List<ChargeDetail> chargeInfo;
  private String shopId;
}
