package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Classname Amount
 * @Description:
 * @Date 2021/10/296:50 下午
 * <AUTHOR>
 */
@Data
public class Amount {

  /**
   * 税额
   */
  @JSONField(name = "gross_amount")
  private BigDecimal taxAmount;

  /**
   * 售价毛额
   */
  @JSONField(name = "gross_amount")
  private BigDecimal grossAmount;

  /**
   * 支付后计算过优惠结果，gross_amount - discount_amount
   */
  @JSONField(name = "net_amount")
  private BigDecimal netAmount;

  /**
   * 支付金额
   */
  @JSONField(name = "pay_amount")
  private BigDecimal payAmount;

  /**
   * 优惠金额
   */
  @JSONField(name = "discount_amount")
  private BigDecimal discountAmount;

  /**
   * 抹零
   */
  private BigDecimal rounding;

  /**
   * 溢收
   */
  @JSONField(name = "overflow_amount")
  private BigDecimal overflowAmount;

  /**
   * 找零金额
   */
  private BigDecimal changeAmount;

  /**
   * 服务费
   */
  private BigDecimal serviceFee;

  /**
   * 小费
   */
  private BigDecimal tip;

  /**
   * 商家收入
   */
  @JSONField(name = "projected_income")
  private BigDecimal projectedIncome;

  /**
   * 商家承担活动金额
   */
  @JSONField(name = "merchant_discount_amount")
  private BigDecimal merchantDiscountAmount;

  /**
   * 平台承担活动金额
   */
  @JSONField(name = "platform_discount_amount")
  private BigDecimal platformDiscountAmount;

  /**
   * 其他费用
   */
  private BigDecimal otherFee;

  /**
   * 价税合一
   */
  private boolean taxIncluded;

  private BigDecimal commission;
  private BigDecimal amount0;
  private BigDecimal amount1;
  private BigDecimal amount2;
  private BigDecimal amount3;
  private BigDecimal amount4;


  private BigDecimal receivable;
  private BigDecimal realAmount;
  private BigDecimal businessAmount;
  private BigDecimal expendAmount;
  private BigDecimal paymentTransferAmount;
  private BigDecimal discountTransferAmount;
  private BigDecimal storeDiscountAmount;
  private BigDecimal discountMerchantContribute;
  private BigDecimal discountPlatformContribute;
  private BigDecimal discountBuyerContribute;
  private BigDecimal discountOtherContribute;
  private BigDecimal payMerchantContribute;
  private BigDecimal payPlatformContribute;
  private BigDecimal payBuyerContribute;
  private BigDecimal payOtherContribute;
  private BigDecimal deliveryFee;
  private BigDecimal deliveryFeeForPlatform;
  private BigDecimal deliveryFeeForMerchant;

}
