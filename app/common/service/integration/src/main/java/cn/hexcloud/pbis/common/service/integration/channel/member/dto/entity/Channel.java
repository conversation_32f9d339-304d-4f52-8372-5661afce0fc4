package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import lombok.Data;

/**
 * @Classname Channel
 * @Description:
 * @Date 2021/10/296:52 下午
 * <AUTHOR>
 */
@Data
public class Channel {

  /**
   * 区分是POS系统产生或被推送进入 INVALID: 无效编码 POS: POS系统 PUSH: 推送进入
   */
  private String source;

  /**
   * POS系统内部的设备类型，比如pad或者kiosk INVALID: 无效编码 PC: POS系统 PAD: pad KIOSK: kiosk(自助点单)
   */
  private String deviceType;

  /**
   * 订单类型 INVALID: 无效编码 DINEIN: 堂食 TAKEAWAY: 外带 TAKEOUT: 外卖 SELFHELP: 自提
   */
  private String orderType;

  /**
   * 配送类型 INVALID: 无效编码 REALTIME: 实时单 BOOKING: 预约单
   */
  private String deliveryType;

  /**
   * 第三方平台 INVALID: 无效编码 wechat: 微信小程序 meituan: 美团 eleme: 饿了么 koubei: 口碑 alipay: 支付宝小程序
   */
  private String tpName;

  /**
   *
   */
  private String code;

  private String id;
  private String mappingCode;

}
