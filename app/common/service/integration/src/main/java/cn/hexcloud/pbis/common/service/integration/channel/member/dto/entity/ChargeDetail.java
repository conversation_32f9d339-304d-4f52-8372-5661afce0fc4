package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import lombok.Data;

/**
 * @program: pbis
 * @author: miao
 * @create: 2022-04-14 23:10
 **/
@Data
public class ChargeDetail {

  //商品id
  private String keyId;
  //商品价格
  private String price;
  //商品数量
  private Integer qty;
  //折扣后总价
  private String amt;
  //商品折扣金额
  private String priceDiscount;
  //参与优惠的数量
  private Integer discountGoodsNum;
  //回传字段无业务逻辑
  private String shopId;
}
