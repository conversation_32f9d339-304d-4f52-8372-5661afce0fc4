package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import lombok.Data;
import lombok.ToString;

/**
 * @Classname Coupon
 * @Description:
 * @Date 2021/10/262:49 下午
 * <AUTHOR>
 */
@Data
@ToString
public class Coupon {

  /**
   * 卡券id
   */
  private String id;

  private String codeNo;

  /**
   * 卡券编码
   */
  private String code;

  /**
   * 卡券名称
   */
  private String name;

  /**
   * 卡券类别（内部类别）
   */
  private String type;

  /**
   * 卡券类别（外部类别）
   */
  private String typeCode;

  /**
   * 会员券(MEMBERSHIP)或非会员券(NORMAL)
   */
  private String isMember;

  /**
   * 有效期起始时间
   */
  private String startDate;

  private Double parValue;

  private String refAmount;

  /**
   * 有效期结束时间
   */
  private String expiredDate;

  /**
   * 券状态：0(可用), 1(不可用), 2(锁定)
   */
  private int status;

  /**
   * 多次券(MANY)/单次券(ONCE)
   */
  private String useType;

  /**
   * 优惠券图片
   */
  private String couponImgUrl;

  private Integer couponTypeId;

  /**
   * 是否是码商券 0:是
   */
  private int isMerchant;

  /**
   * 优惠券来源  0: 未知 1-后台 2-兑换码兑换 3-券架领取 4-积分商城 5-礼品卡小程序 6-CRM活动 7-签到奖励 8-抽奖 9-marketing活动平台 10-客服补偿 11-咨库 12-分销小程序 21-企微 22-天猫码商
   */
  private long couponChannelType;

  /**
   * 促销id
   */
  private String promotionId;

  /**
   * 用户为这个卡券花费了多少钱（单位分）
   */
  private int payPrice;

  /**
   * 这个卡券售价是多少（单位分）
   */
  private int sellPrice;

  /**
   * 原价是多少（单位分）
   */
  private int originPrice;

  /**
   * 当天剩余可用次数
   */
  private int todayLimit;

  /**
   * 总可剩余次数
   */
  private int sumLimit;

  /**
   * 已使用次数
   */
  private int usedQty;

  /**
   * 促销券规则
   */
  private Rule rule;

  /**
   * 商品
   */
  private SkuDetail skuDetail;

  /**
   * 券核销流水号（核销后返回）
   */
  private String consumeTransactionId;

  /**
   * 券金额信息
   */
  private CouponAmount couponAmount;

}
