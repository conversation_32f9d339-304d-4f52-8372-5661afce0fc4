package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import lombok.Data;
import lombok.ToString;

/**
 * @ClassName CouponAmount.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2022/03/14 14:20:40
 */
@Data
@ToString
public class CouponAmount {

  private Integer parValue; // 券面值，例如：50元券，单位：分
  private Integer price; // 券售价，例如：10元，单位：分
  private Integer payAmount; // 用户实际买券所花费的金额，例如：6元，单位：分
  private Integer discountOnMerchant; // 商家承担的券售价折扣金额，例如：3元，单位：分
  private Integer discountOnPlatform; // 平台（第三方券商）承担的券售价折扣金额，例如：1元，单位：分
  private Integer discountOnOthers; // 其他第三方承担的券售价折扣金额，例如：0元，单位：分

}
