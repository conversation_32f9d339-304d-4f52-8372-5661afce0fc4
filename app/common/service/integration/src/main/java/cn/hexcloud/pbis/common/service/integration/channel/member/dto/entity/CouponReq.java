package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * @Classname CouponReq
 * @Description:
 * @Date 2021/10/285:08 下午
 * <AUTHOR>
 */
@Data
@ToString
public class CouponReq {

  /**
   * 券id
   */
  private String id;

  /**
   * 券号
   */
  private String codeNo;

  /**
   * 券密码
   */
  private String passwordCode;

  /**
   * 核销次数，对应多次券，验证是否可以核销的次数
   */
  private int qty;

  /**
   * 卡券类型
   */
  private String typeId;

  /**
   * 卡券类型
   */
  private String extend;

  /**
   * 商品列表
   */
  private List<Product> products;

  /**
   * 券核销流水号（核销后返回）
   */
  private String consumeTransactionId;
}
