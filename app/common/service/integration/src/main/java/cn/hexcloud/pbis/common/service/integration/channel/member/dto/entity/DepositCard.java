package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import lombok.Data;
import lombok.ToString;

/**
 * @Classname DepositCard
 * @Description:
 * @Date 2021/10/262:49 下午
 * <AUTHOR>
 */
@Data
@ToString
public class DepositCard {

  /**
   * 实充余额，单位分
   */
  private int amount;

  /**
   * 卡规格ID
   */
  private String applyId;

  /**
   * 卡号
   */
  private String cardCode;

  /**
   * 卡名称
   */
  private String cardName;

  /**
   * 赠送余额，单位分
   */
  private int vamount;

  /**
   * 是否有过期时间
   */
  private boolean hasExpireTIme;

  /**
   * 生效时间
   */
  private long validStartTime;

  /**
   * 过期时间
   */
  private long expireTime;
}
