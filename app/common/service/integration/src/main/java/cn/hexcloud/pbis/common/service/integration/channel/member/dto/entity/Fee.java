package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * @Classname Fee
 * @Description:
 * @Date 2021/10/297:04 下午
 * <AUTHOR>
 */
@Data
public class Fee {

  private String name;
  private String type;
  private int qty;
  private BigDecimal price;
  private BigDecimal amount;
  private List<Fee> detailFees;

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public int getQty() {
    return qty;
  }

  public void setQty(int qty) {
    this.qty = qty;
  }

  public BigDecimal getPrice() {
    return price;
  }

  public void setPrice(BigDecimal price) {
    this.price = price;
  }

  public BigDecimal getAmount() {
    return amount;
  }

  public void setAmount(BigDecimal amount) {
    this.amount = amount;
  }

  public List<Fee> getDetailFees() {
    return detailFees;
  }

  public void setDetailFees(List<Fee> detailFees) {
    this.detailFees = detailFees;
  }
}
