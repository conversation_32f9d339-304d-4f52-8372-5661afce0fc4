package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import java.util.List;
import lombok.Data;

/**
 * @program: pbis
 * @author: miao
 * @create: 2022-01-15 11:54
 **/
@Data
public class GoodsDetail {

  // 商品单价
  private String price;
  // 商品总价（不含加料）
  private String amt;
  //加料总价
  private String accAmt;
  //商品数量
  private Integer qty;
  //商品id
  private String keyId;
  //商品分类id数组
  private List<String> categories;
  //加料商品
  private List<ChargeGoods> chargeGoods;
  //门店id
  private String shopId;
}
