package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.ToString;

/**
 * @Classname Member
 * @Description:
 * @Date 2021/10/286:01 下午
 * <AUTHOR>
 */
@Data
@ToString
public class Member {

  /**
   * 会员卡号
   */
  private String cardNo;

  /**
   * 会员编码
   */
  @JSONField(name = "member_code")
  private String memberCode;

  /**
   * 手机号
   */
  private String mobile;

  /**
   * 敏感信息 密码、辅助码、二磁道信息等
   */
  private String secretContent;

  /**
   * 会员登记编码
   */
  private String gradeCode;

  /**
   * 会员等级名称
   */
  private String gradeName;


  /**
   * 会员名称
   */
  private String name;

  private Long balancePoints;
  private Long totalPoints;
  private Long orderPoints;
}
