package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @Classname Operator
 * @Description:
 * @Date 2021/10/296:50 下午
 * <AUTHOR>
 */
@Data
public class Operator {

  /**
   * 收银员
   */
  @JSONField(defaultValue = "")
  private String id;

  /**
   * 收银员姓名
   */
  @JSONField(defaultValue = "")
  private String name;

  /**
   * 收银员code
   */
  @JSONField(defaultValue = "")
  private String code;

  /**
   * 登陆时间
   */
  @JSONField(name = "login_time",defaultValue = "")
  private String loginTime;

  /**
   * 登陆id
   */
  @JSONField(defaultValue = "")
  private String loginId;


}
