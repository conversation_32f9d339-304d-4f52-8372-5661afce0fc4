package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * @Classname Order
 * @Description:
 * @Date 2021/10/287:42 下午
 * <AUTHOR>
 */
@Data
@ToString
public class Order {

  /**
   * 订单交易id
   */
  private String orderTicketId;

  /**
   * pos机id
   */
  private String posId;

  /**
   * pos编码
   */
  private String posCode;

  /**
   * 卓位号
   */
  private String tableNo;

  /**
   * 交易时间
   */
  private String salesTime;

  /**
   * 营业日期
   */
  private String salesDate;

  /**
   * 毛额
   */
  private int grossAmount;

  /**
   * 净额
   */
  private int netAmount;

  /**
   * 商品信息
   */
  private List<Product> products;

}
