package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Classname Payment
 * @Description:
 * @Date 2021/10/296:57 下午
 * <AUTHOR>
 */
@Data
public class Payment {

  /**
   * 支付id
   */
  private String id;

  /**
   * 支付顺序号
   */
  @JSONField(name = "seq_id")
  private String seqId;

  /**
   * 找零
   */
  private BigDecimal change;



  /**
   * 溢收
   */
  private BigDecimal overflow;

  /**
   * 甩尾/抹零
   */
  private BigDecimal rounding;

  /**
   * 支付时间
   */
  @JSONField(name = "pay_time")
  private String payTime;

  /**
   * 支付方式编码
   */
  @JSONField(name = "trans_code")
  private String transCode;

  /**
   * 支付方式名称
   */
  private String name;

  /**
   * 实收金额
   */
  private BigDecimal receivable;

  /**
   * 第三方返回id
   */
  private String tpTransactionNo;

  /**
   * 第三方补贴金额
   */
  @JSONField(name = "tp_allowance")
  private BigDecimal tpAllowance;

  /**
   * 商家补贴金额
   */
  @JSONField(name = "merchant_allowance")
  private BigDecimal merchantAllowance;

  /**
   * 卡券支付名称
   */
  @JSONField(name = "trans_name")
  private String transName;

  /**
   * 售价
   */
  private BigDecimal price;

  /**
   * 用户实际购买金额
   */
  private BigDecimal cost;

  /**
   * 商家实收
   */
  @JSONField(name = "real_amount")
  private BigDecimal realAmount;


   /**
   * 商家实收
   */
  private BigDecimal realPayAmount;


  /**
   * 支付金额
   */
  @JSONField(name = "pay_amount")
  private BigDecimal payAmount;


  /**
   * /支付转折扣金额
   */
  @JSONField(name = "transfer_amount")
  private BigDecimal transferAmount;


  /**
   * /是否已开发票
   */
  @JSONField(name = "has_invoiced")
  private boolean hasInvoiced;

  private BigDecimal platformAllowance;

  private String payerNo;

  private String realChannel;

}
