package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @Classname Pos
 * @Description:
 * @Date 2021/10/296:49 下午
 * <AUTHOR>
 */
@Data
public class Pos {

  /**
   * pos id
   */

  private String id;

  /**
   * pos 编码
   */

  private String code;

  /**
   * pos 名称
   */
  @JSONField(name = "pos_name")
  private String posName;

  /**
   * pos的设备id
   */
  @JSONField(name = "device_id")
  private String deviceId;

  /**
   * pos设备编码
   */
  @JSONField(name = "device_code")
  private String deviceCode;

}
