package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import java.util.List;
import lombok.Data;

/**
 * @program: pbis
 * @author: miao
 * @create: 2022-01-15 12:25
 **/
@Data
public class PriceDiscount {

  // 折扣类型名称
  private String type;
  // 折扣类型
  private String discountType;
  // 折扣金额
  private String discount;
  // 促销名称
  private String name;
  // 促销id
  private String promotionId;
  // 促销编号
  private String promotionCode;
  // 促销类型（NORMAL:普通促销，MEMBER:会员促销，COUPON:卡券促销）
  private String promotionType;
  // 是否允许折上折
  private Boolean allowOverlap;
  // 显示在小票上的促销名称
  private String ticketDisplay;
  // 是否允许多次
  private Boolean triggerTimesCustom;
  // 参与折扣的商品
  private List<String> fired;
  // 分摊信息
  private List<AbsorbedExpenses> product;
  // 优惠券模版ID
  private String promotionTemplateId;
}
