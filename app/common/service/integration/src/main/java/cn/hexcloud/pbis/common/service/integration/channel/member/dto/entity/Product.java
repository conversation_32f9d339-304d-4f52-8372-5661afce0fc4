package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * @Classname Product
 * @Description:
 * @Date 2021/10/285:10 下午
 * <AUTHOR>
 */
@Data
@ToString
public class Product {

  /**
   * 商品id
   */
  private String id;
  /**
   * 商品名称
   */
  private String name;

  /**
   * 商品分类
   */
  private String category;

  /**
   * 商品编码
   */
  private String code;

  /**
   * 商品编码
   */
  private int quantity;

  /**
   * 价格
   */
  private int price;


  /**
   * 加料
   */
  private List<Product> accessories;

  private String shopId;

}
