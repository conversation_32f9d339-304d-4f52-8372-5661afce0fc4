package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Classname PromotionInfo
 * @Description:
 * @Date 2021/10/296:58 下午
 * <AUTHOR>
 */
@Data
public class PromotionInfo {

  private String type;
  @JSONField(name = "discount_type")
  private String discountType;
  private String name;
  @JSONField(name = "promotion_id")
  private String promotionId;
  @JSONField(name = "promotion_code")
  private String promotionCode;
  @JSONField(name = "promotion_type")
  private String promotionType;
  @JSONField(name = "ticket_display")
  private String ticketDisplay;
  @JSONField(name = "allow_overlap")
  private boolean allowOverlap;
  @JSONField(name = "trigger_times_custom")
  private boolean triggerTimesCustom;
  @J<PERSON><PERSON>ield(name = "max_discount")
  private BigDecimal maxDiscount;
  @<PERSON><PERSON>NField(name = "shop_id")
  private String shopId;
}
