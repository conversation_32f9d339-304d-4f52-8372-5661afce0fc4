package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * @Classname PromotionProduct
 * @Description:
 * @Date 2021/10/296:59 下午
 * <AUTHOR>
 */
@Data
public class PromotionProduct {

  private BigDecimal price;
  private BigDecimal amt;
  private BigDecimal accAmt;
  private int qty;
  @JSONField(name = "key_id")
  private String keyId;
  private List<String> accies;
  private String type;
  private String method;
  private BigDecimal discount;
  @JSONField(name = "free_amt")
  private BigDecimal freeAmt;
}
