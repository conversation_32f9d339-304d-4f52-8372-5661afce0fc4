package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * @Classname PromotionSource
 * @Description:
 * @Date 2021/10/296:59 下午
 * <AUTHOR>
 */
@Data
public class PromotionSource {

  private int trigger;
  private BigDecimal discount;
  private List<String> fired;

  /**
   * 活动商家优惠承担
   */
  @JSONField(name = "merchant_discount")
  private BigDecimal merchantDiscount;

  /**
   * 活动平台优惠承担
   */
  @JSONField(name = "platform_discount")
  private BigDecimal platformDiscount;

  private BigDecimal storeDiscount;
  private BigDecimal cost;
  private BigDecimal tpAllowance;
  private BigDecimal merchantAllowance;
  private BigDecimal platformAllowance;
  private BigDecimal realAmount;
  private BigDecimal transferAmount;
}
