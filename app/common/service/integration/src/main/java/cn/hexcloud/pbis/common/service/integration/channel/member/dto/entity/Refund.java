package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @Classname Refund
 * @Description:
 * @Date 2021/10/296:51 下午
 * <AUTHOR>
 */
@Data
public class Refund {

  /**
   * 退单的ticketId
   */
  @JSONField(name = "refund_id")
  private String refundId;

  /**
   * 退单正单的ticketId
   */
  @JSONField(name = "ref_ticket_id")
  private String refTicketId;

  /**
   * 退单正单的refTicketNo
   */
  @JSONField(name = "ref_ticket_no")
  private String refTicketNo;

  /**
   * 退单正单的refTicketNo
   */
  @JSONField(name = "refund_reason")
  private String refundReason;


  /**
   * 退单负单的orderId
   */
  @JSONField(name = "refund_no")
  private String refundNo;

  /**
   * 退单方
   */
  @JSONField(name = "refund_side")
  private String refundSide;

  private String refundCode;

}
