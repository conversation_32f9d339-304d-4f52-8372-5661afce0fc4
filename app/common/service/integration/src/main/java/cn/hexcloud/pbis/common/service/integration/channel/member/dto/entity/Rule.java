package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * @Classname Rule
 * @Description:
 * @Date 2021/10/262:52 下午
 * <AUTHOR>
 */
@Data
@ToString
public class Rule {

  private int amount;
  private int couponAmount;
  private int discount;
  private int quantity;
  private String productId;
  private String buyProductId;
  private String giveProductId;
  private int plusAmount;
  private int buyQuantity;
  private int giveQuantity;
  private List<String> skus;
}
