package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @Classname Table
 * @Description:
 * @Date 2021/10/297:00 下午
 * <AUTHOR>
 */
@Data
public class Table {

  private String id;

  /**
   * 卓位分区id
   */
  @JSONField(name = "zone_id")
  private String zoneId;

  /**
   * 桌位号
   */
  private String no;

  /**
   * 桌位分区号
   */

  private String zoneNo;

  /**
   * 桌位人数
   */
  private int people;

  /**
   * 是否临时桌位
   */
  private boolean temporary;

}
