package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * @Classname Takeaway
 * @Description:
 * @Date 2021/10/297:02 下午
 * <AUTHOR>
 */
@Data
public class Takeaway {

  @JSONField(name = "order_method")
  private String orderMethod;
  @JSONField(name = "is_paid")
  private boolean isPaid;
  @JSONField(name = "tp_order_id")
  private String tpOrderId;
  @JSONField(name = "order_time")
  private String orderTime;
  @JSONField(name = "deliver_time")
  private String deliverTime;
  private String description;
  private String consignee;
  @JSONField(name = "delivery_poi_address")
  private String deliveryPoiAddress;
  @JSONField(name = "phone_list")
  private List<String> phoneList;
  private String tp;
  private String source;
  @JSONField(name = "source_order_id")
  private String sourceOrderId;
  @JSONField(name = "day_seq")
  private String daySeq;
  @JSONField(name = "delivery_type")
  private int deliveryType;
  @JSONField(name = "delivery_name")
  private String deliveryName;
  @JSONField(name = "invoice_title")
  private String invoiceTitle;
  @JSONField(name = "waiting_time")
  private String waitingTime;
  @JSONField(name = "tableware_num")
  private int tablewareNum;
  @JSONField(name = "send_fee")
  private BigDecimal sendFee;
  @JSONField(name = "package_fee")
  private BigDecimal packageFee;
  @JSONField(name = "delivery_time")
  private String deliveryTime;
  @JSONField(name = "take_meal_sn")
  private String takeMealSn;
  private int partnerPlatformId;
  private String partnerPlatformName;
  private String wxName;
  @JSONField(name = "isHighPriority")
  private boolean isHighPriority;
  private String takeoutType;
  private String originalOrderNo;
  private String remarks;
  private Blessing blessing;
  private String nickName;
  @JSONField(name = "delivery_phone")
  private String deliveryPhone;
  @JSONField(name = "invoice_type")
  private String invoiceType;
  @JSONField(name = "invoice_tax_payer_id")
  private String invoiceTaxPayerId;
  @JSONField(name = "invoice_email")
  private String invoiceEmail;
  @JSONField(name = "merchant_send_fee")
  private BigDecimal merchantSendFee;
  private boolean selfDelivery;
  @JSONField(name = "delivery_platform")
  private String deliveryPlatform;

  private String invoiceProvider;
  private String invoiceAmount;
  private String invoiceUrl;
  private BigDecimal sendFeeForMerchant;
  private BigDecimal platformSendFee;
  private BigDecimal sendFeeForPlatform;
}
