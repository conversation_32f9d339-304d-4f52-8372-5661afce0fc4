package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import java.math.BigDecimal;
import lombok.Data;

/**
 * @Classname Tax
 * @Description:
 * @Date 2021/10/297:01 下午
 * <AUTHOR>
 */
@Data
public class Tax {

  /**
   * 税金额
   */
  private BigDecimal amount;

  /**
   * 纳税商品总额
   */
  private BigDecimal subTotal;

  /**
   * 税率
   */
  private BigDecimal rate;


  /**
   * 税种编码
   */
  private String code;


  /**
   * 税种名称
   */
  private String name;

}
