package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;


/**
 * @Classname Ticket
 * @Description:
 * @Date 2021/10/296:48 下午
 * <AUTHOR>
 */
@Data
public class Ticket {

  /**
   * 订单ID
   */
  @JSONField(name = "ticket_id")
  private String ticketId;

  /**
   * 订单号
   */
  @JSONField(name = "ticket_no")
  private String ticketNo;

  /**
   * YYYY-MM-dd HH:MM:SS，订单开始时间
   */
  @JSONField(name = "start_time")
  private String startTime;

  /**
   * YYYY-MM-dd HH:MM:SS，订单开始时间
   */
  @JSONField(name = "end_time")
  private String endTime;

  /**
   * YYYY-MM-dd HH:MM:SS，订单营业日期
   */
  @JSONField(name = "bus_date")
  private String busDate;

  /**
   * pos 信息
   */
  private Pos pos;

  /**
   * 收银员信息
   */
  private Operator operator;

  /**
   * 金额信息
   */
  private Amount amounts;

  /**
   * 取餐号
   */
  private String takemealNumber;

  /**
   * 订单商品总数
   */
  private int qty;

  /**
   * 订单状态
   */
  private String status;

  /**
   * 订单状态
   */
  private Refund refundInfo;

  /**
   * 订单渠道信息
   */
  private Channel channel;

  /**
   * 订单商品信息
   */
  private List<TicketProduct> products;

  /**
   * 订单的支付信息
   */
  private List<Payment> payments;

  /**
   * 订单的促销信息
   */
  private List<Promotion> promotions;

  /**
   * 订单的促销信息
   */
  private List<Member> members;

  /**
   * 桌位信息
   */
  private Table table;

  /**
   * 订单人数
   */
  private int people;

  /**
   * 房间号
   */
  @JSONField(name = "room_no")
  private String roomNo;

  /**
   * 订单备注
   */
  private String remark;

  @JSONField(name = "house_ac")
  private boolean houseAc;

  /**
   * 早中晚餐标志
   */
  @JSONField(name = "order_time_type")
  private String orderTimeType;

  /**
   * 班次号
   */
  private String shiftNumber;

  /**
   * 税项
   */
  private List<Tax> taxList;

  /**
   * 门店信息
   */
  private Store store;

  /**
   * 外卖信息
   */
  @JSONField(name = "takeaway_info")
  private Takeaway takeawayInfo;

  /**
   * 订单唯一流水号
   */
  private String ticketUno;

  /**
   * 卡券信息
   */
  private List<TicketCoupon> coupons;

  /**
   * 费用信息
   */
  private List<Fee> fees;

  /**
   * 时区信息
   */
  private String timeZone;

  /**
   * 插单信息
   */
  private Prior prior;

  /**
   * 币种信息
   */
  private Currency currency;


  /**
   * 是否已折扣分摊
   */
  @JSONField(name = "discount_proportioned")
  private boolean discountProportioned;

  /**
   * 是否是拼单订单
   */
  private boolean spellOrder;

  /**
   * 门店租户id
   */
  @JSONField(name = "partner_id")
  private long partnerId;

  /**
   * 发票信息
   */
  private InvoiceInfo invoiceInfo;

  private BigDecimal weight;

  private Efficiency efficiency;

  private List<Fee> feesNoAccount;


  /**
   * 是否是拼单订单
   */
  private boolean pendingSyncMember;


}
