package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import lombok.Data;

/**
 * @Classname TicketCoupon
 * @Description:
 * @Date 2021/10/297:03 下午
 * <AUTHOR>
 */
@Data
public class TicketCoupon {


  @JSONField(name = "is_online")
  private boolean isOnline;
  private String id;
  private String name;
  private String code;
  private long type;
  @JSONField(name = "par_value")
  private BigDecimal parValue;
  @JSONField(name = "sequence_id")
  private String sequenceId;
  private BigDecimal price;
  private BigDecimal cost;
  @JSONField(name = "tp_allowance")
  private BigDecimal tpAllowance;
  @JSONField(name = "merchant_allowance")
  private BigDecimal merchantAllowance;
  @JSONField(name = "real_amount")
  private BigDecimal realAmount;
  @JSONField(name = "transfer_amount")
  private BigDecimal transferAmount;
  private BigDecimal platformAllowance;
}
