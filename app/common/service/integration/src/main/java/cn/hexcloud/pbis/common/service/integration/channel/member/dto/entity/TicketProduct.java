package cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity;

import com.alibaba.fastjson.annotation.JSONField;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * @Classname TicketProduct
 * @Description:
 * @Date 2021/10/296:52 下午
 * <AUTHOR>
 */
@Data
public class TicketProduct {

  /**
   * 商品id
   */
  private String id;

  /**
   * 商品名称
   */
  private String name;

  /**
   * 商品编码
   */
  private String code;

  /**
   * 商品的顺序号 代表下单的顺序
   */
  @JSONField(name = "seq_id")
  private long seqId;

  /**
   * 商品单价
   */
  private BigDecimal price;

  /**
   * 商品总价
   */
  private BigDecimal amount;

  /**
   * 商品数量
   */
  private int qty;

  /**
   * 商品折扣金额
   */
  @JSONField(name = "discount_amount")
  private BigDecimal discountAmount;

  /**
   * 商品类型
   */
  private String type;

  /**
   * 加料
   */
  private List<TicketProduct> accessories;

  /**
   * 套餐子项
   */
  private List<TicketProduct> comboItems;

  /**
   * 操作记录
   */
  @JSONField(name = "operation_records")
  private String operationRecords;

  /**
   * sku属性
   */
  private List<SkuRemark> skuRemark;

  /**
   * 商品备注
   */
  private String remark;

  /**
   * 税额
   */
  private BigDecimal taxAmount;

  /**
   * 净额
   */
  @JSONField(name = "net_amount")
  private BigDecimal netAmount;

  /**
   * 当前商品所属的用户昵称
   */
  private List<SpellUser> spellUsers;

  /**
   * 商品图片
   */
  private String image;

  /**
   * 商品折扣
   */
  @JSONField(name = "sum_discount_amount")
  private BigDecimal sumDiscountAmount;

  /**
   * 商品实收
   */
  @JSONField(name = "sum_net_amount")
  private BigDecimal sumNetAmount;

  /**
   * 商品原价
   */
  @JSONField(name = "sum_amount")
  private BigDecimal sumAmount;

  private BigDecimal avgMakeSpan;
  private BigDecimal weight;
  private boolean hasMakeSpan;
  private boolean hasWeight;

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public long getSeqId() {
    return seqId;
  }

  public void setSeqId(long seqId) {
    this.seqId = seqId;
  }

  public BigDecimal getPrice() {
    return price;
  }

  public void setPrice(BigDecimal price) {
    this.price = price;
  }

  public BigDecimal getAmount() {
    return amount;
  }

  public void setAmount(BigDecimal amount) {
    this.amount = amount;
  }

  public int getQty() {
    return qty;
  }

  public void setQty(int qty) {
    this.qty = qty;
  }

  public BigDecimal getDiscountAmount() {
    return discountAmount;
  }

  public void setDiscountAmount(BigDecimal discountAmount) {
    this.discountAmount = discountAmount;
  }

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public List<TicketProduct> getAccessories() {
    return accessories;
  }

  public void setAccessories(List<TicketProduct> accessories) {
    this.accessories = accessories;
  }

  public List<TicketProduct> getComboItems() {
    return comboItems;
  }

  public void setComboItems(List<TicketProduct> comboItems) {
    this.comboItems = comboItems;
  }

  public String getOperationRecords() {
    return operationRecords;
  }

  public void setOperationRecords(String operationRecords) {
    this.operationRecords = operationRecords;
  }

  public List<SkuRemark> getSkuRemark() {
    return skuRemark;
  }

  public void setSkuRemark(List<SkuRemark> skuRemark) {
    this.skuRemark = skuRemark;
  }

  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }

  public BigDecimal getTaxAmount() {
    return taxAmount;
  }

  public void setTaxAmount(BigDecimal taxAmount) {
    this.taxAmount = taxAmount;
  }

  public BigDecimal getNetAmount() {
    return netAmount;
  }

  public void setNetAmount(BigDecimal netAmount) {
    this.netAmount = netAmount;
  }

  public List<SpellUser> getSpellUsers() {
    return spellUsers;
  }

  public void setSpellUsers(List<SpellUser> spellUsers) {
    this.spellUsers = spellUsers;
  }

  public String getImage() {
    return image;
  }

  public void setImage(String image) {
    this.image = image;
  }

  public BigDecimal getSumDiscountAmount() {
    return sumDiscountAmount;
  }

  public void setSumDiscountAmount(BigDecimal sumDiscountAmount) {
    this.sumDiscountAmount = sumDiscountAmount;
  }

  public BigDecimal getSumNetAmount() {
    return sumNetAmount;
  }

  public void setSumNetAmount(BigDecimal sumNetAmount) {
    this.sumNetAmount = sumNetAmount;
  }

  public BigDecimal getSumAmount() {
    return sumAmount;
  }

  public void setSumAmount(BigDecimal sumAmount) {
    this.sumAmount = sumAmount;
  }

  public BigDecimal getAvgMakeSpan() {
    return avgMakeSpan;
  }

  public void setAvgMakeSpan(BigDecimal avgMakeSpan) {
    this.avgMakeSpan = avgMakeSpan;
  }

  public BigDecimal getWeight() {
    return weight;
  }

  public void setWeight(BigDecimal weight) {
    this.weight = weight;
  }

  public boolean isHasMakeSpan() {
    return hasMakeSpan;
  }

  public void setHasMakeSpan(boolean hasMakeSpan) {
    this.hasMakeSpan = hasMakeSpan;
  }

  public boolean isHasWeight() {
    return hasWeight;
  }

  public void setHasWeight(boolean hasWeight) {
    this.hasWeight = hasWeight;
  }
}
