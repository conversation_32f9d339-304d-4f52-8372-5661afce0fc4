package cn.hexcloud.pbis.common.service.integration.channel.member.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Member;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Order;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 *
 *
 * <AUTHOR>
 * @date 2023/07/05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class AvailablePointsRequest extends ChannelRequest {
    /**
     * 会员信息
     */
    private Member memberContent;

    /**
     * 订单信息
     */
    private Order orderContent;



}
