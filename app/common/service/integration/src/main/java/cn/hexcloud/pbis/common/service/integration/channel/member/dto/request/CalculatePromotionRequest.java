package cn.hexcloud.pbis.common.service.integration.channel.member.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.GoodsDetail;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.PromotionInfo;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @program: pbis
 * @author: miao
 * @create: 2022-01-09 16:49
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class CalculatePromotionRequest extends ChannelRequest {

  //门店code
  private String storeCode;
  //订单总价
  private String subTotal;
  //商品列表
  private List<GoodsDetail> lines;
  //适用场景
  private Integer scene;
  //用户id
  private Integer userId;
  //打包费
  private String packageFee;
  //配送费
  private String deliveryFee;
  //优惠信息
  private List<PromotionInfo> discs;
  // 门店id
  private Long storeId;
  // 门店partner id
  private Long partnerId;
  // 门店scope id，如果没有就传0
  private Long scopeId;
  // 渠道code
  private String channel;

}
