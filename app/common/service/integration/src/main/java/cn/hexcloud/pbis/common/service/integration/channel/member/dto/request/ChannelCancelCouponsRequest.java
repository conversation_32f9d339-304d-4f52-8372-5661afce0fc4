package cn.hexcloud.pbis.common.service.integration.channel.member.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.CouponReq;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Member;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Order;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Classname ChannelCancelCouponsRequest
 * @Description:
 * @Date 2021/10/288:15 下午
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelCancelCouponsRequest extends ChannelRequest {

  /**
   * 渠道编码
   */
  private String channel;

  /**
   * 支付时传给第三方接口的唯一标识id
   */
  private String batchTicketId;

  /**
   * 门店id
   */
  private long storeId;

  /**
   * 租户id
   */
  private long partnerId;

  /**
   * 门店scope id，如果没有就传0
   */
  private long scopeId;

  /**
   * 用户id
   */
  private long userId;

  /**
   * 会员信息
   */
  private Member memberContent;

  /**
   * 订单信息
   */
  private Order orderContent;

  /**
   * 门店信息
   */
  private String storeCode;

  /**
   * 卡券列表
   */
  private List<CouponReq> coupons;
}
