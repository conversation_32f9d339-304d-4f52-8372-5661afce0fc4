package cn.hexcloud.pbis.common.service.integration.channel.member.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.CouponReq;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Member;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Order;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Classname ChannelConsumeCouponsRequest
 * @Description:
 * @Date 2021/10/287:40 下午
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelConsumeCouponsRequest extends ChannelRequest {


  /**
   * 支付时传给第三方接口的唯一标识id
   */
  private String batchTicketId;

  /**
   * 门店id
   */
  private long storeId;

  /**
   * 租户id
   */
  private long partnerId;

  /**
   * 门店scope id，如果没有就传0
   */
  private long scopeId;

  /**
   * 用户id
   */
  private long userId;

  /**
   * 会员信息
   */
  private Member memberContent;

  /**
   * 订单信息
   */
  private Order orderContent;

  /**
   * 门店信息
   */
  private String storeCode;

  /**
   * 使用积分（没有使用积分填0）
   */
  private long usedPoints;

  /**
   * 卡券列表
   */
  private List<CouponReq> coupons;

  /**
   * 券核销凭证
   */
  private String consumeToken;

}
