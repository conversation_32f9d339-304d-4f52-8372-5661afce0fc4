package cn.hexcloud.pbis.common.service.integration.channel.member.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Member;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Order;
import com.sun.istack.NotNull;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @program: pbis
 * @author: miao
 * @create: 2022-06-23 17:28
 **/

@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelConsumePointsRequest extends ChannelRequest{

  /**
   * 支付时传给第三方接口的唯一标识id
   */
  private String batchTicketId;

  /**
   * 会员信息
   */
  private Member memberContent;

  /**
   * 订单信息
   */
  private Order orderContent;

  /**
   * 门店信息
   */
  private String storeCode;

  /**
   * 使用积分(分单位)
   */
  private Integer point;

  /**
   * 积分抵现金额
   */
  private Integer amount;

  /**
   * 操作场景 true：积分抵现（默认）；false：积分更新
   */
  private boolean usePoint;

  private String sessid;
}
