package cn.hexcloud.pbis.common.service.integration.channel.member.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.CouponReq;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Member;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Classname GetCouponInfoRequest
 * @Description:
 * @Date 2021/10/285:06 下午
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelCouponInfoRequest extends ChannelRequest {

  private String channel;

  /**
   * 门店id
   */
  private String storeId;

  /**
   * 门店code
   */
  private String storeCode;

  /**
   * 租户id
   */
  private long partnerId;

  /**
   * 门店scope id，如果没有就传0
   */
  private long scopeId;

  /**
   * 用户id
   */
  private long userId;

  /**
   * 是否返回对应促销/Hex券id等主档，默认false
   */
  private boolean includeMetadata;

  /**
   * 会员信息
   */
  private Member memberContent;

  /**
   * 卡券信息
   */
  private List<CouponReq> coupons;

}
