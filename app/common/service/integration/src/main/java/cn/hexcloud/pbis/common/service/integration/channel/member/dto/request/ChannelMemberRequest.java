package cn.hexcloud.pbis.common.service.integration.channel.member.dto.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Classname GetMemberRequest
 * @Description:
 * @Date 2021/10/262:08 下午
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelMemberRequest extends ChannelRequest {

    /**
     * 会员卡号
     */
    private String cardNo;

    /**
     * 会员编码
     */
    private String memberCode;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 敏感信息 密码、辅助码、二磁道信息等
     */
    private String secretContent;

    /**
     * 门店id
     */
    private long storeId;

    /**
     * 租户id
     */
    private long partnerId;

    /**
     * 门店scope id，如果没有就传0
     */
    private long scopeId;

    /**
     * 用户id
     */
    private long userId;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 券号
     */
    private String couponNo;

}
