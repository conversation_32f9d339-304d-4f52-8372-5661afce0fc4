package cn.hexcloud.pbis.common.service.integration.channel.member.dto.request;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Classname ChannelNotifyRequest
 * @Description:
 * @Date 2021/12/222:38 下午
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelNotifyRequest extends ChannelRequest {


  /**
   * （必传）渠道编码 SeltekTakeout(雪沥外卖)
   */
  private String channel;

  /**
   * 合阔订单ID (全市场唯一)
   */
  private String ticketId;

  /**
   * 第三方订单ID(必须唯一)
   */
  private String tpOrderId;

  /**
   * 等待制作(1),制作中(2),制作完成(3),已取餐(4),无效或废弃(5),发起退款(10)
   */
  private int status;

  /**
   * 取餐号
   */
  private String takeMealNumber;

  /**
   * 预计等待时间：单位秒
   */
  private int waitingTime;

  /**
   * 前面等待订单数
   */
  private int waitingOrderCount;

  /**
   * 前面等待商品数
   */
  private int waitingProductCount;

  /**
   * 渠道名称
   */
  private String tpName;
  private String source;

  private int makeTime;

}
