package cn.hexcloud.pbis.common.service.integration.channel.member.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Ticket;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Classname ChannelSyncOrderRequest
 * @Description:
 * @Date 2021/10/296:47 下午
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelSyncOrderRequest extends ChannelRequest {

  private Ticket ticket;

  public Ticket getTicket() {
    return ticket;
  }

  public void setTicket(Ticket ticket) {
    this.ticket = ticket;
  }
}
