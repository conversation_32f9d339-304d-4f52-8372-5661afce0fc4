package cn.hexcloud.pbis.common.service.integration.channel.member.dto.response;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 *
 *
 * <AUTHOR>
 * @date 2023/07/05
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class AvailablePointsResponse extends ChannelResponse {
    /**
     * 按商品分类设置的折扣比例与商品金额换算的积分上限和
     */
    private Integer maxPoint;
    /**
     * 商户维度的积分使用下限
     */
    private Integer minPoint;
    /**
     * 积分抵扣比例,100积分=1元
     */
    private Integer ratio;
    private String sessId;
}
