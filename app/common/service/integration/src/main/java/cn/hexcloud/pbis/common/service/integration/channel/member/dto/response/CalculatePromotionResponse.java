package cn.hexcloud.pbis.common.service.integration.channel.member.dto.response;

import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.DeliveryFee;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.PackageFee;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.PriceDiscount;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.SummaryDiscount;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @program: pbis
 * @author: miao
 * @create: 2022-01-09 16:49
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class CalculatePromotionResponse extends ChannelResponse {

  private List<PriceDiscount> discount;
  private SummaryDiscount summary;
  private DeliveryFee deliveryFee;
  private PackageFee packageFee;
}
