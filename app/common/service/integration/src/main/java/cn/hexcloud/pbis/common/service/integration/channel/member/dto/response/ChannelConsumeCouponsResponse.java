package cn.hexcloud.pbis.common.service.integration.channel.member.dto.response;

import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Coupon;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.DeliveryFee;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.PackageFee;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.PriceDiscount;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.SummaryDiscount;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Classname ChannelConsumeCouponsResponse
 * @Description:
 * @Date 2021/10/287:44 下午
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelConsumeCouponsResponse extends ChannelResponse {

  private List<PriceDiscount> discount;
  private SummaryDiscount summary;
  private List<Coupon> couponDetails;
  private DeliveryFee deliveryFee;
  private PackageFee packageFee;
}
