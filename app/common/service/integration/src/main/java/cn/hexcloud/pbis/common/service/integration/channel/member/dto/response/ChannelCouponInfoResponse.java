package cn.hexcloud.pbis.common.service.integration.channel.member.dto.response;

import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Coupon;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Classname GetCouponInfoResponse
 * @Description:
 * @Date 2021/10/285:12 下午
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelCouponInfoResponse extends ChannelResponse {

  /**
   * 卡券信息
   */
  private List<Coupon> details;

  /**
   * 会员id
   */
  private String memberId;

  /**
   * 券核销凭证
   */
  private String consumeToken;
}
