package cn.hexcloud.pbis.common.service.integration.channel.member.dto.response;

import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Benefit;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Coupon;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.DepositCard;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.PurchasesAnalysis;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Classname GetMemberResponse
 * @Description:
 * @Date 2021/10/262:14 下午
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelMemberResponse extends ChannelResponse {

  /**
   * 渠道编码
   */
  private String channel;

  /**
   * 账户余额
   */
  private int accountBalance;

  /**
   * 历史总积分
   */
  private int creditTotal;

  /**
   * 积分余额
   */
  private int creditBalance;

  /**
   * 会员卡号
   */
  private String cardNo;

  /**
   * 会员id
   */
  private String memberId;

  /**
   * 会员编码
   */
  private String memberCode;

  /**
   * 会员名称
   */
  private String name;

  /**
   * MALE/FEMALE/NA(未知)
   */
  private String gender;

  /**
   * 邮箱
   */
  private String email;

  /**
   * 手机号码
   */
  private String mobile;

  /**
   * 会员状态
   */
  private int status;

  /**
   * 会员登记
   */
  private String gradeId;

  /**
   * 会员等级名称
   */
  private String gradeName;

  /**
   * 会员授予时间
   */
  private String grantDate;

  /**
   * 是否员工
   */
  private boolean isEmployee;

  /**
   * 会员头像
   */
  private String avatar;

  private String birthday;

  /**
   * 支付编码
   */
  private String paycode;

  /**
   * 促销id
   */
  private String promotionId;

  /**
   * 提示语
   */
  private String greetings;

  /**
   * 储值卡列表
   */
  private List<DepositCard> depositCard;

  /**
   * 卡券列表
   */
  private List<Coupon> coupons;


  /**
   * 权益列表
   */
  private List<Benefit> benefit;

  /**
   * 猜你喜欢
   */
  private List<PurchasesAnalysis> analysis;

  /**
   * 积分抵扣金额
   */
  private String creditDeductionAmount;

  /**
   * 会员等级名称
   */
  private String memberLevelName;

}
