package cn.hexcloud.pbis.common.service.integration.channel.member.enums;

import java.util.Arrays;
import lombok.Getter;

/**
 * @Classname OrderStatusEnum
 * @Description:
 * @Date 2021/8/312:44 下午
 * <AUTHOR>
 */
@Getter
public enum OrderStatusEnum {

    SALE("SALE", "销售单"),

    REFUND("REFUND", "退单"),

    ;

    OrderStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;

    public static OrderStatusEnum getByCode(String code) {
        return Arrays.stream(OrderStatusEnum.values()).filter((v) -> v.getCode().equals(code)).findFirst().orElse(null);
    }
}
