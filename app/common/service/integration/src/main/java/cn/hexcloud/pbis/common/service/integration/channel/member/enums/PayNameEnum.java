package cn.hexcloud.pbis.common.service.integration.channel.member.enums;

import java.util.Arrays;

/**
 * @Classname PayNameEnum
 * @Description:
 * @Date 2021/9/222:52 下午
 * <AUTHOR>
 */
public enum PayNameEnum {

    REFUND_10211("10211", "微信"),
    REFUND_10300("10300", "支付宝"),
    REFUND_10102("10102", "储值卡"),
    REFUND_100001("100001", "积分支付"),
    REFUND_10400("10400", "其他"),

    ;

    PayNameEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static PayNameEnum getByCode(String code) {
        return Arrays.stream(PayNameEnum.values()).filter((v) -> v.getCode().equals(code)).findFirst().orElse(null);
    }
}
