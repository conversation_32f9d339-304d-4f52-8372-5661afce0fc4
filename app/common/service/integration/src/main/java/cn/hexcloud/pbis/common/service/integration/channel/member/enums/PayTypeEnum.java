package cn.hexcloud.pbis.common.service.integration.channel.member.enums;

import java.util.Arrays;

/**
 * @program: pbis
 * @author: miao
 * @create: 2021-11-19 14:53
 **/
public enum PayTypeEnum {

  ALIPAY("ALIPAY", "支付宝"),
  Wxpay("WXPAY", "微信"),
  Unionpay("UNIONPAY", "银联"),
  Hexunite("Hexunite", "合阔聚合"),
  QMaiPay("QMAIPAY", "合阔聚合"),
  HeyTeaPay("HEYTEAPAY", "喜茶支付"),
  <PERSON>ck("MOCK", "测试渠道"),
  Pay100("PAY100", "扫呗"),
  JDpay("JDpay", "京东支付"),
  QQpay("QQpay", "qq钱包"),
  Yipay("Yipay", "翼支付"),
  <PERSON>uBei("KouBei", "口碑"),
  <PERSON><PERSON><PERSON>("<PERSON><PERSON><PERSON>", "招商银行"),
  XLAccount("XLAccount", "雪沥会员"),
  XLCard("XLCard", "雪沥面值"),
  ChinaUMSPay("ChinaUMSPay", "银商支付"),
  QMAIDEPOSITPAY("QMAIDEPOSITPAY", "企迈支付"),
  CASH_PAYMENT("99999", "现金"),
  FIX_PAYMENT("FIX_PAYMENT", "混合支付"),
  FREEMUDDEPOSITPAY("FREEMUDDEPOSITPAY", "非码储值卡支付"),
  MEMBERFM("MEMBERFM", "非码卡券支付"),
  CASH("801", "现金"),
  ;
  PayTypeEnum(String code, String name) {
    this.code = code;
    this.name = name;
  }

  private String code;
  private String name;

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public static PayTypeEnum getByCode(String code) {
    return Arrays.stream(PayTypeEnum.values()).filter((v) -> v.getCode().equals(code)).findFirst().orElse(null);
  }
}
