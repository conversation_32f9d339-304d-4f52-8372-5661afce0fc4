package cn.hexcloud.pbis.common.service.integration.channel.member.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Coupon
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Product
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.MemberModule
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.google.common.collect.Lists
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.math.NumberUtils
import org.apache.http.NameValuePair
import org.apache.http.message.BasicHeader
import org.springframework.util.DigestUtils

import java.nio.charset.StandardCharsets
import java.text.MessageFormat

class AntankMemberModule extends AbstractExternalChannelModule implements MemberModule {

    private static final String API_SUCCESS_CODE = "0000";
    private static final int API_FAIL_CODE = NumberUtils.INTEGER_ONE;
    // 根据会员手机号获取会员信息
    private static final String MEMBER_INFO = "/thvendor/api/member/getMemberInfo.xhtml";
    // 核销优惠券
    private static final String VERIFICATION_COUPONS = "/thvendor/api/coupon/offlineConfirmUseCouponByPass.xhtml";
    // 取消核销优惠券
    private static final String REFUND_COUPON = "/thvendor/api/coupon/refundCouponByTradeNo.xhtml";

    // 获取订单可用积分范围
    private static final String USABLE_POINT = "/thvendor/api/member/getUsablePoint.xhtml";
    // 消费积分
    private static final String SPEND_POINT = "/thvendor/api/member/spendPoint.xhtml";
    // 取消消费积分
    private static final String RETURN_POINT = "/thvendor/api/member/returnPoint.xhtml";


    // 租户唯一ID 卡司提供
    private String PARTNER_KEY
    // 密钥
    private String SECRET_CODE
    // 第三方域名信息
    private String DOMAIN
    // 第三方接口版本
    private String VERSION

    @Override
    String getModuleName() {
        return super.getModuleName()
    }

    AntankMemberModule(ExternalChannel channel) {
        super(channel)

        // 初始化动态配置信息
        initChannelConfig(channel)
    }

    private void initChannelConfig(ExternalChannel channel) {
        ChannelAccessConfig channelConfig = channel.getChannelAccessConfig()
        if (Objects.isNull(channelConfig) || Objects.isNull(channelConfig.getProperties())) {
            throw new CommonException(ServiceError.INVALID_CHANNEL_CONFIG)
        }
        PARTNER_KEY = channelConfig.getAppId()
        SECRET_CODE = channelConfig.getAppKey()
        DOMAIN = channelConfig.getGatewayUrl()
        VERSION = "1.0" // 暂时先定死
    }

    /**
     * 获取用户信息
     * @param request
     * @return
     */
    @Override
    ChannelMemberResponse getMember(ChannelMemberRequest request) {
        LoggerUtil.info("调用卡司会员接口,请求参数:{0}", JSON.toJSONString(request))

        // 业务请求参数
        Long currentTimeMillis = System.currentTimeMillis();
        TreeMap<String, String> businessParams = new TreeMap<>();
        businessParams.put("memberEncode", request.getMobile());
        businessParams.put("timestamp", "" + currentTimeMillis);

        // 系统 + 业务请求参数
        TreeMap<String, String> bodyParams = new TreeMap<>();
        bodyParams.put("timestamp", "" + currentTimeMillis);
        bodyParams.put("partnerKey", PARTNER_KEY);
        bodyParams.put("v", VERSION);
        bodyParams.put("sign", getSignature(businessParams));
        bodyParams.putAll(businessParams)

        LoggerUtil.info("调用卡司会员接口,请求URL:{0},请求参数:{1}", getRequestUrl(MEMBER_INFO), JSON.toJSONString(bodyParams))
        JSONObject jsonData = doPostMap(getRequestUrl(MEMBER_INFO), bodyParams);
        LoggerUtil.info("调用卡司会员接口,返回结果:{0}", JSON.toJSONString(jsonData))
        if (!Objects.equals(API_SUCCESS_CODE, jsonData.getString("errcode"))) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, jsonData.getString("message"))
        }

        ChannelMemberResponse response = new ChannelMemberResponse()
        response.setResponseCode(API_SUCCESS_CODE)
        response.setSuccess(true)
        memberInfoResultMapping(response, jsonData)
        LoggerUtil.info("调用卡司会员接口,C端返回值:{0}",  JSON.toJSONString(response))
        return response
    }

    /**
     * 核销优惠券
     * @param request
     * @return
     */
    @Override
    ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest request) {
        LoggerUtil.info("调用卡司核销优惠券接口,请求参数:{0}", JSON.toJSONString(request))
        ChannelConsumeCouponsResponse response = new ChannelConsumeCouponsResponse()

        Long currentTimeMillis = System.currentTimeMillis();
        TreeMap<String, String> businessParams = new TreeMap<>();
        businessParams.put("timestamp", "" + currentTimeMillis);
        if (CollectionUtils.isEmpty(request.getCoupons())) {
            response.setMessage(ServiceError.INVALID_PARAM.getMessage("缺少优惠券信息"))
            response.setSuccess(false)
            return response;
        }
        // 卡密 核销的凭证
        businessParams.put("cardpass", request.getCoupons().get(0).getCodeNo());
        // 最终优惠金额
        businessParams.put("useAmount", String.valueOf(request.getCoupons().get(0).getExtend()));
        // 订单号
        businessParams.put("tradeNo", request.getOrderContent().getOrderTicketId());
        businessParams.put("mobile", request.getMemberContent().getMobile());
        businessParams.put("merchantId", channel.getChannelAccessConfig().getMerchantId());
        // 核销人员信息（id、账号名等标识
        businessParams.put("checkUser", String.valueOf(request.getUserId()));
        businessParams.put("deviceId", channel.getChannelAccessConfig().getTerminalId());

        // body 请求参数
        TreeMap<String, String> bodyParams = new TreeMap<>();
        bodyParams.put("timestamp", "" + currentTimeMillis);
        bodyParams.put("partnerKey", PARTNER_KEY);
        bodyParams.put("v", VERSION);
        bodyParams.put("sign", getSignature(businessParams));
        bodyParams.putAll(businessParams)

        LoggerUtil.info("调用卡司核销优惠券接口,请求URL:{0},请求参数:{1}", getRequestUrl(VERIFICATION_COUPONS), JSON.toJSONString(bodyParams))
        JSONObject jsonData = doPostMap(getRequestUrl(VERIFICATION_COUPONS), bodyParams);
        LoggerUtil.info("调用卡司核销优惠券接口,返回结果:{0}", JSON.toJSONString(jsonData))
        if (!Objects.equals(API_SUCCESS_CODE, jsonData.getString("errcode"))) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, jsonData.getString("message"))
        }


        response.setResponseCode(API_SUCCESS_CODE)
        response.setSuccess(true)
        response.setResponseContent(JSON.toJSONString(jsonData))
        return response
    }


    /**
     * 取消核销优惠券
     * @param request
     * @return
     */
    @Override
    ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest request) {
        LoggerUtil.info("调用卡司取消核销优惠券接口,请求参数:{0}", JSON.toJSONString(request))

        Long currentTimeMillis = System.currentTimeMillis();
        TreeMap<String, String> businessParams = new TreeMap<>();
        businessParams.put("timestamp", "" + currentTimeMillis);
        // 订单号
        businessParams.put("tradeNo", request.getOrderContent().getOrderTicketId());
        // 消费时用支付订单号，取消时用退款订单号
        businessParams.put("serialNo", request.getBatchTicketId());
        businessParams.put("merchantId", channel.getChannelAccessConfig().getMerchantId());
        businessParams.put("deviceId", channel.getChannelAccessConfig().getTerminalId());
        // 返还人员信息（id、账号名等标识）
        businessParams.put("returnUser", String.valueOf(request.getUserId()));

        // body 请求参数
        TreeMap<String, String> bodyParams = new TreeMap<>();
        bodyParams.put("timestamp", "" + currentTimeMillis);
        bodyParams.put("partnerKey", PARTNER_KEY);
        bodyParams.put("v", VERSION);
        bodyParams.put("sign", getSignature(businessParams));
        bodyParams.putAll(businessParams)

        LoggerUtil.info("调用卡司取消核销优惠券接口,请求URL:{0},请求参数:{1}", getRequestUrl(REFUND_COUPON), JSON.toJSONString(bodyParams))
        JSONObject jsonData = doPostMap(getRequestUrl(REFUND_COUPON), bodyParams);
        LoggerUtil.info("调用卡司取消核销优惠券接口,返回结果:{0}", JSON.toJSONString(jsonData))
        if (!Objects.equals(API_SUCCESS_CODE, jsonData.getString("errcode"))) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, jsonData.getString("message"))
        }

        ChannelCancelCouponsResponse response = new ChannelCancelCouponsResponse()
        response.setResponseCode(API_SUCCESS_CODE)
        response.setSuccess(true)
        response.setResponseContent(JSON.toJSONString(jsonData))
        return response
    }


    /**
     * 获取订单可用积分
     * @param request
     * @return
     */
    @Override
    AvailablePointsResponse getAvailablePoints(AvailablePointsRequest request) {
        LoggerUtil.info("调用卡司获取可用积分接口,请求参数:{0}", JSON.toJSONString(request))

        // 业务请求参数
        Long currentTimeMillis = System.currentTimeMillis();
        TreeMap<String, String> businessParams = new TreeMap<>();
        businessParams.put("timestamp", "" + currentTimeMillis);
        businessParams.put("mobile", request.getMemberContent().getMobile());
        // 商品信息
        businessParams.put("goods", converterGoods(request.getOrderContent().getProducts()))
        // 订单号
        businessParams.put("tradeNo", request.getOrderContent().getOrderTicketId());
        businessParams.put("merchantId", channel.getChannelAccessConfig().getMerchantId());
        businessParams.put("deviceId", channel.getChannelAccessConfig().getTerminalId());

        // body 请求参数
        TreeMap<String, String> bodyParams = new TreeMap<>();
        bodyParams.put("timestamp", "" + currentTimeMillis);
        bodyParams.put("partnerKey", PARTNER_KEY);
        bodyParams.put("v", VERSION);
        bodyParams.put("sign", getSignature(businessParams));
        bodyParams.putAll(businessParams);

        LoggerUtil.info("调用卡司可用积分接口,请求URL:{0},请求参数:{1}", getRequestUrl(MEMBER_INFO), JSON.toJSONString(bodyParams))
        JSONObject jsonData = doPostMap(getRequestUrl(USABLE_POINT), bodyParams);
        LoggerUtil.info("调用卡司可用积分接口,返回结果:{0}", JSON.toJSONString(jsonData))
        if (!Objects.equals(API_SUCCESS_CODE, jsonData.getString("errcode"))) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, jsonData.getString("message"))
        }
        JSONObject jsonObj = jsonData.getJSONObject("data");
        AvailablePointsResponse availablePointsResponse = new AvailablePointsResponse()
        availablePointsResponse.setResponseCode(API_SUCCESS_CODE)
        availablePointsResponse.setSuccess(true)
        if (Objects.isNull(jsonObj)) {
            availablePointsResponse.setErrorCode(jsonData.getString("errcode"))
            availablePointsResponse.setErrorMessage(jsonData.getString("message"))
            availablePointsResponse.setResponseContent(JSON.toJSONString(jsonData))
            return availablePointsResponse;
        }
        availablePointsResponse.setMaxPoint(jsonObj.getInteger("maxpoint"))
        availablePointsResponse.setMinPoint(jsonObj.getInteger("minpoint"))
        availablePointsResponse.setRatio(jsonObj.getInteger("ratio"))
        availablePointsResponse.setSessId(jsonObj.getString("sessid"))
        return availablePointsResponse
    }
    /**
     * sunny 消费积分 + .获取订单可用积分范围 一起， 需要通过  获取订单可用积分范围返回sessid 来调用消费积分接口消费
     * @param request
     * @return
     */
    @Override
    ChannelResponse consumePoints(ChannelConsumePointsRequest request) {
        LoggerUtil.info("调用卡司消费积分接口,请求参数:{0}", JSON.toJSONString(request))

        Long currentTimeMillis = System.currentTimeMillis();
        TreeMap<String, String> businessParams = new TreeMap<>();
        businessParams.put("timestamp", "" + currentTimeMillis);
        businessParams.put("mobile", request.getMemberContent().getMobile());
        // 本次消费积分
        businessParams.put("spendPoint", String.valueOf(request.getPoint()));
        // 订单号
        businessParams.put("tradeNo", request.getOrderContent().getOrderTicketId());
        // 支付时间
        businessParams.put("spendTime", request.getOrderContent().getSalesTime());
        // todo 本次接口新增字段
        businessParams.put("sessid", request.getSessid());
        businessParams.put("serialNo", request.getBatchTicketId());
        businessParams.put("merchantId", channel.getChannelAccessConfig().getMerchantId());
        // 商户名称
        businessParams.put("merchantName", request.getStoreCode());
        businessParams.put("deviceId", channel.getChannelAccessConfig().getTerminalId());

        // body 请求参数
        TreeMap<String, String> bodyParams = new TreeMap<>();
        bodyParams.put("timestamp", "" + businessParams);
        bodyParams.put("partnerKey", PARTNER_KEY);
        bodyParams.put("v", VERSION);
        bodyParams.put("sign", getSignature(businessParams));
        bodyParams.putAll(businessParams)

        LoggerUtil.info("调用卡司消费积分接口,请求URL:{0},请求参数:{1}", getRequestUrl(SPEND_POINT), JSON.toJSONString(bodyParams))
        JSONObject jsonData = doPostMap(getRequestUrl(SPEND_POINT), bodyParams);
        LoggerUtil.info("调用卡司消费积分接口,返回结果:{0}", JSON.toJSONString(jsonData))
        if (!Objects.equals(API_SUCCESS_CODE, jsonData.getString("errcode"))) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, jsonData.getString("message"))
        }

        ChannelResponse response = new ChannelResponse()
        response.setResponseCode(API_SUCCESS_CODE)
        response.setSuccess(true)
        response.setResponseContent(JSON.toJSONString(jsonData))
        return response
    }

    /**
     * 取消消费积分
     * @param request
     * @return
     */
    @Override
    ChannelResponse returnPoints(ChannelReturnPointsRequest request) {
        LoggerUtil.info("调用卡司取消消费积分接口,请求参数:{0}", JSON.toJSONString(request))

        Long currentTimeMillis = System.currentTimeMillis();
        TreeMap<String, String> businessParams = new TreeMap<>();
        businessParams.put("timestamp", "" + currentTimeMillis);
        businessParams.put("mobile", request.getMemberContent().getMobile());
        // 返还积分
        businessParams.put("returnPoint", String.valueOf(request.getPoint()));
        // 取消时间
        businessParams.put("returnTime", cn.hexcloud.pbis.common.util.LocalDateTimeUtil.convertTimeToString(System.currentTimeMillis()));
        // 订单号
        businessParams.put("tradeNo", request.getOrderContent().getOrderTicketId());
        // 唯一单号（消费时用支付订单号，取消时用退款订单号）
        businessParams.put("serialNo", request.getBatchTicketId());
        businessParams.put("merchantId", channel.getChannelAccessConfig().getMerchantId());
        businessParams.put("deviceId", channel.getChannelAccessConfig().getTerminalId());

        // body 请求参数
        TreeMap<String, String> bodyParams = new TreeMap<>();
        bodyParams.put("timestamp", "" + currentTimeMillis);
        bodyParams.put("partnerKey", PARTNER_KEY);
        bodyParams.put("v", VERSION);
        bodyParams.put("sign", getSignature(businessParams));
        bodyParams.putAll(businessParams)


        LoggerUtil.info("调用卡司取消积分接口,请求URL:{0},请求参数:{1}", getRequestUrl(RETURN_POINT), JSON.toJSONString(bodyParams))
        JSONObject jsonData = doPostMap(getRequestUrl(RETURN_POINT), bodyParams);
        LoggerUtil.info("调用卡司取消积分接口,返回结果:{0}", JSON.toJSONString(jsonData))
        if (!Objects.equals(API_SUCCESS_CODE, jsonData.getString("errcode"))) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, jsonData.getString("message"))
        }

        ChannelResponse response = new ChannelResponse()
        response.setResponseCode(API_SUCCESS_CODE)
        response.setSuccess(true)
        return response
    }


    @Override
    ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "KasiOperation.getCouponInfo")
    }

    @Override
    CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("cancel"))
    }

    @Override
    String getSignature(Map<String, String> rawMessage) {
        LoggerUtil.info("调用卡司获取签名,请求参数:{0}", JSON.toJSONString(rawMessage))
        // 需要将系统参数 + 业务参数 放在一起进行加签
        TreeMap<String, String> params = new TreeMap<>();
        params.put("partnerKey", PARTNER_KEY);
        params.put("v", VERSION);
        params.putAll(rawMessage)

        Map<String, String> replaceMap = new TreeMap<>()
        replaceMap.putAll(params)

        // 以下业务参数需要被特殊处理, 签名的时候要保证顺序,因为下面会进行字符串的替换操作,这几个参数需要被排除在外
        if (Objects.nonNull(params.get("goods"))) {
            replaceMap.put("goods", "{0}")
        }
        if (Objects.nonNull(params.get("spendTime"))) {
            replaceMap.put("spendTime", "{0}")
        }
        if (Objects.nonNull(params.get("returnTime"))) {
            replaceMap.put("returnTime", "{0}")
        }

        // 按照卡司的加签的规则，这里需要做处理
        String replaceParameter = replaceMap.toString()
                .replaceAll(",", "&")
                .replaceAll(" ", "")
                .replaceAll(":", "=");
        String concatSign = replaceParameter.substring(NumberUtils.INTEGER_ONE, replaceParameter.length() - NumberUtils.INTEGER_ONE)
                .concat(SECRET_CODE);

        if (Objects.nonNull(params.get("goods"))) {
            concatSign = MessageFormat.format(concatSign, params.get("goods"))
        }
        if (Objects.nonNull(params.get("spendTime"))) {
            concatSign = MessageFormat.format(concatSign, params.get("spendTime"))
        }
        if (Objects.nonNull(params.get("returnTime"))) {
            concatSign = MessageFormat.format(concatSign, params.get("returnTime"))
        }
        LoggerUtil.info("调用卡司获取签名加签内容:{0}", concatSign)
        LoggerUtil.info("调用卡司获取签名结果:{0}", DigestUtils.md5DigestAsHex(concatSign.getBytes(StandardCharsets.UTF_8)).toUpperCase())

        return DigestUtils.md5DigestAsHex(concatSign.getBytes(StandardCharsets.UTF_8)).toUpperCase();
    }


    /**
     * http请求
     * @param url
     * @param json
     * @return
     */
    private static JSONObject doPostMap(String url, Map<String, String> mapBody) {
        JSONObject res = new JSONObject()
        res.put('errcode', API_FAIL_CODE)

        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/x-www-form-urlencoded")

        List<NameValuePair> list = new ArrayList<>()
        for (Map.Entry<String, String> entry : mapBody.entrySet()) {
            list.add(new BasicHeader(entry.getKey(), entry.getValue()))
        }

        byte[] result = null;
        try {
            result = HttpUtil.doPost(url, list, header);
        } catch (Exception e) {
            LoggerUtil.error("调用第三方接口失败: {0},请求参数:{1}", e.printStackTrace(), JSON.toJSONString(list))
        }
        if (Objects.isNull(result)) {
            LoggerUtil.error("第三方接口返回数据为空{0}, 请求参数:{1}", null, JSON.toJSONString(list))
            res.put('message', "第三方接口返回NULL数据")
            return res
        }

        String resultJSONStr = new String(result, StandardCharsets.UTF_8)
        JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)

        if (Objects.isNull(resultJSON) || !resultJSON.containsKey("errcode")
                || !Objects.equals(API_SUCCESS_CODE, resultJSON.getString("errcode"))) {
            LoggerUtil.error("调用第三方接口失败:{0},请求url:{1},请求参数:{2}", null, url, JSON.toJSONString(mapBody))
            //获取业务异常
            res.put('message', resultJSON.getString('msg'))
            return res
        }
        return resultJSON
    }

    /**
     * 会员信息转换
     * @param response
     * @param jsonData
     */
    private static memberInfoResultMapping(ChannelMemberResponse response, JSONObject jsonData) {
        JSONObject dataObj = jsonData.getJSONObject("data")
        // 基础信息
        response.setName(dataObj.getString("name"))
        response.setCardNo(dataObj.getString("mobile"))
        response.setMemberCode(dataObj.getString("mobile"))
        response.setMemberLevelName(dataObj.getString("memberLevelName"))
        response.setCreditBalance(dataObj.getInteger("point"))
        response.setMobile(dataObj.getString("mobile"))
        response.setGrantDate(dataObj.getString("beginTime"))

        // 优惠券信息
        List<Coupon> couponList = Lists.newArrayList()
        response.setCoupons(couponList)

        JSONArray couponJsonArray = dataObj.getJSONArray("couponList")
        if (couponJsonArray.size() > 0) {
            for (int i = 0; i < couponJsonArray.size(); i++) {
                Coupon coupon = new Coupon();
                couponList.add(coupon)

                JSONObject obj = couponJsonArray.getJSONObject(i)
                coupon.setId(obj.getString("cardno"))
                coupon.setCodeNo(obj.getString("cardpass"))
                coupon.setCode(obj.getString("cardpass"))
                coupon.setName(obj.getString("title"))
                coupon.setType(obj.getString("batchId"))
                // 第三方卡券类型说明: "A":"兑换券（兑换单张票）"; "C": "抵用券（订单立减）"; "P": "打折券（订单折扣）";"W": "微信现金券"
                coupon.setTypeCode(obj.getString("cardtype"))
                coupon.setExpiredDate(obj.getString("endtime"))
                // 单次券
                coupon.setUseType("ONCE")
                // 外部返回的优惠券都是可用的,已过期的优惠券不会返回给我们
                coupon.setStatus(NumberUtils.BYTE_ZERO)
            }
        }
    }

    /**
     * 转换商品信息
     * @param products
     * @return
     */
    private static String converterGoods(List<Product> products) {
        if (CollectionUtils.isEmpty(products)) {
            return ""
        }

        List<Map<String, Object>> goodsList = Lists.newArrayList()
        for (Product product : products) {
            Map<String, Object> goodsInfoMap = new HashMap<>()
            goodsInfoMap.put("goodsName", product.getName())
            goodsInfoMap.put("category", product.getCategory())
            goodsInfoMap.put("amount", product.getPrice())
            goodsList.add(goodsInfoMap)
        }

        return JSON.toJSONString(goodsList)
    }

    private String getMethodFullName(String method) {
        return channel.getChannelCode() + "." + getModuleName() + "." + method
    }

    private String getRequestUrl(String urlSign) {
        return DOMAIN.concat(urlSign)
    }
}
