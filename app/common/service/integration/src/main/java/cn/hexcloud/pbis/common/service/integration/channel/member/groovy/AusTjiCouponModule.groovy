package cn.hexcloud.pbis.common.service.integration.channel.member.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Coupon
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.CouponAmount
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.CouponReq
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.SkuDetail
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.MemberModule
import cn.hexcloud.pbis.common.service.integration.tji.*
import cn.hexcloud.pbis.common.service.integration.tji.client.TjiCouponClient
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.google.common.collect.Lists

import java.text.SimpleDateFormat

/**
 * 谭仔卡券（澳洲门店）
 *
 * <AUTHOR> Wang
 */
class AusTjiCouponModule extends AbstractExternalChannelModule implements MemberModule {

    private static final String SUCCESS_CODE = "200"

    AusTjiCouponModule(ExternalChannel channel) {
        super(channel)
    }

    @Override
    protected String getSignModuleName() {
        return this.getModuleName()
    }

    @Override
    String getModuleName() {
        return "Member"
    }

    @Override
    ChannelMemberResponse getMember(ChannelMemberRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("getMember"))
    }

    @Override
    ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request) {
        String methodFullName = getFullMethodName("getCouponInfo")

        // 设置响应参数
        ChannelCouponInfoResponse response = new ChannelCouponInfoResponse()
        response.setChannel(request.getChannel())

        // 请求参数
        String codeNo = request.getCoupons().get(0).getCodeNo()

        // 设置请求参数
        Coupon coupon = queryCouponDetail(methodFullName, codeNo)

        // 解析coupon
        List<Coupon> couponList = Lists.asList(coupon)
        response.setSuccess(true)
        response.setResponseCode("200")
        response.setDetails(couponList)
        return response
    }

    @Override
    CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("calculate"))
    }

    @Override
    ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest request) {
        String methodFullName = getFullMethodName("consumeCoupons")
        // 参数：ticket_no
        String ticketNo = request.getOrderContent().getTableNo()

        // 参数：store_code
        String storeCode = request.getStoreCode()

        // 参数：卡券信息
        boolean success = true
        StringBuffer errMsgBF = new StringBuffer()
        List<Coupon> couponList = new ArrayList<>()
        List<CouponReq> couponReqList = request.getCoupons()
        for (CouponReq couponReq : couponReqList) {
            String couponNo = couponReq.getCodeNo()
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd HH:mm")
            String currentTime = simpleDateFormat.format(new Date())
            String[] currentTimes = currentTime.split(" ")
            String optDate = currentTimes[0]
            String optTime = currentTimes[1]

            // 设置请求参数
            ConsumeRequest consumeRequest = ConsumeRequest
                    .newBuilder()
                    .setLang("en-US")
                    .setCouponNo(couponNo)
                    .setShopNo(storeCode)
                    .setBillNo(ticketNo)
                    .setOptDate(optDate)
                    .setOptTime(optTime)
                    .build()

            String requestJSON = consumeRequest.toString()
            LoggerUtil.info("{0} is sending message: {1}.", methodFullName, requestJSON)

            // 请求
            TjiCouponClient tjiCouponClient = SpringContextUtil.bean(TjiCouponClient.class)
            CommonResponse commonResponse = tjiCouponClient.consume(consumeRequest)
            String responseJSON = commonResponse.toString()
            LoggerUtil.info("{0} received message: {1}.", methodFullName, responseJSON)

            // 设置返回参数
            String bizCode = commonResponse.getCode()
            if (SUCCESS_CODE != bizCode) {
                errMsgBF.append(commonResponse.getMessage())
                success = false
            }

            // 成功后查询对应的卡券信息
            Coupon coupon = queryCouponDetail(methodFullName, couponNo)
            couponList.add(coupon)
        }

        ChannelConsumeCouponsResponse response = new ChannelConsumeCouponsResponse()
        response.setSuccess(success)
        response.setMessage(errMsgBF.toString())
        response.setErrorMessage(errMsgBF.toString())
        response.setResponseCode("200")
        response.setChannel(request.getChannel())
        response.setCouponDetails(couponList)
        return response
    }

    @Override
    ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest request) {
        String methodFullName = getFullMethodName("cancelCoupons")

        boolean success = true
        StringBuffer errMsgBF = new StringBuffer()
        List<CouponReq> couponReqList = request.getCoupons()
        for (CouponReq couponReq : couponReqList) {
            String couponNo = couponReq.getCodeNo()

            // 设置请求参数
            CancelRequest cancelRequest = CancelRequest
                    .newBuilder()
                    .setCouponNo(couponNo)
                    .setLang("en-US")
                    .build()

            String requestJSON = cancelRequest.toString()
            LoggerUtil.info("{0} is sending message: {1}.", methodFullName, requestJSON)

            // 请求
            TjiCouponClient tjiCouponClient = SpringContextUtil.bean(TjiCouponClient.class)
            CommonResponse commonResponse = tjiCouponClient.cancel(cancelRequest)
            String responseJSON = commonResponse.toString()
            LoggerUtil.info("{0} received message: {1}.", methodFullName, responseJSON)

            // 设置返回参数
            String bizCode = commonResponse.getCode()
            if (SUCCESS_CODE != bizCode) {
                errMsgBF.append(commonResponse.getMessage())
                success = false
            }
        }

        ChannelCancelCouponsResponse response = new ChannelCancelCouponsResponse()
        response.setSuccess(success)
        response.setMessage(errMsgBF.toString())
        response.setErrorMessage(errMsgBF.toString())
        response.setResponseCode("200")
        response.setChannel(request.getChannel())
        return response
    }

    // 查询卡券详情
    private static Coupon queryCouponDetail(String methodFullName, String couponCode) {
        // 设置请求参数
        QueryRequest queryReq = QueryRequest
                .newBuilder()
                .setLang("en-US")
                .setCouponNo(couponCode)
                .build()
        String requestJSON = queryReq.toString()
        LoggerUtil.info("{0}->queryCouponDetail is sending message: {1}.", methodFullName, requestJSON)

        // 请求
        TjiCouponClient tjiCouponClient = SpringContextUtil.bean(TjiCouponClient.class)
        CommonResponse commonResponse = tjiCouponClient.query(queryReq)
        String responseJSON = commonResponse.toString()
        LoggerUtil.info("{0}->queryCouponDetail received message: {1}.", methodFullName, responseJSON)

        // 状态不成功时，报错
        String bizCode = commonResponse.getCode()
        String errorMessage = commonResponse.getMessage()
        if (SUCCESS_CODE != bizCode) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED_EN, errorMessage)
        }

        // 卡券不存在时，报错
        TjiCoupon tjiCoupon = commonResponse.getData()
        if (tjiCoupon == null) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED_EN, "The coupon doesn't exist")
        }

        // 返回结果
        return parseCoupon(tjiCoupon)
    }

    // 解析券信息
    private static Coupon parseCoupon(TjiCoupon tjiCoupon) {
        Coupon coupon = new Coupon()
        coupon.setId(tjiCoupon.getCouponNo()) // HEX券类别(coupon_type)ID
        coupon.setCodeNo(tjiCoupon.getCouponNo()) // 加密券码
        coupon.setStatus(tjiCoupon.getStatus()) // 表示券可用
        coupon.setType("NORMAL") // 表示非会员券
        coupon.setTypeCode(tjiCoupon.getAssignNo()) // 会员券(MEMBERSHIP)或非会员券(NORMAL)(seltek:MEMBERSHIP)
        coupon.setUseType("ONCE") // 表示单次券（只能使用一次）
        coupon.setParValue(tjiCoupon.getAmount().toDouble()) // 券面值
        coupon.setName(tjiCoupon.getSnType())
        coupon.setExpiredDate(tjiCoupon.getExpireTime())
        SkuDetail skuDetail = new SkuDetail()
        skuDetail.setCount(0L)
        skuDetail.setSkus(new ArrayList<>())
        coupon.setSkuDetail(skuDetail)
        coupon.setCouponTypeId(1)

        // 金额信息
        CouponAmount couponAmount = new CouponAmount()
        couponAmount.setPrice(0) // 券售价
        couponAmount.setPayAmount(0) // 用户实付金额
        couponAmount.setDiscountOnMerchant(0) // 商家承担的券售价折扣金额
        couponAmount.setDiscountOnPlatform(0) // 平台承担的券售价折扣金额
        couponAmount.setParValue(tjiCoupon.getAmount()) // 券面值

        coupon.setCouponAmount(couponAmount)
        return coupon
    }

    private String getMethodFullName(String method) {
        return channel.getChannelCode() + "." + getModuleName() + "." + method
    }
}
