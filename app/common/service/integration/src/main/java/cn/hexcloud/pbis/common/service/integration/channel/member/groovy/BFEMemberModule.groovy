package cn.hexcloud.pbis.common.service.integration.channel.member.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Coupon
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.DepositCard
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.CalculatePromotionRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCancelCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelConsumeCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCouponInfoRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelMemberRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.CalculatePromotionResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCancelCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelConsumeCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCouponInfoResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelMemberResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.MemberModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils
import org.springframework.util.DigestUtils

import java.nio.charset.StandardCharsets
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.stream.Collectors

/**
 * @Classname BFEMember* @Description:
 * @Date 2021/11/111:40 上午
 * <AUTHOR>
 */
class BFEMemberModule extends AbstractExternalChannelModule implements MemberModule {

  BFEMemberModule(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Member"
  }

  private String gatewayUrl(){
    String[] baseUrl = channel.getChannelAccessConfig().getGatewayUrl().split("&")
    return baseUrl[0]
  }

  @Override
  ChannelMemberResponse getMember(ChannelMemberRequest channelMemberRequest) {
    String url = "";
    Map<String, String> body = new HashMap<>()
    if (StringUtils.isEmpty(channelMemberRequest.getMobile())) {
      url = gatewayUrl() + "/user/memberInfoByPayCode"
      body.put("payCode", channelMemberRequest.getCardNo())
    } else {
      url = gatewayUrl() + "/user/memberInfoByPhone"
      body.put("mobile", channelMemberRequest.getMobile())
    }

    // 请求参数
    body.put("storeCode", channelMemberRequest.getStoreCode())

    // 添加时区
    buildTimeStr(body)

    String signStr = getSignature(body);
    body.put("sign", signStr)
    String jsonStr = JSON.toJSONString(body)
    // 发起HTTP请求

    LoggerUtil.info("BFEMember.getMember is sending message: {0}, signature: {1}.", jsonStr, signStr)
    byte[] result = HttpUtil.doPost(url, jsonStr, getRequestHeader())
    if (null == result) {
      LoggerUtil.error("BFEMember.getMember is failed with null result.", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("BFEMember.getMember received message: {0}.", resultJSONStr)
    if (StringUtils.isEmpty(resultJSON.getString("status")) || !"success".equals(resultJSON.get("status"))) {
      // 请求失败
      LoggerUtil.error("BFEMember.getMember is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("errorCode"),
          resultJSON.getString("errorMsg"), resultJSON.getString("data"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED,resultJSON.getString("errorMsg"))
    }
    resultJSON = resultJSON.getJSONObject("data")

    if (resultJSON == null) {
      // 请求失败
      LoggerUtil.error("BFEMember.getMember is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("errorCode"),
          resultJSON.getString("errorMsg"), resultJSON.getString("data"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    ChannelMemberResponse channelMemberResponse = new ChannelMemberResponse()
    channelMemberResponse.setCardNo(channelMemberRequest.getCardNo())
    channelMemberResponse.setGradeName(resultJSON.getString("level"))

    channelMemberResponse.setAccountBalance(resultJSON.getIntValue("balance"))

    channelMemberResponse.setCreditBalance(resultJSON.getIntValue("points"))
    channelMemberResponse.setName(resultJSON.getString("nickname"))
    channelMemberResponse.setMobile(resultJSON.getString("mobile"))

    // 卡券信息
    List<Coupon> coupons = new ArrayList<>()

    JSONArray jsonArray = (JSONArray) resultJSON.get("coupons")
    if (jsonArray.size() != 0) {
      for (int i = 0; i < jsonArray.size(); i++) {
        JSONObject jsonObject = (JSONObject) jsonArray.get(i)
        Coupon coupon = new Coupon()
        coupon.setCode(jsonObject.getString("id"))
        coupon.setId(jsonObject.getString("id"))
        coupon.setName(jsonObject.getString("name"))
        coupon.setTypeCode("0")
        coupon.setType(jsonObject.getString("templateId"))
        coupons.add(coupon)
      }
    }
    channelMemberResponse.setCoupons(coupons)

    // 储值卡信息
    List<DepositCard> depositCards = new ArrayList<>()
    DepositCard card = new DepositCard()

    card.setAmount(resultJSON.getIntValue("balance"))

    String payCode = resultJSON.getString("payCode")

    if (StringUtils.isEmpty(payCode)) {
      payCode = channelMemberRequest.getCardNo()
    }

    card.setCardCode(payCode)

    depositCards.add(card)
    channelMemberResponse.setDepositCard(depositCards)
    return channelMemberResponse;
  }

  private static void buildTimeStr(HashMap<String, String> body) {
    LocalDateTime localDateTime = LocalDateTime.now()
        .atZone(ZoneId.systemDefault()).toLocalDateTime()
    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    String str = localDateTime.format(dateTimeFormatter)
    body.put("timestamp", str)
  }


  private static String getTimeStr() {
    LocalDateTime localDateTime = LocalDateTime.now()
        .atZone(ZoneId.systemDefault()).toLocalDateTime()
    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    String str = localDateTime.format(dateTimeFormatter)
    return str
  }

  private Map<String, String> getRequestHeader() {
    Map<String, String> header = new HashMap<>()
    header.put("Content-Type", "application/json")
    return header
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    String[] keys = (String[]) rawMessage.keySet().toArray(new String[0])
    Arrays.sort(keys)
    StringBuilder query = new StringBuilder()

    String[] var6 = keys
    int var7 = keys.length

    for (int var8 = 0; var8 < var7; ++var8) {
      String key = var6[var8]
      String value = (String) rawMessage.get(key)
      if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)
          && !key.equals("sign")) {
        if (var8 == var7 - 1) {
          query.append(key).append("=").append(value)
        } else {
          query.append(key).append("=").append(value).append("&")
        }
      }
    }

    query.append(channel.getChannelAccessConfig().getAppKey())
    return DigestUtils.md5DigestAsHex(query.toString().getBytes(StandardCharsets.UTF_8)).toUpperCase()
  }


  @Override
  ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "BFEMember.getCouponInfo")
  }

  @Override
  CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "BFEMember.calculatePromotion")
  }

  @Override
  ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest channelConsumeCouponsRequest) {
    String url = gatewayUrl() + "/coupon/writeOff"
    List<String> couponIds = channelConsumeCouponsRequest.getCoupons().stream()
        .map({ x -> x.getCodeNo() }).collect(Collectors.toList())
// 请求参数
    Map<String, String> body = new HashMap<>()
    body.put("orderId", channelConsumeCouponsRequest.getOrderContent().getOrderTicketId())
    // 添加时区
    buildTimeStr(body)

    String signStr = getSignature(body);
    body.put("sign", signStr)
    body.put("couponIds", couponIds)
    String jsonStr = JSON.toJSONString(body);
    // 发起HTTP请求
    LoggerUtil.info("BFEMember.consumeCoupon is sending message: {0}, signature: {1}.", jsonStr, signStr)
    byte[] result = HttpUtil.doPost(url, jsonStr, getRequestHeader())
    if (null == result) {
      LoggerUtil.error("BFEMember.consumeCoupon is failed with null result.", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSON.toJSONString(body))
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("BFEMember.consumeCoupon received message: {0}.", resultJSONStr)

    if (StringUtils.isEmpty(resultJSON.getString("status"))) {
      // 请求失败
      LoggerUtil.error("BFEMember.consumeCoupon is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("errorCode"),
          resultJSON.getString("errorMsg"), resultJSON.getString("data"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }

    ChannelConsumeCouponsResponse consumeCouponsResponse = new ChannelConsumeCouponsResponse()

    if ("success".equals(resultJSON.getString("status"))) {
      consumeCouponsResponse.setSuccess(true)
      consumeCouponsResponse.setResponseCode(resultJSON.getString("errorCode"))
      consumeCouponsResponse.setResponseContent(resultJSON.getString("message"))
      consumeCouponsResponse.setMessage(resultJSON.getString("message"))
    } else {
      consumeCouponsResponse.setErrorCode(resultJSON.getString("errorCode"))
      consumeCouponsResponse.setResponseContent(resultJSON.getString("errorMsg"))
      consumeCouponsResponse.setMessage(resultJSON.getString("errorMsg"))
    }
    return consumeCouponsResponse

  }

  @Override
  ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest cancelCouponsRequest) {
    String url = gatewayUrl() + "/coupon/reverseWriteOff"
    List<String> couponIds = cancelCouponsRequest.getCoupons().stream()
        .map({ x -> x.getCodeNo() }).collect(Collectors.toList())

    // 请求参数
    Map<String, String> body = new HashMap<>()
    body.put("orderId", cancelCouponsRequest.getOrderContent().getOrderTicketId())
    // 添加时区
    buildTimeStr(body)
    String signStr = getSignature(body)
    body.put("couponIds", couponIds)
    body.put("sign", signStr)
    String jsonStr = JSON.toJSONString(body)

    // 发起HTTP请求
    LoggerUtil.info("BFEMember.cancelCoupons is sending message: {0}, signature: {1}.", jsonStr, signStr)
    byte[] result = HttpUtil.doPost(url, jsonStr, getRequestHeader())
    if (null == result) {
      LoggerUtil.error("BFEMember.cancelCoupons is failed with null result.", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)
    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSON.toJSONString(body))
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("BFEMember.cancelCoupons received message: {0}.", resultJSONStr)
    if (StringUtils.isEmpty(resultJSON.getString("status"))) {
      // 请求失败
      LoggerUtil.error("BFEMember.cancelCoupons is failed, errorCode: {0}, errorMsg: {1},data:{2}",
          null, resultJSON.getString("errorCode"),
          resultJSON.getString("errorMsg"), resultJSON.getString("data"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    ChannelCancelCouponsResponse cancelCouponsResponse = new ChannelCancelCouponsResponse()
    if ("success".equals(resultJSON.getString("status"))) {
      cancelCouponsResponse.setSuccess(true)
      cancelCouponsResponse.setResponseCode(resultJSON.getString("errorCode"))
      cancelCouponsResponse.setResponseContent(resultJSON.getString("message"))
      cancelCouponsResponse.setMessage(resultJSON.getString("message"))
    } else {
      cancelCouponsResponse.setErrorCode(resultJSON.getString("errorCode"))
      cancelCouponsResponse.setResponseContent(resultJSON.getString("errorMsg"))
      cancelCouponsResponse.setMessage(resultJSON.getString("errorMsg"))
    }
    return cancelCouponsResponse

  }

  /**
   * 递归查询
   *
   * @param jsonMap
   * @param jsonStr
   */
  static void parseJSON2Map(Map jsonMap, String jsonStr) {
    //字符串转换成JSON对象
    JSONObject json = JSON.parseObject(jsonStr);
    //最外层JSON解析
    for (Object k : json.keySet()) {
      Object v = json.get(k);

      if (v instanceof JSONArray) {
        //如果内层还是数组的话，继续解析
        List listV = JSONArray.toJavaObject((JSONArray) v, List.class);
        if (listV.size() == 0) {
          continue
        }
        Object o = listV.get(0);
        if (o instanceof JSONArray || isNested(v)) {
          parseJSON2Map(jsonMap, listV.get(0).toString());
        }
      } else if (isNested(v)) {
        parseJSON2Map(jsonMap, v.toString());
      } else {
        Object o = jsonMap.get(k.toString());
        if (o != null && o.toString() > v.toString()) {
          v = o
        }
        if (v.equals(0) || v.equals(false) || v.equals("") || v.equals(0.0)) {
          continue;
        }
        String s = v.toString();
        if (s.endsWith(".0")) {
          s = s.substring(0, s.length() - 2);
          jsonMap.put(k.toString(), s)
        } else {
          jsonMap.put(k.toString(), v)
        }
      }
    }
  }

  static boolean isNested(Object jsonObj) {
    return jsonObj.toString().contains("{");
  }
}