package cn.hexcloud.pbis.common.service.integration.channel.member.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.commons.utils.RedisUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Coupon
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.CouponAmount
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.SkuDetail
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.MemberModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import cn.hexcloud.pbis.common.util.exception.ThirdPartyRetryException
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils
import org.apache.http.NameValuePair
import org.apache.http.message.BasicNameValuePair
import org.springframework.util.CollectionUtils

import java.nio.charset.StandardCharsets
import java.sql.Timestamp
import java.util.concurrent.TimeUnit

class DouyinISVCouponModule extends AbstractExternalChannelModule implements MemberModule {

  // 指客户端和服务器建立连接的timeout
  private static final Integer CONN_TIME_OUT = 10000
  // 从连接池获取连接的timeout
  private static final Integer CONN_REQUEST_TIMEOUT = 10000
  // 指客户端从服务器读取数据的timeout
  private static final Integer SOCKET_TIMEOUT = 10000
  private static final String SUCCESS_CODE = "0"
  private static final Map<String, String> URL_MAP

  static {
    URL_MAP = new HashMap<>()
    URL_MAP.put("getCouponInfo", "coupon_info_url")
    URL_MAP.put("verifyCoupon", "coupon_verify_url")
    URL_MAP.put("consumeCoupons", "coupon_consume_url")
    URL_MAP.put("cancelCoupons", "coupon_cancel_url")
    URL_MAP.put("getAccessToken", "get_access_token_url")
  }

  DouyinISVCouponModule(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Member"
  }

  @Override
  ChannelMemberResponse getMember(ChannelMemberRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("cancel"))
  }

  @Override
  ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request) {
    List<Coupon> couponList = new ArrayList<>(request.getCoupons().size())

    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    String code = request.getCoupons().get(0).getCodeNo()
    if (code.startsWith("http") || code.startsWith("https")) {
      // 扫码识别方式，券码为短链接形式
      bizParams.put("encrypted_data", getCouponCodeFromURL(code))
    } else {
      // 手动输入方式，券码为明文形式
      bizParams.put("code", code)
    }

    // 发起请求
    JSONObject resultJSON = doRequest("verifyCoupon", "GET", bizParams, false, false)

    // 解析并返回结果
    JSONObject dataNode = resultJSON.getJSONObject("data")
    JSONArray certificatesArray = dataNode.getJSONArray("certificates") // 用户所有可用券信息
    certificatesArray.forEach({ ce -> couponList.add(parseCoupon((JSONObject) ce)) })
    ChannelCouponInfoResponse response = new ChannelCouponInfoResponse()
    response.setSuccess(true)
    response.setResponseCode("200")
    response.setChannel(request.getChannel())
    response.setConsumeToken(dataNode.getString("verify_token"))
    response.setDetails(couponList)
    return response
  }

  @Override
  CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("cancel"))
  }

  @Override
  ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest request) {
    List<Coupon> couponList = new ArrayList<>(request.getCoupons().size())
    List<String> couponCodeList = new ArrayList<>(request.getCoupons().collect { x -> x.getCodeNo() })

    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("verify_token", request.getConsumeToken())
    bizParams.put("encrypted_codes", couponCodeList)
    bizParams.put("poi_id", getDouyinPoiId(ServiceContext.getString(ContextKeyConstant.STORE_ID)))

    // 发起请求
    JSONObject resultJSON = doRequest("consumeCoupons", "POST", bizParams, true, false)

    // 解析并返回结果
    boolean success = true
    String errorMessage = "success"
    JSONArray verifyResultsNode = resultJSON.getJSONObject("data").getJSONArray("verify_results")
    verifyResultsNode.forEach({ r ->
      JSONObject verifyResultObject = (JSONObject) r
      if (verifyResultObject.getString("result") == SUCCESS_CODE) {
        request.getCoupons().find({ c ->
          if (c.getCodeNo() == verifyResultObject.getString("code")) {
            // 核销成功，查询券信息
            Coupon coupon = queryCouponDetail(c.getCodeNo())
            // 抖音在核销成功后立即查券可能不会返回券id和核销流水号，通过核销的返回报文获取这两个字段
            coupon.setId(verifyResultObject.getString("certificate_id"))
            coupon.setConsumeTransactionId(verifyResultObject.getString("verify_id"))
            // 如果抖音次卡，覆盖CardNo
            if ("3" == c.getTypeId()) {
              coupon.setCodeNo(coupon.getConsumeTransactionId())
            }
            couponList.add(coupon)
          }
        })
      } else {
        // 一张券核销失败则标记整个响应结果为失败
        success = false
        errorMessage = verifyResultObject.getString("msg")
      }
    })

    ChannelConsumeCouponsResponse response = new ChannelConsumeCouponsResponse()
    response.setSuccess(success)
    response.setMessage(errorMessage)
    response.setErrorMessage(errorMessage)
    response.setResponseCode("200")
    response.setChannel(request.getChannel())
    response.setCouponDetails(couponList)
    return response
  }

  // https://partner.open-douyin.com/docs/resource/zh-CN/local-life/guide/guide
  @Override
  ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest request) {
    boolean success = true
    TransactionLoggerContext.setIfNotExists(ContextKeyConstant.REQUEST_TIME, DateUtil.getNowTimeStamp())
    String errorMessage = "success"
    request.getCoupons().forEach({ c ->
      // 请求参数
      Map<String, Object> bizParams = new HashMap<>()
      bizParams.put("verify_id", c.getConsumeTransactionId()) // 券核销流水号
      bizParams.put("certificate_id", c.getId()) // 券id（券核销后得到）
      // 发起请求
      JSONObject resultJSON = doRequest("cancelCoupons", "POST", bizParams, true, true)
      if (resultJSON.getJSONObject("data").getString("error_code") != SUCCESS_CODE) {
        // 一张券撤销失败则标记整个响应结果为失败
        success = false
        errorMessage = resultJSON.getJSONObject("data").getString("description")
      }
    })
    TransactionLoggerContext.setIfNotExists(ContextKeyConstant.RESPONSE_TIME, DateUtil.getNowTimeStamp())

    // 解析并返回结果
    ChannelCancelCouponsResponse response = new ChannelCancelCouponsResponse()
    response.setSuccess(success)
    response.setMessage(errorMessage)
    response.setErrorMessage(errorMessage)
    response.setResponseCode("200")
    response.setChannel(request.getChannel())
    return response
  }

  private Coupon queryCouponDetail(String couponCode) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("encrypted_code", URLEncoder.encode(couponCode, StandardCharsets.UTF_8.toString())) // 加密券码

    // 发起请求
    JSONObject resultJSON = doRequest("getCouponInfo", "GET", bizParams, true, false)

    // 返回结果
    return parseCoupon(resultJSON.getJSONObject("data").getJSONObject("certificate"))
  }

  // https://partner.open-douyin.com/docs/resource/zh-CN/local-life/guide/guide
  private static Coupon parseCoupon(JSONObject couponJSON) {
    Coupon coupon = new Coupon()
    coupon.setCodeNo(couponJSON.getString("encrypted_code")) // 加密券码
    coupon.setStatus(1) // 表示券可用
    coupon.setType("NORMAL") // 表示非会员券
    coupon.setUseType("ONCE") // 表示单次券（只能使用一次）
    coupon.setExpiredDate(couponJSON.getString("expire_time")) // 券有效期截止
    coupon.setSumLimit(1)
    if (couponJSON.containsKey("time_card")){
      JSONObject timeCardJSON = couponJSON.getJSONObject("time_card")
      coupon.setSumLimit(timeCardJSON.getIntValue("times_count"))
      coupon.setUsedQty(timeCardJSON.getIntValue("times_used"))
    }
    // 金额信息
    CouponAmount couponAmount = new CouponAmount()
    JSONObject amountJSON = couponJSON.getJSONObject("amount")
    if (amountJSON) {
      coupon.setRefAmount(String.valueOf(parseFenToYuan(amountJSON.getInteger("pay_amount")))) // 用户实付金额
      couponAmount.setPrice(amountJSON.getInteger("original_amount")) // 券售价
      couponAmount.setPayAmount(amountJSON.getInteger("pay_amount")) // 用户实付金额
      /** 兼容抖音报文缺失金额字段的情况 */
      // 商家承担的券售价折扣金额
      Integer merchantTicketAmount = amountJSON.containsKey("merchant_ticket_amount") ? amountJSON.getInteger("merchant_ticket_amount") : 0
      couponAmount.setDiscountOnMerchant(merchantTicketAmount)
      Integer paymentDiscountAmount = amountJSON.containsKey("payment_discount_amount") ? amountJSON.getInteger("payment_discount_amount") : 0
      couponAmount.setDiscountOnOthers(paymentDiscountAmount)
      // 计算平台分摊金额
      couponAmount.setDiscountOnPlatform(couponAmount.getPrice() - couponAmount.getPayAmount() - couponAmount.getDiscountOnMerchant() - couponAmount.getDiscountOnOthers())
    }

    // SKU信息
    JSONObject skuJSON = couponJSON.getJSONObject("sku")
    if (skuJSON) {
      coupon.setStartDate(skuJSON.getString("sold_start_time")) // 券有效期开始
      coupon.setParValue(parseFenToYuan(skuJSON.getInteger("market_price"))) // 券面值
      coupon.setName(skuJSON.getString("title")) // 券名称
      coupon.setCouponTypeId(parseCouponTypeId(skuJSON.getInteger("groupon_type"))) // 券类型
      couponAmount.setParValue(skuJSON.getInteger("market_price")) // 券面值
      SkuDetail skuDetail = new SkuDetail()
      List<String> skuIds = new ArrayList<>(1)
      skuIds.add(skuJSON.getString("third_sku_id"))
      skuDetail.setSkus(skuIds)
      coupon.setSkuDetail(skuDetail)
    }

    // 核销信息
    JSONObject verifyJSON = couponJSON.getJSONObject("verify")
    if (verifyJSON) {
      coupon.setId(verifyJSON.getString("certificate_id")) // 券id
      coupon.setConsumeTransactionId(verifyJSON.getString("verify_id")) // 券核销流水号
    }

    coupon.setCouponAmount(couponAmount)
    return coupon
  }

  private static String getCouponCodeFromURL(String couponURL) {
    // 抖音券通过扫码枪识别后为一个短链接，券码需访问短链接后得到其对应的长链接（抖音302跳转），然后解析object_id参数后得到
    // 抖音接口文档地址：https://bytedance.feishu.cn/docs/doccngzPY6tIPlPKfYkI8DU2prh#
    URL url = new URL(couponURL)
    HttpURLConnection conn = (HttpURLConnection) url.openConnection()
    conn.setReadTimeout(5000)
    conn.setConnectTimeout(5000)
    conn.setRequestMethod("OPTIONS")
    conn.getResponseCode() // 发起请求
    Map<String, String> urlPramsMap = getUrlParamsMap(conn.getURL().getQuery())
    String couponCode = urlPramsMap.get("object_id")
    if (StringUtils.isEmpty(couponCode)) {
      throw new CommonException(ServiceError.INVALID_COUPON_CODE)
    }

    return URLEncoder.encode(couponCode, StandardCharsets.UTF_8.toString())
  }

  private String getDouyinPoiId(String hexStoreId) {
    return channel.getChannelAccessSupportService().getExternalStoreId(channel.getChannelCode(), hexStoreId)
  }

  private static Map<String, String> getUrlParamsMap(String urlParams) {
    Map<String, String> paramsMap = new HashMap<>()
    if (StringUtils.isEmpty(urlParams)) {
      return paramsMap
    }

    String[] urlParamArr = urlParams.split("&")
    for (String urlParam : urlParamArr) {
      String[] paramPair = urlParam.split("=")
      if (paramPair.length > 1) {
        paramsMap.put(paramPair[0], URLDecoder.decode(paramPair[1], StandardCharsets.UTF_8.toString()))
      } else {
        paramsMap.put(paramPair[0], "")
      }
    }

    return paramsMap
  }

  private static BigDecimal parseFenToYuan(Integer fen) {
    if (null == fen) {
      return null
    }
    return new BigDecimal(String.valueOf((double) fen / 100))
  }

  private static Integer parseCouponTypeId(Integer tpCouponTypeId) {
    Integer couponTypeId = 0
    switch (tpCouponTypeId) {
      case 1:
        couponTypeId = 2 // 商品券
        break
      case 2:
        couponTypeId = 1 //代金券
        break
      case 3:
        couponTypeId = 3 //次卡
        break
    }
    return couponTypeId
  }

  private JSONObject doRequest(String method, String httpMethod, Map<String, Object> bizParams, boolean reserveData, boolean errorSilenced) {
    try {
      return doRequest(method, httpMethod, getAccessToken(true), bizParams, reserveData, errorSilenced)
    } catch (CommonException e) {
      if (e.getError().getCode() == ServiceError.THIRD_PARTY_INVALID_TOKEN.getCode()) {
        return doRequest(method, httpMethod, getAccessToken(false), bizParams, reserveData, errorSilenced)
      } else {
        throw e
      }
    }
  }

  private JSONObject doRequest(String method, String httpMethod, String clientToken, Map<String, Object> bizParams, boolean reserveData, boolean errorSilenced) {
    // 请求参数
    Map<String, Object> body = new HashMap<>()
    body.putAll(bizParams)

    // 发起HTTP请求
    String methodFullName = getMethodFullName(method)
    StringBuilder requestUrl = new StringBuilder()
    requestUrl.append(channel.getChannelAccessConfig().getProperty(URL_MAP.get(method)))
    Map<String, String> header = new HashMap<>()
    header.put("access-token", clientToken)
    String bodyJSONStr = JSON.toJSONString(body)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result
    if (httpMethod == "GET") {
      String requestUrlStr = requestUrl.toString()
      if (!CollectionUtils.isEmpty(bizParams)) {
        requestUrl.append("?")
        for (Map.Entry<String, Object> entry : bizParams) {
          if (entry.getValue()) {
            requestUrl.append(entry.getKey()).append("=").append(entry.getValue().toString()).append("&")
          }
        }
        requestUrlStr = requestUrl.substring(0, requestUrl.length() - 1)
      }
      LoggerUtil.info("{0} is sending message to: {1}.", methodFullName, requestUrlStr)
      result = HttpUtil.doGet(requestUrlStr, header)
    } else {
      LoggerUtil.info("{0} is sending message to: {1}, body: {2}.", methodFullName, requestUrl.toString(), bodyJSONStr)
      try {
        result = HttpUtil.doPost(requestUrl.toString(), bodyJSONStr, header, CONN_REQUEST_TIMEOUT, CONN_REQUEST_TIMEOUT, CONN_REQUEST_TIMEOUT)
      } catch (Exception ex) {
        LoggerUtil.error("request DouyinOperationISV error", ex)
        throw new ThirdPartyRetryException(ServiceError.THIRD_PARTY_RETRY_ERROR, "抖音卡券核销超时，请稍后重试")
      }
    }
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      if (!errorSilenced) {
        throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
      }
      return null
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.append(ContextKeyConstant.REQUEST_DATA, bodyJSONStr)
      TransactionLoggerContext.append(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.setIfNotExists(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.setIfNotExists(ContextKeyConstant.RESPONSE_TIME, respTime)
    }

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
    JSONObject dataNode = resultJSON.getJSONObject("data")
    String errorCode = dataNode.getString("error_code")
    String errorMessage = dataNode.getString("description")
    if (errorCode != SUCCESS_CODE) {
      // 请求失败
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, methodFullName, errorCode, errorMessage)
      // token过期
      if (errorCode == "2190008") {
        throw new CommonException(ServiceError.THIRD_PARTY_INVALID_TOKEN)
      }
      //抖音接口服务异常，抛出重试异常，发起重试
      //https://partner.open-douyin.com/docs/resource/zh-CN/dop/develop/openapi/life-service-open-ability/life.capacity/life.capacity.fulfilment/certificate.verify/
      if (errorCode == "2119002" || errorCode == "2119003" || errorCode == "2100004") {
        throw new ThirdPartyRetryException(ServiceError.THIRD_PARTY_RETRY_ERROR)
      }
      if (!errorSilenced) {
        throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
      }
    }

    return resultJSON
  }

  private String getAccessToken(boolean cacheFirst) {
    String redisKey = "TBIS-ACCESS-TOKEN:" + channel.getChannelCode()
    String accessToken
    if (cacheFirst) {
      accessToken = RedisUtil.StringOps.get(redisKey)
      if (StringUtils.isNotEmpty(accessToken)) {
        // 缓存中有accessToken，直接返回
        return accessToken
      }
    }

    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    List<NameValuePair> body = new ArrayList<>(3)
    body.add(new BasicNameValuePair("client_key", channelAccessConfig.getAppId()))
    body.add(new BasicNameValuePair("client_secret", channelAccessConfig.getAppKey()))
    body.add(new BasicNameValuePair("grant_type", "client_credential"))

    String method = "getAccessToken"
    String methodFullName = getMethodFullName(method)
    String requestUrl = channelAccessConfig.getProperty(URL_MAP.get(method))
    String bodyJSONStr = JSON.toJSONString(body)
    LoggerUtil.info("{0} is sending message to: {1}, body: {2}.", methodFullName, requestUrl, bodyJSONStr)
    byte[] result = HttpUtil.doPost(requestUrl, body, null)
    if (!result) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }

    String resultJSONStr = new String(result, StandardCharsets.UTF_8)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    JSONObject dataNode = resultJSON.getJSONObject("data")
    String errorCode = dataNode.getString("error_code")
    String errorMessage = dataNode.getString("description")
    if (errorCode == SUCCESS_CODE) {
      accessToken = dataNode.getString("access_token")
      Integer accessTokenExp = dataNode.getInteger("expires_in")
      if (accessToken && accessTokenExp) {
        // 将accessToken放入缓存，在第三方有效期的基础上减去300秒作为缓存的过期时间
        RedisUtil.StringOps.setEx(redisKey, accessToken, accessTokenExp - 300, TimeUnit.SECONDS)
      }
    } else {
      // 请求失败
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, methodFullName, errorCode, errorMessage)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }

    return accessToken
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }
}