package cn.hexcloud.pbis.common.service.integration.channel.member.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.commons.utils.RedisUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Coupon
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.CouponReq
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.DepositCard
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Member
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Order
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Product
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.CalculatePromotionRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCancelCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelConsumeCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCouponInfoRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelMemberRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.CalculatePromotionResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCancelCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelConsumeCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCouponInfoResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelMemberResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.MemberModule
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.util.RequestSignUtil
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import java.nio.charset.StandardCharsets
import java.util.concurrent.TimeUnit

/**
 * Created on 2021/11/3.
 * <AUTHOR>
 */
class FreemudMemberModule extends AbstractExternalChannelModule implements MemberModule {


  FreemudMemberModule(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Member"
  }

  @Override
  ChannelMemberResponse getMember(ChannelMemberRequest channelMemberRequest) {
    String memberId = "/member/getMemberInfoByIdOrMobile"

    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    String url = channelAccessConfig.getProperty("member_info_url") + memberId

    // requestBody拼接
    JSONObject requestJson = new JSONObject();

    String mobile = channelMemberRequest.getMobile()
    String memberCode = channelMemberRequest.getMemberCode()
    String cardCode = channelMemberRequest.getCardNo()

    if (memberCode == "") {

      if (mobile == "" && cardCode == "") {

        throw new CommonException(ServiceError.INVALID_SIGNATURE)

      }
      if (cardCode != "") {
        requestJson.put("dynamicCode", cardCode);
      }
      if (mobile != "") {
        requestJson.put("mobile", mobile);
      }
    } else {
      requestJson.put("memberId", memberCode);
    }

    String storeId = channelMemberRequest.getStoreCode()

    // 装填参数
    JSONObject postJson = new JSONObject();
    postJson.put("appId", channelAccessConfig.getAppId());
    postJson.put("partnerId", channelAccessConfig.getMerchantId());
    postJson.put("ver", 1);

    requestJson.put("storeId", storeId);
    requestJson.put("showCouponSwitch", 1);
    requestJson.put("showCardSwitch", 1);
    requestJson.put("showScoreSwitch", 1);
    String body = JSONObject.toJSONString(requestJson);
    postJson.put("requestBody", body);

    String sign = RequestSignUtil.signTopRequest(postJson, channelAccessConfig.getPrivateKey())
    postJson.put("sign", sign);
    String jsonStr = JSON.toJSONString(postJson)
    LoggerUtil.info("FreemudOperation.getMember is sending message: {0}.", jsonStr)
    Map<String, String> header = getRequestHeader()
    byte[] result = HttpUtil.doPost(url, jsonStr, header)
    if (null == result) {
      LoggerUtil.error("FreemudOperation.getMember is failed with null result.", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)

    LoggerUtil.info("FreemudOperation.getMember received message: {0}",resultJSONStr)

    String statusCode = String.valueOf(resultJSON.get("statusCode"))

    if ("100" != statusCode) {

      LoggerUtil.error("FreemudOperation.getMember is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("code"),
          resultJSON.getString("message"), resultJSON.getString("responseBody"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED,resultJSON.getString("message"))

    }
    String responseBody = (String) resultJSON.get("responseBody");
    if (responseBody == null) {
      LoggerUtil.error("FreemudOperation.getMember is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("code"),
          resultJSON.getString("message"), resultJSON.getString("responseBody"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    resultJSON = JSONObject.parseObject(responseBody);
    return memberInfo(resultJSON)


  }

  private ChannelMemberResponse memberInfo(JSONObject jsonResp) {
    ChannelMemberResponse channelMemberResponse = new ChannelMemberResponse()

    JSONArray coupons = (JSONArray) jsonResp.get("memberCoupons");
    JSONArray cards = (JSONArray) jsonResp.get("memberCards");

    channelMemberResponse.setCreditBalance(jsonResp.get("currentScore") as int)
    channelMemberResponse.setName(jsonResp.get("memberName") as String)
    channelMemberResponse.setMemberCode(jsonResp.get("memberId") as String)
    channelMemberResponse.setMobile(jsonResp.get("mobile") as String)

    if (cards != null) {

      int amount = 0;
      List<DepositCard> depositCard = new ArrayList<>()
      for (int i = 0; i < cards.size(); i++) {


        JSONObject card1 = (JSONObject) cards.get(i);
        if (card1 == null) {
          continue;
        }
        DepositCard card = new DepositCard();
        card.setAmount(card1.getIntValue("amount"))
        card.setApplyId(card1.getString("applyId"))
        card.setCardCode(card1.getString("cardCode"))
        card.setCardName(card1.getString("cardName"))
        card.setVamount(card1.getIntValue("vamount"))
        card.setHasExpireTIme(false)
        amount = amount + card1.getIntValue("amount") + card1.getIntValue("vamount");
        depositCard.add(card)
      }

      channelMemberResponse.setDepositCard(depositCard)
      channelMemberResponse.setAccountBalance(amount)
    }

    List<Coupon> coupons1 = new ArrayList<>()
    if (coupons != null) {

      for (int i = 0; i < coupons.size(); i++) {
        Coupon coupon = new Coupon()
        JSONObject o = (JSONObject) coupons.get(i);
        if (o.getInteger("status") != 0) {
          continue;
        }
        String cuoponType = o.getString("cuoponType");
        if ("1".equals(cuoponType) || "0".equals(cuoponType) || "3".equals(cuoponType)) {
          coupon.setName(o.getString("couponName"))
          coupon.setCode(o.getString("couponCode"))
          coupon.setType(o.getString("actCode"))
          coupon.setTypeCode(o.getString("cuoponType"))
          convert2CouponNew(coupon, o)
          coupon.setStatus(couponStatuConvert(o.getInteger("status")))
        }
        coupons1.add(coupon)
      }
    }
    channelMemberResponse.setCoupons(coupons1)
    return channelMemberResponse
  }

  private Integer couponStatuConvert(Integer couponStatu) {
    switch (couponStatu) {
    //未激活
      case -1:
        return 2;
    //可用(剔除过期)
      case 0:
        return 1;
    //已使用
      case 1:
        return 2;
    //部分使用
      case 2:
        return 1;
    //取消
      case 3:
        return 3;
    //已过期(根据生失效日期)
      case 6:
        return 2;
    }
    return 3;
  }

  static void convert2CouponNew(Coupon coupon, JSONObject o) {

    String data = (String) o.get("couponLimit");

    if (data == null) {

      return;
    }

    try {
      String[] strings = data.split("至");
      coupon.setStartDate(strings[0])
      String date = strings[1];
      String expiredDate = date.substring(0, date.indexOf("当"));
      coupon.setExpiredDate(expiredDate)
    } catch (Exception e) {
      e.printStackTrace();
    }

  }


  private Map<String, String> getRequestHeader() {
    Map<String, String> header = new HashMap<>()
    header.put("x-transaction-id", UUID.randomUUID().toString())
    header.put("Content-Type", "application/json")
    header.put("access_token", channel.getChannelAccessConfig().getAppKey());
    return header
  }


  @Override
  ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "FreemudOperation.getCouponInfo")
  }

  @Override
  CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "FreemudOperation.calculatePromotion")
  }

  @Override
  ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest couponsRequest) {
    JSONObject result = getOrderCode(couponsRequest);
    String orderCode = result.getString("orderCode");
    if (orderCode == null) {
      LoggerUtil.error("orderCode返回为空", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    Order orderContent = couponsRequest.getOrderContent()
    String orderTicketId = orderContent.getOrderTicketId()

    String redisKey = "coupon" + ":" + orderTicketId
    RedisUtil.StringOps.setEx(redisKey, orderCode, 36000L, TimeUnit.SECONDS)

    //核销
    return consume(orderCode, couponsRequest)

  }

  private ChannelConsumeCouponsResponse consume(String orderCode, ChannelConsumeCouponsRequest couponsRequest) {

    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

    String paySuccess = "/order/paysuccess"
    String url = channelAccessConfig.getProperty("member_info_url") + paySuccess

    //装填参数
    JSONObject postJson = new JSONObject()
    postJson.put("appId", channelAccessConfig.getAppId())
    postJson.put("partnerId", channelAccessConfig.getMerchantId())
    postJson.put("ver", 1)

    Order orderContent = couponsRequest.getOrderContent()

    JSONObject requestJson = new JSONObject()
    requestJson.put("orderCode", orderCode)
    requestJson.put("operator", "pos")
    requestJson.put("transNo", orderContent.getOrderTicketId())

    // 支付信息
    List<JSONObject> repPay = new ArrayList<>()
    JSONObject pay = new JSONObject()
    pay.put("payChannel", 10400)
    pay.put("payChannelName", "卡券支付")
    pay.put("payAmount", 0)
    pay.put("payTime", orderContent.getSalesTime())
    repPay.add(pay)
    postJson.put("requestBody", JSONObject.toJSONString(requestJson))

    String sign = RequestSignUtil.signTopRequest(postJson, channelAccessConfig.getPrivateKey())

    postJson.put("sign", sign)

    String jsonStr = JSON.toJSONString(postJson)

    LoggerUtil.info("FreemudOperation.consume is sending message: {0}.", jsonStr)

    byte[] result = HttpUtil.doPost(url, jsonStr, getRequestHeader())
    if (null == result) {
      LoggerUtil.error("FreemudOperation.consume is failed with null result.", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    LoggerUtil.info("FreemudOperation.consume received message: {0}.", resultJSONStr)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSON.toJSONString(postJson))
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)

    String statusCode = (String) resultJSON.get("statusCode");
    if ("100" != statusCode) {

      LoggerUtil.error("FreemudOperation.consume is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("code"),
          resultJSON.getString("message"), resultJSON.getString("responseBody"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED,resultJSON.getString("message"))

    }

    ChannelConsumeCouponsResponse couponsResponse = new ChannelConsumeCouponsResponse()
    couponsResponse.setSuccess(true)
    couponsResponse.setResponseContent(resultJSONStr)
    couponsResponse.setResponseCode(resultJSON.getString("statusCode"))
    couponsResponse.setMessage(resultJSON.getString("message"))
    return couponsResponse

  }


  private JSONObject getOrderCode(ChannelConsumeCouponsRequest couponsRequest) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

    String submit = "/order/v2/submit"
    String url = channelAccessConfig.getProperty("member_info_url") + submit

    //装填参数
    JSONObject postJson = new JSONObject()
    postJson.put("appId", channelAccessConfig.getAppId())
    postJson.put("partnerId", channelAccessConfig.getMerchantId())
    postJson.put("ver", 1)

    Order orderContent = couponsRequest.getOrderContent()
    Member memberContent = couponsRequest.getMemberContent()
    long originalAmount = orderContent.getGrossAmount()
    long actualPayAmount = orderContent.getNetAmount()
    long discountAmount = originalAmount - actualPayAmount

    JSONObject requestJson = new JSONObject()
    requestJson.put("actualPayAmount", actualPayAmount)
    String storeId = memberContent.getCardNo()
    requestJson.put("storeId", storeId)
    requestJson.put("storeName", storeId)
    requestJson.put("originalAmount", originalAmount);
    requestJson.put("deliverAmount", 0)
    requestJson.put("discountAmount", discountAmount);
    requestJson.put("canRefund", true)
    requestJson.put("orderClient", 15)
    requestJson.put("orderStatus", 2)
    requestJson.put("thirdOrderCode", orderContent.getOrderTicketId());
    requestJson.put("orderType", 1)
    requestJson.put("userId", memberContent.getMemberCode())
    requestJson.put("needBonus", "1")
    requestJson.put("payStatus", 1)

    JSONArray orderItemList = new JSONArray()
    List<Product> products = orderContent.getProducts()
    if (products) {
      Map<String, JSONObject> objectObjectHashMap = new HashMap<>()

      for (int i = 0; i < products.size(); i++) {
        Product product = products.get(i)
        JSONObject orderItemJson = new JSONObject()
        String code = product.getCode()
        JSONObject linkedHashMap = objectObjectHashMap.get(code)

        if (linkedHashMap != null) {

          Integer productQuantity = (Integer) linkedHashMap.get("productQuantity");
          linkedHashMap.put("productQuantity", productQuantity + 1);

        } else {
          orderItemJson.put("productSeq", 32131)
          orderItemJson.put("productId", code)
          orderItemJson.put("thirdProductId", code)
          orderItemJson.put("productName", product.getName())
          orderItemJson.put("productPrice", product.getPrice())
          orderItemJson.put("productQuantity", product.getQuantity())
          orderItemList.add(orderItemJson)
          objectObjectHashMap.put(code, orderItemJson);
        }
      }
    }
    requestJson.put("orderItemList", orderItemList);
    List<CouponReq> coupons = couponsRequest.getCoupons()
    JSONArray repCoupons = new JSONArray();

    if (coupons == null || coupons.size() == 0 || coupons.size() != 1) {
      LoggerUtil.error("待核销卡券数量异常", null)
      throw new CommonException(ServiceError.PARAM_CONSTRAINTS_VIOLATION)
    }

    for (int i = 0; i < coupons.size(); i++) {
      CouponReq couponReq = coupons.get(i)
      JSONObject repCoupon = new JSONObject();
      repCoupon.put("couponCode", couponReq.getCodeNo())
      repCoupon.put("cuoponType", couponReq.getTypeId())
      String extend = (String) couponReq.getExtend()
      if (extend == null || extend.equals("")) {
        repCoupon.put("discountAmount", 100);
      } else {
        repCoupon.put("discountAmount", Integer.parseInt(extend));
      }

      String typeId = couponReq.getTypeId()
      if ("0".equals(typeId)) {
        List<Product> products1 = couponReq.getProducts()

        if (products1 == null || products1.size() == 0) {
          LoggerUtil.error("该商品券商品信息不全", null)
          throw new CommonException(ServiceError.PARAM_CONSTRAINTS_VIOLATION)
        }
        JSONArray productLists = new JSONArray();

        for (int j = 0; j < products1.size(); j++) {
          JSONObject repProduct = new JSONObject()
          Product product = products1.get(j)
          repProduct.put("thirdProductId", product.getCode())
          repProduct.put("productQuantity", product.getQuantity())
          repProduct.put("productPrice", product.getPrice())
          repProduct.put("productCode", product.getCode())
          productLists.add(repProduct);
        }

        repCoupon.put("productList", productLists);
      }
      repCoupons.add(repCoupon)
    }
    requestJson.put("couponList", repCoupons)
    //支付
    JSONArray repPay = new JSONArray()
    requestJson.put("payList", repPay)
    return getOrderResponse(url, postJson, requestJson)
  }

  private JSONObject getOrderResponse(String url, JSONObject postJson, JSONObject requestJson) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    String body = JSONObject.toJSONString(requestJson)
    postJson.put("requestBody", body)

    String sign = RequestSignUtil.signTopRequest(postJson, channelAccessConfig.getPrivateKey())
    postJson.put("sign", sign)

    String jsonStr = JSON.toJSONString(postJson)
    LoggerUtil.info("FreemudOperation.getOrderResponse is sending message: {0}.", jsonStr)

    byte[] result = HttpUtil.doPost(url, jsonStr, getRequestHeader())
    if (null == result) {
      LoggerUtil.error("FreemudOperation.getOrderResponse is failed with null result.", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    LoggerUtil.info("FreemudOperation.getOrderResponse received message: {0}.", resultJSONStr)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSON.toJSONString(postJson))
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)

    String statusCode = (String) resultJSON.get("statusCode");
    if ("100" != statusCode) {

      LoggerUtil.error("FreemudOperation.getMember is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("code"),
          resultJSON.getString("message"), resultJSON.getString("responseBody"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED,resultJSON.getString("message"))

    }

    String responseBody = resultJSON.getString("responseBody");
    resultJSON = JSONObject.parseObject(responseBody)
    return resultJSON;

  }


  @Override
  ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest channelCancelCouponsRequest) {

    Order orderContent = channelCancelCouponsRequest.getOrderContent()
    String orderTicketId = orderContent.getOrderTicketId()

    String redisKey = "coupon" + ":" + orderTicketId
    String redisKey1 = "coupon" + ":" + orderTicketId + ":" + "REFUND"

    String orderCode = RedisUtil.StringOps.get(redisKey)
    RedisUtil.StringOps.setEx(redisKey1, orderCode, 3600L, TimeUnit.SECONDS)

    // 反核销
    return getCallback(orderCode, orderTicketId);

  }

  private ChannelCancelCouponsResponse getCallback(String orderCode, String orderTicketId) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    String cancel = "/order/v2/cancel"
    String url = channelAccessConfig.getProperty("member_info_url") + cancel

    // 装填参数
    JSONObject postJson = new JSONObject()
    postJson.put("appId", channelAccessConfig.getAppId())
    postJson.put("partnerId", channelAccessConfig.getMerchantId())
    postJson.put("ver", 1)
    // requestBody拼接
    JSONObject requestJson = new JSONObject();

    if (orderCode != null) {
      requestJson.put("orderCode", orderCode);
    }
    requestJson.put("thirdOrderCode", orderTicketId);
    requestJson.put("orderClient", "2")
    requestJson.put("operator", "NA")
    requestJson.put("reason", "退订")
    postJson.put("requestBody", JSONObject.toJSONString(requestJson))

    String sign = RequestSignUtil.signTopRequest(postJson, channelAccessConfig.getPrivateKey())
    postJson.put("sign", sign)

    String jsonStr = JSON.toJSONString(postJson)

    LoggerUtil.info("FreemudOperation.getCallback is sending message: {0}.", jsonStr)

    byte[] result = HttpUtil.doPost(url, jsonStr, getRequestHeader())
    if (null == result) {
      LoggerUtil.error("FreemudOperation.getCallback is failed with null result.", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    LoggerUtil.info("FreemudOperation.getCallback received message: {0}.", resultJSONStr)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSON.toJSONString(postJson))
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)

    String statusCode = (String) resultJSON.get("statusCode");
    if ("100" != statusCode) {

      LoggerUtil.error("FreemudOperation.getCallback is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("code"),
          resultJSON.getString("message"), resultJSON.getString("responseBody"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED,resultJSON.getString("message"))

    }

    ChannelCancelCouponsResponse couponsResponse = new ChannelCancelCouponsResponse()
    couponsResponse.setMessage(resultJSON.getString("message"))
    couponsResponse.setResponseCode(resultJSON.getString("statusCode"))
    couponsResponse.setSuccess(true)
    couponsResponse.setResponseContent(resultJSONStr)
    return couponsResponse
  }
}
