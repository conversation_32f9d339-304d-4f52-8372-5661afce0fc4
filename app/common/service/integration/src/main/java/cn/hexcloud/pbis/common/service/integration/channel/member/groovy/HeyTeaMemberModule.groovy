package cn.hexcloud.pbis.common.service.integration.channel.member.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.CalculatePromotionRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCancelCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelConsumeCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCouponInfoRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelMemberRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.CalculatePromotionResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCancelCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelConsumeCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCouponInfoResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelMemberResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.MemberModule
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.util.XichaAESUtil
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils


import java.nio.charset.StandardCharsets

/**
 * @Classname XichaMember* @Description:
 * @Date 2022/2/191:36 下午
 * <AUTHOR>
 */
class HeyTeaMemberModule extends AbstractExternalChannelModule implements MemberModule {
  HeyTeaMemberModule(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  ChannelMemberResponse getMember(ChannelMemberRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "XichaMember.getMember")
  }

  @Override
  ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "XichaMember.getCouponInfo")
  }

  @Override
  CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "XichaMember.calculatePromotion")
  }

  @Override
  ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "XichaMember.consumeCoupons")
  }

  @Override
  ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "XichaMember.cancelCoupons")
  }

  @Override
  String getModuleName() {
    return "Member"
  }


  private String getAccessToken() {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

    String clientId = channelAccessConfig.getAppId()
    String clientSecret = channelAccessConfig.getAccessKey()
    String key = channelAccessConfig.getAppKey()

    String clientSecretAESStr = XichaAESUtil.getClientSecretStr(clientSecret, key)
    String accessTokenUrl = channelAccessConfig.getGatewayUrl()
    Map<String,String> map = new HashMap<>()
    map.put("clientId",clientId)
    map.put("clientSecret",clientSecretAESStr)

    byte[] result = HttpUtil.doPost(accessTokenUrl,JSON.toJSONString(map))

    if (null == result) {
      LoggerUtil.error("请求喜茶获取token接口返回报文为空{0}", null, accessTokenUrl)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }

    String resultJSONStr = new String(result, StandardCharsets.UTF_8)
    LoggerUtil.info("请求喜茶获取token接口返回报文{0}", resultJSONStr)

    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    if (resultJSON.get('message') != "SUCCESS") {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    JSONObject data = resultJSON.get('data')

    if (null == result) {
      LoggerUtil.error("请求喜茶获取token接口返回token为空{0}", null, accessTokenUrl)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String accessToken = data.get("access_token")

    if (StringUtils.isBlank(accessToken)) {
      LoggerUtil.error("请求喜茶获取token接口返回token为空{0}", null, accessTokenUrl)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    return accessToken;
  }

  Map<String, String> getRequestHeader(String accessToken) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    Map<String, String> header = new HashMap<>()
    header.put("Content-Type", "application/json")
    header.put("access_token", accessToken)
    header.put("X-tenant_id", channelAccessConfig.getMerchantId())
    return header
  }

}
