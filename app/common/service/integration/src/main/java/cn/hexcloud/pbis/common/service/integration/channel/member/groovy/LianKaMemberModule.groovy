package cn.hexcloud.pbis.common.service.integration.channel.member.groovy

import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.CalculatePromotionRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCancelCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelConsumeCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCouponInfoRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelMemberRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.CalculatePromotionResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCancelCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelConsumeCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCouponInfoResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelMemberResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.MemberModule
import org.apache.commons.lang3.StringUtils
import org.springframework.util.DigestUtils

/**
 * @Classname LianKaMember* @Description:
 * @Date 2022/2/145:59 下午
 * <AUTHOR>
 */
class LianKaMemberModule extends AbstractExternalChannelModule implements MemberModule{
  LianKaMemberModule(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Member"
  }

  @Override
  ChannelMemberResponse getMember(ChannelMemberRequest request) {
    return null
  }

  @Override
  ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request) {
    return null
  }

  @Override
  CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request) {
    return null
  }

  @Override
  ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest request) {
    return null
  }

  @Override
  ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest request) {
    return null
  }

  Map<String, String> getRequestHeader(String sign) {
    Map<String, String> header = new HashMap<>()
    header.put("sign", sign)
    return header
  }

  static String signTopRequest(Map<String, String> params, String key1) throws Exception {

    String[] keys = (String[]) params.keySet().toArray(new String[0]);
    Arrays.sort(keys)
    StringBuilder query = new StringBuilder();

    String[] var6 = keys
    int var7 = keys.length

    for (int var8 = 0; var8 < var7; ++var8) {
      String key = var6[var8]
      String value = (String) params.get(key);
      if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)
          && !key.equals("sign")) {

        query.append(key).append("=").append(value).append("&");
      }
    }
    query.append("key=").append(key1);

    return DigestUtils.md5DigestAsHex(query.toString().getBytes("UTF-8"))
  }
}
