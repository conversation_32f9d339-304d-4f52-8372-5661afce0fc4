package cn.hexcloud.pbis.common.service.integration.channel.member.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.AbsorbedExpenses
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.ChargeDetail
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Coupon
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.CouponReq
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.DeliveryFee
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.GoodsDetail
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Order
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.PackageFee
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.PriceDiscount
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Product
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.PromotionInfo
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Rule
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.SummaryDiscount
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.CalculatePromotionRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCancelCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelConsumeCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCouponInfoRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelMemberRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.CalculatePromotionResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCancelCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelConsumeCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCouponInfoResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelMemberResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.MemberModule
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.client.M82CalculateClient
import cn.hexcloud.pbis.common.service.integration.m82.BackPromotionsIn
import cn.hexcloud.pbis.common.service.integration.m82.BackPromotionsOut
import cn.hexcloud.pbis.common.service.integration.m82.ChargeGoods
import cn.hexcloud.pbis.common.service.integration.m82.ChargeInfo
import cn.hexcloud.pbis.common.service.integration.m82.DiscountCalculateIn
import cn.hexcloud.pbis.common.service.integration.m82.DiscountCalculateOut
import cn.hexcloud.pbis.common.service.integration.m82.DiscountInfo
import cn.hexcloud.pbis.common.service.integration.m82.GoodsInfo
import cn.hexcloud.pbis.common.service.integration.m82.TotalDiscount
import cn.hexcloud.pbis.common.service.integration.m82.UsePromotionsIn
import cn.hexcloud.pbis.common.service.integration.m82.UsePromotionsOut
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import groovy.transform.PackageScope

import java.math.RoundingMode
import java.nio.charset.StandardCharsets

/**
 * @program: pbis* @author: miao* @create: 2021-12-15 14:03
 * */
class M82MemberModule extends AbstractExternalChannelModule implements MemberModule {

  private static final int API_SUCCESS_CODE = 0

  M82MemberModule(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Member"
  }

  /**
   * 发起请求
   * @param json
   * @param uri
   * @return
   */
  @PackageScope
  JSONObject doPost(String json, String uri) {
    JSONObject res = new JSONObject()
    res.put('status_code', 1)

    byte[] result = HttpUtil.doPost(uri, json.toString())
    if (null == result) {
      LoggerUtil.error("IDSMember.response is null {0}", null, json.toString())
      return res
    }

    String resultJSONStr = new String(result, StandardCharsets.UTF_8)
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    
    if (null == resultJSON || !resultJSON.containsKey("status_code")
        || resultJSON.getIntValue("status_code") != API_SUCCESS_CODE) {
      LoggerUtil.error("IDSMember.response error{0}", null, json.toString())
      //获取业务异常
      res.put('message', resultJSON?.getString('description'))
      return res
    }
    return resultJSON
  }


  @Override
  ChannelMemberResponse getMember(ChannelMemberRequest request) {
    ChannelMemberResponse response = new ChannelMemberResponse()
    response.setResponseCode("1")
    response.setSuccess(false)
    // 业务参数
    Map<String, String> params = new HashMap<>()
    params.put("store_code", request.getStoreCode())
    params.put("partner_id", Long.toString(request.getPartnerId()))
    if (StrUtil.isNotBlank(request.getMemberCode())) {
      params.put("member_code", request.getMemberCode())
    }
    if (StrUtil.isNotBlank(request.getMobile())) {
      params.put("mobile", request.getMobile())
    }
    if (StrUtil.isNotBlank(request.getCardNo())) {
      params.put("card_no", request.getCardNo())
    }
    LoggerUtil.info("IDSMember.getMember request{0}", JSON.toJSONString(params))
    JSONObject jsonData = doPost(JSON.toJSONString(params), channel.getChannelAccessConfig().getProperty("member_info_url"))
    LoggerUtil.info("IDSMember.getMember response{0}", JSON.toJSONString(jsonData))
    if (jsonData.getIntValue("status_code") != API_SUCCESS_CODE) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, jsonData.getString("message"))
    }
    //解析
    memberInfoResultMapping(response, jsonData)
    return response
  }

  static memberInfoResultMapping(ChannelMemberResponse response, JSONObject jsonData) {
    response.setResponseCode('0')
    response.setMessage('success')
    response.setSuccess(true)
    //用户信息
    JSONObject dataObject = jsonData.get("payload") as JSONObject
    if (!dataObject) {
      return
    }
    response.setAccountBalance(dataObject.getIntValue("account_balance"))
    response.setCreditBalance(dataObject.getIntValue("credit_balance"))
    response.setCardNo(dataObject.getString("card_no"))
    response.setMemberCode(dataObject.getString("member_code"))
    response.setMemberId(dataObject?.getString("member_id"))
    response.setName(dataObject.getString("name"))
    response.setGradeId(dataObject?.getString("grade_id"))
    response.setGradeName(dataObject?.getString("grade_name"))
    response.setMobile(dataObject.getString("mobile"))
    response.setAvatar(dataObject.getString("avatar"))
    response.setPaycode(dataObject.getString("paycode"))
    response.setGreetings(dataObject?.getString("greetings"))
    //卡券
    JSONArray array = dataObject.getJSONArray("coupons")
    if (!array || array.size() == 0) {
      return
    }
    List<Coupon> couponList = new ArrayList<>()
    array.eachWithIndex { it, i ->
      JSONObject jobject = (JSONObject) it
      Coupon coupon = new Coupon()
      //券码
      //M82 status 状态转换
      coupon.setStatus(0)
      coupon.setStartDate(jobject.getString("start_date"))
      coupon.setExpiredDate(jobject.getString("expired_date"))
      coupon.setCode(jobject.getString("coupon_id"))
      coupon.setCouponImgUrl(jobject?.getString("coupon_img"))
      coupon.setName(jobject.getString("coupon_name"))
      coupon.setType(jobject.getString("coupon_type_id"))
      coupon.setTypeCode(jobject?.getString("coupon_type_code"))
      coupon.setCouponChannelType(jobject.getIntValue("coupon_channel_type"))
      coupon.setIsMember("MEMBERSHIP")
      coupon.setUseType("ONCE")
      //券码规则
      Rule rule = new Rule()
      JSONObject jsonRule = jobject.get("rule") as JSONObject
      if (jsonRule) {
        rule.setAmount(jsonRule.getIntValue("amount"))
        rule.setCouponAmount(jsonRule.getIntValue("coupon_amount"))
        rule.setDiscount(jsonRule.getIntValue("discount"))
        rule.setQuantity(jsonRule.getIntValue("quantity"))
        rule.setProductId(jsonRule.getString("product_id"))
        rule.setPlusAmount(jsonRule.getIntValue("plus_amount"))
        rule.setBuyQuantity(jsonRule.getIntValue("buy_quantity"))
        rule.setBuyProductId(jsonRule.getString("buy_productId"))
        rule.setGiveQuantity(jsonRule.getIntValue("give_quantity"))
        rule.setGiveProductId(jsonRule.getString("give_product_id"))
        coupon.setRule(rule)
      }
      // 商户信息
      JSONObject merchant = jobject.get("merchant") as JSONObject
      if (merchant != null) {
        coupon.setPayPrice(merchant.getIntValue("pay_price"))
        coupon.setSellPrice(merchant.getIntValue("sell_price"))
        coupon.setOriginPrice(merchant.getIntValue("origin_price"))
      }
      //使用限制
      JSONObject limited = jobject.get("limited") as JSONObject
      if (limited != null) {
        coupon.setTodayLimit(limited.getIntValue("today_limit"))
        coupon.setSumLimit(limited.getIntValue("sum_limit"))
      }
      couponList.add(coupon)
    }
    response.setCoupons(couponList)
  }

  // 商品
  static List<GoodsInfo> mappingCalGoods(List<GoodsDetail> products) {
    List<GoodsInfo> goodsInfos = new ArrayList<>()
    if (products) {
      for (product in products) {
        GoodsInfo goodsInfo = GoodsInfo.newBuilder()
            .setPrice(String.valueOf(product.getPrice()))
            .setKeyId(product.getKeyId())
            .setAmt(product.getAmt())
            .setAccAmt(product.getAccAmt())
            .addAllCategories(product?.getCategories())
            .addAllAccies(mappingCalChargeGoods(product))
            .setQty(product.getQty())
            .setShopId(product.getShopId())
            .build()
        goodsInfos.add(goodsInfo)
      }
    }
    return goodsInfos
  }

  // 加料商品
  static List<ChargeGoods> mappingCalChargeGoods(GoodsDetail goodsDetail) {
    List<ChargeGoods> addGoods = new ArrayList<>()
    if (goodsDetail.getChargeGoods()) {
      for (chargeGood in goodsDetail.getChargeGoods()) {
        ChargeGoods chargeFoods = ChargeGoods.newBuilder()
            .setAmt(chargeGood.getAmt())
            .setPrice(chargeGood.getPrice())
            .setQty(chargeGood.getQty())
            .setKeyId(chargeGood.getKeyId())
            .setShopId(chargeGood.getShopId())
            .build()
        addGoods.add(chargeFoods)
      }
    }
    return addGoods
  }

  // 卡券
  static List<DiscountInfo> mappingCalCoupons(List<PromotionInfo> coupons) {
    List<DiscountInfo> discountInfos = new ArrayList<>()
    if (coupons) {
      for (coupon in coupons) {
        DiscountInfo discount = DiscountInfo.newBuilder()
            .setPromotionId(coupon.getPromotionId())
            .setPromotionType(Integer.parseInt(coupon.getPromotionType()))
            .build()
        discountInfos.add(discount)
      }
    }
    return discountInfos
  }

  // 卡券
  static List<ChargeDetail> mappingCalChargeInfo(List<ChargeInfo> chargeInfos) {
    List<ChargeDetail> details = new ArrayList<>()
    if (chargeInfos) {
      for (chargeInfo in chargeInfos) {
        ChargeDetail detail = new ChargeDetail()
        detail.setShopId(chargeInfo.getShopId())
        detail.setAmt(chargeInfo.getAmt())
        detail.setKeyId(chargeInfo.getKeyId())
        detail.setQty(chargeInfo.getQty())
        detail.setPrice(chargeInfo.getPrice())
        detail.setPriceDiscount(chargeInfo.getPriceDiscount())
        detail.setDiscountGoodsNum(chargeInfo.getDiscountGoodsNum())
        details.add(detail)
      }
    }
    return details
  }

  /**
   * 算价
   * @param request
   * @return
   */
  @Override
  CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request) {
    CalculatePromotionResponse response = new CalculatePromotionResponse()
    response.setResponseCode("1")
    response.setSuccess(false)
    M82CalculateClient calculatePromotionClient = SpringContextUtil.bean(M82CalculateClient.class)
    //打包费处理
    String packageCost = "0"
    if (StrUtil.isNotBlank(request.getPackageFee())) {
      packageCost = request.getPackageFee()
    }
    //请求m82
    DiscountCalculateIn calculateIn = DiscountCalculateIn.newBuilder()
        .setStoreCode(request.getStoreCode())
        .setUserId(request.getUserId())
        .addAllDiscs(mappingCalCoupons(request.getDiscs()))
        .addAllLines(mappingCalGoods(request.getLines()))
        .setSubTotal(request.getSubTotal())
        .setChannel(2).setScene(4)
        .setDeliveryFee("0").setPackageFee(packageCost).build()
    LoggerUtil.info("IDSMember.calculatePromotion request{0}", calculateIn.toString())
    // 设置上下文
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, calculateIn.toString())
    DiscountCalculateOut calculateOut = calculatePromotionClient.discountCalculate(calculateIn)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, calculateOut.toString())
    LoggerUtil.info("IDSMember.calculatePromotion response{0}", calculateOut.toString())
    if (!calculateOut.getVerifyFlag()) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, calculateOut.getFailReason())
    }
    // 返回成功
    response.setSuccess(calculateOut.getVerifyFlag())
    response.setResponseCode(calculateOut.getVerifyFlag() ? "0" : "1")
    response.setMessage(calculateOut.getFailReason())
    response.setChannel(request.getChannel())
    //配送费
    if (calculateOut.getDeliveryFee()){
      DeliveryFee deliveryFee = new DeliveryFee()
      deliveryFee.setDeliveryTotal(calculateOut.getDeliveryFee()?.getDeliveryTotal())
      deliveryFee.setDeliveryDiscount(calculateOut.getDeliveryFee()?.getDeliveryDiscount())
      response.setDeliveryFee(deliveryFee)
    }
    if (calculateOut.getPackageFee()){
      PackageFee packageFee = new PackageFee()
      packageFee.setPackageTotal(calculateOut.getPackageFee()?.getPackageTotal())
      packageFee.setPackageDiscount(calculateOut.getPackageFee()?.getPackageDiscount())
      response.setPackageFee(packageFee)
    }
    // 抵扣信息
    response.setDiscount(mappingDiscounts(calculateOut.getDiscountList()))
    // 总价
    TotalDiscount summary = calculateOut.getSummary()
    SummaryDiscount summaryDiscount = new SummaryDiscount()
    summaryDiscount.setDiscount(summary.getDiscount())
    summaryDiscount.setSubTotal(summary.getSubTotal())
    summaryDiscount.setGrantTotal(summary.getGrantTotal())
    response.setSummary(summaryDiscount)
    return response
  }

  // 优惠信息
  static List<PriceDiscount> mappingDiscounts(List<cn.hexcloud.pbis.common.service.integration.m82.PriceDiscount> priceDiscounts) {
    List<PriceDiscount> priceDiscountList = new ArrayList<>()
    for (priceDiscount in priceDiscounts) {
      PriceDiscount discount = new PriceDiscount()
      discount.setDiscount(priceDiscount.getDiscount())
      discount.setPromotionType(priceDiscount.getPromotionType())
      discount.setPromotionId(priceDiscount.getPromotionId())
      discount.setPromotionTemplateId(priceDiscount.getPromotionTemplateId())
      discount.setName(priceDiscount.getName())
      discount.setType(priceDiscount.getType())
      List<AbsorbedExpenses> absorbedExpenses = new ArrayList<>()
      //返回产品信息
      for (product in priceDiscount.getProductList()) {
        AbsorbedExpenses expense = new AbsorbedExpenses()
        expense.setAmt(product.getAmt())
        expense.setQty(product.getQty())
        expense.setPrice(product.getPrice())
        expense.setDiscount(product.getDiscount())
        expense.setPriceDiscount(product.getPriceDiscount())
        expense.setChargeInfo(mappingCalChargeInfo(product.getChargeInfoList()))
        expense.setKey_id(product.getKeyId())
        expense.setMethod(product.getMethod())
        expense.setShopId(product.getShopId())
        //产品信息
        absorbedExpenses.add(expense)
      }
      discount.setProduct(absorbedExpenses)
      discount.setPromotionCode(priceDiscount.getPromotionCode())
      discount.setFired(priceDiscount.getFiredList())
      discount.setDiscountType(priceDiscount.getDiscountType())
      priceDiscountList.add(discount)
    }
    return priceDiscountList
  }

  // 加料商品
  static List<ChargeGoods> mappingChargeGoods(Product product) {
    List<ChargeGoods> addGoods = new ArrayList<>()
    if (product.getAccessories()) {
      for (product1 in product.getAccessories()) {
        BigDecimal price = BigDecimal.valueOf(product1.getPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN)
        ChargeGoods chargeFoods = ChargeGoods.newBuilder()
            .setPrice(price.toString())
            .setQty(product1.getQuantity())
            .setAmt(price.multiply(BigDecimal.valueOf(product1.getQuantity())).toString())
            .setKeyId(product1.getCode())
            .build()
        addGoods.add(chargeFoods)
      }
    }
    return addGoods
  }

  @Override
  ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request) {
    return null
  }

  @Override
  ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest request) {
    ChannelConsumeCouponsResponse response = new ChannelConsumeCouponsResponse()
    response.setResponseCode("1")
    response.setSuccess(false)
    M82CalculateClient calculateClient = SpringContextUtil.bean(M82CalculateClient.class)
    //商品列表
    List<Product> productList = request?.getOrderContent()?.getProducts()
    //订单信息
    Order order = request.getOrderContent()
    //请求m82
    UsePromotionsIn usePromotionsIn = UsePromotionsIn.newBuilder()
        .setStoreCode(request.getStoreCode())
        .setSubTotal(String.valueOf(order.getGrossAmount()/100))
        .setUserId(request.getUserId())
        .setOrderNo(request.getOrderContent().getOrderTicketId())
        .addAllDiscs(mappingCoupons(request.getCoupons()))
        .addAllLines(mappingGoods(productList))
        .setChannel(2).setScene(4)
        .setDeliveryFee("0").setPackageFee("0").build()
    LoggerUtil.info("IDSMember.consumeCoupons request{0}", usePromotionsIn.toString())
    // 设置上下文
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, usePromotionsIn.toString())
    UsePromotionsOut usePromotionsOut = calculateClient.usePromotions(usePromotionsIn)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, UsePromotionsOut.toString())
    LoggerUtil.info("IDSMember.consumeCoupons response{0}", usePromotionsOut.toString())
    // 返回成功
    response.setSuccess(usePromotionsOut.getVerifyFlag())
    response.setResponseCode(usePromotionsOut.getVerifyFlag() ? "0" : "1")
    response.setMessage(usePromotionsOut.getFailReason())
    response.setChannel(request.getChannel())
    //配送费
    if (usePromotionsOut.getDeliveryFee()){
      DeliveryFee deliveryFee = new DeliveryFee()
      deliveryFee.setDeliveryTotal(usePromotionsOut.getDeliveryFee()?.getDeliveryTotal())
      deliveryFee.setDeliveryDiscount(usePromotionsOut.getDeliveryFee()?.getDeliveryDiscount())
      response.setDeliveryFee(deliveryFee)
    }
    if (usePromotionsOut.getPackageFee()){
      PackageFee packageFee = new PackageFee()
      packageFee.setPackageTotal(usePromotionsOut.getPackageFee()?.getPackageTotal())
      packageFee.setPackageDiscount(usePromotionsOut.getPackageFee()?.getPackageDiscount())
      response.setPackageFee(packageFee)
    }
    // 抵扣信息
    response.setDiscount(mappingDiscounts(usePromotionsOut.getDiscountList()))
    // 总价
    TotalDiscount summary = usePromotionsOut.getSummary()
    SummaryDiscount summaryDiscount = new SummaryDiscount()
    summaryDiscount.setDiscount(summary.getDiscount())
    summaryDiscount.setSubTotal(summary.getSubTotal())
    summaryDiscount.setGrantTotal(summary.getGrantTotal())
    response.setSummary(summaryDiscount)
    return response
  }

  // 商品
  static List<GoodsInfo> mappingGoods(List<Product> products) {
    List<GoodsInfo> goodsInfos = new ArrayList<>()
    if (products) {
      for (product in products) {
        //金额转为元/单位
        BigDecimal price = BigDecimal.valueOf(product.getPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN)
        GoodsInfo goodsInfo = GoodsInfo.newBuilder()
            .setPrice(price.toString())
            .setKeyId(product.getCode())
            .setQty(product.getQuantity())
            .setAmt(price.multiply(BigDecimal.valueOf(product.getQuantity())).toString())
            .setAccAmt("0")
            .addAllAccies(mappingChargeGoods(product))
            .build()
        goodsInfos.add(goodsInfo)
      }
    }
    return goodsInfos
  }

  // 卡券
  static List<DiscountInfo> mappingCoupons(List<CouponReq> coupons) {
    List<DiscountInfo> discountInfos = new ArrayList<>()
    if (coupons) {
      for (coupon in coupons) {
        DiscountInfo discount = DiscountInfo.newBuilder()
            .setPromotionId(coupon.getCodeNo())
            .setPromotionType(Integer.parseInt(coupon.getTypeId()))
            .build()
        discountInfos.add(discount)
      }
    }
    return discountInfos
  }

  @Override
  ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest request) {
    ChannelCancelCouponsResponse response = new ChannelCancelCouponsResponse()
    response.setResponseCode("1")
    response.setSuccess(false)
    M82CalculateClient calculateClient = SpringContextUtil.bean(M82CalculateClient.class)
    BackPromotionsIn backPromotionsIn = BackPromotionsIn.newBuilder()
        .setOrderNo(request.getOrderContent().getOrderTicketId())
        .setOrderStatus(2)
        .build()
    LoggerUtil.info("IDSMember.cancelCoupons request{0}", backPromotionsIn.toString())
    // 设置上下文
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, backPromotionsIn.toString())
    BackPromotionsOut backPromotionsOut = calculateClient.backPromotions(backPromotionsIn)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, backPromotionsOut.toString())
    LoggerUtil.info("IDSMember.cancelCoupons response{0}", backPromotionsOut.toString())
    // 返回成功
    response.setSuccess(backPromotionsOut.getReverseFlag())
    response.setResponseCode(backPromotionsOut.getReverseFlag() ? "0" : "1")
    response.setMessage(backPromotionsOut.getFailReason())
    response.setChannel(request.getChannel())
    return response
  }
}
