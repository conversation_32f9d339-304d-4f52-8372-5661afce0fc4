package cn.hexcloud.pbis.common.service.integration.channel.member.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Coupon
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.CouponReq
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Order
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Product
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Rule
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.SkuDetail
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.CalculatePromotionRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCancelCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelConsumeCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCouponInfoRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelMemberRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.CalculatePromotionResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCancelCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelConsumeCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCouponInfoResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelMemberResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.MemberModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import cn.hutool.http.HttpStatus
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import groovy.transform.PackageScope

import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.text.MessageFormat

/**
 * @program: pbis* @author: Miao* @create: 2021-11-10 14:19
 */
class MeituanCouponModule extends AbstractExternalChannelModule implements MemberModule {
  static final String VERSION = '2.0'
  static final char[] HEX_DIGITS = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'] as char[]
  static final String UNIT = "个"
  static final String E_ID = '0000'
  static final String E_NAME = '风清扬'

  MeituanCouponModule(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Member"
  }

  /**
   * 请求头
   *
   * @param model
   * @param method
   * @param msg
   * @return
   */
  private Map<String, String> getSignParamsMap(String msg) {
    String ts = String.valueOf(System.currentTimeMillis())
    Map<String, String> signParams = new HashMap<>()
    signParams.put("ts", ts)
    signParams.put("version", VERSION)
    signParams.put("appKey", channel.getChannelAccessConfig().getAppKey())
    signParams.put("Params", msg)
    signParams.put('sign', getSignature(signParams))
    return signParams
  }

  /**
   * 根据手机号查询用户券码
   * https://o.dianping.com/#/doc/overview/BizUni_57_101/670
   *
   * @param channelMemberRequest
   * @return
   */
  @Override
  ChannelMemberResponse getMember(ChannelMemberRequest channelMemberRequest) {
    ChannelMemberResponse response = new ChannelMemberResponse()
    return response
  }

  //加密
  @Override
  String getSignature(Map<String, String> rawMessage) {
    // 美团加密规则
    String str = MessageFormat.format("{0}appKey{2}ts{3}version{4}{1}", channel.getChannelAccessConfig().getAccessKey(),
        rawMessage.get("Params"), channel.getChannelAccessConfig().getAppKey(), rawMessage.get("ts"), VERSION)
    if (str != null && str.length() != 0) {
      try {
        MessageDigest mdTemp = MessageDigest.getInstance("SHA1")
        mdTemp.update(str.getBytes("UTF-8"))
        byte[] md = mdTemp.digest()
        int j = md.length
        char[] buf = new char[j * 2]
        int k = 0

        for (int i = 0; i < j; ++i) {
          byte byte0 = md[i]
          buf[k++] = HEX_DIGITS[byte0 >>> 4 & 15]
          buf[k++] = HEX_DIGITS[byte0 & 15]
        }
        return new String(buf)
      } catch (Exception ex) {
        LoggerUtil.error('美团API加密失败{0}', ex, str)
        return null
      }
    } else {
      return null
    }
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }

  private JSONObject doRequest(String uri, String method, Map<String, Object> params, boolean reserveData) {
    String content = JSON.toJSONString(params)
    //公共参数
    Map<String, String> signParams = getSignParamsMap(content)
    String url = createUrlParams(signParams, uri)
    // 发起HTTP请求
    String methodFullName = getMethodFullName(method)
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, content)
    byte[] result = HttpUtil.doPost(url, content)
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)
    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, content)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
    }
    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
    return resultJSON
  }

  /**
   * 券查询
   * https://open-api.dianping.com/tuangou/coupon/status/query
   *
   * @param request
   * @return
   */
  @Override
  ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request) {
    ChannelCouponInfoResponse response = new ChannelCouponInfoResponse()
    response.setSuccess(false)
    response.setResponseCode("1")
    List<Coupon> couponList = new ArrayList<>()
    //Pos传入券码列表
    if (null == request.getCoupons()) {
      response.setMessage("券码不能为空")
      return response
    }
    request.getCoupons().each { x ->
      //美团券码
      String couponCode = x.getCodeNo()
      //业务参数
      Map<String, Object> bizParams = new HashMap<>()
      bizParams.put("vendorShopId", String.valueOf(request.getMemberContent().getCardNo()))
      bizParams.put("couponCode", couponCode)
      //请求美团
      JSONObject jsonResp = doRequest(channel.getChannelAccessConfig()
          .getProperty("coupon_info_url"), "couponInfo", bizParams, false)
      if (jsonResp.get('code') != HttpStatus.HTTP_OK) {
        response.setMessage(jsonResp.getString('message'))
        throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, jsonResp?.getString('msg'))
      }
      //转换优惠券信息
      Coupon coupon = couponInfoResultMapping(couponCode, jsonResp)
      if (coupon) {
        couponList.add(coupon)
      }
    }
    response.setDetails(couponList)
    response.setChannel(request.getChannel())
    response.setResponseCode("200")
    response.setSuccess(true)
    return response

  }

  @Override
  CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request) {
    CalculatePromotionResponse response = new CalculatePromotionResponse()
    response.setSuccess(true)
    response.setResponseCode("0")
    return response
  }

  //转换返回结果
  static Coupon couponInfoResultMapping(String couponCode, JSONObject jsonResp) {
    //获取券信息
    JSONObject content = jsonResp.getJSONObject("content")
    if (null == content) {
      return null
    }
    Coupon coupon = new Coupon()
    boolean success = false
    //优惠券状态
    int couponStatus = content.getIntValue("status")
    int status = 0
    switch (couponStatus) {
      case 10:
        status = 1
        success = true
        break
      case 20:
        status = 2
        success = false
        break
    }
    if (!success) {
      return null
    }
    coupon.setStatus(status)
    //规则
    Rule rule = new Rule()
    int dealType = content.getIntValue("dealType")
    double dealValue = content.getBigDecimal("dealValue").doubleValue()
    double realAmount = content.getBigDecimal("realAmount").doubleValue()
    // hex typeId
    // 会员券券类别：2 商品券
    String hexTypeId = "2"
    switch (dealType) {
      case 1:
        hexTypeId = "2"
        break
      case 2:
        hexTypeId = "11"
        break
      case 3:
        hexTypeId = "12"
        break
    }
    coupon.setCodeNo(couponCode)
    coupon.setId(hexTypeId)
    coupon.setCode(hexTypeId)
    coupon.setParValue(dealValue)
    coupon.setRefAmount(String.valueOf(realAmount))
    coupon.setType("NORMAL")
    coupon.setUseType("ONCE")
    // 转换美团券类别：美团团单类型，1:代金券 2:套餐券 4:免费试券
    // 合阔券类别
    int couponTypeId = 2
    switch (dealType) {
      case 1:
        couponTypeId = 1
        break
      case 2:
        couponTypeId = 2
        break
      case 4:
        couponTypeId = 4
        break
    }
    coupon.setCouponTypeId(couponTypeId)
    coupon.setName(content.getString("dealTitle"))
    JSONObject dealSku = content.getJSONObject("dealSkuMappingDetail")
    if (dealSku) {
      SkuDetail skuDetail = new SkuDetail()
      skuDetail.setCount(dealSku.getIntValue("count"))
      skuDetail.setSkus(dealSku.getJSONArray("vendorSkus").toJavaList(String.class))
      coupon.setSkuDetail(skuDetail)
    }
    coupon.setRule(rule)
    //券码
    coupon.setTypeCode(content.getString("dealId"))
    coupon.setStartDate(content.getString("couponStartTime"))
    coupon.setExpiredDate(content.getString("couponEndTime"))
    return coupon
  }

  /**
   * 美团核销券
   * https://o.dianping.com/#/doc/overview/BizUni_57_101/744
   *
   * @param request
   * @return
   */
  @Override
  ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest request) {
    ChannelConsumeCouponsResponse response = new ChannelConsumeCouponsResponse()
    response.setSuccess(false)
    response.setResponseCode("1")
    if (!request.getCoupons()) {
      return response
    }
    // 订单信息
    Order orderContent = request.getOrderContent()
    List<CouponReq> couponReqs = request.getCoupons()
    //核销结果
    Map<String, Boolean> consumeResult = new HashMap<>()
    Map<String, String> contentMap = new HashMap<>()
    //处理失败信号
    int failed = 0
    int i = 0
    //美团报文处理
    for (couponReq in couponReqs) {
      //业务参数
      Map<String, Object> bizParams = new HashMap<>()
      bizParams.put("vendorOrderId", orderContent?.getOrderTicketId())
      //商品列表
      List<Product> productList = request?.getOrderContent()?.getProducts()
      int toPayAmount = request?.getOrderContent()?.getGrossAmount()
      List<Map<String, Object>> orderSkus = new ArrayList<>()
      if (productList) {
        productList.each { it ->
          def skus = ['vendorSkuId'  : it.getCode(),
                      'VendorSkuName': it.getName(),
                      'Unit'         : UNIT,
                      'UnitPrice'    : it.getPrice() / 100,
                      'Count'        : it.getQuantity()]
          orderSkus.add(skus)
          //计算订单总金额
          toPayAmount += (it.getPrice() * it.getQuantity()) / 100
        }
      }
      bizParams.put("toPayAmount", toPayAmount)
      bizParams.put("orderSkus", orderSkus)
      bizParams.put("couponCode", couponReq.getCodeNo())
      bizParams.put("vendorShopId", request.getMemberContent().getCardNo())
      bizParams.put("eId", E_ID)
      bizParams.put("eName", E_NAME)
      //公共参数
      JSONObject jsonResp = doRequest(channel.getChannelAccessConfig()
          .getProperty("coupon_consume_url"), "consumeCoupons", bizParams, true)
      //核销结果
      consumeResult.put(couponReq.getCodeNo(), jsonResp.get('code') == HttpStatus.HTTP_OK)
      if (jsonResp.get('code') != HttpStatus.HTTP_OK) {
        response.setMessage(jsonResp.getString("msg"))
        failed++
        break
      } else {
        contentMap.put(String.valueOf(i), jsonResp.getString("content"))
        i++
      }
    }
    response.setChannel(request.getChannel())
    //没有失败直接返回
    if (failed <= 0) {
      response.setResponseContent(JSON.toJSONString(contentMap))
      response.setSuccess(true)
      response.setResponseCode('0')
      return response
    } else {
      //处理券核销，如果其中一张券处理失败，全部撤销
      List<CouponReq> failedCoupons = new ArrayList<>()
      for (coupon in couponReqs) {
        if (consumeResult.get(coupon.getCodeNo()) == Boolean.TRUE) {
          failedCoupons.add(coupon)
        }
      }
      //执行取消
      if (failedCoupons) {
        ChannelCancelCouponsRequest cancelCoupon = new ChannelCancelCouponsRequest()
        cancelCoupon.setStoreId(request.getStoreId())
        cancelCoupon.setCoupons(failedCoupons)
        cancelCoupons(cancelCoupon)
      }
      response.setChannel(request.getChannel())
      response.setSuccess(false)
      response.setResponseCode('1')
      return response
    }
  }

  /**
   * 美团反核销
   * https://o.dianping.com/#/doc/overview/BizUni_57_101/463
   *
   * @param request
   * @return
   */
  @Override
  ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest request) {
    ChannelCancelCouponsResponse response = new ChannelCancelCouponsResponse()
    response.setSuccess(true)
    response.setResponseCode("0")
    if (!request.getCoupons()) {
      return response
    }
    // 订单信息
    List<CouponReq> couponReqs = request.getCoupons()
    List<String> contents = new ArrayList<>()
    for (couponReq in couponReqs) {
      //业务参数
      Map<String, Object> bizParams = new HashMap<>()
      bizParams.put("vendorShopId", request.getMemberContent().getCardNo())
      bizParams.put("couponCode", couponReq.getCodeNo())
      bizParams.put("eId", E_ID)
      bizParams.put("eName", E_NAME)
      //请求美团
      JSONObject jsonResp = doRequest(channel.getChannelAccessConfig()
          .getProperty("coupon_cancel_url"), "cancelCoupons", bizParams, true)
      if (jsonResp.get('code') != HttpStatus.HTTP_OK) {
        response.setMessage(jsonResp.getString('msg'))
        response.setSuccess(false)
        response.setResponseCode('1')
      } else {
        contents.add(jsonResp.getString("content"))
      }
    }
    response.setResponseContent(JSON.toJSONString(contents))
    response.setChannel(request.getChannel())
    return response
  }

/**
 * 拼接美团url参数
 * @param urlParameters
 * @param baseUrl
 * @return
 */
  @PackageScope
  static String createUrlParams(Map<String, String> urlParameters, String baseUrl) {
    if (urlParameters != null && urlParameters.size() != 0) {
      StringBuilder buffer
      buffer = new StringBuilder()
      int i = 0
      Iterator var4 = urlParameters.keySet().iterator()
      while (var4.hasNext()) {
        String key = (String) var4.next()
        if (key == 'Params') {
          continue
        }
        if (i == 0) {
          buffer.append(MessageFormat.format("{0}={1}", key, urlParameters.get(key)))
          ++i
        } else {
          buffer.append(MessageFormat.format("&{0}={1}", key, urlParameters.get(key)))
        }
      }
      if (buffer != null && buffer.length() > 0) {
        int index = baseUrl.indexOf("?")
        if (index >= 0) {
          if (index < baseUrl.length() - 1) {
            baseUrl = baseUrl + "&" + buffer.toString()
          } else {
            baseUrl = baseUrl + buffer.toString()
          }
        } else {
          baseUrl = baseUrl + "?" + buffer.toString()
        }
      }
      return baseUrl
    } else {
      return baseUrl
    }
  }
}