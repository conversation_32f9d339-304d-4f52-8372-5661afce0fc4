package cn.hexcloud.pbis.common.service.integration.channel.member.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.commons.utils.cipher.SHAUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Coupon
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.CouponAmount
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.CalculatePromotionRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCancelCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelConsumeCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCouponInfoRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelMemberRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.CalculatePromotionResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCancelCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelConsumeCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCouponInfoResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelMemberResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.MemberModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.apache.http.NameValuePair
import org.apache.http.message.BasicNameValuePair

import java.nio.charset.StandardCharsets
import java.sql.Timestamp

class MeituanISVCouponModule extends AbstractExternalChannelModule implements MemberModule {

  private static final String SUCCESS_CODE = "OP_SUCCESS"
  private static final int RESULT = 0
  private static final List<String> TOLERANCE_ERROR_CODE_LIST
  private static final Map<String, String> URL_MAP
  private static final String E_ID = "0000"
  private static final String E_NAME = "风清扬"

  static {
    URL_MAP = new HashMap<>()
    URL_MAP.put("getCouponInfo", "coupon_info_url")
    URL_MAP.put("verifyCoupon", "pre_coupon_consume_url")
    URL_MAP.put("consumeCoupons", "coupon_consume_url")
    URL_MAP.put("cancelCoupons", "coupon_cancel_url")
    TOLERANCE_ERROR_CODE_LIST = new ArrayList<>()
    TOLERANCE_ERROR_CODE_LIST.add("808") // 操作失败（如订单在操作时，状态已变更等情况）
  }

  /**
   * 美团文档
   * https://developer.meituan.com/docs/biz/biz_tuangoung_e9e35039-f3f8-4fae-8950-c9f6c96a604c
   * @param channel
   */
  MeituanISVCouponModule(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return null
  }

  @Override
  ChannelMemberResponse getMember(ChannelMemberRequest request) {
    return null
  }

  @Override
  ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request) {
    List<Coupon> couponList = new ArrayList<>()
    for (coupon in request.getCoupons()) {
      Map<String, Object> bizParams = new HashMap<>()
      bizParams.put("code", coupon.getCodeNo())
      // 发起请求
      JSONObject resultJSON = doRequest("verifyCoupon", bizParams, false)
      if (resultJSON.get('code') != SUCCESS_CODE) {
        throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON?.getString('msg'))
      }
      // 解析优惠券
      Coupon couponNew = couponInfoMapping(coupon.getCodeNo(), resultJSON.getJSONObject('data'))
      if (Objects.nonNull(couponNew)) {
        couponList.add(couponNew)
      }
    }
    ChannelCouponInfoResponse response = new ChannelCouponInfoResponse()
    response.setDetails(couponList)
    response.setChannel(request.getChannel())
    response.setResponseCode("200")
    response.setSuccess(true)
    return response
  }

  // yuan to fen
  private static Integer yuanToFen(BigDecimal yuan) {
        return (yuan * new BigDecimal(100)).intValue();
  }

  //转换返回结果
  private static Coupon couponInfoMapping(String couponCode, JSONObject jsonResp) {
    if (null == jsonResp) {
      return null
    }
    Coupon coupon = new Coupon()
    // 默认可用
    coupon.setStatus(1)
    coupon.setCodeNo(couponCode)
    // 是否代金券,true代表代金券,false代表套餐券
    boolean dealType = jsonResp.getBoolean("vourcher")
    if (dealType) {
      coupon.setCouponTypeId(1)
    } else {
      coupon.setCouponTypeId(2)
    }
    // 金额信息
    CouponAmount couponAmount = new CouponAmount()
    couponAmount.setParValue(yuanToFen(jsonResp.getBigDecimal("dealValue")))
    couponAmount.setPrice(yuanToFen(jsonResp.getBigDecimal("dealPrice"))) // 券售价
    couponAmount.setPayAmount(yuanToFen(jsonResp.getBigDecimal("couponBuyPrice"))) // 用户实付金额
    coupon.setCouponAmount(couponAmount)

    double dealValue = jsonResp.getBigDecimal("dealValue").doubleValue()
    double realAmount = jsonResp.getBigDecimal("dealPrice").doubleValue()
    coupon.setParValue(dealValue)
    coupon.setRefAmount(String.valueOf(realAmount))
    coupon.setType("NORMAL")
    coupon.setUseType("ONCE")
    // 名称：dealPrice + '代' + platformTitle
    Integer dealPrice = jsonResp.getInteger("dealPrice")
    String platformTitle = jsonResp.getString("platformTitle")
    String couponName = String.format("%s 代 %s", dealPrice, platformTitle)
    coupon.setName(couponName)
    coupon.setSumLimit(jsonResp.getInteger("count"))
    // 券码
    coupon.setTypeCode(jsonResp.getString("dealId"))
    coupon.setStartDate(jsonResp.getString("dealBeginTime"))
    coupon.setExpiredDate(jsonResp.getString("couponEndTime"))

    return coupon
  }

  @Override
  CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request) {
    return null
  }

  @Override
  ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest request) {
    List<Coupon> coupons = new ArrayList<>()
    for (coupon in request.getCoupons()) {
      Map<String, Object> bizParams = new HashMap<>()
      bizParams.put("code", coupon.getCodeNo())
      if (coupon.getQty() <= 0) {
        bizParams.put("num", 1)
      } else {
        bizParams.put("num", coupon.getQty())
      }
      // 发起请求
      JSONObject resultJSON = doRequest("consumeCoupons", bizParams, true)
      if (resultJSON.get('code') != SUCCESS_CODE) {
        throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON.getString("msg"))
      }
      // 返回券码
      if (resultJSON.containsKey("data")) {
        JSONObject data = resultJSON.getJSONObject("data")
        if (data.containsKey("couponCodes")) {
          for (couponCode in data.getJSONArray("couponCodes")) {
            Coupon coupon1 = new Coupon()
            coupon1.setCodeNo(couponCode.toString())
            coupons.add(coupon1)
          }
        }
      }
    }
    ChannelConsumeCouponsResponse response = new ChannelConsumeCouponsResponse()
    // 拼接券码
    response.setCouponDetails(coupons)
    response.setSuccess(true)
    response.setResponseCode('0')
    return response
  }

  @Override
  ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest request) {
    for (coupon in request.getCoupons()) {
      Map<String, Object> bizParams = new HashMap<>()
      bizParams.put("couponCode", coupon.getCodeNo())
      bizParams.put("eId", E_ID)
      bizParams.put("eName", E_NAME)
      // type值永远为1撤销验券
      bizParams.put("type", 1)
      // 发起请求
      JSONObject resultJSON = doRequest("cancelCoupons", bizParams, true)
      if (resultJSON.get('code') != SUCCESS_CODE) {
        throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON.getString("msg"))
      }
      if (resultJSON.containsKey("data")) {
        JSONObject data = resultJSON.getJSONObject("data")
        if (data.containsKey("result") && data.getInteger("result") != RESULT) {
          throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, data.getString("message"))
        }
      }
    }
    ChannelCancelCouponsResponse response = new ChannelCancelCouponsResponse()
    response.setSuccess(true)
    response.setResponseCode('0')
    return response
  }

  @Override
  String getModuleName() {
    return "Member"
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    // 加工数据并得到签名原文
    StringBuilder sb = new StringBuilder()
    for (Map.Entry<String, String> entry : rawMessage) {
      sb.append(entry.getKey()).append(entry.getValue())
    }
    String dataBeforeSign = sb.insert(0, channel.getChannelAccessConfig().getAccessKey()).toString()

    // 签名并返回签名信息
    return SHAUtil.signSHA1(dataBeforeSign)
  }

  @Override
  boolean isValidSignature(Map<String, String> unverifiedMessage) {
    String tpSignature = unverifiedMessage.get("sign")
    unverifiedMessage.remove("sign")
    String signature = getSignature(unverifiedMessage)
    return tpSignature == signature
  }

  private JSONObject doRequest(String method, Map<String, Object> bizParams, boolean reserveData) {
    // 请求参数
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    List<NameValuePair> body = new ArrayList<>()
    body.add(new BasicNameValuePair("appAuthToken", channelAccessConfig.getAuthToken()))
    body.add(new BasicNameValuePair("timestamp", DateUtil.getNowSeconds().toString()))
    body.add(new BasicNameValuePair("charset", StandardCharsets.UTF_8.toString()))
    body.add(new BasicNameValuePair("developerId", channelAccessConfig.getMerchantId()))
    body.add(new BasicNameValuePair("version", "2"))
    body.add(new BasicNameValuePair("businessId", "1"))
    body.add(new BasicNameValuePair("biz", JSON.toJSONString(bizParams)))

    // 签名
    StringBuilder sb = new StringBuilder()
    Map<String, String> rawMessage = new TreeMap<>()
    for (NameValuePair pair : body) {
      sb.append(pair.getName()).append("=").append(pair.getValue()).append("&")
      rawMessage.put(pair.getName(), pair.getValue())
    }
    String sign = getSignature(rawMessage)
    body.add(new BasicNameValuePair("sign", sign))
    sb.append("sign=").append(sign)

    // 发起HTTP请求
    String methodFullName = getMethodFullName(method)
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, sb.toString())
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result = HttpUtil.doPost(getRequestUrl(method), body, null)
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)

    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, sb.toString())
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
      TransactionLoggerContext.set(ContextKeyConstant.THIRD_PARTY_TRANSACTION_ID, resultJSON.getString("traceId"))
    }
    return resultJSON

  }

  private String getRequestUrl(String method) {
    return channel.getChannelAccessConfig().getProperty(URL_MAP.get(method))
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }

}
