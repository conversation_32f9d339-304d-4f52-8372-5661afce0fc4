package cn.hexcloud.pbis.common.service.integration.channel.member.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Coupon
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.CouponReq
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.DepositCard
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Member
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Order
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Store
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.CalculatePromotionRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCancelCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelConsumeCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCouponInfoRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelMemberRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.CalculatePromotionResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCancelCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelConsumeCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCouponInfoResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelMemberResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.MemberModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import org.apache.commons.codec.binary.Base64
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils

import javax.crypto.Mac
import javax.crypto.SecretKey
import javax.crypto.spec.SecretKeySpec
import java.nio.charset.Charset
import java.nio.charset.StandardCharsets

/**
 * Created on 2021/10/29.
 * <AUTHOR> Desc 企迈接口文档 ：
 * https://open.qimai.shop/doc2/article?id=476
 */
class QmaiMemberModule extends AbstractExternalChannelModule implements MemberModule {

  QmaiMemberModule(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Member"
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    List<Map.Entry<String, Object>> sortList = getAsciiSort(rawMessage);

    StringBuilder sb = new StringBuilder()
    for (Map.Entry<String, Object> mapData : sortList) {
      String dataKey = mapData.getKey()
      Object dataValue = mapData.getValue()
      sb.append(dataKey).append("=").append(dataValue).append("&")
    }
    String sign = sb.toString()
    if (StringUtils.isNotBlank(sign) && StringUtils.isNotBlank(sign)) {
      sign = sign.substring(0, sign.lastIndexOf("&"))
    }
    String signStr = hmacSHA1Encrypt(sign, channel.getChannelAccessConfig().getAppKey())

    return signStr
  }

  static String hmacSHA1Encrypt(String encryptText, String encryptKey) throws Exception {
    byte[] data = encryptKey.getBytes("UTF-8")
    SecretKey secretKey = new SecretKeySpec(data, "HmacSHA1")
    Mac mac = Mac.getInstance("HmacSHA1")
    mac.init(secretKey)
    byte[] text = encryptText.getBytes("UTF-8")
    String str = new String(Base64.encodeBase64(mac.doFinal(text)), Charset.forName("UTF-8"))
    return URLEncoder.encode(str, "UTF-8")
  }

  private static List<Map.Entry<String, String>> getAsciiSort(Map<String, String> map) {
    List<Map.Entry<String, String>> infoIds = new ArrayList<>(map.entrySet())
    // 对所有传入参数按照字段名的 ASCII 码从小到大排序（字典序）
    Collections.sort(infoIds, new Comparator<Map.Entry<String, String>>() {
      int compare(Map.Entry<String, String> o1, Map.Entry<String, String> o2) {
        return ((String) o1.getKey()).compareToIgnoreCase((String) o2.getKey())
      }
    })
    return infoIds
  }

  @Override
  ChannelMemberResponse getMember(ChannelMemberRequest channelMemberRequest) {
    // 签名获取
    Map<String, Object> signMap = new HashMap();
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    signMap.put("GrantCode", channelAccessConfig.getMerchantId())
    String Nonce = String.valueOf((int) (Math.random() * 1000000))
    signMap.put("Nonce", Nonce)
    signMap.put("OpenId", channelAccessConfig.getAppId())
    String timestamp = String.valueOf(System.currentTimeMillis())
    signMap.put("Timestamp", timestamp)

    String token = getSignature(signMap)
    // 装填参数
    JSONObject postJson = new JSONObject()
    postJson.put("OpenId", channelAccessConfig.getAppId())
    postJson.put("GrantCode", channelAccessConfig.getMerchantId())
    postJson.put("Timestamp", timestamp)
    postJson.put("Nonce", Nonce)
    postJson.put("Token", token)
    postJson.put("Action", "communal.user.all-customer-info")
    //Params拼接
    JSONObject params = new JSONObject()

    if (StringUtils.isNotEmpty(channelMemberRequest.getMemberCode())) {

      params.put("customerId", channelMemberRequest.getMemberCode())// customerId  与type 互斥

    } else {
      if (StringUtils.isNotEmpty(channelMemberRequest.getMobile())) {

        params.put("type", 1)
        params.put("phone", channelMemberRequest.getMobile())

      } else if (StringUtils.isNotEmpty(channelMemberRequest.getCardNo())) {
        params.put("type", 2)
        params.put("cardNo", channelMemberRequest.getCardNo())

      } else if (StringUtils.isNotEmpty(channelMemberRequest.getMemberCode())) {
        params.put("type", 2)
        params.put("cardNo", channelMemberRequest.getMemberCode())
      }
    }
    params.put("pageSize", 50)
    postJson.put("Params", params)
    String jsonStr = JSON.toJSONString(postJson)
    LoggerUtil.info("QmaiOperation.getMember is sending message: {0}, token: {1}.", jsonStr, token)

    byte[] result = HttpUtil.doPost(channelAccessConfig.getProperty("member_info_url"), jsonStr, getRequestHeader())
    if (null == result) {
      LoggerUtil.error("QmaiOperation.getMember is failed with null result.", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("QmaiOperation.getMember received message: {0}, token: {1}.", resultJSON, token)
    String statusCode = String.valueOf(resultJSON.get("code"));
    if (!statusCode.equals("0")) {
      LoggerUtil.error("QmaiOperation.getMember is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("code"),
          resultJSON.getString("errorMsg"), resultJSON.getString("message"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON.getString("message"))

    }

    resultJSON = resultJSON.getJSONObject("data")

    if (resultJSON == null) {
      // 请求失败
      LoggerUtil.error("QmaiOperation.getMember is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("code"),
          resultJSON.getString("errorMsg"), resultJSON.getString("message"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }

    return memberInfo(resultJSON);

  }

  private ChannelMemberResponse memberInfo(JSONObject data) {

    ChannelMemberResponse channelMemberResponse = new ChannelMemberResponse()

    JSONObject detailInfo = data.getJSONObject("detailInfo");
    JSONArray couponsLists = data.getJSONArray("CouponsLists");
    if (detailInfo != null) {

      channelMemberResponse.setCardNo(detailInfo.getString("cardNo"))
      channelMemberResponse.setMemberCode(detailInfo.getString("customerId"))
      channelMemberResponse.setName(detailInfo.getString("username"))
      channelMemberResponse.setMobile(detailInfo.getString("mobilePhone"))
      channelMemberResponse.setGradeName(detailInfo.getString("memberLevel"))
      channelMemberResponse.setGrantDate(detailInfo.getString("openingCardDate"))
    }

    String totalPoints = data.getString("totalPoints")
    if (totalPoints != null) {
      double value = Double.parseDouble(totalPoints);
      channelMemberResponse.setCreditBalance((int) Math.round(value))
    }

    double giftBalance = Double.parseDouble(data.getString("giftBalance"))
    double rechargeBalance = Double.parseDouble(data.getString("rechargeBalance"))
    channelMemberResponse.setAccountBalance((int) Math.round((giftBalance + rechargeBalance) * 100))

    List<Coupon> coupons = new ArrayList<>()

    if (couponsLists != null && couponsLists.size() != 0) {
      for (int i = 0; i < couponsLists.size(); i++) {
        JSONObject cou = (JSONObject) couponsLists.get(i)
        Coupon coupon = new Coupon()
        String useStatus = cou.getString("useStatus")
        if (!"0".equals(useStatus)) {
          continue
        }
        coupon.setCode(cou.getString("cardId"))
        coupon.setId(cou.getString("couponId"))
        coupon.setName(cou.getString("title"))
        coupon.setType(cou.getString("templateId"))
        coupon.setTypeCode(cou.getString("couponType"))
        coupon.setExpiredDate(cou.getString("endAt"))
        coupon.setStatus(couponStatuConvert(useStatus))
        coupons.add(coupon)
      }
    }
    channelMemberResponse.setCoupons(coupons)

    List<DepositCard> depositCards = new ArrayList<>()
    Integer vamount = convert2Fen(data.getString("giftBalance"));
    Integer amount = convert2Fen(data.getString("rechargeBalance"));

    DepositCard depositCard = new DepositCard()
    depositCard.setCardCode(detailInfo.getString("customerId"))
    depositCard.setAmount(amount)
    depositCard.setVamount(vamount)
    depositCard.setHasExpireTIme(false)
    depositCard.setCardCode(detailInfo.getString("customerId"))
    depositCards.add(depositCard)
    channelMemberResponse.setDepositCard(depositCards)

    return channelMemberResponse
  }

  private int convert2Fen(String yuan) {
    if (org.springframework.util.StringUtils.isEmpty(yuan) || "null".equals(yuan)) {
      return 0
    }

    return BigDecimal.valueOf(Double.valueOf(yuan)).multiply(new BigDecimal(100))
        .setScale(2, BigDecimal.ROUND_HALF_UP).abs().intValue();
  }

  private Map<String, String> getRequestHeader() {
    Map<String, String> header = new HashMap<>()
    header.put("Content-Type", "application/json")
    return header
  }

  //优惠券状态
  private Integer couponStatuConvert(String useStatus) {
    switch (useStatus) {
    //:0：未使用/已冲正、1：已核销、2：已冻结、4：转赠中、5：转赠超时、6：已失效、7：已删除/已回收'
    //可用(剔除过期)
      case "0":
        return 1;
    //已使用
      case "1":
        return 2;
    //取消
      case "7":
        return 3;
    //已过期(根据生失效日期)
      case "6":
        return 2;
      default:
        return 3;
    }

  }


  @Override
  ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "QmaiOperation.getCouponInfo")
  }

  @Override
  CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "QmaiOperation.calculatePromotion")
  }

  @Override
  ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest channelConsumeCouponsRequest) {
    // 签名获取
    Map<String, Object> signMap = new HashMap();

    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

    signMap.put("GrantCode", channelAccessConfig.getMerchantId())
    String Nonce = String.valueOf((int) (Math.random() * 1000000))
    signMap.put("Nonce", Nonce)
    signMap.put("OpenId", channelAccessConfig.getAppId())
    String timestamp = String.valueOf(System.currentTimeMillis())
    signMap.put("Timestamp", timestamp)

    String token = getSignature(signMap)

    Member member = channelConsumeCouponsRequest.getMemberContent()
    Order order = channelConsumeCouponsRequest.getOrderContent()
    // 装填参数
    JSONObject postJson = new JSONObject();

    postJson.put("OpenId", signMap.get("OpenId"));
    postJson.put("GrantCode", signMap.get("GrantCode"));
    postJson.put("Timestamp", signMap.get("Timestamp"));
    postJson.put("Nonce", signMap.get("Nonce"));
    postJson.put("Token", token);
    postJson.put("Action", "communal.user.consume");

    // Params拼接
    Map<String, Object> param = new HashMap()
    param.put("bizId", order.getOrderTicketId())
    List couponsCardList = new ArrayList()
    List<CouponReq> coupons = channelConsumeCouponsRequest.getCoupons()

    if (CollectionUtils.isNotEmpty(coupons)) {
      for (int i = 0; i < coupons.size(); i++) {
        CouponReq coupon = coupons.get(i)
        couponsCardList.add(coupon.getCodeNo())
      }
    }
    param.put("couponsCardList", couponsCardList)
    // amount 用来扣减 会员储值
    param.put("amount", 0)
    param.put("multiMark", channelConsumeCouponsRequest.getStoreCode())
    param.put("customerId", member.getMemberCode())

    postJson.put("Params", param)

    String jsonStr = JSON.toJSONString(postJson)
    LoggerUtil.info("QmaiOperation.consume is sending message: {0}, token: {1}.", jsonStr, token)

    byte[] result = HttpUtil.doPost(channelAccessConfig.getProperty("member_info_url"), jsonStr, getRequestHeader())
    if (null == result) {
      LoggerUtil.error("QmaiOperation.consume is failed with null result.", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSON.toJSONString(postJson))
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("QmaiOperation.consume received message: {0}, token: {1}.", resultJSON, token)

    String statusCode = String.valueOf(resultJSON.get("code"));
    if (!statusCode.equals("0")) {
      LoggerUtil.error("QmaiOperation.consume is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("code"),
          resultJSON.getString("errorMsg"), resultJSON.getString("message"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON.getString("message"))

    }
    ChannelConsumeCouponsResponse ChannelConsumeCouponsResponse = new ChannelConsumeCouponsResponse();
    ChannelConsumeCouponsResponse.setMessage(resultJSON.getString("message"))
    ChannelConsumeCouponsResponse.setResponseContent(JSON.toJSONString(resultJSON))
    ChannelConsumeCouponsResponse.setResponseCode(resultJSON.getString("code"))
    ChannelConsumeCouponsResponse.setSuccess(true)
    return ChannelConsumeCouponsResponse

  }

  @Override
  ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest channelCancelCouponsRequest) {

    // 签名获取
    Map<String, Object> signMap = new HashMap();
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    signMap.put("GrantCode", channelAccessConfig.getMerchantId())
    String Nonce = String.valueOf((int) (Math.random() * 1000000))
    signMap.put("Nonce", Nonce)
    signMap.put("OpenId", channelAccessConfig.getAppId())
    String timestamp = String.valueOf(System.currentTimeMillis())
    signMap.put("Timestamp", timestamp)

    String token = getSignature(signMap)
    Order order = channelCancelCouponsRequest.getOrderContent()

    //装填参数
    JSONObject postJson = new JSONObject();
    postJson.put("OpenId", signMap.get("OpenId"));
    postJson.put("GrantCode", signMap.get("GrantCode"));
    postJson.put("Timestamp", signMap.get("Timestamp"));
    postJson.put("Nonce", signMap.get("Nonce"));
    postJson.put("Token", token);
    postJson.put("Action", "communal.user.consume-reverse");

    //Params拼接

    Map<String, Object> param = new HashMap()
    param.put("bizId", order.getOrderTicketId())
    param.put("changeType", 3)
    postJson.put("Params", param)

    String jsonStr = JSON.toJSONString(postJson)
    LoggerUtil.info("QmaiOperation.cancel is sending message: {0}, token: {1}.", jsonStr, token)

    byte[] result = HttpUtil.doPost(channelAccessConfig.getProperty("member_info_url"), jsonStr, getRequestHeader())
    if (null == result) {
      LoggerUtil.error("QmaiOperation.cancel is failed with null result.", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSON.toJSONString(postJson))
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("QmaiOperation.cancelCoupons received message: {0}, token: {1}.", resultJSON, token)

    String statusCode = String.valueOf(resultJSON.get("code"));
    if (!statusCode.equals("0")) {
      LoggerUtil.error("QmaiOperation.cancel is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("code"),
          resultJSON.getString("errorMsg"), resultJSON.getString("message"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON.getString("message"))

    }
    ChannelCancelCouponsResponse cancelCouponsResponse = new ChannelCancelCouponsResponse()
    cancelCouponsResponse.setResponseCode(resultJSON.getString("code"))
    cancelCouponsResponse.setSuccess(true)
    cancelCouponsResponse.setResponseContent(JSON.toJSONString(resultJSON))
    cancelCouponsResponse.setMessage(resultJSON.getString("message"))

    return cancelCouponsResponse

  }
}


