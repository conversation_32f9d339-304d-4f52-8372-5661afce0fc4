package cn.hexcloud.pbis.common.service.integration.channel.member.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.CouponReq
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.DepositCard
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Member
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Order
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Rule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hutool.core.collection.CollectionUtil
import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Benefit
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Coupon
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.CalculatePromotionRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCancelCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelConsumeCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCouponInfoRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelMemberRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.CalculatePromotionResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCancelCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelConsumeCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCouponInfoResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelMemberResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.MemberModule
import cn.hexcloud.pbis.common.util.exception.ServiceError
import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.util.DigestUtils

import java.nio.charset.StandardCharsets
import java.sql.Timestamp
import java.text.MessageFormat
import java.util.stream.Collectors

/**
 * @program: pbis*
 * @author: miao*
 * @create: 2021-11-11 15:23
 */
class SeltekMemberModule extends AbstractExternalChannelModule implements MemberModule {
    //接口返回成功标记
    private static final int API_SUCCESS_CODE = 200
    private static final int API_FAILED_CODE = 400
    private static final String VOUCHER_TYPE_ID = "10000"
    private static final Map<String, String> PATH
    // 支持手机号查询的api
    private static final List<String> SUPPORT_MOBILE_API

    SeltekMemberModule(ExternalChannel channel) {
        super(channel)
    }

    static {
        PATH = new HashMap<>()
        PATH.put("getMemAndCouByCardNo", "/openapi/member/getMemAndCouByCardNo") //根据cardNo查询用户信息
        PATH.put("getMemAndCouByMobile", "/openapi/member/getMemAndCouByMobile") //根据手机号查询用户信息
        PATH.put("getCouInfo", "/openapi/coupon/getCouInfo") //根据券码查询会员与优惠券信息

        PATH.put("getVoucherListByMemberCode", "/openapi/voucher/getVoucherListByMemberCode") //根据会员ID查询优惠券
        PATH.put("used", "/openapi/voucher/used")//核销
        PATH.put("submitOrderWithMeasuredCard", "/openapi/order/submitOrderWithMeasuredCard")//提交订单
        PATH.put("couponCancel", "/openapi/voucher/couponCancel")//反核销
        PATH.put("cancelSubmitOrder", "/openapi/order/cancelSubmitOrder")//取消订单

        SUPPORT_MOBILE_API = new ArrayList<>()
        SUPPORT_MOBILE_API.add("https://open-test.hexcloud.net.cn")
        SUPPORT_MOBILE_API.add("https://open.hexcloud.cn")
    }

    @Override
    protected String getSignModuleName() {
        return this.getModuleName()
    }

    @Override
    String getModuleName() {
        return "Member"
    }

    /**
     * 获取会员信息
     * @param channelMemberRequest
     * @return
     */
    @Override
    ChannelMemberResponse getMember(ChannelMemberRequest request) {
        // 兼容 jpg IDS POS参数传错的情况 cardNo 会员卡号错传到了mobile字段
        // mobile 长度超过11位设置为卡号
        if (StrUtil.isNotBlank(request.getMobile())
                && request.getMobile().length() > 17
                && StrUtil.isBlank(request.getCardNo())) {
            request.setCardNo(request.getMobile())
            request.setMobile(null)
        }
        if (!SUPPORT_MOBILE_API.contains(channel.getChannelAccessConfig().getGatewayUrl())) {
            request.setMobile(null)
        }
        // 不支持手机号查询的渠道，不传手机号
        if (StrUtil.isBlank(request.getCardNo())
                && StrUtil.isBlank(request.getMobile())
                && StrUtil.isBlank(request.getMemberCode())
                && StrUtil.isBlank(request.getCouponNo())) {
            throw new CommonException(ServiceError.INVALID_PARAM, "CardNo或Mobile或MemberCode或CouponNo")
        }
        ChannelMemberResponse response = new ChannelMemberResponse()
        // 根据卡号/手机号查询会员信息
        if (StrUtil.isNotBlank(request.getCardNo()) || StrUtil.isNotBlank(request.getMobile())) {
            response = getMemberByCardNoOrMobile(request)
            // 设置卡号/会员码
            response.setCardNo(request.getCardNo())
            request.setMemberCode(response.getMemberCode())
        }
        // 根据会员码查询礼品卡
        if (StrUtil.isNotBlank(request.getMemberCode())) {
            List<Coupon> vouchers = getVouchersByMemberCode(request)
            if (null != response.getCoupons() && CollectionUtil.isNotEmpty(vouchers)) {
                response.getCoupons().addAll(vouchers)
            } else if (CollectionUtil.isNotEmpty(vouchers)) {
                if (CollectionUtil.isNotEmpty(vouchers)) {
                    response.setCoupons(vouchers)
                }
            }
        }
        // 根据券号查询会员和优惠券信息
        if (StrUtil.isNotEmpty(request.getCouponNo())) {
            response = getCouponInfoByCouponNo(request)
        }
        return response
    }

    // 根据券码查询会员与优惠券信息
    private ChannelMemberResponse getCouponInfoByCouponNo(ChannelMemberRequest request) {
        // 业务参数
        Map<String, String> params = new HashMap<>()
        params.put("storeCode", request.getStoreCode())
        params.put("code", request.getCouponNo())
        JSONObject resultJSON = doRequest(params, "getCouInfo", false)
        if (resultJSON.getIntValue("code") != API_SUCCESS_CODE) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON.getString("message"))
        }
        ChannelMemberResponse response = new ChannelMemberResponse()
        response.setChannel(request.getChannel())
        response.setResponseCode("0")
        response.setSuccess(true)

        JSONObject dataJSON = resultJSON.getJSONObject("data")

        // 会员信息
        JSONObject memberJSON = dataJSON.getJSONObject("member")
        response.setCreditBalance(memberJSON.getInteger("pointsBalance"))
        DepositCard card = new DepositCard()
        Integer amount = memberJSON.getIntValue("balance") + memberJSON.getIntValue("giftBalance")
        response.setAccountBalance(amount)
        card.setAmount(amount)
        card.setCardCode(memberJSON.getString("memberCode"))
        response.setDepositCard(Arrays.asList(card))
        response.setMemberCode(memberJSON.getString("memberCode"))
        response.setName(memberJSON.getString("memberName"))
        response.setGradeId(memberJSON.getString("gradeId"))
        response.setGradeName(memberJSON.getString("gradeName"))
        response.setMobile(memberJSON.getString("mobile"))
        response.setCardNo(memberJSON.getString("cardNo"))

        // 优惠券信息
        JSONObject couponJSON = dataJSON.getJSONObject("coupon")
        Coupon coupon = new Coupon()
        String expiredData = couponJSON.getString("expireDate")
        String startDate = couponJSON.getString("startDate")
        coupon.setStartDate(DateUtil.parse(startDate, DatePattern.PURE_DATE_PATTERN).toString())
        coupon.setExpiredDate(DateUtil.parse(expiredData, DatePattern.PURE_DATE_PATTERN).toString())
        coupon.setId(couponJSON.getString("couponId"))
        coupon.setCode(couponJSON.getString("couponCode"))
        coupon.setCodeNo(couponJSON.getString("couponInstanceCode"))
        coupon.setName(couponJSON.getString("couponName"))
        coupon.setType(couponJSON.getString("couponTypeId"))
        coupon.setTypeCode(couponJSON.getString("couponChannelType"))
        if (couponJSON.containsKey("price")) {
            coupon.setOriginPrice(couponJSON.getIntValue("price"))
        }
        if (couponJSON.containsKey("payPrice")) {
            coupon.setPayPrice(couponJSON.getIntValue("payPrice"))
        }
        Rule rule = new Rule()
        JSONObject jsonRule = couponJSON.getJSONObject("rule")
        rule.setAmount(jsonRule.getIntValue("price"))
        rule.setCouponAmount(jsonRule.getIntValue("price"))
        coupon.setRule(rule)
        response.setCoupons(Arrays.asList(coupon))

        return response
    }

    // 根据会员码获取礼品卡
    private List<Coupon> getVouchersByMemberCode(ChannelMemberRequest request) {
        // 业务参数
        Map<String, String> params = new HashMap<>()
        params.put("storeCode", request.getStoreCode())
        params.put("memberCode", request.getMemberCode())
        JSONObject resultJSON = doRequest(params, "getVoucherListByMemberCode", false)
        if (resultJSON.getIntValue("code") != API_SUCCESS_CODE) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON.getString("message"))
        }
        //用户信息
        JSONObject jsonData = resultJSON.getJSONObject("data")
        JSONArray vouchers = jsonData.getJSONArray("vouchers")
        List<Coupon> couponList = new ArrayList<>()
        vouchers.eachWithIndex { it, i ->
            JSONObject voucher = (JSONObject) it
            Coupon coupon = new Coupon()
            //券码
            String expiredData = voucher.getString("expireDate")
            String startDate = voucher.getString("startDate")
            coupon.setStartDate(DateUtil.parse(startDate, DatePattern.PURE_DATE_PATTERN).toString())
            coupon.setExpiredDate(DateUtil.parse(expiredData, DatePattern.PURE_DATE_PATTERN).toString())
            coupon.setId(voucher.getString("voucherId"))
            coupon.setName(voucher.getString("voucherName"))
            coupon.setType(voucher.getString("voucherId"))
            coupon.setTypeCode("10000")
            if (voucher.containsKey("price")) {
                coupon.setOriginPrice(voucher.getIntValue("price"))
            }
            if (voucher.containsKey("payPrice")) {
                coupon.setPayPrice(voucher.getIntValue("payPrice"))
            }
            //券码规则
            Rule rule = new Rule()
            JSONObject jsonRule = voucher.get("rule") as JSONObject
            rule.setAmount(jsonRule.getIntValue("amount"))
            rule.setCouponAmount(jsonRule.getIntValue("couponAmount"))
            coupon.setRule(rule)
            couponList.add(coupon)
        }
        return couponList
    }

    // 根据卡号查询会员信息
    private ChannelMemberResponse getMemberByCardNoOrMobile(ChannelMemberRequest request) {
        // 业务参数
        Map<String, String> params = new HashMap<>()
        params.put("storeCode", request.getStoreCode())
        JSONObject resultJSON
        if (StrUtil.isNotBlank(request.getCardNo())) {
            // 根据卡号查询
            params.put("cardNo", request.getCardNo())
            resultJSON = doRequest(params, "getMemAndCouByCardNo", false)
        } else {
            // 根据手机号查询
            params.put("mobile", request.getMobile())
            resultJSON = doRequest(params, "getMemAndCouByMobile", false)
        }
        if (resultJSON.getIntValue("code") != API_SUCCESS_CODE) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON?.getString("message"))
        }
        ChannelMemberResponse response = new ChannelMemberResponse()
        response.setChannel(request.getChannel())
        response.setResponseCode('0')
        response.setSuccess(true)
        //用户信息
        JSONObject jsonData = resultJSON.getJSONObject("data")
        JSONObject member = jsonData.getJSONObject("member")
        Double pointsBalance = member.getDouble("pointsBalance")
        int balance = (BigDecimal.valueOf(Double.valueOf(pointsBalance)) * new BigDecimal(1))
                .setScale(2, BigDecimal.ROUND_HALF_UP).abs().intValue()
        response.setCreditBalance(balance)
        DepositCard card = new DepositCard()
        Integer amount = member.getIntValue("balance") + member.getIntValue("giftBalance")
        response.setAccountBalance(amount)
        card.setAmount(amount)
        card.setCardCode(member.getString("memberCode"))
        response.setDepositCard(Arrays.asList(card))
        response.setMemberCode(member.getString("memberCode"))
        response.setName(member.getString("name"))
        response.setGradeId(member.getString("gradeId"))
        response.setGradeName(member.getString("gradeName"))
        response.setMobile(member.getString("mobile"))
        // 会员权益
        if (jsonData.containsKey("benefit")) {
            JSONArray benefits = jsonData.getJSONArray("benefit")
            if (CollectionUtil.isNotEmpty(benefits)) {
                List<Benefit> benefitList = benefits.stream().map({ x ->
                    Benefit benefit = new Benefit()
                    benefit.setBenefitId(x as Integer)
                    return benefit
                }).collect(Collectors.toList())
                response.setBenefit(benefitList)
            }
        }
        //卡券
        if (!jsonData.containsKey("coupons")) {
            return response
        }
        JSONArray coupons = jsonData.getJSONArray("coupons")
        if (CollectionUtil.isEmpty(coupons)) {
            return response
        }
        List<Coupon> couponList = new ArrayList<>()
        coupons.eachWithIndex { it, i ->
            JSONObject couponOjb = (JSONObject) it
            Coupon coupon = new Coupon()
            //券码
            String expiredData = couponOjb.getString("expireDate")
            coupon.setExpiredDate(DateUtil.parse(expiredData, DatePattern.PURE_DATE_PATTERN).toString())
            coupon.setCode(couponOjb.getString("couponInstanceCode"))
            coupon.setCouponImgUrl(couponOjb?.getString("couponImg2"))
            coupon.setName(couponOjb.getString("couponName"))
            coupon.setType(couponOjb.getString("couponId"))
            coupon.setTypeCode(couponOjb.getString("couponTypeId"))
            coupon.setIsMember("MEMBERSHIP")
            coupon.setUseType("ONCE")
            if (couponOjb.containsKey("price")) {
                coupon.setOriginPrice(couponOjb.getIntValue("price"))
            }
            if (couponOjb.containsKey("payPrice")) {
                coupon.setPayPrice(couponOjb.getIntValue("payPrice"))
            }
            if (couponOjb.containsKey("isMerchant")) {
                coupon.setIsMerchant(couponOjb.getIntValue("isMerchant"))
            }
            //券码规则
            Rule rule = new Rule()
            JSONObject jsonRule = resultJSON.getJSONObject("rule")
            if (jsonRule != null) {
                rule.setAmount(jsonRule.getIntValue("amount"))
                rule.setCouponAmount(jsonRule.getIntValue("couponAmount"))
                rule.setDiscount(jsonRule.getIntValue("discount"))
                rule.setQuantity(jsonRule.getIntValue("quantity"))
                rule.setProductId(jsonRule.getString("productId"))
                rule.setPlusAmount(jsonRule.getIntValue("plusAmount"))
                rule.setBuyQuantity(jsonRule.getIntValue("buyQuantity"))
                rule.setBuyProductId(jsonRule.getString("buyProductId"))
                rule.setGiveQuantity(jsonRule.getIntValue("giveQuantity"))
                rule.setGiveProductId(jsonRule.getString("giveProductId"))
                coupon.setRule(rule)
            }
            // 商户信息
            JSONObject merchant = couponOjb.getJSONObject("merchant")
            if (merchant != null) {
                coupon.setPayPrice(merchant.getIntValue("payPrice"))
                coupon.setSellPrice(merchant.getIntValue("sellPrice"))
                coupon.setOriginPrice(merchant.getIntValue("originPrice"))
            }
            //使用限制
            JSONObject limited = couponOjb.getJSONObject("limited")
            if (limited != null) {
                coupon.setTodayLimit(limited.getIntValue("todayLimit"))
                coupon.setSumLimit(limited.getIntValue("sumLimit"))
            }
            couponList.add(coupon)
        }
        response.setCoupons(couponList)
        return response
    }

    private Map<String, String> getRequestHeader(String sign) {
        Map<String, String> header = new HashMap<>()
        header.put("tenantId", "1")
        header.put("channelId", channel.getChannelAccessConfig().getMerchantId())
        header.put("sign", sign)
        return header
    }

    private JSONObject doRequest(Map<String, Object> params, String method, boolean reserveData) {
        JSONObject res = new JSONObject()
        res.put("code", API_FAILED_CODE)
        // 签名前对数据做处理
        // 1去掉数组
        // 2去掉结构体字符串
        Map<String, String> signMap = new HashMap<>()
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            // 如果是数组，则跳过
            if (entry.getValue() instanceof JSONArray) {
                continue
            }
            // 如果是字符串并且包含{，则跳过
            if (entry.getValue() instanceof String && entry.getValue().toString().contains("{")) {
                continue
            }
            signMap.put(entry.getKey(), entry.getValue().toString())
        }
        // 签名
        String sign = getSignature(signMap)
        String json = JSON.toJSONString(params)
        ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
        String url = channelAccessConfig.getGatewayUrl() + PATH.get(method)

        Timestamp reqTime = cn.hexcloud.commons.utils.DateUtil.getNowTimeStamp()
        if (reserveData) {
            TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, json)
            TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
        }

        String methodFullName = getMethodFullName(method)
        LoggerUtil.info("{0} is sending message: {1}.", methodFullName, json)

        byte[] result = HttpUtil.doPost(url, json, getRequestHeader(sign))
        if (null == result) {
            LoggerUtil.error("{0} is failed with null result.", null, methodFullName)
            return res
        }
        String resultJSONStr = new String(result, StandardCharsets.UTF_8)
        Timestamp respTime = cn.hexcloud.commons.utils.DateUtil.getNowTimeStamp()
        if (reserveData) {
            TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
            TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
        }

        LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
        JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
        if (null == resultJSON) {
            LoggerUtil.error("{0} is failed with null result.", null, methodFullName)
            return res
        }
        if (resultJSON.containsKey("code")
                && API_SUCCESS_CODE != resultJSON.getIntValue("code")) {
            res.put('message', StringUtils.isNotBlank(resultJSON.getString('message')) ?
                    resultJSON.getString('message') : 'request failed')
            return res
        }

        return resultJSON
    }

    @Override
    String getSignature(Map<String, String> rawMessage) {
        StringBuffer sb = new StringBuffer()
        //参数按照ACCSii排序(升序)
        Map<String, String> sortedMap = new TreeMap<String, String>(rawMessage)
        Set set = sortedMap.entrySet()
        Iterator it = set.iterator()
        int i = 0
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next()
            String k = entry.getKey()
            String value = entry.getValue()
            if (StringUtils.isBlank(value)) {
                continue
            }
            if (i == 0) {
                sb.append(MessageFormat.format("{0}={1}", k, value))
                ++i
            } else {
                sb.append(MessageFormat.format("&{0}={1}", k, value))
            }
        }
        sb.append(MessageFormat.format("&{0}={1}", "key", channel.getChannelAccessConfig().getAppKey()))
        String params = sb.toString()
        String sign = DigestUtils.md5DigestAsHex(params.getBytes("UTF-8"))
        return sign

    }

    @Override
    ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "SeltekMember.getCouponInfo")
    }

    @Override
    CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "SeltekMember.calculatePromotion")
    }

    @Override
    ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest request) {
        ChannelConsumeCouponsResponse response = new ChannelConsumeCouponsResponse()
        response.setChannel(request.getChannel())
        response.setSuccess(false)
        response.setResponseCode("1")
        //会员信息
        Member member = request.getMemberContent()
        if (null == member || null == member.getMemberCode()) {
            response.setMessage(ServiceError.INVALID_PARAM.getMessage("缺少必须参数:MemberCode"))
            return response
        }
        //订单信息
        Order order = request.getOrderContent()
        if (null == order || null == order.getOrderTicketId()) {
            response.setMessage(ServiceError.INVALID_PARAM.getMessage("缺少必须参数:OrderTicketId"))
            return response
        }
        //券信息
        List<CouponReq> couponList = request.getCoupons()
        if (CollectionUtils.isEmpty(couponList)) {
            response.setMessage(ServiceError.INVALID_PARAM.getMessage("缺少优惠券信息"))
            return response
        }

        /// 核销
        response.setSuccess(true)
        response.setResponseCode("0")
        TransactionLoggerContext.set(ContextKeyConstant.ORDER_ID, order.getOrderTicketId())

        List<String> voucherCodes = new ArrayList<>()//代金券
        JSONArray couponCodes = new JSONArray()//商品券
        for (CouponReq coupon : couponList) {
            // 商品券
            if (VOUCHER_TYPE_ID != coupon.getTypeId()) {
                JSONObject object = new JSONObject()
                object.put("couponInstanceCode", coupon.getCodeNo())
                object.put("useTimes", coupon.getQty())
                couponCodes.add(object)
            } else {
                // 代金券
                voucherCodes.add(coupon.getCodeNo())
            }
        }
        // 开始核销代金券
        if (CollectionUtil.isNotEmpty(voucherCodes)) {
            for (String code : voucherCodes) {
                // 业务参数
                Map<String, String> params = new HashMap<>()
                params.put("memberCode", member.getMemberCode())
                params.put("voucherInstanceCode", code)
                params.put("storeCode", request.getStoreCode())
                params.put("externalId", order.getOrderTicketId())
                // 请求第三方接口
                JSONObject resultJSON = doRequest(params, "used", true)
                if (resultJSON.getIntValue("code") != API_SUCCESS_CODE) {
                    response.setMessage(ServiceError.THIRD_PARTY_API_FAILED.getMessage(resultJSON.getString("message")))
                    response.setSuccess(false)
                    response.setResponseCode("1")
                    break
                }
            }
        }
        // 核销商品券
        if (CollectionUtils.isNotEmpty(couponCodes)) {
            // 业务参数
            Map<String, Object> params = new HashMap<>()
            params.put("memberCode", member.getMemberCode())
            params.put("storeCode", request.getStoreCode())
            params.put("externalId", order.getOrderTicketId())
            params.put("usedPoints", Long.toString(request.getUsedPoints()))
            params.put("orderCouponDtoList", couponCodes)
            JSONObject resultJSON = doRequest(params, "submitOrderWithMeasuredCard", true)
            if (resultJSON.getIntValue("code") != API_SUCCESS_CODE) {
                response.setMessage(ServiceError.THIRD_PARTY_API_FAILED.getMessage(resultJSON.getString("message")))
                response.setSuccess(false)
                response.setResponseCode("1")
            }
        }

        return response
    }

    @Override
    ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest request) {
        ChannelCancelCouponsResponse response = new ChannelCancelCouponsResponse()
        response.setChannel(request.getChannel())
        response.setSuccess(false)
        response.setResponseCode("1")
        //会员信息
        Member member = request.getMemberContent()
        if (null == member || null == member.getMemberCode()) {
            response.setMessage(ServiceError.INVALID_PARAM.getMessage("缺少必须参数:MemberCode"))
            return response
        }
        //订单信息
        Order order = request.getOrderContent()
        if (null == order || null == order.getOrderTicketId()) {
            response.setMessage(ServiceError.INVALID_PARAM.getMessage("缺少必须参数:OrderTicketId"))
            return response
        }
        //券信息
        List<CouponReq> couponList = request.getCoupons()
        if (CollectionUtils.isEmpty(couponList)) {
            response.setMessage(ServiceError.INVALID_PARAM.getMessage("缺少优惠券信息"))
            return response
        }

        // 反核销代金券商品券
        response.setSuccess(true)
        response.setResponseCode("0")
        TransactionLoggerContext.set(ContextKeyConstant.ORDER_ID, order.getOrderTicketId())

        JSONArray voucherCodes = new JSONArray()//代金券
        List<String> couponCodes = new ArrayList<>()//商品券
        for (CouponReq coupon : couponList) {
            if (VOUCHER_TYPE_ID != coupon.getTypeId()) {
                couponCodes.add(coupon.getCodeNo())
            } else {
                voucherCodes.add(coupon.getCodeNo())
            }
        }
        if (CollectionUtils.isNotEmpty(voucherCodes)) {
            //业务参数
            Map<String, Object> params = new HashMap<>()
            params.put("memberCode", member.getMemberCode())
            params.put("storeCode", request.getStoreCode())
            params.put("voucherInstanceCodes", voucherCodes)
            JSONObject resultJSON = doRequest(params, "couponCancel", true)
            if (resultJSON.getIntValue("code") != API_SUCCESS_CODE) {
                response.setMessage(ServiceError.THIRD_PARTY_API_FAILED.getMessage(resultJSON?.get("message")))
                response.setSuccess(false)
                response.setResponseCode("1")
            }
        }

        // 反核销商品券
        if (CollectionUtils.isNotEmpty(couponCodes)) {
            // 业务参数
            Map<String, Object> params = new HashMap<>()
            params.put("memberCode", member.getMemberCode())
            params.put("externalId", order.getOrderTicketId())
            JSONObject resultJSON = doRequest(params, "cancelSubmitOrder", true)
            if (resultJSON.getIntValue("code") != API_SUCCESS_CODE) {
                response.setMessage(ServiceError.THIRD_PARTY_API_FAILED.getMessage(resultJSON?.get("message")))
                response.setSuccess(false)
                response.setResponseCode("1")
            }
        }

        return response
    }

    private String getMethodFullName(String method) {
        return channel.getChannelCode() + "." + getModuleName() + "." + method
    }

}
