package cn.hexcloud.pbis.common.service.integration.channel.member.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Coupon
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.CouponAmount
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.SkuDetail
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.CalculatePromotionRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCancelCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelConsumeCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCouponInfoRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelMemberRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.CalculatePromotionResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCancelCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelConsumeCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCouponInfoResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelMemberResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.MemberModule
import cn.hexcloud.pbis.common.service.integration.m82.RedCouponOrderDetailResp
import cn.hexcloud.pbis.common.service.integration.m82.client.M82CouponClient
import cn.hexcloud.pbis.common.service.integration.metadata.Entity
import cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest
import cn.hexcloud.pbis.common.service.integration.metadata.MetadataClient
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import cn.hutool.core.collection.CollectionUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import org.springframework.util.DigestUtils
import java.nio.charset.StandardCharsets
import java.sql.Timestamp

class XiaohongshuISVCouponModule extends AbstractExternalChannelModule implements MemberModule {
  private static final String VERSION = "2.0"
  private static final Map<String, String> METHOD_MAP
  private static int SUCCESS_CODE = 0

  static {
    METHOD_MAP = new HashMap<>()
    METHOD_MAP.put("getCouponInfo", "localLife.queryCouponStatusInfo")
    METHOD_MAP.put("consumeCoupons", "localLife.verifyCoupon")
    METHOD_MAP.put("cancelCoupons", "localLife.verifyCancel")
  }

  XiaohongshuISVCouponModule(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  ChannelMemberResponse getMember(ChannelMemberRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("getMember"))
  }

  static String getStoreCode(String storeId) {
    String code = ""
    try {
      MetadataClient metadataClient = SpringContextUtil.bean(MetadataClient.class)
      Entity storeEntity = metadataClient.getMetadata(GetEntityByIdRequest.newBuilder()
              .setSchemaName("STORE")
              .setId(new Long(storeId)).build())
      code = storeEntity.getFields().getFieldsMap().get("code").getStringValue()
    } catch (Exception ex) {
      LoggerUtil.error("XiaohongshuISVCouponModule.getStoreCode f", ex)
    }
    LoggerUtil.info("XiaohongshuISVCouponModule.getStoreCode, storeId: {0}, code: {1}", storeId, code)
    return code
  }

  @Override
  ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request) {
    if (CollectionUtil.isEmpty(request.getCoupons())) {
      throw new CommonException(ServiceError.PARAM_CONSTRAINTS_VIOLATION, "codeNo")
    }
    // 提取codeNo
    List<String> codeNos = new ArrayList<>()
    request.getCoupons().forEach { coupon ->
      codeNos.add(coupon.getCodeNo())
    }
    LoggerUtil.info("xiaohongshu.getCouponInfo, {0}", JSON.toJSONString(request))

    String storeCode = getStoreCode(request.getStoreId())
    // 调用m82查询订单信息
    Map<String, String> orderInfo = getM82CouponInfo(codeNos, storeCode)
    // 业务参数,请求小红书券状态查询接口
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("page_no", 1)
    bizParams.put("page_size", 10)
    bizParams.put("codes", codeNos)
    bizParams.put("status", 1)
    // 发送请求
    boolean success = false
    String msg = ""
    JSONObject resultJSON = doRequest("getCouponInfo", bizParams, false)
    JSONObject data = resultJSON.getJSONObject("data")
    if (resultJSON.containsKey("success")) {
      success = resultJSON.getBooleanValue("success")
      msg = resultJSON.getString("description")
    } else {
      success = data.getBooleanValue("success")
      msg = data.getString("description")
    }
    if (!success) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, msg)
    }
    // 解析并返回结果
    ChannelCouponInfoResponse response = new ChannelCouponInfoResponse()
    response.setDetails(parseCoupon(data, orderInfo))
    response.setConsumeToken(orderInfo.get("order_id"))
    response.setSuccess(true)
    response.setResponseCode("200")
    return response
  }

  private static List<Coupon> parseCoupon(JSONObject couponJSON, Map<String, String> orderInfo) {
    // 获取券列表
    JSONArray array = couponJSON.getJSONArray("certificates_list")
    if (null == array || array.isEmpty()) {
      return null
    }
    // 解析券列表
    List<Coupon> coupons = new ArrayList<>()
    for (int i = 0; i < array.size(); i++) {
      JSONObject item = array.getJSONObject(i) as JSONObject
      Coupon coupon = new Coupon()
      // 金额信息
      int status = item.getIntValue("status")
      if (status != 1) {
        continue
      }
      coupon.setStatus(status)
      coupon.setCodeNo(item.getString("code"))
      coupon.setId(item.getString("certificate_id"))
      coupon.setCouponTypeId(2)
      // 获取商品信息
      if (orderInfo.containsKey("code_no") && orderInfo.get("code_no") == item.getString("code")) {
        SkuDetail sku = new SkuDetail()
        List<String> strings = new ArrayList<>()
        String skuCode = orderInfo.get("good_code")
        strings.add(skuCode)
        sku.setSkus(strings)
        sku.setCount(strings.size())
        coupon.setSkuDetail(sku)
      }
      JSONObject product = item.getJSONObject("sku")
      coupon.setName(product.getString("sku_name"))
      coupon.setSumLimit(1)
      // 金额信息
      JSONObject amount = item.getJSONObject("amount")
      CouponAmount couponAmount = new CouponAmount()
      couponAmount.setPrice(amount.getIntValue("original_amount"))
      couponAmount.setPayAmount(amount.getIntValue("pay_amount"))
      coupon.setCouponAmount(couponAmount)
      coupons.add(coupon)
    }
    return coupons
  }

  @Override
  CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("calculatePromotion"))
  }

  // 请求券服务
  private static Map<String, String> getM82CouponInfo(List<String> codeNos, String storeCode) {
    Map<String, String> result = new HashMap<>()
    // 根据券码查询订单id
    M82CouponClient m82CouponClient = SpringContextUtil.bean(M82CouponClient.class)
    String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
    LoggerUtil.info("XiaohongshuOperationISV.queryCouponOrderDetail, partnerId: {0}, codeNo: {1}", partnerId, codeNos.get(0))
    RedCouponOrderDetailResp resp = m82CouponClient.queryCouponOrderDetail(partnerId, codeNos.get(0), storeCode)
    LoggerUtil.info("XiaohongshuOperationISV.queryCouponOrderDetail, partnerId: {0}, codeNo: {1}, orderId: {2} , storeCode{3}", partnerId, codeNos.get(0), resp.getThirdOrderId(),storeCode)
    if (!resp.getCanUse()) {
      throw new CommonException(ServiceError.COUPON_NOT_AVAILABLE, resp.getReason())
    }
    result.put("code_no", codeNos.get(0))
    result.put("order_id", resp.getThirdOrderId())
    result.put("good_code", resp.getGoodCode())

    LoggerUtil.info("{0}.getM82CouponInfo{1}", "XiaohongshuOperationISV", JSON.toJSONString(result))
    return result
  }

  @Override
  ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest request) {
    if (CollectionUtil.isEmpty(request.getCoupons())) {
      throw new CommonException(ServiceError.PARAM_CONSTRAINTS_VIOLATION, "codeNo")
    }
    List<Coupon> couponList = new ArrayList<>(request.getCoupons().size())
    // 提取codeNo
    List<String> codeNos = new ArrayList<>()
    request.getCoupons().forEach { coupon ->
      codeNos.add(coupon.getCodeNo())
    }

    // 业务参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("poi_id", getXiaohongshuPoiId(ServiceContext.getString(ContextKeyConstant.STORE_ID)))
    bizParams.put("codes", codeNos)
    bizParams.put("order_id", request.getConsumeToken())
    bizParams.put("verify_method", "1")
    // 发送请求
    JSONObject resultJSON = doRequest("consumeCoupons", bizParams, true)
    boolean success = false
    String errorMessage = "success"
    JSONObject data
    if (resultJSON.containsKey("data")) {
      // 是否核销成功
      data = resultJSON.getJSONObject("data")
      int errorCode = data.getIntValue("error_code")
      if (errorCode == SUCCESS_CODE) {
        success = true
      } else {
        success = false
        errorMessage = data.getString("description")
      }
    } else {
      errorMessage = resultJSON.getString("error_msg")
      success = resultJSON.getBooleanValue("success")
    }
    if (success && null != data) {
      JSONArray nodes = data.getJSONArray("verify_results")
      nodes.forEach { item ->
        JSONObject itemJSON = item as JSONObject
        int status = itemJSON.getIntValue("result")
        if (SUCCESS_CODE != status) {
          success = false
          errorMessage = itemJSON.getString("msg")
        } else {
          Coupon coupon = new Coupon()
          coupon.setCodeNo(itemJSON.getString("code"))
          coupon.setId(itemJSON.getString("certificate_id"))
          coupon.setConsumeTransactionId(itemJSON.getString("verify_id"))
          couponList.add(coupon)
        }
      }
    }

    // 核销上报核销结果
    if (success) {
      syncCouponState(codeNos, 1)
    }

    ChannelConsumeCouponsResponse response = new ChannelConsumeCouponsResponse()
    response.setSuccess(success)
    response.setMessage(errorMessage)
    response.setCouponDetails(couponList)
    response.setResponseCode("200")
    return response
  }

  private static void syncCouponState(List<String> codeNos, int status) {
    try {
      M82CouponClient m82CouponClient = SpringContextUtil.bean(M82CouponClient.class)
      String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
      LoggerUtil.info("XiaohongshuOperationISV.syncCouponStatusUpdate, partnerId: {0}, codeNo: {1}", partnerId, codeNos.get(0))
      m82CouponClient.syncCouponStatusUpdate(codeNos, status)
      LoggerUtil.info("XiaohongshuOperationISV.syncCouponStatusUpdate, partnerId: {0}, codeNo: {1}", partnerId, codeNos.get(0))
    } catch (Exception e) {
      LoggerUtil.error("XiaohongshuOperationISV.syncCouponStatusUpdate f", e)
    }
  }

  private String getXiaohongshuPoiId(String hexStoreId) {
    return channel.getChannelAccessSupportService().getExternalStoreId(channel.getChannelCode(), hexStoreId)
  }

  @Override
  ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest request) {
    boolean success = false
    String errorMessage = "success"
    TransactionLoggerContext.setIfNotExists(ContextKeyConstant.REQUEST_TIME, DateUtil.getNowTimeStamp())
    List<String> codes = new ArrayList<>()
    request.getCoupons().forEach({ c ->
      // 请求参数
      Map<String, Object> bizParams = new HashMap<>()
      bizParams.put("verify_id", c.getConsumeTransactionId()) // 券核销流水号
      bizParams.put("code", c.getCodeNo()) // 券id（券核销后得到）
      // 发起请求
      JSONObject resultJSON = doRequest("cancelCoupons", bizParams, true)
      JSONObject data = resultJSON.getJSONObject("data")
      success = data.getBoolean("success")
      if (!success) {
        errorMessage = data.getString("description")
      } else {
        codes.add(c.getCodeNo())
      }
    })
    TransactionLoggerContext.setIfNotExists(ContextKeyConstant.RESPONSE_TIME, DateUtil.getNowTimeStamp())
    // 反核销上报核销结果
    if (success) {
      syncCouponState(codes, 2)
    }
    // 解析并返回结果
    ChannelCancelCouponsResponse response = new ChannelCancelCouponsResponse()
    response.setSuccess(success)
    response.setMessage(errorMessage)
    response.setErrorMessage(errorMessage)
    response.setResponseCode("200")
    response.setChannel(request.getChannel())
    return response
  }

  private JSONObject doRequest(String method, Map<String, Object> bizParams, boolean reserveData) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    String methodFullName = getMethodFullName(method)
    // 公共参数
    Map<String, String> body = new HashMap<>()
    body.put("appId", channelAccessConfig.getAppId())
    body.put("timestamp", System.currentTimeMillis().toString())
    body.put("version", VERSION)
    body.put("method", METHOD_MAP.get(method))
    // 签名
    String sign = getSignature(body)
    body.put("sign", sign)
    body.put("accessToken", channelAccessConfig.getAuthToken())
    body.putAll(bizParams)
    // 发送请求
    String url = channelAccessConfig.getProperty("gateway_url")
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    String bodyJSON = JSON.toJSONString(body)
    byte[] result = HttpUtil.doPost(url, bodyJSON)
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)
    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.append(ContextKeyConstant.REQUEST_DATA, bodyJSON)
      TransactionLoggerContext.append(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.setIfNotExists(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.setIfNotExists(ContextKeyConstant.RESPONSE_TIME, respTime)
    }
    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)

    return resultJSON
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    // 加工数据并得到签名原文
    StringBuilder sb = new StringBuilder()
    sb.append(rawMessage.get("method"))
    sb.append("?")
            .append("appId=").append(rawMessage.get("appId"))
            .append("&timestamp=").append(rawMessage.get("timestamp"))
            .append("&version=").append(rawMessage.get("version"))
            .append(channel.getChannelAccessConfig().getAppKey())
    // 签名并返回签名信息
    return DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8))
  }

  @Override
  String getModuleName() {
    return "Member"
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }
}
