package cn.hexcloud.pbis.common.service.integration.channel.member.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Coupon
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.CouponReq
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.DepositCard
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Member
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Order
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.PurchasesAnalysis
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Rule
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.SkuDetail
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.CalculatePromotionRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCancelCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelConsumeCouponsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelConsumePointsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelCouponInfoRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelMemberRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelReturnPointsRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.CalculatePromotionResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCancelCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelConsumeCouponsResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelCouponInfoResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelMemberResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.MemberModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateTime
import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.time.DateUtils
import org.springframework.util.DigestUtils

import java.nio.charset.StandardCharsets
import java.sql.Timestamp

/**
 * @program: pbis* @author: miao* @create: 2021-12-27 14:35
 * */
class YunxiMemberModule extends AbstractExternalChannelModule implements MemberModule {

  private static final API_SUCCESS_CODE = 1
  private static final String ADD = "ADD"
  private static final String REDUCE = "REDUCE"

  YunxiMemberModule(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Member"
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }

  private Map<String, String> requestHeader(String model, String method, String msg, String appKey) {
    String ts = String.valueOf(System.currentTimeMillis())
    Map<String, String> headerMap = new HashMap<>()
    headerMap.put("appid", appKey)
    headerMap.put("model", model)
    headerMap.put("method", method)
    headerMap.put("ts", ts)
    headerMap.put("msg", msg)
    String sign = getSignature(headerMap)
    headerMap.put("sign", sign)
    return headerMap
  }

  // 统一请求
  private JSONObject doRequest(String model, String method, String method2, Map<String, Object> bizParams, boolean reserveData) {
    ChannelAccessConfig accessConfig = channel.getChannelAccessConfig()
    // json
    String bodyJSON = JSON.toJSONString(bizParams)
    // header
    Map<String, String> headerMap = requestHeader(model, method, bodyJSON, accessConfig.getAppKey())

    if (StrUtil.isBlank(channel.getChannelAccessConfig().getGatewayUrl())) {
      throw new CommonException(ServiceError.INVALID_PARAM, "gateway_url")
    }
    String methodFullName = getMethodFullName(method2)
    // 发起HTTP请求
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result = HttpUtil.doPost(accessConfig.getGatewayUrl(), bodyJSON, headerMap)
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)
    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.append(ContextKeyConstant.REQUEST_DATA, bodyJSON)
      TransactionLoggerContext.append(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.setIfNotExists(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.setIfNotExists(ContextKeyConstant.RESPONSE_TIME, respTime)
    }
    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
    return resultJSON
  }

  /**
   * 签名
   *
   * @param rawMessage
   * @return
   */
  @Override
  String getSignature(Map<String, String> rawMessage) {
    String ts = rawMessage.get("ts")
    //拼接加密字符串
    StringBuffer b = new StringBuffer()
    b.append(channel.getChannelAccessConfig().getAppKey())
            .append(channel.getChannelAccessConfig().getPrivateKey())
            .append(ts)
            .append(JSON.toJSON(rawMessage.get("msg")).toString())
    LoggerUtil.debug("{0}待加密参数{1}", channel.getChannelAccessConfig().getChannelCode(), b.toString())
    DigestUtils.md5DigestAsHex(b.toString().getBytes(StandardCharsets.UTF_8))
  }

  @Override
  ChannelMemberResponse getMember(ChannelMemberRequest request) {
    // 业务参数
    Map<String, String> businessMap = new HashMap<>()
    businessMap.put("memberCardNo", request.getCardNo())
    businessMap.put("phone", request.getMobile())
    if (StrUtil.isNotBlank(request.getStoreCode())){
      businessMap.put("storeCode", request.getMobile())
    }
    JSONObject jsonData = doRequest("member", "queryExt", "getMember", businessMap, false)
    if (jsonData.getIntValue("code") != API_SUCCESS_CODE) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, jsonData.getString("message"))
    }
    ChannelMemberResponse response = new ChannelMemberResponse()
    response.setSuccess(true)
    response.setResponseCode("0")
    JSONObject result = jsonData.getJSONObject("result")
    // 会员信息
    response.setAvatar(result?.getString("avatar"))
    response.setChannel(channel.getChannelAccessConfig().getChannelCode())
    response.setCardNo(result.getString("memberCardNo"))
    response.setBirthday(result?.getString("birthday"))
    response.setName(result?.getString("nickName"))
    response.setMobile(result?.getString("phone"))
    response.setStatus(result.getInteger("status"))
    // 积分信息
    JSONObject pointDetail = result.getJSONObject("pointDetail")
    if (Objects.nonNull(pointDetail)) {
      response.setCreditBalance(pointDetail.getInteger("availablePoints"))
      if (pointDetail.containsKey("payMoney") && null != pointDetail.getBigDecimal("payMoney")) {
        response.setCreditDeductionAmount(pointDetail.getBigDecimal("payMoney").toString())
      } else {
        response.setCreditDeductionAmount("0")
      }
    }
    // 余额信息
    JSONObject accountDetail = result.getJSONObject("accountDetail")
    if (Objects.nonNull(accountDetail)) {
      response.setAccountBalance((accountDetail.getBigDecimal("balance") * 100).intValue())
    }
    // 账户信息
    DepositCard depositCard = new DepositCard()
    if (StrUtil.isNotBlank(request.getCardNo())) {
      depositCard.setCardCode(request.getCardNo())
    } else {
      depositCard.setCardCode(result.getString("memberCardNo"))
    }
    depositCard.setAmount((accountDetail.getBigDecimal("balance") * 100).intValue())
    //depositCard.setVamount((accountDetail.getBigDecimal("presentBalance") * 100).intValue())
    response.setDepositCard(Arrays.asList(depositCard))
    // 会员等级
    JSONObject levelDetail = result.getJSONObject("levelDetail")
    if (Objects.nonNull(levelDetail)) {
      response.setGradeId(levelDetail.getString("levelCode"))
      response.setGradeName(levelDetail.getString("levelName"))
    }
    // 卡券信息
    ChannelCouponInfoRequest couponInfoRequest = new ChannelCouponInfoRequest()
    Member member = new Member()
    member.setCardNo(response.getCardNo())
    couponInfoRequest.setMemberContent(member)
    ChannelCouponInfoResponse couponInfo = getCouponInfo(couponInfoRequest)
    response.setCoupons(couponInfo.getDetails())
    // 猜你喜欢
    List<PurchasesAnalysis> analysis = purchasesAnalysis(response.getCardNo(), response?.getMobile())
    response.setAnalysis(analysis)
    response.setMemberCode(result.getString("memberCardNo"))
    return response
  }

  /**
   * 云徙猜你喜欢
   *
   * @param cardNo
   * @param phone
   * @param memberCode
   * @return
   */
  def List<PurchasesAnalysis> purchasesAnalysis(String cardNo, String phone) {
    // 业务参数
    Map<String, String> businessMap = new HashMap<>()
    businessMap.put("memberCardNo", cardNo)
    businessMap.put("phone", phone)
    JSONObject jsonData = doRequest("member", "queryPurchasesAnalysis", "purchasesAnalysis", businessMap, false)
    if (jsonData.getIntValue("code") != API_SUCCESS_CODE) {
      return null
    }
    List<PurchasesAnalysis> analyses = new ArrayList<>()
    //卡券列表
    JSONArray analysisList = jsonData.getJSONArray("result")
    if (!analysisList) {
      return analyses
    }
    for (object in analysisList) {
      PurchasesAnalysis analysis = new PurchasesAnalysis()
      JSONObject obj = (JSONObject) object
      analysis.setCode(obj.getString("itemCode"))
      analysis.setName(obj.getString("itemName"))
      analyses.add(analysis)
    }
    return analyses
  }

  @Override
  ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request) {
    // 业务参数
    Map<String, String> businessMap = new HashMap<>()
    businessMap.put("memberCardNo", request.getMemberContent().getCardNo())
    businessMap.put("couponStatus", "10")
    businessMap.put("pageNo", "0")
    businessMap.put("pageSize", "100")
    // 开始请求
    ChannelCouponInfoResponse response = new ChannelCouponInfoResponse()
    JSONObject jsonData = doRequest("member", "couponQuery", "getCouponInfo", businessMap, false)
    if (jsonData.getIntValue("code") != API_SUCCESS_CODE) {
      response.setSuccess(false)
      response.setResponseCode(jsonData.getString("code"))
      return response
    }
    //卡券信息
    response.setDetails(mappingCoupons(jsonData))
    response.setSuccess(true)
    response.setResponseCode("0")
    return response
  }

  // 卡券转换
  // 测试数据 *********** 164251057830601571 ，*********** 164251044327401570
  static List<Coupon> mappingCoupons(JSONObject jsonData) {
    List<Coupon> couponList = new ArrayList<>()
    JSONObject result = jsonData.getJSONObject("result")
    if (!result) {
      return couponList
    }
    //卡券列表
    JSONArray coupons = result.getJSONArray("data")
    if (!coupons) {
      return couponList
    }
    for (object in coupons) {
      Coupon coupon = new Coupon()
      JSONObject obj = (JSONObject) object
      int status = obj.getIntValue("status")
      if (status != 10) {
        //LoggerUtil.info("{0}状态{1}不可用",obj.getString("couponCode"),status)
        continue
      }
      coupon.setId(obj.getString("couponId"))
      coupon.setCode(obj.getString("couponCode"))
      coupon.setName(obj.getString("title"))
      coupon.setType(obj.getString("couponTemplateCode"))
      coupon.setTypeCode(obj.getString("couponCategory"))
      Date stat = DateUtils.parseDate(obj.getString("startTime"), DatePattern.PURE_DATETIME_PATTERN)
      coupon.setStartDate(DateTime.of(stat).toString())
      Date end = DateUtils.parseDate(obj.getString("endTime"), DatePattern.PURE_DATETIME_PATTERN)
      // 过滤无效卡券
      if (end.before(new Date())) {
        //LoggerUtil.info("{0}状态{1}时间不可用{2}",obj.getString("startTime"),obj.getString("endTime"),obj.getString("couponCode"))
        continue
      }
      coupon.setExpiredDate(DateTime.of(end).toString())
      // 使用规则
      Rule rule = new Rule()
      rule.setAmount((obj.getIntValue("amount")))
      rule.setCouponAmount(obj.getIntValue("faceAmount"))
      rule.setDiscount(obj.getIntValue("maxDiscount"))
      // 设置 skus
      JSONArray skus = obj.getJSONArray("items")
      if (skus) {
        List<String> skuItems = new ArrayList<>()
        SkuDetail skuDetail = new SkuDetail()
        for (sku in skus) {
          JSONObject goods = (JSONObject) sku
          skuItems.add(goods.getString("skuCode"))
        }
        skuDetail.setSkus(skuItems)
        skuDetail.setCount(skuItems.size())
        coupon.setSkuDetail(skuDetail)
        rule.setSkus(skuItems)
      }
      coupon.setRule(rule)
      couponList.add(coupon)
    }
    return couponList
  }

  @Override
  CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request) {
    CalculatePromotionResponse response = new CalculatePromotionResponse()
    response.setSuccess(true)
    response.setResponseCode("0")
    return response
  }

  @Override
  ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest request) {
    ChannelConsumeCouponsResponse response = new ChannelConsumeCouponsResponse()
    Map<String, Boolean> consumeResult = new HashMap<>()
    int failed = 0
    // 业务参数
    for (couponReq in request.getCoupons()) {
      Map<String, Object> businessMap = new HashMap<>()
      businessMap.put("storeCode", request.getStoreCode())
      businessMap.put("couponCode", couponReq.getCodeNo())
      businessMap.put("memberCardNo", request.getMemberContent().getMemberCode())
      BigDecimal amount = new BigDecimal(couponReq.getExtend())
      businessMap.put("couponFee", amount.intValue())
      businessMap.put("orderId", request.getOrderContent().getOrderTicketId())
      // 开始请求
      JSONObject jsonData = doRequest("order", "couponUse", "consumeCoupons", businessMap, true)
      consumeResult.put(couponReq.getCodeNo(), jsonData.getIntValue("code") == API_SUCCESS_CODE)
      if (jsonData.getIntValue("code") != API_SUCCESS_CODE) {
        response.setMessage(jsonData.getString("message"))
        failed++
        break
      }
    }
    //没有失败直接返回
    if (failed <= 0) {
      response.setSuccess(true)
      response.setResponseCode('0')
      return response
    } else {
      //处理券核销，如果其中一张券处理失败，全部撤销
      List<CouponReq> needCancel = new ArrayList<>()
      for (coupon in request.getCoupons()) {
        if (consumeResult.get(coupon.getCodeNo()) == Boolean.TRUE) {
          needCancel.add(coupon)
        }
      }
      //执行取消
      if (needCancel) {
        ChannelCancelCouponsRequest cancelCoupon = new ChannelCancelCouponsRequest()
        cancelCoupon.setStoreCode(request.getStoreCode())
        cancelCoupon.setCoupons(needCancel)
        //订单信息
        Order orderContext = new Order()
        orderContext.setOrderTicketId(request.getOrderContent().getOrderTicketId())
        cancelCoupon.setOrderContent(orderContext)
        cancelCoupons(cancelCoupon)
      }
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, response.getMessage())
    }
  }

  @Override
  ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest request) {
    ChannelCancelCouponsResponse response = new ChannelCancelCouponsResponse()
    response.setSuccess(true)
    response.setResponseCode("0")
    // 循环调用
    for (couponReq in request.getCoupons()) {
      Map<String, String> businessMap = new HashMap<>()
      businessMap.put("storeCode", request.getStoreCode())
      businessMap.put("couponCode", couponReq.getCodeNo())
      businessMap.put("orderId", request.getOrderContent().getOrderTicketId())
      // 开始请求
      JSONObject jsonData = doRequest("order", "couponReturn", "cancelCoupons", businessMap, true)
      if (jsonData.getIntValue("code") != API_SUCCESS_CODE) {
        response.setMessage(jsonData.getString("message"))
        response.setSuccess(false)
      }
    }
    return response
  }

  /**
   * 积分消费
   * https://www.apifox.cn/apidoc/shared-bd1ad6b2-c31d-461e-b5bd-0603889f861c/api-23274272
   * aGX7z7Vm
   * @param request
   * @return
   */
  @Override
  ChannelResponse consumePoints(ChannelConsumePointsRequest request) {
    ChannelResponse response = new ChannelResponse()
    response.setErrorCode("SUCCESS")

    Map<String, Object> businessMap = new HashMap<>()
    // 会员信息
    Member member = request.getMemberContent()
    // 订单信息
    Order order = request.getOrderContent()

    businessMap.put("operator", REDUCE)
    businessMap.put("memberCardNo", member.getCardNo())
    businessMap.put("storeCode", ServiceContext.getString(ContextKeyConstant.STORE_CODE))
    businessMap.put("pointPay", request.isUsePoint())
    businessMap.put("orderNo", order.getOrderTicketId())
    // 积分抵现
    if (request.isUsePoint()) {
      businessMap.put("pointAmount", request.getAmount())
    } else {
      businessMap.put("point", request.getPoint())
    }
    businessMap.put("channel", "pos")
    JSONObject resultJSON = doRequest("point", "operator", "consumePoints", businessMap, true)
    if (resultJSON.getIntValue("code") != API_SUCCESS_CODE) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON.getString("message"))
    }
    response.setMessage(resultJSON.getString("message"))
    response.setChannel(request.getChannel())
    return response
  }

  @Override
  ChannelResponse returnPoints(ChannelReturnPointsRequest request) {
    ChannelResponse response = new ChannelResponse()
    response.setErrorCode("SUCCESS")
    Map<String, Object> businessMap = new HashMap<>()
    // 会员信息
    Member member = request.getMemberContent()
    // 订单信息
    Order order = request.getOrderContent()

    businessMap.put("operator", ADD)
    businessMap.put("memberCardNo", member.getCardNo())
    businessMap.put("storeCode", ServiceContext.getString(ContextKeyConstant.STORE_CODE))
    businessMap.put("pointPay", request.isUsePoint())
    businessMap.put("orderNo", order.getOrderTicketId())
    // 积分抵现
    if (request.isUsePoint()) {
      businessMap.put("pointAmount", request.getAmount())
    } else {
      businessMap.put("point", request.getPoint())
    }
    businessMap.put("channel", "pos")
    JSONObject resultJSON = doRequest("point", "operator", "returnPoints", businessMap, true)
    if (resultJSON.getIntValue("code") != API_SUCCESS_CODE) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON.getString("message"))
    }
    return response
  }
}