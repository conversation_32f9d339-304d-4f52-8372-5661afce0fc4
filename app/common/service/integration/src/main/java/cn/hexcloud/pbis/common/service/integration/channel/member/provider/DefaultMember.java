package cn.hexcloud.pbis.common.service.integration.channel.member.provider;

import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannel;
import cn.hexcloud.pbis.common.service.integration.channel.ChannelAccessSupportService;
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

/**
 * @program: pbis
 * @author: miao
 * @create: 2021-11-23 17:18
 **/
@Service
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
public class DefaultMember extends AbstractExternalChannel implements MemberChannel {

  @Override
  public String getChannelCode() {
    return channelAccessConfig.getChannelCode();
  }

  @Override
  public String getChannelName() {
    return channelAccessConfig.getChannelCode();
  }

  @Override
  public void init(ChannelAccessConfig channelAccessConfig) {
    this.channelAccessConfig = channelAccessConfig;
  }

  @Override
  public ChannelAccessConfig getChannelAccessConfig() {
    return channelAccessConfig;
  }

  @Override
  public ChannelAccessSupportService getChannelAccessSupportService() {
    return channelAccessSupportService;
  }

  @Override
  public MemberModule getMemberModule() {
    return new DefaultMemberModule(this);
  }
}
