package cn.hexcloud.pbis.common.service.integration.channel.member.provider;

import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule;
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.*;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.*;

import java.util.Map;

/**
 * @ClassName DefaultMemberModule.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2022/01/22 16:38:38
 */
public class DefaultMemberModule extends AbstractExternalChannelModule implements MemberModule {

  public DefaultMemberModule(ExternalChannel channel) {
    super(channel);
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName();
  }

  @Override
  public ChannelMemberResponse getMember(ChannelMemberRequest request) {
    return (ChannelMemberResponse) channel.doRequest(this.getModuleName(), "getMember", request);
  }

  @Override
  public ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request) {
    return (ChannelCouponInfoResponse) channel.doRequest(this.getModuleName(), "getCouponInfo", request);
  }

  @Override
  public CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request) {
    return (CalculatePromotionResponse) channel.doRequest(this.getModuleName(), "calculatePromotion", request);
  }

  @Override
  public ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest request) {
    return (ChannelConsumeCouponsResponse) channel.doRequest(this.getModuleName(), "consumeCoupons", request);
  }

  @Override
  public ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest request) {
    return (ChannelCancelCouponsResponse) channel.doRequest(this.getModuleName(), "cancelCoupons", request);
  }

  @Override
  public ChannelResponse consumePoints(ChannelConsumePointsRequest request) {
    return (ChannelResponse) channel.doRequest(this.getModuleName(), "consumePoints", request);
  }

  @Override
  public ChannelResponse returnPoints(ChannelReturnPointsRequest request) {
    return (ChannelResponse) channel.doRequest(this.getModuleName(), "returnPoints", request);
  }

  @Override
  public AvailablePointsResponse getAvailablePoints(AvailablePointsRequest request) {
    return (AvailablePointsResponse) channel.doRequest(this.getModuleName(), "getAvailablePoints", request);
  }

  @Override
  public String getSignature(Map<String, String> rawMessage) {
    return super.getSignature(rawMessage);
  }

  @Override
  public boolean isValidSignature(Map<String, String> unverifiedMessage) {
    return super.isValidSignature(unverifiedMessage);
  }
}
