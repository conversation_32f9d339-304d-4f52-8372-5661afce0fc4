package cn.hexcloud.pbis.common.service.integration.channel.member.provider;

import cn.hexcloud.commons.utils.SpringContextUtil;
import cn.hexcloud.pbis.common.service.integration.channel.ChannelHub;
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @Classname MemberChannelHub
 * @Description:
 * @Date 2021/10/2611:27 上午
 * <AUTHOR>
 */
@Component
public class MemberChannelHub implements ChannelHub<MemberChannel> {

  @Resource
  private Map<String, MemberChannel> memberChannelMap;

  @Override
  public MemberChannel on(String channelCode) {
    MemberChannel memberChannel = memberChannelMap.get(channelCode);
    if (null == memberChannel) {
//      memberChannel = memberChannelMap.get("defaultMember");
      memberChannel = SpringContextUtil.bean(DefaultMember.class);
    }
    return memberChannel;
  }

  public MemberChannel on(String channelCode, ChannelAccessConfig channelAccessConfig) {
    MemberChannel memberChannel = this.on(channelCode);
    memberChannel.init(channelAccessConfig);
    return memberChannel;
  }
}
