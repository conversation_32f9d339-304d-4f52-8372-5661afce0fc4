package cn.hexcloud.pbis.common.service.integration.channel.member.provider;

import cn.hexcloud.commons.exception.CommonException;
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannelModule;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.*;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.*;
import cn.hexcloud.pbis.common.util.exception.ServiceError;

/**
 * @ClassName MemberModule.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2022/01/22 16:33:11
 */
public interface MemberModule extends ExternalChannelModule {

  ChannelMemberResponse getMember(ChannelMemberRequest request);

  ChannelCouponInfoResponse getCouponInfo(ChannelCouponInfoRequest request);

  CalculatePromotionResponse calculatePromotion(CalculatePromotionRequest request);

  ChannelConsumeCouponsResponse consumeCoupons(ChannelConsumeCouponsRequest request);

  ChannelCancelCouponsResponse cancelCoupons(ChannelCancelCouponsRequest request);

  default AvailablePointsResponse getAvailablePoints(AvailablePointsRequest request){
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "getAvailablePoints");
  }

  default ChannelResponse consumePoints(ChannelConsumePointsRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "consumePoints");
  }

  default ChannelResponse returnPoints(ChannelReturnPointsRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "returnPoints");
  }

  @Override
  default String getModuleName() {
    return "Member";
  }

}
