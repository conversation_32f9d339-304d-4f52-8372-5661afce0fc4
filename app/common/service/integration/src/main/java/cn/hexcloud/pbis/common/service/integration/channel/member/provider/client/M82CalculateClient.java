package cn.hexcloud.pbis.common.service.integration.channel.member.provider.client;

import cn.hexcloud.pbis.common.service.integration.m82.BackPromotionsIn;
import cn.hexcloud.pbis.common.service.integration.m82.BackPromotionsOut;
import cn.hexcloud.pbis.common.service.integration.m82.CalculateGrpc;
import cn.hexcloud.pbis.common.service.integration.m82.DiscountCalculateIn;
import cn.hexcloud.pbis.common.service.integration.m82.DiscountCalculateOut;
import cn.hexcloud.pbis.common.service.integration.m82.UsePromotionsIn;
import cn.hexcloud.pbis.common.service.integration.m82.UsePromotionsOut;
import cn.hexcloud.pbis.common.util.config.PBISApplicationConfig;
import io.grpc.Channel;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import net.devh.boot.grpc.client.inject.GrpcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: pbis
 * @author: miao
 * @create: 2022-01-09 17:07
 **/
@Service
public class M82CalculateClient {

  @GrpcClient("M82CalculateService")
  private Channel serviceChannel;

  @Autowired
  private PBISApplicationConfig pbisApplicationConfig;

  private CalculateGrpc.CalculateBlockingStub calculateBlockingStub;

  @PostConstruct
  public void init() {
    this.calculateBlockingStub = CalculateGrpc.newBlockingStub(serviceChannel);
  }

  /**
   * 算价
   *
   * @param discountCalculateIn
   * @return
   */
  public DiscountCalculateOut discountCalculate(DiscountCalculateIn discountCalculateIn) {
    return calculateBlockingStub
        .withDeadlineAfter(pbisApplicationConfig.getGrpcClientTimeout(), TimeUnit.MILLISECONDS)
        .discountCalculate(discountCalculateIn);
  }

  /**
   * 卡券核销
   *
   * @param usePromotionsIn
   * @return
   */
  public UsePromotionsOut usePromotions(UsePromotionsIn usePromotionsIn) {
    return calculateBlockingStub
        .withDeadlineAfter(pbisApplicationConfig.getGrpcClientTimeout(), TimeUnit.MILLISECONDS)
        .usePromotions(usePromotionsIn);
  }

  /**
   * 卡券撤销
   *
   * @param backPromotionsIn
   * @return
   */
  public BackPromotionsOut backPromotions(BackPromotionsIn backPromotionsIn) {
    return calculateBlockingStub
        .withDeadlineAfter(pbisApplicationConfig.getGrpcClientTimeout(), TimeUnit.MILLISECONDS)
        .backPromotions(backPromotionsIn);
  }

}
