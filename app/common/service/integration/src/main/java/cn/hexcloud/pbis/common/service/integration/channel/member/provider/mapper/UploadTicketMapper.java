package cn.hexcloud.pbis.common.service.integration.channel.member.provider.mapper;

import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Amount;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Channel;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Efficiency;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Fee;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Member;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Operator;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Payment;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Pos;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Promotion;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Refund;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.SkuName;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.SkuRemark;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.SkuValue;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Store;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Table;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Takeaway;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Tax;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Ticket;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.TicketCoupon;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.TicketProduct;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelSyncOrderRequest;
import cn.hexcloud.pbis.common.service.integration.eticket.Product;
import cn.hexcloud.pbis.common.service.integration.eticket.SkuRemark.skuName;
import cn.hexcloud.pbis.common.service.integration.eticket.SkuRemark.skuValue;
import cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

/**
 * @Classname ChannelSyncOrderMapper
 * @Description:
 * @Date 2021/10/297:06 下午
 * <AUTHOR>
 */

public interface UploadTicketMapper {



  //@Mapping(source = "ticket", target = "ticket")
  UploadTicketRequest toUploadTicket(ChannelSyncOrderRequest channelSyncOrderRequest);

 // @Mapping(source = "detailFees", target = "detailFeesList")
  cn.hexcloud.pbis.common.service.integration.eticket.Fee feeToFee(Fee fee);


  cn.hexcloud.pbis.common.service.integration.eticket.Payment paymentToPayment(Payment payment);

 // @Mapping(source = "products", target = "productsList")
  cn.hexcloud.pbis.common.service.integration.eticket.Promotion promotionToPromotion(Promotion promotion);

  cn.hexcloud.pbis.common.service.integration.eticket.Member memberToMember(Member member);

  cn.hexcloud.pbis.common.service.integration.eticket.Tax taxToTax(Tax tax);

  cn.hexcloud.pbis.common.service.integration.eticket.Coupon ticketCouponToCoupon(TicketCoupon coupon);

  cn.hexcloud.pbis.common.service.integration.eticket.Pos posToPos(Pos pos);

  cn.hexcloud.pbis.common.service.integration.eticket.Operator operatorToOperator(Operator operator);

  cn.hexcloud.pbis.common.service.integration.eticket.Amount amountToAmount(Amount amount);

  cn.hexcloud.pbis.common.service.integration.eticket.RefundInfo refundToRefundInfo(Refund refundInfo);

  cn.hexcloud.pbis.common.service.integration.eticket.Channel channelToChannel(Channel channel);

  cn.hexcloud.pbis.common.service.integration.eticket.Table tableToTable(Table table);

  cn.hexcloud.pbis.common.service.integration.eticket.Store storeToStore(Store store);

  cn.hexcloud.pbis.common.service.integration.eticket.Takeaway takeawayToTakeaway(Takeaway takeaway);

  cn.hexcloud.pbis.common.service.integration.eticket.Efficiency efficiencyToEfficiency(Efficiency efficiency);

  cn.hexcloud.pbis.common.service.integration.eticket.SkuRemark skuRemarkToSkuRemark(SkuRemark skuRemark);

  cn.hexcloud.pbis.common.service.integration.eticket.Product productToProductList(TicketProduct product);










}
