package cn.hexcloud.pbis.common.service.integration.channel.member.provider.mapper;

import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Amount;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Pos;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Promotion;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.PromotionInfo;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.PromotionProduct;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Refund;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.SkuName;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.SkuValue;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Takeaway;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.TicketCoupon;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.TicketProduct;
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelSyncOrderRequest;
import cn.hexcloud.pbis.common.service.integration.eticket.Channel;
import cn.hexcloud.pbis.common.service.integration.eticket.Coupon;
import cn.hexcloud.pbis.common.service.integration.eticket.Efficiency;
import cn.hexcloud.pbis.common.service.integration.eticket.Fee;
import cn.hexcloud.pbis.common.service.integration.eticket.Member;
import cn.hexcloud.pbis.common.service.integration.eticket.Operator;
import cn.hexcloud.pbis.common.service.integration.eticket.Payment;
import cn.hexcloud.pbis.common.service.integration.eticket.Product;
import cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource;
import cn.hexcloud.pbis.common.service.integration.eticket.RefundInfo;
import cn.hexcloud.pbis.common.service.integration.eticket.SkuRemark;
import cn.hexcloud.pbis.common.service.integration.eticket.SkuRemark.skuName;
import cn.hexcloud.pbis.common.service.integration.eticket.SkuRemark.skuValue;
import cn.hexcloud.pbis.common.service.integration.eticket.Store;
import cn.hexcloud.pbis.common.service.integration.eticket.Table;
import cn.hexcloud.pbis.common.service.integration.eticket.Tax;
import cn.hexcloud.pbis.common.service.integration.eticket.Ticket;
import cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest;
import cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest.Builder;
import javax.annotation.Generated;


public class UploadTicketMapperImpl implements UploadTicketMapper {

    @Override
    public UploadTicketRequest toUploadTicket(ChannelSyncOrderRequest channelSyncOrderRequest) {
        if ( channelSyncOrderRequest == null ) {
            return null;
        }

        Builder uploadTicketRequest = UploadTicketRequest.newBuilder();

        if ( channelSyncOrderRequest.getTicket() != null ) {
            uploadTicketRequest.setTicket( ticketToTicket( channelSyncOrderRequest.getTicket() ) );
        }

        return uploadTicketRequest.build();
    }

    @Override
    public Fee feeToFee(cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Fee fee) {
        if ( fee == null ) {
            return null;
        }

        Fee.Builder fee1 = Fee.newBuilder();

        if ( fee.getDetailFees() != null ) {
            for ( cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Fee detailFee : fee.getDetailFees() ) {
                fee1.addDetailFees( feeToFee( detailFee ) );
            }
        }
        if ( fee.getName() != null ) {
            fee1.setName( fee.getName() );
        }
        if ( fee.getPrice() != null ) {
            fee1.setPrice( fee.getPrice().doubleValue() );
        }
        fee1.setQty( fee.getQty() );
        if ( fee.getAmount() != null ) {
            fee1.setAmount( fee.getAmount().doubleValue() );
        }
        if ( fee.getType() != null ) {
            fee1.setType( fee.getType() );
        }

        return fee1.build();
    }

    @Override
    public Payment paymentToPayment(cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Payment payment) {
        if ( payment == null ) {
            return null;
        }

        Payment.Builder payment1 = Payment.newBuilder();

        if ( payment.getId() != null ) {
            payment1.setId( payment.getId() );
        }
        if ( payment.getSeqId() != null ) {
            payment1.setSeqId( payment.getSeqId() );
        }
        if ( payment.getPayAmount() != null ) {
            payment1.setPayAmount( payment.getPayAmount().doubleValue() );
        }
        if ( payment.getRealPayAmount() != null ) {
            payment1.setRealPayAmount( payment.getRealPayAmount().doubleValue() );
        }
        if ( payment.getChange() != null ) {
            payment1.setChange( payment.getChange().doubleValue() );
        }
        if ( payment.getOverflow() != null ) {
            payment1.setOverflow( payment.getOverflow().doubleValue() );
        }
        if ( payment.getRounding() != null ) {
            payment1.setRounding( payment.getRounding().doubleValue() );
        }
        if ( payment.getPayTime() != null ) {
            payment1.setPayTime( payment.getPayTime() );
        }
        if ( payment.getTransCode() != null ) {
            payment1.setTransCode( payment.getTransCode() );
        }
        if ( payment.getName() != null ) {
            payment1.setName( payment.getName() );
        }
        if ( payment.getReceivable() != null ) {
            payment1.setReceivable( payment.getReceivable().doubleValue() );
        }
        if ( payment.getTpTransactionNo() != null ) {
            payment1.setTpTransactionNo( payment.getTpTransactionNo() );
        }
        if ( payment.getTpAllowance() != null ) {
            payment1.setTpAllowance( payment.getTpAllowance().floatValue() );
        }
        if ( payment.getMerchantAllowance() != null ) {
            payment1.setMerchantAllowance( payment.getMerchantAllowance().floatValue() );
        }
        if ( payment.getTransName() != null ) {
            payment1.setTransName( payment.getTransName() );
        }
        if ( payment.getPrice() != null ) {
            payment1.setPrice( payment.getPrice().floatValue() );
        }
        if ( payment.getCost() != null ) {
            payment1.setCost( payment.getCost().floatValue() );
        }
        if ( payment.getRealAmount() != null ) {
            payment1.setRealAmount( payment.getRealAmount().floatValue() );
        }
        payment1.setHasInvoiced( payment.isHasInvoiced() );
        if ( payment.getTransferAmount() != null ) {
            payment1.setTransferAmount( payment.getTransferAmount().doubleValue() );
        }
        if ( payment.getPlatformAllowance() != null ) {
            payment1.setPlatformAllowance( payment.getPlatformAllowance().doubleValue() );
        }

        return payment1.build();
    }

    @Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Promotion promotionToPromotion(Promotion promotion) {
        if ( promotion == null ) {
            return null;
        }

        cn.hexcloud.pbis.common.service.integration.eticket.Promotion.Builder promotion1 = cn.hexcloud.pbis.common.service.integration.eticket.Promotion.newBuilder();

        if ( promotion.getProducts() != null ) {
            for ( PromotionProduct product : promotion.getProducts() ) {
                promotion1.addProducts( promotionProductToPromotionProduct( product ) );
            }
        }
        if ( promotion.getPromotionInfo() != null ) {
            promotion1.setPromotionInfo( promotionInfoToPromotionInfo( promotion.getPromotionInfo() ) );
        }
        if ( promotion.getSource() != null ) {
            promotion1.setSource( promotionSourceToPromotionSource( promotion.getSource() ) );
        }

        return promotion1.build();
    }

    @Override
    public Member memberToMember(cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Member member) {
        if ( member == null ) {
            return null;
        }

        Member.Builder member1 = Member.newBuilder();

        if ( member.getMemberCode() != null ) {
            member1.setMemberCode( member.getMemberCode() );
        }
        if ( member.getMobile() != null ) {
            member1.setMobile( member.getMobile() );
        }
        if ( member.getBalancePoints() != null ) {
            member1.setBalancePoints( member.getBalancePoints() );
        }
        if ( member.getTotalPoints() != null ) {
            member1.setTotalPoints( member.getTotalPoints() );
        }
        if ( member.getOrderPoints() != null ) {
            member1.setOrderPoints( member.getOrderPoints() );
        }
        if ( member.getGradeCode() != null ) {
            member1.setGradeCode( member.getGradeCode() );
        }
        if ( member.getGradeName() != null ) {
            member1.setGradeName( member.getGradeName() );
        }

        return member1.build();
    }

    @Override
    public Tax taxToTax(cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Tax tax) {
        if ( tax == null ) {
            return null;
        }

        Tax.Builder tax1 = Tax.newBuilder();

        if ( tax.getAmount() != null ) {
            tax1.setAmount( tax.getAmount().doubleValue() );
        }
        if ( tax.getSubTotal() != null ) {
            tax1.setSubTotal( tax.getSubTotal().doubleValue() );
        }
        if ( tax.getCode() != null ) {
            tax1.setCode( tax.getCode() );
        }
        if ( tax.getName() != null ) {
            tax1.setName( tax.getName() );
        }
        if ( tax.getRate() != null ) {
            tax1.setRate( tax.getRate().doubleValue() );
        }

        return tax1.build();
    }

    @Override
    public Coupon ticketCouponToCoupon(TicketCoupon coupon) {
        if ( coupon == null ) {
            return null;
        }

        Coupon.Builder coupon1 = Coupon.newBuilder();

        if ( coupon.getId() != null ) {
            coupon1.setId( coupon.getId() );
        }
        if ( coupon.getName() != null ) {
            coupon1.setName( coupon.getName() );
        }
        if ( coupon.getCode() != null ) {
            coupon1.setCode( coupon.getCode() );
        }
        coupon1.setType( coupon.getType() );
        if ( coupon.getParValue() != null ) {
            coupon1.setParValue( coupon.getParValue().doubleValue() );
        }
        if ( coupon.getSequenceId() != null ) {
            coupon1.setSequenceId( coupon.getSequenceId() );
        }
        if ( coupon.getPrice() != null ) {
            coupon1.setPrice( coupon.getPrice().floatValue() );
        }
        if ( coupon.getCost() != null ) {
            coupon1.setCost( coupon.getCost().floatValue() );
        }
        if ( coupon.getTpAllowance() != null ) {
            coupon1.setTpAllowance( coupon.getTpAllowance().floatValue() );
        }
        if ( coupon.getMerchantAllowance() != null ) {
            coupon1.setMerchantAllowance( coupon.getMerchantAllowance().floatValue() );
        }
        if ( coupon.getTransferAmount() != null ) {
            coupon1.setTransferAmount( coupon.getTransferAmount().doubleValue() );
        }
        if ( coupon.getPlatformAllowance() != null ) {
            coupon1.setPlatformAllowance( coupon.getPlatformAllowance().doubleValue() );
        }

        return coupon1.build();
    }

    @Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Pos posToPos(Pos pos) {
        if ( pos == null ) {
            return null;
        }

        cn.hexcloud.pbis.common.service.integration.eticket.Pos.Builder pos1 = cn.hexcloud.pbis.common.service.integration.eticket.Pos.newBuilder();

        if ( pos.getId() != null ) {
            pos1.setId( pos.getId() );
        }
        if ( pos.getCode() != null ) {
            pos1.setCode( pos.getCode() );
        }
        if ( pos.getPosName() != null ) {
            pos1.setPosName( pos.getPosName() );
        }
        if ( pos.getDeviceId() != null ) {
            pos1.setDeviceId( pos.getDeviceId() );
        }
        if ( pos.getDeviceCode() != null ) {
            pos1.setDeviceCode( pos.getDeviceCode() );
        }

        return pos1.build();
    }

    @Override
    public Operator operatorToOperator(cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Operator operator) {
        if ( operator == null ) {
            return null;
        }

        Operator.Builder operator1 = Operator.newBuilder();

        if ( operator.getId() != null ) {
            operator1.setId( operator.getId() );
        }
        if ( operator.getName() != null ) {
            operator1.setName( operator.getName() );
        }
        if ( operator.getCode() != null ) {
            operator1.setCode( operator.getCode() );
        }
        if ( operator.getLoginTime() != null ) {
            operator1.setLoginTime( operator.getLoginTime() );
        }
        if ( operator.getLoginId() != null ) {
            operator1.setLoginId( operator.getLoginId() );
        }

        return operator1.build();
    }

    @Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Amount amountToAmount(Amount amount) {
        if ( amount == null ) {
            return null;
        }

        cn.hexcloud.pbis.common.service.integration.eticket.Amount.Builder amount1 = cn.hexcloud.pbis.common.service.integration.eticket.Amount.newBuilder();

        if ( amount.getTaxAmount() != null ) {
            amount1.setTaxAmount( amount.getTaxAmount().doubleValue() );
        }
        if ( amount.getGrossAmount() != null ) {
            amount1.setGrossAmount( amount.getGrossAmount().doubleValue() );
        }
        if ( amount.getNetAmount() != null ) {
            amount1.setNetAmount( amount.getNetAmount().doubleValue() );
        }
        if ( amount.getPayAmount() != null ) {
            amount1.setPayAmount( amount.getPayAmount().doubleValue() );
        }
        if ( amount.getDiscountAmount() != null ) {
            amount1.setDiscountAmount( amount.getDiscountAmount().doubleValue() );
        }
        if ( amount.getRounding() != null ) {
            amount1.setRounding( amount.getRounding().doubleValue() );
        }
        if ( amount.getOverflowAmount() != null ) {
            amount1.setOverflowAmount( amount.getOverflowAmount().doubleValue() );
        }
        if ( amount.getChangeAmount() != null ) {
            amount1.setChangeAmount( amount.getChangeAmount().doubleValue() );
        }
        if ( amount.getServiceFee() != null ) {
            amount1.setServiceFee( amount.getServiceFee().doubleValue() );
        }
        if ( amount.getTip() != null ) {
            amount1.setTip( amount.getTip().doubleValue() );
        }
        if ( amount.getCommission() != null ) {
            amount1.setCommission( amount.getCommission().doubleValue() );
        }
        if ( amount.getAmount0() != null ) {
            amount1.setAmount0( amount.getAmount0().doubleValue() );
        }
        if ( amount.getAmount1() != null ) {
            amount1.setAmount1( amount.getAmount1().doubleValue() );
        }
        if ( amount.getAmount2() != null ) {
            amount1.setAmount2( amount.getAmount2().doubleValue() );
        }
        if ( amount.getAmount3() != null ) {
            amount1.setAmount3( amount.getAmount3().doubleValue() );
        }
        if ( amount.getAmount4() != null ) {
            amount1.setAmount4( amount.getAmount4().doubleValue() );
        }
        amount1.setTaxIncluded( amount.isTaxIncluded() );
        if ( amount.getOtherFee() != null ) {
            amount1.setOtherFee( amount.getOtherFee().floatValue() );
        }
        if ( amount.getMerchantDiscountAmount() != null ) {
            amount1.setMerchantDiscountAmount( amount.getMerchantDiscountAmount().floatValue() );
        }
        if ( amount.getPlatformDiscountAmount() != null ) {
            amount1.setPlatformDiscountAmount( amount.getPlatformDiscountAmount().floatValue() );
        }
        if ( amount.getProjectedIncome() != null ) {
            amount1.setProjectedIncome( amount.getProjectedIncome().floatValue() );
        }
        if ( amount.getReceivable() != null ) {
            amount1.setReceivable( amount.getReceivable().doubleValue() );
        }
        if ( amount.getRealAmount() != null ) {
            amount1.setRealAmount( amount.getRealAmount().doubleValue() );
        }
        if ( amount.getBusinessAmount() != null ) {
            amount1.setBusinessAmount( amount.getBusinessAmount().doubleValue() );
        }
        if ( amount.getExpendAmount() != null ) {
            amount1.setExpendAmount( amount.getExpendAmount().doubleValue() );
        }
        if ( amount.getPaymentTransferAmount() != null ) {
            amount1.setPaymentTransferAmount( amount.getPaymentTransferAmount().doubleValue() );
        }
        if ( amount.getDiscountTransferAmount() != null ) {
            amount1.setDiscountTransferAmount( amount.getDiscountTransferAmount().doubleValue() );
        }
        if ( amount.getStoreDiscountAmount() != null ) {
            amount1.setStoreDiscountAmount( amount.getStoreDiscountAmount().doubleValue() );
        }
        if ( amount.getDiscountMerchantContribute() != null ) {
            amount1.setDiscountMerchantContribute( amount.getDiscountMerchantContribute().doubleValue() );
        }
        if ( amount.getDiscountPlatformContribute() != null ) {
            amount1.setDiscountPlatformContribute( amount.getDiscountPlatformContribute().doubleValue() );
        }
        if ( amount.getDiscountBuyerContribute() != null ) {
            amount1.setDiscountBuyerContribute( amount.getDiscountBuyerContribute().doubleValue() );
        }
        if ( amount.getDiscountOtherContribute() != null ) {
            amount1.setDiscountOtherContribute( amount.getDiscountOtherContribute().doubleValue() );
        }
        if ( amount.getPayMerchantContribute() != null ) {
            amount1.setPayMerchantContribute( amount.getPayMerchantContribute().doubleValue() );
        }
        if ( amount.getPayPlatformContribute() != null ) {
            amount1.setPayPlatformContribute( amount.getPayPlatformContribute().doubleValue() );
        }
        if ( amount.getPayBuyerContribute() != null ) {
            amount1.setPayBuyerContribute( amount.getPayBuyerContribute().doubleValue() );
        }
        if ( amount.getPayOtherContribute() != null ) {
            amount1.setPayOtherContribute( amount.getPayOtherContribute().doubleValue() );
        }
        if ( amount.getDeliveryFee() != null ) {
            amount1.setDeliveryFee( amount.getDeliveryFee().doubleValue() );
        }
        if ( amount.getDeliveryFeeForPlatform() != null ) {
            amount1.setDeliveryFeeForPlatform( amount.getDeliveryFeeForPlatform().doubleValue() );
        }
        if ( amount.getDeliveryFeeForMerchant() != null ) {
            amount1.setDeliveryFeeForMerchant( amount.getDeliveryFeeForMerchant().doubleValue() );
        }

        return amount1.build();
    }

    @Override
    public RefundInfo refundToRefundInfo(Refund refundInfo) {
        if ( refundInfo == null ) {
            return null;
        }

        RefundInfo.Builder refundInfo1 = RefundInfo.newBuilder();

        if ( refundInfo.getRefundId() != null ) {
            refundInfo1.setRefundId( refundInfo.getRefundId() );
        }
        if ( refundInfo.getRefundNo() != null ) {
            refundInfo1.setRefundNo( refundInfo.getRefundNo() );
        }
        if ( refundInfo.getRefTicketId() != null ) {
            refundInfo1.setRefTicketId( refundInfo.getRefTicketId() );
        }
        if ( refundInfo.getRefTicketNo() != null ) {
            refundInfo1.setRefTicketNo( refundInfo.getRefTicketNo() );
        }
        if ( refundInfo.getRefundReason() != null ) {
            refundInfo1.setRefundReason( refundInfo.getRefundReason() );
        }
        if ( refundInfo.getRefundCode() != null ) {
            refundInfo1.setRefundCode( refundInfo.getRefundCode() );
        }
        if ( refundInfo.getRefundSide() != null ) {
            refundInfo1.setRefundSide( refundInfo.getRefundSide() );
        }

        return refundInfo1.build();
    }

    @Override
    public Channel channelToChannel(cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Channel channel) {
        if ( channel == null ) {
            return null;
        }

        Channel.Builder channel1 = Channel.newBuilder();

        if ( channel.getSource() != null ) {
            channel1.setSource( channel.getSource() );
        }
        if ( channel.getDeviceType() != null ) {
            channel1.setDeviceType( channel.getDeviceType() );
        }
        if ( channel.getOrderType() != null ) {
            channel1.setOrderType( channel.getOrderType() );
        }
        if ( channel.getDeliveryType() != null ) {
            channel1.setDeliveryType( channel.getDeliveryType() );
        }
        if ( channel.getTpName() != null ) {
            channel1.setTpName( channel.getTpName() );
        }
        if ( channel.getCode() != null ) {
            channel1.setCode( channel.getCode() );
        }
        if ( channel.getId() != null ) {
            channel1.setId( channel.getId() );
        }
        if ( channel.getMappingCode() != null ) {
            channel1.setMappingCode( channel.getMappingCode() );
        }

        return channel1.build();
    }

    @Override
    public Table tableToTable(cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Table table) {
        if ( table == null ) {
            return null;
        }

        Table.Builder table1 = Table.newBuilder();

        if ( table.getId() != null ) {
            table1.setId( table.getId() );
        }
        if ( table.getZoneId() != null ) {
            table1.setZoneId( table.getZoneId() );
        }
        if ( table.getNo() != null ) {
            table1.setNo( table.getNo() );
        }
        if ( table.getZoneNo() != null ) {
            table1.setZoneNo( table.getZoneNo() );
        }
        table1.setPeople( table.getPeople() );
        table1.setTemporary( table.isTemporary() );

        return table1.build();
    }

    @Override
    public Store storeToStore(cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Store store) {
        if ( store == null ) {
            return null;
        }

        Store.Builder store1 = Store.newBuilder();

        if ( store.getId() != null ) {
            store1.setId( store.getId() );
        }
        if ( store.getCode() != null ) {
            store1.setCode( store.getCode() );
        }
        if ( store.getSecondCode() != null ) {
            store1.setSecondCode( store.getSecondCode() );
        }
        if ( store.getCompanyId() != null ) {
            store1.setCompanyId( store.getCompanyId() );
        }
        if ( store.getPartnerId() != null ) {
            store1.setPartnerId( store.getPartnerId() );
        }
        if ( store.getScopeId() != null ) {
            store1.setScopeId( store.getScopeId() );
        }
        if ( store.getBranchId() != null ) {
            store1.setBranchId( store.getBranchId() );
        }

        return store1.build();
    }

    @Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Takeaway takeawayToTakeaway(Takeaway takeaway) {
        if ( takeaway == null ) {
            return null;
        }

        cn.hexcloud.pbis.common.service.integration.eticket.Takeaway.Builder takeaway1 = cn.hexcloud.pbis.common.service.integration.eticket.Takeaway.newBuilder();

        if ( takeaway.getOrderMethod() != null ) {
            takeaway1.setOrderMethod( takeaway.getOrderMethod() );
        }
        if ( takeaway.getTpOrderId() != null ) {
            takeaway1.setTpOrderId( takeaway.getTpOrderId() );
        }
        if ( takeaway.getOrderTime() != null ) {
            takeaway1.setOrderTime( takeaway.getOrderTime() );
        }
        if ( takeaway.getDeliverTime() != null ) {
            takeaway1.setDeliverTime( takeaway.getDeliverTime() );
        }
        if ( takeaway.getDescription() != null ) {
            takeaway1.setDescription( takeaway.getDescription() );
        }
        if ( takeaway.getConsignee() != null ) {
            takeaway1.setConsignee( takeaway.getConsignee() );
        }
        if ( takeaway.getDeliveryPoiAddress() != null ) {
            takeaway1.setDeliveryPoiAddress( takeaway.getDeliveryPoiAddress() );
        }
        if ( takeaway.getTp() != null ) {
            takeaway1.setTp( takeaway.getTp() );
        }
        if ( takeaway.getSource() != null ) {
            takeaway1.setSource( takeaway.getSource() );
        }
        if ( takeaway.getSourceOrderId() != null ) {
            takeaway1.setSourceOrderId( takeaway.getSourceOrderId() );
        }
        if ( takeaway.getDaySeq() != null ) {
            takeaway1.setDaySeq( takeaway.getDaySeq() );
        }
        takeaway1.setDeliveryType( takeaway.getDeliveryType() );
        if ( takeaway.getDeliveryName() != null ) {
            takeaway1.setDeliveryName( takeaway.getDeliveryName() );
        }
        if ( takeaway.getInvoiceTitle() != null ) {
            takeaway1.setInvoiceTitle( takeaway.getInvoiceTitle() );
        }
        if ( takeaway.getWaitingTime() != null ) {
            takeaway1.setWaitingTime( takeaway.getWaitingTime() );
        }
        takeaway1.setTablewareNum( takeaway.getTablewareNum() );
        if ( takeaway.getSendFee() != null ) {
            takeaway1.setSendFee( takeaway.getSendFee().doubleValue() );
        }
        if ( takeaway.getPackageFee() != null ) {
            takeaway1.setPackageFee( takeaway.getPackageFee().doubleValue() );
        }
        if ( takeaway.getDeliveryTime() != null ) {
            takeaway1.setDeliveryTime( takeaway.getDeliveryTime() );
        }
        if ( takeaway.getTakeMealSn() != null ) {
            takeaway1.setTakeMealSn( takeaway.getTakeMealSn() );
        }
        takeaway1.setPartnerPlatformId( takeaway.getPartnerPlatformId() );
        if ( takeaway.getPartnerPlatformName() != null ) {
            takeaway1.setPartnerPlatformName( takeaway.getPartnerPlatformName() );
        }
        if ( takeaway.getWxName() != null ) {
            takeaway1.setWxName( takeaway.getWxName() );
        }
        if ( takeaway.getTakeoutType() != null ) {
            takeaway1.setTakeoutType( takeaway.getTakeoutType() );
        }
        if ( takeaway.getOriginalOrderNo() != null ) {
            takeaway1.setOriginalOrderNo( takeaway.getOriginalOrderNo() );
        }
        if ( takeaway.getMerchantSendFee() != null ) {
            takeaway1.setMerchantSendFee( takeaway.getMerchantSendFee().floatValue() );
        }
        takeaway1.setSelfDelivery( takeaway.isSelfDelivery() );
        if ( takeaway.getDeliveryPhone() != null ) {
            takeaway1.setDeliveryPhone( takeaway.getDeliveryPhone() );
        }
        if ( takeaway.getDeliveryPlatform() != null ) {
            takeaway1.setDeliveryPlatform( takeaway.getDeliveryPlatform() );
        }
        if ( takeaway.getInvoiceType() != null ) {
            takeaway1.setInvoiceType( takeaway.getInvoiceType() );
        }
        if ( takeaway.getInvoiceTaxPayerId() != null ) {
            takeaway1.setInvoiceTaxPayerId( takeaway.getInvoiceTaxPayerId() );
        }
        if ( takeaway.getInvoiceEmail() != null ) {
            takeaway1.setInvoiceEmail( takeaway.getInvoiceEmail() );
        }
        if ( takeaway.getPlatformSendFee() != null ) {
            takeaway1.setPlatformSendFee( takeaway.getPlatformSendFee().floatValue() );
        }
        if ( takeaway.getSendFeeForPlatform() != null ) {
            takeaway1.setSendFeeForPlatform( takeaway.getSendFeeForPlatform().floatValue() );
        }
        if ( takeaway.getSendFeeForMerchant() != null ) {
            takeaway1.setSendFeeForMerchant( takeaway.getSendFeeForMerchant().floatValue() );
        }
        if ( takeaway.getInvoiceProvider() != null ) {
            takeaway1.setInvoiceProvider( takeaway.getInvoiceProvider() );
        }
        if ( takeaway.getInvoiceAmount() != null ) {
            takeaway1.setInvoiceAmount( takeaway.getInvoiceAmount() );
        }
        if ( takeaway.getInvoiceUrl() != null ) {
            takeaway1.setInvoiceUrl( takeaway.getInvoiceUrl() );
        }

        return takeaway1.build();
    }

    @Override
    public Efficiency efficiencyToEfficiency(cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Efficiency efficiency) {
        if ( efficiency == null ) {
            return null;
        }

        Efficiency.Builder efficiency1 = Efficiency.newBuilder();

        if ( efficiency.getConfirmedTime() != null ) {
            efficiency1.setConfirmedTime( efficiency.getConfirmedTime() );
        }
        if ( efficiency.getMadeTime() != null ) {
            efficiency1.setMadeTime( efficiency.getMadeTime() );
        }
        if ( efficiency.getAssignedTime() != null ) {
            efficiency1.setAssignedTime( efficiency.getAssignedTime() );
        }
        if ( efficiency.getArrivedTime() != null ) {
            efficiency1.setArrivedTime( efficiency.getArrivedTime() );
        }
        if ( efficiency.getFetchedTime() != null ) {
            efficiency1.setFetchedTime( efficiency.getFetchedTime() );
        }
        if ( efficiency.getDeliveredTime() != null ) {
            efficiency1.setDeliveredTime( efficiency.getDeliveredTime() );
        }
        if ( efficiency.getMakeSpan() != null ) {
            efficiency1.setMakeSpan( Float.parseFloat( efficiency.getMakeSpan() ) );
        }
        if ( efficiency.getAvgMakeSpan() != null ) {
            efficiency1.setAvgMakeSpan( Float.parseFloat( efficiency.getAvgMakeSpan() ) );
        }
        if ( efficiency.getArriveSpan() != null ) {
            efficiency1.setArriveSpan( Float.parseFloat( efficiency.getArriveSpan() ) );
        }
        if ( efficiency.getDeliverSpan() != null ) {
            efficiency1.setDeliverSpan( Float.parseFloat( efficiency.getDeliverSpan() ) );
        }

        return efficiency1.build();
    }

    @Override
    public SkuRemark skuRemarkToSkuRemark(cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.SkuRemark skuRemark) {
        if ( skuRemark == null ) {
            return null;
        }

        SkuRemark.Builder skuRemark1 = SkuRemark.newBuilder();

        if ( skuRemark.getName() != null ) {
            skuRemark1.setName( skuNameToskuName( skuRemark.getName() ) );
        }
        if ( skuRemark.getValues() != null ) {
            skuRemark1.setValues( skuValueToskuValue( skuRemark.getValues() ) );
        }

        return skuRemark1.build();
    }



    public Ticket ticketToTicket(cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Ticket ticket) {
        if (ticket == null) {
            return null;
        }

        cn.hexcloud.pbis.common.service.integration.eticket.Ticket.Builder ticket1 = cn.hexcloud.pbis.common.service.integration.eticket.Ticket
            .newBuilder();

        if (ticket.getFeesNoAccount() != null) {
            for (cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Fee fee : ticket.getFeesNoAccount()) {
                ticket1.addFeesNoAccount(feeToFee(fee));
            }
        }

        if (ticket.getProducts() != null) {
            for (TicketProduct ticketProduct : ticket.getProducts()) {
                ticket1.addProducts(productToProductList(ticketProduct));
            }
        }
        if (ticket.getPayments() != null) {
            for (cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Payment ticketPayment : ticket.getPayments()) {
                ticket1.addPayments(paymentToPayment(ticketPayment));
            }
        }
        if (ticket.getPromotions() != null) {
            for (Promotion ticketPromotion : ticket.getPromotions()) {
                ticket1.addPromotions(promotionToPromotion(ticketPromotion));
            }
        }
        if (ticket.getMembers() != null) {
            for (cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Member ticketMember : ticket.getMembers()) {
                ticket1.addMembers(memberToMember(ticketMember));
            }
        }
        if (ticket.getTaxList() != null) {
            for (cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Tax ticketTaxList : ticket.getTaxList()) {
                ticket1.addTaxList(taxToTax(ticketTaxList));
            }
        }
        if (ticket.getCoupons() != null) {
            for (TicketCoupon ticketCoupon : ticket.getCoupons()) {
                ticket1.addCoupons(ticketCouponToCoupon(ticketCoupon));
            }
        }
        if (ticket.getFeesNoAccount() != null) {
            for (cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.Fee fee : ticket.getFees()) {
                ticket1.addFees(feeToFee(fee));
            }
        }
        if (ticket.getTicketId() != null) {
            ticket1.setTicketId(ticket.getTicketId());
        }
        if (ticket.getTicketNo() != null) {
            ticket1.setTicketNo(ticket.getTicketNo());
        }
        if (ticket.getStartTime() != null) {
            ticket1.setStartTime(ticket.getStartTime());
        }
        if (ticket.getEndTime() != null) {
            ticket1.setEndTime(ticket.getEndTime());
        }
        if (ticket.getBusDate() != null) {
            ticket1.setBusDate(ticket.getBusDate());
        }
        if (ticket.getPos() != null) {
            ticket1.setPos(posToPos(ticket.getPos()));
        }
        if (ticket.getOperator() != null) {
            ticket1.setOperator(operatorToOperator(ticket.getOperator()));
        }
        if (ticket.getAmounts() != null) {
            ticket1.setAmounts(amountToAmount(ticket.getAmounts()));
        }
        if (ticket.getTakemealNumber() != null) {
            ticket1.setTakemealNumber(ticket.getTakemealNumber());
        }
        ticket1.setQty(ticket.getQty());
        if (ticket.getStatus() != null) {
            ticket1.setStatus(ticket.getStatus());
        }
        if (ticket.getRefundInfo() != null) {
            ticket1.setRefundInfo(refundToRefundInfo(ticket.getRefundInfo()));
        }
        if (ticket.getChannel() != null) {
            ticket1.setChannel(channelToChannel(ticket.getChannel()));
        }
        if (ticket.getTable() != null) {
            ticket1.setTable(tableToTable(ticket.getTable()));
        }
        ticket1.setPeople(ticket.getPeople());
        if (ticket.getRoomNo() != null) {
            ticket1.setRoomNo(ticket.getRoomNo());
        }
        if (ticket.getRemark() != null) {
            ticket1.setRemark(ticket.getRemark());
        }
        ticket1.setHouseAc(ticket.isHouseAc());
        if (ticket.getOrderTimeType() != null) {
            ticket1.setOrderTimeType(ticket.getOrderTimeType());
        }
        if (ticket.getShiftNumber() != null) {
            ticket1.setShiftNumber(ticket.getShiftNumber());
        }
        if (ticket.getStore() != null) {
            ticket1.setStore(storeToStore(ticket.getStore()));
        }
        if (ticket.getTakeawayInfo() != null) {
            ticket1.setTakeawayInfo(takeawayToTakeaway(ticket.getTakeawayInfo()));
        }
        if (ticket.getTicketUno() != null) {
            ticket1.setTicketUno(ticket.getTicketUno());
        }
        if (ticket.getTimeZone() != null) {
            ticket1.setTimeZone(ticket.getTimeZone());
        }
        ticket1.setDiscountProportioned(ticket.isDiscountProportioned());
        if (ticket.getEfficiency() != null) {
            ticket1.setEfficiency(efficiencyToEfficiency(ticket.getEfficiency()));
        }
        ticket1.setPendingSyncMember(ticket.isPendingSyncMember());
        if (ticket.getWeight() != null) {
            ticket1.setWeight(ticket.getWeight().floatValue());
        }

        return ticket1.build();
    }

    public skuValue skuValueToskuValue(SkuValue skuValue) {
        if (skuValue == null) {
            return null;
        }

        cn.hexcloud.pbis.common.service.integration.eticket.SkuRemark.skuValue.Builder skuValue1
            = cn.hexcloud.pbis.common.service.integration.eticket.SkuRemark.skuValue.newBuilder();

        if (skuValue.getCode() != null) {
            skuValue1.setCode(skuValue.getCode());
        }
        if (skuValue.getName() != null) {
            skuValue1.setName(skuValue.getName());
        }

        return skuValue1.build();
    }

    public skuName skuNameToskuName(SkuName skuName) {
        if (skuName == null) {
            return null;
        }

        cn.hexcloud.pbis.common.service.integration.eticket.SkuRemark.skuName.Builder skuName1
            = cn.hexcloud.pbis.common.service.integration.eticket.SkuRemark.skuName.newBuilder();

        if (skuName.getId() != null) {
            skuName1.setId(skuName.getId());
        }
        if (skuName.getCode() != null) {
            skuName1.setCode(skuName.getCode());
        }
        if (skuName.getName() != null) {
            skuName1.setName(skuName.getName());
        }

        return skuName1.build();
    }

    @Override
    public Product productToProductList(TicketProduct product) {
        if (product == null) {
            return null;
        }

        cn.hexcloud.pbis.common.service.integration.eticket.Product.Builder product1 = Product.newBuilder();

        if (product.getSkuRemark() != null) {
            for (cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.SkuRemark skuRemark : product.getSkuRemark()) {
                product1.addSkuRemark(skuRemarkToSkuRemark(skuRemark));
            }
        }
        if (product.getAccessories() != null) {
            for (TicketProduct ticketProduct : product.getAccessories()) {
                product1.addAccessories(productToProductList(ticketProduct));
            }
        }
        if (product.getComboItems() != null) {
            for (TicketProduct ticketProduct : product.getComboItems()) {
                product1.addComboItems(productToProductList(ticketProduct));
            }
        }
        if (product.getId() != null) {
            product1.setId(product.getId());
        }
        if (product.getName() != null) {
            product1.setName(product.getName());
        }
        if (product.getCode() != null) {
            product1.setCode(product.getCode());
        }
        product1.setSeqId(product.getSeqId());
        if (product.getPrice() != null) {
            product1.setPrice(product.getPrice().doubleValue());
        }
        if (product.getAmount() != null) {
            product1.setAmount(product.getAmount().doubleValue());
        }
        product1.setQty(product.getQty());
        if (product.getDiscountAmount() != null) {
            product1.setDiscountAmount(product.getDiscountAmount().doubleValue());
        }
        if (product.getType() != null) {
            product1.setType(product.getType());
        }
        if (product.getOperationRecords() != null) {
            product1.setOperationRecords(product.getOperationRecords());
        }
        if (product.getRemark() != null) {
            product1.setRemark(product.getRemark());
        }
        if (product.getTaxAmount() != null) {
            product1.setTaxAmount(product.getTaxAmount().doubleValue());
        }
        if (product.getSumAmount() != null) {
            product1.setSumAmount(product.getSumAmount().floatValue());
        }
        if (product.getSumDiscountAmount() != null) {
            product1.setSumDiscountAmount(product.getSumDiscountAmount().floatValue());
        }
        if (product.getSumNetAmount() != null) {
            product1.setSumNetAmount(product.getSumNetAmount().floatValue());
        }
        product1.setHasMakeSpan(product.isHasMakeSpan());
        if (product.getAvgMakeSpan() != null) {
            product1.setAvgMakeSpan(product.getAvgMakeSpan().floatValue());
        }
        if (product.getNetAmount() != null) {
            product1.setNetAmount(product.getNetAmount().doubleValue());
        }
        if (product.getWeight() != null) {
            product1.setWeight(product.getWeight().floatValue());
        }
        product1.setHasWeight(product.isHasWeight());

        return product1.build();
    }

    protected cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct promotionProductToPromotionProduct(PromotionProduct promotionProduct) {
        if ( promotionProduct == null ) {
            return null;
        }

        cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder promotionProduct1 = cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.newBuilder();

        if ( promotionProduct.getPrice() != null ) {
            promotionProduct1.setPrice( promotionProduct.getPrice().doubleValue() );
        }
        if ( promotionProduct.getAmt() != null ) {
            promotionProduct1.setAmt( promotionProduct.getAmt().doubleValue() );
        }
        if ( promotionProduct.getAccAmt() != null ) {
            promotionProduct1.setAccAmt( promotionProduct.getAccAmt().doubleValue() );
        }
        promotionProduct1.setQty( promotionProduct.getQty() );
        if ( promotionProduct.getKeyId() != null ) {
            promotionProduct1.setKeyId( promotionProduct.getKeyId() );
        }
        if ( promotionProduct.getType() != null ) {
            promotionProduct1.setType( promotionProduct.getType() );
        }
        if ( promotionProduct.getDiscount() != null ) {
            promotionProduct1.setDiscount( promotionProduct.getDiscount().doubleValue() );
        }
        if ( promotionProduct.getFreeAmt() != null ) {
            promotionProduct1.setFreeAmt( promotionProduct.getFreeAmt().doubleValue() );
        }
        if ( promotionProduct.getMethod() != null ) {
            promotionProduct1.setMethod( promotionProduct.getMethod() );
        }

        return promotionProduct1.build();
    }

    protected cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo promotionInfoToPromotionInfo(PromotionInfo promotionInfo) {
        if ( promotionInfo == null ) {
            return null;
        }

        cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.Builder promotionInfo1 = cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.newBuilder();

        if ( promotionInfo.getType() != null ) {
            promotionInfo1.setType( promotionInfo.getType() );
        }
        if ( promotionInfo.getDiscountType() != null ) {
            promotionInfo1.setDiscountType( promotionInfo.getDiscountType() );
        }
        if ( promotionInfo.getName() != null ) {
            promotionInfo1.setName( promotionInfo.getName() );
        }
        if ( promotionInfo.getPromotionId() != null ) {
            promotionInfo1.setPromotionId( promotionInfo.getPromotionId() );
        }
        if ( promotionInfo.getPromotionCode() != null ) {
            promotionInfo1.setPromotionCode( promotionInfo.getPromotionCode() );
        }
        if ( promotionInfo.getPromotionType() != null ) {
            promotionInfo1.setPromotionType( promotionInfo.getPromotionType() );
        }
        promotionInfo1.setAllowOverlap( promotionInfo.isAllowOverlap() );
        promotionInfo1.setTriggerTimesCustom( promotionInfo.isTriggerTimesCustom() );
        if ( promotionInfo.getTicketDisplay() != null ) {
            promotionInfo1.setTicketDisplay( promotionInfo.getTicketDisplay() );
        }
        if ( promotionInfo.getMaxDiscount() != null ) {
            promotionInfo1.setMaxDiscount( promotionInfo.getMaxDiscount().doubleValue() );
        }

        return promotionInfo1.build();
    }

    protected PromotionSource promotionSourceToPromotionSource(cn.hexcloud.pbis.common.service.integration.channel.member.dto.entity.PromotionSource promotionSource) {
        if ( promotionSource == null ) {
            return null;
        }

        PromotionSource.Builder promotionSource1 = PromotionSource.newBuilder();

        promotionSource1.setTrigger( promotionSource.getTrigger() );
        if ( promotionSource.getDiscount() != null ) {
            promotionSource1.setDiscount( promotionSource.getDiscount().doubleValue() );
        }
        if ( promotionSource.getMerchantDiscount() != null ) {
            promotionSource1.setMerchantDiscount( promotionSource.getMerchantDiscount().doubleValue() );
        }
        if ( promotionSource.getPlatformDiscount() != null ) {
            promotionSource1.setPlatformDiscount( promotionSource.getPlatformDiscount().doubleValue() );
        }
        if ( promotionSource.getStoreDiscount() != null ) {
            promotionSource1.setStoreDiscount( promotionSource.getStoreDiscount().doubleValue() );
        }
        if ( promotionSource.getCost() != null ) {
            promotionSource1.setCost( promotionSource.getCost().doubleValue() );
        }
        if ( promotionSource.getTpAllowance() != null ) {
            promotionSource1.setTpAllowance( promotionSource.getTpAllowance().doubleValue() );
        }
        if ( promotionSource.getMerchantAllowance() != null ) {
            promotionSource1.setMerchantAllowance( promotionSource.getMerchantAllowance().doubleValue() );
        }
        if ( promotionSource.getPlatformAllowance() != null ) {
            promotionSource1.setPlatformAllowance( promotionSource.getPlatformAllowance().doubleValue() );
        }
        if ( promotionSource.getRealAmount() != null ) {
            promotionSource1.setRealAmount( promotionSource.getRealAmount().doubleValue() );
        }
        if ( promotionSource.getTransferAmount() != null ) {
            promotionSource1.setTransferAmount( promotionSource.getTransferAmount().doubleValue() );
        }

        return promotionSource1.build();
    }
}
