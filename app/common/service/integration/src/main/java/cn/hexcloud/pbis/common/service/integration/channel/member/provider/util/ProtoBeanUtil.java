package cn.hexcloud.pbis.common.service.integration.channel.member.provider.util;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;
import java.io.IOException;

/**
 * @Classname ProtoBeanUtil
 * @Description:
 * @Date 2022/1/125:34 下午
 * <AUTHOR>
 */
public class ProtoBeanUtil {
  /**
   * 将ProtoBean转化成JSON String
   *
   * @return
   * @throws IOException
   */
  public static String toJSONString(Message sourceMessage) {
    if (sourceMessage == null) {
      throw new IllegalArgumentException
          ("No source message specified");
    }
    String json = null;
    try {
      json = JsonFormat.printer().includingDefaultValueFields().omittingInsignificantWhitespace().print(sourceMessage);
    } catch (InvalidProtocolBufferException e) {
      throw new RuntimeException(e);
    }
    return json;
  }

  /**
   * 将JSON String转化为ProtoBean对象
   *
   * @param destBuilder 目标Message对象的Builder类
   * @param json        含有数据的JSON String
   * @return
   * @throws IOException
   */
  public static void toProtoBean(Message.Builder destBuilder, String json) throws IOException {
    if (destBuilder == null) {
      throw new IllegalArgumentException
          ("No destination message builder specified");
    }
    if (json == null) {
      throw new IllegalArgumentException("No source pojo specified");
    }
    JsonFormat.parser().ignoringUnknownFields().merge(json, destBuilder);
  }

  public static String toJSONString(Object body) {
    return body instanceof Message ? toJSONString((Message) body) : JSON.toJSONString(body);
  }


  /**
   * 将ProtoBean对象转化为POJO对象
   *
   * @param destPojoClass 目标POJO对象的类类型
   * @param sourceMessage 含有数据的ProtoBean对象实例
   * @param <PojoType> 目标POJO对象的类类型范型
   * @return
   * @throws IOException
   */
  public static <PojoType> PojoType toPojoBean(Class<PojoType> destPojoClass, Message sourceMessage)
      throws IOException {
    if (destPojoClass == null) {
      throw new IllegalArgumentException
          ("No destination pojo class specified");
    }
    if (sourceMessage == null) {
      throw new IllegalArgumentException("No source message specified");
    }
    String json = JsonFormat.printer().print(sourceMessage);
    return new Gson().fromJson(json, destPojoClass);
  }

  /**
   * 将POJO对象转化为ProtoBean对象
   *
   * @param destBuilder 目标Message对象的Builder类
   * @param sourcePojoBean 含有数据的POJO对象
   * @return
   * @throws IOException
   */
  public static void toProtoBean(Message.Builder destBuilder, Object sourcePojoBean) throws IOException {
    if (destBuilder == null) {
      throw new IllegalArgumentException
          ("No destination message builder specified");
    }
    if (sourcePojoBean == null) {
      throw new IllegalArgumentException("No source pojo specified");
    }
    String json = new Gson().toJson(sourcePojoBean);
    JsonFormat.parser().merge(json, destBuilder);
  }
}
