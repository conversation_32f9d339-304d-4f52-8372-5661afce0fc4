package cn.hexcloud.pbis.common.service.integration.channel.member.provider.util;


import com.alibaba.fastjson.JSONObject;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

/**
 * @Classname RequestSignUtil
 * @Description:
 * @Date 2021/7/225:57 下午
 * <AUTHOR>
 */
public class RequestSignUtil {


    public static String signTopRequest(JSONObject json, String key1) throws Exception {

        Map<String, String> params = new HashMap<>();
        jsonToMap(json, params);
        String[] keys = (String[]) params.keySet().toArray(new String[0]);
        Arrays.sort(keys);
        StringBuilder query = new StringBuilder();
        String[] var6 = keys;
        int var7 = keys.length;
        for (int var8 = 0; var8 < var7; ++var8) {
            String key = var6[var8];
            String value = (String) params.get(key);
            if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)
                    && !key.equals("sign")) {
                query.append(key).append("=").append(value).append("&");
            }
        }
        String s = query.toString();
        String substring = s.substring(0, (s.length() - 1));
        return SignatureAlgorithmExec.sign(substring, key1, SignatureAlgorithmEnum.SHA256withRSA);
    }


    public static void jsonToMap(JSONObject json, Map params) {

        //最外层JSON解析
        for (Object k : json.keySet()) {
            Object v = json.get(k);
            String key = k.toString();
            params.put(key, v.toString());
        }

    }

}
