package cn.hexcloud.pbis.common.service.integration.channel.member.provider.util;
import java.security.GeneralSecurityException;
import java.security.SecureRandom;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * @Description: 喜茶密钥加密
 * @License: <p>https://www.hexcloud.cn</p> <p>上海合阔信息技术有限公司</p>
 * @Author: Luck
 * @Date: 2022/2/22
 */
public class XichaAESUtil {

  public static String getClientSecretStr(String secretText, String key) throws Exception {
    // 原文:String message = "da7e9fa3-1bf5-4be8-a3ba-5bc041640c11";
    System.out.println("secretText: " + secretText);
    // 256位密钥 = 32 bytes Key: byte[] keys = "1234567890abcdef1234567890abcdef".getBytes("UTF-8");
    byte[] keys = key.getBytes("UTF-8");
    // 加密:
    byte[] data = secretText.getBytes("UTF-8");
    byte[] encrypted = encrypt(keys, data);
    String encryptedStr = Base64.getEncoder().encodeToString(encrypted);
    return encryptedStr;
  }

  // 加密:
  public static byte[] encrypt(byte[] key, byte[] input) throws GeneralSecurityException {
    Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
    SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
    // CBC模式需要生成一个16 bytes的initialization vector:
    SecureRandom sr = SecureRandom.getInstanceStrong();
    byte[] iv = sr.generateSeed(16);
    IvParameterSpec ivps = new IvParameterSpec(iv);
    cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivps);
    byte[] data = cipher.doFinal(input);
    // IV不需要保密，把IV和密文一起返回:
    return join(iv, data);
  }

  // 解密:
  public static byte[] decrypt(byte[] key, byte[] input) throws GeneralSecurityException {
    // 把input分割成IV和密文:
    byte[] iv = new byte[16];
    byte[] data = new byte[input.length - 16];
    System.arraycopy(input, 0, iv, 0, 16);
    System.arraycopy(input, 16, data, 0, data.length);
    // 解密:
    Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
    SecretKeySpec keySpec = new SecretKeySpec(key, "AES");
    IvParameterSpec ivps = new IvParameterSpec(iv);
    cipher.init(Cipher.DECRYPT_MODE, keySpec, ivps);
    return cipher.doFinal(data);
  }

  public static byte[] join(byte[] bs1, byte[] bs2) {
    byte[] r = new byte[bs1.length + bs2.length];
    System.arraycopy(bs1, 0, r, 0, bs1.length);
    System.arraycopy(bs2, 0, r, bs1.length, bs2.length);
    return r;
  }

}
