package cn.hexcloud.pbis.common.service.integration.channel.payment.dto;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * @ClassName Commodity.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/21 13:04:51
 */
@Data
@ToString
public class Commodity {

  private String id;
  private String code;
  private String name;
  private Integer quantity;
  private BigDecimal price;
  private String imageUrl;
  private List<CommodityProperty> properties;

}
