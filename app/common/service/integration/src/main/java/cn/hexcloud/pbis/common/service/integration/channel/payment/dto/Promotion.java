package cn.hexcloud.pbis.common.service.integration.channel.payment.dto;

import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

/**
 * @ClassName Promotion.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/21 13:09:57
 */
@Data
@ToString
public class Promotion {

  private String id;
  private String code;
  private String name;
  private BigDecimal discount;
  private BigDecimal discountOnMerchant;
  private BigDecimal discountOnPlatform;
  private BigDecimal discountOnOthers;
  private BigDecimal userPayAmount;
  private BigDecimal merchantPayAmount;
  private BigDecimal platformPayAmount;
  private BigDecimal othersPayAmount;

}
