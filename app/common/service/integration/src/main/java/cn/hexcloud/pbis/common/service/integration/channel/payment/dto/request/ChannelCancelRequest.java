package cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Commodity;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName ChannelCancelRequest.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/13 19:03:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelCancelRequest extends ChannelRequest {

  private String channel;
  private String transactionId;
  private Date transactionTime;
  private String relatedTransactionId;
  private String relatedTPTransactionId;
  private String payCode;
  private PayMethod payMethod;
  private BigDecimal amount;
  private String secretContent;
  private String extendedParams;
  private String orderNo;
  private String posId;
  private String posCode;
  private String tableNo;
  private Date orderTime;
  private BigDecimal orderAmount;
  private List<Commodity> commodities;

}
