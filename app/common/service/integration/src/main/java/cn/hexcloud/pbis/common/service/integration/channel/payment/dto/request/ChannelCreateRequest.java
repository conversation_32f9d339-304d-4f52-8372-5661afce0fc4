package cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Commodity;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName ChannelCreateRequest.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/21 12:52:56
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelCreateRequest extends ChannelRequest {

  private String channel;
  private String transactionId;
  private Date transactionTime;
  private String payer;
  private BigDecimal amount;
  private String description;
  private String extendedParams;
  private String orderNo;
  private String posId;
  private String posCode;
  private String tableNo;
  private Date orderTime;
  private BigDecimal orderAmount;
  private BigDecimal discountAmount;
  private String orderDescription;
  private List<Commodity> commodities;
  private String memberCardNo;
  private String memberId;
  private String memberNo;
  private String mobile;
  private String remoteIp;
  private String currency;

}
