package cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Commodity;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName PayRequest.java
 * <AUTHOR>
 * @Version 1.0
 * @Description 支付渠道支付请求
 * @CreateTime 2021/10/13 18:57:55
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelPayRequest extends ChannelRequest {

  private String channel;
  private String transactionId;
  private Date transactionTime;
  private String payCode;
  private BigDecimal amount;
  private String secretContent;
  private String extendedParams;
  private String orderNo;
  private String posId;
  private String posCode;
  private String tableNo;
  private Date orderTime;
  private BigDecimal orderAmount;
  private BigDecimal orderDiscountAmount;
  private String orderDescription;
  private List<Commodity> commodities;
  private String memberCardNo;
  private String memberId;
  private String memberNo;
  private String mobile;
  private String remoteIp;
  private String currency;

}
