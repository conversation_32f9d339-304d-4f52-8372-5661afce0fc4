package cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelRequest;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName ChannelQueryRequest.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/13 18:59:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelQueryRequest extends ChannelRequest {

  private String channel;
  private String transactionId;
  private String orderNo;
  private Date transactionTime;
  private String tpTransactionId;
  private String payCode;
  private String extendedParams;
}
