package cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName ChannelCancelResponse.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/13 19:03:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelCancelResponse extends ChannelResponse {

  private TransactionState transactionState;
  private BigDecimal realAmount;
  private String tpTransactionId;
  private String extendedParams;

}
