package cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName ChannelCreateResponse.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/21 12:53:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelCreateResponse extends ChannelResponse {

  private String prePayId;
  private String prePaySign;
  private String payCode;
  private String channel;
  private PayMethod payMethod;
  private String extendedParams;
  private String tpTransactionId;
  private String packStr;
  private String frontUrl;
  private String timeExpire;

}
