package cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName ChannelNotifyResponse.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/21 13:49:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelNotificationResponse extends ChannelResponse {

  private String response;
  private ChannelPayResponse payResponse;

}
