package cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Promotion;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.NotificationType;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName PayResponse.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/13 18:58:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ChannelPayResponse extends ChannelResponse {

  private TransactionState transactionState;
  private NotificationType notificationType;
  private String channel;
  private PayMethod payMethod;
  private String payer;
  private BigDecimal realAmount;
  private String transactionId;
  private String tpTransactionId;
  private Double transactionPoints;
  private Double accountPoints;
  private String extendedParams;
  private List<Promotion> promotions;

}
