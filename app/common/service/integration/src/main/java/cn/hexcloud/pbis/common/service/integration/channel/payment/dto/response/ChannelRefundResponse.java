package cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response;

import cn.hexcloud.pbis.common.service.integration.channel.dto.ChannelResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @ClassName ChannelRefundResponse.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/13 19:00:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class ChannelRefundResponse extends ChannelResponse {

  private TransactionState transactionState;
  private BigDecimal realAmount;
  private String transactionId;
  private String tpTransactionId;
  private String extendedParams;

}
