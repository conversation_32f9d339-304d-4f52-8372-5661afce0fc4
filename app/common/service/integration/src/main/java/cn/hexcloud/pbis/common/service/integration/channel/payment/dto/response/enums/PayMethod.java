package cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums;

/**
 * @ClassName PayMethod.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/12/27 14:21:26
 */
public enum PayMethod {

  ALI_PAY("AliPay", "支付宝"),
  WX_PAY("WXPay", "微信支付"),
  FUIOUM_PAY("FuiouMPay", "富友微信小程序支付"),
  EFTM_PAY("EFTMPay", "EFT小程序支付"),
  UNION_PAY("UnionPay", "银联二维码"),
  JD_PAY("JDPay", "京东钱包"),
  QQ_PAY("QQPay", "QQ钱包"),
  KOUBEI("Koubei", "口碑支付"),
  BEST_PAY("BestPay", "翼支付"),
  BAIDU_PAY("BaiduPay", "百度支付"),
  HUIFU("<PERSON><PERSON>", "汇付天下"),
  CM_PAY("CMPay", "和包支付"),
  CMB("CMB", "招行"),
  ICBC("ICBC", "工行"),
  CCB("CCB", "建行龙支付二维码"),
  CDBC("CDBC", "数字人民币"),
  BFE_PAY("BFEPay", "BFE余额支付"),
  QBAL_PAY("QBALPay", "企迈余额支付"),
  YUNXI_PAY("YunxiPay", "云徙余额支付"),
  CASH("Cash", "现金"),
  Point("Point","积分支付"),
  QuickPass("QuickPass","云闪付"),
  BOH_CREDIT_PAY("BohCreditPay", "boh信用付"),
  BOH_VOUCHER_PAY("BohVoucherPay", "boh代金券支付"),
  JSB_EPAY("JSB_EPay", "e融支付"),
  SELTEKBAL_PAY("SeltekBalPay","储值卡"),
  YMBAL_PAY("YmBalPay","翼码余额支付"),
  TJI_COUPON_PAY("TjiCouponPay","谭仔卡券支付"),
  OTHERS("Others", "其他"),
  EMPTY("", ""),

  // 和消费者支付方式配置保持一致
  EOctopus("EOctopus", "E-Octopus"),
  EPayme("EPayme", "E-Payme"),

  // Fiserv用到的支付方式enum(deprecated)
  HAlipay("HAlipay", "H-Alipay"),
  HWechatpay("HWechatpay", "H-Wechatpay"),
  HBOCPAY("HBOCPAY", "H-BOCPAY"),
  HVisa("HVisa", "H-Visa"),
  HMaster("HMaster", "H-Master"),
  HApplePay("HApplePay", "H-ApplePay"),
  HGooglePay("HGooglePay", "H-GooglePay"),
  HStripePay("HStripePay", "H-StripePay"),

  // MPGS用到的支付方式enum
  EVisa("EVisa", "E-Visa"),
  EMaster("EMaster", "E-Master"),
  EApplePay("EApplePay", "E-ApplePay"),
  EGooglePay("EGooglePay", "E-GooglePay"),

  // EFT用到的支付方式enum
  // 线上（H5、小程序）支付
  MWXPayCN("MWXPayCN", "M-WXPay-CN"),
  MWXPayHK("MWXPayHK", "M-WXPay-HK"),
  MAliPayCN("MAliPayCN", "M-AliPay-CN"),
  MAliPayHK("MAliPayHK", "M-AliPay-HK"),
  // 线下（POS）支付
  PWXPayCN("PWXPayCN", "P-WXPay-CN"),
  PWXPayHK("PWXPayHK", "P-WXPay-HK"),
  PAliPayCN("PAliPayCN", "P-AliPay-CN"),
  PAliPayHK("PAliPayHK", "P-AliPay-HK"),
  ;

  private final String code;
  private final String name;

  PayMethod(String code, String name) {
    this.code = code;
    this.name = name;
  }

  public static PayMethod byCode(String code) {
    for (PayMethod value : PayMethod.values()) {
      if (value.code.equals(code)) {
        return value;
      }
    }

    return null;
  }

  public String getCode() {
    return code;
  }

  public String getName() {
    return name;
  }

}
