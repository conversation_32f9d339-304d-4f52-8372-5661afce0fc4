package cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums;

/**
 * @ClassName TransactionType.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/18 15:49:19
 */
public enum TransactionState {

  WAITING("WAITING", "待支付"),
  SUCCESS("SUCCESS", "支付成功"),
  FAILED("FAILED", "支付失败"),
  CANCELED("CANCELED", "支付撤销"),
  PENDING("PENDING", "交易处理中"),
  REFUNDED("REFUNDED", "支付退款"),
  CLOSED("CLOSED", "支付关闭"),
  UNKNOWN("UNKNOWN", "未知状态"),
  ;

  private final String code;
  private final String name;

  TransactionState(String code, String name) {
    this.code = code;
    this.name = name;
  }

  public static TransactionState byCode(String code) {
    for (TransactionState value : TransactionState.values()) {
      if (value.code.equals(code)) {
        return value;
      }
    }

    return null;
  }

  public String getCode() {
    return code;
  }

  public String getName() {
    return name;
  }

}
