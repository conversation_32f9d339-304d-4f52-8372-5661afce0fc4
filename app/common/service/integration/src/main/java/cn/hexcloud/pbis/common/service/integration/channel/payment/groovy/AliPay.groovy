package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Commodity
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Promotion
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.alipay.api.AlipayClient
import com.alipay.api.DefaultAlipayClient
import com.alipay.api.domain.VoucherDetail
import com.alipay.api.internal.util.AlipaySignature
import com.alipay.api.request.AlipayTradeCancelRequest
import com.alipay.api.request.AlipayTradePayRequest
import com.alipay.api.request.AlipayTradeQueryRequest
import com.alipay.api.request.AlipayTradeRefundRequest
import com.alipay.api.response.AlipayTradeCancelResponse
import com.alipay.api.response.AlipayTradePayResponse
import com.alipay.api.response.AlipayTradeQueryResponse
import com.alipay.api.response.AlipayTradeRefundResponse
import org.apache.commons.lang3.StringUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp

class AliPay extends AbstractExternalChannelModule implements PaymentModule {

  private static final String FORMAT = "JSON"
  private static final String SIGN_TYPE = "RSA2"
  private static final String PAY_SUCCESS = "10000"

  AliPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("create"))
  }

  //https://opendocs.alipay.com/open/194/105072?ref=api
  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    // 请求参数
    JSONObject body = new JSONObject()
    body.put("out_trade_no", request.getTransactionId())
    body.put("total_amount", getAliPayAmount(request.getAmount()))
    body.put("subject", request.getOrderDescription())
    body.put("store_id", ServiceContext.getString(ContextKeyConstant.STORE_CODE))
    body.put("trans_currency", "CNY")
    body.put("settle_currency", "CNY")
    body.put("timeout_express", "5m")
    body.put("scene", "bar_code")
    body.put("auth_code", request.getPayCode())
    String payDesc = null
    String extendedParams = request.getExtendedParams()
    if (StringUtils.isNotEmpty(extendedParams)) {
      JSONObject extendedParamsJSON = JSONObject.parseObject(extendedParams)
      if(extendedParamsJSON != null) {
        String storeCode = extendedParamsJSON.getString("store_code")
        String storeName = extendedParamsJSON.getString("store_name")
        payDesc = String.format("%s（%s）", storeName, storeCode)
      }
    }
    // 商品处理
    if (request.getCommodities()) {
      JSONArray goodsDetail = new JSONArray();
      for (Commodity commodity in request.getCommodities()) {
        JSONObject goods = new JSONObject()
        goods.put("goods_id", commodity.getCode())
        if (payDesc != null) {
          goods.put("goods_name", payDesc)
        } else {
          goods.put("goods_name", commodity.getName())
        }
        goods.put("quantity", commodity.getQuantity())
        goods.put("price", getAliPayAmount(commodity.getPrice()))
        goodsDetail.add(goods)
      }
      if (goodsDetail.size() > 0) {
        body.put("goods_detail", goodsDetail)
      }
    }
    body.put("query_options", ["buyer_pay_amount", "fund_bill_list", "discount_goods_detail", "voucher_detail_list",
                               "advance_amount", "charge_amount", "mdiscount_amount", "discount_amount"])
    String bodyJSON = body.toJSONString()
    // 发起请求
    String methodFullName = getMethodFullName("pay")
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    AlipayClient alipayClient = getAlipayClient()
    AlipayTradePayRequest alipayRequest = new AlipayTradePayRequest()
    alipayRequest.setNotifyUrl(getNotificationUrl())
    alipayRequest.setBizContent(bodyJSON)
    AlipayTradePayResponse alipayResponse = alipayClient.execute(alipayRequest)
    Timestamp respTime = DateUtil.getNowTimeStamp()
    String resultJSON = JSONObject.toJSONString(alipayResponse)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSON)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyJSON)
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSON)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)

    // 解析并返回结果
    ChannelPayResponse response = new ChannelPayResponse()
    if (alipayResponse.isSuccess()) {
      // https://opendocs.alipay.com/common/02km9f
      response.setTransactionState(TransactionState.SUCCESS)
      if (alipayResponse.getCode() != PAY_SUCCESS) {
        response.setTransactionState(TransactionState.PENDING)
        response.setErrorMessage(alipayResponse.getSubMsg())
      }
      response.setChannel(request.getChannel())
      response.setPayMethod(PayMethod.ALI_PAY)
      response.setTpTransactionId(alipayResponse.getTradeNo())
      response.setTransactionId(request.getTransactionId())
      response.setRealAmount(new BigDecimal(getHexCloudAmount(alipayResponse.getBuyerPayAmount())))
      response.setExtendedParams(request.getExtendedParams())
      List<VoucherDetail> voucherDetailList = alipayResponse.getVoucherDetailList()
      if (null != voucherDetailList && voucherDetailList.size() > 0) {
        List<Promotion> promotions = new ArrayList<>()
        for (VoucherDetail voucherDetail : voucherDetailList) {
          Promotion promotion = new Promotion()
          promotion.setId(voucherDetail.getId())
          promotion.setName(voucherDetail.getName())
          promotion.setDiscount(getHexCloudAmount(voucherDetail.getAmount()))
          promotion.setDiscountOnMerchant(getHexCloudAmount(voucherDetail.getMerchantContribute()))
          promotion.setDiscountOnOthers(getHexCloudAmount(voucherDetail.getOtherContribute()))
          promotion.setUserPayAmount(getHexCloudAmount(voucherDetail.getPurchaseBuyerContribute()))
          promotion.setMerchantPayAmount(getHexCloudAmount(voucherDetail.getPurchaseMerchantContribute()))
          promotion.setPlatformPayAmount(getHexCloudAmount(voucherDetail.getPurchaseAntContribute()))
          promotions.add(promotion)
        }
        response.setPromotions(promotions)
      }
    } else {
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null,
          methodFullName, alipayResponse.getSubCode(), alipayResponse.getSubMsg())
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, alipayResponse.getSubCode() + "-" + alipayResponse.getSubMsg())
    }

    return response
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    // 请求参数
    JSONObject body = new JSONObject()
    body.put("out_trade_no", request.getTransactionId())
    String bodyJSON = body.toJSONString()

    // 发起请求
    String methodFullName = getMethodFullName("query")
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
    AlipayClient alipayClient = getAlipayClient()
    AlipayTradeQueryRequest alipayRequest = new AlipayTradeQueryRequest()
    alipayRequest.setBizContent(bodyJSON)
    AlipayTradeQueryResponse alipayResponse = alipayClient.execute(alipayRequest)
    String resultJSON = JSONObject.toJSONString(alipayResponse)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSON)

    // 解析并返回结果
    ChannelQueryResponse response = new ChannelQueryResponse()
    if (alipayResponse.isSuccess()) {
      response.setChannel(request.getChannel())
      response.setPayMethod(PayMethod.ALI_PAY)
      response.setTransactionState(mapTransactionState(alipayResponse.getTradeStatus()))
      response.setTpTransactionId(alipayResponse.getTradeNo())
      response.setTransactionId(request.getTransactionId())
      response.setRealAmount(new BigDecimal(getHexCloudAmount(alipayResponse.getBuyerPayAmount())))
      List<VoucherDetail> voucherDetailList = alipayResponse.getVoucherDetailList()
      if (null != voucherDetailList && voucherDetailList.size() > 0) {
        List<Promotion> promotions = new ArrayList<>()
        for (VoucherDetail voucherDetail : voucherDetailList) {
          Promotion promotion = new Promotion()
          promotion.setId(voucherDetail.getId())
          promotion.setName(voucherDetail.getName())
          promotion.setDiscount(getHexCloudAmount(voucherDetail.getAmount()))
          promotion.setDiscountOnMerchant(getHexCloudAmount(voucherDetail.getMerchantContribute()))
          promotion.setDiscountOnOthers(getHexCloudAmount(voucherDetail.getOtherContribute()))
          promotion.setUserPayAmount(getHexCloudAmount(voucherDetail.getPurchaseBuyerContribute()))
          promotion.setMerchantPayAmount(getHexCloudAmount(voucherDetail.getPurchaseMerchantContribute()))
          promotion.setPlatformPayAmount(getHexCloudAmount(voucherDetail.getPurchaseAntContribute()))
          promotions.add(promotion)
        }
        response.setPromotions(promotions)
      }
    } else {
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null,
          methodFullName, alipayResponse.getSubCode(), alipayResponse.getSubMsg())
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, alipayResponse.getSubCode() + "-" + alipayResponse.getSubMsg())
    }

    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    JSONObject body = new JSONObject()
    body.put("out_trade_no", request.getRelatedTransactionId())
    body.put("trade_no", request.getRelatedTPTransactionId())
    body.put("refund_amount", getAliPayAmount(request.getAmount()))
    body.put("out_request_no", request.getTransactionId())
    String bodyJSON = body.toJSONString()

    // 发起请求
    String methodFullName = getMethodFullName("refund")
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    AlipayClient alipayClient = getAlipayClient()
    AlipayTradeRefundRequest alipayRequest = new AlipayTradeRefundRequest()
    alipayRequest.setBizContent(bodyJSON)
    AlipayTradeRefundResponse alipayResponse = alipayClient.execute(alipayRequest)
    Timestamp respTime = DateUtil.getNowTimeStamp()
    String resultJSON = JSONObject.toJSONString(alipayResponse)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSON)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyJSON)
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSON)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)

    // 解析并返回结果
    ChannelRefundResponse response = new ChannelRefundResponse()
    if (alipayResponse.isSuccess()) {
      response.setTransactionState(TransactionState.PENDING)
      response.setTpTransactionId(alipayResponse.getTradeNo())
      response.setTransactionId(request.getTransactionId())
      response.setRealAmount(request.getAmount())
    } else {
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null,
          methodFullName, alipayResponse.getSubCode(), alipayResponse.getSubMsg())
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, alipayResponse.getSubCode() + "-" + alipayResponse.getSubMsg())
    }

    return response
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    // 请求参数
    JSONObject body = new JSONObject()
    body.put("out_trade_no", request.getRelatedTransactionId())
    String bodyJSON = body.toJSONString()

    // 发起请求
    String methodFullName = getMethodFullName("cancel")
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    AlipayClient alipayClient = getAlipayClient()
    AlipayTradeCancelRequest alipayRequest = new AlipayTradeCancelRequest()
    alipayRequest.setBizContent(bodyJSON)
    AlipayTradeCancelResponse alipayResponse = alipayClient.execute(alipayRequest)
    Timestamp respTime = DateUtil.getNowTimeStamp()
    String resultJSON = JSONObject.toJSONString(alipayResponse)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSON)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyJSON)
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSON)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)

    // 解析并返回结果
    ChannelCancelResponse response = new ChannelCancelResponse()
    if (alipayResponse.isSuccess()) {
      response.setTransactionState(TransactionState.SUCCESS)
      response.setTpTransactionId(alipayResponse.getTradeNo())
      response.setRealAmount(request.getAmount())
    } else {
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null,
          methodFullName, alipayResponse.getSubCode(), alipayResponse.getSubMsg())
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, alipayResponse.getSubCode() + "-" + alipayResponse.getSubMsg())
    }

    return response
  }

  @Override
  ChannelNotificationResponse payNotify(HttpServletRequest request) {
    // 解析参数并对请求进行验签
    String callbackStr = request.getParameter("payload")
    LoggerUtil.info("{0} received message: {1}.", getMethodFullName("payNotify"), callbackStr)
    Map<String, String> unverifiedMessage = getUrlParamsMap(callbackStr)
    if (!isValidSignature(unverifiedMessage)) {
      // 验签失败
      throw new CommonException(ServiceError.INVALID_SIGNATURE)
    }

    // 返回结果
    ChannelNotificationResponse response = new ChannelNotificationResponse()
    String response2ThirdParty = "success"
    response.setResponse("response2ThirdParty")
    ChannelPayResponse payResponse = new ChannelQueryResponse()
    payResponse.setTransactionId(unverifiedMessage.get("out_trade_no"))
    payResponse.setTpTransactionId(unverifiedMessage.get("trade_no"))
    payResponse.setRealAmount(getHexCloudAmount(unverifiedMessage.get("total_amount")))
    payResponse.setTransactionState(mapTransactionState(unverifiedMessage.get("trade_status")))
    response.setPayResponse(payResponse)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, callbackStr)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, response2ThirdParty)

    return response
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("getSignature"))
  }

  @Override
  boolean isValidSignature(Map<String, String> unverifiedMessage) {
    // 解析请求，获取待验签所需的数据
    String charset = unverifiedMessage.get("charset")
    String signType = unverifiedMessage.get("sign_type")
    if (StringUtils.isAnyBlank(charset, signType)) {
      return false
    }

    return AlipaySignature.verifyV1(unverifiedMessage, channel.getChannelAccessConfig().getThirdPartyPublicKey(), charset, signType)
  }

  private static TransactionState mapTransactionState(String alipayTransactionState) {
    TransactionState transactionState
    switch (alipayTransactionState) {
      case "WAIT_BUYER_PAY":
        transactionState = TransactionState.WAITING
        break
      case "TRADE_SUCCESS":
      case "TRADE_FINISHED":
        transactionState = TransactionState.SUCCESS
        break
      case "TRADE_CLOSED":
        transactionState = TransactionState.REFUNDED
        break
      default:
        transactionState = TransactionState.UNKNOWN
    }
    return transactionState
  }

  private static BigDecimal getHexCloudAmount(String alipayAmount) {
    return new BigDecimal(alipayAmount) * 100
  }

  private static BigDecimal getAliPayAmount(BigDecimal hexCloudAmount) {
    return hexCloudAmount / 100
  }

  private String getNotificationUrl() {
    String notificationUrl = channel.getChannelAccessConfig().getProperty("notification_url")
    if (StringUtils.isNotBlank(notificationUrl)) {
      String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
      String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
      String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId
      return notificationUrl.endsWith("/") ? notificationUrl + path : notificationUrl + "/" + path
    }

    return null
  }

  private AlipayClient getAlipayClient() {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    return new DefaultAlipayClient(channelAccessConfig.getProperty("gateway_url"),
        channelAccessConfig.getAppId(), channelAccessConfig.getPrivateKey(), FORMAT,
        StandardCharsets.UTF_8.toString(), channelAccessConfig.getThirdPartyPublicKey(), SIGN_TYPE)
  }

  private static Map<String, String> getUrlParamsMap(String urlParams) {
    Map<String, String> paramsMap = new HashMap<>()
    if (StringUtils.isEmpty(urlParams)) {
      return paramsMap
    }

    String[] urlParamArr = urlParams.split("&")
    for (String urlParam : urlParamArr) {
      String[] paramPair = urlParam.split("=")
      paramsMap.put(paramPair[0], URLDecoder.decode(paramPair[1], StandardCharsets.UTF_8.toString()))
    }

    return paramsMap
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }

}
