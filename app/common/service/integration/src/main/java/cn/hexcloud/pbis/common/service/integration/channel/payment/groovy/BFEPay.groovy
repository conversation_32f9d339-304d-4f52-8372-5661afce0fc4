package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils
import org.springframework.util.DigestUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp

class BFEPay extends AbstractExternalChannelModule implements PaymentModule {

  private static final String SUCCESS_CODE = "0"

  private static final Map<String, String> METHOD_MAP

  static {
    METHOD_MAP = new HashMap<>()
    METHOD_MAP.put("pay", "/user/balance/pay")
    METHOD_MAP.put("refund", "/user/balance/refund")
  }

  BFEPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("create"))
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("payCode", request.getPayCode())
    bizParams.put("amount", request.getAmount())
    bizParams.put("orderId", request.getTransactionId())
    bizParams.put("timestamp", getTimestamp())

    // 发起请求
    JSONObject resultJSON = doRequest("pay", bizParams, true)

    // 解析并返回结果
    ChannelPayResponse response = new ChannelPayResponse()
    response.setTransactionState(TransactionState.SUCCESS)
    response.setChannel(request.getChannel())
    response.setPayMethod(PayMethod.BFE_PAY)
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(resultJSON.get("data")["transId"] as String)
    response.setRealAmount(request.getAmount())
    response.setExtendedParams(request.getExtendedParams())
    return response
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("query"))
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("userMobile", request.getMobile())
    bizParams.put("amount", request.getAmount().negate())
    bizParams.put("orderId", request.getTransactionId())
    bizParams.put("timestamp", getTimestamp())
    if (StringUtils.isNotBlank(request.getRelatedTransactionId())) {
      bizParams.put("refOrderId", request.getRelatedTransactionId())
    }

    // 发起请求
    JSONObject resultJSON = doRequest("refund", bizParams, true)

    // 解析并返回结果
    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionState(TransactionState.SUCCESS)
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(resultJSON.get("data")["transId"] as String)
    response.setRealAmount(request.getAmount())
    return response
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("cancel"))
  }

  @Override
  ChannelNotificationResponse payNotify(HttpServletRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("payNotify"))
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    StringBuilder sb = new StringBuilder()
    for (Map.Entry<String, String> entry : rawMessage.entrySet()) {
      if (null != entry.getValue()) {
        sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&")
      }
    }

    String rawStr = sb.substring(0, sb.length() - 1) + channel.getChannelAccessConfig().getAppKey()
    return DigestUtils.md5DigestAsHex(rawStr.getBytes(StandardCharsets.UTF_8)).toUpperCase()
  }

  @Override
  boolean isValidSignature(Map<String, String> unverifiedMessage) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("isValidSignature"))
  }

  private static String getTimestamp() {
    return DateUtil.getDate("yyyy-MM-dd HH:mm:ss")
  }

  private static Map<String, String> getRequestHeader() {
    Map<String, String> header = new HashMap<>()
    header.put("Content-Type", "application/json")
    return header
  }

  private JSONObject doRequest(String method, Map<String, Object> bizParams, boolean reserveData) {
    // 请求参数
    Map<String, Object> body = new HashMap<>()
    body.putAll(bizParams)

    // 签名
    Map<String, String> rawMessage = new TreeMap<>()
    for (Map.Entry<String, Object> entry : body) {
      rawMessage.put(entry.getKey(), entry.getValue().toString())
    }
    body.put("sign", getSignature(rawMessage))

    // 发起HTTP请求
    String methodFullName = getMethodFullName(method)
    String bodyJSON = JSON.toJSONString(body)
    String requestUrl = getCompleteUrl(method)
    LoggerUtil.info("{0} is sending message to: {1}, body: {2}.", methodFullName, requestUrl, bodyJSON)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result = HttpUtil.doPost(getCompleteUrl(method), bodyJSON, getRequestHeader())
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyJSON)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
    String errorCode = resultJSON.getString("errorCode")
    String errorMessage = resultJSON.getString("errorMsg")
    if (errorCode != SUCCESS_CODE) {
      // 请求失败
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, methodFullName, errorCode, errorMessage)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }

    return resultJSON
  }

  private String getCompleteUrl(String method) {
    String baseUrl = channel.getChannelAccessConfig().getGatewayUrl()
    return baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) + METHOD_MAP.get(method) : baseUrl + METHOD_MAP.get(method)
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }

}
