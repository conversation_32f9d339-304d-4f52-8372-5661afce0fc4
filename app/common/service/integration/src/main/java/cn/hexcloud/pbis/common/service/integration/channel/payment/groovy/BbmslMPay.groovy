package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.NotificationType
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.security.GeneralSecurityException
import java.security.KeyFactory
import java.security.PrivateKey
import java.security.PublicKey
import java.security.Signature
import java.security.spec.PKCS8EncodedKeySpec
import java.security.spec.X509EncodedKeySpec
import java.sql.Timestamp

/**
 * bbMSL支付
 * <pre>
 *    URL: https://docs.bbmsl.com/docs/guide/
 *    描述: H5
 * </pre>
 * <AUTHOR> Wang
 */
class BbmslMPay extends AbstractExternalChannelModule implements PaymentModule {

    private static final String KEY_ALGORITHM = "RSA"
    private static final String SIGNATURE_ALGORITHM = "SHA256WithRSA"
    private static final String DEFAULT_CHARSET = "UTF-8"
    private static final String SUCCESS_CODE = "0000"

    private static final Map<String, String> URL_MAP

    static {
        URL_MAP = new HashMap<>()
        URL_MAP.put("create", "create_url")
        URL_MAP.put("query", "query_url")
        URL_MAP.put("refund", "refund_url")
        URL_MAP.put("cancel", "cancel_url")
        URL_MAP.put("notification", "notification_url")
    }

    BbmslMPay(ExternalChannel channel) {
        super(channel)
    }

    @Override
    String getModuleName() {
        return "Payment"
    }

    @Override
    ChannelCreateResponse create(ChannelCreateRequest request) {
        String method = "create"

        // 请求URL
        String requestUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get(method))

        // 请求参数
        Map<String, Object> bizParams = new LinkedHashMap<>()
        // 参数（商户编码）
        bizParams.put("merchantId", channel.getChannelAccessConfig().getMerchantId())
        // 参数（币种：默认HKD）
        bizParams.put("currency", "HKD")
        // 参数（金额：元）
        bizParams.put("amount", convertToDollar(request.getAmount()))
        // 参数（识别此订单的唯一参考）
        bizParams.put("merchantReference", request.getTransactionId())
        // 回调URL
        Map<String, String> callBackUrl = new LinkedHashMap<>()
        callBackUrl.put("success", getGatewayUrl())
        callBackUrl.put("notify", getNotificationUrl())
        bizParams.put("callbackUrl", callBackUrl)
        // 虚拟固定一个商品
        List<Map<String, Object>> lineItems = new ArrayList<>()
        Map<String, Object> data = new LinkedHashMap<>()
        // 商品数量
        data.put("quantity", 1)
        // 商品价格详情
        Map<String, Object> priceData = new LinkedHashMap<>()
        // 商品价格
        priceData.put("unitAmount", convertToDollar(request.getAmount()))
        // 商品名称
        priceData.put("name", "bbMSL支付商品")
        data.put("priceData", priceData)
        lineItems.add(data)
        bizParams.put("lineItems", lineItems)

        // 执行
        JSONObject resultJSON = doRequest(method, requestUrl, bizParams, true)

        // 解析结果集
        String frontUrl = resultJSON.getString("checkoutUrl")
        JSONObject orderJSON = resultJSON.getJSONObject("order")
        String orderId = orderJSON.getString("id")

        ChannelCreateResponse response = new ChannelCreateResponse()
        response.setChannel(request.getChannel())
        // 支付界面URL
        response.setFrontUrl(frontUrl)
        // 预支付ID
        response.setPrePayId(orderId)
        // 第三方tpTransactionId
        response.setTpTransactionId(orderId)
        return response
    }

    @Override
    ChannelQueryResponse query(ChannelQueryRequest request) {
        String method = "query"

        // 请求URL
        String requestUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get(method))

        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("merchantId", channel.getChannelAccessConfig().getMerchantId())
        bizParams.put("orderId", request.getTpTransactionId())

        // 执行
        JSONObject resultJSON = doRequest(method, requestUrl, bizParams, false)

        // 解析结果集
        ChannelPayResponse response = new ChannelQueryResponse()
        response.setChannel(request.getChannel())
        JSONObject orderJSON = resultJSON.getJSONObject("order")
        String orderId = orderJSON.getString("id")
        response.setTpTransactionId(orderId)
        response.setRealAmount(convertToPoint(orderJSON.getBigDecimal("amount")))
        response.setTransactionState(mapTransactionState(orderJSON.getString("status")))
        return response
    }

    @Override
    ChannelRefundResponse refund(ChannelRefundRequest request) {
        String method = "refund"

        // 请求URL
        String requestUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get(method))

        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("merchantId", channel.getChannelAccessConfig().getMerchantId())
        bizParams.put("orderId", request.getRelatedTPTransactionId())
        bizParams.put("amount", convertToDollar(request.getAmount()))

        // 执行
        JSONObject resultJSON = doRequest(method, requestUrl, bizParams, true)

        // 解析结果集
        ChannelRefundResponse response = new ChannelRefundResponse()
        JSONObject orderJSON = resultJSON.getJSONObject("transaction")
        String orderId = orderJSON.getString("id")
        response.setTpTransactionId(orderId)
        response.setRealAmount(convertToPoint(orderJSON.getBigDecimal("amount")))
        response.setTransactionState(mapTransactionState(orderJSON.getString("status")))
        return response
    }

    @Override
    ChannelCancelResponse cancel(ChannelCancelRequest request) {
        String method = "cancel"

        // 请求URL
        String requestUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get(method))

        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("merchantId", channel.getChannelAccessConfig().getMerchantId())
        bizParams.put("orderId", request.getRelatedTPTransactionId())

        // 执行
        JSONObject resultJSON = doRequest(method, requestUrl, bizParams, true)

        // 解析结果集
        ChannelCancelResponse response = new ChannelCancelResponse()
        JSONObject orderJSON = resultJSON.getJSONObject("transaction")
        String orderId = orderJSON.getString("id")
        response.setTpTransactionId(orderId)
        response.setRealAmount(convertToPoint(orderJSON.getBigDecimal("amount")))
        response.setTransactionState(mapTransactionState(orderJSON.getString("status")))
        return response
    }

    @Override
    ChannelNotificationResponse payNotify(HttpServletRequest request) {
        String method = "payNotify"
        String fullMethod = getFullMethodName(method)
        String payloadJSONStr = request.getParameter("payload")
        LoggerUtil.info("{0} received message: {1}", fullMethod, payloadJSONStr)
        JSONObject payloadJSON = JSONObject.parseObject(payloadJSONStr)
        Map<String, String> unverifiedMessage = getUnverifiedMessage(payloadJSON)
        if (!isValidSignature(unverifiedMessage)) {
            throw new CommonException(ServiceError.INVALID_SIGNATURE)
        }

        // 解析返回结果集
        ChannelNotificationResponse response = new ChannelNotificationResponse()
        String third2Response = "{\"responseCode\":\"OK\",\"message\":\"SUCCESS\"}"
        response.setResponse(third2Response)
        ChannelPayResponse payResponse = new ChannelPayResponse()
        // （参数）通知类型
        payResponse.setNotificationType(NotificationType.PAY)
        // （参数）实际支付金额
        payResponse.setRealAmount(convertToPoint(payloadJSON.getBigDecimal("amount")))
        // （参数）支付请求ID
        payResponse.setTpTransactionId(payloadJSON.getString("orderId"))
        // （参数）支付状态
        payResponse.setTransactionState(mapTransactionState(payloadJSON.getString("status")))
        response.setPayResponse(payResponse)

        // 设置上下文（出入报文）
        TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, payloadJSONStr)
        TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, third2Response)

        return response
    }

    @Override
    boolean isValidSignature(Map<String, String> unverifiedMessage) {
        // 参数（公钥）
        String publicKey = channel.channelAccessConfig.getThirdPartyPublicKey()
        // 参数（signature）
        String signature = unverifiedMessage.get("signature")
        // 去掉signature
        unverifiedMessage.remove("signature")
        // 参数（验证的内容）
        String preVerifyContent = createPreVerifyString(unverifiedMessage)
        return verify(preVerifyContent, publicKey, signature)
    }

    // 统一请求
    private JSONObject doRequest(String method, String requestUrl, Map<String, Object> bizParams, boolean reserveData) {
        String fullMethodName = getFullMethodName(method)
        LoggerUtil.info("{0} is sending request URL: {1}", fullMethodName, requestUrl)

        String requestParams = JSONObject.toJSONString(bizParams)
        LoggerUtil.info("{0} is sending request params: {1}", fullMethodName, requestParams)
        Timestamp reqTime = cn.hexcloud.commons.utils.DateUtil.getNowTimeStamp()
        try {
            // 请求Header
            Map<String, String> headers = new HashMap<>()
            headers.put("Content-Type", "application/json")

            // 请求Body
            Map<String, Object> body = new HashMap<>()
            body.put("request", requestParams)
            body.put("signature", createSign(requestParams))
            String requestBody = JSONObject.toJSONString(body)

            byte[] result = HttpUtil.doPost(requestUrl, requestBody, headers)
            if (null == result) {
                LoggerUtil.error("{0} is failed with null result.", null, fullMethodName)
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
            }
            Timestamp respTime = cn.hexcloud.commons.utils.DateUtil.getNowTimeStamp()
            String resultJSONStr = new String(result, StandardCharsets.UTF_8)
            LoggerUtil.info("{0} received message: {1}", fullMethodName, resultJSONStr)

            // 设置上下文（出入报文）
            if (reserveData) {
                TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
                TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, requestBody)
                TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
                TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
            }

            JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
            String responseCode = resultJSON.getString("responseCode")
            if (SUCCESS_CODE.equalsIgnoreCase(responseCode)) {
                return resultJSON
            }
            String message = resultJSON.getString("message")
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, message)
        } catch (Exception ex) {
            LoggerUtil.error("Error {0} payment request: ", ex, fullMethodName)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, ex.getMessage())
        }
    }

    // 产生 Signature
    private String createSign(String content) throws GeneralSecurityException {
        String privateKeyPEM = channel.getChannelAccessConfig().getPrivateKey()
        // 1. 将 base64 编码的私钥字符串解码为字节数组
        byte[] privateKeyBytes = Base64.getDecoder().decode(privateKeyPEM)

        // 2. 创建 PKCS8EncodedKeySpec 对象
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(privateKeyBytes)

        // 3. 获取 KeyFactory 实例
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM)

        // 4. 通过 KeyFactory 生成 PrivateKey 对象
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec)

        // 5. 创建 Signature 实例并初始化
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM)
        signature.initSign(privateKey)

        // 6. 对 JSON 内容进行哈希处理并签名
        byte[] jsonBytes = content.getBytes(DEFAULT_CHARSET)
        signature.update(jsonBytes)
        byte[] signatureBytes = signature.sign()

        // 7. 将签名结果 base64 编码并返回
        return Base64.getEncoder().encodeToString(signatureBytes)
    }

    // 获取通知URL
    private String getGatewayUrl() {
        return channel.getChannelAccessConfig().getProperty("gateway_url")
    }

    // 获取通知URL
    private String getNotificationUrl() {
        String notificationUrl = channel.getChannelAccessConfig().getProperty("notification_url")
        if (StringUtils.isNotBlank(notificationUrl)) {
            String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
            String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
            String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId
            return notificationUrl.endsWith("/") ? notificationUrl + path : notificationUrl + "/" + path
        }
        return null
    }

    // 获取未验证的消息
    private Map<String, String> getUnverifiedMessage(JSONObject jsonObject) {
        Map<String, String> resultMap = new HashMap<>()
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            String key = entry.getKey()
            Object value = jsonObject.get(key)
            resultMap.put(key, String.valueOf(value))
        }
        return resultMap
    }

    // 产生带验证的内容
    private String createPreVerifyString(Map<String, String> params) {
        List<String> keys = new ArrayList<>(params.keySet())
        Collections.sort(keys)
        String prestr = ""
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i)
            String value = params.get(key)
            if (i == keys.size() - 1) {
                prestr = prestr + key + "=" + value
            } else {
                prestr = prestr + key + "=" + value + "&"
            }
        }
        return prestr
    }

    // 验证
    private boolean verify(String content, String publicKey, String sign) throws Exception {
        byte[] publicKeyBytes = Base64.getDecoder().decode(publicKey)
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes)
        KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM)
        PublicKey pubKey = keyFactory.generatePublic(keySpec)
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM)
        signature.initVerify(pubKey)
        signature.update(content.getBytes(DEFAULT_CHARSET))
        return signature.verify(Base64.getDecoder().decode(sign))
    }

    // 状态转换
    private static TransactionState mapTransactionState(String tpTransactionState) {
        TransactionState transactionState
        switch (tpTransactionState) {
            case "OPEN":
                transactionState = TransactionState.WAITING
                break
            case "SUCCESS":
                transactionState = TransactionState.SUCCESS
                break
            case "REFUNDED":
                transactionState = TransactionState.REFUNDED
                break
            case "VOIDED":
                transactionState = TransactionState.CANCELED
                break
            case "CLOSED":
                transactionState = TransactionState.CLOSED
                break
            default:
                transactionState = TransactionState.FAILED
        }
        return transactionState
    }

    // 分转成元
    private static BigDecimal convertToDollar(BigDecimal dollar) {
        if (dollar == null) {
            return null
        }
        return dollar.divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP)
    }

    // 元转换成分
    private static BigDecimal convertToPoint(BigDecimal point) {
        if (point == null) {
            return null
        }
        return (point * new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP)
    }

}
