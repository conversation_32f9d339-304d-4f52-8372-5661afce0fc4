package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.pbis.common.service.integration.boh.CommonResponse
import cn.hexcloud.pbis.common.service.integration.boh.QueryPayStatusRequest
import cn.hexcloud.pbis.common.service.integration.boh.QueryPayStatusResponse
import cn.hexcloud.pbis.common.service.integration.boh.VoucherPayRequest
import cn.hexcloud.pbis.common.service.integration.boh.client.BohCreditPayClient
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext

/**
 * @program: pbis
 * @author: miao
 * @create: 2022-11-17 15:41
 * */
class BohVoucherPay extends AbstractExternalChannelModule implements PaymentModule {
  BohVoucherPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    ChannelPayResponse response = new ChannelPayResponse()
    response.setPayMethod(PayMethod.BOH_CREDIT_PAY)
    response.setTransactionState(TransactionState.SUCCESS)
    BohCreditPayClient bohCreditPayClient = SpringContextUtil.bean(BohCreditPayClient.class)
    VoucherPayRequest voucherPayRequest = VoucherPayRequest
        .newBuilder()
        .setOrderNo(request.getOrderNo())
        .setVoucherId(request.getPayCode())
        .build()
    String methodFullName = getFullMethodName("pay")
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, voucherPayRequest.toString())
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, voucherPayRequest.toString())
    CommonResponse commonResponse = bohCreditPayClient.voucherPayRequest(voucherPayRequest)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, commonResponse.toString())
    LoggerUtil.info("{0} received message: {1}.", methodFullName, commonResponse.toString())
    if (commonResponse.getCode() != 0) {
      response.setTransactionState(TransactionState.FAILED)
      response.setErrorMessage(commonResponse.getMessage())
    }
    return response
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setTransactionState(TransactionState.SUCCESS)
    BohCreditPayClient bohCreditPayClient = SpringContextUtil.bean(BohCreditPayClient.class)
    QueryPayStatusRequest queryRequest = QueryPayStatusRequest
        .newBuilder()
        .setChannel(channel.getChannelAccessConfig().getChannelCode())
        .setOrderNo(request.getOrderNo())
        .setTransactionId(request.getTransactionId())
        .build()
    String methodFullName = getFullMethodName("query")
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, queryRequest.toString())
    QueryPayStatusResponse queryResponse = bohCreditPayClient.queryPayStatus(queryRequest)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, queryResponse.toString())
    if (queryResponse.getErrorCode() != "0") {
      response.setTransactionState(TransactionState.FAILED)
      response.setErrorMessage(queryResponse.getErrorMessage())
      return response
    }
    response.setTransactionState(mapTransactionState(queryResponse.getTransactionState()))
    response.setErrorMessage(queryResponse.getErrorMessage())
    response.setChannel(queryResponse.getPayChannel())
    response.setRealAmount(BigDecimal.valueOf(queryResponse.getRealAmount()))
    response.setTransactionId(queryResponse.getTpTransactionId())
    return response
  }

  private static TransactionState mapTransactionState(String tpTransactionState) {
    TransactionState transactionState
    switch (tpTransactionState) {
      case "Waiting":
        transactionState = TransactionState.WAITING
        break
      case "Success":
        transactionState = TransactionState.SUCCESS
        break
      case "Failed":
        transactionState = TransactionState.FAILED
        break
      default:
        transactionState = TransactionState.UNKNOWN
    }
    return transactionState
  }
}
