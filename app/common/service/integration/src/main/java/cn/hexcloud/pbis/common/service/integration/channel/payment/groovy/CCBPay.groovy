package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.RedisUtil
import cn.hexcloud.commons.utils.cipher.RSAUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.ccb.mis.CcbMisSdk
import com.ccb.mis.entity.ParamEntity
import com.ccb.mis.service.inf.AuthService
import com.ccb.mis.service.inf.AuthorKeyService
import com.ccb.mis.service.inf.MisAggregateService
import org.apache.commons.lang3.StringUtils

import java.sql.Timestamp
import java.util.concurrent.TimeUnit

class CCBPay extends AbstractExternalChannelModule implements PaymentModule {

  private static final String SUCCESS_CODE = "00"
  private static final String PENDING_CODE = "-2"
  private static final String INVALID_KEY_CODE = "30"

  CCBPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    String fullMethodName = getFullMethodName("pay")

    // 发起请求
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    String resultJSONStr = getMisAggregateService().aggregatePay(parseYuan(request.getAmount()) as String,
        request.getTransactionId(),
        request.getPayCode())
    Timestamp respTime = DateUtil.getNowTimeStamp()
    LoggerUtil.info("{0} received message: {1}.", fullMethodName, resultJSONStr)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSON.toJSONString(request))
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)

    // 解析并返回结果
    JSONObject resultJSON = JSON.parseObject(resultJSONStr)
    String errorCode = resultJSON.getString("retCode")
    String errorMessage = resultJSON.getString("retMsg")
    JSONObject transDataNode = resultJSON.getJSONObject("transData")
    if (SUCCESS_CODE != errorCode && null == transDataNode) {
      // 请求失败
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, fullMethodName, errorCode, errorMessage)
      if (INVALID_KEY_CODE == errorCode) {
        // 通讯密钥错误，尝试针对性处理
        handleInvalidKeyException()
      }
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }

    ChannelPayResponse response = new ChannelPayResponse()
    response.setTransactionId(request.getTransactionId())
    response.setChannel(request.getChannel())
    response.setRealAmount(request.getAmount())
    response.setExtendedParams(request.getExtendedParams())
    response.setTpTransactionId(transDataNode.getString("refNo"))
    response.setPayMethod(parsePayMethod(transDataNode.getString("transType")))
    if (SUCCESS_CODE == errorCode) {
      response.setTransactionState(TransactionState.SUCCESS)
    } else if (PENDING_CODE == errorCode) {
      response.setTransactionState(TransactionState.PENDING)
    } else {
      response.setTransactionState(TransactionState.FAILED)
      if (StringUtils.isNotBlank(errorMessage)) {
        response.setWarning(true)
        response.setWarningMessage(errorMessage)
      }
    }

    return response
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    String fullMethodName = getFullMethodName("query")

    // 发起请求
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    String resultJSONStr = getMisAggregateService().aggregatePayQuery(request.getTransactionId())
    Timestamp respTime = DateUtil.getNowTimeStamp()
    LoggerUtil.info("{0} received message: {1}.", fullMethodName, resultJSONStr)

    // 解析并返回结果
    JSONObject resultJSON = JSON.parseObject(resultJSONStr)
    String errorCode = resultJSON.getString("retCode")
    String errorMessage = resultJSON.getString("retMsg")
    if (SUCCESS_CODE != errorCode) {
      // 请求失败
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, fullMethodName, errorCode, errorMessage)
      if (INVALID_KEY_CODE == errorCode) {
        // 通讯密钥错误，尝试针对性处理
        handleInvalidKeyException()
      }
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }

    JSONObject transDataNode = resultJSON.getJSONObject("transData")
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setChannel(request.getChannel())
    response.setPayMethod(parsePayMethod(transDataNode.getString("transType")))
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(transDataNode.getString("refNo"))
    response.setRealAmount(parseFen(transDataNode.getBigDecimal("amt")))
    response.setTransactionState(parseTransactionState(transDataNode.getString("statusCode")))
    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    String fullMethodName = getFullMethodName("refund")

    // 发起请求
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    String resultJSONStr = getMisAggregateService().aggregatePayRefund(parseYuan(request.getAmount()) as String,
        request.getRelatedTransactionId(),
        request.getTransactionId())
    Timestamp respTime = DateUtil.getNowTimeStamp()
    LoggerUtil.info("{0} received message: {1}.", fullMethodName, resultJSONStr)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSON.toJSONString(request))
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)

    // 解析并返回结果
    JSONObject resultJSON = JSON.parseObject(resultJSONStr)
    String errorCode = resultJSON.getString("retCode")
    String errorMessage = resultJSON.getString("retMsg")
    JSONObject transDataNode = resultJSON.getJSONObject("transData")
    if (SUCCESS_CODE != errorCode && null == transDataNode) {
      // 请求失败
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, fullMethodName, errorCode, errorMessage)
      if (INVALID_KEY_CODE == errorCode) {
        // 通讯密钥错误，尝试针对性处理
        handleInvalidKeyException()
      }
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }

    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(transDataNode.getString("refNo"))
    response.setRealAmount(request.getAmount())
    if (SUCCESS_CODE == errorCode) {
      response.setTransactionState(TransactionState.SUCCESS)
    } else if (PENDING_CODE == errorCode) {
      response.setTransactionState(TransactionState.PENDING)
    } else {
      response.setTransactionState(TransactionState.FAILED)
      if (StringUtils.isNotBlank(errorMessage)) {
        response.setWarning(true)
        response.setWarningMessage(errorMessage)
      }
    }
    return response
  }

  private AuthService getAuthService() {
    return CcbMisSdk.getInstance().getAuthService(getParamEntity())
  }

  private AuthorKeyService getAuthorKeyService() {
    return CcbMisSdk.getInstance().getAuthorKeyService(getParamEntityWithAuthKey(false))
  }

  private MisAggregateService getMisAggregateService() {
    return CcbMisSdk.getInstance().getMisAggregateService(getParamEntityWithAuthKey(true))
  }

  private ParamEntity getParamEntity() {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    ParamEntity paramEntity = new ParamEntity()
    paramEntity.setMerchantId(channelAccessConfig.getMerchantId())
    paramEntity.setTerminalId(channelAccessConfig.getTerminalId())
    return paramEntity
  }

  private ParamEntity getParamEntityWithAuthKey(boolean alwaysNewKey) {
    ParamEntity paramEntity = getParamEntity()
    paramEntity.setKey(getAuthKey(alwaysNewKey))
    return paramEntity
  }

  private String getAuthKey(boolean alwaysNewKey) {
    String fullMethodName = getFullMethodName("getAuthKey")
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    String authKey = getAuthKeyFromRedis(false) // 从redis中获取当前密钥
    LoggerUtil.info("AuthKey value is {0}", authKey)
    if (StringUtils.isNotEmpty(authKey)) {
      if (alwaysNewKey) {
        // 查看authKey有效期（最长有效期为90天）
        long expiration = getAuthKeyExpiration()
        Date now = new Date()
        // 密钥临近截止期，更新密钥
        if (DateUtil.getDaysBetween(now, DateUtil.getSpecifiedDateAfterSeconds(now, (int) expiration)) <= 18) {
          // 更新密钥
          String newAuthKey = updateAuthKey()
          // 密钥更新成功时返回新密钥，否则继续使用当前密钥
          return StringUtils.isNotBlank(newAuthKey) ? newAuthKey : authKey
        }
      }

      // 密钥在有效期内，直接返回
      return authKey
    }

    // 缓存中不存在密钥，下载初始密钥
    LoggerUtil.info("{0} is downloading authKey.", fullMethodName)
    String resultJSONStr = getAuthService().getAuthKey(channelAccessConfig.getThirdPartyPublicKey(),
        channelAccessConfig.getAccessKey(),
        channelAccessConfig.getMerchantId(),
        channelAccessConfig.getTerminalId())
    LoggerUtil.info("{0} receives downloading response: {1}", fullMethodName, resultJSONStr)
    JSONObject resultJSON = JSON.parseObject(resultJSONStr)
    String errorCode = resultJSON.getString("retCode")
    String errorMessage = resultJSON.getString("retMsg")
    if (errorCode != SUCCESS_CODE) {
      // updateKey失败
      LoggerUtil.error("{0} fail to download authKey, code: {1}, message: {2}.", null, fullMethodName, errorCode, errorMessage)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }

    // 解密密钥
    authKey = decryptKey(resultJSON.getJSONObject("transData").getString("authKey"))

    // 缓存并返回密钥
    setAuthKeyIntoRedis(authKey, false)
    return authKey
  }

  private String updateAuthKey() {
    String fullMethodName = getFullMethodName("updateAuthKey")

    // 更新密钥
    LoggerUtil.info("{0} is updating authKey.", fullMethodName)
    String updateKeyResultJSONStr = getAuthorKeyService().updateKey(channel.getChannelAccessConfig().getThirdPartyPublicKey())
    LoggerUtil.info("{0} receives authKey update response: {1}.", fullMethodName, updateKeyResultJSONStr)
    JSONObject updateKeyResultJSON = JSON.parseObject(updateKeyResultJSONStr)
    String updateKeyErrorCode = updateKeyResultJSON.getString("retCode")
    String updateKeyErrorMessage = updateKeyResultJSON.getString("retMsg")
    if (updateKeyErrorCode != SUCCESS_CODE) {
      // updateKey失败
      LoggerUtil.error("{0} fail to update authKey, code: {1}, message: {2}.", null, fullMethodName, updateKeyErrorCode,
          updateKeyErrorMessage)
      return null
    }
    // 解密密钥
    String authKey = decryptKey(updateKeyResultJSON.getJSONObject("transData").getString("authKey"))
    setAuthKeyIntoRedis(authKey, true) // 设置待确认的密钥到缓存（不设有效期）

    // 密钥更新确认
    boolean confirmResult = confirmAuthKey(authKey)
    removeBackupAuthKey() // 得到明确响应后，删除备份密钥缓存

    // 返回新密钥
    return confirmResult ? authKey : null
  }

  private boolean confirmAuthKey(String newAuthKey) {
    String fullMethodName = getFullMethodName("confirmAuthKey")
    LoggerUtil.info("{0} is confirming update result, authKey {1}.", fullMethodName, newAuthKey)
    String confirmResultJSONStr = getAuthorKeyService().sureKey(channel.getChannelAccessConfig().getThirdPartyPublicKey(), newAuthKey)
    LoggerUtil.info("{0} receives authKey confirming response: {1}.", fullMethodName, confirmResultJSONStr)
    JSONObject confirmResultJSON = JSON.parseObject(confirmResultJSONStr)
    String confirmKeyErrorCode = confirmResultJSON.getString("retCode")
    String confirmKeyErrorMessage = confirmResultJSON.getString("retMsg")
    if (confirmKeyErrorCode != SUCCESS_CODE) {
      // sureKey失败
      LoggerUtil.error("{0} fail to confirm update result, code: {1}, message: {2}.", null, fullMethodName, confirmKeyErrorCode,
          confirmKeyErrorMessage)
      return false
    }

    // 新密钥确认成功，将新密钥设置到缓存中（有效期90天）
    setAuthKeyIntoRedis(newAuthKey, false)
    return true
  }

  private String decryptKey(String encryptedKey) {
    if (StringUtils.isBlank(encryptedKey)) {
      return null
    }
    return RSAUtil.decryptByPrivateKey(encryptedKey, channel.getChannelAccessConfig().getPrivateKey())
  }

  private static BigDecimal parseYuan(BigDecimal hexCloudAmount) {
    return hexCloudAmount / 100
  }

  private static BigDecimal parseFen(BigDecimal tpAmount) {
    return tpAmount * 100
  }

  private static PayMethod parsePayMethod(String tpPayMethod) {
    PayMethod payMethod
    switch (tpPayMethod) {
      case "3":
        payMethod = PayMethod.WX_PAY
        break
      case "2":
        payMethod = PayMethod.ALI_PAY
        break
      case "5":
        payMethod = PayMethod.UNION_PAY
        break
      case "6":
        payMethod = PayMethod.CDBC
        break
      case "4":
        payMethod = PayMethod.CCB
        break
      default:
        payMethod = PayMethod.OTHERS
    }
    return payMethod
  }

  private static TransactionState parseTransactionState(String tpTransactionState) {
    TransactionState transactionState
    switch (tpTransactionState) {
      case "00": // 成功
        transactionState = TransactionState.SUCCESS
        break
      case "01": // 失败
        transactionState = TransactionState.FAILED
        break
      case "02": // 不确定，继续轮询、等待客户输密
        transactionState = TransactionState.WAITING
        break
      case "04": // 已撤销
        transactionState = TransactionState.CANCELED
        break
      case "05": // 交易已关闭或已全额退货
        transactionState = TransactionState.REFUNDED
        break
      case "03": // 退货中
      case "06": // 交易不确定，单前端无需冲正
      case "07": // 交易超时
        transactionState = TransactionState.PENDING
        break
      default:
        transactionState = TransactionState.UNKNOWN
    }
    return transactionState
  }

  private String getAuthKeyRedisKey(boolean backup) {
    String keyPrefix
    if (backup) {
      keyPrefix = "TBIS-ACCESS-TOKEN-BAK:"
    } else {
      keyPrefix = "TBIS-ACCESS-TOKEN:"
    }

    String key = keyPrefix + channel.getChannelCode() + ":" + ServiceContext.getString(ContextKeyConstant.PARTNER_ID) + ":" + ServiceContext.getString(ContextKeyConstant.STORE_ID)

    LoggerUtil.info("getAuthKey RedisKey is {0}", key)

    return key
  }

  private String getAuthKeyFromRedis(boolean backup) {
    return RedisUtil.StringOps.get(getAuthKeyRedisKey(backup))
  }

  private long getAuthKeyExpiration() {
    return RedisUtil.KeyOps.getExpire(getAuthKeyRedisKey(false))
  }

  private void setAuthKeyIntoRedis(String authKey, boolean backup) {
    if (backup) {
      // 备份未成功确认的密钥，有效期无限
      RedisUtil.StringOps.set(getAuthKeyRedisKey(true), authKey)
    } else {
      // 已确认密钥，有效期90天
      RedisUtil.StringOps.setEx(getAuthKeyRedisKey(false), authKey, 90, TimeUnit.DAYS)
    }
  }

  private void removeBackupAuthKey() {
    RedisUtil.KeyOps.delete(getAuthKeyRedisKey(true))
  }

  private void handleInvalidKeyException() {
    LoggerUtil.info("Handling the invalid key exception.")

    // 获取备份密钥
    String backupAuthKey = getAuthKeyFromRedis(true)
    if (StringUtils.isBlank(backupAuthKey)) {
      // 备份密钥不存在，需人工介入更新密钥
      LoggerUtil.info("Can't do anything, the backup auth key doesn't exist.")
    } else {
      // 存在备份密钥，尝试重新确认密钥
      LoggerUtil.info("Backup auth key exists, try to reconfirm it.")
      boolean confirmResult = confirmAuthKey(backupAuthKey)
      if (!confirmResult) {
        LoggerUtil.warn("Failed to reconfirm the backup auth key.")
      } else {
        removeBackupAuthKey() // 确认成功后，删除备份密钥缓存
        LoggerUtil.info("Backup auth key has been successfully reconfirmed.")
        LoggerUtil.info("The invalid key exception has been fixed.")
      }
    }
  }

}
