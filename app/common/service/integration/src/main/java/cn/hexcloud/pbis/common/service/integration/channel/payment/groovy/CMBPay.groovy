package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import cn.hexcloud.pbis.common.util.sm2.SM2Util
import cn.hutool.core.date.DatePattern
import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.commons.lang3.StringUtils
import org.springframework.util.DigestUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp

/**
 * @program: pbis
 * @author: miao
 * @create: 2023-05-15 14:49
 * */
class CMBPay extends AbstractExternalChannelModule implements PaymentModule {

  private static final String SUCCESS_CODE = "SUCCESS"
  private static final String TRADE_FAILED = "F"

  CMBPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    // 请求参数
    Map<String, String> bizParams = new HashMap<>()
    bizParams.put("merId", channel.getChannelAccessConfig().getMerchantId());   //商户号(必传)
    bizParams.put("orderId", request.getTransactionId()); //商户订单号(必传)
    bizParams.put("authCode", request.getPayCode());  // 授权码
    bizParams.put("userId", channel.getChannelAccessConfig().getAccessKey());   //收银员
    bizParams.put("termId", channel.getChannelAccessConfig().getTerminalId());  //终端号
    bizParams.put("txnAmt", request.getAmount().toString());  //交易金额,单位为分(必传)
    bizParams.put("currencyCode", "156");    //交易币种，默认156，目前只支持人民币（156）
    bizParams.put("notifyUrl", getNotificationUrl());   //商户号
    bizParams.put("tradeScene", "OFFLINE");   //交易场景
    // 数字人民币入参
    ObjectMapper mapper = new ObjectMapper();
    if (isE_chy(request.getPayCode())) {
      Map<String, String> ecny = new TreeMap<>()
      ecny.put("transactionType", "TT01")
      ecny.put("terminalNo", channel.getChannelAccessConfig().getTerminalId())
      ecny.put("terminalIp", request.getRemoteIp())
      // 招行限制商品名称不能包含特殊字符串，全渠道商品创建商品时未对特殊字符做校验
      ecny.put("goodsName", "Product")
      String utcTime = cn.hutool.core.date.DateUtil.offsetDay(request.getTransactionTime(), 14).toString(DatePattern.UTC_SIMPLE_PATTERN)
      ecny.put("orderTimeExpire", utcTime)
      ecny.put("tradePlace", ServiceContext.getString(ContextKeyConstant.STORE_ADDRESS))
      bizParams.put("ecnyPayment", mapper.writeValueAsString(ecny))
    }
    // 设备信息
    Map<String, String> terminalInfo = new TreeMap<>()
    terminalInfo.put("device_type", "11")
    terminalInfo.put("device_id", channel.getChannelAccessConfig().getTerminalId())
    Map<String, Object> rst = SM2Util.encrypt(channel.getChannelAccessConfig().getThirdPartyPublicKey(), mapper.writeValueAsString(terminalInfo))
    String[] encContents = (String[]) rst.get("encContents");
    bizParams.put("terminalInfo", encContents[0])
    // 数字信封
    String digEvp = (String) rst.get("digEvp")
    bizParams.put("encryptKey", digEvp)
    // 发起请求
    JSONObject resultJSON = doRequest("pay", bizParams, true)
    // 支付状态
    if (TRADE_FAILED == resultJSON.getString("tradeState")) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON.getString("errDescription"))
    }
    // 解析并返回结果
    ChannelPayResponse response = new ChannelPayResponse()
    response.setTransactionState(mapTransactionState(resultJSON.getString("tradeState")))
    response.setChannel(request.getChannel())
    response.setPayMethod(mapPayMethod(resultJSON.getString("payType")))
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(resultJSON.getString("cmbOrderId"))
    return response
  }

  private static boolean isE_chy(String payCode) {
    if (StringUtils.isBlank(payCode)) {
      throw new CommonException(ServiceError.INVALID_PAY_CODE)
    }
    String prefix = StrUtil.sub(payCode, 0, 2)
    return prefix == "01"
  }

  private static PayMethod mapPayMethod(String tpPayMethod) {
    PayMethod payMethod
    switch (tpPayMethod) {
      case "WX":
        payMethod = PayMethod.WX_PAY
        break
      case "ZF":
        payMethod = PayMethod.ALI_PAY
        break
      case "YL":
        payMethod = PayMethod.UNION_PAY
        break
      case "EC":
        payMethod = PayMethod.CDBC
        break
      default:
        payMethod = PayMethod.OTHERS
    }
    return payMethod
  }

  private static TransactionState mapCancelState(String tpCancelState) {
    TransactionState transactionState
    switch (tpCancelState) {
      case "F":
        transactionState = TransactionState.FAILED
        break
      case "D":
        transactionState = TransactionState.SUCCESS
        break
      default:
        transactionState = TransactionState.UNKNOWN
    }
    return transactionState
  }

  private static TransactionState mapTransactionState(String state) {
    TransactionState transactionState
    switch (state) {
      case "F":
        transactionState = TransactionState.FAILED
        break
      case "S":
        transactionState = TransactionState.SUCCESS
        break
      case "P":
        transactionState = TransactionState.PENDING
        break
      case "C":
        transactionState = TransactionState.CLOSED
        break
      case "D":
        transactionState = TransactionState.CANCELED
        break
      case "R":
        transactionState = TransactionState.SUCCESS
        break
      default:
        transactionState = TransactionState.UNKNOWN
    }
    return transactionState
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    // 请求参数
    Map<String, String> bizParams = new HashMap<>()
    bizParams.put("merId", channel.getChannelAccessConfig().getMerchantId());   //商户号(必传)
    bizParams.put("orderId", request.getTransactionId()); //商户订单号(必传)
    bizParams.put("cmbOrderId", request.getTpTransactionId()); //商户订单号(必传)
    bizParams.put("userId", channel.getChannelAccessConfig().getAccessKey());   //收银员
    // 发起请求
    JSONObject resultJSON = doRequest("query", bizParams, false)
    // 解析并返回结果
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setTransactionState(mapTransactionState(resultJSON.getString("tradeState")))
    response.setChannel(request.getChannel())
    response.setWarningMessage(resultJSON.getString("errDescription"))
    response.setPayMethod(mapPayMethod(resultJSON.getString("payType")))
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(resultJSON.getString("cmbOrderId"))
    return response
  }


  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, String> bizParams = new HashMap<>()
    bizParams.put("merId", channel.getChannelAccessConfig().getMerchantId());   //商户号(必传)
    bizParams.put("orderId", request.getTransactionId()); //商户订单号(必传)
    bizParams.put("userId", channel.getChannelAccessConfig().getAccessKey());   //收银员
    //原交易招行订单号，此字段和原交易商户订单号字段至少要上送一个，若两个都上送，则以此字段为准
    bizParams.put("origCmbOrderId", request.getRelatedTPTransactionId());
    bizParams.put("txnAmt", request.getAmount().toString());  //交易金额,单位为分(必传)
    bizParams.put("refundAmt", request.getAmount().toString()) //退款金额
    bizParams.put("currencyCode", "156");    //交易币种，默认156，目前只支持人民币（156）
    // 发起请求
    JSONObject resultJSON = doRequest("refund", bizParams, true)
    // 支付状态
    if (TRADE_FAILED == resultJSON.getString("tradeState")) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON.getString("errDescription"))
    }
    // 解析并返回结果
    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionState(mapTransactionState(resultJSON.getString("refundState")))
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(resultJSON.getString("cmbOrderId"))
    // 查询退款结果
    //queryRefund(request.getTransactionId(), resultJSON.getString("cmbOrderId"))
    return response
  }

  private TransactionState queryRefund(String transactionId, String tpTransactionId) {
    // 请求参数
    Map<String, String> bizParams = new HashMap<>()
    bizParams.put("merId", channel.getChannelAccessConfig().getMerchantId());   //商户号(必传)
    bizParams.put("orderId", transactionId); //商户订单号(必传)
    bizParams.put("cmbOrderId", tpTransactionId) //商户订单号(必传)
    bizParams.put("userId", channel.getChannelAccessConfig().getAccessKey());   //收银员
    // 发起请求
    JSONObject resultJSON = doRequest("query_refund", bizParams, false)
    return mapTransactionState(resultJSON.getString("tradeState"))
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    // 请求参数
    Map<String, String> bizParams = new HashMap<>()
    bizParams.put("merId", channel.getChannelAccessConfig().getMerchantId());   //商户号(必传)
    bizParams.put("userId", channel.getChannelAccessConfig().getAccessKey());   //收银员
    //原交易招行订单号，此字段和原交易商户订单号字段至少要上送一个，若两个都上送，则以此字段为准
    bizParams.put("origCmbOrderId", request.getRelatedTPTransactionId());
    // 发起请求
    JSONObject resultJSON = doRequest("cancel", bizParams, true)
    String returnCode = resultJSON.getString("returnCode")
    String respCode = resultJSON.getString("respCode")
    String errorMessage = resultJSON.getString("respMsg")
    if (SUCCESS_CODE != returnCode || SUCCESS_CODE != respCode) {
      // 取消特殊处理
      if ("FAIL" == returnCode) {
        String errCode = resultJSON.getString("errCode")
        // 未付款的订单
        if ("CANCEL_NOT_ALLOWED" == errCode) {
          // 解析并返回结果
          ChannelCancelResponse response = new ChannelCancelResponse()
          response.setTransactionState(TransactionState.SUCCESS)
          response.setWarningMessage(errorMessage)
          return response
        }
      }
      // 请求失败
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, getMethodFullName("cancel"), returnCode, errorMessage)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }

    // 支付状态
    if (TRADE_FAILED == resultJSON.getString("tradeState")) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON.getString("errDescription"))
    }
    // 解析并返回结果
    ChannelCancelResponse response = new ChannelCancelResponse()
    response.setTransactionState(mapCancelState(resultJSON.getString("cancelState")))
    return response
  }

  @Override
  ChannelNotificationResponse payNotify(HttpServletRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("payNotify"))
  }

  private static String getSignContent(Map<String, String> sortedParams) {
    try {
      List<String> keyList = new ArrayList<>(sortedParams.keySet());
      Collections.sort(keyList);
      boolean stared = false;
      StringBuilder signParams = new StringBuilder();
      for (String key : keyList) {
        if (stared) {
          signParams.append("&")
        }
        stared = true
        signParams.append(key).append("=").append(sortedParams.get(key))
      }
      return signParams.toString();
    } catch (Exception e) {
      throw e;
    }
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    // 加工数据并得到签名原文
    ObjectMapper mapper = new ObjectMapper()
    Map<String, String> requestPublicParams = new TreeMap<>();
    //公共请求参数
    requestPublicParams.put("version", "0.0.1");    //版本号，固定为0.0.1(必传字段)
    requestPublicParams.put("encoding", "UTF-8");   //编码方式，固定为UTF-8(必传)
    requestPublicParams.put("signMethod", "02");    //签名方法，固定为02，表示签名方式为国密(必传)
    if (rawMessage.containsKey("encryptKey")) {
      requestPublicParams.put("encryptKey", rawMessage.get("encryptKey"))
      rawMessage.remove("encryptKey")
    }
    requestPublicParams.put("biz_content", mapper.writeValueAsString(rawMessage))
    //对待加签内容进行排序拼接
    String signStr = getSignContent(requestPublicParams)
    String sign = SM2Util.sm2Sign(signStr, channel.getChannelAccessConfig().getPrivateKey())
    requestPublicParams.put("sign", sign)
    return mapper.writeValueAsString(requestPublicParams)
  }

  private JSONObject doRequest(String method, Map<String, String> bizParams, boolean reserveData) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    // 加密内容
    String signJsonResult = getSignature(bizParams)
    JSONObject jsonObject = JSON.parseObject(signJsonResult)

    long timeStamp = (long) (System.currentTimeMillis() / 1000);
    Map<String, String> apiSign = new TreeMap<>();
    apiSign.put("appid", channel.getChannelAccessConfig().getAppId());
    apiSign.put("secret", channel.getChannelAccessConfig().getAppKey());
    apiSign.put("sign", jsonObject.getString("sign"));
    apiSign.put("timestamp", "" + timeStamp);

    String MD5Content = getSignContent(apiSign);
    String apiSignString = DigestUtils.md5DigestAsHex(MD5Content.getBytes(StandardCharsets.UTF_8));

    // 组request头部Map
    Map<String, String> apiHeader = new HashMap<>();
    apiHeader.put("appid", channel.getChannelAccessConfig().getAppId());
    apiHeader.put("timestamp", "" + timeStamp);
    apiHeader.put("apisign", apiSignString);

    // 发起HTTP请求
    String methodFullName = getMethodFullName(method)
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, signJsonResult)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result = HttpUtil.doPost(channelAccessConfig.getProperty(method + "_url"), signJsonResult, apiHeader)
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)
    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, signJsonResult)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }
    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
    String returnCode = resultJSON.getString("returnCode")
    String respCode = resultJSON.getString("respCode")
    String errorMessage = resultJSON.getString("respMsg")
    if (SUCCESS_CODE != returnCode || SUCCESS_CODE != respCode) {
      // 取消特殊处理
      if (method == "cancel") {
        return resultJSON
      }
      // 请求失败
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, methodFullName, returnCode, errorMessage)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }
    // 验签
    Boolean checkResult1 = checkSign(resultJSONStr, channel.getChannelAccessConfig().getThirdPartyPublicKey());
//    if (!checkResult1) {
//      throw new CommonException(ServiceError.INVALID_SIGNATURE)
//    }
    // biz_content 内容，解析实际的支付结果
    JSONObject bizContent = JSON.parseObject(resultJSON.getString("biz_content"))
    // 如果支付失败，获取支付失败的信息
    bizContent.put("errDescription", resultJSON.getString("errDescription"))
    bizContent.put("returnCode", resultJSON.getString("returnCode"))
    bizContent.put("respCode", resultJSON.getString("respCode"))

    return bizContent
  }

  private static Boolean checkSign(String string, String publicKey) {
    System.out.println("要验签的报文内容：" + string);
    try {
      //验签
      ObjectMapper objectMapper = new ObjectMapper();
      Map<String, String> responseBodyMap = objectMapper.readValue(string, Map.class);
      String sign = responseBodyMap.remove("sign")
      String contentStr = getSignContent(responseBodyMap);
      boolean result = SM2Util.sm2Check(contentStr, sign, publicKey);

      if (result) {
        System.out.println("报文验签成功!");
      } else {
        System.out.println("报文验签失败!");
      }
      return result;
    } catch (Exception e) {
      System.out.println("验签发生异常！");
      e.printStackTrace();
      return false;
    }
  }

  private String getNotificationUrl() {
    String notificationUrl = channel.getChannelAccessConfig().getProperty("notification_url")
    if (StringUtils.isNotBlank(notificationUrl)) {
      String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
      String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
      String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId
      return notificationUrl.endsWith("/") ? notificationUrl + path : notificationUrl + "/" + path
    }
    return null
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }

}
