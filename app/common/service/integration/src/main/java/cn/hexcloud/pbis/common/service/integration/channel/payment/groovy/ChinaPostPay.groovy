package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateTime
import cn.hutool.core.util.RandomUtil
import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONException
import com.alibaba.fastjson.JSONObject
import com.baomidou.mybatisplus.core.toolkit.EncryptUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp

/**
 * @program: pbis
 * @author: miao
 * @create: 2023-05-09 15:36
 * */
class ChinaPostPay extends AbstractExternalChannelModule implements PaymentModule {

  private static final String DST_SYS_ID = "XYDYYQDXT"
  private static final String SUCCESS_CODE = "0"
  private static final String V_RESULT = "F0"
  private static final Map<String, String> METHOD_MAP

  static {
    METHOD_MAP = new HashMap<>()
    METHOD_MAP.put("getThirdOrderNo", "YY1062")
    METHOD_MAP.put("pay", "YY3066")
    METHOD_MAP.put("refund", "YY3067")
    METHOD_MAP.put("queryRefundResult", "YY3068")
    METHOD_MAP.put("query", "YY3069")
  }

  ChinaPostPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }


  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    return null;
  }

  /**
   * 2.5.1.2.获取第三方支付或退款的流水号服务
   * @return
   */
  private String getThirdOrderNo() {
    String orderNo = ""
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("bz", "1")
    JSONObject obj = doRequest("getThirdOrderNo", bizParams, true)
    JSONArray resultArray
    try {
      resultArray = obj.getJSONObject("SessionBody").getJSONArray("result")
    } catch (Exception ex) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "第三方接口未知异常，请联系运维")
    }
    if (resultArray.size() > 0) {
      JSONObject resultNode = resultArray.getJSONObject(0)
      if (resultNode.getString("code") != "100") {
        throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultNode.getString("V_REMARK"))
      }
      orderNo = resultNode.getString("zflsh")
    }
    return orderNo
  }

  @Override
  ChannelNotificationResponse payNotify(HttpServletRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("payNotify"))
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new TreeMap<>()
    bizParams.put("vCxlsh", request.getTransactionId())// 查询流水号
    bizParams.put("vZflsh", getThirdOrderNo())// 支付流水号
    bizParams.put("smxx", request.getPayCode())// 付款码
    bizParams.put("vShbm", channel.getChannelAccessConfig().getMerchantId())// 商户号
    bizParams.put("fZfje", request.getAmount() / 100)// 支付金额 支持到两位小数
    // 渠道交易日期时间 格式化：yyyy-MM-dd HH:mm:ss
    bizParams.put("time", DateUtil.formatDate(request.getTransactionTime(), 'yyyy-MM-dd HH:mm:ss'))
    // 发起请求
    JSONObject obj = doRequest("pay", bizParams, true)
    JSONArray resultArray
    try {
      resultArray = obj.getJSONObject("SessionBody").getJSONArray("result")
    } catch (Exception ex) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "第三方接口未知异常，请联系运维")
    }
    ChannelPayResponse response = new ChannelPayResponse()
    response.setTransactionId(request.getTransactionId())
    response.setChannel(request.getChannel())
    response.setRealAmount(request.getAmount())
    // extend
    Map<String, Object> objectMap = new HashMap<String, Object>()
    objectMap.put("transactionTime", DateUtil.formatDate(request.getTransactionTime(), DatePattern.UTC_SIMPLE_PATTERN))
    response.setExtendedParams(JSON.toJSONString(objectMap))
    response.setTransactionState(TransactionState.PENDING)
    if (resultArray.size() > 0) {
      JSONObject resultNode = resultArray.getJSONObject(0)
      if (resultNode.getString("V_RESULT") != V_RESULT) {
        throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultNode.getString("V_REMARK"))
      }
      response.setTpTransactionId(resultNode.getString("V_LSH"))
      response.setTransactionState(mapTransactionState(resultNode.getString("C_ZFZT")))
      if (TransactionState.FAILED == response.getTransactionState()) {
        throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultNode.getString("V_REMARK"))
      }
      response.setWarningMessage(resultNode.getString("V_REMARK"))
      response.setPayMethod(mapPayMethod(resultNode.getString("V_ZFFS")))
    }
    // 交易时间
    return response
  }

  /**
   * 查询退款状态
   * @param refundTime
   * @param refundId
   * @return
   */
  private TransactionState queryRefundResult(String orderNo, String refundId) {
    String refundStatus = TransactionState.SUCCESS
    // 请求参数
    Map<String, Object> bizParams = new TreeMap<>()
    bizParams.put("vCxlsh", orderNo)
    bizParams.put("dTkrq", DateUtil.getDate(DatePattern.NORM_DATETIME_PATTERN))// 退款时间
    bizParams.put("vTklsh", refundId)// 退款流水号
    // 发起请求
    JSONObject obj = doRequest("queryRefundResult", bizParams, true)
    JSONArray resultArray
    try {
      resultArray = obj.getJSONObject("SessionBody").getJSONArray("result")
    } catch (Exception ex) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "第三方接口未知异常，请联系运维")
    }
    if (resultArray.size() > 0) {
      JSONObject resultNode = resultArray.getJSONObject(0)
      if (resultNode.getString("V_RESULT") != V_RESULT) {
        throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultNode.getString("V_REMARK"))
      }
      refundStatus = mapRefundTransactionState(resultNode.getString("C_TKZT"))
    }
    return refundStatus
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new TreeMap<>()
    bizParams.put("vCxlsh", request.getTransactionId())// 查询流水号
    //// 原渠道交易日期时间 格式化：yyyy-MM-dd HH:mm:ss
    bizParams.put("dZfrq", DateUtil.getDate(DatePattern.NORM_DATETIME_PATTERN))
    bizParams.put("vZflsh", request.getTpTransactionId())// 交易流水号
    // 发起请求
    JSONObject obj = doRequest("query", bizParams, false)
    JSONArray resultArray
    try {
      resultArray = obj.getJSONObject("SessionBody").getJSONArray("result")
    } catch (Exception ex) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "第三方接口未知异常，请联系运维")
    }
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setTransactionId(request.getTransactionId())
    response.setChannel(request.getChannel())
    response.setTransactionState(TransactionState.PENDING)
    if (resultArray.size() > 0) {
      JSONObject resultNode = resultArray.getJSONObject(0)
      if (resultNode.getString("V_RESULT") != V_RESULT) {
        throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultNode.getString("V_REMARK"))
      }
      response.setWarningMessage(resultNode.getString("V_REMARK"))
      response.setTpTransactionId(request.getTpTransactionId())
      response.setTransactionState(mapTransactionState(resultNode.getString("C_ZFZT")))
    }
    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new TreeMap<>()
    bizParams.put("vCxlsh", request.getTransactionId())// 查询流水号
    bizParams.put("dQdjyrq", DateUtil.getDate(DatePattern.NORM_DATETIME_PATTERN))// 支付流水号
    bizParams.put("vTklsh", getThirdOrderNo())// 退款流水
    bizParams.put("vShbm", channel.getChannelAccessConfig().getMerchantId())// 商户号
    bizParams.put("fTkje", request.getAmount() / 100)// 退款金额
    bizParams.put("vZflsh", request.getRelatedTPTransactionId())// 原支付流水号
    //// 渠道交易日期时间 格式化：yyyy-MM-dd HH:mm:ss
    bizParams.put("dZfrq", DateUtil.formatDate(request.getTransactionTime(), 'yyyy-MM-dd HH:mm:ss'))
    // 发起请求
    JSONObject obj = doRequest("refund", bizParams, true)
    JSONArray resultArray
    try {
      resultArray = obj.getJSONObject("SessionBody").getJSONArray("result")
    } catch (Exception ex) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "第三方接口未知异常，请联系运维")
    }
    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionId(request.getTransactionId())
    response.setRealAmount(request.getAmount())
    response.setTransactionState(TransactionState.PENDING)
    if (resultArray.size() > 0) {
      JSONObject resultNode = resultArray.getJSONObject(0)
      if (resultNode.getString("V_RESULT") != V_RESULT) {
        throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultNode.getString("V_REMARK"))
      }
      response.setTpTransactionId(resultNode.getString("V_TKLSH"))
      response.setWarningMessage(resultNode.getString("V_REMARK"))
      response.setTransactionState(mapRefundTransactionState(resultNode.getString("C_TKZT")))
    }
    // 退款状态查询
//    TransactionState refundState = queryRefundResult(request.getTransactionId(),request.getRelatedTPTransactionId())
//    response.setTransactionState(refundState)
    return response
  }

  private static TransactionState mapRefundTransactionState(String tpTransactionState) {
    TransactionState transactionState
    switch (tpTransactionState) {
      case "01":
        transactionState = TransactionState.SUCCESS
        break
      case "02":
        transactionState = TransactionState.PENDING
        break
      case "00":
        transactionState = TransactionState.FAILED
        break
      default:
        transactionState = TransactionState.UNKNOWN
    }
    return transactionState
  }

  private static TransactionState mapTransactionState(String tpTransactionState) {
    TransactionState transactionState
    switch (tpTransactionState) {
      case "01":
        transactionState = TransactionState.SUCCESS
        break
      case "03":
        // 已退款
        transactionState = TransactionState.SUCCESS
        break
      case "05":
        // 部分退款
        transactionState = TransactionState.SUCCESS
        break
      case "02":
        transactionState = TransactionState.FAILED
        break
      case "00":
        transactionState = TransactionState.WAITING
        break
      default:
        transactionState = TransactionState.UNKNOWN
    }
    return transactionState
  }

  private static PayMethod mapPayMethod(String method) {
    PayMethod payMethod
    switch (method) {
      case "1":
        payMethod = PayMethod.ALI_PAY
        break
      case "2":
        payMethod = PayMethod.WX_PAY
        break
      case "3":
        payMethod = PayMethod.CDBC
        break
      case "9":
        payMethod = PayMethod.UNION_PAY
        break
      default:
        payMethod = PayMethod.OTHERS
    }
    return payMethod
  }


  @Override
  String getSignature(Map<String, String> rawMessage) {
    // 加工数据并得到签名原文
    StringBuilder sb = new StringBuilder()
    for (Map.Entry<String, String> entry : rawMessage) {
      sb.append(entry.getValue())
    }
    String dataBeforeSign = sb.append(channel.getChannelAccessConfig().getAppKey())
    // 签名并返回签名信息
    return EncryptUtils.md5Base64(dataBeforeSign.toString())
  }

  /**
   * 获取流水号
   * @return
   */
  private static getTransactionId() {
    return "000003" + DateTime.now().toString(DatePattern.PURE_DATETIME_MS_PATTERN) + RandomUtil.randomNumbers(10)
  }

  /**
   * 请求第三方
   * @param method
   * @param bizParams
   * @param reserveData
   * @return
   */
  private JSONObject doRequest(String method, Map<String, Object> bizParams, boolean reserveData) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    if (StrUtil.isBlank(channelAccessConfig.getGatewayUrl())) {
      throw new CommonException(ServiceError.INVALID_CHANNEL_ACCESS_CONFIG)
    }

    String bizParamsJSON = JSON.toJSONString(bizParams)

    // 请求头信息
    Map<String, String> bodyHeader = new LinkedHashMap<>()
    bodyHeader.put("ServiceCode", METHOD_MAP.get(method))
    bodyHeader.put("Version", "YY-1.0")
    bodyHeader.put("ActionCode", "0")
    bodyHeader.put("TransactionID", getTransactionId())
    bodyHeader.put("SrcSysID", channelAccessConfig.getAppId())
    bodyHeader.put("DstSysID", DST_SYS_ID)
    bodyHeader.put("ReqTime", DateUtil.getDate("yyyyMMddHHmmss"))

    // 签名
    Map<String, String> rawMessage = new LinkedHashMap<>(bodyHeader)
    rawMessage.put("bz", bizParamsJSON)
    bodyHeader.put("DigitalSign", getSignature(rawMessage))

    // 构造请求信息
    Map<String, Object> rootBody = new HashMap<>()
    rootBody.put("SessionHeader", bodyHeader)
    rootBody.put("SessionBody", bizParamsJSON)

    Map<String, Map> params = new HashMap<>()
    params.put("YYRoot", rootBody)

    // 发起HTTP请求
    String requestUrl = channelAccessConfig.getGatewayUrl()
    String methodFullName = getFullMethodName(method)
    String requestBody = JSON.toJSONString(params)
    LoggerUtil.info("{0} is sending message to: {1}, body: {2}", methodFullName, requestUrl, requestBody)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result = HttpUtil.doPost(channelAccessConfig.getGatewayUrl(), requestBody)
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, requestBody)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }
    // 解析并返回结果
    JSONObject resultJSON
    try {
      resultJSON = JSONObject.parseObject(resultJSONStr)
    } catch (JSONException je) {
      LoggerUtil.error("解析json异常", je)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "PostCoffee响应报文解析异常，请联系运维")
    }
    JSONObject responseNode = resultJSON.getJSONObject("SessionHeader").getJSONObject("Response")
    String rspType = responseNode.getString("RspType")
    if (rspType != SUCCESS_CODE) {
      // 请求失败
      String errorMessage = responseNode.getString("RspDesc")
      LoggerUtil.error("{0} is failed, message: {1}.", null, methodFullName, errorMessage)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }

    return resultJSON
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }
}
