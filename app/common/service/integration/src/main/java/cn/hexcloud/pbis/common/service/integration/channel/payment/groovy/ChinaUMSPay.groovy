package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.commons.utils.UUIDUtil
import cn.hexcloud.commons.utils.cipher.SHAUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp

class ChinaUMSPay extends AbstractExternalChannelModule implements PaymentModule {

  private static final String SUCCESS_CODE = "00"

  ChinaUMSPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("create"))
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("merchantOrderId", request.getTransactionId())
    bizParams.put("payCode", request.getPayCode())
    bizParams.put("payMode", "CODE_SCAN")
    bizParams.put("transactionAmount", request.getAmount())
    bizParams.put("transactionCurrencyCode", "156")
    bizParams.put("merchantRemark", request.getOrderDescription())
    bizParams.put("storeId", ServiceContext.getString(ContextKeyConstant.STORE_CODE))
    bizParams.put("deviceType", "11")
    bizParams.put("ip", request.getRemoteIp())
    // 发起请求
    JSONObject resultJSON = doRequest("pay", bizParams, true)

    // 解析并返回结果
    ChannelPayResponse response = new ChannelPayResponse()
    response.setTransactionState(TransactionState.SUCCESS)
    response.setChannel(request.getChannel())
//    response.setPayMethod(PayMethod.CDBC)
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(resultJSON.getString("orderId"))
    response.setRealAmount(resultJSON.getBigDecimal("amount"))
    response.setExtendedParams(request.getExtendedParams())
    return response
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("merchantOrderId", request.getTransactionId())

    // 发起请求
    JSONObject resultJSON = doRequest("query", bizParams, false)

    // 解析并返回结果
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setChannel(request.getChannel())
//    response.setPayMethod(PayMethod.CDBC)
    response.setTransactionId(resultJSON.getString("merchantOrderId"))
    response.setTpTransactionId(resultJSON.getString("orderId"))
    response.setRealAmount(resultJSON.getBigDecimal("amount"))
    response.setTransactionState(mapTransactionState(resultJSON.getString("queryResCode")))
    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new TreeMap<>()
    bizParams.put("merchantOrderId", request.getRelatedTransactionId())
    bizParams.put("refundRequestId", request.getTransactionId())
    bizParams.put("transactionAmount", request.getAmount())

    // 发起请求
    JSONObject resultJSON = doRequest("refund", bizParams, true)

    // 解析并返回结果
    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionState(TransactionState.PENDING)
    response.setTransactionId(request.getTransactionId())
    response.setRealAmount(resultJSON.getBigDecimal("refundInvoiceAmount"))
    return response
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("merchantOrderId", request.getRelatedTransactionId())

    // 发起请求
    JSONObject resultJSON = doRequest("cancel", bizParams, true)

    // 解析并返回结果
    ChannelCancelResponse response = new ChannelCancelResponse()
    response.setRealAmount(resultJSON.getBigDecimal("actualTransactionAmount"))
    response.setTransactionState(TransactionState.SUCCESS)
    return response
  }

  @Override
  ChannelNotificationResponse payNotify(HttpServletRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("payNotify"))
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    // 加工数据并得到签名原文
    String appId = channel.getChannelAccessConfig().getAppId()
    String timestamp = DateUtil.getDate("yyyyMMddHHmmss")
    String nonce = UUIDUtil.getUUID()
    String body = SHAUtil.signSHA256(rawMessage.get("bodyJSON")).toLowerCase()
    String dataBeforeSign = appId + timestamp + nonce + body

    // 签名并返回签名信息
    String signature = SHAUtil.signHAMC2Base64(dataBeforeSign, channel.getChannelAccessConfig().getAppKey())
    StringBuilder sb = new StringBuilder()
    return sb.append("AppId=\"" + appId + "\",")
        .append("Timestamp=\"" + timestamp + "\",")
        .append("Nonce=\"" + nonce + "\",")
        .append("Signature=\"" + signature + "\"")
        .toString()
  }

  @Override
  boolean isValidSignature(Map<String, String> unverifiedMessage) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("isValidSignature"))
  }

  private static Map<String, String> getRequestHeader(String signature) {
    Map<String, String> header = new HashMap<>()
    header.put("Authorization", "OPEN-BODY-SIG " + signature)
    return header
  }

  private JSONObject doRequest(String method, Map<String, Object> bizParams, boolean reserveData) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

    // 请求参数
    Map<String, Object> body = new HashMap<>()
    body.put("merchantCode", channelAccessConfig.getMerchantId())
    body.put("terminalCode", channelAccessConfig.getTerminalId())
    body.putAll(bizParams)
    String bodyJSON = JSON.toJSONString(body)

    // 签名
    Map<String, String> rawMessage = new HashMap<>()
    rawMessage.put("bodyJSON", bodyJSON)
    String signature = getSignature(rawMessage)

    // 发起HTTP请求
    String methodFullName = getMethodFullName(method)
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result = HttpUtil.doPost(channelAccessConfig.getProperty(method + "_url"), bodyJSON, getRequestHeader(signature))
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyJSON)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
    String errorCode = resultJSON.getString("errCode")
    String errorMessage = resultJSON.getString("errInfo")
    if (errorCode != SUCCESS_CODE) {
      // 请求失败
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, methodFullName, errorCode, errorMessage)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }

    return resultJSON
  }

//  queryResCode
//  查询结果
//  字符串
//  否
//  0:成功
//  1:超时
//  2:已撤销
//  3:已退货
//  4:已冲正 5:失败(失败情况，后面 追加失败描述)
//  FF:交易状态未知
  private static TransactionState mapTransactionState(String tpTransactionState) {
    TransactionState transactionState
    switch (tpTransactionState) {
      case "NOTPAY":
        transactionState = TransactionState.WAITING
        break
      case "SUCCESS":
      case "0":
        transactionState = TransactionState.SUCCESS
        break
      case "PAYERROR":
      case "NOPAY":
      case "5":
        transactionState = TransactionState.FAILED
        break
      case "CLOSED":
      case "4":
        transactionState = TransactionState.CLOSED
        break
      case "REVOKED":
      case "2":
        transactionState = TransactionState.CANCELED
        break
      case "USERPAYING":
      case "1":
        transactionState = TransactionState.PENDING
        break
      case "REFUND":
      case "3":
        transactionState = TransactionState.REFUNDED
        break
      default:
        transactionState = TransactionState.UNKNOWN
    }
    return transactionState
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }

}
