package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import cn.hutool.core.convert.Convert
import cn.hutool.core.date.DatePattern
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.stream.Collectors
/**
 * https://vmp.eftpay.com.cn/vmp-docs/api-reference/scene/cashier/Flows.html
 */
class EFTMPay extends AbstractExternalChannelModule implements PaymentModule {

    private static final String SUCCESS_CODE = "00"

    EFTMPay(ExternalChannel channel) {
        super(channel)
    }

    @Override
    protected String getSignModuleName() {
        return this.getModuleName()
    }

    @Override
    String getModuleName() {
        return "Payment"
    }

    @Override
    ChannelCreateResponse create(ChannelCreateRequest request) {
        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("service", "service.united.wap.PreOrder")
        bizParams.put("user_confirm_key", channel.getChannelAccessConfig().getAppKey())
        bizParams.put("tid", channel.getChannelAccessConfig().getTerminalId())
        bizParams.put("transaction_amount", toEftAmount(request.getAmount()))
        bizParams.put("out_trade_no", request.getTransactionId())
        bizParams.put("subject", request.getDescription())
        bizParams.put("body", request.getDescription())
        bizParams.put("pay_scene", "WAP")
        bizParams.put("notify_url", getNotificationUrl())
        bizParams.put("return_url", getReturnUrl(request.getExtendedParams()))
        // 默认五分钟
        Long activeTime = Convert.toLong(channel.getChannelAccessConfig().getProperty("active_time"), 300)
        bizParams.put("active_time", activeTime.toString())
        bizParams.put("time", generateTime())

        // 发起请求
        Map<String, String> resultMap = doRequest("create", null, bizParams, true)

        // 解析并返回结果
        ChannelCreateResponse response = new ChannelCreateResponse()
        String timeExpire = LocalDateTime.parse(resultMap.get("time"), DatePattern.PURE_DATETIME_FORMATTER)
                .plusSeconds(activeTime)
                .format(DatePattern.NORM_DATETIME_FORMATTER)
        response.setTimeExpire(timeExpire)
        response.setFrontUrl(resultMap.get("pay_apptrade"))
        response.setChannel(request.getChannel())
        response.setPayMethod(PayMethod.EFTM_PAY)
        return response
    }

    @Override
    ChannelPayResponse pay(ChannelPayRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("pay"))
    }

    @Override
    ChannelQueryResponse query(ChannelQueryRequest request) {
        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("querytype", "OUT_TRADE")
        bizParams.put("out_trade_no", request.getTransactionId())
        bizParams.put("user_confirm_key", channel.getChannelAccessConfig().getAppKey())
        bizParams.put("time", generateTime())

        // 发起请求
        Map<String, String> resultMap = doRequest("query", null, bizParams, false)

        // 解析并返回结果
        ChannelQueryResponse response = new ChannelQueryResponse()
        response.setChannel(request.getChannel())
        response.setPayMethod(PayMethod.EFTM_PAY)
        response.setTransactionId(request.getTransactionId())
        response.setTpTransactionId(resultMap.get("eft_trade_no"))
        response.setRealAmount(toHexAmt(new BigDecimal(resultMap.get("transaction_amount"))))
        response.setTransactionState(mapTransactionState(resultMap.get("trade_status")))
        return response
    }

    @Override
    ChannelRefundResponse refund(ChannelRefundRequest request) {
        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("service", "service.united.wap.Refund")
        bizParams.put("user_confirm_key", channel.getChannelAccessConfig().getAppKey())
        bizParams.put("return_amount", toEftAmount(request.getAmount()))
        bizParams.put("out_trade_no", request.getRelatedTransactionId())
        bizParams.put("out_refund_no", request.getTransactionId())
        bizParams.put("pay_scene", "WAP")
        bizParams.put("time", generateTime())

        // 发起请求
        Map<String, String> resultMap = doRequest("refund", null, bizParams, true)

        // 解析并返回结果
        ChannelRefundResponse response = new ChannelRefundResponse()
        response.setTransactionState(mapTransactionState(resultMap.get("trade_status")))
        response.setTransactionId(request.getTransactionId())
        response.setTpTransactionId(resultMap.get("eft_trade_no"))
        response.setRealAmount(toHexAmt(new BigDecimal(resultMap.get("return_amount"))))
        return response
    }

    @Override
    ChannelCancelResponse cancel(ChannelCancelRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("cancel"))
    }

    @Override
    ChannelNotificationResponse payNotify(HttpServletRequest request) {
        String callbackJSONStr = request.getParameter("payload")
        return doPayNotify(callbackJSONStr)
    }

    ChannelNotificationResponse doPayNotify(String callbackJSONStr) {
        LoggerUtil.info("{0} received message: {1}.", getMethodFullName("payNotify"), callbackJSONStr)

        // 对请求进行验签
        Map<String, String> resultMap = JSON.parseObject(callbackJSONStr, Map.class)
        if (!isValidSignature(resultMap)) {
            // 验签失败
            throw new CommonException(ServiceError.INVALID_SIGNATURE)
        }

        // 返回结果
        ChannelNotificationResponse response = new ChannelNotificationResponse()

        Map<String, String> bizParams = new HashMap<>()
        bizParams.put("return_code", "success")
        bizParams.put("time", generateTime())
        String sign = buildSignature(bizParams)
        bizParams.put("sign", sign)
        response.setResponse(JSONObject.toJSONString(bizParams))

        ChannelPayResponse payResponse = new ChannelQueryResponse()
        payResponse.setTransactionId(resultMap.get("out_trade_no"))
        payResponse.setTpTransactionId(resultMap.get("eft_trade_no"))
        payResponse.setRealAmount(toHexAmt(new BigDecimal(resultMap.get("total_fee"))))
        payResponse.setTransactionState(mapTransactionState(resultMap.get("trade_status")))
        response.setPayResponse(payResponse)

        // 设置上下文（出入报文）
        TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, callbackJSONStr)
        TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, response.getResponse())

        return response
    }

    @Override
    String getSignature(Map<String, String> rawMessage) {
        ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
        return sha256(channelAccessConfig.getAccessKey() + rawMessage.get("rawStr"), "UTF-8")
    }

    @Override
    boolean isValidSignature(Map<String, String> unverifiedMessage) {
        Map<String, String> message = new HashMap<>(unverifiedMessage)
        String paramSign = message.remove("sign")
        String sign = buildSignature(message)
        return Objects.equals(paramSign, sign)
    }

    private String buildSignature(Map message) {
        String rawStr = buildRawStr(message)
        String sign = getSignature(["rawStr": rawStr])
        return sign
    }

    private String buildRawStr(Map rawMessage) {
        return rawMessage.keySet().stream().sorted().filter { k -> rawMessage.get(k) != null }.map { k ->
            k + "=" + rawMessage.get(k)
        }.collect(Collectors.joining("&"))
    }

    private String sha256(String signTempString, String charset)
            throws UnsupportedEncodingException, NoSuchAlgorithmException {
        byte[] bt = signTempString.getBytes(charset);
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        md.update(bt);
        byte[] r = md.digest();
        return bytes2Hex(r);
    }

    private String bytes2Hex(byte[] bts) {
        String des = "";
        for (int i = 0; i < bts.length; i++) {
            String tmp = (Integer.toHexString(bts[i] & 0xFF));
            if (tmp.length() == 1) {
                des += "0";
            }
            des += tmp;
        }
        return des;
    }

    private String generateTime() {
        return LocalDateTime.now().format(DatePattern.PURE_DATETIME_FORMATTER)
    }

    private BigDecimal toEftAmount(BigDecimal hexAmt) {
        hexAmt / 100
    }

    private BigDecimal toHexAmt(BigDecimal eftAmt) {
        eftAmt * 100
    }

    private String getNotificationUrl() {
        String notificationUrl = channel.getChannelAccessConfig().getProperty("notification_url")
        if (StringUtils.isNotBlank(notificationUrl)) {
            String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
            String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
            String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId
            return notificationUrl.endsWith("/") ? notificationUrl + path : notificationUrl + "/" + path
        }

        return null
    }

    private String getReturnUrl(String extendedParams) {
        String returnUrl = channel.getChannelAccessConfig().getProperty("return_url")
        if (StringUtils.isNotBlank(returnUrl)) {
            String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
            String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
            String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId + "/" + URLEncoder.encode(extendedParams, "UTF-8")
            return returnUrl.endsWith("/") ? returnUrl + path : returnUrl + "/" + path
        }

        return null
    }

    private static Map<String, String> getRequestHeader() {
        Map<String, String> header = new HashMap<>()
        header.put("Accept", "application/json")
        header.put("User-Agent", "Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.122 Safari/537.36 SE 2.X MetaSr 1.0")
        return header
    }

    private Map<String, String> doRequest(String method, String url, Map<String, Object> bizParams, boolean reserveData) {
        ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
        String requestUrl

        // 请求 url
        if (StringUtils.isBlank(url)) {
            requestUrl = channelAccessConfig.getProperty(method + "_url")
        } else {
            requestUrl = url
        }

        // 签名
        Map<String, Object> body = new HashMap<>(bizParams)
        body.put("sign", buildSignature(body))
        String bodyJSON = JSONObject.toJSONString(body)

        // 发起HTTP请求
        String methodFullName = getMethodFullName(method)
        byte[] result
        Timestamp reqTime = DateUtil.getNowTimeStamp()
        LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
        result = HttpUtil.doPost(requestUrl, bodyJSON, getRequestHeader())
        Timestamp respTime = DateUtil.getNowTimeStamp()
        if (null == result) {
            LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
        }
        String resultJSONStr = new String(result, StandardCharsets.UTF_8)

        // 设置上下文（出入报文）
        if (reserveData) {
            TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyJSON)
            TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
            TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
            TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
        }

        // 解析并返回结果
        Map<String, String> resultMap = JSONObject.parseObject(resultJSONStr, Map.class)
        LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
        if (!isValidSignature(resultMap)) {
            // 验签失败
            throw new CommonException(ServiceError.INVALID_SIGNATURE)
        }
        String status = resultMap.get("return_status")
        String message = resultMap.get("return_char")
        if (!SUCCESS_CODE.equals(status)) {
            // 请求失败
            LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, methodFullName, status, message)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, message)
        }

        return resultMap
    }

    private static TransactionState mapTransactionState(String tpTransactionState) {
        TransactionState transactionState
        switch (tpTransactionState) {
            case "APPLY_SUCCESS":
            case "WAITTING_PAYMENT":
                transactionState = TransactionState.WAITING
                break
            case "TRADE_SUCCESS":
                transactionState = TransactionState.SUCCESS
                break
            case "TRADE_CLOSED":
            case "TRADE_REFUND":
                transactionState = TransactionState.REFUNDED
                break
            case "TRADE_PROCESSING":
                transactionState = TransactionState.PENDING
                break
            case "TRADE_FAIL":
                transactionState = TransactionState.FAILED
                break
            default:
                transactionState = TransactionState.UNKNOWN
        }
        return transactionState
    }

    private String getMethodFullName(String method) {
        return channel.getChannelCode() + "." + getModuleName() + "." + method
    }

}
