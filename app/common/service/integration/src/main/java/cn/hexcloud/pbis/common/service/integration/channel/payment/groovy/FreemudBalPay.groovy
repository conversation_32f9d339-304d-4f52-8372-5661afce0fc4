package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.commons.utils.RedisUtil;
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule;
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.util.RequestSignUtil
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Commodity
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState;
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.util.concurrent.TimeUnit;

/**
 * @Classname FreemudPay* @Description:
 * @Date 2022/8/84:02 下午
 * <AUTHOR>
 */
class FreemudBalPay extends AbstractExternalChannelModule implements PaymentModule {

  final String submit = "/order/v2/submit";
  final String paySuccess = "/order/paysuccess";
  final String cancel = "/order/v2/cancel";

  final String ver = "1";

  FreemudBalPay(ExternalChannel channel) {
    super(channel);
  }

  @Override
  String getModuleName() {
    return "Payment"
  }


  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("create"))
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {

    JSONObject result = getOrderCode(request)
    String orderCode = result.getString("orderCode");
    if (orderCode == null) {
      LoggerUtil.error("orderCode返回为空", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }

    String orderTicketId = request.getOrderNo()
    String redisKey = "coupon" + ":" + orderTicketId
    RedisUtil.StringOps.setEx(redisKey, orderCode, 36000L, TimeUnit.SECONDS)

    ChannelPayResponse response =   getPay( orderCode, request)
    response.setTransactionId(request.getTransactionId())

    return  response

  }
  private JSONObject getOrderCode(ChannelPayRequest request) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

    if(checkConfig(channelAccessConfig)){

      throw new CommonException(ServiceError.PAYMENT_CONFIG_NOT_EXISTS)
    }

    String url = channelAccessConfig.getProperty("gateway_url")  + submit

    //装填参数
    JSONObject postJson = new JSONObject()
    postJson.put("appId", channelAccessConfig.getAppId())
    postJson.put("partnerId", channelAccessConfig.getMerchantId())
    postJson.put("ver", 1)


    long originalAmount = request.getOrderAmount().longValue()
    long discountAmount =request.getOrderDiscountAmount().longValue()
    long actualPayAmount = originalAmount-discountAmount


    JSONObject requestJson = new JSONObject()
    requestJson.put("actualPayAmount", actualPayAmount)
    String storeCode = ServiceContext.get(ContextKeyConstant.STORE_CODE)
    requestJson.put("storeId", storeCode)
    requestJson.put("storeName", storeCode)
    requestJson.put("originalAmount", originalAmount);
    requestJson.put("deliverAmount", 0)
   // requestJson.put("discountAmount", discountAmount);
    requestJson.put("canRefund", true)
    requestJson.put("orderClient", 15)
    requestJson.put("orderStatus", 2)
    requestJson.put("thirdOrderCode", request.getOrderNo());
    requestJson.put("orderType", 1)
    requestJson.put("userId",request.getMemberNo())
    requestJson.put("needBonus", "1")
    requestJson.put("payStatus", 1)

    JSONArray orderItemList = new JSONArray()
    List<Commodity> commodities = request.getCommodities()
    if (commodities) {
      Map<String, JSONObject> objectObjectHashMap = new HashMap<>()

      for (int i = 0; i < commodities.size(); i++) {
        Commodity product = commodities.get(i)
        JSONObject orderItemJson = new JSONObject()
        String code = product.getCode()
        JSONObject linkedHashMap = objectObjectHashMap.get(code)

        if (linkedHashMap != null) {

          Integer productQuantity = (Integer) linkedHashMap.get("productQuantity");
          linkedHashMap.put("productQuantity", productQuantity + 1);

        } else {
          orderItemJson.put("productSeq", 32131)
          orderItemJson.put("productId", code)
          orderItemJson.put("thirdProductId", code)
          orderItemJson.put("productName", product.getName())
          orderItemJson.put("productPrice", product.getPrice())
          orderItemJson.put("productQuantity", product.getQuantity())
          orderItemList.add(orderItemJson)
          objectObjectHashMap.put(code, orderItemJson);
        }
      }
    }
    requestJson.put("orderItemList", orderItemList);

    //支付
    JSONArray repPay = new JSONArray()
    JSONObject payInfo =    new JSONObject()
    payInfo.put("cardCode",request.getPayCode())
    payInfo.put("payChannel","10102")
    payInfo.put("payChannelName","储值卡")
    payInfo.put("payType",1)
    payInfo.put("payStatus",2)
    payInfo.put("payAmount",request.amount.intValue())
    repPay.add(payInfo)
  //  requestJson.put("payList", repPay)


    return getOrderResponse(url, postJson, requestJson)
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("query"))
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {

   String orderTicketId  = request.getOrderNo()
    String redisKey = "coupon" + ":" + orderTicketId
    String redisKey1 = "coupon" + ":" + orderTicketId + ":" + "REFUND"

    String orderCode = RedisUtil.StringOps.get(redisKey)
    RedisUtil.StringOps.setEx(redisKey1, orderCode, 3600L, TimeUnit.SECONDS)

    ChannelRefundResponse response = getCallback(orderCode, orderTicketId);
    response.setTransactionId(request.getTransactionId())
    // 反核销
    return response

  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("cancel"))
  }

  @Override
  ChannelNotificationResponse payNotify(HttpServletRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("payNotify"))
  }

  private ChannelRefundResponse getCallback(String orderCode, String orderTicketId) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    if(checkConfig(channelAccessConfig)){

      throw new CommonException(ServiceError.PAYMENT_CONFIG_NOT_EXISTS)
    }
    String url = channelAccessConfig.getProperty("gateway_url") + cancel

    // 装填参数
    JSONObject postJson = new JSONObject()
    postJson.put("appId", channelAccessConfig.getAppId())
    postJson.put("partnerId", channelAccessConfig.getMerchantId())
    postJson.put("ver", 1)
    // requestBody拼接
    JSONObject requestJson = new JSONObject();

    if (orderCode != null) {
      requestJson.put("orderCode", orderCode);
    }
    requestJson.put("thirdOrderCode", orderTicketId);
    requestJson.put("orderClient", "2")
    requestJson.put("operator", "NA")
    requestJson.put("reason", "退订")
    postJson.put("requestBody", JSONObject.toJSONString(requestJson))

    String sign = RequestSignUtil.signTopRequest(postJson, channelAccessConfig.getPrivateKey())
    postJson.put("sign", sign)

    String jsonStr = JSON.toJSONString(postJson)

    LoggerUtil.info("FreemudPay.getCallback is sending message: {0}.", jsonStr)

    byte[] result = HttpUtil.doPost(url, jsonStr, getRequestHeader())
    if (null == result) {
      LoggerUtil.error("FreemudPay.getCallback is failed with null result.", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    LoggerUtil.info("FreemudPay.getCallback received message: {0}.", resultJSONStr)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSON.toJSONString(postJson))
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)

    String statusCode = (String) resultJSON.get("statusCode");
    if ("100" != statusCode) {

      LoggerUtil.error("FreemudPay.getCallback is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("code"),
          resultJSON.getString("message"), resultJSON.getString("responseBody"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED,resultJSON.getString("message"))

    }

    ChannelRefundResponse response = new ChannelRefundResponse();
    response.setTransactionState(TransactionState.SUCCESS)
    return  response
  }


  private Map<String, String> getRequestHeader() {
    Map<String, String> header = new HashMap<>()
    header.put("x-transaction-id", UUID.randomUUID().toString())
    header.put("Content-Type", "application/json")
    header.put("access_token", channel.getChannelAccessConfig().getAppKey());
    return header
  }


  private JSONObject getOrderResponse(String url, JSONObject postJson, JSONObject requestJson) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    String body = JSONObject.toJSONString(requestJson)
    postJson.put("requestBody", body)

    String sign = RequestSignUtil.signTopRequest(postJson, channelAccessConfig.getPrivateKey())
    postJson.put("sign", sign)

    String jsonStr = JSON.toJSONString(postJson)
    LoggerUtil.info("FreemudPay.getOrderResponse is sending message: {0}.", jsonStr)

    byte[] result = HttpUtil.doPost(url, jsonStr, getRequestHeader())
    if (null == result) {
      LoggerUtil.error("FreemudPay.getOrderResponse is failed with null result.", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    LoggerUtil.info("FreemudPay.getOrderResponse received message: {0}.", resultJSONStr)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSON.toJSONString(postJson))
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)

    String statusCode = (String) resultJSON.get("statusCode");
    if ("100" != statusCode) {

      LoggerUtil.error("FreemudPay.getMember is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("code"),
          resultJSON.getString("message"), resultJSON.getString("responseBody"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED,resultJSON.getString("message"))

    }

    String responseBody = resultJSON.getString("responseBody");
    resultJSON = JSONObject.parseObject(responseBody)
    return resultJSON;

  }

  private ChannelPayResponse getPay(String orderCode, ChannelPayRequest request) {

    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    if(checkConfig(channelAccessConfig)){

      throw new CommonException(ServiceError.PAYMENT_CONFIG_NOT_EXISTS)
    }
    String url = channelAccessConfig.getProperty("gateway_url") + paySuccess

    // 装填参数
    JSONObject postJson = new JSONObject()
    postJson.put("appId", channelAccessConfig.getAppId())
    postJson.put("partnerId", channelAccessConfig.getMerchantId())
    postJson.put("ver", ver)
    // requestBody拼接
    JSONObject requestJson = new JSONObject();
    JSONArray payInfoList =  new JSONArray()
    JSONObject payInfo =    new JSONObject()
    payInfo.put("cardCode",request.getPayCode())
    payInfo.put("payChannel","10102")
    payInfo.put("payChannelName","储值卡")
    payInfo.put("payAmount",request.amount.intValue())
    payInfoList.add(payInfo)

    requestJson.put("transNo", request.getTransactionId())
    requestJson.put("operator", "pos");
    requestJson.put("orderCode", orderCode);
    requestJson.put("payList", payInfoList);

    postJson.put("requestBody", JSONObject.toJSONString(requestJson));

    String sign = RequestSignUtil.signTopRequest(postJson, channelAccessConfig.getPrivateKey())
    postJson.put("sign", sign)

    String jsonStr = JSON.toJSONString(postJson)

    LoggerUtil.info("FreemudPay.pay is sending message: {0}.", jsonStr)

    byte[] result = HttpUtil.doPost(url, jsonStr, getRequestHeader())
    if (null == result) {
      LoggerUtil.error("FreemudPay.pay is failed with null result.", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    LoggerUtil.info("FreemudPay.pay received message: {0}.", resultJSONStr)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSON.toJSONString(postJson))
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)

    String statusCode = (String) resultJSON.get("statusCode");
    if ("100" != statusCode) {

      LoggerUtil.error("FreemudPay.pay is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("code"),
          resultJSON.getString("message"), resultJSON.getString("responseBody"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED,resultJSON.getString("message"))

    }

    ChannelPayResponse response = new ChannelPayResponse();
    response.setTransactionState(TransactionState.SUCCESS)
    return  response


  }

  private static boolean checkConfig(ChannelAccessConfig channelAccessConfig) {

    return StringUtils.isAnyBlank(channelAccessConfig.getAppId(), channelAccessConfig.getMerchantId(),
        channelAccessConfig.getProperty("gateway_url"),channelAccessConfig.getAppKey()
        ,channelAccessConfig.getPrivateKey())

  }

}
