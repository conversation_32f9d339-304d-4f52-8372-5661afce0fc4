package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.commons.utils.UUIDUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.XmlUtil
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import cn.hutool.core.codec.Base64Decoder
import cn.hutool.core.codec.Base64Encoder
import cn.hutool.core.util.StrUtil
import org.apache.commons.lang3.StringUtils
import org.apache.http.NameValuePair
import org.apache.http.message.BasicNameValuePair

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.security.KeyFactory
import java.security.PrivateKey
import java.security.PublicKey
import java.security.Signature
import java.security.spec.PKCS8EncodedKeySpec
import java.security.spec.X509EncodedKeySpec
import java.sql.Timestamp
import java.text.DateFormat
import java.text.SimpleDateFormat

class FuiouMPay extends AbstractExternalChannelModule implements PaymentModule {

  private static final String SUCCESS_CODE = "000000"
  private static final String CHARSET = "GBK"
  private static final String SIGN_ALGORITHM = "RSA"
  private static final String SIGN_METHOD = "MD5withRSA"
  private static final String PAY_VER = "1"
  private static final String ORDER_NO_PREFIX = "2178F"
  private static final List<String> PENDING_CODE_LIST
  private static final ORDER_TYPE = "WECHAT"

  static {
    // 支付结果未知，需发查询
    PENDING_CODE_LIST = new ArrayList<>()
    PENDING_CODE_LIST.add("030010")
    PENDING_CODE_LIST.add("010002")
    PENDING_CODE_LIST.add("9999")
    PENDING_CODE_LIST.add("010001")
    PENDING_CODE_LIST.add("2001")
    PENDING_CODE_LIST.add("2002")
  }

  // 拼接富友订单号前缀
  private static String addPrefix(String transactionId) {
    return StrUtil.addPrefixIfNot(transactionId, ORDER_NO_PREFIX)
  }

  FuiouMPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    /*
      订单类型:
      JSAPI--公众号支付
      FWC--支付宝服务窗、支付宝小程序
      LETPAY-微信小程序
      BESTPAY--翼支付js
      MPAY--云闪付小程序（控件支付）
      APPLEPAY--APPLE相机扫码（监管要求，已停用）
      UNIONPAY--云闪付扫码
      UPBXJS--云闪付保险缴费
     */
    bizParams.put("trade_type", "JSAPI")
    /*
      子商户用户标识
      支付宝服务窗为用户buyer_id(此场景必填)
      微信公众号为用户的openid(小程序,公众号,服务窗必填)
      APPLEPAY（相机扫码）该值不用填
      UNIONPAY（云闪付扫码）该值必填
     */
    bizParams.put("sub_openid", request.getPayer())
    // 子商户公众号id, 微信交易为商户的appid(小程序,公众号必填)
    bizParams.put("sub_appid", channel.getChannelAccessConfig().getAppId())
    // 商品名称, 显示在用户账单的商品、商品说明等地方
    bizParams.put("goods_des", request.getDescription())
    // 商户订单号, 商户系统内部的订单号（5到30个字符、只能包含字母数字,区分大小写)
    bizParams.put("mchnt_order_no", addPrefix(request.getTransactionId()))
    // 总金额, 订单总金额，单位为分
    bizParams.put("order_amt", request.getAmount())
    // 实时交易终端IP(后期富友、银联侧风控主要依据，请真实填写)，暂时仅支持IPV4，IPV6 大概3月中旬支持
    bizParams.put("term_ip", request.getRemoteIp())
    // 交易起始时间, 订单生成时间，格式为yyyyMMddHHmmss
    bizParams.put("txn_begin_ts", parseTimestamp(request.getTransactionTime()))
    // 通知地址,接收富友异步通知回调地址,通知url必须为直接可访问的url,不能携带参数
    bizParams.put("notify_url", getNotificationUrl())
    // 其他非必填（但参与签名计算）字段
    bizParams.put("curr_type", "")
    bizParams.put("goods_detail", "")
    bizParams.put("goods_tag", "")
    bizParams.put("addn_inf", "")
    bizParams.put("limit_pay", "")
    bizParams.put("openid", "")
    bizParams.put("product_id", "")

    // 发起请求
    Map<String, String> resultMap = doRequest("create", bizParams, true)

    // 解析并返回结果
    String resultCode = resultMap.get("result_code")
    String resultMessage = resultMap.get("result_msg")
    ChannelCreateResponse response = new ChannelCreateResponse()
    if (resultCode == SUCCESS_CODE) {
      response.setPrePayId("")
    } else {
      LoggerUtil.error("{0} is failed, message: {1}.", null, getFullMethodName("create"), resultMessage)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultMessage)
    }
    response.setPrePayId(resultMap.get("session_id"))
    response.setPackStr("timeStamp=" + resultMap.get("sdk_timestamp") + "&nonceStr=" + resultMap.get("sdk_noncestr"))
    // 返回前端拉起支付需要的sign值
    response.setPrePaySign(resultMap.get("sdk_paysign"))
    response.setTpTransactionId(resultMap.get("reserved_fy_order_no"))
    response.setChannel(request.getChannel())
    response.setPayMethod(PayMethod.WX_PAY)
    return response
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("order_type", ORDER_TYPE)
    // 商户订单号, 商户系统内部的订单号(5到30个字符、只能包含字母数字,区分大小写)
    bizParams.put("mchnt_order_no", addPrefix(request.getTransactionId()))

    // 发起请求
    Map<String, String> resultMap = doRequest("query", bizParams, false)

    // 解析并返回结果
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setTransactionState(parseTransactionState(resultMap.get("trans_stat")))
    response.setChannel(request.getChannel())
    response.setWarningMessage(resultMap.get("result_msg"))
    response.setPayMethod(parsePayMethod(resultMap.get("order_type")))
    response.setTransactionId(resultMap.get("mchnt_order_no"))
    response.setTpTransactionId(resultMap.get("transaction_id"))
    response.setRealAmount(resultMap.get("order_amt") as BigDecimal)
    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("order_type", ORDER_TYPE)
    // 商户订单号, 商户系统内部的订单号(5到30个字符、只能包含字母数字,区分大小写)
    bizParams.put("mchnt_order_no", addPrefix(request.getRelatedTransactionId()))
    // 商户退款单号(5到30个字符、只能包含字母数字或者下划线，区分大小写)
    bizParams.put("refund_order_no", addPrefix("R" + request.getRelatedTransactionId()))
    // 总金额
    bizParams.put("total_amt", request.getAmount())
    // 退款金额
    bizParams.put("refund_amt", request.getAmount())
    // 其他非必填（但参与签名计算）字段
    bizParams.put("operator_id", "")

    // 发起请求
    Map<String, String> resultMap = doRequest("refund", bizParams, true)

    // 解析并返回结果
    String resultCode = resultMap.get("result_code")
    String resultMessage = resultMap.get("result_msg")
    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(resultMap.get("transaction_id"))
    response.setRealAmount(request.getAmount())
    if (resultCode == SUCCESS_CODE || PENDING_CODE_LIST.contains(resultCode)) {
      response.setTransactionState(TransactionState.PENDING)
    } else {
      response.setTransactionState(TransactionState.FAILED)
      if (StringUtils.isNotBlank(resultMessage)) {
        response.setWarning(true)
        response.setWarningMessage(resultMessage)
      }
    }
    //response.setTransactionState(TransactionState.PENDING) // 成功返回即代表退款已受理
    return response
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("order_type", ORDER_TYPE)
    // 商户订单号, 商户系统内部的订单号(5到30个字符、只能包含字母数字,区分大小写)
    bizParams.put("mchnt_order_no", addPrefix(request.getRelatedTransactionId()))
    // 商户撤销单号
    bizParams.put("cancel_order_no", addPrefix("C" + request.getRelatedTransactionId()))
    // 其他非必填（但参与签名计算）字段
    bizParams.put("operator_id", "")

    // 发起请求
    Map<String, String> resultMap = doRequest("cancel", bizParams, true)

    // 解析并返回结果
    String resultCode = resultMap.get("result_code")
    String resultMessage = resultMap.get("result_msg")
    ChannelCancelResponse response = new ChannelCancelResponse()
    response.setTpTransactionId(resultMap.get("transaction_id"))
    response.setRealAmount(request.getAmount())
    if (resultCode == SUCCESS_CODE) {
      response.setTransactionState(TransactionState.SUCCESS)
    } else if (PENDING_CODE_LIST.contains(resultCode)) {
      response.setTransactionState(TransactionState.PENDING)
    } else {
      response.setTransactionState(TransactionState.FAILED)
      if (StringUtils.isNotBlank(resultMessage)) {
        response.setWarning(true)
        response.setWarningMessage(resultMessage)
      }
    }
    return response
  }

  @Override
  ChannelNotificationResponse payNotify(HttpServletRequest request) {
    // 对请求进行验签
    String callbackXMLStr = request.getParameter("payload")
    String xmlJson = StrUtil.subAfter(callbackXMLStr, "req=", false)
    String xml = URLDecoder.decode(URLDecoder.decode(xmlJson, "UTF-8"), "UTF-8")
    LoggerUtil.info("call：{0}", xml)
    Map<String, String> resultMap = XmlUtil.xmlToMap(xml)
    if (!isValidSignature(resultMap)) {
      // 验签失败
      throw new CommonException(ServiceError.INVALID_SIGNATURE)
    }
    LoggerUtil.info("{0} received message: {1}.", getFullMethodName("payNotify"), callbackXMLStr)

    // 返回结果
    ChannelNotificationResponse response = new ChannelNotificationResponse()
    String response2ThirdParty = "1"
    response.setResponse(response2ThirdParty)
    ChannelPayResponse payResponse = new ChannelQueryResponse()
    payResponse.setPayMethod(parsePayMethod(resultMap.get("order_type")))
    payResponse.setTransactionId(resultMap.get("mchnt_order_no"))
    // 回调去掉订单前缀
    String mchntOrderNo = resultMap.get("mchnt_order_no")
    if (null != resultMap.get("mchnt_order_no") && mchntOrderNo.contains(ORDER_NO_PREFIX)) {
      // 去掉订单前缀
      String newMchntOrderNo = StrUtil.subAfter(mchntOrderNo, ORDER_NO_PREFIX, false)
      payResponse.setTransactionId(newMchntOrderNo)
    }
    payResponse.setTpTransactionId(resultMap.get("transaction_id"))
    payResponse.setRealAmount(resultMap.get("order_amt") as BigDecimal)
    if (SUCCESS_CODE != resultMap.get("result_code")) {
      payResponse.setTransactionState(TransactionState.FAILED)
    } else {
      payResponse.setTransactionState(TransactionState.SUCCESS)
    }
    response.setPayResponse(payResponse)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, callbackXMLStr)
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, DateUtil.getNowTimeStamp())
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, response2ThirdParty)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, DateUtil.getNowTimeStamp())

    return response
  }


  @Override
  String getSignature(Map<String, String> rawMessage) {
    String dataBeforeSign = getSignStr(rawMessage)

    String privateKeyStr = channel.getChannelAccessConfig().getPrivateKey()
    if (StringUtils.isBlank(privateKeyStr)) {
      throw new CommonException(ServiceError.INVALID_CHANNEL_ACCESS_CONFIG)
    }
    PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64Decoder.decode(privateKeyStr.getBytes(StandardCharsets.UTF_8)))
    KeyFactory factory = KeyFactory.getInstance(SIGN_ALGORITHM)
    PrivateKey privateKey = factory.generatePrivate(keySpec)
    Signature signature = Signature.getInstance(SIGN_METHOD)
    signature.initSign(privateKey)
    signature.update(dataBeforeSign.getBytes(CHARSET))
    return Base64Encoder.encode(signature.sign())
  }

  @Override
  boolean isValidSignature(Map<String, String> unverifiedMessage) {
    String sign = unverifiedMessage.get("sign")
    if (StringUtils.isBlank(sign)) {
      return false
    }
    String dataBeforeSign = getSignStr(unverifiedMessage)

    String publicKeyStr = channel.getChannelAccessConfig().getThirdPartyPublicKey()
    if (StringUtils.isBlank(publicKeyStr)) {
      throw new CommonException(ServiceError.INVALID_CHANNEL_ACCESS_CONFIG)
    }
    X509EncodedKeySpec keySpec = new X509EncodedKeySpec(Base64Decoder.decode(publicKeyStr.getBytes(StandardCharsets.UTF_8)))
    KeyFactory factory = KeyFactory.getInstance(SIGN_ALGORITHM)
    PublicKey publicKey = factory.generatePublic(keySpec)
    Signature signature = Signature.getInstance(SIGN_METHOD)
    signature.initVerify(publicKey)
    signature.update(dataBeforeSign.getBytes(CHARSET))
    return signature.verify(Base64Decoder.decode(sign.getBytes(CHARSET)))
  }

  private Map<String, String> doRequest(String method, Map<String, Object> bizParams, boolean reserveData) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

    // 请求参数
    Map<String, Object> body = new HashMap<>()
    body.put("version", PAY_VER)
    body.put("ins_cd", channelAccessConfig.getMerchantId()) // 机构号,接入机构在富友的唯一代码
    body.put("mchnt_cd", channelAccessConfig.getSubMerchantId()) // 商户号, 富友分配给二级商户的商户号
    body.put("term_id", channelAccessConfig.getTerminalId()) // 终端号(没有真实终端号统一填88888888)
    body.put("random_str", UUIDUtil.getUUID()) // 随机字符串
    body.putAll(bizParams)

    // 签名
    Map<String, String> rawMessage = new HashMap<>()
    for (Map.Entry<String, Object> entry : body) {
      rawMessage.put(entry.getKey(), entry.getValue().toString())
    }
    body.put("sign", getSignature(rawMessage))

    // 发起HTTP请求
    String methodFullName = getFullMethodName(method)
    String requestUrl = channelAccessConfig.getProperty(method + "_url")
    if (StringUtils.isBlank(requestUrl)) {
      throw new CommonException(ServiceError.INVALID_CHANNEL_CONFIG)
    }
    String bodyXML = XmlUtil.mapToXml(body, "xml", CHARSET)
    List<NameValuePair> requestBody = new ArrayList<>()
    requestBody.add(new BasicNameValuePair("req", URLEncoder.encode(bodyXML, CHARSET)))
    LoggerUtil.info("{0} is sending message to: {1}, body: {2}", methodFullName, requestUrl, bodyXML)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result = HttpUtil.doPost(requestUrl, requestBody, null)
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed with null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultXML = URLDecoder.decode(new String(result, CHARSET), CHARSET)

    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyXML)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultXML)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }

    // 解析并返回结果
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultXML)
    return getResultMap(resultXML)
  }

  private static TransactionState parseTransactionState(String tpTransactionState) {
    TransactionState transactionState
    switch (tpTransactionState) {
      case "NOTPAY":
        transactionState = TransactionState.WAITING
        break
      case "SUCCESS":
        transactionState = TransactionState.SUCCESS
        break
      case "PAYERROR":
        transactionState = TransactionState.FAILED
        break
      case "CLOSED":
        transactionState = TransactionState.CLOSED
        break
      case "REVOKED":
        transactionState = TransactionState.CANCELED
        break
      case "USERPAYING":
        transactionState = TransactionState.PENDING
        break
      case "REFUND":
        transactionState = TransactionState.REFUNDED
        break
      default:
        transactionState = TransactionState.UNKNOWN
    }
    return transactionState
  }

  private static PayMethod parsePayMethod(String tpPayMethod) {
    PayMethod payMethod
    switch (tpPayMethod) {
      case "010":
        payMethod = PayMethod.WX_PAY
        break
      case "020":
        payMethod = PayMethod.ALI_PAY
        break
      case "060":
        payMethod = PayMethod.QQ_PAY
        break
      case "080":
        payMethod = PayMethod.JD_PAY
        break
      case "090":
        payMethod = PayMethod.KOUBEI
        break
      case "100":
        payMethod = PayMethod.BEST_PAY
        break
      case "110":
        payMethod = PayMethod.UNION_PAY
        break
      default:
        payMethod = PayMethod.OTHERS
    }
    return payMethod
  }

  private static String getSignStr(Map<String, String> data) {
    Map<String, String> treeMap = new TreeMap<>(data)
    StringBuilder sb = new StringBuilder()
    for (Map.Entry<String, String> entry : treeMap.entrySet()) {
      if (!entry.getKey().startsWith("reserved") && !entry.getKey().startsWith("sign")) {
        sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&")
      }
    }
    return sb.substring(0, sb.length() - 1)
  }

  private String getNotificationUrl() {
    String notificationUrl = channel.getChannelAccessConfig().getProperty("notification_url")
    if (StringUtils.isNotBlank(notificationUrl)) {
      String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
      String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
      String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId
      return notificationUrl.endsWith("/") ? notificationUrl + path : notificationUrl + "/" + path
    }

    return null
  }

  private Map<String, String> getResultMap(String resultXML) {
    Map<String, String> resultMap = XmlUtil.xmlToMap(resultXML, CHARSET)
    if (!isValidSignature(resultMap)) {
      // 验签失败
      throw new CommonException(ServiceError.INVALID_SIGNATURE)
    }
    return resultMap
  }

  private static String parseTimestamp(Date hexDate) {
    DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss")
    return df.format(hexDate)
  }

}
