package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.UUIDUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.PaymentHttpUtil
import cn.hexcloud.pbis.common.util.XmlUtil
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import cn.hutool.core.codec.Base64Decoder
import cn.hutool.core.codec.Base64Encoder
import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson.JSON
import org.apache.commons.lang3.StringUtils
import org.apache.http.NameValuePair
import org.apache.http.message.BasicNameValuePair

import java.nio.charset.StandardCharsets
import java.security.KeyFactory
import java.security.PrivateKey
import java.security.PublicKey
import java.security.Signature
import java.security.spec.PKCS8EncodedKeySpec
import java.security.spec.X509EncodedKeySpec
import java.sql.Timestamp
import java.text.DateFormat
import java.text.SimpleDateFormat

class FuiouPayISV extends AbstractExternalChannelModule implements PaymentModule {
  // 指客户端和服务器建立连接的timeout
  private static final Integer CONN_TIME_OUT = 10000
  // 从连接池获取连接的timeout
  private static final Integer CONN_REQUEST_TIMOUT = 10000
  // 指客户端从服务器读取数据的timeout
  private static final Integer SOCKET_TIMEOUT = 10000
  private static final String SUCCESS_CODE = "000000"
  private static final String CHARSET = "GBK"
  private static final String SIGN_ALGORITHM = "RSA"
  private static final String SIGN_METHOD = "MD5withRSA"
  private static final String PAY_VER = "1"
  private static final String ORDER_NO_PREFIX = "2178F"
  private static final List<String> PENDING_CODE_LIST

  static {
    // 支付结果未知，需发查询
    PENDING_CODE_LIST = new ArrayList<>()
    PENDING_CODE_LIST.add("030010")
    PENDING_CODE_LIST.add("010002")
    PENDING_CODE_LIST.add("9999")
    PENDING_CODE_LIST.add("010001")
    PENDING_CODE_LIST.add("2001")
    PENDING_CODE_LIST.add("2002")
  }

  FuiouPayISV(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    StrUtil
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  // 拼接富友订单号前缀
  private static String addPrefix(String transactionId) {
    return StrUtil.addPrefixIfNot(transactionId, ORDER_NO_PREFIX)
  }

  // https://fundwx.fuiou.com/doc-yzf/#/scanpay/
  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    /*
      订单类型：
        ALIPAY(支付宝)
        WECHAT(微信)
        UNIONPAY(银联二维码)
        BESTPAY(翼支付)
        DIGICCY(数字货币)
     */
    bizParams.put("order_type", getPayType(request.getPayCode()))
    // 商品名称, 显示在用户账单的商品、商品说明等地方
    bizParams.put("goods_des", request.getOrderDescription())
    // 商户订单号, 商户系统内部的订单号（5到30个字符、 只能包含字母数字,区分大小写)
    bizParams.put("mchnt_order_no", addPrefix(request.getTransactionId()))
    // 总金额, 订单总金额，单位为分
    bizParams.put("order_amt", request.getAmount())
    // 实时交易终端IP(后期富友、银联侧风控主要依据，请真实填写)，暂时仅支持IPV4，IPV6 大概3月中旬支持
    bizParams.put("term_ip", request.getRemoteIp())
    // 交易起始时间, 订单生成时间，格式为yyyyMMddHHmmss
    bizParams.put("txn_begin_ts", parseTimestamp(request.getTransactionTime()))
    // 扫码支付授权码，设备读取用户的条码或者二维码信息
    bizParams.put("auth_code", request.getPayCode())
    // 终端信息
    Map<String, String> termInfo = new HashMap<>()
    termInfo.put("serial_num", getTermSN())
    termInfo.put("encrypt_rand_num", "")
    termInfo.put("secret_text", "")
    termInfo.put("app_version", "")
    bizParams.put("reserved_terminal_info", JSON.toJSONString(termInfo))
    // 其他非必填（但参与签名计算）字段
    bizParams.put("curr_type", "")
    bizParams.put("goods_detail", "")
    bizParams.put("goods_tag", "")
    bizParams.put("addn_inf", "")
    bizParams.put("sence", "")

    // 发起请求
    Map<String, String> resultMap = doRequest("pay", bizParams, true)

    // 解析并返回结果
    String resultCode = resultMap.get("result_code")
    String resultMessage = resultMap.get("result_msg")
    ChannelPayResponse response = new ChannelPayResponse()
    if (resultCode == SUCCESS_CODE) {
      response.setTransactionState(TransactionState.SUCCESS)
    } else if (PENDING_CODE_LIST.contains(resultCode)) {
      response.setTransactionState(TransactionState.PENDING)
    } else {
      response.setTransactionState(TransactionState.FAILED)
      if (StringUtils.isNotBlank(resultMessage)) {
        response.setWarning(true)
        response.setWarningMessage(resultMessage)
      }
    }
    response.setChannel(request.getChannel())
    response.setPayMethod(parsePayMethod(resultMap.get("order_type")))
    response.setPayer(resultMap.get("buyer_id")) // 买家在渠道(微信、支付宝)的用户ID
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(resultMap.get("transaction_id")) // 渠道交易流水号
    response.setRealAmount(request.getAmount())
    response.setExtendedParams(request.getExtendedParams())
    return response
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("order_type", getPayType(request.getPayCode()))
    // 商户订单号, 商户系统内部的订单号(5到30个字符、只能包含字母数字,区分大小写)
    bizParams.put("mchnt_order_no", addPrefix(request.getTransactionId()))

    // 发起请求
    Map<String, String> resultMap = doRequest("query", bizParams, false)

    // 解析并返回结果
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setTransactionState(parseTransactionState(resultMap.get("trans_stat")))
    response.setChannel(request.getChannel())
    response.setWarningMessage(resultMap.get("result_msg"))
    response.setPayMethod(parsePayMethod(resultMap.get("order_type")))
    response.setTransactionId(resultMap.get("mchnt_order_no"))
    response.setTpTransactionId(resultMap.get("transaction_id"))
    response.setRealAmount(resultMap.get("order_amt") as BigDecimal)
    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("order_type", getPayType(request.getPayCode()))
    // 商户订单号, 商户系统内部的订单号(5到30个字符、只能包含字母数字,区分大小写)
    bizParams.put("mchnt_order_no", addPrefix(request.getRelatedTransactionId()))
    // 商户退款单号(5到30个字符、只能包含字母数字或者下划线，区分大小写)
    bizParams.put("refund_order_no", addPrefix("R" + request.getRelatedTransactionId()))
    // 总金额
    bizParams.put("total_amt", request.getAmount())
    // 退款金额
    bizParams.put("refund_amt", request.getAmount())
    // 其他非必填（但参与签名计算）字段
    bizParams.put("operator_id", "")
    // 发起请求
    Map<String, String> resultMap = doRequest("refund", bizParams, true)
    // 解析并返回结果
    String resultCode = resultMap.get("result_code")
    String resultMessage = resultMap.get("result_msg")
    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(resultMap.get("transaction_id"))
    response.setRealAmount(request.getAmount())
    if (resultCode == SUCCESS_CODE || PENDING_CODE_LIST.contains(resultCode)) {
      response.setTransactionState(TransactionState.PENDING)
    } else {
      response.setTransactionState(TransactionState.FAILED)
      if (StringUtils.isNotBlank(resultMessage)) {
        response.setWarning(true)
        response.setWarningMessage(resultMessage)
      }
    }
    //response.setTransactionState(TransactionState.PENDING) // 成功返回即代表退款已受理
    return response
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("order_type", getPayType(request.getPayCode()))
    // 商户订单号, 商户系统内部的订单号(5到30个字符、只能包含字母数字,区分大小写)
    bizParams.put("mchnt_order_no", addPrefix(request.getRelatedTransactionId()))
    // 商户撤销单号
    bizParams.put("cancel_order_no", addPrefix("C" + request.getRelatedTransactionId()))
    // 其他非必填（但参与签名计算）字段
    bizParams.put("operator_id", "")

    // 发起请求
    Map<String, String> resultMap = doRequest("cancel", bizParams, true)

    // 解析并返回结果
    String resultCode = resultMap.get("result_code")
    String resultMessage = resultMap.get("result_msg")
    ChannelCancelResponse response = new ChannelCancelResponse()
    response.setTpTransactionId(resultMap.get("transaction_id"))
    response.setRealAmount(request.getAmount())
    if (resultCode == SUCCESS_CODE) {
      response.setTransactionState(TransactionState.SUCCESS)
    } else {
      response.setTransactionState(TransactionState.FAILED)
      if (StringUtils.isNotBlank(resultMessage)) {
        response.setWarning(true)
        response.setWarningMessage(resultMessage)
        response.setErrorMessage(resultMessage)
      }
    }
    return response
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    String dataBeforeSign = getSignStr(rawMessage)

    String privateKeyStr = channel.getChannelAccessConfig().getPrivateKey()
    if (StringUtils.isBlank(privateKeyStr)) {
      throw new CommonException(ServiceError.INVALID_CHANNEL_ACCESS_CONFIG)
    }
    PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64Decoder.decode(privateKeyStr.getBytes(StandardCharsets.UTF_8)))
    KeyFactory factory = KeyFactory.getInstance(SIGN_ALGORITHM)
    PrivateKey privateKey = factory.generatePrivate(keySpec)
    Signature signature = Signature.getInstance(SIGN_METHOD)
    signature.initSign(privateKey)
    signature.update(dataBeforeSign.getBytes(CHARSET))
    return Base64Encoder.encode(signature.sign())
  }

  @Override
  boolean isValidSignature(Map<String, String> unverifiedMessage) {
    String sign = unverifiedMessage.get("sign")
    if (StringUtils.isBlank(sign)) {
      return false
    }
    String dataBeforeSign = getSignStr(unverifiedMessage)

    String publicKeyStr = channel.getChannelAccessConfig().getThirdPartyPublicKey()
    if (StringUtils.isBlank(publicKeyStr)) {
      throw new CommonException(ServiceError.INVALID_CHANNEL_ACCESS_CONFIG)
    }
    X509EncodedKeySpec keySpec = new X509EncodedKeySpec(Base64Decoder.decode(publicKeyStr.getBytes(StandardCharsets.UTF_8)))
    KeyFactory factory = KeyFactory.getInstance(SIGN_ALGORITHM)
    PublicKey publicKey = factory.generatePublic(keySpec)
    Signature signature = Signature.getInstance(SIGN_METHOD)
    signature.initVerify(publicKey)
    signature.update(dataBeforeSign.getBytes(CHARSET))
    return signature.verify(Base64Decoder.decode(sign.getBytes(CHARSET)))
  }

  private Map<String, String> doRequest(String method, Map<String, Object> bizParams, boolean reserveData) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

    // 请求参数
    Map<String, Object> body = new HashMap<>()
    body.put("version", PAY_VER)
    body.put("ins_cd", channelAccessConfig.getMerchantId()) // 机构号,接入机构在富友的唯一代码
    body.put("mchnt_cd", channelAccessConfig.getSubMerchantId()) // 商户号, 富友分配给二级商户的商户号
    body.put("term_id", getTermId()) // 终端号(没有真实终端号统一填88888888)
    body.put("random_str", UUIDUtil.getUUID()) // 随机字符串
    body.putAll(bizParams)

    // 签名
    Map<String, String> rawMessage = new HashMap<>()
    for (Map.Entry<String, Object> entry : body) {
      rawMessage.put(entry.getKey(), entry.getValue().toString())
    }
    body.put("sign", getSignature(rawMessage))


    // 发起HTTP请求
    String methodFullName = getFullMethodName(method)
    String requestUrl = channelAccessConfig.getProperty(method + "_url")
    if (StringUtils.isBlank(requestUrl)) {
      throw new CommonException(ServiceError.INVALID_CHANNEL_CONFIG)
    }
    String bodyXML = XmlUtil.mapToXml(body, "xml", CHARSET)
    List<NameValuePair> requestBody = new ArrayList<>()
    requestBody.add(new BasicNameValuePair("req", URLEncoder.encode(bodyXML, CHARSET)))
    LoggerUtil.info("{0} is sending message to: {1}, body: {2}", methodFullName, requestUrl, bodyXML)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result = null
    try {
      result = PaymentHttpUtil.doPost(requestUrl, requestBody, null, CONN_TIME_OUT, CONN_TIME_OUT, CONN_TIME_OUT)
    } catch (Exception e) {
      LoggerUtil.error("request FuiouPayISV error", e)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "富友api超时，请稍后重试")
    }

    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed with null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultXML = URLDecoder.decode(new String(result, CHARSET), CHARSET)

    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyXML)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultXML)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }

    // 解析并返回结果
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultXML)
    return getResultMap(resultXML)
  }

  private static TransactionState parseTransactionState(String tpTransactionState) {
    TransactionState transactionState
    switch (tpTransactionState) {
      case "NOTPAY":
        transactionState = TransactionState.WAITING
        break
      case "SUCCESS":
        transactionState = TransactionState.SUCCESS
        break
      case "PAYERROR":
        transactionState = TransactionState.FAILED
        break
      case "CLOSED":
        transactionState = TransactionState.CLOSED
        break
      case "REVOKED":
        transactionState = TransactionState.CANCELED
        break
      case "USERPAYING":
        transactionState = TransactionState.PENDING
        break
      case "REFUND":
        transactionState = TransactionState.REFUNDED
        break
      default:
        transactionState = TransactionState.UNKNOWN
    }
    return transactionState
  }

  private static PayMethod parsePayMethod(String tpPayMethod) {
    PayMethod payMethod
    switch (tpPayMethod) {
      case "WECHAT":
        payMethod = PayMethod.WX_PAY
        break
      case "ALIPAY":
        payMethod = PayMethod.ALI_PAY
        break
      case "UNIONPAY":
      case "PY68": // 银联分期-商户贴息
      case "PY69": // 银联分期-持卡人贴息
        payMethod = PayMethod.UNION_PAY
        break
      case "BESTPAY": // 翼支付
        payMethod = PayMethod.BEST_PAY
        break
      default:
        payMethod = PayMethod.OTHERS
    }
    return payMethod
  }

  private static String getPayType(String payCode) {
    if (StringUtils.isBlank(payCode)) {
      throw new CommonException(ServiceError.INVALID_PAY_CODE)
    }

    String payCodeStarts = payCode.substring(0, 2)
    String fuiouPayType
    switch (payCodeStarts) {
      case "10":
      case "11":
      case "12":
      case "13":
      case "14":
      case "15":
        fuiouPayType = "WECHAT"
        break
      case "18":
        fuiouPayType = "JD"
        break
      case "25":
      case "26":
      case "27":
      case "28":
      case "29":
      case "30":
        fuiouPayType = "ALIPAY"
        break
      case "51":
        fuiouPayType = "BESTPAY"
        break
      case "62":
        fuiouPayType = "UNIONPAY"
        break
      case "91":
        fuiouPayType = "QQ"
        break
      default:
        throw new CommonException(ServiceError.INVALID_PAY_CODE)
    }
    return fuiouPayType
  }

  private static String getSignStr(Map<String, String> data) {
    Map<String, String> treeMap = new TreeMap<>(data)
    StringBuilder sb = new StringBuilder()
    for (Map.Entry<String, String> entry : treeMap.entrySet()) {
      if (!entry.getKey().startsWith("reserved") && !entry.getKey().startsWith("sign")) {
        sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&")
      }
    }
    return sb.substring(0, sb.length() - 1)
  }

  private Map<String, String> getResultMap(String resultXML) {
    Map<String, String> resultMap = XmlUtil.xmlToMap(resultXML, CHARSET)
    // 校验签名
    if (!isValidSignature(resultMap)) {
      // 验签失败
      throw new CommonException(ServiceError.INVALID_SIGNATURE)
    }
    return resultMap
  }

  private static String parseTimestamp(Date hexDate) {
    DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss")
    return df.format(hexDate)
  }

  private String getTermId() {
    if (StringUtils.isBlank(channel.getChannelAccessConfig().getTerminalId())) {
      return null
    }

    String[] termArr = channel.getChannelAccessConfig().getTerminalId().split("&")
    if (null == termArr || termArr.length == 0) {
      return null
    }

    return termArr[0]
  }

  private String getTermSN() {
    if (StringUtils.isBlank(channel.getChannelAccessConfig().getTerminalId())) {
      return null
    }

    String[] termArr = channel.getChannelAccessConfig().getTerminalId().split("&")
    if (null == termArr || termArr.length < 2) {
      return null
    }

    return termArr[1]
  }

}
