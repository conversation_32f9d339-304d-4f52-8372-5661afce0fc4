package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.commons.utils.cipher.RSAUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import org.apache.commons.lang3.StringUtils
import cn.hutool.core.date.DateTime
import cn.hutool.core.date.DateUtil
import cn.hutool.core.date.DateField
import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson.JSONObject
import com.alibaba.fastjson.JSON
import java.sql.Timestamp
import org.apache.http.conn.ConnectTimeoutException
import java.nio.charset.StandardCharsets

class HuifuUnifiedPay extends AbstractExternalChannelModule implements PaymentModule {

  private static final String SUCCESS_CODE = "00000000" //交易受理成功
  private static final String PROCESSING_CODE = "00000100"// 交易正在处理中
  private static final String PRODUCT_ID = "KAZX" // 产品id
  private static final List<String> ALLOWED_RETRY_CODES = Arrays.asList("20000001", "99999999")
  private static final int CONNECTION_TIMEOUT = 10000 // 连接超时时间

  HuifuUnifiedPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    StrUtil
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("create"))
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("req_seq_id", request.getTransactionId())
    //日期格式：yyyyMMdd；示例值：********
    bizParams.put("req_date", DateUtil.format(request.getTransactionTime(), "yyyyMMdd"))
    bizParams.put("huifu_id", channel.getChannelAccessConfig().getMerchantId())
    bizParams.put("trans_amt", parseFenToYuan(request.getAmount()))
    bizParams.put("auth_code", request.getPayCode())
    bizParams.put("rule_origin","02") //01：接口发起，02：控台配置；
    bizParams.put("div_flag","Y")//Y：开，N：关；示例值：Y
    if (request.getCommodities()) {
      bizParams.put("goods_desc", request.getCommodities().get(0).getName())
    } else {
      bizParams.put("goods_desc", "商品支付")
    }
    Map<String, String> extMap = new HashMap<>()
    extMap.put("ip_addr", request.getRemoteIp())
    bizParams.put("risk_check_data", extMap)
    // 5分钟过期
    DateTime timeExpire = DateUtil.offset(DateTime.now(), DateField.MINUTE, 5)
    bizParams.put("time_expire", DateUtil.format(timeExpire, "yyyyMMddHHmmss"))
    JSONObject jsonResult = doRequest("pay", bizParams, true)
    ChannelPayResponse response = new ChannelPayResponse()
    response.setTpTransactionId(jsonResult.getString("hf_seq_id"))
    response.setTransactionState(mapTransactionState(jsonResult.getString("trans_stat")))
    response.setPayMethod(mapPayMethod(jsonResult.getString("trade_type")))
    response
  }

  private static PayMethod mapPayMethod(String payMethod) {
    switch (payMethod) {
      case "T_MICROPAY":
        return PayMethod.WX_PAY
      case "A_MICROPAY":
        return PayMethod.ALI_PAY
      case "U_MICROPAY":
        return PayMethod.UNION_PAY
      case "D_MICROPAY":
        return PayMethod.CDBC
      default:
        return PayMethod.OTHERS
    }
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("huifu_id", channel.getChannelAccessConfig().getMerchantId())
    bizParams.put("org_req_date", DateUtil.format(request.getTransactionTime(), "yyyyMMdd"))
    bizParams.put("org_req_seq_id", request.getTransactionId())
    JSONObject jsonResult = doRequest("query", bizParams, false)
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(jsonResult.getString("org_hf_seq_id"))
    response.setTransactionState(mapTransactionState(jsonResult.getString("trans_stat")))
    response.setPayMethod(mapPayMethod(jsonResult.getString("trans_type")))
    response
  }

  // 退款
  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("req_seq_id", request.getTransactionId())
    bizParams.put("org_req_seq_id", request.getRelatedTransactionId())
    //日期格式：yyyyMMdd；示例值：********
    bizParams.put("req_date", DateUtil.format(DateTime.now(), "yyyyMMdd"))
    bizParams.put("huifu_id", channel.getChannelAccessConfig().getMerchantId())
    bizParams.put("ord_amt", parseFenToYuan(request.getAmount()))
    bizParams.put("org_req_date", DateUtil.format(request.getTransactionTime(), "yyyyMMdd"))
    JSONObject jsonResult = doRequest("refund", bizParams, true)
    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTpTransactionId(jsonResult.getString("hf_seq_id"))
    response.setTransactionState(mapTransactionState(jsonResult.getString("trans_stat")))
    response.setWarningMessage(jsonResult.getString("bank_message"))
    return response
  }

  ChannelQueryResponse queryRefundResult(ChannelQueryRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("org_req_seq_id", request.getTransactionId())
    //日期格式：yyyyMMdd；示例值：********
    bizParams.put("huifu_id", channel.getChannelAccessConfig().getMerchantId())
    bizParams.put("org_req_date", DateUtil.format(request.getTransactionTime(), "yyyyMMdd"))
    JSONObject jsonResult = doRequest("refund_query", bizParams, false)
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setTpTransactionId(jsonResult.getString("org_hf_seq_id"))
    response.setTransactionState(mapTransactionStateWithCode(jsonResult.getString("trans_stat"), jsonResult.getString("resp_code")))
    response.setPayMethod(PayMethod.WX_PAY)
    Map<String, String> feeFiled = new HashMap<>()
    feeFiled.put("fee_amount", parseYuanToFen(jsonResult.getString("fee_amt")))
    response.setExtendedParams(JSON.toJSONString(feeFiled))
    response.setWarningMessage(jsonResult.getString("resp_desc"))
    return response
  }

  private static String parseYuanToFen(String amount) {
    if (StrUtil.isBlank(amount)) {
      return 0L
    }
    // 转换为BigDecimal
    BigDecimal amt = new BigDecimal(amount)
    if (amt <= BigDecimal.ZERO) {
      return 0L
    }
    // 乘以100，小数点后截断2位
    return (amt * new BigDecimal(100)).longValue()
  }

  // 注意：
  // 不支持关闭一分钟内刚刚发起的交易；
  // 原交易已是终态（成功/失败）的，关单会失败；
  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("req_seq_id", request.getTransactionId())
    bizParams.put("org_req_seq_id", request.getRelatedTransactionId())
    //日期格式：yyyyMMdd；示例值：********
    bizParams.put("req_date", DateUtil.format(DateTime.now(), "yyyyMMdd"))
    bizParams.put("huifu_id", channel.getChannelAccessConfig().getMerchantId())
    bizParams.put("org_req_date", DateUtil.format(request.getTransactionTime(), "yyyyMMdd"))
    ChannelCancelResponse response = new ChannelCancelResponse()
    JSONObject jsonResult
    jsonResult = doRequest("cancel", bizParams, true)
    response.setTpTransactionId(jsonResult.getString("hf_seq_id"))
    response.setTransactionState(mapTransactionState(jsonResult.getString("trans_stat")))
    return response
  }

  // 转换汇付交易状态
  private static TransactionState mapTransactionStateWithCode(String status, String code) {
    switch (status) {
      case "S":
        return TransactionState.SUCCESS
      case "F":
        if (ALLOWED_RETRY_CODES.contains(code)) {
          return TransactionState.WAITING
        } else {
          return TransactionState.FAILED
        }
      case "P":
        return TransactionState.PENDING
      case "I":
        return TransactionState.PENDING
      default:
        return "UNKNOWN"
    }
  }

  // 转换汇付交易状态
  private static TransactionState mapTransactionState(String status) {
    switch (status) {
      case "S":
        return TransactionState.SUCCESS
      case "F":
        return TransactionState.FAILED
      case "P":
        return TransactionState.PENDING
      default:
        return TransactionState.UNKNOWN
    }
  }


  private static BigDecimal parseFenToYuan(BigDecimal fen) {
    if (null == fen) {
      return null
    }
    // 除以100，小数点后截断2位
    return fen.divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN)
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    String paramsJson = JSON.toJSONString(new TreeMap<>(rawMessage))
    return RSAUtil.signByPrivateKey(paramsJson.getBytes("UTF-8"),
            channel.getChannelAccessConfig().getPrivateKey())
  }

  private JSONObject doRequest(String method, Map<String, Object> bizParams, boolean reserveData) {
    return doRequest(method, bizParams, reserveData, false)
  }

  private JSONObject doRequest(String method, Map<String, Object> bizParams, boolean reserveData, boolean timeout) {
    // 检查配置
    if (StringUtils.isAnyBlank(channel.getChannelAccessConfig().getMerchantId(),
            channel.getChannelAccessConfig().getPrivateKey(), channel.getChannelAccessConfig().getThirdPartyPublicKey()
    )) {
      throw new CommonException(ServiceError.INVALID_CHANNEL_CONFIG)
    }

    Map<String, Object> commonParams = new HashMap<>()
    commonParams.put("sys_id", channel.getChannelAccessConfig().getMerchantId())
    commonParams.put("product_id", PRODUCT_ID)
    commonParams.put("sign", getSignature(bizParams))
    commonParams.put("data", bizParams)

    String methodFullName = getMethodFullName(method)
    // 接口地址
    String url = channel.getChannelAccessConfig().getProperty(method + "_url")
    // json
    String bodyJSON = JSON.toJSONString(commonParams)
    LoggerUtil.info("{0} is sending message to: {1}, body: {2}", methodFullName, url, bodyJSON)

    Timestamp reqTime = cn.hexcloud.commons.utils.DateUtil.getNowTimeStamp()
    byte[] result
    try {
      if (timeout) {
        result = HttpUtil.doPost(url, bodyJSON, null, CONNECTION_TIMEOUT, CONNECTION_TIMEOUT, CONNECTION_TIMEOUT)
      } else {
        result = HttpUtil.doPost(url, bodyJSON)
      }
    } catch (ConnectTimeoutException ex) {
      LoggerUtil.error("{0} is failed, timeout.", ex, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "请求超时.")
    } catch (Exception ex) {
      LoggerUtil.error("{0} is failed, exception: {1}.", ex, methodFullName, ex.getMessage())
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, ex.getMessage())
    }
    Timestamp respTime = cn.hexcloud.commons.utils.DateUtil.getNowTimeStamp()
    LoggerUtil.info("{0} received message: {1}.", methodFullName, result)
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)
    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyJSON)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }
    JSONObject resultJSON
    try {
      resultJSON = JSON.parseObject(resultJSONStr)
    } catch (Exception ex) {
      LoggerUtil.error("{0} is failed, result: {1}.", ex, methodFullName, resultJSONStr)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "汇付接口响应异常.")
    }
    JSONObject jsonData = resultJSON.getJSONObject("data")
    // 校验响应码
    String respCode = jsonData.getString("resp_code")
    String respDesc = jsonData.getString("resp_desc")
    LoggerUtil.error("{0} is failed, respCode: {1}, respDesc: {2}.", null, methodFullName, respCode, respDesc)
    if (SUCCESS_CODE != respCode && PROCESSING_CODE != respCode) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, respDesc)
    }
    // 返回结果
    return jsonData
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }
}
