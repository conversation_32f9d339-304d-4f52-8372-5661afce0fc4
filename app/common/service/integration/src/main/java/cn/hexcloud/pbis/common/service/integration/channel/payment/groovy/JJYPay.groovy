package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Commodity
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.CommodityProperty
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import cn.hexcloud.pbis.common.util.exception.ThirdPartyApiFailedException
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils
import org.springframework.util.DigestUtils

import java.nio.charset.StandardCharsets
import java.sql.Timestamp

class JJYPay extends AbstractExternalChannelModule implements PaymentModule {

  private static final String SUCCESS_CODE = "200"
  private static final Map<String, String> URL_MAP
  private static final List<String> TOLERANCE_ERROR_CODE_LIST

  static {
    URL_MAP = new HashMap<>()
    URL_MAP.put("create", "/openapi/member_payment_order/create")
    URL_MAP.put("query", "/openapi/member_payment_order/status")
    URL_MAP.put("refund", "/openapi/member_payment_order/refund")

    TOLERANCE_ERROR_CODE_LIST = new ArrayList<>()
    TOLERANCE_ERROR_CODE_LIST.add("9997") // 退款时订单不存在（用户未在久久丫小程序上点击【立即付款】情况下调退款接口将产生此错误）
  }

  JJYPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("storeCode", ServiceContext.getString(ContextKeyConstant.STORE_CODE))
    bizParams.put("thirdPlatformKey", "hekuo")
    bizParams.put("thirdPlatformOrderId", request.getTransactionId())
    bizParams.put("thirdPlatformOrderNo", request.getTransactionId())
    bizParams.put("discountAmount", request.getDiscountAmount())
    bizParams.put("actualPaymentAmount", request.getAmount())
    List<Map<String, Object>> skuList = new ArrayList<>()
    if (request.getCommodities()) {
      for (Commodity commodity : request.getCommodities()) {
        List<CommodityProperty> commodityProperties = commodity.getProperties()
        StringBuilder commodityPropertyBuilder = new StringBuilder()
        Map<String, Object> skuBody = new HashMap<>()
        skuBody.put("skuName", commodity.getName())
        skuBody.put("quantity", commodity.getQuantity())
        skuBody.put("skuAmount", commodity.getPrice())
        skuBody.put("imageUrl", commodity.getImageUrl())
        if (commodityProperties) {
          for (CommodityProperty prop : commodityProperties) {
            commodityPropertyBuilder.append(prop.getPropertyValue().getName()).append("/")
          }
          skuBody.put("general", commodityPropertyBuilder.substring(0, commodityPropertyBuilder.length() - 1))
        }

        skuList.add(skuBody)
      }
    }
    bizParams.put("skus", skuList)

    // 发起请求
    JSONObject resultJSON = doRequest("create", bizParams, true)

    // 解析并返回结果
    JSONObject dataNode = resultJSON.getJSONObject("data")
    ChannelCreateResponse response = new ChannelCreateResponse()
    response.setPayCode(dataNode.getString("qrUrl"))
    response.setChannel(request.getChannel())
    return response
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("storeCode", ServiceContext.getString(ContextKeyConstant.STORE_CODE))
    bizParams.put("thirdPlatformKey", "hekuo")
    bizParams.put("thirdPlatformOrderId", request.getTransactionId())
    bizParams.put("thirdPlatformOrderNo", request.getTransactionId())

    // 发起请求
    JSONObject resultJSON = doRequest("query", bizParams, false)

    // 解析并返回结果
    JSONObject dataNode = resultJSON.getJSONObject("data")
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setChannel(request.getChannel())
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(dataNode.getString("orderNo"))
    response.setTransactionState(mapTransactionState(dataNode.getString("status")))
    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new LinkedHashMap<>()
    bizParams.put("storeCode", ServiceContext.getString(ContextKeyConstant.STORE_CODE))
    bizParams.put("thirdPlatformKey", "hekuo")
    bizParams.put("thirdPlatformOrderId", request.getRelatedTransactionId())

    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionId(request.getTransactionId())
    response.setRealAmount(request.getAmount())
    try {
      // 发起请求
      JSONObject resultJSON = doRequest("refund", bizParams, true)

      // 解析并返回结果
      JSONObject dataNode = resultJSON.getJSONObject("data")
      response.setTpTransactionId(dataNode.getString("orderNo"))
      response.setTpTransactionId(dataNode.getString("orderNo"))
      if (dataNode.getBoolean("status")) {
        response.setTransactionState(TransactionState.PENDING)
      } else {
        response.setTransactionState(TransactionState.FAILED)
      }
      return response
    } catch (ThirdPartyApiFailedException e) {
      if (TOLERANCE_ERROR_CODE_LIST.contains(e.getError().getCode())) {
        // 容错处理
        LoggerUtil.warn("{0} tolerates the exception with code: {1}", getFullMethodName("refund"), e.getError().getCode())
        response.setTransactionState(TransactionState.PENDING)
        return response
      } else {
        throw e
      }
    }
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    StringBuilder sb = new StringBuilder()
    for (Map.Entry<String, String> entry : rawMessage.entrySet()) {
      if (StringUtils.isNotBlank(entry.getValue())) {
        sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&")
      }
    }

    String dataBeforeSign = sb.append("key=").append(channel.getChannelAccessConfig().getAppKey())
    return DigestUtils.md5DigestAsHex(dataBeforeSign.getBytes(StandardCharsets.UTF_8))
  }

  private static TransactionState mapTransactionState(String tpTransactionState) {
    TransactionState transactionState
    switch (tpTransactionState) {
      case "20":
        transactionState = TransactionState.SUCCESS
        break
      case "10":
        transactionState = TransactionState.PENDING
        break
      case "30":
        transactionState = TransactionState.REFUNDED
        break
      case "40":
        transactionState = TransactionState.CANCELED
        break
      default:
        transactionState = TransactionState.UNKNOWN
    }
    return transactionState
  }

  private JSONObject doRequest(String method, Map<String, Object> bizParams, boolean reserveData) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

    // 请求参数
    Map<String, Object> body = new HashMap<>()
    body.put("version", "v1.0")
    body.put("timestamp", String.valueOf(new Date().getTime() / 1000))
    body.put("nonceStr", String.valueOf(System.currentTimeMillis()))
    body.put("accessKey", channelAccessConfig.getAppId())
    body.put("merchantCode", channelAccessConfig.getMerchantId())
    body.put("data", JSON.toJSONString(bizParams))

    // 签名
    Map<String, String> rawMessage = new TreeMap<>()
    for (Map.Entry<String, Object> entry : body) {
      rawMessage.put(entry.getKey(), entry.getValue().toString())
    }
    body.put("sign", getSignature(rawMessage))

    // 发起HTTP请求
    String methodFullName = getFullMethodName(method)
    String bodyJSONStr = JSON.toJSONString(body)
    String requestUrl = getCompleteUrl(method)
    LoggerUtil.info("{0} is sending message to: {1}, body: {2}.", methodFullName, requestUrl, bodyJSONStr)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result = HttpUtil.doPost(requestUrl, bodyJSONStr)
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
    String errorCode = resultJSON.getString("code")
    String errorMessage = resultJSON.getString("msg")
    if (errorCode != SUCCESS_CODE) {
      // 请求失败
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, methodFullName, errorCode, errorMessage)
      throw new ThirdPartyApiFailedException(errorCode, errorMessage)
    }

    return resultJSON
  }

  private String getCompleteUrl(String method) {
    String baseUrl = channel.getChannelAccessConfig().getGatewayUrl()
    return baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) + URL_MAP.get(method) : baseUrl + URL_MAP.get(method)
  }
}
