package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.cert.CertHelper
import cn.hexcloud.pbis.common.util.cert.CertUtil
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import cn.hutool.core.date.DatePattern
import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson.JSONObject

import java.sql.Timestamp

class JSBPay extends AbstractExternalChannelModule implements PaymentModule {
  private static final String SUCCESS_CODE = "000000"
  private static final String DEVICE_ID = "0000001"

  JSBPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  private Map<String, String> buildCommonParams(String transactionId, Date transactionTime) {
    Map<String, String> params = new HashMap<>()
    params.put("msgID", transactionId) //业务id
    params.put("svrCode", "")//服务代码
    params.put("partnerId", channel.getChannelAccessConfig().getMerchantId())//服务名称
    params.put("channelNo", "m")//渠道id
    params.put("publicKeyCode", "00")
    params.put("version", "v1.0.0")
    params.put("charset", "utf-8")
    params.put("createDate", DateUtil.format(transactionTime, DatePattern.PURE_DATE_PATTERN))
    params.put("createTime", DateUtil.format(transactionTime, DatePattern.PURE_TIME_PATTERN))
    params.put("bizDate", DateUtil.format(transactionTime, DatePattern.PURE_DATE_PATTERN))
    return params
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    Map<String, String> params = new HashMap<>()
    // 公共参数
    params.putAll(buildCommonParams(request.getTransactionId(), request.getTransactionTime()))
    // 业务参数
    params.put("outTradeNo", request.getTransactionId())
    params.put("service", "ePay")
    params.put("authCode", request.getPayCode())
    params.put("totalFee", parseFenToYuan(request.getAmount()))
    params.put("deviceNo", channel.getChannelAccessConfig().getTerminalId())
    params.put("mchIp", request.getRemoteIp())
    if (request.getCommodities()) {
      params.put("proInfo", request.getCommodities().get(0).getName())
    } else {
      params.put("proInfo", "商品支付")
    }
    JSONObject resultJSON = doRequest("pay", params, true)
    ChannelPayResponse response = new ChannelPayResponse()
    response.setTpTransactionId(resultJSON.getString("orderNo"))
    response.setTransactionState(mapTransactionState(resultJSON.getString("orderStatus")))
    response.setWarningMessage(resultJSON.getString("respMsg"))
    String tradeType = resultJSON.getString("tradeType")
    if ("1" == tradeType) {
      response.setPayMethod(PayMethod.JSB_EPAY)
    } else if ("2" == tradeType) {
      response.setPayMethod(PayMethod.WX_PAY)
    } else if ("3" == tradeType) {
      response.setPayMethod(PayMethod.ALI_PAY)
    } else if ("4" == tradeType) {
      response.setPayMethod(PayMethod.UNION_PAY)
    } else {
      response.setPayMethod(PayMethod.OTHERS)
    }
    return response
  }

  private static TransactionState mapTransactionState(String state) {
    switch (state) {
      case "1":
        return TransactionState.SUCCESS
      case "2":
        return TransactionState.PENDING
      case "3":
        return TransactionState.FAILED
      default:
        return TransactionState.UNKNOWN
    }
  }

  private static TransactionState mapRefundTransactionState(String state) {
    switch (state) {
      case "1":
        return TransactionState.SUCCESS
      case "2":
        return TransactionState.FAILED
      case "3":
        return TransactionState.PENDING
      case "4":
        return TransactionState.CLOSED
      default:
        return TransactionState.UNKNOWN
    }
  }

  private static String parseFenToYuan(BigDecimal fen) {
    if (null == fen) {
      return null
    }
    // 除以100，小数点后截断2位
    def amt = fen.divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN)
    return amt.toString()
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    Map<String, String> params = new HashMap<>()
    // 公共参数
    params.putAll(buildCommonParams(request.getTransactionId(), request.getTransactionTime()))
    // 业务参数
    params.put("service", "payCheck")
    params.put("outTradeNo", request.getTransactionId())
    params.put("deviceNo", channel.getChannelAccessConfig().getTerminalId())
    JSONObject resultJSON = doRequest("query", params, false)
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setTransactionState(mapTransactionState(resultJSON.getString("orderStatus")))
    response.setTpTransactionId(resultJSON.getString("orderNo"))
    response.setWarningMessage(resultJSON.getString("respMsg"))
    return response
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    Map<String, String> params = new HashMap<>()
    // 公共参数
    params.putAll(buildCommonParams(request.getTransactionId(), request.getTransactionTime()))
    // 业务参数
    params.put("service", "payCancel")
    params.put("outTradeNo", request.getRelatedTransactionId())
    params.put("deviceNo", channel.getChannelAccessConfig().getTerminalId())
    JSONObject resultJSON = doRequest("cancel", params, true)
    ChannelCancelResponse response = new ChannelCancelResponse()
    response.setTransactionState(mapRefundTransactionState(resultJSON.getString("orderStatus")))
    response.setTpTransactionId(resultJSON.getString("orderNo"))
    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    Map<String, String> params = new HashMap<>()
    // 公共参数
    params.putAll(buildCommonParams(request.getTransactionId(), request.getTransactionTime()))
    // 业务参数
    params.put("service", "payRefund")
    params.put("outTradeNo", request.getRelatedTransactionId())
    params.put("outRefundNo", request.getTransactionId())
    params.put("refundAmt", parseFenToYuan(request.getAmount()))
    params.put("deviceNo", channel.getChannelAccessConfig().getTerminalId())
    JSONObject resultJSON = doRequest("refund", params, true)
    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionState(mapRefundTransactionState(resultJSON.getString("orderStatus")))
    response.setWarningMessage(resultJSON.getString("respMsg"))
    response.setTpTransactionId(resultJSON.getString("refundNo"))
    return response
  }

  private JSONObject doRequest(String method, Map<String, String> params, boolean reserveData) {
    String methodFullName = getMethodFullName(method)
    String unSignContent = CertHelper.sortParams(params)
    String signContent = CertUtil.getSign4Merchant(unSignContent, channel.getChannelAccessConfig().getPrivateKey())
    LoggerUtil.info("HTTP {0} ->{1} is sending message: {2}.",channel.getChannelAccessConfig().getGatewayUrl() ,methodFullName, signContent)
    JSONObject json = null;
    HttpURLConnection conn = null;
    // 设置上下文（出入报文）
    Timestamp reqTime = cn.hexcloud.commons.utils.DateUtil.getNowTimeStamp()
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, unSignContent)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
    }
    try {
      URL url = new URL(channel.getChannelAccessConfig().getGatewayUrl())
      conn = (HttpURLConnection) url.openConnection()
      conn.setRequestMethod("POST")
      conn.setDoInput(true)
      conn.setDoOutput(true)
      conn.setConnectTimeout(10000)
      conn.setReadTimeout(10000)
      conn.setRequestProperty("Content-Type", "text/html")
      conn.connect()
      Timestamp respTime = cn.hexcloud.commons.utils.DateUtil.getNowTimeStamp()
      OutputStream out = conn.getOutputStream()
      PrintWriter pw = new PrintWriter(new OutputStreamWriter(out, "UTF-8"))
      pw.print(signContent)
      pw.flush()
      InputStream is = conn.getInputStream()
      BufferedReader br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
      String result = br.readLine()
      LoggerUtil.info("{0} received message: {1}.", methodFullName, result)
      // 是否包含&=格数的数据
      if (!result.contains("=")) {
        throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, result)
      }
      // 将=&格数的数据转换为json
      json = parseDataToJson(result)
      LoggerUtil.info("{0} received json message: {1}.", methodFullName, json.toJSONString())
      // 设置上下文（出入报文）
      if (reserveData) {
        TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, result)
        TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
      }
    } catch (SocketTimeoutException ex) {
      LoggerUtil.error("Error occurred during POST request: ", ex)
      JSONObject result = new JSONObject()
      result.put("respCode", SUCCESS_CODE)
      result.put("respMsg", "请求超时")
      result.put("orderStatus", "2")// 返回处理中
      return result
    } catch (Exception e) {
      LoggerUtil.error("Error occurred during POST request: ", e)
      throw e
    } finally {
      if (conn != null) {
        conn.disconnect()
      }
    }
    // 接口调用错误码
    String respCode = json.getString("respCode")
    String respMsg = json.getString("respMsg")
    if (SUCCESS_CODE != respCode) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, respMsg)
    }
    // 业务错误码
    String errCode = json.getString("errCode")
    String errMsg = json.getString("errMsg")
    if (StrUtil.isNotBlank(errCode)) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errMsg)
    }

    return json
  }

  private static JSONObject parseDataToJson(String data) {
    JSONObject json = new JSONObject();
    if (data != null && !data.isEmpty()) {
      String[] keyValuePairs = data.split("&");

      for (String pair : keyValuePairs) {
        String[] keyValue = pair.split("=");
        if (keyValue.length == 2) {
          json.put(keyValue[0], keyValue[1]);
        } else if (keyValue.length == 1) {
          json.put(keyValue[0], "");
        }
      }
    }
    return json
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }
}
