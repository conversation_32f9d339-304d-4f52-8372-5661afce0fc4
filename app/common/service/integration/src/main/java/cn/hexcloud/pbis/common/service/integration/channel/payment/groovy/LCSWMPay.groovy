package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils
import org.springframework.util.DigestUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp
import java.text.DateFormat
import java.text.SimpleDateFormat

class LCSWMPay extends AbstractExternalChannelModule implements PaymentModule {

  private static final String PAY_VER = "100"
  private static final String PAY_TYPE = "010" // 微信
  private static final String SUCCESS_CODE = "01"
  private static final String PENDING_CODE = "03"
  private static final Map<String, String> METHOD_MAP

  LCSWMPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  static {
    METHOD_MAP = new HashMap<>()
    METHOD_MAP.put("create", "015") // 小程序支付
    METHOD_MAP.put("query", "020")
    METHOD_MAP.put("refund", "030")
    METHOD_MAP.put("cancel", "040")
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new LinkedHashMap<>()
    // 实时交易终端IP(银联侧风控主要依据，请真实填写),该字段不参与签名
    bizParams.put("terminal_ip", request.getRemoteIp())
    // 终端流水号，填写商户系统的订单号，不可重复
    bizParams.put("terminal_trace", request.getTransactionId())
    // 终端交易时间，yyyyMMddHHmmss，全局统一时间格式
    bizParams.put("terminal_time", parseTimestamp(request.getTransactionTime()))
    // 金额，单位分
    bizParams.put("total_fee", request.getAmount())
    // 传商户自己的小程序appid，微信支付时此参数必传，但不参与签名。（即获取open_id所使用的appid）
    bizParams.put("sub_appid", channel.getChannelAccessConfig().getAppId())
    // 用户标识（微信openid，支付宝user_id），必须传入，但不参与签名，通过官方接口获得
    bizParams.put("open_id", request.getPayer())
    // 订单描述（官方订单详情里显示为商品名称）禁止使用+，空格，/，?，%，#，&，=这几类特殊符号
    bizParams.put("order_body", request.getDescription())
    // 支付结果回调通知地址
    bizParams.put("notify_url", getNotificationUrl())

    // 发起请求
    List<String> signExcludes = new ArrayList<>()
    signExcludes.add("sub_appid")
    signExcludes.add("open_id")
    signExcludes.add("terminal_ip")
    signExcludes.add("order_body")
    signExcludes.add("notify_url")
    JSONObject resultJSON = doRequest("create", bizParams, true, signExcludes)

    // 解析并返回结果
    ChannelCreateResponse response = new ChannelCreateResponse()
    response.setChannel(request.getChannel())
    response.setPayMethod(PayMethod.WX_PAY)
    response.setTpTransactionId(resultJSON.getString("out_trade_no"))
    String resultCode = resultJSON.getString("result_code")
    if (SUCCESS_CODE == resultCode) {
      response.setPrePayId(getPrepayId(resultJSON.getString("package_str")))
      response.setPrePaySign(resultJSON.getString("paySign"))
      response.setPackStr("timeStamp=" + resultJSON.getString("timeStamp") + "&nonceStr=" + resultJSON.getString("nonceStr"))
    }
    return response
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getFullMethodName("pay"))
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new LinkedHashMap<>()
    List<String> signExcludes = new ArrayList<>()
    bizParams.put("terminal_trace", request.getTransactionId())
    bizParams.put("terminal_time", parseTimestamp(request.getTransactionTime()))
    bizParams.put("out_trade_no", request.getTpTransactionId())
    bizParams.put("pay_trace", request.getTransactionId())
    bizParams.put("pay_time", parseTimestamp(request.getTransactionTime()))
    signExcludes.add("pay_trace")
    signExcludes.add("pay_time")

    // 发起请求
    JSONObject resultJSON = doRequest("query", bizParams, false, signExcludes)

    // 解析并返回结果
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setChannel(request.getChannel())
    response.setPayMethod(PayMethod.WX_PAY)
    response.setTransactionId(resultJSON.getString("pay_trace"))
    response.setTpTransactionId(resultJSON.getString("out_trade_no"))
    response.setRealAmount(resultJSON.getBigDecimal("total_fee"))
    response.setTransactionState(mapTransactionState(resultJSON.getString("trade_state")))
    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new LinkedHashMap<>()
    bizParams.put("terminal_trace", request.getTransactionId())
    bizParams.put("terminal_time", parseTimestamp(request.getTransactionTime()))
    bizParams.put("refund_fee", request.getAmount())
    bizParams.put("out_trade_no", request.getRelatedTPTransactionId())

    // 发起请求
    JSONObject resultJSON = doRequest("refund", bizParams, true, null)

    // 解析并返回结果
    String resultCode = resultJSON.getString("result_code")
    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(resultJSON.getString("out_refund_no"))
    response.setRealAmount(request.getAmount())
    if (SUCCESS_CODE == resultCode || PENDING_CODE == resultCode) {
      response.setTransactionState(TransactionState.PENDING)
    } else {
      response.setTransactionState(TransactionState.FAILED)
    }
    return response
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new LinkedHashMap<>()
    bizParams.put("terminal_trace", request.getTransactionId())
    bizParams.put("terminal_time", parseTimestamp(request.getTransactionTime()))
    bizParams.put("out_trade_no", request.getRelatedTPTransactionId())

    List<String> signExcludes = new ArrayList<>()
    signExcludes.add("out_trade_no")

    // 发起请求
    JSONObject resultJSON = doRequest("cancel", bizParams, true, signExcludes)

    // 解析并返回结果
    String resultCode = resultJSON.getString("result_code")
    ChannelCancelResponse response = new ChannelCancelResponse()
    response.setRealAmount(request.getAmount())
    if (SUCCESS_CODE == resultCode) {
      response.setTransactionState(TransactionState.SUCCESS)
    } else if (PENDING_CODE == resultCode) {
      response.setTransactionState(TransactionState.PENDING)
    } else {
      response.setTransactionState(TransactionState.FAILED)
    }
    return response
  }

  @Override
  ChannelNotificationResponse payNotify(HttpServletRequest request) {
    // 对请求进行验签
    String callbackJSONStr = request.getParameter("payload")
    Map<String, String> notifyMessage = JSONObject.parseObject(callbackJSONStr, Map.class)
    if (!isValidSignature(notifyMessage)) {
      // 验签失败
      throw new CommonException(ServiceError.INVALID_SIGNATURE)
    }

    // 返回结果
    ChannelNotificationResponse response = new ChannelNotificationResponse()
    String response2ThirdParty = "{\"return_code\":\"01\",\"return_msg\":\"success\"}"
    response.setResponse(response2ThirdParty)
    ChannelPayResponse payResponse = new ChannelQueryResponse()
    payResponse.setTransactionId(notifyMessage.get("terminal_trace"))
    payResponse.setTpTransactionId(notifyMessage.get("out_trade_no"))
    payResponse.setRealAmount(notifyMessage.get("total_fee") as BigDecimal)
    payResponse.setExtendedParams(notifyMessage.get("attach"))
    payResponse.setPayMethod(PayMethod.WX_PAY)
    String resultCode = notifyMessage.get("result_code")
    if (SUCCESS_CODE == resultCode) {
      payResponse.setTransactionState(TransactionState.SUCCESS)
    } else if (PENDING_CODE == resultCode) {
      payResponse.setTransactionState(TransactionState.PENDING)
    } else {
      payResponse.setTransactionState(TransactionState.FAILED)
    }
    response.setPayResponse(payResponse)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, callbackJSONStr)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, response2ThirdParty)

    return response
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    // 加工数据并得到签名原文
    StringBuilder sb = new StringBuilder()
    for (Map.Entry<String, String> entry : rawMessage) {
      sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&")
    }
    String dataBeforeSign = sb.append("access_token=").append(channel.getChannelAccessConfig().getAccessKey()).toString()

    // 签名并返回签名信息
    return DigestUtils.md5DigestAsHex(dataBeforeSign.getBytes(StandardCharsets.UTF_8)).toLowerCase()
  }

  @Override
  boolean isValidSignature(Map<String, String> unverifiedMessage) {
    Map<String, String> map = new LinkedHashMap<>()
    String tpSignature = unverifiedMessage.get("key_sign")
    map.put("return_code", unverifiedMessage.get("return_code"))
    map.put("return_msg", unverifiedMessage.get("return_msg"))
    map.put("result_code", unverifiedMessage.get("result_code"))
    map.put("pay_type", unverifiedMessage.get("pay_type"))
    map.put("user_id", unverifiedMessage.get("user_id"))
    map.put("merchant_name", unverifiedMessage.get("merchant_name"))
    map.put("merchant_no", unverifiedMessage.get("merchant_no"))
    map.put("terminal_id", unverifiedMessage.get("terminal_id"))
    map.put("terminal_trace", unverifiedMessage.get("terminal_trace"))
    map.put("terminal_time", unverifiedMessage.get("terminal_time"))
    map.put("total_fee", unverifiedMessage.get("total_fee"))
    map.put("end_time", unverifiedMessage.get("end_time"))
    map.put("out_trade_no", unverifiedMessage.get("out_trade_no"))
    map.put("channel_trade_no", unverifiedMessage.get("channel_trade_no"))
    map.put("attach", unverifiedMessage.get("attach"))
    String signature = getSignature(map)
    return tpSignature == signature
  }

  private static TransactionState mapTransactionState(String tpTransactionState) {
    TransactionState transactionState
    switch (tpTransactionState) {
      case "NOTPAY":
        transactionState = TransactionState.WAITING
        break
      case "SUCCESS":
        transactionState = TransactionState.SUCCESS
        break
      case "PAYERROR":
      case "NOPAY":
        transactionState = TransactionState.FAILED
        break
      case "CLOSED":
        transactionState = TransactionState.CLOSED
        break
      case "REVOKED":
        transactionState = TransactionState.CANCELED
        break
      case "USERPAYING":
        transactionState = TransactionState.PENDING
        break
      case "REFUND":
        transactionState = TransactionState.REFUNDED
        break
      default:
        transactionState = TransactionState.UNKNOWN
    }
    return transactionState
  }

  private static String parseTimestamp(Date hexDate) {
    DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss")
    return df.format(hexDate)
  }

  private JSONObject doRequest(String method, Map<String, Object> bizParams, boolean reserveData, List<String> signExcludes) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

    // 请求参数
    Map<String, Object> body = new LinkedHashMap<>()
    body.put("pay_ver", PAY_VER)
    body.put("pay_type", PAY_TYPE)
    body.put("service_id", METHOD_MAP.get(method))
    body.put("merchant_no", channelAccessConfig.getMerchantId())
    body.put("terminal_id", channelAccessConfig.getTerminalId())
    body.putAll(bizParams)

    // 签名
    Map<String, String> rawMessage = new LinkedHashMap<>()
    for (Map.Entry<String, Object> entry : body) {
      if (null != signExcludes && signExcludes.size() > 0 && signExcludes.contains(entry.getKey())) {
        continue
      }
      rawMessage.put(entry.getKey(), entry.getValue().toString())
    }
    body.put("key_sign", getSignature(rawMessage))

    // 发起HTTP请求
    String requestUrl = channelAccessConfig.getProperty(method + "_url")
    String methodFullName = getFullMethodName(method)
    String bodyJSON = JSON.toJSONString(body)
    LoggerUtil.info("{0} is sending message to: {1}, body: {2}.", methodFullName, requestUrl, bodyJSON)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result = HttpUtil.doPost(requestUrl, bodyJSON)
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyJSON)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
    String errorCode = resultJSON.getString("return_code")
    String errorMessage = resultJSON.getString("return_msg")
    if (errorCode != SUCCESS_CODE) {
      // 请求失败
      LoggerUtil.error("{0} is failed, message: {1}.", null, methodFullName, errorMessage)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }

    return resultJSON
  }

  private String getNotificationUrl() {
    String notificationUrl = channel.getChannelAccessConfig().getProperty("notification_url")
    if (StringUtils.isNotBlank(notificationUrl)) {
      String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
      String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
      String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId
      return notificationUrl.endsWith("/") ? notificationUrl + path : notificationUrl + "/" + path
    }

    return null
  }

  private static String getPrepayId(String packageStr) {
    String keyword = "prepay_id"
    if (StringUtils.isBlank(packageStr) || !packageStr.contains(keyword)) {
      return null
    }
    return packageStr.substring(packageStr.indexOf(keyword) + keyword.length() + 1)
  }

}
