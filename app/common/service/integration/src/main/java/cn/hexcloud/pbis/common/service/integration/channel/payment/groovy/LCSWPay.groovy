package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.service.integration.metadata.Entity
import cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest
import cn.hexcloud.pbis.common.service.integration.metadata.MetadataClient
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils
import org.apache.http.conn.ConnectTimeoutException
import org.springframework.util.DigestUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp
import java.text.DateFormat
import java.text.SimpleDateFormat

class LCSWPay extends AbstractExternalChannelModule implements PaymentModule {

  private static final String PAY_VER = "100"
  private static final String PAY_TYPE = "000"
  private static final String SUCCESS_CODE = "01"
  private static final String PENDING_CODE = "03"
  private static final String[] FAILURE_CODE = ["02", "99"]
  private static final Map<String, String> METHOD_MAP
  private static final Integer CONN_REQUEST_TIMEOUT = 10000

  LCSWPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  static {
    METHOD_MAP = new HashMap<>()
    METHOD_MAP.put("pay", "010")
    METHOD_MAP.put("query", "020")
    METHOD_MAP.put("refund", "030")
    METHOD_MAP.put("cancel", "040")
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("create"))
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new LinkedHashMap<>()
    List<String> signExcludes = new ArrayList<>()
    bizParams.put("terminal_trace", request.getTransactionId())
    bizParams.put("terminal_time", parseTimestamp(request.getTransactionTime()))
    bizParams.put("auth_no", request.getPayCode())
    bizParams.put("total_fee", request.getAmount())
    bizParams.put("terminal_ip", request.getRemoteIp())
    bizParams.put("order_body", getOrderBody())

    // 发起请求
    signExcludes.add("terminal_ip")
    signExcludes.add("order_body")
    JSONObject resultJSON = doRequest("pay", bizParams, true, signExcludes)

    // 解析并返回结果
    String resultCode = resultJSON.getString("result_code")
    ChannelPayResponse response = new ChannelPayResponse()
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(resultJSON.getString("out_trade_no"))
    response.setChannel(request.getChannel())
    response.setPayMethod(mapPayMethod(resultJSON.getString("pay_type")))
    response.setRealAmount(request.getAmount())
    response.setExtendedParams(request.getExtendedParams())
    response.setWarningMessage(resultJSON.getString("return_msg"))
    if (SUCCESS_CODE == resultCode) {
      response.setTransactionState(TransactionState.SUCCESS)
    } else if (PENDING_CODE == resultCode) {
      response.setTransactionState(TransactionState.PENDING)
    } else {
      response.setTransactionState(TransactionState.FAILED)
    }
    return response
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new LinkedHashMap<>()
    List<String> signExcludes = new ArrayList<>()
    bizParams.put("terminal_trace", request.getTransactionId())
    bizParams.put("terminal_time", parseTimestamp(request.getTransactionTime()))
    bizParams.put("out_trade_no", request.getTpTransactionId())
    bizParams.put("pay_trace", request.getTransactionId())
    bizParams.put("pay_time", parseTimestamp(request.getTransactionTime()))
    signExcludes.add("pay_trace")
    signExcludes.add("pay_time")

    // 发起请求
    JSONObject resultJSON = doRequest("query", bizParams, false, signExcludes)

    // 解析并返回结果
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setChannel(request.getChannel())
    response.setPayMethod(mapPayMethod(resultJSON.getString("pay_type")))
    response.setTransactionId(resultJSON.getString("pay_trace"))
    response.setTpTransactionId(resultJSON.getString("out_trade_no"))
    response.setRealAmount(resultJSON.getBigDecimal("total_fee"))
    response.setWarningMessage(resultJSON.getString("return_msg"))
    response.setTransactionState(mapTransactionState(resultJSON.getString("trade_state")))
    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new LinkedHashMap<>()
    bizParams.put("terminal_trace", request.getTransactionId())
    bizParams.put("terminal_time", parseTimestamp(request.getTransactionTime()))
    bizParams.put("refund_fee", request.getAmount())
    bizParams.put("out_trade_no", request.getRelatedTPTransactionId())

    // 发起请求
    JSONObject resultJSON = doRequest("refund", bizParams, true, null)

    // 解析并返回结果
    String resultCode = resultJSON.getString("result_code")
    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(resultJSON.getString("out_refund_no"))
    response.setRealAmount(request.getAmount())
    response.setWarningMessage(resultJSON.getString("return_msg"))
    if (SUCCESS_CODE == resultCode || PENDING_CODE == resultCode) {
      response.setTransactionState(TransactionState.PENDING)
    } else {
      response.setTransactionState(TransactionState.FAILED)
    }
    return response
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new LinkedHashMap<>()
    bizParams.put("terminal_trace", request.getTransactionId())
    bizParams.put("terminal_time", parseTimestamp(request.getTransactionTime()))
    bizParams.put("out_trade_no", request.getRelatedTPTransactionId())

    List<String> signExcludes = new ArrayList<>()
    signExcludes.add("out_trade_no")

    // 发起请求
    JSONObject resultJSON = doRequest("cancel", bizParams, true, signExcludes)

    // 解析并返回结果
    String resultCode = resultJSON.getString("result_code")
    ChannelCancelResponse response = new ChannelCancelResponse()
    response.setRealAmount(request.getAmount())
    response.setWarningMessage(resultJSON.getString("return_msg"))
    if (SUCCESS_CODE == resultCode) {
      response.setTransactionState(TransactionState.SUCCESS)
    } else if (PENDING_CODE == resultCode) {
      response.setTransactionState(TransactionState.PENDING)
    } else {
      response.setTransactionState(TransactionState.FAILED)
    }
    return response
  }

  @Override
  ChannelNotificationResponse payNotify(HttpServletRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("payNotify"))
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    // 加工数据并得到签名原文
    StringBuilder sb = new StringBuilder()
    for (Map.Entry<String, String> entry : rawMessage) {
      sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&")
    }
    String dataBeforeSign = sb.append("access_token=").append(channel.getChannelAccessConfig().getAccessKey()).toString()

    // 签名并返回签名信息
    return DigestUtils.md5DigestAsHex(dataBeforeSign.getBytes(StandardCharsets.UTF_8)).toLowerCase()
  }

  @Override
  boolean isValidSignature(Map<String, String> unverifiedMessage) {
    Map<String, String> map = new LinkedHashMap<>(unverifiedMessage)
    String tpSignature = map.get("key_sign")
    map.remove("sign")
    String signature = getSignature(map)
    return tpSignature == signature
  }

  private static TransactionState mapTransactionState(String tpTransactionState) {
    TransactionState transactionState
    switch (tpTransactionState) {
      case "NOTPAY":
        transactionState = TransactionState.WAITING
        break
      case "SUCCESS":
        transactionState = TransactionState.SUCCESS
        break
      case "PAYERROR":
      case "NOPAY":
        transactionState = TransactionState.FAILED
        break
      case "CLOSED":
        transactionState = TransactionState.CLOSED
        break
      case "REVOKED":
        transactionState = TransactionState.CANCELED
        break
      case "USERPAYING":
        transactionState = TransactionState.PENDING
        break
      case "REFUND":
        transactionState = TransactionState.REFUNDED
        break
      default:
        transactionState = TransactionState.UNKNOWN
    }
    return transactionState
  }

  private static PayMethod mapPayMethod(String tpPayMethod) {
    PayMethod payMethod
    switch (tpPayMethod) {
      case "010":
        payMethod = PayMethod.WX_PAY
        break
      case "020":
        payMethod = PayMethod.ALI_PAY
        break
      case "060":
        payMethod = PayMethod.QQ_PAY
        break
      case "080":
        payMethod = PayMethod.JD_PAY
        break
      case "090":
        payMethod = PayMethod.KOUBEI
        break
      case "100":
        payMethod = PayMethod.BEST_PAY
        break
      case "110":
        payMethod = PayMethod.UNION_PAY
        break
      default:
        payMethod = PayMethod.OTHERS
    }
    return payMethod
  }

  private static String parseTimestamp(Date hexDate) {
    DateFormat df = new SimpleDateFormat("yyyyMMddHHmmss")
    return df.format(hexDate)
  }

  private JSONObject doRequest(String method, Map<String, Object> bizParams, boolean reserveData, List<String> signExcludes) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

    // 请求参数
    Map<String, Object> body = new LinkedHashMap<>()
    body.put("pay_ver", PAY_VER)
    body.put("pay_type", PAY_TYPE)
    body.put("service_id", METHOD_MAP.get(method))
    body.put("merchant_no", channelAccessConfig.getMerchantId())
    body.put("terminal_id", channelAccessConfig.getTerminalId())
    body.putAll(bizParams)

    // 签名
    Map<String, String> rawMessage = new LinkedHashMap<>()
    for (Map.Entry<String, Object> entry : body) {
      if (null != signExcludes && signExcludes.size() > 0 && signExcludes.contains(entry.getKey())) {
        continue
      }
      rawMessage.put(entry.getKey(), entry.getValue().toString())
    }
    body.put("key_sign", getSignature(rawMessage))

    // 发起HTTP请求
    String methodFullName = getMethodFullName(method)
    String bodyJSON = JSON.toJSONString(body)
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result
    try {
      result = HttpUtil.doPost(channelAccessConfig.getProperty(method + "_url"), bodyJSON, null, CONN_REQUEST_TIMEOUT, CONN_REQUEST_TIMEOUT, CONN_REQUEST_TIMEOUT)
    } catch (ConnectTimeoutException e) {
      LoggerUtil.error("{0} is failed, message: {1}.", e, methodFullName, e.getMessage())
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Connection timeout.")
    }
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyJSON)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
    String errorCode = resultJSON.getString("return_code")
    String errorMessage = resultJSON.getString("return_msg")
    if (errorCode != SUCCESS_CODE) {
      // 请求失败
      LoggerUtil.error("{0} is failed, message: {1}.", null, methodFullName, errorMessage)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }

    return resultJSON
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }

  private static String getOrderBody() {
    String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
    if (StringUtils.isBlank(storeId)) {
      return null
    }

    MetadataClient metadataClient = SpringContextUtil.bean(MetadataClient.class)
    Entity storeEntity = metadataClient.getMetadata(GetEntityByIdRequest.newBuilder()
            .setSchemaName("STORE")
            .setId(new Long(storeId))
            .build())
    if (null == storeEntity) {
      return null
    }

    return null == storeEntity.getFields().getFieldsMap().get("name") ? null : storeEntity.getFields().getFieldsMap().get("name").getStringValue()
  }

}
