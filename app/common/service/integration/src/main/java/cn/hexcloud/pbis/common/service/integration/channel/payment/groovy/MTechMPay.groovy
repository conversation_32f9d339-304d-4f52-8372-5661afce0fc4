package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.NotificationType
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.PaymentHttpUtil
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.commons.lang3.StringUtils
import org.apache.http.NameValuePair
import org.apache.http.message.BasicNameValuePair

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.sql.Timestamp
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class MTechMPay extends AbstractExternalChannelModule implements PaymentModule {

    private final String secureKey

    // 指客户端和服务器建立连接的timeout
    private static final Integer CONN_TIME_OUT = 10000
    private static final String CHARSET = "UTF-8"
    private static final String PAY_VER = "5.0"

    MTechMPay(ExternalChannel channel) {
        super(channel)
        this.secureKey = channel.getChannelAccessConfig().getAccessKey()
    }

    @Override
    protected String getSignModuleName() {
        return this.getModuleName()
    }

    @Override
    String getModuleName() {
        return "Payment"
    }

    private String getMethodFullName(String method) {
        return channel.getChannelCode() + "." + getModuleName() + "." + method
    }

    @Override
    // 建单
    ChannelCreateResponse create(ChannelCreateRequest request) {
        String methodFullName = getMethodFullName("create")
        ChannelCreateResponse response = new ChannelCreateResponse()

        ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
        String targetUrl = channelAccessConfig.getGatewayUrl() + "/MPay/MerchantPay.jsp"

        // 构建表单参数
        String storeId = ServiceContext.get(ContextKeyConstant.STORE_ID) // store id provided by mert side
        String merchantId = channelAccessConfig.getMerchantId()
        String merchantTid = channelAccessConfig.getTerminalId()
        String orderNum = request.getOrderNo()
        String transactionId = request.getTransactionId()
        String datetime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")).toString()
        String amt = centToYuan(request.getAmount().toString())
        String currency = request.getCurrency()
        String accountType = ""
        String paymentMethod = "27"// 支付方式自选"0",八达通pc选"19",mobile选"27"
        String locale = "zh_TW"
        String notifyUrl = getNotificationUrl()//通知server-to-server,状态推进
        String customizedData = ""
        String extraField1 = ""
        String extraField2 = ""
        String extraField3 = ""
        String salt = genSalt()
        String secureKey = secureKey

        // extendedParams,默认不传
        String extendedParams = request.getExtendedParams()
        // 检查 exJson 是否为 null，如果是则给它赋值一个默认的空 JSONObject
        JSONObject exJson = JSON.parseObject(extendedParams)
        if (exJson == null) {
            exJson = new JSONObject()
        }

        // 手动设置 oid 参数
        LoggerUtil.info("oid: {0}", transactionId)
        // platform平台
        LoggerUtil.info("platform: {0}", exJson.getString("platform"))
        String returnUrl = getReturnUrl(transactionId, exJson.getString("platform"))
        // returnUrl
        LoggerUtil.info("returnUrl: {0}", returnUrl)
        // remark 备注：格式为"门店｜取餐号"
        LoggerUtil.info("store_code: {0}", exJson.getString("store_code"))
        LoggerUtil.info("ticket_no: {0}", exJson.getString("ticket_no"))
        customizedData = exJson.getString("store_code") + "|" + exJson.getString("ticket_no")
        LoggerUtil.info("remark: {0}", customizedData)

        // orderNo -> transactionId
        orderNum = transactionId
        String hash = genHashValue(genCreateOderPlainText(salt, accountType, amt, currency, customizedData, datetime, "", extraField1, extraField2, extraField3, locale, merchantTid, merchantId, notifyUrl, orderNum, paymentMethod, returnUrl, storeId, secureKey))
        String html = buildHtmlWithForm(targetUrl, merchantId, storeId, merchantTid, orderNum, datetime, amt, "", currency, paymentMethod, locale, returnUrl, notifyUrl, accountType, customizedData, extraField1, extraField2, extraField3, salt, hash)

        LoggerUtil.info("{0} received message: \n{1}.", methodFullName, html)

        response.setPrePayId(orderNum)//预支付单号，不知道填啥,就填单号
        response.setChannel(request.getChannel())

        // 把要请求的完整页面,丢给上游PC/H5浏览器去加载
        response.setPackStr(html)
        response.setPayCode(paymentMethod)
        return response
    }

    @Override
    // 查交易流水
    ChannelQueryResponse query(ChannelQueryRequest request) {
        String methodFullName = getMethodFullName("query")

        ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
        String targetUrl = channelAccessConfig.getGatewayUrl() + "/MPayJWS/TxnEnquiry.jsp"

        // 构建表单参数
        String storeId = ServiceContext.get(ContextKeyConstant.STORE_ID) // store id provided by mert side
        String merchantId = channelAccessConfig.getMerchantId()
        String merchantTid = channelAccessConfig.getTerminalId()
//        String orderNum = request.getOrderNo()
        String orderNum = request.getTransactionId()

        String salt = genSalt()
        String secureKey = secureKey

        String hash = genHashValue(genEnquiryPlainText(salt, merchantTid, merchantId, orderNum, secureKey))

        // 统一请求
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("version", PAY_VER)
        bizParams.put("merchantid", merchantId)
        bizParams.put("merchant_tid", merchantTid)
        bizParams.put("ordernum", orderNum)
        bizParams.put("salt", salt)
        bizParams.put("hash", hash)

        // send query
        LoggerUtil.info("{0} send message: \n{1}.", methodFullName, bizParams.toString())

        Map<String, String> resultMap = doRequest("query", bizParams, targetUrl, false)

        // 解析并返回结果
        LoggerUtil.info("{0} received message: \n{1}.", methodFullName, resultMap.toString())

        ChannelQueryResponse response = new ChannelQueryResponse()
        response.setTransactionState(mapPayNotifyState(resultMap.get("paymethod"), resultMap.get("rspcode")))
        response.setTransactionId(resultMap.get("ordernum"))
        response.setTpTransactionId(resultMap.get("ref"))
        response.setRealAmount(yuanToCent(resultMap.get("amt")))
        response.setPayMethod(PayMethod.EOctopus)

        return response
    }

    @Override
    // 关单
    ChannelCancelResponse cancel(ChannelCancelRequest request) {
        String methodFullName = getMethodFullName("cancel")

//        ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
//        String targetUrl = channelAccessConfig.getGatewayUrl() + "/MPayJWS/TxnVoid.jsp"
//
//        // 构建表单参数
//        String storeId = ServiceContext.get(ContextKeyConstant.STORE_ID) // store id provided by mert side
//        String merchantId = channelAccessConfig.getMerchantId()
//        String merchantTid = channelAccessConfig.getTerminalId()
//
//        String orderNum = randomStr(20)
//        String originOrderNum = request.getRelatedTransactionId()
//        String originOrderRef = request.getRelatedTPTransactionId()
//        String payMethod = request.getPayCode()
//
//        String salt = genSalt()
//        String secureKey = secureKey
//
//        String hash = genHashValue(genVoidPlainText(salt, merchantTid, merchantId, orderNum, originOrderNum, originOrderRef, payMethod, secureKey))
//
//        // 统一请求
//        Map<String, Object> bizParams = new HashMap<>()
//        bizParams.put("version", PAY_VER)
//        bizParams.put("merchantid", merchantId)
//        bizParams.put("merchant_tid", merchantTid)
//        bizParams.put("ordernum", orderNum)
//        bizParams.put("org_ordernum", originOrderNum)
//        bizParams.put("org_ref", originOrderRef)
//        bizParams.put("paymethod", payMethod)
//        bizParams.put("salt", salt)
//        bizParams.put("hash", hash)
//        Map<String, String> resultMap = doRequest("query", bizParams, targetUrl, false)
//
//        // 解析并返回结果
//        LoggerUtil.info("{0} received message: \n{1}.", methodFullName, resultMap.toString())
//
//        ChannelCancelResponse response = new ChannelCancelResponse()
//        response.setTransactionState(mapPayNotifyState("", resultMap.get("rspcode")))
//        response.setTpTransactionId(resultMap.get("ref"))
//        response.setRealAmount(yuanToCent(resultMap.get("amt")))

        // 如果mpay不支持八达通渠道的关单，这里就写死假的，永远成功
        ChannelCancelResponse response = new ChannelCancelResponse()
        response.setTransactionState(TransactionState.SUCCESS)
        return response
    }

    @Override
    ChannelRefundResponse refund(ChannelRefundRequest request) {
        String methodFullName = getMethodFullName("refund")
        ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
        String targetUrl = channelAccessConfig.getGatewayUrl() + "/MPayJWS/TxnRefund.jsp"

        // 构建表单参数
        String storeId = ServiceContext.get(ContextKeyConstant.STORE_ID) // store id provided by mert side
        String merchantId = channelAccessConfig.getMerchantId()
        String merchantTid = channelAccessConfig.getTerminalId()

        String orderNum = randomStr(20)
        String originOrderNum = request.getRelatedTransactionId()
        String originOrderRef = request.getRelatedTPTransactionId()
        String payMethod = request.getPayCode()
        String amount = centToYuan(request.getAmount().toString())

        String salt = genSalt()
        String secureKey = secureKey

        String hash = genHashValue(genRefundPlainText(salt, amount, merchantTid, merchantId, orderNum, originOrderNum, originOrderRef, payMethod, secureKey))

        // 统一请求
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("version", PAY_VER)
        bizParams.put("amount", amount)
        bizParams.put("merchantid", merchantId)
        bizParams.put("merchant_tid", merchantTid)
        bizParams.put("ordernum", orderNum)
        bizParams.put("org_ordernum", originOrderNum)
        bizParams.put("org_ref", originOrderRef)
        bizParams.put("paymethod", payMethod)
        bizParams.put("salt", salt)
        bizParams.put("hash", hash)

        // send refund
        LoggerUtil.info("{0} send message: \n{1}.", methodFullName, bizParams.toString())

        Map<String, String> resultMap = doRequest("refund", bizParams, targetUrl, false)

        // 解析并返回结果
        LoggerUtil.info("{0} received message: \n{1}.", methodFullName, resultMap.toString())

//        {
//            "merchantid": 1100880,
//            "merchant_tid": "001",
//            "ordernum": "4ffeda496ab14f0faf7f",
//            "ref": "190000154170",
//            "amt": 0.6,
//            "currency": "HKD",
//            "rspcode": 100,
//            "paymethod": 19,
//            "remark": "Initial Status",
//            "salt": "hjd5ig7r6t0oyf76",
//            "hash": "A19D7C3CFB6DE4AED81814A81E101D6A3F2B9E0A17EE95D74215F1E3EBFA4AA9"
//        }

        ChannelRefundResponse response = new ChannelRefundResponse()
        response.setTransactionState(mapPayNotifyState("", resultMap.get("rspcode")))
        response.setTransactionId(resultMap.get("ordernum"))
        response.setTpTransactionId(resultMap.get("ref"))
        response.setRealAmount(yuanToCent(resultMap.get("amt")))
        return response
    }


    // 映射回调状态
    private static TransactionState mapPayNotifyState(String payMethod, String code) {
        switch (payMethod) {
        // 第一大类：mpay之外支付渠道码：
            case "5":
                // "ALIPAY CN" + "PC"
            case "23":
                // "ALIPAY CN" + "MOBILE"
                switch (code) {
                    case "100": // Transaction successful.
                        return TransactionState.SUCCESS
                    case "8A2": // Transaction failed as closed/timeout in Financial Institution side
                        return TransactionState.CLOSED
                    case "8A1": // Initial status of transaction (Transaction not success)
                    case "899": // Unknown system error
                        return TransactionState.FAILED
                }
                break

            case "35":
                // "ALIPAY HK" + "PC"
            case "36":
                // "ALIPAY HK" + "MOBILE"
                switch (code) {
                    case "100": // Transaction successful.
                        return TransactionState.SUCCESS
                    case "C0A1": // Transaction is in progress
                    case "C1A2": // Transaction has been sent to Financial Institution
                        return TransactionState.WAITING
                    case "C0A2": // Transaction failed as closed/timeout in Financial Institution side
                    case "C005": // Transaction closed
                        return TransactionState.CLOSED
                    case "C1A1": // Initial status of transaction (Transaction not success)
                    case "C099": // Unknown system error
                    case "C001": // Transaction fail. Transaction not found in Alipay side
                    case "C003": // Transaction canceled
                        return TransactionState.FAILED
                }
                break

            case "41":
                // "WECHAT PAY" + "PC"
            case "42":
                // "WECHAT PAY" + "MOBILE"
                break
                switch (code) {
                    case "100": // Transaction successful.
                        return TransactionState.SUCCESS
                    case "C0A1": // Transaction is in progress
                        return TransactionState.WAITING
                    case "C003": // Transaction closed/timeout
                    case "C0A2": // Transaction failed as closed/timeout in Financial Institution side
                        return TransactionState.CLOSED
                    case "C001": // Transaction fail. Transaction already refunded in WeChat side
                    case "C004": // Transaction revoked
                    case "C006": // Payment failed (payment status failed to be returned by bank or other reasons)
                    case "C099": // Transaction fail with unknown reason
                        return TransactionState.FAILED
                }

            case "19":
                // "OCTOPUS" + "PC"
            case "27":
                // "OCTOPUS" + "MOBILE"
                switch (code) {
                    case "100": // Transaction successful.
                        return TransactionState.SUCCESS
                    case "C1A2": // Transaction has been sent to Financial Institution
                    case "CQ27": // Payment in progress
                    case "CQ28": // Payment request created
                        return TransactionState.WAITING
                    case "CQ22": // Timeout – no valid card presented (OOS may ask to user to present another card if the current card is invalid)
                        return TransactionState.CLOSED
                    case "C1A1": // Initial status of transaction (Transaction not success)
                    case "C0Q2": // Unrecognized merchant ID
                    case "C0Q3": // Invalid currency
                    case "C0Q4": // Invalid payment amount. i.e. negative
                    case "C0Q5": // Transaction amount exceeds Octopus’s maximum acceptable amount
                    case "C0Q6": // Invalid transaction type
                    case "C0Q7": // Unsupported language locale
                    case "C0Q9": // Duplicated Payment Gateway Reference No
                    case "CQ10": // Missing field
                    case "CQ13": // Invalid timeout value
                    case "CQ15": // Invalid Non HKD Payment Parameter
                    case "CQ21": // Unsupported Java version
                    case "CQ23": // User cancel transaction
                        return TransactionState.CANCELED
                    case "CQ24": // Invalid Payment Reference
                    case "CQ25": // Invalid Business Date
                    case "CQ26": // Payment request not found
                    case "CQ29": // Invalid Location
                    case "CQ30": // Invalid Request Time
                        return TransactionState.FAILED
                }
                break

                // 第二大类：默认payMethod="",则为mpay侧（上面剩余没命中的，会fallback到default)
            default:
                switch (code) {
                    case "100": // Transaction successful.
                        return TransactionState.SUCCESS
                    case "1A2": // Transaction has been sent to Financial Institution
                        return TransactionState.WAITING
                    case "1A3": // Transaction timeout at mPay
                    case "1A4": // Transaction timeout at Financial Institution
                    case "1A5": // Transaction cancel at mPay
                        return TransactionState.CANCELED
                    case "1A1": // Initial status of transaction (Transaction not success)
                    case "1A9": // Duplicate form submission
                    case "101": // Invalid certificate
                    case "102": // Data verification fail
                    case "103": // Amount should be greater than zero
                    case "104": // Invalid amount format
                    case "105": // Invalid currency
                    case "106": // Invalid order date
                    case "107": // Invalid merchant IP address
                    case "108": // Invalid merchant ID
                    case "109": // Invalid merchant order number
                    case "110": // Invalid system reference number
                    case "111": // Invalid return URL
                    case "112": // Invalid response code
                    case "113": // Daily maximum amount of transactions exceed
                    case "114": // Daily maximum number of transactions exceed
                    case "115": // Maximum amount of transaction exceed
                    case "116": // Merchant not exist
                    case "117": // Merchant not enable
                    case "118": // Merchant terminal not exist
                    case "119": // Merchant terminal not enable
                    case "120": // Duplicate form submission
                    case "121": // Connection error
                    case "122": // No merchant id found
                    case "123": // Invalid password
                    case "124": // Duplicate merchant order number
                    case "125": // Invalid Recurring Amount
                    case "126": // Invalid Recurring Period
                    case "127": // Invalid Recurring Time
                    case "128": // Invalid Recurring Number
                    case "129": // Merchant Not Allow to perform Recurring Transaction
                    case "130": // Merchant Not Allow to perform Recurring Transaction with Trial
                    case "131": // Merchant Not Allow to perform Recurring Transaction with 2 Trials
                    case "132": // Financial Institution is now temporary unavailable
                    case "133": // Invalid payment request
                    case "134": // Duplicate response for the same transaction
                    case "135": // Transaction amount less than minimum amount limit
                    case "136": // Duplicate payment request for the same transaction
                    case "137": // Decimal places exceed for transaction amount
                    case "187": // Security error. Please check the hash value.
                    case "196": // System error
                        return TransactionState.FAILED
                }
        }
        return TransactionState.WAITING
    }

    // 统一请求
    private Map<String, String> doRequest(String method, Map<String, Object> bizParams, String requestUrl, boolean reserveData) {
        // 发起HTTP请求
        String methodFullName = getFullMethodName(method)
        if (StringUtils.isBlank(requestUrl)) {
            throw new CommonException(ServiceError.INVALID_CHANNEL_CONFIG)
        }

        // 请求参数
        Map<String, Object> body = new HashMap<>()
        body.putAll(bizParams)
        List<NameValuePair> requestBody = new ArrayList<>()
        for (Map.Entry<String, Object> entry : body.entrySet()) {
            requestBody.add(new BasicNameValuePair(entry.getKey(), URLEncoder.encode(entry.getValue().toString(), CHARSET)));
        }
        LoggerUtil.info("{0} is sending message to: {1}, body: {2}", methodFullName, requestUrl, requestBody.toString())
        Timestamp reqTime = DateUtil.getNowTimeStamp()
        // 发起HTTP请求
        byte[] result = null
        try {
            result = PaymentHttpUtil.doPost(requestUrl, requestBody, null, CONN_TIME_OUT, CONN_TIME_OUT, CONN_TIME_OUT)
        } catch (Exception e) {
            LoggerUtil.error("request mpay error", e)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "request mpay error,please try again later.")
        }

        Timestamp respTime = DateUtil.getNowTimeStamp()
        if (null == result) {
            LoggerUtil.error("{0} is failed with null result.", null, methodFullName)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
        }
        String resultJson = URLDecoder.decode(new String(result, CHARSET), CHARSET)

        // 设置上下文（出入报文）
        if (reserveData) {
            TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, requestBody)
            TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
            TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJson)
            TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
        }

        // 解析并返回结果
        LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJson)
        return getResultMap(resultJson)
    }

    private static Map<String, String> getResultMap(String resultJson) {
        try {
            // 将JSON字符串转换为Map<String, String>
            final ObjectMapper objectMapper = new ObjectMapper()
            // 将JSON字符串转换为List<Map<String, String>>
            List<Map<String, String>> resultList = objectMapper.readValue(resultJson, new TypeReference<List<Map<String, String>>>() {
            });
            // 假设我们只需要第一个元素的Map
            if (!resultList.isEmpty()) {
                return resultList.get(0);
            }
            throw new CommonException(ServiceError.INVALID_PARAM)
        } catch (IOException e) {
            // 处理异常，返回空的Map或者抛出运行时异常
            LoggerUtil.warn("getResultMap,json:{0},exception:{1}", resultJson, e.message)
            throw new CommonException(ServiceError.INVALID_PARAM)
        }
    }

    @Override
    ChannelNotificationResponse payNotify(HttpServletRequest request) {
//    ChannelNotificationResponse payNotify(String request) {
//        String payload = request
        String payload = request.getParameter("payload")
        String methodFullName = getMethodFullName("payNotify")

//        {
//            "ordernum": "3424071000",
//            "sysdatetime": "20240710171058",
//            "ref": "190000154033",
//            "amt": "1.23",
//            "currency": "HKD",
//            "settledate": "**************",
//            "rspcode": "100",
//            "customizeddata": "",
//            "authcode": "2024071017122044393",
//            "fi_post_dt": "**************",
//            "version": "5.0",
//            "merchantid": "1100880",
//            "storeid": "",
//            "merchant_tid": "001",
//            "paymethod": "19",
//            "tokenid": "",
//            "depositamt": "",
//            "accounttype": "",
//            "salt": "1xfxhe6lvgw3gp5q",
//            "hash": "CB1A67C6A7ED36EA14B11C2A90A2E58A2FAA26B7D09A07FF2CDD19F651010565"
//        }

//        {
//            "ordernum": "**********",
//            "sysdatetime": "**************",
//            "ref": "************",
//            "amt": "1.20",
//            "currency": "HKD",
//            "settledate": "**************",
//            "rspcode": "CQ23",
//            "customizeddata": "",
//            "authcode": "",
//            "fi_post_dt": "**************",
//            "version": "5.0",
//            "merchantid": "1100880",
//            "storeid": "",
//            "merchant_tid": "001",
//            "paymethod": "19",
//            "tokenid": "",
//            "depositamt": "",
//            "accounttype": "",
//            "salt": "rkrovcxf5bryduls",
//            "hash": "710B922D841629EAEFD000E74EB75D8BC8EA8C4DA0077DF39990DDC80A0DF7BD"
//        }


        LoggerUtil.info("{0} received message: {1}", methodFullName, payload)
        Map<String, String> unverifiedMessage = getUrlParamsMap(payload)
        LoggerUtil.info("{0} received message: \n{1}.", methodFullName, unverifiedMessage.toString())
        if (!isValidSignature(unverifiedMessage)) {
            // 验签失败
            LoggerUtil.warn("signature verify failed, payload: {0}", payload)
            throw new CommonException(ServiceError.INVALID_SIGNATURE)
        }

        // 解析返回结果集
        ChannelNotificationResponse response = new ChannelNotificationResponse()
        ChannelPayResponse payResponse = new ChannelPayResponse()
        // transactionId
        payResponse.setTransactionId(unverifiedMessage.get("ordernum"))
        // tpTransactionId
        payResponse.setTpTransactionId(unverifiedMessage.get("ref"))
        // 通知类型: 目前只接付款流水回调，退款返回实时结果
        payResponse.setNotificationType(NotificationType.PAY)
        // 实际支付金额
        payResponse.setRealAmount(yuanToCent(unverifiedMessage.get("amt")))
        // 消息通知状态
        String statusCode = unverifiedMessage.get("rspcode")
        // 支付方式
        String payMethod = unverifiedMessage.get("paymethod")
        payResponse.setTransactionState(mapPayNotifyState(payMethod, statusCode))
        // 真实支付方式
        payResponse.setPayMethod(PayMethod.EOctopus)

        // 设定payResponse
        response.setPayResponse(payResponse)
        // 设置上下文（出入报文）
        TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, payload)
        return response
    }

    // 获取回调地址
    private String getNotificationUrl() {
        String notificationUrl = channel.getChannelAccessConfig().getProperty("notification_url")
        if (StringUtils.isNotBlank(notificationUrl)) {
            String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
            String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
            String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId
            return notificationUrl.endsWith("/") ? notificationUrl + path : notificationUrl + "/" + path
        }
        return null
    }

    // 获取回跳地址
    private String getReturnUrl(String oid, String platform) {
        String returnUrl = channel.getChannelAccessConfig().getProperty("return_url")
        if (StringUtils.isNotBlank(returnUrl)) {
            String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
            String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
            String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId + "/" + "oid=" + oid + "&platform=" + platform
            return returnUrl.endsWith("/") ? returnUrl + path : returnUrl + "/" + path
        }
        return null
    }

    // 解析x-www-form-urlencoded参数
    private static Map<String, String> getUrlParamsMap(String urlParams) {
        Map<String, String> paramsMap = new HashMap<>()
        if (StringUtils.isEmpty(urlParams)) {
            return paramsMap
        }
        String[] urlParamArr = urlParams.split("&")
        for (String urlParam : urlParamArr) {
            String[] paramPair = urlParam.split("=")
            String key = paramPair[0]
            String value = paramPair.length > 1 ? URLDecoder.decode(paramPair[1], StandardCharsets.UTF_8.toString()) : ""
            paramsMap.put(key, value)
        }
        return paramsMap
    }

    // 元->分
    private static BigDecimal yuanToCent(String yuan) {
        if (yuan == null) {
            return null
        }
        return new BigDecimal(yuan) * 100
    }

    // 分->元
    private static BigDecimal centToYuan(String cent) {
        if (cent == null) {
            return null
        }
        BigDecimal centDecimal = new BigDecimal(cent)
        return centDecimal.divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP)
    }

    @Override
    boolean isValidSignature(Map<String, String> unverifiedMessage) {
        String notificationHash = unverifiedMessage.get("hash")
        if (StringUtils.isBlank(notificationHash)) {
            return false
        }
        String secureKey = secureKey
        String accountType = unverifiedMessage.get("accounttype")
        String amt = unverifiedMessage.get("amt")
        String authCode = unverifiedMessage.get("authcode")
        String cardNum = "" // 模拟数据报文中没有 cardNum
        String currency = unverifiedMessage.get("currency")
        String customizedData = unverifiedMessage.get("customizeddata")
        String depositAmt = unverifiedMessage.get("depositamt")
        String fiPostDt = unverifiedMessage.get("fi_post_dt")
        String merchantTid = unverifiedMessage.get("merchant_tid")
        String merchantId = unverifiedMessage.get("merchantid")
        String orderNum = unverifiedMessage.get("ordernum")
        String payMethod = unverifiedMessage.get("paymethod")
        String ref = unverifiedMessage.get("ref")
        String rspCode = unverifiedMessage.get("rspcode")
        String settleDate = unverifiedMessage.get("settledate")
        String storeId = unverifiedMessage.get("storeid")
        String sysDateTime = unverifiedMessage.get("sysdatetime")
        String tokenId = unverifiedMessage.get("tokenid")
        String salt = unverifiedMessage.get("salt")

        String plainText = genNotifyPlainText(secureKey, accountType, amt, authCode, cardNum, currency, customizedData, depositAmt, fiPostDt, merchantTid, merchantId, orderNum, payMethod, ref, rspCode, settleDate, storeId, sysDateTime, tokenId, salt)
        String dataAfterSign = genHashValue(plainText)
        LoggerUtil.info("dataAfterSign:{0},givenHash: {1}.", dataAfterSign, notificationHash)
        return notificationHash == dataAfterSign
    }

    private String buildHtmlWithForm(String mpayPaymentURL, String merchantId, String storeId, String merchantTid, String orderNum, String datetime, String amt, String depositAmt, String currency, String payMethod, String locale, String returnUrl, String notifyUrl, String accountType, String customizedData, String extraField1, String extraField2, String extraField3, String salt, String hash) {
        StringBuilder html = new StringBuilder()
        html.append("<html><head></head><body>")
        html.append("<form method=\"post\" action=\"").append(mpayPaymentURL).append("\" id=\"").append(channel.getChannelCode()).append("\" name=\"").append(channel.getChannelCode()).append("\">")
        html.append("<input type=\"hidden\" id=\"version\" name=\"version\" value=\"5.0\" />")
        html.append("<input type=\"hidden\" id=\"merchantid\" name=\"merchantid\" value=\"").append(merchantId).append("\" />")
        html.append("<input type=\"hidden\" id=\"storeid\" name=\"storeid\" value=\"").append(storeId).append("\" />")
        html.append("<input type=\"hidden\" id=\"merchant_tid\" name=\"merchant_tid\" value=\"").append(merchantTid).append("\" />")
        html.append("<input type=\"hidden\" id=\"ordernum\" name=\"ordernum\" value=\"").append(orderNum).append("\" />")
        html.append("<input type=\"hidden\" id=\"datetime\" name=\"datetime\" value=\"").append(datetime).append("\" />")
        html.append("<input type=\"hidden\" id=\"amt\" name=\"amt\" value=\"").append(amt).append("\" />")
        html.append("<input type=\"hidden\" id=\"depositamt\" name=\"depositamt\" value=\"\" />")
        html.append("<input type=\"hidden\" id=\"currency\" name=\"currency\" value=\"").append(currency).append("\" />")
        html.append("<input type=\"hidden\" id=\"paymethod\" name=\"paymethod\" value=\"").append(payMethod).append("\" />")
        html.append("<input type=\"hidden\" id=\"locale\" name=\"locale\" value=\"").append(locale).append("\" />")
        html.append("<input type=\"hidden\" id=\"returnurl\" name=\"returnurl\" value=\"").append(returnUrl).append("\" />")
        html.append("<input type=\"hidden\" id=\"notifyurl\" name=\"notifyurl\" value=\"").append(notifyUrl).append("\" />")
        html.append("<input type=\"hidden\" id=\"accounttype\" name=\"accounttype\" value=\"").append(accountType).append("\" />")
        html.append("<input type=\"hidden\" id=\"customizeddata\" name=\"customizeddata\" value=\"").append(customizedData).append("\" />")
        html.append("<input type=\"hidden\" id=\"extrafield1\" name=\"extrafield1\" value=\"").append(extraField1).append("\" />")
        html.append("<input type=\"hidden\" id=\"extrafield2\" name=\"extrafield2\" value=\"").append(extraField2).append("\" />")
        html.append("<input type=\"hidden\" id=\"extrafield3\" name=\"extrafield3\" value=\"").append(extraField3).append("\" />")
        html.append("<input type=\"hidden\" id=\"salt\" name=\"salt\" value=\"").append(salt).append("\" />")
        html.append("<input type=\"hidden\" id=\"hash\" name=\"hash\" value=\"").append(hash).append("\" />")
        html.append("</form>")
        html.append("<script type=\"text/javascript\">")
        html.append("document.getElementById('").append(channel.getChannelCode()).append("').submit();");
        html.append("</script>")
        html.append("</body></html>")
        return html.toString()
    }

    private static String genHashValue(String plainText) {
        return hashString(plainText, "SHA-256")
    }

    private static String genCreateOderPlainText(String salt, String accountType, String amt, String currency, String customizeddata, String datetime, String depositamt, String extrafield1, String extrafield2, String extrafield3, String locale, String merchant_tid, String merchantid, String notifyurl, String ordernum, String paymentmethod, String returnurl, String storeid, String securekey) {
        String delr = ";"
        String plainText = salt + delr + accountType + amt + currency + customizeddata + datetime + depositamt + extrafield1 + extrafield2 + extrafield3 + locale + merchant_tid + merchantid + notifyurl + ordernum + paymentmethod + returnurl + storeid + delr + securekey
        return plainText
    }

    private static String genNotifyPlainText(String secureKey, String accountType, String amt, String authCode, String cardNum, String currency, String customizedData, String depositAmt, String fiPostDt, String merchantTid, String merchantId, String orderNum, String payMethod, String ref, String rspCode, String settleDate, String storeId, String sysDateTime, String tokenId, String salt) {
        String delr = ";"
        String plainText = secureKey + delr + accountType + amt + authCode + cardNum + currency + customizedData + depositAmt + fiPostDt + merchantTid + merchantId + orderNum + payMethod + ref + rspCode + settleDate + storeId + sysDateTime + tokenId + delr + salt
        return plainText
    }

    private static String genEnquiryPlainText(String salt, String merchant_tid, String merchantid, String ordernum, String securekey) {
        String delr = ";"
        String plainText = salt + delr + merchant_tid + merchantid + ordernum + delr + securekey
        return plainText
    }

    private static String genVoidPlainText(String salt, String merchant_tid, String merchantid, String ordernum, String org_ordernum, String org_ref, String paymethod, String securekey) {
        String delr = ";"
        String plainText = salt + delr + merchant_tid + merchantid + ordernum + org_ordernum + org_ref + paymethod + delr + securekey
        return plainText
    }

    private static String genRefundPlainText(String salt, String amount, String merchant_tid, String merchantid, String ordernum, String org_ordernum, String org_ref, String paymethod, String securekey) {
        String delr = ";"
        String plainText = salt + delr + amount + merchant_tid + merchantid + ordernum + org_ordernum + org_ref + paymethod + delr + securekey
        return plainText
    }

    private static String hashString(String plainText, String hashType) {
        String hashCode = ""
        try {
            MessageDigest md = MessageDigest.getInstance(hashType)
            byte[] hashByte = md.digest(plainText.getBytes("UTF-8"))
            hashCode = byte2hex(hashByte)
        } catch (Exception exception) {
            LoggerUtil.warn("hashing failed, exception: {0}", exception.getMessage())
        }
        return hashCode
    }

    private static String byte2hex(byte[] bytes) {
        StringBuilder sb = new StringBuilder(bytes.length * 2)
        for (int i = 0; i < bytes.length; ++i) {
            sb.append("0123456789ABCDEF".charAt((bytes[i] & 240) >> 4))
            sb.append("0123456789ABCDEF".charAt((bytes[i] & 15) >> 0))
        }
        return sb.toString()
    }

    private static String genSalt() {
        int length = 16
        String salt = ""
        StringBuilder tmp = new StringBuilder()

        char ch
        for (ch = '0'; ch <= '9'; ++ch) {
            tmp.append(ch)
        }

        for (ch = 'a'; ch <= 'z'; ++ch) {
            tmp.append(ch)
        }

        char[] symbols = tmp.toString().toCharArray()
        Random random = new Random()
        char[] buf = new char[length]

        for (int idx = 0; idx < buf.length; ++idx) {
            buf[idx] = symbols[random.nextInt(symbols.length)]
        }

        salt = new String(buf)
        return salt
    }

    private static String randomStr(int length) {
        return UUID.randomUUID().toString().replaceAll("-", "").substring(0, length);
    }
}


