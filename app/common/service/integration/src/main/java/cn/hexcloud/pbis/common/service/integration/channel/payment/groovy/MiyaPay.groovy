package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Commodity
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Promotion
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.XmlUtil
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils
import org.springframework.util.CollectionUtils
import org.springframework.util.DigestUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp

class MiyaPay extends AbstractExternalChannelModule implements PaymentModule {

  private static final String SUCCESS_CODE = "SUCCESS"
  private static final Map<String, String> METHOD_MAP

  MiyaPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  static {
    METHOD_MAP = new HashMap<>()
    METHOD_MAP.put("create", "F")
    METHOD_MAP.put("pay", "A")
    METHOD_MAP.put("query", "B")
    METHOD_MAP.put("refund", "C")
    METHOD_MAP.put("cancel", "E")
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new LinkedHashMap<>()
    bizParams.put("A12", "1") // 支付渠道，1-微信;3-支付宝
    bizParams.put("B1", request.getTransactionId()) // 商户订单号
    bizParams.put("B3", request.getDescription()) // 手机小票标题
    bizParams.put("B4", request.getAmount()) // 金额，单位：分
    bizParams.put("B13", getNotificationUrl()) // 通知地址，接收异步通知回调地址

    // 发起请求
    Map<String, String> resultMap = doRequest("create", bizParams, true)

    // 解析并返回结果
    ChannelCreateResponse response = new ChannelCreateResponse()
    response.setPrePayId(resultMap.get("C26"))
    response.setChannel(request.getChannel())
    response.setPayMethod(mapPayMethod(resultMap.get("C8")))
    return response
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new LinkedHashMap<>()
    bizParams.put("B1", request.getTransactionId()) // 商户订单号
    bizParams.put("B2", request.getPayCode()) // 支付码
    bizParams.put("B3", request.getOrderDescription()) // 手机小票标题
    bizParams.put("B4", request.getAmount()) // 金额，单位：分
    List<Commodity> commodities = request.getCommodities()
    if (!CollectionUtils.isEmpty(commodities)) {
      JSONArray commodityArr = new JSONArray()
      for (Commodity commodity : commodities) {
        JSONObject commodityObj = new JSONObject()
        commodityObj.put("goodsId", commodity.getCode())
        commodityObj.put("goodsName", commodity.getName())
        commodityObj.put("price", parseYuan(commodity.getPrice()))
        commodityObj.put("quantity", commodity.getQuantity().intValue())
        commodityArr.push(commodityObj)
      }
      // 商品信息，格式：[{商品编号 1，商品名称 1，单价 1，数量 1},{商品编号 2，商品名称 2，单价 2， 数量 2}......]
      bizParams.put("B5", commodityArr.toJSONString())
    }

    // 发起请求
    Map<String, String> resultMap = doRequest("pay", bizParams, true)

    // 解析并返回结果
    ChannelPayResponse response = new ChannelPayResponse()
    response.setTransactionState(mapTransactionState(resultMap.get("C2")))
    response.setChannel(request.getChannel())
    response.setPayMethod(mapPayMethod(resultMap.get("C8")))
    response.setPayer(resultMap.get("C9")) // 买家标识，支付宝返回buyer_logon_id, 微信返回openid
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(resultMap.get("C6"))
    response.setRealAmount(request.getAmount())
    response.setExtendedParams(request.getExtendedParams())
    if (response.getTransactionState() == TransactionState.FAILED && StringUtils.isNotBlank(resultMap.get("C4"))) {
      response.setWarning(true)
      response.setWarningMessage(resultMap.get("C4"))
    }
    response.setPromotions(parsePromotion(resultMap))

    return response
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("B1", request.getTransactionId()) // 商户订单号

    // 发起请求
    Map<String, String> resultMap = doRequest("query", bizParams, false)

    // 解析并返回结果
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setTransactionState(mapTransactionState(resultMap.get("C2")))
    response.setChannel(request.getChannel())
    response.setPayMethod(mapPayMethod(resultMap.get("C8")))
    response.setTransactionId(resultMap.get("C5"))
    response.setTpTransactionId(resultMap.get("C6"))
    response.setRealAmount(resultMap.get("C7") as BigDecimal)
    response.setPromotions(parsePromotion(resultMap))
    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("B1", request.getRelatedTransactionId()) // 原商户订单号
    bizParams.put("B2", request.getTransactionId()) // 退款单号
    bizParams.put("B4", request.getAmount()) // 退款金额

    // 发起请求
    Map<String, String> resultMap = doRequest("refund", bizParams, true)

    // 解析并返回结果
    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionId(resultMap.get("C7"))
    response.setRealAmount(resultMap.get("C9") as BigDecimal)
    response.setTransactionState("REFUNDSUCCESS" == resultMap.get("C2") ? TransactionState.PENDING : TransactionState.FAILED)
    if (response.getTransactionState() == TransactionState.FAILED && StringUtils.isNotBlank(resultMap.get("C4"))) {
      response.setWarning(true)
      response.setWarningMessage(resultMap.get("C4"))
    }
    return response
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("B1", request.getRelatedTransactionId()) // 原商户订单号

    // 发起请求
    Map<String, String> resultMap = doRequest("cancel", bizParams, true)

    // 解析并返回结果
    ChannelCancelResponse response = new ChannelCancelResponse()
    response.setTpTransactionId(resultMap.get("C6"))
    response.setRealAmount(resultMap.get("C10") as BigDecimal)
    response.setTransactionState("CANCELSUCCESS" == resultMap.get("C2") ? TransactionState.SUCCESS : TransactionState.FAILED)
    if (response.getTransactionState() == TransactionState.FAILED && StringUtils.isNotBlank(resultMap.get("C4"))) {
      response.setWarning(true)
      response.setWarningMessage(resultMap.get("C4"))
    }
    return response
  }

  @Override
  ChannelNotificationResponse payNotify(HttpServletRequest request) {
    // 对请求进行验签
    String callbackXMLStr = request.getParameter("payload")
    Map<String, String> resultMap = XmlUtil.xmlToMap(callbackXMLStr)
    if (!isValidSignature(resultMap)) {
      // 验签失败
      throw new CommonException(ServiceError.INVALID_SIGNATURE)
    }
    LoggerUtil.info("{0} received message: {1}.", getMethodFullName("payNotify"), callbackXMLStr)

    // 返回结果
    ChannelNotificationResponse response = new ChannelNotificationResponse()
    String response2ThirdParty = "<xml><D1>SUCCESS</D1></xml>"
    response.setResponse(response2ThirdParty)
    ChannelPayResponse payResponse = new ChannelQueryResponse()
    payResponse.setPayMethod(mapPayMethod(resultMap.get("C8")))
    payResponse.setTransactionId(resultMap.get("C5"))
    payResponse.setTpTransactionId(resultMap.get("C6"))
    payResponse.setRealAmount(resultMap.get("C7") as BigDecimal)
    if (SUCCESS_CODE != resultMap.get("C1")) {
      payResponse.setTransactionState(TransactionState.FAILED)
    } else {
      payResponse.setTransactionState(mapTransactionState(resultMap.get("C2")))
    }
    response.setPayResponse(payResponse)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, callbackXMLStr)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, response2ThirdParty)

    return response
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    StringBuilder sb = new StringBuilder()
    sb.append("&")
    for (Map.Entry<String, String> entry : rawMessage.entrySet()) {
      if (StringUtils.isNotBlank(entry.getValue())) {
        sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&")
      }
    }

    String dataBeforeSign = sb.append("KEY=").append(channel.getChannelAccessConfig().getAccessKey())
    return DigestUtils.md5DigestAsHex(dataBeforeSign.getBytes(StandardCharsets.UTF_8)).toUpperCase()
  }

  @Override
  boolean isValidSignature(Map<String, String> unverifiedMessage) {
    Map<String, String> map = new TreeMap<>(unverifiedMessage)
    String returnCode = map.get("C1")
    String tpSignature = map.get("C30")
    if (StringUtils.isBlank(tpSignature) && SUCCESS_CODE != returnCode) {
      return true
    }
    map.remove("C30")
    String signature = getSignature(map)
    return tpSignature == signature
  }

  private static TransactionState mapTransactionState(String tpTransactionState) {
    TransactionState transactionState
    switch (tpTransactionState) {
      case "PAYSUCCESS":
        transactionState = TransactionState.SUCCESS
        break
      case "PAYFAIL":
        transactionState = TransactionState.FAILED
        break
      case "PAYWAIT":
        transactionState = TransactionState.PENDING
        break
      case "PAYCANCEL":
        transactionState = TransactionState.REFUNDED
        break
      default:
        transactionState = TransactionState.UNKNOWN
    }
    return transactionState
  }

  private static PayMethod mapPayMethod(String tpPayMethod) {
    PayMethod payMethod
    switch (tpPayMethod) {
      case "1":
        payMethod = PayMethod.WX_PAY
        break
      case "3":
        payMethod = PayMethod.ALI_PAY
        break
      case "4":
        payMethod = PayMethod.BAIDU_PAY
        break
      case "5":
        payMethod = PayMethod.BEST_PAY
        break
      case "6":
        payMethod = PayMethod.QQ_PAY
        break
      case "E":
        payMethod = PayMethod.CMB
        break
      case "K":
        payMethod = PayMethod.ICBC
        break
      case "M":
        payMethod = PayMethod.CDBC
        break
      case "N":
        payMethod = PayMethod.UNION_PAY
        break
      case "S":
        payMethod = PayMethod.HUIFU
        break
      case "U":
        payMethod = PayMethod.CM_PAY
        break
      default:
        payMethod = PayMethod.OTHERS
    }
    return payMethod
  }

  private Map<String, String> getResultMap(String resultXML) {
    Map<String, String> resultMap = XmlUtil.xmlToMap(resultXML)
    if (!isValidSignature(resultMap)) {
      // 验签失败
      throw new CommonException(ServiceError.INVALID_SIGNATURE)
    }
    return resultMap
  }

  private String getNotificationUrl() {
    String notificationUrl = channel.getChannelAccessConfig().getProperty("notification_url")
    if (StringUtils.isNotBlank(notificationUrl)) {
      String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
      String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
      String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId
      return notificationUrl.endsWith("/") ? notificationUrl + path : notificationUrl + "/" + path
    }

    return null
  }

  private Map<String, String> doRequest(String method, Map<String, Object> bizParams, boolean reserveData) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

    // 请求参数
    Map<String, Object> body = new LinkedHashMap<>()
    Map<String, String> requestPart = new LinkedHashMap<>()
    requestPart.put("A1", "A") // 接口类型，默认 A-交易
    requestPart.put("A2", channelAccessConfig.getMerchantId()) // 商户号，米雅提供的商户号
    requestPart.put("A3", ServiceContext.getString(ContextKeyConstant.STORE_CODE)) // 门店账号，通常为商户门店号
    requestPart.put("A4", "0") // 设备号，通常为商户门店 pos 机编号
    requestPart.put("A5", "0") // 收银编号，通常为商户门店收银员编号
    requestPart.put("A6", METHOD_MAP.get(method)) // 操作类型，默认 A-支付
    requestPart.put("A7", "1.5") // 版本号，默认1.5
    requestPart.put("A10", "1.0") // 客户端版本号

    // 签名
    Map<String, String> rawMessage = new TreeMap<>(requestPart)
    Iterator<Map.Entry<String, Object>> iterator = bizParams.entrySet().iterator()
    while (iterator.hasNext()) {
      Map.Entry<String, Object> entry = iterator.next()
      rawMessage.put(entry.getKey(), entry.getValue().toString())
      if (entry.getKey().startsWith("A")) {
        requestPart.put(entry.getKey(), entry.getValue().toString())
        iterator.remove()
      }
    }
    requestPart.put("A8", getSignature(rawMessage))
    body.put("request", requestPart)
    body.put("data", bizParams)

    // 发起HTTP请求
    String methodFullName = getMethodFullName(method)
    String requestUrl = getRequestUrl()
    String bodyXML = XmlUtil.mapToXml(body, "xml")
    LoggerUtil.info("{0} is sending message to: {1}, body: {2}", methodFullName, requestUrl, bodyXML)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result = HttpUtil.doPost(requestUrl, bodyXML)
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed with null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultXML = new String(result, StandardCharsets.UTF_8)

    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyXML)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultXML)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }

    // 解析并返回结果
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultXML)
    Map<String, String> resultMap = getResultMap(resultXML)
    String returnCode = resultMap.get("C1")
    String returnMessage = resultMap.get("C4")
    if (returnCode != SUCCESS_CODE) {
      // 请求失败
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, methodFullName, returnCode, returnMessage)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, returnMessage)
    }

    return resultMap
  }

  private String getRequestUrl() {
    if (StringUtils.isNotBlank(channel.getChannelAccessConfig().getGatewayUrl())) {
      return channel.getChannelAccessConfig().getGatewayUrl()
    }
    return channel.channelAccessConfig.getProperty("gateway_url")
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }

  private static BigDecimal parseYuan(BigDecimal hexCloudAmount) {
    if (null == hexCloudAmount) {
      return null
    }
    return hexCloudAmount / 100
  }

  private static BigDecimal parseFen(BigDecimal tpAmount) {
    if (null == tpAmount) {
      return null
    }
    return tpAmount * 100
  }

  private static List<Promotion> parsePromotion(Map<String, String> resultMap) {
    // 口碑券优惠信息解析（C27字段）
    String c27Str = resultMap.get("C27")
    List<Promotion> promotions = new ArrayList<>()
    if (StringUtils.isNotBlank(c27Str)) {
      JSONArray c27Node = JSON.parseArray(c27Str)
      c27Node.forEach({ item ->
        JSONObject promotionJSON = (JSONObject) item
        Promotion promotion = new Promotion()
        promotion.setId(promotionJSON.getString("id"))
        promotion.setName(promotionJSON.getString("name"))
        promotion.setDiscount(parseFen(promotionJSON.getBigDecimal("amount")))
        promotion.setDiscountOnMerchant(parseFen(promotionJSON.getBigDecimal("merchant_contribute")))
        promotion.setDiscountOnOthers(parseFen(promotionJSON.getBigDecimal("other_contribute")))
        promotion.setUserPayAmount(parseFen(promotionJSON.getBigDecimal("purchase_buyer_contribute")))
        promotion.setMerchantPayAmount(parseFen(promotionJSON.getBigDecimal("purchase_merchant_contribute")))
        promotion.setPlatformPayAmount(parseFen(promotionJSON.getBigDecimal("purchase_ant_contribute")))
        promotions.add(promotion)
      })
    }
    return promotions
  }
}
