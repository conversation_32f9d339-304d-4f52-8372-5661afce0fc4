package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.RedisUtil
import cn.hexcloud.commons.utils.UUIDUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.NotificationType
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.ObjectUtils
import org.apache.commons.lang3.StringUtils
import org.apache.http.HttpEntityEnclosingRequest
import org.apache.http.client.methods.CloseableHttpResponse
import org.apache.http.client.methods.HttpGet
import org.apache.http.client.methods.HttpPost
import org.apache.http.client.methods.HttpPut
import org.apache.http.client.methods.HttpUriRequest
import org.apache.http.entity.StringEntity
import org.apache.http.impl.client.CloseableHttpClient
import org.apache.http.impl.client.HttpClients
import org.apache.http.util.EntityUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp
import java.util.concurrent.TimeUnit

/**
 * Mastercard Gateway支付
 * <pre>
 *      URL: https://ap-gateway.mastercard.com/api/documentation/integrationGuidelines/index.html?locale=zh_CN
 *      场景: H5、小程序APP
 * </pre>
 */
class MpgsMPay extends AbstractExternalChannelModule implements PaymentModule {

    private static final String API_VERSION = "100"
    private static final Map<String, String> URL_MAP
    private static final String PAY_INFO_PREFIX = "PBIS-PAYMENT-INFO:MPGSMPAY:"

    MpgsMPay(ExternalChannel channel) {
        super(channel)
    }

    @Override
    String getModuleName() {
        return "Payment"
    }

    static {
        URL_MAP = new HashMap<>()
        URL_MAP.put("verify", "verify_url") // 验证
        URL_MAP.put("checkout", "checkout_url") // 收银台
        URL_MAP.put("order", "order_url") // 查询Order
        URL_MAP.put("session", "session_url") // 查询Session
        URL_MAP.put("authorize", "authorize_url") // 认证URL
        URL_MAP.put("refund", "refund_url") // 退款
        URL_MAP.put("cancel", "cancel_url") // 取消
        URL_MAP.put("notification", "notification_url") // 回调
    }

    @Override
    ChannelCreateResponse create(ChannelCreateRequest request) {
        String method = "create"

        // 语言：默认繁体
        String locale = "zh-HK"
        String storeCode = ""
        String paymentMethod = ""
        String ticketNo = ""
        String extendParams = request.getExtendedParams()
        if (StringUtils.isNotEmpty(extendParams)) {
            JSONObject extendParamJSON = JSONObject.parseObject(extendParams)
            // 语言
            String language = extendParamJSON.getString("language")
            if (StringUtils.isNotEmpty(language)) {
                locale = language
            }

            // 取餐号
            ticketNo = extendParamJSON.getString("ticket_no")

            // 门店编码
            storeCode = extendParamJSON.getString("store_code")

            // 支付方式
            paymentMethod = extendParamJSON.getString("pay_method")
        }

        // 默认：港币
        String currencyCode = request.getCurrency()
        if (StringUtils.isEmpty(currencyCode)) {
            currencyCode = "HKD"
        }

        // 支付方式
        PayMethod payMethod
        if (paymentMethod.startsWithAny("MASTER", "master")) {
            payMethod = PayMethod.EMaster
        } else if (paymentMethod.startsWithAny("VISA", "visa")) {
            payMethod = PayMethod.EVisa
        } else if (paymentMethod.startsWithAny("mpgs")) {
            payMethod = PayMethod.OTHERS
        } else {
            throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "Param 'pay_method' only support 'VISA' or 'MASTERCARD'")
        }

        String orderNo = request.getOrderNo()

        String transactionId = request.getTransactionId()

        String amount = convertToYuan(request.getAmount()).toString()

        // 执行
        JSONObject resultJSON = visaOrMaster(method, orderNo, transactionId, currencyCode, amount, locale, storeCode, ticketNo)

        JSONObject sessionJSON = resultJSON.getJSONObject("session")
        if (ObjectUtils.isEmpty(sessionJSON)) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Session ID is null")
        }

        String sessionId = sessionJSON.getString("id")
        String checkUrl = getUrl("checkout", sessionId)
        String successIndicatorValue = resultJSON.getString("successIndicator")

        // 设置缓存
        String payInfoKey = PAY_INFO_PREFIX + transactionId
        Map<String, String> payInfoCache = new HashMap<>()
        payInfoCache.put("successIndicator", successIndicatorValue)
        payInfoCache.put("hasProcessed", "NOT_PROCESSED")
        payInfoCache.put("realAmount", amount)
        payInfoCache.put("payMethod", paymentMethod)
        RedisUtil.HashOps.hPutAll(payInfoKey, payInfoCache)
        RedisUtil.KeyOps.expire(payInfoKey, 1800, TimeUnit.SECONDS)

        // 正常业务结果
        ChannelCreateResponse response = new ChannelCreateResponse()
        response.setChannel(request.getChannel())
        response.setPayMethod(payMethod)
        response.setPrePayId(sessionId)
        response.setPackStr(checkUrl)
        return response
    }

    @Override
    ChannelPayResponse pay(ChannelPayRequest request) {
        // 解析结果集
        ChannelPayResponse response = new ChannelPayResponse()
        response.setChannel(request.getChannel())
        response.setTransactionId(request.getTransactionId())

        // 参数
        String payMethod = ""
        String storeCode = ""
        String ticketNo = ""
        String extendParams = request.getExtendedParams()
        if (StringUtils.isNotEmpty(extendParams)) {
            JSONObject extendParamJSON = JSONObject.parseObject(extendParams)

            // 门店编码
            storeCode = extendParamJSON.getString("store_code")

            // 取餐号
            ticketNo = extendParamJSON.getString("ticket_no")

            // 支付方式
            payMethod = extendParamJSON.getString("pay_method")
        }

        // 支付方式
        JSONObject resultJSON
        if (payMethod.startsWithAny("GooglePay", "googlePay", "google_pay", "google pay")) {
            resultJSON = googlePay(request, storeCode, ticketNo)
            response.setPayMethod(PayMethod.EGooglePay)
        } else if (payMethod.startsWithAny("ApplePay", "applePay", "apple_pay", "apple pay")) {
            resultJSON = applePay(request, storeCode, ticketNo)
            response.setPayMethod(PayMethod.EApplePay)
        } else {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Param 'pay_method' only support 'ApplePay' and 'GooglePay'")
        }

        // 响应
        JSONObject responseJSON = resultJSON.getJSONObject("response")
        String gatewayCode = responseJSON.getString("gatewayCode")

        String testOrProd = channel.getChannelAccessConfig().getAppKey()
        if ("TEST".equalsIgnoreCase(testOrProd) && "DECLINED".equalsIgnoreCase(gatewayCode)) {
            gatewayCode = "SUCCESS"
        }

        JSONObject sourceOfFundsJSON = resultJSON.getJSONObject("sourceOfFunds")
        if (sourceOfFundsJSON != null) {
            JSONObject providedJSON = sourceOfFundsJSON.getJSONObject("provided")
            JSONObject cardJSON = providedJSON.getJSONObject("card")
            String brand = cardJSON.getString("brand")
            String deviceSpecificNumber = cardJSON.getString("deviceSpecificNumber")
            Map<String, String> extendParamsMap = new HashMap<>()
            extendParamsMap.put("cardType", brand)
            extendParamsMap.put("cardNo", deviceSpecificNumber)
            response.setExtendedParams(JSONObject.toJSONString(extendParamsMap))

            if ("VISA".equalsIgnoreCase(brand)) {
                response.setPayMethod(PayMethod.EVisa)
            } else if ("MASTERCARD".equalsIgnoreCase(brand)) {
                response.setPayMethod(PayMethod.EMaster)
            }
        }

        String hasProcessed = "NOT_PROCESSED"
        if ("CAPTURED".equalsIgnoreCase(gatewayCode) || "APPROVED".equalsIgnoreCase(gatewayCode) || "SUCCESS".equalsIgnoreCase(gatewayCode)) {
            hasProcessed = "PROCESSED"
        }

        String transactionId = request.getTransactionId()

        // 设置缓存状态
        String payInfoKey = PAY_INFO_PREFIX + transactionId
        Map<String, String> payInfoCache = new HashMap<>()
        payInfoCache.put("hasProcessed", hasProcessed)
        payInfoCache.put("payMethod", payMethod)
        RedisUtil.HashOps.hPutAll(payInfoKey, payInfoCache)
        RedisUtil.KeyOps.expire(payInfoKey, 1800, TimeUnit.SECONDS)

        response.setTpTransactionId(transactionId)
        response.setTransactionState(mapTransactionState(gatewayCode))
        return response
    }

    // Visa或Master
    private JSONObject visaOrMaster(String method,
                                    String orderNo,
                                    String transactionId,
                                    String currencyCode,
                                    String amount,
                                    String locale,
                                    String storeCode,
                                    String ticketNo) {
        // 商户号
        String merchantId = channel.getChannelAccessConfig().getMerchantId()

        // 获取请求URL（checkout）
        String requestUrl = getUrl("verify", API_VERSION, merchantId)

        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("apiOperation", "INITIATE_CHECKOUT")
        bizParams.put("checkoutMode", "WEBSITE")

        // 请求参数-> interaction
        Map<String, Object> interaction = new HashMap<>()
        interaction.put("operation", "PURCHASE")
        interaction.put("returnUrl", getReturnUrl(orderNo, transactionId))
        interaction.put("redirectMerchantUrl", getCallBackUrl(locale, orderNo, "fail"))
        interaction.put("retryAttemptCount", 1)
        interaction.put("timeout", 1800)

        // 请求参数-> displayControl
        Map<String, Object> displayControl = new HashMap<>()
        displayControl.put("billingAddress", "HIDE")
        displayControl.put("customerEmail", "HIDE")
        displayControl.put("paymentTerms", "SHOW_IF_SUPPORTED")
        displayControl.put("shipping", "HIDE")
        interaction.put("displayControl", displayControl)

        // 请求参数-> interaction -> merchant
        Map<String, Object> merchant = new HashMap<>()
        merchant.put("name", channel.getChannelAccessConfig().getAppId()) // 商户名称
        interaction.put("merchant", merchant)
        bizParams.put("interaction", interaction)

        // 请求参数-> order
        Map<String, Object> order = new HashMap<>()
        order.put("id", transactionId)
        order.put("currency", currencyCode)
        order.put("amount", amount)
        order.put("description", storeCode + "|" + ticketNo)
        order.put("reference", storeCode + "|" + ticketNo)
        order.put("notificationUrl", getNotificationUrl())
        bizParams.put("order", order)

        // 请求Body（checkout）
        String requestBody = JSONObject.toJSONString(bizParams)

        // 获取Session
        return doRequest(method, "POST", requestUrl, requestBody, false)
    }

    // ApplePay（Direct Pay）
    private JSONObject applePay(ChannelPayRequest request, String storeCode, String ticketNo) {

        // orderId
        String orderId = request.getTransactionId()

        // 商户号
        String merchantId = channel.getChannelAccessConfig().getMerchantId()

        // paymentToken
        String paymentToken = ""
        String extendedParams = request.getExtendedParams()
        JSONObject extJSON = JSONObject.parseObject(extendedParams)
        if (ObjectUtils.isNotEmpty(extJSON)) {
            String paymentTokenStr = extJSON.getString("paymentToken")
            if (StringUtils.isNotEmpty(paymentTokenStr)) {
                JSONObject paymentTokenJSON = JSONObject.parseObject(paymentTokenStr)
                String tokenStr = paymentTokenJSON.getString("token")
                if (ObjectUtils.isNotEmpty(tokenStr)) {
                    paymentToken = tokenStr
                }
            }
        }

        // 币种，默认：港币
        String currencyCode = request.getCurrency()
        if (StringUtils.isEmpty(currencyCode)) {
            currencyCode = "HKD"
        }

        String transactionId = UUIDUtil.getUUID()

        // 获取请求URL
        String requestUrl = getUrl("authorize", API_VERSION, merchantId, orderId, transactionId)

        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("apiOperation", "PAY")

        Map<String, Object> order = new HashMap<>()
        order.put("amount", convertToYuan(request.getAmount()))
        order.put("currency", currencyCode)
        order.put("walletProvider", "APPLE_PAY")
        order.put("description", storeCode + "|" + ticketNo)
        order.put("reference", storeCode + "|" + ticketNo)
        bizParams.put("order", order)

        Map<String, Object> sourceOfFunds = new HashMap<>()
        sourceOfFunds.put("type", "CARD")

        Map<String, Object> provided = new HashMap<>()
        Map<String, Object> card = new HashMap<>()
        Map<String, Object> devicePayment = new HashMap<>()
        devicePayment.put("paymentToken", paymentToken)
        card.put("devicePayment", devicePayment)
        provided.put("card", card)
        sourceOfFunds.put("provided", provided)

        bizParams.put("sourceOfFunds", sourceOfFunds)

        Map<String, Object> transaction = new HashMap<>()
        transaction.put("source", "INTERNET")
        bizParams.put("transaction", transaction)

        // 请求Body
        String requestBody = JSONObject.toJSONString(bizParams)
        return doRequest("pay", "PUT", requestUrl, requestBody, false)
    }

    // GooglePay（Direct Pay）
    private JSONObject googlePay(ChannelPayRequest request, String storeCode, String ticketNo) {

        // orderId
        String orderId = request.getTransactionId()

        // 商户号
        String merchantId = channel.getChannelAccessConfig().getMerchantId()

        // paymentToken
        String paymentToken = ""
        String extendedParams = request.getExtendedParams()
        JSONObject extJSON = JSONObject.parseObject(extendedParams)
        if (ObjectUtils.isNotEmpty(extJSON)) {
            String paymentTokenStr = extJSON.getString("paymentToken")
            if (StringUtils.isNotEmpty(paymentTokenStr)) {
                JSONObject paymentTokenJSON = JSONObject.parseObject(paymentTokenStr)
                JSONObject paymentMethodData = paymentTokenJSON.getJSONObject("paymentMethodData")
                JSONObject tokenizationData = paymentMethodData.getJSONObject("tokenizationData")
                String tokenStr = tokenizationData.getString("token")
                if (ObjectUtils.isNotEmpty(tokenStr)) {
                    paymentToken = tokenStr
                }
            }
        }

        // 币种，默认：港币
        String currencyCode = request.getCurrency()
        if (StringUtils.isEmpty(currencyCode)) {
            currencyCode = "HKD"
        }

        String transactionId = UUIDUtil.getUUID()

        // 获取请求URL
        String requestUrl = getUrl("authorize", API_VERSION, merchantId, orderId, transactionId)

        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("apiOperation", "PAY")

        Map<String, Object> order = new HashMap<>()
        order.put("amount", convertToYuan(request.getAmount()))
        order.put("currency", currencyCode)
        order.put("walletProvider", "GOOGLE_PAY")
        order.put("description", storeCode + "|" + ticketNo)
        order.put("reference", storeCode + "|" + ticketNo)
        bizParams.put("order", order)

        Map<String, Object> sourceOfFunds = new HashMap<>()
        sourceOfFunds.put("type", "CARD")

        Map<String, Object> provided = new HashMap<>()
        Map<String, Object> card = new HashMap<>()
        Map<String, Object> devicePayment = new HashMap<>()
        devicePayment.put("paymentToken", paymentToken)
        card.put("devicePayment", devicePayment)
        provided.put("card", card)
        sourceOfFunds.put("provided", provided)

        bizParams.put("sourceOfFunds", sourceOfFunds)

        Map<String, Object> transaction = new HashMap<>()
        transaction.put("source", "INTERNET")
        bizParams.put("transaction", transaction)

        // 请求Body
        String requestBody = JSONObject.toJSONString(bizParams)
        return doRequest("pay", "PUT", requestUrl, requestBody, false)
    }

    @Override
    ChannelQueryResponse query(ChannelQueryRequest request) {
        String method = "query"

        String fullMethodName = getFullMethodName(method)

        String merchantId = channel.getChannelAccessConfig().getMerchantId()

        String orderId = request.getTransactionId()

        // 请求URL
        String orderReqUrl = getUrl("order", API_VERSION, merchantId, orderId)

        // 发起请求
        JSONObject orderResJSON = retryOrderQuery(method, orderReqUrl, 3)

        // 响应
        JSONObject errorJSON = orderResJSON.getJSONObject("error")
        String status = orderResJSON.getString("status")
        String orderResCode = orderResJSON.getString("result")

        // 失败直接抛出异常
        if ("ERROR".equalsIgnoreCase(orderResCode)) {
            String errorMsg = errorJSON.getString("explanation")
            if (errorMsg.contains("Unable to find order")) {
                // 还没拿到对应的参数，设置状态为等待
                status = "PENDING"
            } else {
                LoggerUtil.error("{0} fail: {1}", null, fullMethodName, errorMsg)
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMsg)
            }
        }

        // ApplePay & GooglePay，在测试环境直接成功
        String walletProvider = orderResJSON.getString("walletProvider")
        if ("APPLE_PAY".equalsIgnoreCase(walletProvider) || ("GOOGLE_PAY".equalsIgnoreCase(walletProvider))) {
            String testOrProd = channel.getChannelAccessConfig().getAppKey()
            if ("TEST".equalsIgnoreCase(testOrProd)) {
                status = "SUCCESS"
            }
        }

        ChannelQueryResponse response = new ChannelQueryResponse()
        JSONObject sourceOfFundsJSON = orderResJSON.getJSONObject("sourceOfFunds")
        if (sourceOfFundsJSON != null) {
            JSONObject providedJSON = sourceOfFundsJSON.getJSONObject("provided")
            JSONObject cardJSON = providedJSON.getJSONObject("card")
            String brand = cardJSON.getString("brand")
            String cardNo = cardJSON.getString("deviceSpecificNumber")
            if (StringUtils.isEmpty(cardNo)) {
                cardNo = cardJSON.getString("number")
            }
            Map<String, String> extendParamsMap = new HashMap<>()
            extendParamsMap.put("cardType", brand)
            extendParamsMap.put("cardNo", cardNo)
            response.setExtendedParams(JSONObject.toJSONString(extendParamsMap))

            if ("VISA".equalsIgnoreCase(brand)) {
                response.setPayMethod(PayMethod.EVisa)
            } else if ("MASTERCARD".equalsIgnoreCase(brand)) {
                response.setPayMethod(PayMethod.EMaster)
            }
        }

        // 从缓存中获取对应的处理状态，若缓存中的状态为“NOT_PROCESSED”，则获取当前查询的状态
        String payInfoKey = PAY_INFO_PREFIX + orderId
        String hasProcessed = RedisUtil.HashOps.hGet(payInfoKey, "hasProcessed")
        if ("PROCESSED".equalsIgnoreCase(hasProcessed)) {
            response.setTransactionState(TransactionState.SUCCESS)
        } else {
            response.setTransactionState(mapTransactionState(status))
        }

        response.setTransactionId(request.getTransactionId())
        response.setRealAmount(convertToFen(orderResJSON.getBigDecimal("amount")))
        return response
    }

    // 查询重试
    private JSONObject retryOrderQuery(String method, String orderReqUrl, int maxRetryCount) {
        JSONObject orderResJSON = doRequest(method, "GET", orderReqUrl, null, false)
        String orderResCode = orderResJSON.getString("result")

        // 达到次数直接返回
        if (maxRetryCount <= 0) {
            return orderResJSON
        }

        // 发生 not found 错误，重试一下
        if ("ERROR".equalsIgnoreCase(orderResCode)) {
            JSONObject errorJSON = orderResJSON.getJSONObject("error")
            String errorMsg = errorJSON.getString("explanation")
            if (errorMsg.contains("Unable to find order")) {
                Thread.sleep(2000)
                return retryOrderQuery(method, orderReqUrl, maxRetryCount - 1)
            }
        }

        // 状态为FAILED、FAILURE时，重试一遍
        String status = orderResJSON.getString("status")
        if ("FAILED".equalsIgnoreCase(status) || "FAILURE".equalsIgnoreCase(status)) {
            Thread.sleep(1000)
            return retryOrderQuery(method, orderReqUrl, maxRetryCount - 1)
        }

        return orderResJSON
    }

    // 状态转换
    private static TransactionState mapTransactionState(String tpTransactionState) {
        TransactionState transactionState
        switch (tpTransactionState) {
            case "PENDING":
                transactionState = TransactionState.WAITING
                break
            case "CANCELLED":
                transactionState = TransactionState.CANCELED
                break
            case "REFUNDED":
                transactionState = TransactionState.REFUNDED
                break
            case "CAPTURED":
                transactionState = TransactionState.SUCCESS
                break
            case "APPROVED":
                transactionState = TransactionState.SUCCESS
                break
            case "AUTHENTICATED":
                transactionState = TransactionState.WAITING
                break
            case "AUTHENTICATION_INITIATED":
                transactionState = TransactionState.WAITING
                break
            case "AUTHENTICATION_UNSUCCESSFUL":
                transactionState = TransactionState.WAITING
                break
            case "SUCCESS":
                transactionState = TransactionState.SUCCESS
                break
            case "FAILURE":
                transactionState = TransactionState.FAILED
                break
            default:
                transactionState = TransactionState.FAILED
        }
        return transactionState
    }

    @Override
    ChannelRefundResponse refund(ChannelRefundRequest request) {
        String method = "refund"

        String merchantId = channel.getChannelAccessConfig().getMerchantId()

        // 默认：港币
        String currencyCode = request.getCurrency()
        if (StringUtils.isEmpty(currencyCode)) {
            currencyCode = "HKD"
        }

        // 退货单号
        String transactionId = request.getTransactionId()

        // 原单号
        String relatedTransactionId = request.getRelatedTransactionId()

        // 请求URL
        String requestUrl = getUrl(method, API_VERSION, merchantId, relatedTransactionId, transactionId)

        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("apiOperation", "REFUND")

        // 请求参数-> interaction
        Map<String, Object> transaction = new HashMap<>()
        transaction.put("amount", convertToYuan(request.getAmount()))
        transaction.put("currency", currencyCode)
        bizParams.put("transaction", transaction)

        String requestBody = JSONObject.toJSONString(bizParams)

        // 执行
        JSONObject resultJSON = doRequest(method, "PUT", requestUrl, requestBody, false)

        JSONObject orderJSON = resultJSON.getJSONObject("order")

        String status = orderJSON.getString("status")

        // 解析结果集
        ChannelRefundResponse response = new ChannelRefundResponse()
        response.setTransactionId(request.getTransactionId())
        response.setRealAmount(convertToFen(resultJSON.getBigDecimal("amount")))
        response.setTransactionState(mapTransactionState(status))
        return response
    }

    @Override
    ChannelCancelResponse cancel(ChannelCancelRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "cancel")
    }

    @Override
    ChannelNotificationResponse payNotify(HttpServletRequest request) {
        String method = "payNotify"
        String fullMethod = getFullMethodName(method)

        String txnId = request.getParameter("txnId")
        String orderId = request.getParameter("orderId")
        String payResult = request.getParameter("payResult")
        String paymentMethod = request.getParameter("payMethod") == null ? "" : request.getParameter("payMethod")

        PayMethod payMethod
        if (paymentMethod.startsWithAny("MASTER", "master")) {
            payMethod = PayMethod.EMaster
        } else if (paymentMethod.startsWithAny("VISA", "visa")) {
            payMethod = PayMethod.EVisa
        } else {
            payMethod = PayMethod.OTHERS
        }

        if (StringUtils.isAllEmpty(txnId, orderId, payResult)) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Param 'txnId' and 'orderId' and 'payResult' must not be empty")
        }

        LoggerUtil.info("{0} received message: txnId={1}, orderId={2}, payResult={3}", fullMethod, txnId, orderId, payResult)

        // 解析返回结果集
        ChannelNotificationResponse response = new ChannelNotificationResponse()
        //（参数）返给第三方的报文
        String resp2ThirdParty = "{\"code\":\"200\",\"message\":\"success\"}"
        response.setResponse(resp2ThirdParty)
        ChannelPayResponse payResponse = new ChannelPayResponse()
        //（参数）通知类型
        payResponse.setNotificationType(NotificationType.PAY)
        //（参数）交易ID
        payResponse.setTransactionId(txnId)
        //（参数）第三方交易ID
        payResponse.setTpTransactionId(txnId)
        //（参数）支付状态
        payResponse.setTransactionState(mapTransactionState(payResult))
        //（参数）支付方式
        payResponse.setPayMethod(payMethod)
        response.setPayResponse(payResponse)
        return response
    }

    // 统一请求
    private JSONObject doRequest(String method, String requestMethod, String requestUrl, String requestBody, boolean reserveData) {
        String fullMethodName = getFullMethodName(method)
        LoggerUtil.info("{0} is sending request URL: {1}", fullMethodName, requestUrl)

        if (requestBody != null) {
            LoggerUtil.info("{0} is sending request body: {1}", fullMethodName, requestBody)
        }

        String merchantId = channel.getChannelAccessConfig().getMerchantId()
        String accessKey = channel.getChannelAccessConfig().getAccessKey()

        String credentials = "merchant." + merchantId + ":" + accessKey
        byte[] encodedCredentials = Base64.getEncoder().encode(credentials.getBytes(StandardCharsets.UTF_8))
        String authorization = "Basic " + new String(encodedCredentials, StandardCharsets.UTF_8)

        Map<String, String> headers = new HashMap<>()
        headers.put("Content-Type", "application/json")
        headers.put("Authorization", authorization)

        try {
            Timestamp reqTime = DateUtil.getNowTimeStamp()

            // 执行请求
            String response = executeRequest(requestMethod, requestUrl, requestBody, headers)
            if (null == response) {
                LoggerUtil.error("{0} is failed, null result.", null, fullMethodName)
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
            }
            Timestamp respTime = DateUtil.getNowTimeStamp()
            LoggerUtil.info("{0} received message: {1}", fullMethodName, response)

            // 设置上下文（出入报文）
            if (reserveData) {
                Map<String, Object> requestFullMessage = new HashMap<>()
                requestFullMessage.put("header", headers)
                requestFullMessage.put("body", response)
                TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
                TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSONObject.toJSONString(requestFullMessage))
                TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, response)
                TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
            }

            JSONObject resultJSON = JSONObject.parseObject(response)

            // 查询时，直接返回
            if ("query".equalsIgnoreCase(method)) {
                return resultJSON
            }

            String resultCode = resultJSON.getString("result")
            if (resultCode.equalsIgnoreCase("ERROR")) {
                JSONObject errorJSON = resultJSON.getJSONObject("error")
                String errorMsg = errorJSON.getString("explanation")
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMsg)
            }
            return resultJSON
        } catch (Exception ex) {
            LoggerUtil.error("Error {0} payment request: ", ex, fullMethodName)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, ex.getMessage())
        }
    }

    // 执行请求
    private String executeRequest(String requestMethod, String url, String content, Map<String, String> headers) {
        if (!("GET".equalsIgnoreCase(requestMethod) || "POST".equalsIgnoreCase(requestMethod) || "PUT".equalsIgnoreCase(requestMethod))) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Only support 'GET'、'POST' or 'PUT'")
        }

        CloseableHttpClient httpClient = HttpClients.createDefault()

        HttpUriRequest httpUriRequest = null
        if ("POST".equalsIgnoreCase(requestMethod)) {
            httpUriRequest = new HttpPost(url)
        } else if ("PUT".equalsIgnoreCase(requestMethod)) {
            httpUriRequest = new HttpPut(url)
        } else if ("GET".equalsIgnoreCase(requestMethod)) {
            httpUriRequest = new HttpGet(url)
        }

        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpUriRequest.setHeader(entry.getKey(), entry.getValue())
            }
        }

        if (content != null) {
            StringEntity entity = new StringEntity(content, StandardCharsets.UTF_8)
            ((HttpEntityEnclosingRequest) httpUriRequest).setEntity(entity)
        }

        CloseableHttpResponse response = httpClient.execute(httpUriRequest)
        return EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8)
    }

    // 获取URL
    private String getReturnUrl(String orderNo, String txnId) {
        String gatewayUrl = channel.getChannelAccessConfig().getGatewayUrl()
        if (StringUtils.isNotEmpty(gatewayUrl)) {
            String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
            String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
            String path = String.format("callback/pbis/payment-return/MpgsMpay/redirect/%s/%s?orderId=%s&txnId=%s", partnerId, storeId, orderNo, txnId)
            return gatewayUrl.endsWith("/") ? gatewayUrl + path : gatewayUrl + "/" + path
        }
        throw new CommonException(ServiceError.PAYMENT_CONFIG_NOT_EXISTS, "支付跳转GatewayUrl")
    }

    // 获取回调URL
    private String getCallBackUrl(String locale, String orderNo, String successOrFail) {
        String gatewayUrl = channel.getChannelAccessConfig().getGatewayUrl()
        if (StringUtils.isNotEmpty(gatewayUrl)) {
            String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
            String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
            String path = String.format("order/%s/%s/%s/submitted-order?orderId=%s&result=%s", partnerId, storeId, locale, orderNo, successOrFail)
            return gatewayUrl.endsWith("/") ? gatewayUrl + path : gatewayUrl + "/" + path
        }
        throw new CommonException(ServiceError.PAYMENT_CONFIG_NOT_EXISTS, "支付跳转GatewayUrl")
    }

    // 获取webhook通知地址
    private String getNotificationUrl() {
        String notificationUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get("notification"))
        if (StringUtils.isNotBlank(notificationUrl)) {
            String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
            String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
            String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId
            return notificationUrl.endsWith("/") ? notificationUrl + path : notificationUrl + "/" + path
        }
        return null
    }

    // 获取URL
    private String getUrl(String method, String... args) {
        String host = channel.getChannelAccessConfig().getProperty("third_part_host")
        String context = channel.getChannelAccessConfig().getProperty(URL_MAP.get(method))
        String urlParam = context.startsWith("/") ? host + context : host + "/" + context
        if (ObjectUtils.isEmpty(args)) {
            return urlParam
        }
        return String.format(urlParam, args)
    }

    // 分转成元
    private static BigDecimal convertToYuan(BigDecimal dollar) {
        if (dollar == null) {
            return null
        }
        return dollar.divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP)
    }

    // 元转换成分
    private static BigDecimal convertToFen(BigDecimal point) {
        if (point == null) {
            return null
        }
        return (point * new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP)
    }

}

