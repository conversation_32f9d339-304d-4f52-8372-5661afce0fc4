package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.ObjectUtils
import org.apache.commons.lang3.StringUtils
import org.apache.http.HttpEntityEnclosingRequest
import org.apache.http.client.methods.*
import org.apache.http.entity.StringEntity
import org.apache.http.impl.client.CloseableHttpClient
import org.apache.http.impl.client.HttpClients
import org.apache.http.util.EntityUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp

/**
 * Mastercard Gateway支付
 * <pre>
 *      URL: https://ap-gateway.mastercard.com/api/documentation/integrationGuidelines/index.html?locale=zh_CN
 *      场景: H5、小程序APP
 * </pre>
 */
class MpgsMPay02 extends AbstractExternalChannelModule implements PaymentModule {

    private static final String API_VERSION = "100"
    private static final Map<String, String> URL_MAP

    MpgsMPay02(ExternalChannel channel) {
        super(channel)
    }

    @Override
    String getModuleName() {
        return "Payment"
    }

    static {
        URL_MAP = new HashMap<>()
        URL_MAP.put("create", "create_url") // 支付（APP）
        URL_MAP.put("checkout", "checkout_url") // 收银台
        URL_MAP.put("order", "order_url") // 查询Order
        URL_MAP.put("session", "session_url") // 查询Session
        URL_MAP.put("authorize", "authorize_url") // 认证URL
        URL_MAP.put("query", "query_url") // 查询
        URL_MAP.put("refund", "refund_url") // 退款
        URL_MAP.put("cancel", "cancel_url") // 取消
        URL_MAP.put("notification", "notification_url") // 回调
    }

    @Override
    ChannelCreateResponse create(ChannelCreateRequest request) {
        // 正常业务结果
        ChannelCreateResponse response = new ChannelCreateResponse()
        response.setChannel(request.getChannel())

        String method = "create"

        // 语言：默认繁体
        String locale = "zh-HK"
        String storeCode = ""
        String payMethodStr = ""
        String extendParams = request.getExtendedParams()
        if (StringUtils.isNotEmpty(extendParams)) {
            JSONObject extendParamJSON = JSONObject.parseObject(extendParams)
            // 语言
            String language = extendParamJSON.getString("language")
            if (StringUtils.isNotEmpty(language)) {
                locale = language
            }

            // 门店编码
            storeCode = extendParamJSON.getString("store_code")

            // 支付方式
            String paymentMethod = extendParamJSON.getString("pay_method")
            if (StringUtils.isNotEmpty(paymentMethod)) {
                payMethodStr = paymentMethod
            }
        }

        // 默认：港币
        String currencyCode = request.getCurrency()
        if (StringUtils.isEmpty(currencyCode)) {
            currencyCode = "HKD"
        }

        // 支付方式
        PayMethod payMethod
        if (payMethodStr.startsWithAny("MASTER", "master")) {
            payMethod = PayMethod.HMaster
        } else if (payMethodStr.startsWithAny("VISA", "visa")) {
            payMethod = PayMethod.HVisa
        } else if (payMethodStr.startsWithAny("GooglePay", "googlePay", "google_pay", "google pay")) {
            payMethod = PayMethod.HGooglePay
        } else if (payMethodStr.startsWithAny("ApplePay", "applePay", "apple_pay", "apple pay")) {
            payMethod = PayMethod.HApplePay
        } else {
            payMethod = PayMethod.OTHERS
        }

        response.setPayMethod(payMethod)

        // VISA 和 MASTER
        if (payMethod == PayMethod.HVisa || payMethod == PayMethod.HMaster) {
            JSONObject resultJSON = visaOrMaster(request, method, locale, storeCode)
            String sessionId = resultJSON.getString("id")
            response.setPrePayId(sessionId)
            String checkUrl = getUrl("checkout", API_VERSION, sessionId)
            response.setPackStr(checkUrl)
            // GooglePay 和 ApplePay
        } else if (PayMethod.HApplePay == payMethod || PayMethod.HGooglePay == payMethod) {
            String transactionId = request.getTransactionId()
            response.setPrePayId(transactionId)
            response.setTpTransactionId(transactionId)

            Map<String, Object> extendedParams = new HashMap<>()
            extendedParams.put("currencyCode", currencyCode)
            extendedParams.put("merchantCountryCode", mapCountry(currencyCode))
            extendedParams.put("testEnv", true)
            extendedParams.put("clientSecret", "")
            extendedParams.put("merchantIdentifier", channel.getChannelAccessConfig().getProperty("merchantIdentifier"))
            extendedParams.put("stripeAccountId", "")
            extendedParams.put("stripePublishableKey", "")
            extendedParams.put("urlScheme", getCallbackUrl(locale, request.getOrderNo()))
            response.setExtendedParams(JSONObject.toJSONString(extendedParams))
        } else {
            throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "Payment method")
        }

        return response
    }

    // Visa或Master
    private JSONObject visaOrMaster(ChannelCreateRequest request, String method, String locale, String storeCode) {
        // 单号
        String orderNo = request.getOrderNo()

        // transactionID
        String transactionId = request.getTransactionId()

        // 商户号
        String merchantId = channel.getChannelAccessConfig().getMerchantId()

        // 获取请求URL（checkout）
        String requestUrl = getUrl("create", API_VERSION, merchantId)

        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("apiOperation", "INITIATE_CHECKOUT")
        bizParams.put("checkoutMode", "WEBSITE")

        // 请求参数-> interaction
        Map<String, Object> interaction = new HashMap<>()
        interaction.put("operation", "VERIFY")
        interaction.put("returnUrl", getCallbackUrl(locale, orderNo))
        interaction.put("redirectMerchantUrl", getCallbackUrl(locale, orderNo))
        interaction.put("retryAttemptCount", 3)
        interaction.put("timeout", 1800)

        // 请求参数-> displayControl
        Map<String, Object> displayControl = new HashMap<>()
        displayControl.put("billingAddress", "HIDE")
        displayControl.put("customerEmail", "HIDE")
        displayControl.put("paymentTerms", "SHOW_IF_SUPPORTED")
        displayControl.put("shipping", "HIDE")
        interaction.put("displayControl", displayControl)

        // 请求参数-> interaction -> merchant
        Map<String, Object> merchant = new HashMap<>()
        merchant.put("name", channel.getChannelAccessConfig().getAppId()) // 商户名称
        interaction.put("merchant", merchant)
        bizParams.put("interaction", interaction)

        // 请求参数-> order
        Map<String, Object> order = new HashMap<>()
        order.put("id", transactionId)
        order.put("currency", currencyCode)
        order.put("amount", convertToYuan(request.getAmount()).toString())
        order.put("description", storeCode + "|" + orderNo)
        order.put("notificationUrl", getNotificationUrl())
        bizParams.put("order", order)

        // 请求Body（checkout）
        String requestBody = JSONObject.toJSONString(bizParams)

        // 获取Session
        JSONObject checkoutResJSON = doRequest(method, "POST", requestUrl, requestBody, false)
        JSONObject checkoutJSON = checkoutResJSON.getJSONObject("session")
        if (ObjectUtils.isEmpty(checkoutJSON)) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Session ID is null")
        }
        return checkoutJSON
    }



    @Override
    ChannelPayResponse pay(ChannelPayRequest request) {
        String payMethodStr = ""
        String storeCode = ""
        String extendParams = request.getExtendedParams()
        if (StringUtils.isNotEmpty(extendParams)) {
            JSONObject extendParamJSON = JSONObject.parseObject(extendParams)

            // 门店编码
            storeCode = extendParamJSON.getString("store_code")

            // 支付方式
            String paymentMethod = extendParamJSON.getString("pay_method")
            if (StringUtils.isNotEmpty(paymentMethod)) {
                payMethodStr = paymentMethod
            }
        }

        // 支付方式
        String payMethod
        if (payMethodStr.startsWithAny("GooglePay", "googlePay", "google_pay", "google pay")) {
            payMethod = "GOOGLE_PAY"
        } else if (payMethodStr.startsWithAny("ApplePay", "applePay", "apple_pay", "apple pay")) {
            payMethod = "APPLE_PAY"
        } else {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "pay")
        }

        JSONObject resultJSON = googlePayOrApplePay(request, payMethod, storeCode)
        JSONObject responseJSON = resultJSON.getJSONObject("response")
        String gatewayCode = responseJSON.getString("gatewayCode")

        JSONObject transactionJSON = resultJSON.getJSONObject("transaction")
        String transactionId = transactionJSON.getString("id")

        // 解析结果集
        ChannelPayResponse response = new ChannelPayResponse()
        response.setChannel(request.getChannel())
        response.setTransactionId(request.getTransactionId())
        response.setTpTransactionId(transactionId)
        response.setTransactionState(mapTransactionState(gatewayCode))
        return response
    }

    // GooglePay或ApplePay（Direct Pay）
    private JSONObject googlePayOrApplePay(ChannelPayRequest request, String payMethod, String storeCode) {
        // 单号
        String orderNo = request.getOrderNo()

        // TransactionID
        String transactionId = request.getTransactionId()

        // 商户号
        String merchantId = channel.getChannelAccessConfig().getMerchantId()

        // paymentToken
        String paymentToken = ""
        String extendedParams = request.getExtendedParams()
        JSONObject extJSON = JSONObject.parseObject(extendedParams)
        if (ObjectUtils.isNotEmpty(extJSON)) {
            paymentToken = extJSON.getString("paymentToken")
        }

        // 币种，默认：港币
        String currencyCode = request.getCurrency()
        if (StringUtils.isEmpty(currencyCode)) {
            currencyCode = "HKD"
        }

        // 获取请求URL
        String requestUrl = getUrl("authorize", API_VERSION, merchantId, transactionId, orderNo)

        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("apiOperation", "AUTHORIZE")

        Map<String, Object> order = new HashMap<>()
        order.put("amount", convertToYuan(request.getAmount()))
        order.put("currency", currencyCode)
        order.put("walletProvider", payMethod)
        order.put("description", storeCode + "|" + orderNo)
        bizParams.put("order", order)

        Map<String, Object> sourceOfFunds = new HashMap<>()
        sourceOfFunds.put("type", "CARD")

        Map<String, Object> provided = new HashMap<>()
        Map<String, Object> card = new HashMap<>()
        Map<String, Object> devicePayment = new HashMap<>()
        devicePayment.put("paymentToken", paymentToken)
        devicePayment.put("devicePayment", devicePayment)
        card.put("devicePayment", devicePayment)
        provided.put("card", card)
        sourceOfFunds.put("provided", provided)

        bizParams.put("sourceOfFunds", sourceOfFunds)

        Map<String, Object> transaction = new HashMap<>()
        transaction.put("source", "INTERNET")
        bizParams.put("transaction", transaction)

        // 请求Body
        String requestBody = JSONObject.toJSONString(bizParams)
        return doRequest("create", "POST", requestUrl, requestBody, false)
    }

    // 使用Masterpass
    private JSONObject masterPass(ChannelCreateRequest request, String payMethod, String storeCode) {
        // 单号
        String orderNo = request.getOrderNo()

        // TransactionID
        String transactionId = request.getTransactionId()

        // 商户号
        String merchantId = channel.getChannelAccessConfig().getMerchantId()

        // 币种，默认：港币
        String currencyCode = request.getCurrency()
        if (StringUtils.isEmpty(currencyCode)) {
            currencyCode = "HKD"
        }

        // create session
        String sessionUrl = getUrl("create_session", merchantId)

        Map<String, Object> sessionParams = new HashMap<>()
        Map<String, Object> session = new HashMap<>()
        session.put("authenticationLimit", 25)
        sessionParams.put("session", session)

        String sessionReqBody = JSONObject.toJSONString(sessionParams)

        JSONObject sessionResJSON = doRequest("create", "POST", sessionUrl, sessionReqBody, false)
        JSONObject sessionJSON = sessionResJSON.getJSONObject("session")
        String sessionId = sessionJSON.getString("id")

        // open wallet
        String openWalletUrl = getUrl("open_wallet", API_VERSION, merchantId, sessionId)

        Map<String, Object> openWalletParams = new HashMap<>()

        Map<String, Object> order = new HashMap<>()
        order.put("walletProvider", "MASTERPASS_ONLINE")
        order.put("amount", convertToYuan(request.getAmount()))
        order.put("currency", currencyCode)
        openWalletParams.put("order", order)

        Map<String, Object> wallet = new HashMap<>()
        Map<String, Object> masterpass = new HashMap<>()
        masterpass.put("originUrl", "")
        wallet.put("masterpass", masterpass)
        openWalletParams.put("wallet", wallet)

        String openWalletReqBody = JSONObject.toJSONString(openWalletParams)
        JSONObject openWalletResJSON = doRequest("create", "POST", openWalletUrl, openWalletReqBody, false)
        JSONObject walletJSON = openWalletResJSON.getJSONObject("wallet")
        JSONObject masterpassJSON = walletJSON.getJSONObject("masterpass")
        String requestToken = masterpassJSON.getString("requestToken")
        String merchantCheckoutId = masterpassJSON.getString("merchantCheckoutId")
        String allowedCardTypes = masterpassJSON.getString("allowedCardTypes")
        String oauthToken = masterpassJSON.getString("oauthToken")
        String oauthVerifier = masterpassJSON.getString("oauthVerifier")
        return null
    }

    @Override
    ChannelQueryResponse query(ChannelQueryRequest request) {
        ChannelQueryResponse response = new ChannelQueryResponse()
        String method = "query"
        String fullMethodName = getFullMethodName(method)

        String merchantId = channel.getChannelAccessConfig().getMerchantId()

        String orderId = request.getTransactionId()

        String orderReqUrl = getUrl(method, API_VERSION, merchantId, orderId)

        // 请求Retrieve Order
        JSONObject orderResJSON = new JSONObject()
        String status = ""
        for (int retryCount = 1; retryCount <= 3; retryCount++) {
            orderResJSON = doRequest(method, "GET", orderReqUrl, null, false)
            String orderResCode = orderResJSON.getString("result")
            // 发生 not found 错误重试一下
            if ("ERROR".equalsIgnoreCase(orderResCode)) {
                JSONObject errorJSON = orderResJSON.getJSONObject("error")
                String errorMsg = errorJSON.getString("explanation")
                String notFoundErrMsg = String.format("Unable to find order %s for merchant %s", orderId, merchantId)
                if (errorMsg.contains(notFoundErrMsg)) {
                    if (retryCount >= 2) {
                        response.setTransactionState(TransactionState.WAITING)
                        return response
                    } else {
                        Thread.sleep(500)
                        LoggerUtil.info("{0} retry time: [{1}]", fullMethodName, retryCount)
                        continue
                    }
                }
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMsg)
            }
            // 状态为“AUTHENTICATION_INITIATED”，重试一次
            status = orderResJSON.getString("status")
            if ("AUTHENTICATION_INITIATED".equalsIgnoreCase(status)) {
                continue
            }
            break
        }

        response.setTransactionState(mapTransactionState(status))
        response.setRealAmount(convertToFen(orderResJSON.getBigDecimal("totalAmount")))
        return response
    }

    // 状态转换
    private static TransactionState mapTransactionState(String tpTransactionState) {
        TransactionState transactionState
        switch (tpTransactionState) {
            case "PENDING":
                transactionState = TransactionState.PENDING
                break
            case "CANCELLED":
                transactionState = TransactionState.CANCELED
                break
            case "REFUNDED":
                transactionState = TransactionState.REFUNDED
                break
            case "VERIFIED":
                transactionState = TransactionState.SUCCESS
                break
            case "VERIFIED":
                transactionState = TransactionState.SUCCESS
                break
            case "SUCCESS":
                transactionState = TransactionState.SUCCESS
                break
            case "FAILURE":
                transactionState = TransactionState.FAILED
                break
            default:
                transactionState = TransactionState.FAILED
        }
        return transactionState
    }

    // 国家转换
    private static String mapCountry(String currency) {
        String country = ""
        switch (currency.toUpperCase()) {
            case "CNY":
                country = "China"
                break
            case "JP":
                country = "Japan"
                break
            case "HKD":
                country = "HongKong"
                break
            case "USD":
                country = "America"
                break
            default:
                break
        }
        return country
    }

    @Override
    ChannelRefundResponse refund(ChannelRefundRequest request) {
        String method = "refund"

        // 默认：港币
        String currencyCode = request.getCurrency()
        if (StringUtils.isEmpty(currencyCode)) {
            currencyCode = "HKD"
        }

        // 退货单号
        String transactionId = request.getTransactionId()

        // 原单号
        String relatedTransactionId = request.getRelatedTransactionId()

        // 请求URL
        String requestUrl = getUrl(method, API_VERSION, relatedTransactionId, transactionId)

        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("apiOperation", "REFUND")

        // 请求参数-> interaction
        Map<String, Object> transaction = new HashMap<>()
        transaction.put("amount", convertToYuan(request.getAmount()))
        transaction.put("currency", currencyCode)
        bizParams.put("transaction", transaction)

        String requestBody = JSONObject.toJSONString(bizParams)

        // 执行
        JSONObject resultJSON = doRequest(method, "PUT", requestUrl, requestBody, false)

        JSONObject responseJSON = resultJSON.getJSONObject("response")

        String status = responseJSON.getString("status")

        // 解析结果集
        ChannelRefundResponse response = new ChannelRefundResponse()
        response.setTransactionId(request.getTransactionId())
        response.setRealAmount(convertToFen(resultJSON.getBigDecimal("refundAmount")))
        response.setTransactionState(mapTransactionState(status))
        return response
    }

    @Override
    ChannelCancelResponse cancel(ChannelCancelRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "cancel")
    }

    @Override
    ChannelNotificationResponse payNotify(HttpServletRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "payNotify")
    }

    // 统一请求
    private JSONObject doRequest(String method, String requestMethod, String requestUrl, String requestBody, boolean reserveData) {
        String fullMethodName = getFullMethodName(method)
        LoggerUtil.info("{0} is sending request URL: {1}", fullMethodName, requestUrl)

        if (requestBody != null) {
            LoggerUtil.info("{0} is sending request body: {1}", fullMethodName, requestBody)
        }

        String merchantId = channel.getChannelAccessConfig().getMerchantId()
        String accessKey = channel.getChannelAccessConfig().getAccessKey()

        String credentials = "merchant." + merchantId + ":" + accessKey
        byte[] encodedCredentials = Base64.getEncoder().encode(credentials.getBytes(StandardCharsets.UTF_8))
        String authorization = "Basic " + new String(encodedCredentials, StandardCharsets.UTF_8)

        Map<String, String> headers = new HashMap<>()
        headers.put("Content-Type", "application/json")
        headers.put("Authorization", authorization)

        try {
            Timestamp reqTime = DateUtil.getNowTimeStamp()

            // 执行请求
            String response = executeRequest(requestMethod, requestUrl, requestBody, headers)
            if (null == response) {
                LoggerUtil.error("{0} is failed, null result.", null, fullMethodName)
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
            }
            Timestamp respTime = DateUtil.getNowTimeStamp()
            LoggerUtil.info("{0} received message: {1}", fullMethodName, response)

            // 设置上下文（出入报文）
            if (reserveData) {
                Map<String, Object> requestFullMessage = new HashMap<>()
                requestFullMessage.put("header", headers)
                requestFullMessage.put("body", response)
                TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
                TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSONObject.toJSONString(requestFullMessage))
                TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, response)
                TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
            }

            JSONObject resultJSON = JSONObject.parseObject(response)

            // 查询时，直接返回
            if ("query".equalsIgnoreCase(method)) {
                return resultJSON
            }

            String resultCode = resultJSON.getString("result")
            if (resultCode.equalsIgnoreCase("ERROR")) {
                JSONObject errorJSON = resultJSON.getJSONObject("error")
                String errorMsg = errorJSON.getString("explanation")
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMsg)
            }
            return resultJSON
        } catch (Exception ex) {
            LoggerUtil.error("Error {0} payment request: ", ex, fullMethodName)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, ex.getMessage())
        }
    }

    // 执行请求
    private String executeRequest(String requestMethod, String url, String content, Map<String, String> headers) {
        if (!("GET".equalsIgnoreCase(requestMethod) || "POST".equalsIgnoreCase(requestMethod) || "PUT".equalsIgnoreCase(requestMethod))) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Only support 'GET'、'POST' or 'PUT'")
        }

        CloseableHttpClient httpClient = HttpClients.createDefault()

        HttpUriRequest httpUriRequest = null
        if ("POST".equalsIgnoreCase(requestMethod)) {
            httpUriRequest = new HttpPost(url)
        } else if ("PUT".equalsIgnoreCase(requestMethod)) {
            httpUriRequest = new HttpPut(url)
        } else if ("GET".equalsIgnoreCase(requestMethod)) {
            httpUriRequest = new HttpGet(url)
        }

        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpUriRequest.setHeader(entry.getKey(), entry.getValue())
            }
        }

        if (content != null) {
            StringEntity entity = new StringEntity(content, StandardCharsets.UTF_8)
            ((HttpEntityEnclosingRequest) httpUriRequest).setEntity(entity)
        }

        CloseableHttpResponse response = httpClient.execute(httpUriRequest)
        return EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8)
    }

    // 获取回调URL
    private String getCallbackUrl(String locale, String orderNo) {
        String gatewayUrl = channel.getChannelAccessConfig().getGatewayUrl()
        if (StringUtils.isNotEmpty(gatewayUrl)) {
            String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
            String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
            String path = String.format("%s/%s/%s/submitted-order?orderId=%s", partnerId, storeId, locale, orderNo)
            return gatewayUrl.endsWith("/") ? gatewayUrl + path : gatewayUrl + "/" + path
        }
        throw new CommonException(ServiceError.PAYMENT_CONFIG_NOT_EXISTS, "支付跳转GatewayUrl")
    }

    // 获取webhook通知地址
    private String getNotificationUrl() {
        String notificationUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get("notification"))
        if (StringUtils.isNotBlank(notificationUrl)) {
            String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
            String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
            String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId
            return notificationUrl.endsWith("/") ? notificationUrl + path : notificationUrl + "/" + path
        }
        return null
    }

    // 获取URL
    private String getUrl(String method, String... args) {
        String urlParam = channel.getChannelAccessConfig().getProperty(URL_MAP.get(method))
        if (ObjectUtils.isEmpty(args)) {
            return urlParam
        }
        return String.format(urlParam, args)
    }

    // 分转成元
    private static BigDecimal convertToYuan(BigDecimal dollar) {
        if (dollar == null) {
            return null
        }
        return dollar.divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP)
    }

    // 元转换成分
    private static BigDecimal convertToFen(BigDecimal point) {
        if (point == null) {
            return null
        }
        return (point * new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP)
    }

}
