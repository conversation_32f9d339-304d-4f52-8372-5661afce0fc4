package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.RedisUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Commodity
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.NotificationType
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils
import org.apache.http.HttpEntityEnclosingRequest
import org.apache.http.NameValuePair
import org.apache.http.client.entity.UrlEncodedFormEntity
import org.apache.http.client.methods.CloseableHttpResponse
import org.apache.http.client.methods.HttpGet
import org.apache.http.client.methods.HttpPost
import org.apache.http.client.methods.HttpPut
import org.apache.http.client.methods.HttpUriRequest
import org.apache.http.entity.ContentType
import org.apache.http.entity.StringEntity
import org.apache.http.impl.client.CloseableHttpClient
import org.apache.http.impl.client.HttpClients
import org.apache.http.message.BasicNameValuePair
import org.apache.http.util.EntityUtils

import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.security.InvalidKeyException
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.sql.Timestamp
import java.time.Instant
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.concurrent.TimeUnit

/**
 * PayMe线下支付
 * <pre>
 *      URL: https://develop.hsbc.com/payme-apis-business/endpoints
 *      场景: POS扫码
 * </pre>
 * <AUTHOR> Wang
 */
class PayMePay extends AbstractExternalChannelModule implements PaymentModule {

    private static final String API_VERSION = "0.12"
    private static final String PAY_CONTENT_TYPE = "application/json"
    private static final String TOKEN_CONTENT_TYPE = "application/x-www-form-urlencoded"

    private static final Map<String, String> URL_MAP
    private static final Map<String, String> REQUEST_TARGET_MAP

    PayMePay(ExternalChannel channel) {
        super(channel)
    }

    static {
        URL_MAP = new HashMap<>()
        URL_MAP.put("getAccessToken", "get_access_token_url") // 获取token
        URL_MAP.put("refund", "refund_url") // 退款
        URL_MAP.put("pay", "pay_url") // 支付（POS）
        URL_MAP.put("query", "query_url") // 查询
        URL_MAP.put("cancel", "cancel_url") // 取消
        URL_MAP.put("notification", "notification_url") // 回调

        REQUEST_TARGET_MAP = new HashMap<>()
        REQUEST_TARGET_MAP.put("pay", "(request-target): post /payments/payment")
        REQUEST_TARGET_MAP.put("query", "(request-target): get /payments/paymentrequests/:contextPath")
        REQUEST_TARGET_MAP.put("refund", "(request-target): post /payments/transactions/:contextPath/refunds")
        REQUEST_TARGET_MAP.put("cancel", "(request-target): put /payments/paymentrequests/:contextPath/cancel")
    }

    @Override
    String getModuleName() {
        return "Payment"
    }

    @Override
    ChannelCreateResponse create(ChannelCreateRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "create")
    }

    @Override
    ChannelPayResponse pay(ChannelPayRequest request) {
        String method = "pay"

        // 门店
        String storeCode = ""
        String ticketNo = ""
        String extendParams = request.getExtendedParams()
        if (StringUtils.isNotEmpty(extendParams)) {
            JSONObject extendParamJSON = JSONObject.parseObject(extendParams)
            // 门店编码
            storeCode = extendParamJSON.getString("store_code")
            // 取餐号
            ticketNo = extendParamJSON.getString("ticket_no")
        }

        // POS编码
        String posId = request.getPosId()

        // 默认：港币
        String currencyCode = request.getCurrency()
        if (StringUtils.isEmpty(currencyCode)) {
            currencyCode = "HKD"
        }

        // 请求URL
        String requestUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get(method))

        // 请求Body
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("clientToken", request.getPayCode())
        bizParams.put("totalAmount", convertToDollar(request.getAmount()))
        bizParams.put("currencyCode", currencyCode)
        bizParams.put("notificationUri", getNotificationUrl())
        Map<String, Object> merchantData = new HashMap<>()
        merchantData.put("orderDescription", storeCode + "|" + ticketNo)
        merchantData.put("orderId", request.getTransactionId())
        List<Map<String, Object>> shoppingCart = new ArrayList<>()
        for (Commodity commodity : request.getCommodities()) {
            Map<String, Object> data = new HashMap<>()
            data.put("sku", commodity.getCode())
            data.put("name", commodity.getName())
            data.put("quantity", commodity.getQuantity())
            data.put("price", convertToDollar(commodity.getPrice()))
            data.put("currencyCode", currencyCode)
            shoppingCart.add(data)
        }
        if (shoppingCart.size() > 0) {
            merchantData.put("shoppingCart", shoppingCart)
        }
        bizParams.put("merchantData", merchantData)

        String requestBody = JSONObject.toJSONString(bizParams)

        // 请求Header
        Map<String, String> headers = getRequestHeader(requestBody, method, null, posId)

        // 执行
        JSONObject resultJSON = doRequest("POST", method, requestUrl, requestBody, headers, false)

        // 解析结果集
        ChannelPayResponse response = new ChannelPayResponse()
        response.setChannel(request.getChannel())
        response.setTransactionId(request.getTransactionId())
        response.setTpTransactionId(resultJSON.getString("paymentRequestId"))
        response.setTransactionState(TransactionState.WAITING)
        return response
    }

    @Override
    ChannelQueryResponse query(ChannelQueryRequest request) {
        String method = "query"
        String tpTransactionId = request.getTpTransactionId()

        // 请求URL
        String requestUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get(method)).replace(":contextPath", tpTransactionId)

        // 请求Header
        Map<String, String> headers = getRequestHeader(null, method, tpTransactionId, null)

        // 执行
        JSONObject resultJSON = doRequest("GET", method, requestUrl, null, headers, false)

        // 获取第一条数据
        ChannelQueryResponse response = new ChannelQueryResponse()
        response.setTransactionState(mapTransactionState(resultJSON.getString("statusCode")))
        response.setRealAmount(convertToPoint(resultJSON.getBigDecimal("totalAmount")))
        // 成功时，transactions不为空
        JSONArray transactions = resultJSON.getJSONArray("transactions")
        if (Objects.nonNull(transactions)) {
            //（参数）第三方transactionId
            JSONObject firstTransaction = transactions.get(0)
            Map<String, String> extendParams = new HashMap<>()
            extendParams.put("transactionId", firstTransaction.getString("transactionId"))
            response.setExtendedParams(JSONObject.toJSONString(extendParams))
            //（参数）合阔transactionId
            response.setTransactionId(firstTransaction.getString("orderId"))
        }
        return response
    }

    @Override
    ChannelRefundResponse refund(ChannelRefundRequest request) {
        String method = "refund"
        String extendParams = request.getExtendedParams()
        if (StringUtils.isEmpty(extendParams)) {
            throw new ConnectException("Param 'extendParams' is null")
        }
        JSONObject extendParamJSON = JSONObject.parseObject(extendParams)
        String tpTransactionId = extendParamJSON.getString("transactionId")
        if (StringUtils.isEmpty(tpTransactionId)) {
            throw new ConnectException("Param extendParams's 'transactionId' is null")
        }
        Date transactionTime = request.getTransactionTime()
        if (Objects.isNull(transactionTime)) {
            throw new ConnectException("Param 'transactionTime' is null")
        }

        // 请求URL
        String requestUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get(method)).replace(":contextPath", tpTransactionId)

        // 请求Body
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("amount", convertToDollar(request.getAmount()))
        bizParams.put("currencyCode", request.getCurrency() == null ? "HKD" : request.getCurrency().toUpperCase())
        bizParams.put("reasonCode", "00")
        bizParams.put("reasonMessage", "Refund for TJI order")

        String requestBody = JSONObject.toJSONString(bizParams)

        // 请求Header
        Map<String, String> headers = getRequestHeader(requestBody, method, tpTransactionId, null)

        // 执行
        JSONObject resultJSON = doRequest("POST", method, requestUrl, requestBody, headers, false)

        // 解析结果集
        ChannelRefundResponse response = new ChannelRefundResponse()
        response.setTransactionId(request.getTransactionId())
        response.setTpTransactionId(resultJSON.getString("refundId"))
        response.setRealAmount(convertToPoint(resultJSON.getBigDecimal("refundAmount")))
        response.setTransactionState(TransactionState.REFUNDED)
        return response
    }

    @Override
    ChannelCancelResponse cancel(ChannelCancelRequest request) {
        String method = "cancel"
        String paymentRequestId = request.getRelatedTPTransactionId()

        // 请求URL
        String requestUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get(method)).replace(":contextPath", paymentRequestId)

        // 请求Header
        Map<String, String> headers = getRequestHeader(null, method, paymentRequestId, null)

        // 执行
        JSONObject resultJSON = doRequest("PUT", method, requestUrl, null, headers, false)

        // 解析结果集
        ChannelCancelResponse response = new ChannelCancelResponse()
        response.setTransactionState(mapTransactionState(resultJSON.getString("statusCode")))
        return response
    }

    @Override
    ChannelNotificationResponse payNotify(HttpServletRequest request) {
        String method = "payNotify"
        String fullMethod = getFullMethodName(method)
        String payloadJSONStr = request.getParameter("payload")
        LoggerUtil.info("{0} received message: {1}", fullMethod, payloadJSONStr)

        // 返回结果
        JSONObject payloadJSON = JSONObject.parseObject(payloadJSONStr)

        // 解析返回结果集
        ChannelNotificationResponse response = new ChannelNotificationResponse()
        //（参数）返给第三方的报文
        String resp2ThirdParty = "{\"code\":\"200\",\"message\":\"success\"}"
        response.setResponse(resp2ThirdParty)
        ChannelPayResponse payResponse = new ChannelPayResponse()
        //（参数）通知类型
        payResponse.setNotificationType(NotificationType.PAY)
        //（参数）实际支付金额
        payResponse.setRealAmount(convertToPoint(payloadJSON.getBigDecimal("totalAmount")))
        //（参数）支付请求ID
        payResponse.setTpTransactionId(payloadJSON.getString("paymentRequestId"))
        //（参数）支付状态
        payResponse.setTransactionState(mapTransactionState(payloadJSON.getString("statusCode")))
        // 成功时，transactions不为空
        JSONArray transactions = payloadJSON.getJSONArray("transactions")
        if (Objects.nonNull(transactions)) {
            //（参数）第三方transactionId
            JSONObject firstTransaction = transactions.get(0)
            Map<String, String> extendParams = new HashMap<>()
            extendParams.put("transactionId", firstTransaction.getString("transactionId"))
            payResponse.setExtendedParams(JSONObject.toJSONString(extendParams))
            //（参数）合阔transactionId
            payResponse.setTransactionId(firstTransaction.getString("orderId"))
        } else {
            //（参数）合阔transactionId
            payResponse.setTransactionId(payloadJSON.getString("orderId"))
        }
        response.setPayResponse(payResponse)

        // 设置上下文（出入报文）
        TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, payloadJSONStr)
        TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resp2ThirdParty)

        return response
    }

    // 统一请求
    private JSONObject doRequest(String requestMethod, String method, String requestUrl, Object requestBody, Map<String, String> headers, boolean reserveData) {
        String fullMethodName = getFullMethodName(method)
        LoggerUtil.info("{0} is sending request URL: {1}", fullMethodName, requestUrl)
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            LoggerUtil.info("{0} is sending request header, {1}: {2}", fullMethodName, entry.getKey(), entry.getValue())
        }
        if (requestBody != null) {
            LoggerUtil.info("{0} is sending request body: {1}", fullMethodName, requestBody)
        }
        try {
            Timestamp reqTime = cn.hexcloud.commons.utils.DateUtil.getNowTimeStamp()

            // 执行请求
            String response = executeRequest(requestMethod, requestUrl, requestBody, headers)
            if (null == response) {
                LoggerUtil.error("{0} is failed with null result.", null, fullMethodName)
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
            }
            Timestamp respTime = cn.hexcloud.commons.utils.DateUtil.getNowTimeStamp()
            LoggerUtil.info("{0} received message: {1}", fullMethodName, response)

            // 设置上下文（出入报文）
            if (reserveData) {
                Map<String, Object> requestFullMessage = new HashMap<>()
                requestFullMessage.put("header", headers)
                requestFullMessage.put("body", requestBody)
                TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
                TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSONObject.toJSONString(requestFullMessage))
                TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, response)
                TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
            }

            return JSONObject.parseObject(response)
        } catch (Exception ex) {
            LoggerUtil.error("Error {0} payment request: ", ex, fullMethodName)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, ex.getMessage())
        }
    }

    // 执行请求
    private String executeRequest(String method, String url, Object content, Map<String, String> headers) {
        if (!("GET".equalsIgnoreCase(method) || "POST".equalsIgnoreCase(method) || "PUT".equalsIgnoreCase(method))) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Only support 'GET'、'POST' or 'PUT'")
        }
        CloseableHttpClient httpClient = HttpClients.createDefault()

        HttpUriRequest httpUriRequest = null
        if ("POST".equalsIgnoreCase(method)) {
            httpUriRequest = new HttpPost(url)
        } else if ("PUT".equalsIgnoreCase(method)) {
            httpUriRequest = new HttpPut(url)
        } else if ("GET".equalsIgnoreCase(method)) {
            httpUriRequest = new HttpGet(url)
        }

        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpUriRequest.setHeader(entry.getKey(), entry.getValue())
            }
        }

        if (content != null) {
            String contentType = headers.get("Content-Type")
            if (TOKEN_CONTENT_TYPE.equalsIgnoreCase(contentType)) {
                UrlEncodedFormEntity entity = new UrlEncodedFormEntity((List<NameValuePair>) content, StandardCharsets.UTF_8)
                entity.setContentType(ContentType.APPLICATION_FORM_URLENCODED.getMimeType())
                ((HttpEntityEnclosingRequest) httpUriRequest).setEntity(entity)
            } else {
                StringEntity entity = new StringEntity((String) content, StandardCharsets.UTF_8)
                ((HttpEntityEnclosingRequest) httpUriRequest).setEntity(entity)
            }
        }

        CloseableHttpResponse response = httpClient.execute(httpUriRequest)
        int statusCode = response.getStatusLine().getStatusCode()
        String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8)
        if (statusCode == 200 || statusCode == 201) {
            return responseBody
        }
        throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, responseBody)
    }

    // 获取Header
    private Map<String, String> getRequestHeader(String messageBody, String requestTargetMethod, String contextPath, String deviceId) {
        // * Headers顺序（1、Api-Version 3、Request-Date-Time 4、Content-Type 5、Digest 6、Accept 7、Trace-id 8、Authorization）
        Map<String, String> headers = new LinkedHashMap<>()
        headers.put("Api-Version", API_VERSION)
        headers.put("Request-Date-Time", getRequestDateTime())
        headers.put("Content-Type", PAY_CONTENT_TYPE)
        // messageBody非空时，Digest参数必填
        boolean hasDigest = StringUtils.isNotEmpty(messageBody)
        if (hasDigest) {
            headers.put("Digest", getDigest(messageBody))
        }
        headers.put("Accept", "application/json")
        headers.put("Trace-Id", UUID.randomUUID().toString())
        headers.put("Authorization", getAuthorization(true))
        headers.put("Signature", getSignature(requestTargetMethod, contextPath, hasDigest, headers))
        if (StringUtils.isNotEmpty(deviceId)) {
            headers.put("X-HSBC-Device-Id", deviceId)
        }
        if (StringUtils.isNotEmpty(channel.getChannelAccessConfig().getMerchantId())) {
            headers.put("X-HSBC-Merchant-Id", channel.getChannelAccessConfig().getMerchantId())
            headers.put("Accept-Language", "en_US")
        }
        return headers
    }

    // 获取回调地址
    private String getNotificationUrl() {
        String notificationUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get("notification"))
        if (StringUtils.isNotBlank(notificationUrl)) {
            String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
            String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
            String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId
            return notificationUrl.endsWith("/") ? notificationUrl + path : notificationUrl + "/" + path
        }
        return null
    }

    // 状态转换
    private static TransactionState mapTransactionState(String tpTransactionState) {
        TransactionState transactionState
        switch (tpTransactionState) {
            case "PR001":
                transactionState = TransactionState.PENDING
                break
            case "PR004":
                transactionState = TransactionState.CANCELED
                break
            case "PR005":
                transactionState = TransactionState.SUCCESS
                break
            case "PR007":
                transactionState = TransactionState.FAILED
                break
            default:
                transactionState = TransactionState.UNKNOWN
        }
        return transactionState
    }

    // 分转成元
    private static BigDecimal convertToDollar(BigDecimal dollar) {
        if (dollar == null) {
            return null
        }
        return dollar.divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP)
    }

    // 元转换成分
    private static BigDecimal convertToPoint(BigDecimal point) {
        if (point == null) {
            return null
        }
        return (point * new BigDecimal(100)).setScale(0, BigDecimal.ROUND_HALF_UP)
    }

    //------------------------------------ Start 获取Header参数 ------------------------------------//

    // 获取 Authorization
    private String getAuthorization(boolean cacheFirst) {
        String redisKey = "PBIS-ACCESS-TOKEN:" + channel.getChannelCode() + ":" + ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
        if (cacheFirst) {
            String accessToken = RedisUtil.StringOps.get(redisKey)
            if (StringUtils.isNotEmpty(accessToken)) {
                return "Bearer " + accessToken
            }
        }

        // 请求header
        HashMap<String, String> headers = new HashMap<>(3)
        headers.put("Api-Version", API_VERSION)
        headers.put("Content-Type", TOKEN_CONTENT_TYPE)
        headers.put("Accept", "application/json")

        ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
        // 请求Url
        String method = "getAccessToken"
        String requestUrl = channelAccessConfig.getProperty(URL_MAP.get(method))

        // 请求body
        List<NameValuePair> bizParams = new ArrayList<>(2)
        bizParams.add(new BasicNameValuePair("client_id", channelAccessConfig.getAppKey()))
        bizParams.add(new BasicNameValuePair("client_secret", channelAccessConfig.getAccessKey()))

        // 执行
        JSONObject resultJSON = doRequest("POST", method, requestUrl, bizParams, headers, false)

        // 解析结果集
        String accessToken = resultJSON.getString("accessToken")
        String expiresOn = resultJSON.getString("expiresOn")
        Integer tokenExpiresIn = getTokenExpiresIn(expiresOn)
        if (accessToken && tokenExpiresIn) {
            // 将accessToken放入缓存，在第三方有效期的基础上减去300秒作为缓存的过期时间
            RedisUtil.StringOps.setEx(redisKey, accessToken, tokenExpiresIn - 300, TimeUnit.SECONDS)
        }

        return "Bearer " + accessToken
    }

    // 获取 Digest
    private String getDigest(String requestBody) {
        try {
            // 1. 将消息体转换为 UTF-8 字节数组
            byte[] utf8Bytes = requestBody.getBytes(StandardCharsets.UTF_8)

            // 2. 计算 SHA-256 哈希值
            MessageDigest sha256 = MessageDigest.getInstance("SHA-256")
            byte[] sha256Bytes = sha256.digest(utf8Bytes)

            // 3. 对 SHA-256 哈希值进行 Base64 编码
            byte[] base64Bytes = Base64.getEncoder().encode(sha256Bytes)
            return "SHA-256=" + new String(base64Bytes, StandardCharsets.UTF_8)
        } catch (Exception e) {
            throw new RuntimeException("Error encoding message", e)
        }
    }

    // 获取 Signature
    // https://develop.hsbc.com/payme-apis-business
    private String getSignature(String requestTargetMethod, String contextPath, boolean hasDigest, LinkedHashMap<String, String> header) {
        // keyId（文档取的 Signing Key Id）
        String signKeyId = channel.getChannelAccessConfig().getPrivateKey()
        // signature key（文档取的 Signing Key）
        String signKey = channel.getChannelAccessConfig().getThirdPartyPublicKey()
        // 1、构建 signature string
        String signatureString = buildSignatureString(requestTargetMethod, contextPath, header)
        // 2、产生 signature参数
        String signature = createSignature(signKey, signatureString)
        // 3、Signature: keyId="${keyId}",algorithm="${algorithm}",headers="${headers}",signature="${signature}"
        String headers = "(request-target) Api-Version Request-Date-Time Content-Type Accept Trace-Id Authorization"
        if (hasDigest) {
            headers = "(request-target) Api-Version Request-Date-Time Content-Type Digest Accept Trace-Id Authorization"
        }
        return String.format("keyId=\"%s\",algorithm=\"hmac-sha256\",headers=\"%s\",signature=\"%s\"", signKeyId, headers, signature)
    }

    // 获取 Request-Date-Time
    private String getRequestDateTime() {
        Instant now = Instant.now()
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
        return now.atOffset(ZoneOffset.UTC).format(formatter)
    }

    // 获取token过期时长
    private Integer getTokenExpiresIn(String expiresOn) {
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(expiresOn, DateTimeFormatter.ISO_OFFSET_DATE_TIME)
        long target = zonedDateTime.toInstant().toEpochMilli()
        long current = Instant.now().toEpochMilli()
        Integer expireIn = (int) (target - current) / 1000L
        return expireIn
    }

    // 产生 signature参数
    private String createSignature(String signingKeyString, String signatureString) {
        try {
            // 1. 对签名密钥进行 Base64 解码
            byte[] signingKeyBytes = Base64.getDecoder().decode(signingKeyString)

            // 2. 将签名内容转换为 UTF-8 字节数组
            byte[] signatureBytes = signatureString.getBytes(StandardCharsets.UTF_8)

            // 3. 使用 HMAC-SHA256 算法计算签名
            Mac hmacSha256 = Mac.getInstance("HmacSHA256")
            SecretKeySpec key = new SecretKeySpec(signingKeyBytes, "HmacSHA256")
            hmacSha256.init(key)
            byte[] hmacBytes = hmacSha256.doFinal(signatureBytes)

            // 4. 对签名结果进行 Base64 编码
            byte[] base64Bytes = Base64.getEncoder().encode(hmacBytes)
            return new String(base64Bytes, StandardCharsets.UTF_8)
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("Error signing message", e)
        }
    }

    // 构建 signature string
    // Headers顺序（1、(request-target) 2、Api-Version 3、Request-Date-Time 4、Content-Type 5、Digest 6、Accept 7、Trace-id 8、Authorization）
    private String buildSignatureString(String requestTargetMethod, String contextPath, LinkedHashMap<String, String> headers) {
        StringBuffer buffer = new StringBuffer()
        String requestTarget = REQUEST_TARGET_MAP.get(requestTargetMethod)
        if (StringUtils.isNotEmpty(contextPath)) {
            requestTarget = requestTarget.replace(":contextPath", contextPath)
        }
        buffer.append(requestTarget).append("\n")
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            String key = entry.getKey().toLowerCase()
            String value = entry.getValue()
            buffer.append(key).append(": ").append(value).append("\n")
        }
        String afterSignature = buffer.toString()
        return afterSignature.substring(0, afterSignature.lastIndexOf("\n"))
    }

    //------------------------------------ End 获取Header参数 ------------------------------------//

}