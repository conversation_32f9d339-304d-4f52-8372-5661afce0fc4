package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.apache.commons.codec.binary.Base64
import org.apache.commons.lang3.StringUtils

import javax.crypto.Mac
import javax.crypto.SecretKey
import javax.crypto.spec.SecretKeySpec
import javax.servlet.http.HttpServletRequest
import java.nio.charset.Charset
import java.nio.charset.StandardCharsets

/**
 * @Classname QmaiPay* @Description:
 * @Date 2022/8/82:57 下午
 * <AUTHOR>
 */
class QBalPay extends AbstractExternalChannelModule implements PaymentModule {

  QBalPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  String getModuleName() {
    return "Payment"
  }


  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("create"))
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {

    Map<String, Object> signMap = new HashMap();
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    if(checkConfig(channelAccessConfig)){

      throw new CommonException(ServiceError.PAYMENT_CONFIG_NOT_EXISTS)
    }

    signMap.put("GrantCode", channelAccessConfig.getMerchantId())
    String Nonce = String.valueOf((int) (Math.random() * 1000000))
    signMap.put("Nonce", Nonce)
    signMap.put("OpenId", channelAccessConfig.getAppId())
    String timestamp = String.valueOf(System.currentTimeMillis())
    signMap.put("Timestamp", timestamp)

    String token = getSignature(signMap)
    // 装填参数
    JSONObject postJson = new JSONObject()
    postJson.put("OpenId", channelAccessConfig.getAppId())
    postJson.put("GrantCode", channelAccessConfig.getMerchantId())
    postJson.put("Timestamp", timestamp)
    postJson.put("Nonce", Nonce)
    postJson.put("Token", token)
    postJson.put("Action", "communal.user.consume")

    String storeCode = ServiceContext.get(ContextKeyConstant.STORE_CODE)

    //Params拼接
    JSONObject params = new JSONObject()
    params.put("bizId", request.getTransactionId())
    params.put("amount", getAliPayAmount(request.getAmount()))
    params.put("multiMark", storeCode)
    params.put("customerId", request.getMemberNo())
    postJson.put("Params", params)


    String jsonStr = JSON.toJSONString(postJson)
    LoggerUtil.info("QmaiOperation.pay is sending message: {0}, token: {1}.", jsonStr, token)

    byte[] result = HttpUtil.doPost(channelAccessConfig.getProperty("gateway_url"), jsonStr, getRequestHeader())
    if (null == result) {
      LoggerUtil.error("QmaiOperation.pay is failed with null result.", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSON.toJSONString(postJson))
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("QmaiOperation.pay received message: {0}, token: {1}.", resultJSON, token)

    String statusCode = String.valueOf(resultJSON.get("code"));
    if (!statusCode.equals("0")) {
      LoggerUtil.error("QmaiOperation.pay is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("code"),
          resultJSON.getString("errorMsg"), resultJSON.getString("message"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON.getString("message"))

    }
    ChannelPayResponse response = new ChannelPayResponse();
    response.setTransactionState(TransactionState.SUCCESS)
    response.setTransactionId(request.getTransactionId())
    return response

  }


  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("query"))
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {

    Map<String, Object> signMap = new HashMap()
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

    if(checkConfig(channelAccessConfig)){
      throw new CommonException(ServiceError.PAYMENT_CONFIG_NOT_EXISTS)
    }

    signMap.put("GrantCode", channelAccessConfig.getMerchantId())
    String Nonce = String.valueOf((int) (Math.random() * 1000000))
    signMap.put("Nonce", Nonce)
    signMap.put("OpenId", channelAccessConfig.getAppId())
    String timestamp = String.valueOf(System.currentTimeMillis())
    signMap.put("Timestamp", timestamp)
    String token = getSignature(signMap)

    // 装填参数
    JSONObject postJson = new JSONObject()
    postJson.put("OpenId", channelAccessConfig.getAppId())
    postJson.put("GrantCode", channelAccessConfig.getMerchantId())
    postJson.put("Timestamp", timestamp)
    postJson.put("Nonce", Nonce)
    postJson.put("Token", token)
    postJson.put("Action", "communal.user.consume-reverse")

    //Params拼接
    JSONObject params = new JSONObject()
    params.put("bizId", request.getRelatedTransactionId())
    params.put("changeType", 3)
    postJson.put("Params", params)

    String jsonStr = JSON.toJSONString(postJson)
    LoggerUtil.info("QmaiOperation.refund is sending message: {0}, token: {1}.", jsonStr, token)

    byte[] result = HttpUtil.doPost(channelAccessConfig.getProperty("gateway_url"), jsonStr, getRequestHeader())
    if (null == result) {
      LoggerUtil.error("QmaiOperation.refund is failed with null result.", null)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, JSON.toJSONString(postJson))
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("QmaiOperation.refund received message: {0}, token: {1}.", resultJSON, token)

    String statusCode = String.valueOf(resultJSON.get("code"));
    if (!statusCode.equals("0")) {
      LoggerUtil.error("QmaiOperation.refund is failed, errorCode: {0}, errorMsg: {1},data:{2}", null, resultJSON.getString("code"),
          resultJSON.getString("errorMsg"), resultJSON.getString("message"))
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON.getString("message"))
    }

    ChannelRefundResponse response = new ChannelRefundResponse();
    response.setTransactionState(TransactionState.SUCCESS)
    response.setTransactionId(request.getTransactionId())
    return response
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("cancel"))
  }

  @Override
  ChannelNotificationResponse payNotify(HttpServletRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("payNotify"))
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    List<Map.Entry<String, Object>> sortList = getAsciiSort(rawMessage);

    StringBuilder sb = new StringBuilder()
    for (Map.Entry<String, Object> mapData : sortList) {
      String dataKey = mapData.getKey()
      Object dataValue = mapData.getValue()
      sb.append(dataKey).append("=").append(dataValue).append("&")
    }
    String sign = sb.toString()
    if (StringUtils.isNotBlank(sign) && StringUtils.isNotBlank(sign)) {
      sign = sign.substring(0, sign.lastIndexOf("&"))
    }
    String signStr = hmacSHA1Encrypt(sign, channel.getChannelAccessConfig().getAppKey())

    return signStr
  }

  static String hmacSHA1Encrypt(String encryptText, String encryptKey) throws Exception {
    byte[] data = encryptKey.getBytes("UTF-8")
    SecretKey secretKey = new SecretKeySpec(data, "HmacSHA1")
    Mac mac = Mac.getInstance("HmacSHA1")
    mac.init(secretKey)
    byte[] text = encryptText.getBytes("UTF-8")
    String str = new String(Base64.encodeBase64(mac.doFinal(text)), Charset.forName("UTF-8"))
    return URLEncoder.encode(str, "UTF-8")
  }

  private static List<Map.Entry<String, String>> getAsciiSort(Map<String, String> map) {
    List<Map.Entry<String, String>> infoIds = new ArrayList<>(map.entrySet())
    // 对所有传入参数按照字段名的 ASCII 码从小到大排序（字典序）
    Collections.sort(infoIds, new Comparator<Map.Entry<String, String>>() {
      int compare(Map.Entry<String, String> o1, Map.Entry<String, String> o2) {
        return ((String) o1.getKey()).compareToIgnoreCase((String) o2.getKey())
      }
    })
    return infoIds
  }


  private Map<String, String> getRequestHeader() {
    Map<String, String> header = new HashMap<>()
    header.put("Content-Type", "application/json")
    return header
  }

  private static BigDecimal getAliPayAmount(BigDecimal hexCloudAmount) {
    return hexCloudAmount / 100
  }

  private static boolean checkConfig(ChannelAccessConfig channelAccessConfig) {

    return StringUtils.isAnyBlank(channelAccessConfig.getAppId(), channelAccessConfig.getMerchantId(),
        channelAccessConfig.getProperty("gateway_url"),channelAccessConfig.getAppKey())

  }
}
