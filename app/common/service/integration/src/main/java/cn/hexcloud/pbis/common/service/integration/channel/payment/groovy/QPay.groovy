package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.commons.utils.cipher.RSAUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp

class QPay extends AbstractExternalChannelModule implements PaymentModule {

  private static final String API_SUCCESS_CODE = "0"
  private static final String SUCCESS_CODE = "01"
  private static final String PENDING_CODE = "03"
  private static final String CHARSET = "UTF-8"
  private static final String SIGN_TYPE = "RSA2"
  private static final String VERSION = "V1"
  private static final String FORMAT = "JSON"
  private static final String DEFAULT_PAY_TYPE = "000"
  private static final Map<String, String> METHOD_MAP

  QPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  static {
    METHOD_MAP = new HashMap<>()
    METHOD_MAP.put("pay", "qpay.pay.barcodepay")
    METHOD_MAP.put("query", "qpay.trade.query")
    METHOD_MAP.put("refund", "qpay.trade.refund")
    METHOD_MAP.put("cancel", "qpay.trade.cancel")
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("create"))
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("pay_type", DEFAULT_PAY_TYPE)
    bizParams.put("merchant_no", channel.getChannelAccessConfig().getMerchantId())
    bizParams.put("terminal_trace", request.getTransactionId())
    bizParams.put("terminal_time", getTerminalTime(request.getOrderTime()))
    bizParams.put("auth_no", request.getPayCode())
    bizParams.put("total_fee", getQPayAmount(request.getAmount()))
    bizParams.put("order_body", request.getOrderDescription())
    bizParams.put("terminal_no", channel.getChannelAccessConfig().getTerminalId())
    bizParams.put("client_id", ServiceContext.getString(ContextKeyConstant.STORE_CODE))

    // 发起请求
    JSONObject resultJSON = doRequest("pay", bizParams, true)

    // 解析并返回结果
    JSONObject dataNode = resultJSON.getJSONObject("data")
    String resultCode = dataNode.getString("result_code")
    ChannelPayResponse response = new ChannelPayResponse()
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(dataNode.getString("out_trade_no"))
    response.setChannel(request.getChannel())
    response.setPayMethod(mapPayMethod(dataNode.getString("pay_type")))
    response.setRealAmount(request.getAmount())
    response.setExtendedParams(request.getExtendedParams())
    if (SUCCESS_CODE == resultCode) {
      response.setTransactionState(TransactionState.SUCCESS)
    } else if (PENDING_CODE == resultCode) {
      response.setTransactionState(TransactionState.PENDING)
    } else {
      response.setTransactionState(TransactionState.FAILED)
    }
    if (response.getTransactionState() == TransactionState.FAILED && StringUtils.isNotBlank(dataNode.getString("return_msg"))) {
      response.setWarning(true)
      response.setWarningMessage(dataNode.getString("return_msg"))
    }
    return response
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("pay_type", DEFAULT_PAY_TYPE)
    bizParams.put("merchant_no", channel.getChannelAccessConfig().getMerchantId())
    bizParams.put("terminal_trace", request.getTransactionId())
    bizParams.put("terminal_time", getTerminalTime(new Date()))
    bizParams.put("pay_trace", request.getTransactionId())
    bizParams.put("out_trade_no", request.getTpTransactionId())
    bizParams.put("terminal_no", channel.getChannelAccessConfig().getTerminalId())

    // 发起请求
    JSONObject resultJSON = doRequest("query", bizParams, false)

    // 解析并返回结果
    JSONObject dataNode = resultJSON.getJSONObject("data")
    String resultCode = dataNode.getString("result_code")
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setChannel(request.getChannel())
    response.setPayMethod(mapPayMethod(dataNode.getString("pay_type")))
    response.setTransactionId(dataNode.getString("pay_trace"))
    response.setTpTransactionId(dataNode.getString("out_trade_no"))
    response.setRealAmount(getHexCloudAmount(dataNode.getString("total_fee")))
    if (SUCCESS_CODE == resultCode) {
      response.setTransactionState(TransactionState.SUCCESS)
    } else if (PENDING_CODE == resultCode) {
      response.setTransactionState(TransactionState.PENDING)
    } else {
      response.setTransactionState(TransactionState.FAILED)
    }
    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("pay_type", mapPayType(request.getPayMethod()))
    bizParams.put("merchant_no", channel.getChannelAccessConfig().getMerchantId())
    bizParams.put("pay_trace", request.getRelatedTransactionId())
    bizParams.put("out_trade_no", request.getRelatedTPTransactionId())
    bizParams.put("refund_fee", getQPayAmount(request.getAmount()))
    bizParams.put("terminal_no", channel.getChannelAccessConfig().getTerminalId())

    // 发起请求
    JSONObject resultJSON = doRequest("refund", bizParams, true)

    // 解析并返回结果
    JSONObject dataNode = resultJSON.getJSONObject("data")
    String resultCode = dataNode.getString("result_code")
    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(resultJSON.getString("out_refund_no"))
    response.setRealAmount(request.getAmount())
    if (SUCCESS_CODE == resultCode || PENDING_CODE == resultCode) {
      response.setTransactionState(TransactionState.PENDING)
    } else {
      response.setTransactionState(TransactionState.FAILED)
    }
    if (response.getTransactionState() == TransactionState.FAILED && StringUtils.isNotBlank(dataNode.getString("return_msg"))) {
      response.setWarning(true)
      response.setWarningMessage(dataNode.getString("return_msg"))
    }

    return response
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("pay_type", mapPayType(request.getPayMethod()))
    bizParams.put("merchant_no", channel.getChannelAccessConfig().getMerchantId())
    bizParams.put("terminal_trace", request.getTransactionId())
    bizParams.put("out_trade_no", request.getRelatedTPTransactionId())
    bizParams.put("refund_fee", getQPayAmount(request.getAmount()))
    bizParams.put("terminal_no", channel.getChannelAccessConfig().getTerminalId())
    bizParams.put("terminal_time", getTerminalTime(request.getOrderTime()))

    // 发起请求
    JSONObject resultJSON = doRequest("refund", bizParams, true)

    // 解析并返回结果
    JSONObject dataNode = resultJSON.getJSONObject("data")
    String resultCode = dataNode.getString("result_code")
    ChannelCancelResponse response = new ChannelCancelResponse()
    response.setTpTransactionId(resultJSON.getString("out_trade_no"))
    response.setRealAmount(request.getAmount())
    if (SUCCESS_CODE == resultCode) {
      response.setTransactionState(TransactionState.SUCCESS)
    } else if (PENDING_CODE == resultCode) {
      response.setTransactionState(TransactionState.PENDING)
    } else {
      response.setTransactionState(TransactionState.FAILED)
    }
    if (response.getTransactionState() == TransactionState.FAILED && StringUtils.isNotBlank(dataNode.getString("return_msg"))) {
      response.setWarning(true)
      response.setWarningMessage(dataNode.getString("return_msg"))
    }

    return response
  }

  @Override
  ChannelNotificationResponse payNotify(HttpServletRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("payNotify"))
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    StringBuilder sb = new StringBuilder()
    for (Map.Entry<String, String> entry : rawMessage.entrySet()) {
      if (null != entry.getValue()) {
        sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&")
      }
    }

    String dataBeforeSign = sb.substring(0, sb.length() - 1)
    return RSAUtil.signByPrivateKey(dataBeforeSign.getBytes(StandardCharsets.UTF_8), channel.getChannelAccessConfig().getPrivateKey())
  }

  @Override
  boolean isValidSignature(Map<String, String> unverifiedMessage) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("isValidSignature"))
  }

  private static PayMethod mapPayMethod(String tpPayMethod) {
    PayMethod payMethod
    switch (tpPayMethod) {
      case "010":
        payMethod = PayMethod.WX_PAY
        break
      case "020":
        payMethod = PayMethod.ALI_PAY
        break
      default:
        payMethod = PayMethod.OTHERS
    }
    return payMethod
  }

  private static String mapPayType(PayMethod payMethod) {
    String payType
    switch (payMethod) {
      case PayMethod.WX_PAY:
        payType = "010"
        break
      case PayMethod.ALI_PAY:
        payType = "020"
        break
      default:
        payType = DEFAULT_PAY_TYPE
    }
    return payType
  }

  private static String getTimestamp() {
    return DateUtil.getDate("yyyy-MM-dd HH:mm:ss")
  }

  private static String getTerminalTime(Date orderTime) {
    return DateUtil.getDate(orderTime, "yyyyMMddHHmmss")
  }

  private static BigDecimal getHexCloudAmount(String tpAmount) {
    return new BigDecimal(tpAmount) * 100
  }

  private static BigDecimal getQPayAmount(BigDecimal hexCloudAmount) {
    return hexCloudAmount / 100
  }

  private JSONObject doRequest(String method, Map<String, Object> bizParams, boolean reserveData) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

    // 请求参数
    Map<String, Object> body = new HashMap<>()
    body.put("biz_content", JSON.toJSONString(bizParams))

    // 签名
    Map<String, String> rawMessage = new TreeMap<>()
    rawMessage.put("app_id", channelAccessConfig.getAppId())
    rawMessage.put("method", METHOD_MAP.get(method))
    rawMessage.put("format", FORMAT)
    rawMessage.put("charset", CHARSET)
    rawMessage.put("sign_type", SIGN_TYPE)
    rawMessage.put("timestamp", getTimestamp())
    rawMessage.put("version", VERSION)
    for (Map.Entry<String, Object> entry : body) {
      rawMessage.put(entry.getKey(), entry.getValue().toString())
    }
    String sign = getSignature(rawMessage)

    // 发起HTTP请求
    String methodFullName = getMethodFullName(method)
    StringBuilder requestUrlBuilder = new StringBuilder()
    requestUrlBuilder.append(channelAccessConfig.getProperty("gateway_url")).append("?")
    for (Map.Entry<String, String> entry : rawMessage.entrySet()) {
      if (entry.getKey() == "biz_content") {
        continue
      }
      requestUrlBuilder.append(entry.getKey()).append("=").append(URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8.name())).append("&")
    }
    String requestUrl = requestUrlBuilder.append("sign=").append(URLEncoder.encode(sign, StandardCharsets.UTF_8.name())).toString()
    String bodyJSONStr = JSON.toJSONString(body)
    LoggerUtil.info("{0} is sending message to: {1}, body: {2}.", methodFullName, requestUrl, bodyJSONStr)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result = HttpUtil.doPost(requestUrl, bodyJSONStr)
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
    boolean status = resultJSON.getBoolean("status")
    String errorCode = resultJSON.getString("code")
    String errorMessage = resultJSON.getString("message")
    if (!status || errorCode != API_SUCCESS_CODE) {
      // 请求失败
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, methodFullName, errorCode, errorMessage)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }

    return resultJSON
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }

}
