package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Promotion
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils
import org.springframework.util.DigestUtils

import java.nio.charset.StandardCharsets
import java.sql.Timestamp
import java.text.MessageFormat

class SeltekBalPay extends AbstractExternalChannelModule implements PaymentModule {
  // 接口请求成功状态码
  private static final int SUCCESS_CODE = 200
  private static final int WAIT_PAY = 100016
  private static final Map<String, String> PATH_MAP

  SeltekBalPay(ExternalChannel channel) {
    super(channel)
  }

  static {
    PATH_MAP = new HashMap<>()
    PATH_MAP.put("pay", "/openapi/accountopen/deduction")
    PATH_MAP.put("query", "/openapi/accountopen/checkPay")
    PATH_MAP.put("cancel", "/openapi/accountopen/return")
    PATH_MAP.put("refund", "/openapi/accountopen/return")
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("create"))
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    // 请求参数
    Map<String, String> bizParams = new HashMap<>()
    bizParams.put("payCode", request.getPayCode())
    bizParams.put("storeCode", ServiceContext.getString(ContextKeyConstant.STORE_CODE))
    bizParams.put("useAmount", String.valueOf(request.getAmount().intValue()))
    bizParams.put("externalId", request.getTransactionId())
    bizParams.put("extOrderCode", request.getOrderNo())

    // 发起请求
    JSONObject resultJSON = doRequest(bizParams, "pay", true)
    // 解析并返回结果
    JSONObject data  = resultJSON.getJSONObject("data")
    ChannelPayResponse response = new ChannelPayResponse()
    List<Promotion> promotions = new ArrayList<>()
    Promotion promotion = new Promotion()
    promotion.setDiscount(BigDecimal.valueOf(data.getIntValue("useAmount") + data.getIntValue("useGiftAmount")))
    promotion.setUserPayAmount(BigDecimal.valueOf(data.getIntValue("useAmount")))
    promotion.setMerchantPayAmount(BigDecimal.valueOf(data.getIntValue("useGiftAmount")))
    promotions.add(promotion)
    response.setPromotions(promotions)
    response.setTransactionState(TransactionState.SUCCESS)
    response.setChannel(request.getChannel())
    response.setPayMethod(PayMethod.SELTEKBAL_PAY)
    response.setTransactionId(request.getTransactionId())
    return response
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    // 请求参数
    Map<String, String> bizParams = new HashMap<>()
    bizParams.put("externalId", request.getRelatedTransactionId())
    // 发起请求
    JSONObject resultJSON = doRequest(bizParams, "cancel", true)
    // 解析并返回结果
    ChannelCancelResponse response = new ChannelCancelResponse()
    response.setTransactionState(TransactionState.SUCCESS)
    return response
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    // 请求参数
    Map<String, String> bizParams = new HashMap<>()
    bizParams.put("externalId", request.getTransactionId())
    // 发起请求
    JSONObject resultJSON = doRequest(bizParams, "query", false)
    // 解析并返回结果
    ChannelQueryResponse response = new ChannelQueryResponse()
    if (WAIT_PAY == resultJSON.getIntValue("code")) {
      response.setTransactionState(TransactionState.WAITING)
      return response
    }
    response.setTransactionState(TransactionState.SUCCESS)
    response.setPayMethod(PayMethod.SELTEKBAL_PAY)
    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, String> bizParams = new HashMap<>()
    bizParams.put("externalId", request.getRelatedTransactionId())
    // 发起请求
    JSONObject resultJSON = doRequest(bizParams, "refund", true)
    // 解析并返回结果
    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionState(TransactionState.SUCCESS)
    return response
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    StringBuffer sb = new StringBuffer()
    //参数按照ACCSii排序(升序)
    Map<String, String> sortedMap = new TreeMap<String, String>(rawMessage)
    Set set = sortedMap.entrySet()
    Iterator it = set.iterator()
    int i = 0
    while (it.hasNext()) {
      Map.Entry entry = (Map.Entry) it.next()
      String k = entry.getKey()
      String value = entry.getValue()
      if (StringUtils.isBlank(value)) {
        continue
      }
      if (i == 0) {
        sb.append(MessageFormat.format("{0}={1}", k, value))
        ++i
      } else {
        sb.append(MessageFormat.format("&{0}={1}", k, value))
      }
    }
    sb.append(MessageFormat.format("&{0}={1}", "key", channel.getChannelAccessConfig().getAppKey()))
    String params = sb.toString()
    String sign = DigestUtils.md5DigestAsHex(params.getBytes("UTF-8"))
    return sign
  }

  private JSONObject doRequest(Map<String, String> params, String method, boolean reserveData) {
    String sign = getSignature(params)

    JSONObject res = new JSONObject()
    res.put('code', 400)
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    String url = channelAccessConfig.getGatewayUrl() + PATH_MAP.get(method)

    String methodFullName = getMethodFullName(method)
    String json = JSON.toJSONString(params)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, json)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
    }
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, json)
    byte[] result = HttpUtil.doPost(url, json, getRequestHeader(sign))
    if (null == result) {
      LoggerUtil.error("{0} is failed with null result.", null, methodFullName)
      return res
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    if (SUCCESS_CODE != resultJSON.getIntValue("code") && WAIT_PAY != resultJSON.getIntValue("code")) {
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, resultJSON.getString('message'))
    }
    return resultJSON
  }

  private Map<String, String> getRequestHeader(String sign) {
    Map<String, String> header = new HashMap<>()
    header.put("tenantId", "1")
    header.put("channelId", channel.getChannelAccessConfig().getMerchantId())
    header.put("sign", sign)
    return header
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }
}
