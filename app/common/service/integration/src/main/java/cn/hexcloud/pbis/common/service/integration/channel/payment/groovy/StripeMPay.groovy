package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.stripe.Stripe
import com.stripe.exception.StripeException
import com.stripe.model.PaymentIntent
import com.stripe.model.Refund
import com.stripe.net.RequestOptions
import com.stripe.param.PaymentIntentCancelParams
import com.stripe.param.PaymentIntentCreateParams
import com.stripe.param.RefundCreateParams

import javax.servlet.http.HttpServletRequest

class StripeMPay extends AbstractExternalChannelModule implements PaymentModule {

    StripeMPay(ExternalChannel channel) {
        super(channel)
    }

    @Override
    protected String getSignModuleName() {
        return this.getModuleName()
    }

    @Override
    String getModuleName() {
        return "Payment"
    }

    // 解决 stripe sdk gson 版本冲突导致 class not found 异常
    // https://github.com/stripe/stripe-java/issues/1525
    @Override
    ChannelCreateResponse create(ChannelCreateRequest request) {
        String methodFullName = getMethodFullName("create")
        Stripe.apiKey = channel.getChannelAccessConfig().getPrivateKey()

        // new一个json存储租户ID，渠道，transactionId，stripe会在查询和webhook返回
        JSONObject metadata = new JSONObject()
        metadata.put("pid", ServiceContext.getString(ContextKeyConstant.PARTNER_ID))
        metadata.put("sid", ServiceContext.getString(ContextKeyConstant.STORE_ID))
        metadata.put("transactionId", request.getTransactionId())
        metadata.put("channel", channel.getChannelAccessConfig().getChannelCode())

        // Create a PaymentIntent with the order amount and currency
        ChannelCreateResponse response = new ChannelCreateResponse()
        // 设置账户
        RequestOptions requestOptions = RequestOptions.builder()
                .setStripeAccount(channel.getChannelAccessConfig().getMerchantId())
                .build()
        PaymentIntentCreateParams.Shipping shipping = PaymentIntentCreateParams.Shipping.builder()
                .setName("Elyse")
                .setPhone("+***********")
                .setAddress(PaymentIntentCreateParams.Shipping.Address.builder()
                        .setCountry("Australia")
                        .setCity("Melbourne")
                        .setLine1("120 Turner St Port Melbourne VIC 3207")
                        .build())
                .build()
        PaymentIntentCreateParams params =
                PaymentIntentCreateParams.builder()
                        .setAmount(request.getAmount().longValue())
                        .setShipping(shipping)
                        .setCurrency(request.getCurrency())
                        .setStatementDescriptorSuffix("TamJai Mixian")
                        .putMetadata("extra", metadata.toString())
                        .setAutomaticPaymentMethods(PaymentIntentCreateParams.AutomaticPaymentMethods
                                .builder()
                                .setEnabled(true)
                                .build())
                        .setUseStripeSdk(true)
                        .build()
        try {
            // Create
            PaymentIntent intent = PaymentIntent.create(params, requestOptions)
            response.setTpTransactionId(intent.getId())
            response.setPrePayId(intent.getClientSecret())
            LoggerUtil.info("{0} received message: {1}.", methodFullName, intent.toJson())
        } catch (StripeException e) {
            LoggerUtil.error("Error creating PaymentIntent: ", e)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, e.getMessage())
        } catch (Exception ex) {
            LoggerUtil.error("Error creating PaymentIntent: ", ex)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, ex.getMessage())
        }
        return response
    }

    private static TransactionState mapTransactionState(String tpTransactionState) {
        TransactionState transactionState
        switch (tpTransactionState) {
            case "succeeded":
                transactionState = TransactionState.SUCCESS
                break
            case "processing":
                transactionState = TransactionState.PENDING
                break
            case "requires_payment_method":
                transactionState = TransactionState.WAITING
                break
            case "requires_confirmation":
                transactionState = TransactionState.WAITING
                break
            case "requires_action":
                transactionState = TransactionState.WAITING
                break
            case "requires_capture":
                transactionState = TransactionState.WAITING
                break
            case "payment_failed":
                transactionState = TransactionState.FAILED
                break
            case "pending":
                transactionState = TransactionState.PENDING
                break
            default:
                transactionState = TransactionState.UNKNOWN
        }
        return transactionState
    }

    // 取消
    ChannelCancelResponse cancel(ChannelCancelRequest request) {
        String methodFullName = getMethodFullName("cancel")
        Stripe.apiKey = channel.getChannelAccessConfig().getPrivateKey()
        ChannelCancelResponse response = new ChannelCancelResponse()
        try {
            PaymentIntent resource = PaymentIntent.retrieve(request.getRelatedTPTransactionId())
            // 设置账户
            RequestOptions requestOptions = RequestOptions.builder()
                    .setStripeAccount(channel.getChannelAccessConfig().getMerchantId())
                    .build()
            PaymentIntentCancelParams params = PaymentIntentCancelParams.builder()
                    .setCancellationReason(PaymentIntentCancelParams.CancellationReason.REQUESTED_BY_CUSTOMER)
                    .build()
            PaymentIntent paymentIntent = resource.cancel(params, requestOptions)
            LoggerUtil.info("{0} received message: {1}.", methodFullName, paymentIntent.toJson())
        } catch (StripeException e) {
            LoggerUtil.error("Error creating PaymentIntent: ", e)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, e.getMessage())
        } catch (Exception ex) {
            LoggerUtil.error("Error creating PaymentIntent: ", ex)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, ex.getMessage())
        }
        return response
    }

    // 退款
    @Override
    ChannelRefundResponse refund(ChannelRefundRequest request) {
        String methodFullName = getMethodFullName("refund")
        Stripe.apiKey = channel.getChannelAccessConfig().getPrivateKey()
        ChannelRefundResponse response = new ChannelRefundResponse()
        try {
            // 设置账户
            RequestOptions requestOptions = RequestOptions.builder()
                    .setStripeAccount(channel.getChannelAccessConfig().getMerchantId())
                    .build()
            RefundCreateParams params = RefundCreateParams.builder()
                    .setPaymentIntent(request.getRelatedTPTransactionId())
                    .setAmount(request.getAmount().longValue())
                    .build();
            Refund refund = Refund.create(params, requestOptions)
            response.setTpTransactionId(refund.getId())
            response.setTransactionState(mapTransactionState(refund.getStatus()))
            response.setTransactionId(refund.getCharge())
            LoggerUtil.info("{0} received message: {1}.", methodFullName, refund.toJson())
        } catch (StripeException e) {
            LoggerUtil.error("Error creating PaymentIntent: ", e)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, e.getMessage())
        } catch (Exception ex) {
            LoggerUtil.error("Error creating PaymentIntent: ", ex)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, ex.getMessage())
        }
        return response
    }


    @Override
    ChannelQueryResponse query(ChannelQueryRequest request) {
        String methodFullName = getMethodFullName("query")
        Stripe.apiKey = channel.getChannelAccessConfig().getPrivateKey()
        ChannelQueryResponse response = new ChannelQueryResponse()
        try {
            // 设置账户
            RequestOptions requestOptions = RequestOptions.builder()
                    .setStripeAccount(channel.getChannelAccessConfig().getMerchantId())
                    .build()
            PaymentIntent paymentIntent = PaymentIntent.retrieve(request.getTpTransactionId(), requestOptions)
            response.setTpTransactionId(paymentIntent.getId())
            response.setTransactionState(mapTransactionState(paymentIntent.getStatus()))
            LoggerUtil.info("{0} received message: {1}.", methodFullName, paymentIntent.toJson())

        } catch (StripeException e) {
            LoggerUtil.error("Error creating PaymentIntent: ", e)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, e.getMessage())
        } catch (Exception ex) {
            LoggerUtil.error("Error creating PaymentIntent: ", ex)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, ex.getMessage())
        }
        return response
    }

    private static TransactionState mapPayNotifyState(String type) {
        switch (type) {
            case "payment_intent.succeeded":
                // Then define and call a function to handle the event payment_intent.succeeded
                return TransactionState.SUCCESS
            case "payment_intent.payment_failed":
                return TransactionState.FAILED
            default:
                return TransactionState.UNKNOWN
        }
    }

    @Override
    ChannelNotificationResponse payNotify(HttpServletRequest request) {
        String methodFullName = getMethodFullName("payNotify")
        Stripe.apiKey = channel.getChannelAccessConfig().getPrivateKey()
//        String payload = request.getParameter("payload")
        String payload = "{  \"id\": \"evt_3QQ3qCEf6VjXcP7g0vUeqhLW\",  \"object\": \"event\",  \"account\": \"acct_1QHxWnEf6VjXcP7g\",  \"api_version\": \"2024-04-10\",  \"created\": **********,  \"data\": {    \"object\": {      \"id\": \"pi_3QQ3qCEf6VjXcP7g0nMLHjJP\",      \"object\": \"payment_intent\",      \"amount\": 5146,      \"amount_capturable\": 0,      \"amount_details\": {        \"tip\": {        }      },      \"amount_received\": 5146,      \"application\": \"ca_QKIPDmhvy9aKp0LXoxLY6vxyODWWTx3C\",      \"application_fee_amount\": null,      \"automatic_payment_methods\": {        \"allow_redirects\": \"always\",        \"enabled\": true      },      \"canceled_at\": null,      \"cancellation_reason\": null,      \"capture_method\": \"automatic\",      \"client_secret\": \"pi_3QQ3qCEf6VjXcP7g0nMLHjJP_secret_MBJhcln1hDdjKwU4cWUPzVM7q\",      \"confirmation_method\": \"automatic\",      \"created\": **********,      \"currency\": \"aud\",      \"customer\": null,      \"description\": null,      \"invoice\": null,      \"last_payment_error\": null,      \"latest_charge\": \"ch_3QQ3qCEf6VjXcP7g01uLV3jo\",      \"livemode\": true,      \"metadata\": {        \"extra\": \"{\\\"channel\\\":\\\"StripeMPay\\\",\\\"pid\\\":\\\"101\\\",\\\"transactionId\\\":\\\"99ce7558-2c88-447c-baba-158ee67e9529\\\",\\\"sid\\\":\\\"*******************\\\"}\"      },      \"next_action\": null,      \"on_behalf_of\": null,      \"payment_method\": \"pm_1QQ3r6Ef6VjXcP7guDTggtVu\",      \"payment_method_configuration_details\": {        \"id\": \"pmc_1QNVKoEf6VjXcP7gjXpLbRhR\",        \"parent\": \"pmc_1PTdp82LmLYPoz59vBXTbLmx\"      },      \"payment_method_options\": {        \"card\": {          \"installments\": null,          \"mandate_options\": null,          \"network\": null,          \"request_three_d_secure\": \"automatic\"        },        \"link\": {          \"persistent_token\": null        }      },      \"payment_method_types\": [        \"card\",        \"link\"      ],      \"processing\": null,      \"receipt_email\": null,      \"review\": null,      \"setup_future_usage\": null,      \"shipping\": {        \"address\": {          \"city\": \"Melbourne\",          \"country\": \"Australia\",          \"line1\": \"231 Swanston St, Melbourne, VIC 3000\",          \"line2\": null,          \"postal_code\": null,          \"state\": null        },        \"carrier\": null,        \"name\": \"Elyse\",        \"phone\": \"0383514708\",        \"tracking_number\": null      },      \"source\": null,      \"statement_descriptor\": null,      \"statement_descriptor_suffix\": \"TamJai Mixian\",      \"status\": \"succeeded\",      \"transfer_data\": null,      \"transfer_group\": null    }  },  \"livemode\": true,  \"pending_webhooks\": 1,  \"request\": {    \"id\": \"req_n07a2rft4BTpvL\",    \"idempotency_key\": \"b612e060-6c52-4108-8b79-ae8830b5062e\"  },  \"type\": \"payment_intent.succeeded\"}"
        LoggerUtil.info("{0} received message: {1}.", methodFullName, payload)

        ChannelNotificationResponse response = new ChannelNotificationResponse()
        ChannelPayResponse payResponse = new ChannelPayResponse()
        JSONObject eventJson = JSON.parseObject(payload)
        payResponse.setTransactionState(mapPayNotifyState(eventJson.getString("type")))
        JSONObject data = eventJson.getJSONObject("data")
        JSONObject object = data.getJSONObject("object")
        if (object.containsKey("metadata")) {
            JSONObject metadata = object.getJSONObject("metadata")
            if (Objects.nonNull(metadata)) {
                JSONObject extra = JSON.parseObject(metadata.getString("extra"))
                payResponse.setTransactionId(extra.getString("transactionId"))
                payResponse.setChannel(channel.getChannelAccessConfig().getChannelCode())
                payResponse.setTpTransactionId(object.getString("id"))
            } else {
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "metadata is null")
            }
        } else {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "metadata is null")
        }
        // 扩展字段设置为空
        payResponse.setExtendedParams("")
        payResponse.setPayMethod(PayMethod.OTHERS)
        response.setPayResponse(payResponse)
        return response
    }

    private String getMethodFullName(String method) {
        return channel.getChannelCode() + "." + getModuleName() + "." + method
    }

}
