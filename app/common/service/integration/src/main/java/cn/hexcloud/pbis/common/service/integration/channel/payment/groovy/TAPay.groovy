package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.commons.utils.cipher.RSAUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp

class TAPay extends AbstractExternalChannelModule implements PaymentModule {

  private static final String SUCCESS_CODE = "100"

  TAPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("create"))
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("qrCode", request.getPayCode())
    bizParams.put("outTradeNo", request.getTransactionId())
    bizParams.put("orderAmount", request.getAmount())
    bizParams.put("tradeTime", getTimestamp())

    // 发起请求
    JSONObject resultJSON = doRequest("pay", bizParams, true)

    // 解析并返回结果
    ChannelPayResponse response = new ChannelPayResponse()
    JSONObject dataNode = resultJSON.getJSONObject("data")
    response.setTransactionState(mapTransactionState(dataNode.getString("payStatus")))
    response.setChannel(request.getChannel())
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(dataNode.getString("orderNo"))
    response.setRealAmount(request.getAmount())
    response.setExtendedParams(request.getExtendedParams())
    return response
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("outTradeNo", request.getTransactionId())

    // 发起请求
    JSONObject resultJSON = doRequest("query", bizParams, false)

    // 解析并返回结果
    ChannelQueryResponse response = new ChannelQueryResponse()
    JSONObject dataNode = resultJSON.getJSONObject("data")
    response.setChannel(request.getChannel())
    response.setTransactionId(dataNode.getString("outTradeNo"))
    response.setTpTransactionId(dataNode.getString("orderNo"))
    response.setRealAmount(dataNode.getBigDecimal("orderAmount"))
    response.setTransactionState(mapTransactionState(dataNode.getString("payStatus")))
    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new TreeMap<>()
    bizParams.put("outRefundNo", request.getTransactionId())
    bizParams.put("outTradeNo", request.getRelatedTransactionId())
    bizParams.put("refundAmount", request.getAmount())

    // 发起请求
    JSONObject resultJSON = doRequest("refund", bizParams, true)

    // 解析并返回结果
    ChannelRefundResponse response = new ChannelRefundResponse()
    JSONObject dataNode = resultJSON.getJSONObject("data")
    response.setTransactionState(TransactionState.PENDING)
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(dataNode.getString("refundNo"))
    response.setRealAmount(request.getAmount())
    return response
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("cancel"))
  }

  @Override
  ChannelNotificationResponse payNotify(HttpServletRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("payNotify"))
  }

  @Override
  String getSignature(Map<String, String> rawMessage) {
    StringBuilder sb = new StringBuilder()
    for (Map.Entry<String, String> entry : rawMessage.entrySet()) {
      if (null != entry.getValue()) {
        sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&")
      }
    }

    String dataBeforeSign = sb.substring(0, sb.length() - 1)
    return RSAUtil.signByPrivateKey(dataBeforeSign.getBytes(StandardCharsets.UTF_8), channel.getChannelAccessConfig().getPrivateKey())
  }

  @Override
  boolean isValidSignature(Map<String, String> unverifiedMessage) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("isValidSignature"))
  }

  private static TransactionState mapTransactionState(String taTransactionState) {
    TransactionState transactionState
    switch (taTransactionState) {
      case "1":
        transactionState = TransactionState.SUCCESS
        break
      case "2":
        transactionState = TransactionState.FAILED
        break
      case "3":
        transactionState = TransactionState.CANCELED
        break
      case "0":
      case "4":
      case "7":
        transactionState = TransactionState.PENDING
        break
      case "5":
      case "6":
        transactionState = TransactionState.REFUNDED
        break
      default:
        throw new CommonException(ServiceError.UNKNOWN_THIRD_PARTY_TRANSACTION_STATUS)
    }
    return transactionState
  }

  private static PayMethod mapPayMethod(String tpPayMethod) {
    PayMethod payMethod
    switch (tpPayMethod) {
      case "010":
        payMethod = PayMethod.WX_PAY
        break
      case "020":
        payMethod = PayMethod.ALI_PAY
        break
      case "060":
        payMethod = PayMethod.QQ_PAY
        break
      case "080":
        payMethod = PayMethod.JD_PAY
        break
      case "090":
        payMethod = PayMethod.KOUBEI
        break
      case "100":
        payMethod = PayMethod.BEST_PAY
        break
      case "110":
        payMethod = PayMethod.UNION_PAY
        break
      default:
        payMethod = PayMethod.OTHERS
    }
    return payMethod
  }

  private static String getTimestamp() {
    return DateUtil.getDate("yyyy-MM-dd HH:mm:ss")
  }

  private JSONObject doRequest(String method, Map<String, Object> bizParams, boolean reserveData) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

    // 请求参数
    Map<String, Object> body = new HashMap<>()
    body.put("appId", channelAccessConfig.getAppId())
    body.put("storeCode", ServiceContext.getString(ContextKeyConstant.STORE_CODE))
    body.putAll(bizParams)

    // 签名
    Map<String, String> rawMessage = new TreeMap<>()
    for (Map.Entry<String, Object> entry : body) {
      rawMessage.put(entry.getKey(), entry.getValue().toString())
    }
    body.put("sign", getSignature(rawMessage))

    // 发起HTTP请求
    String methodFullName = getMethodFullName(method)
    String bodyJSON = JSON.toJSONString(body)
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result = HttpUtil.doPost(channelAccessConfig.getProperty(method + "_url"), bodyJSON)
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)

    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyJSON)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }

    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
    String errorCode = resultJSON.getString("code")
    String errorMessage = resultJSON.getString("message")
    if (errorCode != SUCCESS_CODE) {
      // 请求失败
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, methodFullName, errorCode, errorMessage)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }

    return resultJSON
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }

}
