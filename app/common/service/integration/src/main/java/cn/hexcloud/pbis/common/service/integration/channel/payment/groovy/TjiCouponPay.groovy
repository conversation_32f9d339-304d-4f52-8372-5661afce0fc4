package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.service.integration.tji.CancelRequest
import cn.hexcloud.pbis.common.service.integration.tji.CommonResponse
import cn.hexcloud.pbis.common.service.integration.tji.ConsumeRequest
import cn.hexcloud.pbis.common.service.integration.tji.QueryRequest
import cn.hexcloud.pbis.common.service.integration.tji.TjiCoupon
import cn.hexcloud.pbis.common.service.integration.tji.client.TjiCouponClient
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils

import java.text.SimpleDateFormat

/**
 * 谭仔卡券支付
 *
 * <AUTHOR> Wang
 */
class TjiCouponPay extends AbstractExternalChannelModule implements PaymentModule {

    static final String SUCCESS_CODE = "200"

    TjiCouponPay(ExternalChannel channel) {
        super(channel)
    }

    @Override
    protected String getSignModuleName() {
        return this.getModuleName()
    }

    @Override
    String getModuleName() {
        return "Payment"
    }

    @Override
    ChannelPayResponse pay(ChannelPayRequest request) {
        String methodFullName = getFullMethodName("pay")

        ChannelPayResponse response = new ChannelPayResponse()
        response.setTransactionId(request.getTransactionId())
        response.setPayMethod(PayMethod.TJI_COUPON_PAY)

        // 判断是否是mock数据
        String isMock = channel.channelAccessConfig.getProperty("is_mock")
        if (StringUtils.isNotEmpty(isMock) && Boolean.valueOf(isMock)) {
            response.setTransactionState(TransactionState.SUCCESS)
            return response
        }

        // 参数：extendedParams
        String extendedParams = request.getExtendedParams()
        if (StringUtils.isEmpty(extendedParams)) {
            throw new CommonException(ServiceError.PARAM_CONSTRAINTS_VIOLATION, "Param 'extendedParams' is null")
        }

        // 参数：extendedParams.ticket_no
        JSONObject extendedParamJSON = JSONObject.parseObject(extendedParams)
        String ticketNo = extendedParamJSON.getString("ticket_no")
        if (StringUtils.isEmpty(ticketNo)) {
            throw new CommonException(ServiceError.PARAM_CONSTRAINTS_VIOLATION, "Param 'extendedParams.ticket_no' is null")
        }
        // 参数：extendedParams.store_code
        String storeCode = extendedParamJSON.getString("store_code")
        if (StringUtils.isEmpty(storeCode)) {
            throw new CommonException(ServiceError.PARAM_CONSTRAINTS_VIOLATION, "Param 'extendedParams.store_code' is null")
        }
        // 参数：extendedParams.pur_value
        Integer purValue = extendedParamJSON.getInteger("pur_value")
        if (purValue == null) {
            throw new CommonException(ServiceError.PARAM_CONSTRAINTS_VIOLATION, "Param 'extendedParams.pur_value' is null")
        }
        // 参数：extendedParams.cost
        Integer cost = extendedParamJSON.getInteger("cost")
        if (cost == null) {
            throw new CommonException(ServiceError.PARAM_CONSTRAINTS_VIOLATION, "Param 'extendedParams.cost' is null")
        }

        // 参数：transactionTime
        Date transactionTime = request.getTransactionTime()
        if (transactionTime == null) {
            throw new CommonException(ServiceError.PARAM_CONSTRAINTS_VIOLATION, "Param 'transactionTime' is null")
        }

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd HH:mm")
        String currentTime = simpleDateFormat.format(transactionTime)
        String[] currentTimes = currentTime.split(" ")
        String optDate = currentTimes[0]
        String optTime = currentTimes[1]

        // 设置请求参数
        ConsumeRequest consumeRequest = ConsumeRequest
                .newBuilder()
                .setCouponNo(request.getPayCode())
                .setAmount(request.getAmount().toInteger())
                .setShopNo(storeCode)
                .setBillNo(ticketNo)
                .setOptDate(optDate)
                .setOptTime(optTime)
                .setParValue(purValue)
                .setCost(cost)
                .build()

        String requestJSON = consumeRequest.toString()
        LoggerUtil.info("{0} is sending message: {1}.", methodFullName, requestJSON)

        // 请求
        TjiCouponClient tjiCouponClient = SpringContextUtil.bean(TjiCouponClient.class)
        CommonResponse commonResponse = tjiCouponClient.consume(consumeRequest)
        String responseJSON = commonResponse.toString()
        LoggerUtil.info("{0} received message: {1}.", methodFullName, responseJSON)

        TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, requestJSON)
        TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, responseJSON)

        // 设置返回参数
        String bizCode = commonResponse.getCode()
        String errorMessage = commonResponse.getMessage()
        if (SUCCESS_CODE != bizCode) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
        }

        TjiCoupon coupon = commonResponse.getData()
        if (coupon == null) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Return coupon info is null")
        }

        // 补贴金额
        Map<String, Object> param = new HashMap<>()
        param.put("merchant_allowance", coupon.getMerchantAllowance())
        response.setExtendedParams(JSONObject.toJSONString(param))
        response.setTransactionState(TransactionState.SUCCESS)
        return response
    }

    @Override
    ChannelRefundResponse refund(ChannelRefundRequest request) {
        String methodFullName = getFullMethodName("refund")
        ChannelRefundResponse response = new ChannelRefundResponse()

        // 判断是否是mock数据
        String isMock = channel.channelAccessConfig.getProperty("is_mock")
        if (StringUtils.isNotEmpty(isMock) && Boolean.valueOf(isMock)) {
            response.setTransactionState(TransactionState.SUCCESS)
            return response
        }

        // 设置请求参数
        CancelRequest cancelRequest = CancelRequest
                .newBuilder()
                .setCouponNo(request.getPayCode())
                .build()

        String requestJSON = cancelRequest.toString()
        LoggerUtil.info("{0} is sending message: {1}.", methodFullName, requestJSON)

        // 请求
        TjiCouponClient tjiCouponClient = SpringContextUtil.bean(TjiCouponClient.class)
        CommonResponse commonResponse = tjiCouponClient.cancel(cancelRequest)
        String responseJSON = commonResponse.toString()
        LoggerUtil.info("{0} received message: {1}.", methodFullName, responseJSON)

        TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, requestJSON)
        TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, responseJSON)

        // 设置返回参数
        String bizCode = commonResponse.getCode()
        String errorMessage = commonResponse.getMessage()
        if (SUCCESS_CODE != bizCode) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
        }

        response.setTransactionState(TransactionState.SUCCESS)
        return response
    }

    @Override
    ChannelCancelResponse cancel(ChannelCancelRequest request) {
        String methodFullName = getFullMethodName("cancel")
        ChannelCancelResponse response = new ChannelCancelResponse()

        // 判断是否是mock数据
        String isMock = channel.channelAccessConfig.getProperty("is_mock")
        if (StringUtils.isNotEmpty(isMock) && Boolean.valueOf(isMock)) {
            response.setTransactionState(TransactionState.SUCCESS)
            return response
        }

        // 设置请求参数
        CancelRequest cancelRequest = CancelRequest
                .newBuilder()
                .setCouponNo(request.getPayCode())
                .build()

        String requestJSON = cancelRequest.toString()
        LoggerUtil.info("{0} is sending message: {1}.", methodFullName, requestJSON)

        // 请求
        TjiCouponClient tjiCouponClient = SpringContextUtil.bean(TjiCouponClient.class)
        CommonResponse commonResponse = tjiCouponClient.cancel(cancelRequest)
        String responseJSON = commonResponse.toString()
        LoggerUtil.info("{0} received message: {1}.", methodFullName, responseJSON)

        TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, requestJSON)
        TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, responseJSON)

        // 设置返回参数
        String bizCode = commonResponse.getCode()
        String errorMessage = commonResponse.getMessage()
        if (SUCCESS_CODE != bizCode) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
        }

        response.setTransactionState(TransactionState.SUCCESS)
        return response
    }

    @Override
    ChannelQueryResponse query(ChannelQueryRequest request) {
        String methodFullName = getFullMethodName("query")

        // 设置响应参数
        ChannelQueryResponse response = new ChannelQueryResponse()
        response.setPayMethod(PayMethod.TJI_COUPON_PAY)
        response.setTransactionId(request.getTransactionId())
        response.setChannel(request.getChannel())

        // 判断是否是mock数据
        String isMock = channel.channelAccessConfig.getProperty("is_mock")
        if (StringUtils.isNotEmpty(isMock) && Boolean.valueOf(isMock)) {
            response.setTransactionState(TransactionState.SUCCESS)
            response.setRealAmount(BigDecimal.valueOf(1024))
            return response
        }

        // 参数：payCode
        String payCode = request.getPayCode()
        if (StringUtils.isEmpty(payCode)) {
            throw new CommonException(ServiceError.PARAM_CONSTRAINTS_VIOLATION, "Param 'payCode' is null")
        }

        // 参数：extendedParams
        String extendedParams = request.getExtendedParams()
        if (StringUtils.isEmpty(extendedParams)) {
            throw new CommonException(ServiceError.PARAM_CONSTRAINTS_VIOLATION, "Param 'extendedParams' is null")
        }

        // 参数：extendedParams.ticket_no、extendedParams.store_code、extendedParams.queryAction
        JSONObject extendedParamJSON = JSONObject.parseObject(extendedParams)
        String ticketNo = extendedParamJSON.getString("ticket_no")
        String storeCode = extendedParamJSON.getString("store_code")
        String queryAction = extendedParamJSON.getString("query_action")
        if (StringUtils.isAnyEmpty(queryAction, ticketNo, storeCode)) {
            throw new CommonException(ServiceError.PARAM_CONSTRAINTS_VIOLATION, "Param 'extendedParams.queryAction' or 'extendedParams.ticket_no' or 'extendedParams.store_code' is null")
        }

        // 参数：只能是：PAY - 支付查询，CANCEL - 取消查询，REFUND - 退款查询
        if (!("PAY".equalsIgnoreCase(queryAction) || "CANCEL".equalsIgnoreCase(queryAction) || "REFUND".equalsIgnoreCase(queryAction))) {
            throw new CommonException(ServiceError.PARAM_CONSTRAINTS_VIOLATION, "Param 'extendedParams.queryAction' must be 'PAY' | 'CANCEL' | 'REFUND'")
        }

        // 设置请求参数
        QueryRequest queryReq = QueryRequest
                .newBuilder()
                .setCouponNo(payCode)
                .build()
        String requestJSON = queryReq.toString()
        LoggerUtil.info("{0} is sending message: {1}.", methodFullName, requestJSON)

        // 请求
        TjiCouponClient tjiCouponClient = SpringContextUtil.bean(TjiCouponClient.class)
        CommonResponse commonResponse = tjiCouponClient.query(queryReq)
        String responseJSON = commonResponse.toString()
        LoggerUtil.info("{0} received message: {1}.", methodFullName, responseJSON)

        // 状态不成功时，报错
        String bizCode = commonResponse.getCode()
        String errorMessage = commonResponse.getMessage()
        if (SUCCESS_CODE != bizCode) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
        }

        // 卡券不存在时，报错
        TjiCoupon coupon = commonResponse.getData()
        if (coupon == null) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "卡券不存在")
        }

        // 支付查询
        if ("PAY".equalsIgnoreCase(queryAction)) {
            // 卡券状态未被使用，表示核销失败
            if (coupon.getStatus() != 1) {
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "卡券未核销，请重试")
            }

            // 核销的门店不匹配
            String shopNo = coupon.getShopNo()
            if (shopNo != storeCode) {
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, String.format("卡券已经被门店[%s]使用", shopNo))
            }

            // 取餐号不匹配
            if (coupon.getBillNo() != ticketNo) {
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "取餐号非法，不是核销时的取餐号")
            }
        } else if ("REFUND".equalsIgnoreCase(queryAction) || "CANCEL".equalsIgnoreCase(queryAction)) {
            // 卡券状态未被使用，表示核销失败
            if (coupon.getStatus() != 0) {
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "卡券反核销失败，请重试")
            }
        }

        response.setTransactionState(TransactionState.SUCCESS)
        return response
    }

}
