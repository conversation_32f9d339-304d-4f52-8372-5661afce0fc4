package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.fiserv.client.ApiException
import cn.hexcloud.fiserv.model.*
import cn.hexcloud.fiserv.simple.*
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.NotificationType
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import org.apache.commons.codec.binary.Base64
import org.apache.commons.lang3.StringUtils

import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.temporal.ChronoUnit

class TyroMPay extends AbstractExternalChannelModule implements PaymentModule {
    private final MerchantCredentials merchantCredential
    private final ClientContext clientContext
    private final ClientFactory clientFactory

    private static final String OPENID_CONFIG_URL = "https://auth.connect.tyro.com/.well-known/openid-configuration"
    private static final String API_BASE_URL = "https://api.tyro.com/connect"
    private static final String CLIENT_ID = "y79fgoezj9qeyUBsQsJritGKC2Z7up8O"
    private static final String CLIENT_SECRET = "hfWSkBJwWo1jN4Fk5ADCG_lfrOx4NTTgV3lZ0SgfodIz1en66UsQ4duo6RZlFjOU"
    private static final String GRANT_TYPE = "client_credentials"
    private String accessToken
    private long tokenExpiration
    private final Gson gson = new Gson()

    TyroMPay(ExternalChannel channel) {
        super(channel)
        this.merchantCredential = MerchantCredentials.of(channel.getChannelAccessConfig().getAccessKey(), channel.getChannelAccessConfig().getAppKey())
        this.clientContext = ClientContextImpl.create(merchantCredential, channel.getChannelAccessConfig().getGatewayUrl(), "", channel.getChannelAccessConfig().getMerchantId())
        this.clientFactory = clientContext.getFactory()
    }

    @Override
    protected String getSignModuleName() {
        return this.getModuleName()
    }

    @Override
    String getModuleName() {
        return "Payment"
    }

    private String getMethodFullName(String method) {
        return channel.getChannelCode() + "." + getModuleName() + "." + method
    }

    @Override
    // 建单(Connect方式实现）
    ChannelCreateResponse create(ChannelCreateRequest request) {
        String methodFullName = getMethodFullName("create");
        ChannelCreateResponse response = new ChannelCreateResponse();

        // connect gateway url
        String connectGatewayUrl = channel.getChannelAccessConfig().getProperty("connect_gateway_url")
        // orz: sharedSecret 暂时先放在app_id字段里
        String sharedSecret = channel.getChannelAccessConfig().getAppId()
        // 构建其他业务form参数
        String txntype = "sale"
        String authenticateTransaction = "true"
        String checkoutoption = "combinedpage"
        String hash_algorithm = "HMACSHA256"
        String storename = channel.getChannelAccessConfig().getMerchantId() // store id provided by mert side
        String chargetotal = centToYuan(request.getAmount().toString())
        String oid = request.getOrderNo()
        String transactionId = request.getTransactionId()
        // 货币、语言、时区、时区当前时间
        String currency = CurrencyCodeMapper.getCurrencyCode(request.getCurrency())// e.g.: input:"HKD",output:"344"
        String language = channel.getChannelAccessConfig().getProperty("language") != null ? channel.getChannelAccessConfig().getProperty("language") : "zh_TW"
        // e.g.: "zh_CN","zh_TW","en_US","en_GB"
        String timezone = channel.getChannelAccessConfig().getProperty("timezone") != null ? channel.getChannelAccessConfig().getProperty("timezone") : "Asia/Shanghai"
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy:MM:dd-HH:mm:ss")
        sdf.setTimeZone(TimeZone.getTimeZone(timezone))
        String txndatetime = sdf.format(new Date())

        // 检查 exJson 是否为 null，如果是则给它赋值一个默认的空 JSONObject
        String extendedParams = request.getExtendedParams()
        JSONObject exJson = JSON.parseObject(extendedParams)
        if (exJson == null) {
            exJson = new JSONObject()
        }
        // 手动设置 oid 参数
        exJson.put("oid", oid)

        // 指定具体支付方式,extendedParams,支持构建传入具体的paymentMethod；默认不传
        String paymentMethod = ""
        if (exJson.containsKey("pay_method") && exJson.get("pay_method") != null) {
            paymentMethod = mapPayMethodFiserv(exJson.getString("pay_method"))
        }

        LoggerUtil.info("oid: {0}", transactionId)
        LoggerUtil.info("platform: {0}", exJson.getString("platform"))
        // 支付成功url
        String responseSuccessURL = getReturnUrl(transactionId, exJson.getString("platform"), "success")
        // 支付失败url
        String responseFailURL = getReturnUrl(transactionId, exJson.getString("platform"), "fail")
        LoggerUtil.info("responseSuccessURL: {0}", responseSuccessURL)
        LoggerUtil.info("responseFailURL: {0}", responseFailURL)

        // 通知url
        String transactionNotificationURL = getNotificationUrl()

        // 参与计算hash前字符串
        String separator = "|"
        // 240910: oid -> transaction_id
        String stringToExtendedHash = authenticateTransaction + separator + chargetotal + separator + checkoutoption + separator + currency + separator + hash_algorithm + separator + language + separator + transactionId + separator + responseFailURL + separator + responseSuccessURL + separator + storename + separator + timezone + separator + transactionNotificationURL + separator + txndatetime + separator + txntype;
        if (paymentMethod != "") {
            stringToExtendedHash = authenticateTransaction + separator + chargetotal + separator + checkoutoption + separator + currency + separator + hash_algorithm + separator + language + separator + transactionId + separator + paymentMethod + separator + responseFailURL + separator + responseSuccessURL + separator + storename + separator + timezone + separator + transactionNotificationURL + separator + txndatetime + separator + txntype;
        }

        String hashExtended = calculateHMAC(stringToExtendedHash, sharedSecret)
        String html = buildHtmlWithForm(connectGatewayUrl, txntype, authenticateTransaction, timezone, checkoutoption, txndatetime, hash_algorithm, hashExtended, storename, chargetotal, currency, language, transactionId, paymentMethod, responseFailURL, responseSuccessURL, transactionNotificationURL);
        LoggerUtil.info("{0} received message: &nbsp;{1}.", methodFullName, html);

        // 预支付单号，不知道填啥,就填单号
        response.setPrePayId(transactionId);
        response.setChannel(request.getChannel());
        response.setPayCode("");
        // 把要请求的完整页面,丢给上游PC/H5浏览器去加载
        response.setPackStr(html);
        return response;
    }

    @Override
    // 查交易流水
    ChannelQueryResponse query(ChannelQueryRequest request) {
        String methodFullName = getMethodFullName("query")
        ChannelQueryResponse response = new ChannelQueryResponse()
        LoggerUtil.info("TyroMPay query input:{0}", request.toString())
        // connect方式建单的，当时拿不到ipgGatewayId，所以直接拿订单交易里第一笔，即orderApi.transactions[0]
        if (request.getTpTransactionId() == null || request.getTpTransactionId().isEmpty()) {
            final OrderApi orderApi = this.clientFactory.getOrderApi()
            try {
                // order no -> transaction_id
                OrderResponse orderResp = orderApi.orderInquiry(request.getTransactionId())
                LoggerUtil.info("{0} received message: {1}.", methodFullName, JSONObject.toJSONString(orderResp))
                response.setChannel(request.getChannel())
                // 判断交易列表
                if (orderResp.getTransactions() != null && !orderResp.getTransactions().isEmpty()) {
                    def firstTransaction = orderResp.getTransactions()[0]
                    response.setTpTransactionId(firstTransaction.getIpgTransactionId())
                    response.setTransactionState(mapTransactionState(firstTransaction.getTransactionResult()))
                    BigDecimal approvedAmount = firstTransaction.getApprovedAmount()?.total ?: new BigDecimal(0)
                    response.setRealAmount(yuanToCent(approvedAmount.toString()))
                    // 这笔交易有支付过，才有具体的支付详情
                    if (firstTransaction.getPaymentMethodDetails() != null) {
                        // 最终支付方式映射
                        PaymentMethodType pmt = firstTransaction.getPaymentMethodDetails().getPaymentMethodType()
                        PaymentCard pc = firstTransaction.getPaymentMethodDetails().getPaymentCard()
                        PayMethod pm = mapPayMethodByApi(pmt, pc.getBrand())
                        response.setPayMethod(pm)
                        // 其他一些额外数据:
                        // 支付卡号信息： （VISA)...1960
                        Map<String, String> extendParams = new HashMap<>()
                        extendParams.put("cardnumber", "(" + pc.getBrand() + ")..." + pc.getLast4())
                        response.setExtendedParams(JSONObject.toJSONString(extendParams))
                    }
                } else {
                    // 交易列表为空，也映射到WAITING
                    response.setTransactionState(TransactionState.WAITING)
                    LoggerUtil.warn("No transactions found for order: {0}", request.getTransactionId())
                }
            } catch (ApiException eo) {
                // 给的单号没查到数据，直接返回不报错
                if (eo.getResponseBody().contains("Unable to load order from DB for identifier")) {
                    LoggerUtil.warn("No transactions found for order: {0}", request.getTransactionId())
                    // 不存在的单号，也映射到WAITING
                    response.setTransactionState(TransactionState.WAITING)
                    return response
                }
                LoggerUtil.error("Error orderInquiry: ", eo)
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, eo.getMessage())
            }
            LoggerUtil.info("TyroMPay order level query output:{0}", response.toString())
        } else {
            // 建单后：关单、退款等查询，发生时pbis是给pay-flow记录了ipgGatewayId
            final PaymentApi api = this.clientFactory.getPaymentApi()
            try {
                TransactionResponse tr = api.transactionInquiry(request.getTpTransactionId())
                LoggerUtil.info("{0} received message: {1}.", methodFullName, tr.toString())
                response.setChannel(request.getChannel())
                response.setTpTransactionId(tr.getIpgTransactionId())
                response.setTransactionState(mapTransactionState(tr.getTransactionResult()))
                response.setRealAmount(yuanToCent(tr.getTransactionAmount().total.toString()))
                // 这笔交易有支付过，才有具体的支付详情
                if (tr.getPaymentMethodDetails() != null) {
                    // 最终支付方式映射
                    PaymentMethodType pmt = tr.getPaymentMethodDetails().getPaymentMethodType()
                    PaymentCard pc = tr.getPaymentMethodDetails().getPaymentCard()
                    PayMethod pm = mapPayMethodByApi(pmt, pc.getBrand())
                    response.setPayMethod(pm)
                    // 其他一些额外数据:
                    // 支付卡号信息： （VISA)...1960
                    Map<String, String> extendParams = new HashMap<>()
                    extendParams.put("cardnumber", "(" + pc.getBrand() + ")..." + pc.getLast4())
                    response.setExtendedParams(JSONObject.toJSONString(extendParams))
                }
            }
            catch (ApiException e) {
                // 给的单号没查到数据，直接返回不报错
                if (e.getResponseBody().contains("Unable to load order from DB for identifier")) {
                    LoggerUtil.warn("No transactions found for order: {0}", request.getOrderNo())
                    // 不存在的单号，也映射到WAITING
                    response.setTransactionState(TransactionState.WAITING)
                    return response
                }
                LoggerUtil.error("Error transactionInquiry: ", e)
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, e.getMessage())
            }
        }
        LoggerUtil.info("TyroMPay query output:{0}", response.toString())
        return response
    }

    @Override
    // 关单
    ChannelCancelResponse cancel(ChannelCancelRequest request) {
        String methodFullName = getMethodFullName("cancel")
        ChannelCancelResponse response = new ChannelCancelResponse()
        // 先设置tp_id
        response.setTpTransactionId(request.getRelatedTPTransactionId())
        final PaymentUrlApi paymentUrlApi = this.clientFactory.getPaymentUrlApi()
        final PaymentApi paymentApi = this.clientFactory.getPaymentApi()
        // 1. 先查询流水，同时判断是否是第一笔建单交易
        try {
            TransactionResponse tr = paymentApi.transactionInquiry(request.getRelatedTPTransactionId())
            LoggerUtil.info("{0} received message: {1}.", "query first before cancel", tr.toString())
            // 2 如果是第1笔建单的流水，则尝试销毁支付链接
            if (tr.getTransactionState() == TransactionResponse.TransactionStateEnum.TEMPLATE) {
                try {
                    // 是第一笔，尝试作废链接
                    // transactionId, orderId, paymentUrlId, transactionTime
                    PaymentUrlResponse resp = paymentUrlApi.deletePaymentUrl(request.getRelatedTPTransactionId(), "", "", "")
                    LoggerUtil.info("{0} received message: {1}.", methodFullName, resp.toString())
                    // 1.作废成功，直接返回
                    if (resp.getRequestStatus() == PaymentUrlResponse.RequestStatusEnum.SUCCESS) {
                        // 取消支付链接成功
                        response.setTransactionState(TransactionState.CANCELED)
                        return response
                    }
                } catch (ApiException voidLinkException) {
                    // 2. 作废失败，分情况处理：
                    if (voidLinkException.getCode() != 0) {
                        ObjectMapper objectMapper = new ObjectMapper()
                        JsonNode rootNode = objectMapper.readTree(voidLinkException.getResponseBody())
                        JsonNode errorNode = rootNode.path("error")
                        // 2.1 如果链接已经过期作废、支付过，不能再作废，直接就算已取消
                        // {"code":"5014","message":"Validation problem: paymentUrlAlreadyVoided"}

                        // {"code":"5014","message":"Validation problem: paymentProcess"}
                        // {"code":"5014","message":"Validation problem: paymentUrlDoesNotExistFortransactionID"}
                        // ...
                        if (errorNode.path("code").asText() != "0") {
                            // 已取消，也算是取消成功了
                            if (errorNode.path("message").asText() == "Validation problem: paymentUrlAlreadyVoided") {
                                LoggerUtil.info("PaymentUrl has been voided.")
                                response.setTransactionState(TransactionState.CANCELED)
                                return response
                            }
                            // 其他情况，都算失败，返回具体原因
                            LoggerUtil.warn(errorNode.path("message").asText())
                            response.setWarning(true)
                            response.setWarningMessage(errorNode.path("message").asText())
                            response.setTransactionState(TransactionState.FAILED)
                            return response
                        }
                    }
                    // code!=0,其余异常正常 logging + throw exception
                    LoggerUtil.error("Error transaction cancel: ", voidLinkException)
                    throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, voidLinkException.getMessage())
                }
            }

            // 3 如果是第2笔支付的流水，则尝试做关单操作
            try {
                // 交易关单
                SecondaryTransaction st = new SecondaryTransaction()
                st.setMerchantTransactionId(request.getRelatedTPTransactionId())
                st.setRequestType("VoidTransaction")
                TransactionResponse voidResponse = paymentApi.submitSecondaryTransaction(request.getRelatedTPTransactionId(), st)
                LoggerUtil.info("{0} received message: {1}.", "VoidTransaction", voidResponse.toString())
                response.setTransactionState(mapTransactionState(voidResponse.getTransactionStatus().value))
            } catch (ApiException voidException) {
                if (voidException.getCode() != 0) {
                    ObjectMapper objectMapper = new ObjectMapper()
                    JsonNode rootNode = objectMapper.readTree(voidException.getResponseBody())
                    JsonNode errorNode = rootNode.path("error")
                    // 2.1 如果已经关单过，不能重复关单,或者对应流水不对，不满足关单条件
                    // {"code":"5019","message":"The transaction to be voided is not voidable"}
                    // ...
                    if (errorNode.path("code").asText() != "0") {
                        // 如果已经关单过，不能重复关单,或者对应流水不对，不满足关单条件
                        if (errorNode.path("message").asText() == "The transaction to be voided is not voidable") {
                            LoggerUtil.info("The transaction to be voided is not voidable.")
                            response.setTransactionState(TransactionState.FAILED)
                            return response
                        }
                        // 其他情况，都算失败，返回具体原因
                        LoggerUtil.warn(errorNode.path("message").asText())
                        response.setWarning(true)
                        response.setWarningMessage(errorNode.path("message").asText())
                        response.setTransactionState(TransactionState.FAILED)
                        return response
                    }
                }
                LoggerUtil.error("Error transaction return: ", voidException)
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, voidException.getMessage())
            }
        } catch (ApiException queryException) {
            LoggerUtil.error("Error transactionInquiry: ", queryException)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, queryException.getMessage())
        }
        // 无其他异常，正常返回
        return response
    }

    @Override
    ChannelRefundResponse refund(ChannelRefundRequest request) {
        String methodFullName = getMethodFullName("refund")
        ChannelRefundResponse response = new ChannelRefundResponse()

        final PaymentApi paymentApi = this.clientFactory.getPaymentApi()
        try {
            // 交易退款
            SecondaryTransaction st = new SecondaryTransaction()
            st.setMerchantTransactionId(request.getRelatedTPTransactionId())
            st.setRequestType("ReturnTransaction")
            Amount amount = new Amount()
            amount.setTotal(centToYuan(request.getAmount().toString()))
            amount.setCurrency(request.getCurrency())
            st.setTransactionAmount(amount)
            TransactionResponse tr = paymentApi.submitSecondaryTransaction(request.getRelatedTPTransactionId(), st)
            LoggerUtil.info("{0} received message: {1}.", methodFullName, tr.toString())
            response.setTransactionState(mapTransactionState(tr.getTransactionStatus().value))
            response.setRealAmount(yuanToCent(tr.getApprovedAmount().total.toString()))
        } catch (ApiException returnException) {
            // 2. 退款失败，分情况处理：
            if (returnException.getCode() != 0) {
                ObjectMapper objectMapper = new ObjectMapper()
                JsonNode rootNode = objectMapper.readTree(returnException.getResponseBody())
                JsonNode errorNode = rootNode.path("error")
                // 2.1 原支付单，没有可退的资金了
                // {"code":"5009","message":"No transaction found in order which can be returned"}
                // {"code":"10601","message":"Total amount passed is more than the Return/Void amount."}
                if (errorNode.path("code").asText() != "0") {
                    // 没有可退的金额了、未找到关联支付单，或其他情况，都算失败，返回具体原因
                    LoggerUtil.warn(errorNode.path("message").asText())
                    response.setWarning(true)
                    response.setWarningMessage(errorNode.path("message").asText())
                    response.setTransactionState(TransactionState.FAILED)
                    return response
                }
            }
            // code!=0,其余异常正常 logging + throw exception
            LoggerUtil.error("Error transaction return: ", returnException)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, returnException.getMessage())
        }
        return response
    }

    @Override
    ChannelNotificationResponse payNotify(HttpServletRequest request) {
//    ChannelNotificationResponse payNotify(String payload) {
        String payload = request.getParameter("payload")
        String methodFullName = getMethodFullName("payNotify")

//        api建单,回文:
//        {
//            "txndate_processed": "05/07/24 05:48:29",
//            "ccbin": "400552",
//            "oid": "3424070500",
//            "cccountry": "N/A",
//            "expmonth": "10",
//            "hash_algorithm": "SHA256",
//            "endpointTransactionId": "418703575822",
//            "currency": "344",
//            "processor_response_code": "00",
//            "chargetotal": "12.34",
//            "terminal_id": "00002373",
//            "associationResponseCode": "00",
//            "approval_code": "Y:417587:4661134383:PPX :418703575822",
//            "expyear": "2024",
//            "response_code_3dsecure": "1",
//            "schemeResponseCode": "00",
//            "notification_hash": "a5569b4de355b90b43cfd85af4e61af7102f90c873d4b861b792c07cfa56c9f5",
//            "transactionNotificationURL": "https://hipos-saas-qa.hexcloud.cn/callback/pbis/payment/TyroMPay/1372/*******************",
//            "schemeTransactionId": "000000000000000",
//            "tdate": "1720151309",
//            "associationResponseMessage": "Approved",
//            "bname": "test",
//            "ccbrand": "VISA",
//            "refnumber": "418703575822",
//            "txntype": "sale",
//            "paymentMethod": "V",
//            "txndatetime": "2024:07:05-05:47:52",
//            "cardnumber": "(VISA) ... 0129",
//            "ipgTransactionId": "84661134383",
//            "status": "APPROVED"
//        }


//        connect建单,回文:(注意isValidSignature目前实现，是connect建单的实现，hash都是base64格式的才对）
//        {
//            "txndate_processed": "2024/10/16 上午 09:58:06",
//            "ccbin": "554433",
//            "timezone": "Asia/Shanghai",
//            "oid": "1309366e-f96a-423b-a586-e25db4bce27c",
//            "cccountry": "TWN",
//            "expmonth": "10",
//            "hash_algorithm": "HMACSHA256",
//            "endpointTransactionId": "101607319978",
//            "currency": "344",
//            "processor_response_code": "00",
//            "chargetotal": "103.50",
//            "terminal_id": "00002373",
//            "associationResponseCode": "0",
//            "approval_code": "Y:416600:4670299081:PPX :101607319978",
//            "expyear": "2024",
//            "response_code_3dsecure": "1",
//            "schemeResponseCode": "00",
//            "notification_hash": "3OsStoeg8FgTe4tMWuQuGj6j1gTCIZI2ldPvIocDYXQ=",
//            "transactionNotificationURL": "https://hipos-saas-qa.hexcloud.cn/callback/pbis/payment/TyroMPay/1372/*******************",
//            "schemeTransactionId": "MCG1DXREL1016",
//            "tdate": "1729065486",
//            "installments_interest": "false",
//            "bname": "FISERV UAT/Test Card 01",
//            "ccbrand": "MASTERCARD",
//            "refnumber": "101607319978",
//            "txntype": "sale",
//            "paymentMethod": "M",
//            "txndatetime": "2024:10:16-15:55:53",
//            "cardnumber": "(MASTERCARD) ... 0235",
//            "ipgTransactionId": "84670299081",
//            "status": "交易审批"
//        }

        LoggerUtil.info("{0} received message: {1}", methodFullName, payload)
        Map<String, String> unverifiedMessage = getUrlParamsMap(payload)
        if (!isValidSignature(unverifiedMessage)) {
            // 验签失败
            throw new CommonException(ServiceError.INVALID_SIGNATURE)
        }

        // 解析返回结果集
        ChannelNotificationResponse response = new ChannelNotificationResponse()
        ChannelPayResponse payResponse = new ChannelPayResponse()
        // transactionId
        payResponse.setTransactionId(unverifiedMessage.get("oid"))
        // tpTransactionId
        payResponse.setTpTransactionId(unverifiedMessage.get("ipgTransactionId"))
        // 通知类型: 目前只接付款流水回调，退款返回实时结果
        payResponse.setNotificationType(NotificationType.PAY)
        // 实际支付金额
        payResponse.setRealAmount(yuanToCent(unverifiedMessage.get("chargetotal")))
        // 支付是否成功结果
        String processorResponseCode = unverifiedMessage.get("processor_response_code")
        payResponse.setTransactionState(mapPayResult(processorResponseCode))
        // 最终支付途径
        String paymentMethod = unverifiedMessage.get("paymentMethod")
        payResponse.setPayMethod(mapPayMethodBOH(paymentMethod))
        // 其他一些额外数据
        Map<String, String> extendParams = new HashMap<>()
        extendParams.put("refnumber", unverifiedMessage.get("refnumber"))
        extendParams.put("cardnumber", unverifiedMessage.get("cardnumber"))
        payResponse.setExtendedParams(JSONObject.toJSONString(extendParams))
        // 设定payResponse
        response.setPayResponse(payResponse)
        // 设置上下文（出入报文）
        TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, payload)
        return response
    }

    // 获取回调地址
    private String getNotificationUrl() {
        String notificationUrl = channel.getChannelAccessConfig().getProperty("notification_url")
        if (StringUtils.isNotBlank(notificationUrl)) {
            String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
            String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
            String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId
            return notificationUrl.endsWith("/") ? notificationUrl + path : notificationUrl + "/" + path
        }
        return null
    }

    // 获取回跳地址
    private String getReturnUrl(String oid, String platform, String payResult) {
        String returnUrl = channel.getChannelAccessConfig().getProperty("return_url")
        if (StringUtils.isNotBlank(returnUrl)) {
            String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
            String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
            String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId + "/" + "oid=" + oid + "&platform=" + platform + "&pay_result=" + payResult
            return returnUrl.endsWith("/") ? returnUrl + path : returnUrl + "/" + path
        }
        return null
    }

    // 映射回调状态
    private static TransactionState mapPayResult(String type) {
        // https://developer.fiserv.com/product/IPGNA/docs/?path=docs/schemas-md/AuthorisationPlatform.md&branch=main
        switch (type) {
            case "00":
                return TransactionState.SUCCESS
            default:
                return TransactionState.FAILED
        }
    }

    // 映射 Fiserv 具体支付方式
    private static PayMethod mapPayMethodBOH(String payMethod) {
        // https://docs.fiserv.dev/public/docs/payment-methods-2
        switch (payMethod) {
            case "aliPay":
            case "aliPay_domestic":
                return PayMethod.HAlipay
            case "wechat":
            case "wechat_domestic":
                return PayMethod.HWechatpay
            case "CUP":
            case "CUP_domestic":
                return PayMethod.HBOCPAY
            case "V":
                return PayMethod.HVisa
            case "M":
                return PayMethod.HMaster
            case "applePay":
                return PayMethod.HApplePay
            case "googlePay":
                return PayMethod.HGooglePay
            default:
                return PayMethod.EMPTY;
        }
    }

    // 消费者支付方式 -> fiserv connect page下拉选择的支付方式enum
    private static String mapPayMethodFiserv(String payMethod) {
        // https://docs.fiserv.dev/public/docs/payment-methods-2
        switch (payMethod) {
            case PayMethod.HAlipay.code:
                return "aliPay"
//            case PayMethod.HAlipay.code:
//                return "aliPay_domestic"
            case PayMethod.HWechatpay.code:
                return "wechat"
//            case PayMethod.HWechatpay.code:
//                return "wechat_domestic"
            case PayMethod.HBOCPAY.code:
                return "CUP"
//            case PayMethod.HBOCPAY.code:
//                return "CUP_domestic"
            case PayMethod.HVisa.code:
                return "V"
            case PayMethod.HMaster.code:
                return "M"
            case PayMethod.HApplePay.code:
                return "applePay"
            case PayMethod.HGooglePay.code:
                return "googlePay"
            default:
                // fallback to selection
                return PayMethod.EMPTY.code;
        }
    }

    // 映射 Fiserv 具体支付方式
    private static PayMethod mapPayMethodByApi(PaymentMethodType paymentMethodType, String cardBrand) {
        // https://docs.fiserv.dev/public/docs/payment-methods
        switch (paymentMethodType) {
            case PaymentMethodType.ALIPAY:
            case PaymentMethodType.ALIPAY_DOMESTIC:
                return PayMethod.HAlipay
            case PaymentMethodType.WECHAT:
            case PaymentMethodType.WECHAT_DOMESTIC:
                return PayMethod.HWechatpay
            case PaymentMethodType.CUP_DOMESTIC:
                return PayMethod.HBOCPAY
            case PaymentMethodType.PAYMENT_CARD:
                if (cardBrand == "VISA") {
                    return PayMethod.HVisa
                }
                if (cardBrand == "MASTERCARD") {
                    return PayMethod.HMaster
                }
                return PayMethod.EMPTY
//            case PaymentMethodType.APPLE_PAY:
//                return PayMethod.HApplePay
//            case PaymentMethodType.GOOGLE_PAY:
//                return PayMethod.HGooglePay
            default:
                return PayMethod.EMPTY
        }
    }

    // 映射交易状态
    private static TransactionState mapTransactionState(String tpTransactionStatus) {
        TransactionState transactionState
        switch (tpTransactionStatus) {
            case "APPROVED":
                // 交易已被批准。此状态表示交易已成功获得授权并完成
                transactionState = TransactionState.SUCCESS
                break
            case "FAILED":
                // 失败，如ECI-7 not supported
                transactionState = TransactionState.FAILED
                break
            case "VALIDATION_FAILED":
                // 交易验证失败。此状态表示交易未通过验证步骤，可能是由于输入信息错误等原因
                transactionState = TransactionState.FAILED
                break
            case "PROCESSING_FAILED":
                // 交易处理失败。此状态表示交易在处理过程中出现错误，未能成功完成
                transactionState = TransactionState.FAILED
                break
            case "DECLINED":
                // 交易被拒绝，未获得授权。此状态表示银行或支付处理机构拒绝了交易请求，可能是由于资金不足、卡片信息错误等原因
                transactionState = TransactionState.FAILED
                break
            case "WAITING":
                // 交易在等待中。此状态表示交易正在等待某些条件满足或某些处理步骤完成
                transactionState = TransactionState.WAITING
                break
            default:
                // 其余默认都在等待中
                transactionState = TransactionState.WAITING
        }
        return transactionState
    }

    // 解析x-www-form-urlencoded参数
    private static Map<String, String> getUrlParamsMap(String urlParams) {
        Map<String, String> paramsMap = new HashMap<>()
        if (StringUtils.isEmpty(urlParams)) {
            return paramsMap
        }
        String[] urlParamArr = urlParams.split("&")
        for (String urlParam : urlParamArr) {
            String[] paramPair = urlParam.split("=")
            paramsMap.put(paramPair[0], URLDecoder.decode(paramPair[1], StandardCharsets.UTF_8.toString()))
        }
        return paramsMap
    }

    // 元->分
    private static BigDecimal yuanToCent(String yuan) {
        if (yuan == null) {
            return null
        }
        return new BigDecimal(yuan) * 100
    }

    // 分->元
    private static BigDecimal centToYuan(String cent) {
        if (cent == null) {
            return null
        }
        BigDecimal centDecimal = new BigDecimal(cent)
        return centDecimal.divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP)
    }

    // 辅助：查订单
    ChannelQueryResponse queryOrder(ChannelQueryRequest request) {
        String methodFullName = getMethodFullName("queryOrder")
        ChannelQueryResponse response = new ChannelQueryResponse()

        final MerchantCredentials cred = MerchantCredentials.of(channel.getChannelAccessConfig().getAccessKey(), channel.getChannelAccessConfig().getAppKey())
        final ClientContext context = ClientContextImpl.create(cred, channel.getChannelAccessConfig().getGatewayUrl(), "", channel.getChannelAccessConfig().getMerchantId())
        final ClientFactory factory = context.getFactory()

        final OrderApi api = factory.getOrderApi()
        try {
            OrderResponse tr = api.orderInquiry(request.getOrderNo())
            LoggerUtil.info("{0} received message: {1}.", methodFullName, tr.toString())
        } catch (ApiException e) {
            LoggerUtil.error("Error orderInquiry: ", e)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, e.getMessage())
        }
        return response
    }

    // connect方式，页面参数hmac方法
    private static String calculateHMAC(String data, String key) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256")
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256")
        mac.init(secretKeySpec)
        byte[] hmacData = mac.doFinal(data.getBytes(StandardCharsets.UTF_8))
        return Base64.encodeBase64String(hmacData)
    }

    // 主流世界货币映射
    class CurrencyCodeMapper {
        private static final Map<String, String> currencyCodeMap = new HashMap<>();

        static {
            currencyCodeMap.put("USD", "840"); // 美元
            currencyCodeMap.put("EUR", "978"); // 欧元
            currencyCodeMap.put("JPY", "392"); // 日元
            currencyCodeMap.put("GBP", "826"); // 英镑
            currencyCodeMap.put("AUD", "36");  // 澳大利亚元
            currencyCodeMap.put("CAD", "124"); // 加拿大元
            currencyCodeMap.put("CHF", "756"); // 瑞士法郎
            currencyCodeMap.put("CNY", "156"); // 人民币
            currencyCodeMap.put("HKD", "344"); // 港元
            currencyCodeMap.put("NZD", "554"); // 新西兰元
            currencyCodeMap.put("KRW", "410"); // 韩元
            currencyCodeMap.put("SGD", "702"); // 新加坡元
            currencyCodeMap.put("INR", "356"); // 印度卢比
            currencyCodeMap.put("RUB", "643"); // 俄罗斯卢布
            currencyCodeMap.put("BRL", "986"); // 巴西雷亚尔
            currencyCodeMap.put("ZAR", "710"); // 南非兰特
        }

        static String getCurrencyCode(String currency) {
            String code = currencyCodeMap.get(currency);
            if (code != null) {
                return code;
            } else {
                throw new IllegalArgumentException("Unsupported currency code: " + currency);
            }
        }
    }

    private String buildHtmlWithForm(String connectGatewayUrl, String txntype, String authenticateTransaction, String timezone, String checkoutoption, String txndatetime, String hash_algorithm, String hashExtended, String storename, String chargetotal, String currency, String language, String oid, String paymentMethod, String responseFailURL, String responseSuccessURL, String transactionNotificationURL) {
        StringBuilder html = new StringBuilder();
        html.append("<html><head></head><body>");
        html.append("<form method=\"post\" action=\"").append(connectGatewayUrl).append("\" id=\"").append(channel.getChannelCode()).append("\" name=\"").append(channel.getChannelCode()).append("\">");
        // 是否有传入最终支付方式
        if (paymentMethod != "") {
            html.append("<input type=\"hidden\" name=\"paymentMethod\" value=\"").append(paymentMethod).append("\" id=\"paymentMethod\" />");
        }
        html.append("<input type=\"hidden\" name=\"txntype\" value=\"").append(txntype).append("\" id=\"txntype\" />");
        html.append("<input type=\"hidden\" name=\"authenticateTransaction\" value=\"").append(authenticateTransaction).append("\" id=\"authenticateTransaction\" />");
        html.append("<input type=\"hidden\" name=\"timezone\" value=\"").append(timezone).append("\" id=\"timezone\" />");
        html.append("<input type=\"hidden\" name=\"checkoutoption\" value=\"").append(checkoutoption).append("\" id=\"checkoutoption\" />");
        html.append("<input type=\"hidden\" name=\"txndatetime\" value=\"").append(txndatetime).append("\" id=\"txndatetime\" />");
        html.append("<input type=\"hidden\" name=\"hash_algorithm\" value=\"").append(hash_algorithm).append("\" id=\"hash_algorithm\" />");
        html.append("<input type=\"hidden\" name=\"hashExtended\" value=\"").append(hashExtended).append("\" id=\"hashExtended\" />");
        html.append("<input type=\"hidden\" name=\"storename\" value=\"").append(storename).append("\" id=\"storename\" />");
        html.append("<input type=\"hidden\" name=\"chargetotal\" value=\"").append(chargetotal).append("\" id=\"chargetotal\" />");
        html.append("<input type=\"hidden\" name=\"currency\" value=\"").append(currency).append("\" id=\"currency\" />");
        html.append("<input type=\"hidden\" name=\"language\" value=\"").append(language).append("\" id=\"language\" />");
        html.append("<input type=\"hidden\" name=\"oid\" value=\"").append(oid).append("\" id=\"oid\" />");
        html.append("<input type=\"hidden\" name=\"responseFailURL\" value=\"").append(responseFailURL).append("\" id=\"responseFailURL\" />");
        html.append("<input type=\"hidden\" name=\"responseSuccessURL\" value=\"").append(responseSuccessURL).append("\" id=\"responseSuccessURL\" />");
        html.append("<input type=\"hidden\" name=\"transactionNotificationURL\" value=\"").append(transactionNotificationURL).append("\" id=\"transactionNotificationURL\" />");

        html.append("</form>");
        html.append("<script type=\"text/javascript\">");
        html.append("document.getElementById('").append(channel.getChannelCode()).append("').submit();");
        html.append("</script>");
        html.append("</body></html>");

        return html.toString();
    }

    @Override
    boolean isValidSignature(Map<String, String> unverifiedMessage) {
        // 验证是否是Fiserv返回的
        // https://docs.fiserv.com/docs/payments/ZG9jOjMzMzI5MDg-transaction-response
        String notificationHash = unverifiedMessage.get("notification_hash")
        if (StringUtils.isBlank(notificationHash)) {
            return false
        }
        String approvalCode = unverifiedMessage.get("approval_code")
        String chargeTotal = unverifiedMessage.get("chargetotal")
        String currency = unverifiedMessage.get("currency")
        String txnDateTime = unverifiedMessage.get("txndatetime")
        String storeName = this.channel.getChannelAccessConfig().getMerchantId()

        // chargetotal|currency|txndatetime|storename|approval_code
        String dataBeforeSign = chargeTotal + "|" + currency + "|" + txnDateTime + "|" + storeName + "|" + approvalCode

        // The hash algorithm is the same as the one that you have set in the transaction request.
        // Please note that you have to store the txndatetime in order to be able to calculate the hash.
        String dataAfterSign = calculateHMAC(dataBeforeSign, this.channel.getChannelAccessConfig().getAppId())
        LoggerUtil.info("dataAfterSign:{0},givenHash: {1},isValidSignature: {2}", dataAfterSign, notificationHash, notificationHash == dataAfterSign)
        return notificationHash == dataAfterSign
    }

    // 获取访问令牌
    private String getAccessToken() {
        if (isTokenExpired()) {
            renewToken()
        }
        return accessToken
    }

    // 检查令牌是否过期
    private boolean isTokenExpired() {
        return tokenExpiration == 0 || System.currentTimeMillis() >= tokenExpiration
    }

    // 更新访问令牌
    private void renewToken() {
        def openidConfig = getOpenidConfig()
        def tokenEndpoint = openidConfig.token_endpoint

        def postData = "grant_type=${GRANT_TYPE}&client_id=${CLIENT_ID}&client_secret=${CLIENT_SECRET}"
        def connection = new URL(tokenEndpoint).openConnection() as HttpURLConnection
        connection.requestMethod = "POST"
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded")
        connection.doOutput = true

        def outputStream = connection.outputStream
        outputStream.write(postData.getBytes())
        outputStream.flush()
        outputStream.close()

        def responseCode = connection.responseCode
        if (responseCode == HttpURLConnection.HTTP_OK) {
            def response = gson.fromJson(connection.inputStream.text, new TypeToken<Map<String, Object>>(){}.getType())
            accessToken = response.access_token
            tokenExpiration = System.currentTimeMillis() + (response.expires_in * 1000)
        } else {
            throw new RuntimeException("Failed to get access token: ${connection.errorStream.text}")
        }
    }

    // 获取 OpenID 配置
    private def getOpenidConfig() {
        def connection = new URL(OPENID_CONFIG_URL).openConnection() as HttpURLConnection
        connection.requestMethod = "GET"
        def responseCode = connection.responseCode
        if (responseCode == HttpURLConnection.HTTP_OK) {
            return gson.fromJson(connection.inputStream.text, new TypeToken<Map<String, Object>>(){}.getType())
        } else {
            throw new RuntimeException("Failed to get OpenID configuration: ${connection.errorStream.text}")
        }
    }

    // 创建支付请求
    def createPayRequest(Map<String, Object> requestBody) {
        def url = "${API_BASE_URL}/pay/requests"
        def connection = new URL(url).openConnection() as HttpURLConnection
        connection.requestMethod = "POST"
        connection.setRequestProperty("Authorization", "Bearer ${getAccessToken()}")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.doOutput = true

        def jsonBody = JsonOutput.toJson(requestBody)
        def outputStream = connection.outputStream
        outputStream.write(jsonBody.getBytes())
        outputStream.flush()
        outputStream.close()

        def responseCode = connection.responseCode
        if (responseCode == HttpURLConnection.HTTP_CREATED) {
            return gson.fromJson(connection.inputStream.text, new TypeToken<Map<String, Object>>(){}.getType())
        } else {
            throw new RuntimeException("Failed to create pay request: ${connection.errorStream.text}")
        }
    }

    // 其他 API 调用方法可以类似实现，如获取支付请求、更新支付请求等
}
