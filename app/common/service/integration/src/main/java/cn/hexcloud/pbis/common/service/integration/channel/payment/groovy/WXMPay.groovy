package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.commons.utils.UUIDUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSONObject
import com.squareup.okhttp.HttpUrl
import org.apache.commons.lang3.StringUtils
import cn.hutool.core.util.StrUtil

import javax.crypto.Cipher
import javax.crypto.spec.GCMParameterSpec
import javax.crypto.spec.SecretKeySpec
import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.security.GeneralSecurityException
import java.security.KeyFactory
import java.security.PublicKey
import java.security.Signature
import java.security.spec.PKCS8EncodedKeySpec
import java.security.spec.X509EncodedKeySpec
import java.sql.Timestamp

/**
 * 微信JSAPI下单
 * <pre>
 *     url: https://pay.weixin.qq.com/docs/merchant/apis/jsapi-payment/direct-jsons/jsapi-prepay.html
 * </pre>
 * <AUTHOR> Wang
 */
class WXMPay extends AbstractExternalChannelModule implements PaymentModule {

    WXMPay(ExternalChannel channel) {
        super(channel)
    }

    @Override
    protected String getSignModuleName() {
        return this.getModuleName()
    }

    @Override
    String getModuleName() {
        return "Payment"
    }

    @Override
    ChannelCreateResponse create(ChannelCreateRequest request) {
        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("appid", channel.getChannelAccessConfig().getAppId())
        bizParams.put("mchid", channel.getChannelAccessConfig().getMerchantId())
        bizParams.put("description", request.getDescription())
        bizParams.put("out_trade_no", request.getTransactionId())
        bizParams.put("notify_url", getNotificationUrl())
        Map<String, BigDecimal> amountBody = new HashMap<>()
        amountBody.put("total", request.getAmount())
        bizParams.put("amount", amountBody)
        Map<String, String> payerBody = new HashMap<>()
        payerBody.put("openid", request.getPayer())
        bizParams.put("payer", payerBody)
        String extendedParams = request.getExtendedParams()
        if (StringUtils.isNotEmpty(extendedParams)) {
            JSONObject extendedParamsJSON = JSONObject.parseObject(extendedParams)
            if (extendedParamsJSON != null) {
                String storeCode = extendedParamsJSON.getString("store_code")
                String storeName = extendedParamsJSON.getString("store_name")
                String attach = String.format("%s（%s）", storeName, storeCode)
                // 附加数据（门店名称+门店编码）
                bizParams.put("attach", attach)
                // 支付场景描述
                Map<String, Object> sceneInfo = new HashMap<>()
                // 用户终端IP（固定值：0.0.0.0）
                sceneInfo.put("payer_client_ip", "0.0.0.0")
                // 设备号（门店编码）
                sceneInfo.put("device_id", storeCode)
                bizParams.put("scene_info", sceneInfo)
            }
        }

        // 发起请求
        JSONObject resultJSON = doRequest("create", null, bizParams, true)

        long timestamp = (long) (System.currentTimeMillis() / 1000)
        String nonceStr = StrUtil.uuid().replace("-", "").toUpperCase()

        // 解析并返回结果
        ChannelCreateResponse response = new ChannelCreateResponse()
        response.setPrePayId(resultJSON.getString("prepay_id"))
        response.setPackStr("timeStamp=" + timestamp + "&nonceStr=" + nonceStr)
        // 返回前端拉起支付需要的sign值
        response.setPrePaySign(getSHA256withRSASign(timestamp, nonceStr, resultJSON.getString("prepay_id")))
        response.setChannel(request.getChannel())
        response.setTpTransactionId(request.getTransactionId())
        response.setPayMethod(PayMethod.WX_PAY)
        return response
    }

    // 返回前端拉起支付需要的sign
    private String getSHA256withRSASign(long timestamp, String nonceStr, String prepayId) {
        ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
        String dataBeforeSign = channelAccessConfig.getAppId() + "\n" + timestamp + "\n" + nonceStr + "\n" + "prepay_id=" + prepayId + "\n"
        // 签名并返回签名信息
        Signature sign = Signature.getInstance("SHA256withRSA")
        sign.initSign(KeyFactory.getInstance("RSA")
                .generatePrivate(new PKCS8EncodedKeySpec(Base64.getDecoder().decode(channelAccessConfig.getPrivateKey()))))
        sign.update(dataBeforeSign.getBytes(StandardCharsets.UTF_8))
        return Base64.getEncoder().encodeToString(sign.sign())
    }

    @Override
    ChannelPayResponse pay(ChannelPayRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("pay"))
    }

    @Override
    ChannelQueryResponse query(ChannelQueryRequest request) {
        // 请求URL
        String url = channel.getChannelAccessConfig().getProperty("query_url")
        String completeUrl = url.endsWith("/") ? url + request.getTransactionId() : url + "/" + request.getTransactionId()
        completeUrl = completeUrl + "?mchid=" + channel.getChannelAccessConfig().getMerchantId()

        // 发起请求
        JSONObject resultJSON = doRequest("query", completeUrl, null, false)

        // 解析并返回结果
        ChannelQueryResponse response = new ChannelQueryResponse()
        response.setChannel(request.getChannel())
        response.setPayMethod(PayMethod.WX_PAY)
        response.setTransactionId(resultJSON.getString("out_trade_no"))
        response.setTpTransactionId(resultJSON.getString("transaction_id"))
        response.setRealAmount(null == resultJSON.get("amount") ? null : resultJSON.get("amount")["total"] as BigDecimal)
        response.setTransactionState(mapTransactionState(resultJSON.getString("trade_state")))
        response.setExtendedParams(resultJSON.getString("attach"))
        return response
    }

    @Override
    ChannelRefundResponse refund(ChannelRefundRequest request) {
        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        if (StringUtils.isEmpty(request.getRelatedTransactionId())) {
            bizParams.put("transaction_id", request.getRelatedTPTransactionId())
        } else {
            bizParams.put("out_trade_no", request.getRelatedTransactionId())
        }
        bizParams.put("out_refund_no", request.getTransactionId())
        Map<String, Object> amountBody = new HashMap<>()
        amountBody.put("refund", (request.getAmount()))
        amountBody.put("total", (request.getOrderAmount()))
        amountBody.put("currency", "CNY")
        bizParams.put("amount", amountBody)

        // 发起请求
        JSONObject resultJSON = doRequest("refund", null, bizParams, true)

        // 解析并返回结果
        ChannelRefundResponse response = new ChannelRefundResponse()
        response.setTransactionState(mapTransactionState(resultJSON.getString("status")))
        response.setTransactionId(resultJSON.getString("out_refund_no"))
        response.setTpTransactionId(resultJSON.getString("refund_id"))
        response.setRealAmount(resultJSON.get("amount")["refund"] as BigDecimal)
        return response
    }

    @Override
    ChannelCancelResponse cancel(ChannelCancelRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("cancel"))
    }

    @Override
    ChannelNotificationResponse payNotify(HttpServletRequest request) {
        // 对请求进行验签
        String callbackJSONStr = request.getParameter("payload")
        LoggerUtil.info("{0} received message: {1}.", getMethodFullName("payNotify"), callbackJSONStr)
        Map<String, String> unverifiedMessage = new HashMap<>()
        unverifiedMessage.put("timestamp", request.getHeader("Wechatpay-Timestamp"))
        unverifiedMessage.put("nonce", request.getHeader("Wechatpay-Nonce"))
        unverifiedMessage.put("body", callbackJSONStr)
        unverifiedMessage.put("signature", request.getHeader("Wechatpay-Signature"))
        if (!isValidSignature(unverifiedMessage)) {
            // 验签失败
            throw new CommonException(ServiceError.INVALID_SIGNATURE)
        }

        // 解析参数
        JSONObject callbackJSON = JSONObject.parseObject(callbackJSONStr)
        JSONObject resourceNode = callbackJSON.getJSONObject("resource")
        String cipherText = resourceNode.getString("ciphertext")
        byte[] associatedData = resourceNode.getString("associated_data").getBytes()
        byte[] nonce = resourceNode.getString("nonce").getBytes()
        JSONObject resultJSON = JSONObject.parseObject(decrypt2String(channel.getChannelAccessConfig().getAppKey(),
                associatedData, nonce, cipherText))

        LoggerUtil.info("{0} received message: {1}.", getMethodFullName("payNotify"), resultJSON.toString())

        // 返回结果
        ChannelNotificationResponse response = new ChannelNotificationResponse()
        String response2ThirdParty = "{\"code\": \"SUCCESS\", \"message\": \"成功\"}"
        response.setResponse(response2ThirdParty)
        ChannelPayResponse payResponse = new ChannelQueryResponse()
        payResponse.setTransactionId(resultJSON.getString("out_trade_no"))
        payResponse.setTpTransactionId(resultJSON.getString("transaction_id"))
        payResponse.setRealAmount(resultJSON.get("amount")["payer_total"] as BigDecimal)
        payResponse.setTransactionState(mapTransactionState(resultJSON.getString("trade_state")))
        payResponse.setExtendedParams(resultJSON.getString("attach"))
        response.setPayResponse(payResponse)

        // 设置上下文（出入报文）
        TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, callbackJSONStr)
        TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, response2ThirdParty)

        return response
    }

    @Override
    String getSignature(Map<String, String> rawMessage) {
        ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

        // 解析数据
        String method = rawMessage.get("method")
        String url = rawMessage.get("url")
        String body = rawMessage.get("body")

        // 加工数据并得到签名原文
        HttpUrl httpUrl = HttpUrl.parse(url)
        String canonicalUrl = httpUrl.encodedPath()
        if (StringUtils.isNotEmpty(httpUrl.encodedQuery())) {
            canonicalUrl += "?" + httpUrl.encodedQuery()
        }
        long timestamp = (long) (System.currentTimeMillis() / 1000)
        String nonce = UUIDUtil.getUUID()
        String dataBeforeSign = method + "\n" + canonicalUrl + "\n" + timestamp + "\n" + nonce + "\n" + body + "\n"

        // 签名并返回签名信息
        Signature sign = Signature.getInstance("SHA256withRSA")
        sign.initSign(KeyFactory.getInstance("RSA")
                .generatePrivate(new PKCS8EncodedKeySpec(Base64.getDecoder().decode(channelAccessConfig.getPrivateKey()))))
        sign.update(dataBeforeSign.getBytes(StandardCharsets.UTF_8))
        String signature = Base64.getEncoder().encodeToString(sign.sign())

        StringBuilder sb = new StringBuilder()
        return sb.append("mchid=\"" + channelAccessConfig.getMerchantId() + "\",")
                .append("nonce_str=\"" + nonce + "\",")
                .append("timestamp=\"" + timestamp + "\",")
                .append("serial_no=\"" + channelAccessConfig.getCert().getSerialNumber().toString(16) + "\",")
                .append("signature=\"" + signature + "\"")
                .toString()
    }

    @Override
    boolean isValidSignature(Map<String, String> unverifiedMessage) {
        // 解析请求，获取待验签所需的数据
        String timestamp = unverifiedMessage.get("timestamp")
        String nonce = unverifiedMessage.get("nonce")
        String body = unverifiedMessage.get("body")
        String signature = unverifiedMessage.get("signature")
        if (StringUtils.isAnyBlank(timestamp, nonce, body, signature)) {
            return false
        }

        // 验证签名
        String message = timestamp + "\n" + nonce + "\n" + body + "\n"

        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(Base64.decoder
                .decode(channel.getChannelAccessConfig().getThirdPartyPublicKey().getBytes(StandardCharsets.UTF_8)))
        PublicKey pubKey = KeyFactory.getInstance("RSA").generatePublic(keySpec)
        Signature signatureInstance = Signature.getInstance("SHA256withRSA")
        signatureInstance.initVerify(pubKey)
        signatureInstance.update(message.getBytes(StandardCharsets.UTF_8))
        return signatureInstance.verify(Base64.decoder.decode(signature))
    }

    private static Map<String, String> getRequestHeader(String signature) {
        Map<String, String> header = new HashMap<>()
        header.put("Authorization", "WECHATPAY2-SHA256-RSA2048 " + signature)
        header.put("Accept", "application/json")
        header.put("User-Agent", "Mozilla/5.0 (Windows NT 5.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/38.0.2125.122 Safari/537.36 SE 2.X MetaSr 1.0")
        return header
    }

    private static TransactionState mapTransactionState(String wxTransactionState) {
        TransactionState transactionState
        switch (wxTransactionState) {
            case "NOTPAY":
                transactionState = TransactionState.WAITING
                break
            case "SUCCESS":
                transactionState = TransactionState.SUCCESS
                break
            case "PAYERROR":
            case "ABNORMAL":
                transactionState = TransactionState.FAILED
                break
            case "CLOSED":
                transactionState = TransactionState.CLOSED
                break
            case "REVOKED":
                transactionState = TransactionState.CANCELED
                break
            case "USERPAYING":
            case "PROCESSING":
                transactionState = TransactionState.PENDING
                break
            case "REFUND":
                transactionState = TransactionState.REFUNDED
                break
            default:
                transactionState = TransactionState.UNKNOWN
        }
        return transactionState
    }

    private static String decrypt2String(String apiKey, byte[] associatedData, byte[] nonce, String cipherText) throws GeneralSecurityException, IOException {
        Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding")
        SecretKeySpec key = new SecretKeySpec(apiKey.getBytes(StandardCharsets.UTF_8), "AES")
        GCMParameterSpec spec = new GCMParameterSpec(128, nonce)
        cipher.init(Cipher.DECRYPT_MODE, key, spec)
        cipher.updateAAD(associatedData)
        return new String(cipher.doFinal(Base64.getDecoder().decode(cipherText)), StandardCharsets.UTF_8)
    }

    private String getNotificationUrl() {
        String notificationUrl = channel.getChannelAccessConfig().getProperty("notification_url")
        if (StringUtils.isNotBlank(notificationUrl)) {
            String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
            String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
            String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId
            return notificationUrl.endsWith("/") ? notificationUrl + path : notificationUrl + "/" + path
        }

        return null
    }

    private JSONObject doRequest(String method, String url, Map<String, Object> bizParams, boolean reserveData) {
        ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
        String requestUrl
        String httpMethod
        String bodyJSON = ""

        // 请求参数
        if (StringUtils.isBlank(url)) {
            requestUrl = channelAccessConfig.getProperty(method + "_url")
            Map<String, Object> body = new HashMap<>()
            body.putAll(bizParams)
            bodyJSON = JSONObject.toJSONString(body)
        } else {
            requestUrl = url
        }
        httpMethod = StringUtils.isBlank(bodyJSON) ? "GET" : "POST"

        // 签名
        Map<String, String> rawMessage = new HashMap<>()
        rawMessage.put("method", httpMethod)
        rawMessage.put("url", requestUrl)
        rawMessage.put("body", bodyJSON)
        String signature = getSignature(rawMessage)

        // 发起HTTP请求
        String methodFullName = getMethodFullName(method)
        byte[] result
        Timestamp reqTime = DateUtil.getNowTimeStamp()
        if ("POST" == httpMethod) {
            LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
            result = HttpUtil.doPost(requestUrl, bodyJSON, getRequestHeader(signature))
        } else {
            LoggerUtil.info("{0} is sending message to: {1}.", methodFullName, requestUrl)
            result = HttpUtil.doGet(requestUrl, getRequestHeader(signature))
        }
        Timestamp respTime = DateUtil.getNowTimeStamp()
        if (null == result) {
            LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
        }
        String resultJSONStr = new String(result, StandardCharsets.UTF_8)

        // 设置上下文（出入报文）
        if (reserveData) {
            TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyJSON)
            TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
            TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
            TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
        }

        // 解析并返回结果
        JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
        LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
        String errorCode = resultJSON.getString("code")
        String errorMessage = resultJSON.getString("message")
        if (StringUtils.isNotEmpty(errorCode)) {
            // 请求失败
            LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, methodFullName, errorCode, errorMessage)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
        }

        return resultJSON
    }

    private String getMethodFullName(String method) {
        return channel.getChannelCode() + "." + getModuleName() + "." + method
    }

}
