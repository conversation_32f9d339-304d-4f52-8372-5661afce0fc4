package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.commons.utils.UUIDUtil
import cn.hexcloud.commons.utils.cipher.SHAUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.XmlUtil
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp

/**
 * 微信付款码支付
 * <pre>
 *     url: https://pay.weixin.qq.com/wiki/doc/api/micropay.php?chapter=5_1
 * </pre>
 * <AUTHOR> Wang
 */
class WXPay extends AbstractExternalChannelModule implements PaymentModule {

    private static final String SUCCESS_CODE = "SUCCESS"
    private static final String SIGN_TYPE = "HMAC-SHA256"
    private static final List<String> TOLERANCE_ERROR_CODE_LIST // 需要容错的错误码

    static {
        TOLERANCE_ERROR_CODE_LIST = new ArrayList<>()
        // 系统超时，请立即调用被扫订单结果查询API，查询当前订单状态，并根据订单的状态决定下一步的操作。
        TOLERANCE_ERROR_CODE_LIST.add("SYSTEMERROR")
        // 银行端超时，请立即调用被扫订单结果查询API，查询当前订单的不同状态，决定下一步的操作。
        TOLERANCE_ERROR_CODE_LIST.add("BANKERROR")
        // 用户正在输入密码，等待5秒，然后调用被扫订单结果查询API，查询当前订单的不同状态，决定下一步的操作。
        TOLERANCE_ERROR_CODE_LIST.add("USERPAYING")
    }

    WXPay(ExternalChannel channel) {
        super(channel)
    }

    @Override
    protected String getSignModuleName() {
        return this.getModuleName()
    }

    @Override
    String getModuleName() {
        return "Payment"
    }

    @Override
    ChannelCreateResponse create(ChannelCreateRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("create"))
    }

    @Override
    ChannelPayResponse pay(ChannelPayRequest request) {
        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("body", request.getOrderDescription())
        // 商户订单号
        bizParams.put("out_trade_no", request.getTransactionId())
        // 订单金额
        bizParams.put("total_fee", request.getAmount())
        // 终端IP（固定值：0.0.0.0）
        bizParams.put("spbill_create_ip", "0.0.0.0")
        // 付款码
        bizParams.put("auth_code", request.getPayCode())
        String extendedParams = request.getExtendedParams()
        if (StringUtils.isNotEmpty(extendedParams)) {
            JSONObject extendedParamsJSON = JSONObject.parseObject(extendedParams)
            if (extendedParamsJSON != null) {
                String storeCode = extendedParamsJSON.getString("store_code")
                String storeName = extendedParamsJSON.getString("store_name")
                String attach = String.format("%s（%s）", storeName, storeCode)
                // 附加数据（门店名称+门店编码）
                bizParams.put("attach", attach)
                // 设备号（门店编码）
                bizParams.put("device_info", storeCode)
            }
        }

        // 发起请求
        Map<String, String> resultMap = doRequest("pay", bizParams, true)

        // 解析并返回结果
        String resultCode = resultMap.get("result_code")
        String errorCode = resultMap.get("err_code")
        String errorMessage = resultMap.get("err_code_des")
        ChannelPayResponse response = new ChannelPayResponse()
        if (resultCode == SUCCESS_CODE && resultMap.get("trade_type") == "MICROPAY") {
            response.setTransactionState(TransactionState.SUCCESS)
        } else {
            if (errorCode == "SYSTEMERROR" || errorCode == "BANKERROR" || errorCode == "USERPAYING") {
                response.setTransactionState(TransactionState.PENDING)
            } else {
                // 支付失败
                LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, getMethodFullName("pay"), errorCode, errorMessage)
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
            }
        }
        response.setChannel(request.getChannel())
        response.setPayMethod(PayMethod.WX_PAY)
        response.setTransactionId(request.getTransactionId())
        response.setTpTransactionId(resultMap.get("transaction_id"))
        response.setRealAmount(request.getAmount())
        response.setExtendedParams(request.getExtendedParams())
        return response
    }

    @Override
    ChannelQueryResponse query(ChannelQueryRequest request) {
        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("out_trade_no", request.getTransactionId())

        // 发起请求
        Map<String, String> resultMap = doRequest("query", bizParams, false)

        // 解析并返回结果
        ChannelQueryResponse response = new ChannelQueryResponse()
        response.setChannel(request.getChannel())
        response.setPayMethod(PayMethod.WX_PAY)
        response.setTransactionId(resultMap.get("out_trade_no"))
        response.setTpTransactionId(resultMap.get("transaction_id"))
        response.setRealAmount(resultMap.get("total_fee") as BigDecimal)
        response.setTransactionState(mapTransactionState(resultMap.get("trade_state")))
        response.setExtendedParams(resultMap.get("attach"))
        return response
    }

    @Override
    ChannelRefundResponse refund(ChannelRefundRequest request) {
        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        bizParams.put("out_trade_no", request.getRelatedTransactionId())
        bizParams.put("out_refund_no", request.getTransactionId())
        bizParams.put("total_fee", request.getOrderAmount())
        bizParams.put("refund_fee", request.getAmount())

        // 发起请求
        Map<String, String> resultMap = doRequest("refund", bizParams, true, channel.getChannelAccessConfig().getCertText(),
                channel.getChannelAccessConfig().getMerchantId())

        // 解析并返回结果
        ChannelRefundResponse response = new ChannelRefundResponse()
        response.setTransactionId(request.getTransactionId())
        response.setTpTransactionId(resultMap.get("refund_id"))
        response.setRealAmount(request.getAmount())
        response.setTransactionState(TransactionState.PENDING)
        return response
    }

    @Override
    ChannelCancelResponse cancel(ChannelCancelRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("cancel"))
    }

    @Override
    ChannelNotificationResponse payNotify(HttpServletRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("payNotify"))
    }

    @Override
    String getSignature(Map<String, String> rawMessage) {
        // 加工数据并得到签名原文
        StringBuilder sb = new StringBuilder()
        for (Map.Entry<String, String> entry : rawMessage) {
            if (StringUtils.isNotBlank(entry.getValue())) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&")
            }
        }
        String dataBeforeSign = sb.append("key=").append(channel.getChannelAccessConfig().getAppKey())

        // 签名并返回签名信息
        return SHAUtil.signHAMC2HexStr(dataBeforeSign, channel.getChannelAccessConfig().getAppKey())
    }

    @Override
    boolean isValidSignature(Map<String, String> unverifiedMessage) {
        Map<String, String> map = new TreeMap<>(unverifiedMessage)
        String tpSignature = map.get("sign")
        map.remove("sign")
        String signature = getSignature(map)
        return tpSignature == signature
    }

    private static TransactionState mapTransactionState(String tpTransactionState) {
        TransactionState transactionState
        switch (tpTransactionState) {
            case "NOTPAY":
                transactionState = TransactionState.WAITING
                break
            case "SUCCESS":
                transactionState = TransactionState.SUCCESS
                break
            case "PAYERROR":
            case "ABNORMAL":
                transactionState = TransactionState.FAILED
                break
            case "CLOSED":
                transactionState = TransactionState.CLOSED
                break
            case "REVOKED":
                transactionState = TransactionState.CANCELED
                break
            case "USERPAYING":
            case "ACCEPT":
                transactionState = TransactionState.PENDING
                break
            case "REFUND":
                transactionState = TransactionState.REFUNDED
                break
            default:
                transactionState = TransactionState.UNKNOWN
        }
        return transactionState
    }

    private static Map<String, String> getRequestHeader() {
        Map<String, String> header = new HashMap<>()
        header.put("Content-type", "text/xml")
        header.put("Accept", "text/xml")
        return header
    }

    private Map<String, String> getResultMap(String resultXML) {
        Map<String, String> resultMap = XmlUtil.xmlToMap(resultXML)
        String returnCode = resultMap.get("return_code")
        if (SUCCESS_CODE == returnCode && !isValidSignature(resultMap)) {
            // 验签失败
            throw new CommonException(ServiceError.INVALID_SIGNATURE)
        }
        return resultMap
    }

    private Map<String, String> doRequest(String method, Map<String, Object> bizParams, boolean reserveData) {
        return doRequest(method, bizParams, reserveData, null, null)
    }

    private Map<String, String> doRequest(String method, Map<String, Object> bizParams, boolean reserveData, String cert, String password) {
        ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()

        // 请求参数
        Map<String, Object> body = new HashMap<>()
        body.put("appid", channelAccessConfig.getAppId())
        body.put("mch_id", channelAccessConfig.getMerchantId())
        body.put("sub_mch_id", channelAccessConfig.getSubMerchantId())
        body.put("nonce_str", UUIDUtil.getUUID())
        body.put("sign_type", SIGN_TYPE)
        body.putAll(bizParams)

        // 签名
        Map<String, String> rawMessage = new TreeMap<>()
        for (Map.Entry<String, Object> entry : body) {
            if (entry.getValue()) {
                rawMessage.put(entry.getKey(), entry.getValue().toString())
            }
        }
        body.put("sign", getSignature(rawMessage))

        // 发起HTTP请求
        String methodFullName = getMethodFullName(method)
        String bodyXML = XmlUtil.mapToXml(body, "xml")
        LoggerUtil.info("{0} is sending message: {1}", methodFullName, bodyXML)
        Timestamp reqTime = DateUtil.getNowTimeStamp()
        String url = channelAccessConfig.getProperty(method + "_url")
        byte[] result
        if (StringUtils.isNotBlank(cert) && StringUtils.isNotBlank(password)) {
            result = HttpUtil.doPost(url, bodyXML, getRequestHeader(), cert, password)
        } else {
            result = HttpUtil.doPost(url, bodyXML, getRequestHeader())
        }
        Timestamp respTime = DateUtil.getNowTimeStamp()
        if (null == result) {
            LoggerUtil.error("{0} is failed with null result.", null, methodFullName)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
        }
        String resultXML = new String(result, StandardCharsets.UTF_8)

        // 设置上下文（出入报文）
        if (reserveData) {
            TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyXML)
            TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
            TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultXML)
            TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
        }

        // 解析并返回结果
        LoggerUtil.info("{0} received message: {1}.", methodFullName, resultXML)
        Map<String, String> resultMap = getResultMap(resultXML)
        String returnCode = resultMap.get("return_code")
        String returnMessage = resultMap.get("return_msg")
        String resultCode = resultMap.get("result_code")
        String errorCode = resultMap.get("err_code")
        String errorMessage = resultMap.get("err_code_des")
        if (returnCode != SUCCESS_CODE) {
            // 请求失败
            LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, methodFullName, returnCode, returnMessage)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, returnMessage)
        } else if (resultCode != SUCCESS_CODE) {
            if (!TOLERANCE_ERROR_CODE_LIST.contains(errorCode)) {
                // 请求失败
                LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, methodFullName, errorCode, errorMessage)
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
            }
        }

        return resultMap
    }

    private String getMethodFullName(String method) {
        return channel.getChannelCode() + "." + getModuleName() + "." + method
    }

}
