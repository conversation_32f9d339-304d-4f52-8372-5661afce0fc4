package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Commodity
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSONObject
import org.springframework.util.DigestUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp

/**
 * 翼码支付
 * <pre>
 *     URL: https://open.wangcaio2o.com/docs/ipos-sa/4.1.md
 *     场景: POS扫码、付款码支付
 * </pre>
 *
 * <AUTHOR> Wang
 */
class YmPay extends AbstractExternalChannelModule implements PaymentModule {

    private static final Map<String, String> URL_MAP

    static {
        URL_MAP = new HashMap<>()
        URL_MAP.put("pay", "pay_url")
        URL_MAP.put("query", "query_url")
        URL_MAP.put("refund", "refund_url")
        URL_MAP.put("cancel", "cancel_url")
        URL_MAP.put("notification", "notification_url")
        URL_MAP.put("bindPos", "bind_pos_url")
    }

    YmPay(ExternalChannel channel) {
        super(channel)
    }

    @Override
    String getModuleName() {
        return "Payment"
    }

    @Override
    ChannelCreateResponse create(ChannelCreateRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "create")
    }

    @Override
    ChannelPayResponse pay(ChannelPayRequest request) {
        String method = "pay"

        // 请求URL
        String requestUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get(method))

        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        // 参数：请求类型（固定值：barcode_pay_request）
        bizParams.put("request_type", "barcode_pay_request")
        // 参数：商户号
        bizParams.put("isspid", channel.getChannelAccessConfig().getSubMerchantId())
        // 参数：终端号
        bizParams.put("pos_id", ServiceContext.getString(ContextKeyConstant.STORE_ID))
        // 参数：门店号
        bizParams.put("store_id", ServiceContext.getString(ContextKeyConstant.STORE_ID))
        // 参数：支付请求流水号
        bizParams.put("pos_seq", request.getTransactionId())
        // 参数：系统平台号
        bizParams.put("system_id", channel.getChannelAccessConfig().getMerchantId())

        Map<String, Object> barcodePayRequest = new HashMap<>()
        // 参数：条码支付动态码
        barcodePayRequest.put("barcode_info", request.getPayCode())
        // 参数：交易金额
        barcodePayRequest.put("tx_amt", request.getAmount().toInteger())
        List<Commodity> commodityList = request.getCommodities()
        if (commodityList != null && commodityList.size() > 0) {
            List<Map<String, Object>> goodsDetail = new ArrayList<>()
            for (Commodity commodity : commodityList) {
                Map<String, Object> goods = new HashMap<>()
                // 参数：商品编号
                goods.put("goods_id", commodity.getCode())
                // 参数：商品名称
                goods.put("goods_name", commodity.getName())
                // 参数：商品数量
                goods.put("quantity", commodity.getQuantity())
                // 参数：商品单价
                goods.put("price", commodity.getPrice().toInteger())
                goodsDetail.add(goods)
            }
            barcodePayRequest.put("goods_detail", goodsDetail)
        }
        bizParams.put("barcode_pay_request", barcodePayRequest)

        // 参数：sign
        bizParams.put("sign", createSign(getFilteredParams(bizParams)))

        // 执行
        JSONObject resultJSON = doRequest(method, requestUrl, bizParams, false)
        Map<String, Object> resInfo = getResInfo(resultJSON)

        // 解析结果集
        ChannelPayResponse response = new ChannelPayResponse()
        response.setChannel(request.getChannel())
        response.setTransactionId(request.getTransactionId())
        response.setTpTransactionId((String) resInfo.get("trade_no"))
        response.setPayMethod((PayMethod) resInfo.get("pay_method"))
        response.setTransactionState(TransactionState.WAITING)
        return response

    }

    @Override
    ChannelRefundResponse refund(ChannelRefundRequest request) {
        String method = "refund"

        // 请求URL
        String requestUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get(method))

        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        // 参数：请求类型（固定值：barcode_reverse_request）
        bizParams.put("request_type", "barcode_reverse_request")
        // 参数：商户号
        bizParams.put("isspid", channel.getChannelAccessConfig().getSubMerchantId())
        // 参数：终端号
        bizParams.put("pos_id", ServiceContext.getString(ContextKeyConstant.STORE_ID))
        // 参数：门店号
        bizParams.put("store_id", ServiceContext.getString(ContextKeyConstant.STORE_ID))
        // 参数：撤销请求流水号
        bizParams.put("pos_seq", request.getTransactionId())
        // 参数：系统平台号
        bizParams.put("system_id", channel.getChannelAccessConfig().getMerchantId())

        Map<String, Object> barcodeQueryRequest = new HashMap<>()
        // 参数：退款金额
        barcodeQueryRequest.put("tx_amt", request.getAmount().toInteger())
        // 参数：支付请求流水号
        barcodeQueryRequest.put("org_pos_seq", request.getRelatedTransactionId())

        bizParams.put("barcode_reverse_request", barcodeQueryRequest)

        // 参数：sign
        bizParams.put("sign", createSign(getFilteredParams(bizParams)))

        // 执行
        JSONObject resultJSON = doRequest(method, requestUrl, bizParams, false)
        Map<String, Object> resInfo = getResInfo(resultJSON)

        // 解析结果集
        ChannelRefundResponse response = new ChannelRefundResponse()
        response.setTpTransactionId((String) resInfo.get("trade_no"))
        response.setRealAmount((BigDecimal) resInfo.get("total_fee"))
        response.setTransactionState((TransactionState) resInfo.get("transaction_status"))
        return response
    }

    @Override
    ChannelQueryResponse query(ChannelQueryRequest request) {
        String method = "query"

        // 请求URL
        String requestUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get(method))

        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        // 参数：请求类型（固定值：barcode_query_request）
        bizParams.put("request_type", "barcode_query_request")
        // 参数：商户号
        bizParams.put("isspid", channel.getChannelAccessConfig().getSubMerchantId())
        // 参数：终端号
        bizParams.put("pos_id", ServiceContext.getString(ContextKeyConstant.STORE_ID))
        // 参数：门店号
        bizParams.put("store_id", ServiceContext.getString(ContextKeyConstant.STORE_ID))
        // 参数：系统平台号
        bizParams.put("system_id", channel.getChannelAccessConfig().getMerchantId())

        Map<String, Object> barcodeQueryRequest = new HashMap<>()
        barcodeQueryRequest.put("org_pos_seq", request.getTransactionId())
        // 参数：支付请求流水号
        bizParams.put("barcode_query_request", barcodeQueryRequest)

        // 参数：sign
        bizParams.put("sign", createSign(getFilteredParams(bizParams)))

        // 执行
        JSONObject resultJSON = doRequest(method, requestUrl, bizParams, false)
        Map<String, Object> resInfo = getResInfo(resultJSON)

        // 解析结果集
        ChannelPayResponse response = new ChannelQueryResponse()
        response.setChannel(request.getChannel())
        response.setTpTransactionId((String) resInfo.get("trade_no"))
        response.setRealAmount((BigDecimal) resInfo.get("total_fee"))
        response.setTransactionState((TransactionState) resInfo.get("transaction_status"))
        return response
    }

    @Override
    ChannelCancelResponse cancel(ChannelCancelRequest request) {
        String method = "cancel"

        // 请求URL
        String requestUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get(method))

        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        // 参数：请求类型（固定值：barcode_cancel_request）
        bizParams.put("request_type", "barcode_cancel_request")
        // 参数：商户号
        bizParams.put("isspid", channel.getChannelAccessConfig().getSubMerchantId())
        // 参数：终端号
        bizParams.put("pos_id", ServiceContext.getString(ContextKeyConstant.STORE_ID))
        // 参数：门店号
        bizParams.put("store_id", ServiceContext.getString(ContextKeyConstant.STORE_ID))
        // 参数：撤销请求流水号
        bizParams.put("pos_seq", request.getTransactionId())
        // 参数：系统平台号
        bizParams.put("system_id", channel.getChannelAccessConfig().getMerchantId())

        Map<String, Object> barcodeQueryRequest = new HashMap<>()
        barcodeQueryRequest.put("org_pos_seq", request.getRelatedTransactionId())
        // 参数：支付请求流水号
        bizParams.put("barcode_cancel_request", barcodeQueryRequest)

        // 参数：sign
        bizParams.put("sign", createSign(getFilteredParams(bizParams)))

        // 执行
        JSONObject resultJSON = doRequest(method, requestUrl, bizParams, false)
        Map<String, Object> resInfo = getResInfo(resultJSON)

        // 解析结果集
        ChannelCancelResponse response = new ChannelCancelResponse()
        response.setTpTransactionId((String) resInfo.get("trade_no"))
        response.setRealAmount((BigDecimal) resInfo.get("total_fee"))
        response.setTransactionState((TransactionState) resInfo.get("transaction_status"))
        return response
    }

    @Override
    ChannelNotificationResponse payNotify(HttpServletRequest request) {
        throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "payNotify")
    }

    // 绑定POS门店
    JSONObject bindPosStore(Map<String, String> params) {
        String method = "bindPos"

        // 请求URL
        String requestUrl = channel.getChannelAccessConfig().getProperty(URL_MAP.get(method))

        // 请求参数
        Map<String, Object> bizParams = new HashMap<>()
        // 参数：请求类型（固定值：bind_pos_request）
        bizParams.put("request_type", "bind_pos_request")
        // 参数：系统平台号
        bizParams.put("system_id", channel.getChannelAccessConfig().getMerchantId())
        // 参数：商户号
        bizParams.put("isspid", channel.getChannelAccessConfig().getSubMerchantId())
        // 参数：终端号
        bizParams.put("pos_id", params.get("storeId"))

        Map<String, Object> storeInfo = new HashMap<>()
        // 参数：条码支付动态码
        storeInfo.put("store_id", params.get("storeId"))
        storeInfo.put("store_name", params.get("storeName"))
        storeInfo.put("store_addr", params.get("storeAddress"))
        bizParams.put("store_info", storeInfo)

        // 参数：sign
        bizParams.put("sign", createSign(getFilteredParams(bizParams)))

        // 执行
        return doRequest(method, requestUrl, bizParams, false)
    }

    // 状态转换
    private static TransactionState mapTransactionState(String tpTransactionState) {
        TransactionState transactionState
        switch (tpTransactionState) {
            case "0000":
                transactionState = TransactionState.SUCCESS
                break
            case "9998":
                transactionState = TransactionState.WAITING
                break
            default:
                transactionState = TransactionState.FAILED
        }
        return transactionState
    }

    // 统一请求
    private JSONObject doRequest(String method, String requestUrl, Map<String, Object> bizParams, boolean reserveData) {
        String fullMethodName = getFullMethodName(method)
        LoggerUtil.info("{0} is sending request URL: {1}", fullMethodName, requestUrl)

        String requestBody = JSONObject.toJSONString(bizParams)
        LoggerUtil.info("{0} is sending request body: {1}", fullMethodName, requestBody)
        Timestamp reqTime = cn.hexcloud.commons.utils.DateUtil.getNowTimeStamp()
        try {
            // 请求Header
            Map<String, String> headers = new HashMap<>()
            headers.put("Content-Type", "application/json")

            byte[] payload = HttpUtil.doPost(requestUrl, requestBody, headers)
            if (null == payload) {
                LoggerUtil.error("{0} is failed with null result.", null, fullMethodName)
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
            }
            Timestamp respTime = cn.hexcloud.commons.utils.DateUtil.getNowTimeStamp()
            String payloadJSONStr = new String(payload, StandardCharsets.UTF_8)
            LoggerUtil.info("{0} received message: {1}", fullMethodName, payloadJSONStr)

            // 设置上下文（出入报文）
            if (reserveData) {
                TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
                TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, requestBody)
                TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
                TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, payloadJSONStr)
            }

            JSONObject payloadJSON = JSONObject.parseObject(payloadJSONStr)
            JSONObject resultJSON = payloadJSON.getJSONObject("result")
            if (resultJSON == null) {
                LoggerUtil.error("{0} is failed with null result.", null, fullMethodName)
                throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED)
            }
            return payloadJSON
        } catch (Exception ex) {
            LoggerUtil.error("Error {0} payment request: ", ex, fullMethodName)
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, ex.getMessage())
        }
    }

    // 筛选参数
    private Map<String, String> getFilteredParams(Map<String, Object> params) {
        Map<String, String> result = new HashMap<>()
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey()
            Object value = entry.getValue()
            if (value instanceof Map || value instanceof List || value instanceof byte[]) {
                continue
            }
            result.put(key, value.toString())
        }
        return result
    }

    // 产生sign
    private String createSign(Map<String, String> param) {
        String appSecret = channel.getChannelAccessConfig().getAccessKey()
        String dataBeforeSign = sortAndJoin(param)
        String preSign = appSecret + dataBeforeSign + appSecret
        return DigestUtils.md5DigestAsHex(preSign.getBytes(StandardCharsets.UTF_8)).toUpperCase()
    }

    // 排序并拼接参数
    private String sortAndJoin(Map<String, String> params) {
        List<String> keys = new ArrayList<>(params.keySet())
        Collections.sort(keys)
        StringBuffer resSb = new StringBuffer()
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i)
            String value = params.get(key)
            resSb.append(key).append(value)
        }
        return resSb.toString()
    }

    // 获取结果集信息
    private Map<String, Object> getResInfo(JSONObject dataJSON) {
        Map<String, String> result = new HashMap<>()
        JSONObject resultJSON = dataJSON.getJSONObject("result")
        String statusCode = resultJSON.getString("id")
        String message = resultJSON.getString("comment")
        TransactionState transactionStatus = mapTransactionState(statusCode)
        if (transactionStatus == TransactionState.FAILED) {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, message)
        }
        result.put("transaction_status", transactionStatus)

        // 阿里支付
        JSONObject aliPayJSON = dataJSON.getJSONObject("alipay_res_info")
        // 微信支付
        JSONObject wxPayJSON = dataJSON.getJSONObject("wxpay_res_info")
        // 银联二维码
        JSONObject uPayJSON = dataJSON.getJSONObject("upay_res_info")
        // 会员余额支付
        JSONObject balPayJSON = dataJSON.getJSONObject("balpay_res_info")

        // 只会存在一种支付方式
        if (aliPayJSON != null && aliPayJSON.size() > 0) {
            result.put("pay_method", PayMethod.ALI_PAY)
            result.put("trade_no", aliPayJSON.getString("trade_no"))
            result.put("total_fee", aliPayJSON.getBigDecimal("total_fee"))
        } else if (wxPayJSON != null && wxPayJSON.size() > 0) {
            result.put("pay_method", PayMethod.WX_PAY)
            result.put("trade_no", wxPayJSON.getString("trade_no"))
            result.put("total_fee", wxPayJSON.getBigDecimal("total_fee"))
        } else if (uPayJSON != null && uPayJSON.size() > 0) {
            result.put("pay_method", PayMethod.UNION_PAY)
            result.put("trade_no", uPayJSON.getString("trade_no"))
            result.put("total_fee", uPayJSON.getBigDecimal("total_fee"))
        } else if (balPayJSON != null && balPayJSON.size() > 0) {
            result.put("pay_method", PayMethod.OTHERS)
            result.put("trade_no", balPayJSON.getString("trade_no"))
            result.put("total_fee", balPayJSON.getBigDecimal("total_fee"))
        } else {
            throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Payment result is null, only supply 'AliPay'、'WxPay'、'UnionPay'、'YmBalPay'")
        }
        return result
    }

}
