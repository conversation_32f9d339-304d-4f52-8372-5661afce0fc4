package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.NotificationType
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import cn.hutool.core.util.StrUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.apache.commons.lang3.StringUtils
import org.springframework.util.DigestUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp

/**
 * @program: pbis* @author: miao* @create: 2022-06-15 15:11
 * 接口稳定：
 * https://www.apifox.cn/apidoc/shared-bd1ad6b2-c31d-461e-b5bd-0603889f861c/api-19611996
 * 密码：aGX7z7Vm
 * */
class YunxiIntPay extends AbstractExternalChannelModule implements PaymentModule {
  private static final API_SUCCESS_CODE = 1
  private static final REFUND_NOTIFY = "refundNotify"
  private static final PAY_NOTIFY = "payNotify"
  // 指客户端和服务器建立连接的timeout
  private static final Integer CONN_TIME_OUT = 10000
  // 从连接池获取连接的timeout
  private static final Integer CONN_REQUEST_TIMOUT = 10000
  // 指客户端从服务器读取数据的timeout
  private static final Integer SOCKET_TIMEOUT = 10000

  YunxiIntPay(ExternalChannel channel) {
    super(channel)
  }

  //统一请求方法
  private JSONObject doRequest(String model, String method, String target, Map<String, Object> bizParams, boolean reserveData) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    //json
    String bodyJSON = JSON.toJSONString(bizParams)
    // 发起HTTP请求
    String methodFullName = getMethodFullName(target)

    if (StrUtil.isBlank(channel.getChannelAccessConfig().getGatewayUrl())) {
      throw new CommonException(ServiceError.INVALID_PARAM, "gateway_url")
    }
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result = null
    try {
      result = HttpUtil.doPost(channelAccessConfig.getGatewayUrl(), bodyJSON, getRequestHeader(model, method, bodyJSON), CONN_TIME_OUT, SOCKET_TIMEOUT, CONN_REQUEST_TIMOUT)
    } catch (Exception e) {
      LoggerUtil.error("request yunxiIntPay error", e)
      result = defaultErrorMsg().getBytes()
    }
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)
    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyJSON)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }
    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
    int errorCode = resultJSON.getIntValue("code")
    String errorMessage = resultJSON.getString("message")
    if (errorCode != API_SUCCESS_CODE) {
      // 请求失败
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, methodFullName, errorCode, errorMessage)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }
    return resultJSON
  }

  private Map<String, String> getRequestHeader(String model, String method, String msg) {
    String ts = String.valueOf(System.currentTimeMillis())
    Map<String, String> headerMap = new HashMap<>()
    headerMap.put("appid", channel.getChannelAccessConfig().getAppKey())
    headerMap.put("model", model)
    headerMap.put("method", method)
    headerMap.put("ts", ts)
    headerMap.put("msg", msg)
    String sign = getSignature(headerMap)
    headerMap.put("sign", sign)
    return headerMap
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }

  String getSignature(Map<String, String> rawMessage) {
    String ts = rawMessage.get("ts")
    //拼接加密字符串
    StringBuffer b = new StringBuffer()
    b.append(channel.getChannelAccessConfig().getAppKey())
        .append(channel.getChannelAccessConfig().getAccessKey())
        .append(ts)
        .append(JSON.toJSON(rawMessage.get("msg")).toString())
    LoggerUtil.debug("{0}待加密参数{1}", channel.getChannelAccessConfig().getChannelCode(), b.toString())
    DigestUtils.md5DigestAsHex(b.toString().getBytes(StandardCharsets.UTF_8))
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, getMethodFullName("create"))
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("payCode", request.getTransactionId())
    bizParams.put("totalAmount", request.getAmount().divide(BigDecimal.valueOf(100)))
    bizParams.put("storeCode", ServiceContext.getString(ContextKeyConstant.STORE_CODE))
    try {
      if (request.getExtendedParams()) {
        JSONObject exJson = JSON.parseObject(request.getExtendedParams())
        if (exJson.containsKey("ticket_uno") && null != exJson.get("ticket_uno")) {
          // POS端交易号
          bizParams.put("remark", exJson.getString("ticket_uno"))
        }
      }
    } catch (Exception ex) {
      LoggerUtil.error("解析扩展字段异常{0}", ex, ex.getMessage())
    }

    //1000: 聚合支付
    bizParams.put("payType", 1000)
    bizParams.put("notifyUrl", getNotificationUrl())
    List<Map<String, Object>> skuList = new ArrayList<>()
    if (request.getCommodities()) {
      for (commodity in request.getCommodities()) {
        Map<String, Object> skuBody = new HashMap<>()
        skuBody.put("name", commodity.getName())
        skuBody.put("num", commodity.getQuantity())
        skuBody.put("price", commodity.getPrice().divide(BigDecimal.valueOf(100)))
        skuBody.put("thirdItemCode", commodity.getCode())
        skuList.add(skuBody)
      }
    }
    bizParams.put("authCode", request.getPayCode())
    bizParams.put("items", skuList)
    bizParams.put("merchantCode", channel.getChannelAccessConfig().getMerchantId())
    bizParams.put("merchantAppId", channel.getChannelAccessConfig().getAppId())
    JSONObject resultJSON = doRequest("trade", "payOrder", "pay", bizParams, true)

    JSONObject result = resultJSON.getJSONObject("result")
    ChannelPayResponse response = new ChannelPayResponse()
    response.setChannel(request.getChannel())
    response.setTransactionId(request.getTransactionId())
    response.setTpTransactionId(result.getString("payId"))
    response.setTransactionState(mapTransactionState(result.getString("payStatus")))
    response.setPayMethod(mapPayMethod(result.getString("payChannel")))
    if (result?.getString("childPayChannel")) {
      response.setPayMethod(mapPayMethod(result.getString("childPayChannel")))
    }

    // 查询支付结果
    // 当支付结果同步响应支付业务结果，请根据payStatus字段来判断
    JSONObject payResultJSON = payQuery(result.getString("payId"), request.getTransactionId())
    JSONObject payResult = payResultJSON.getJSONObject("result")
    response.setTransactionState(mapTransactionState(payResult.getString("payStatus")))
    response.setWarningMessage(payResult.getString("remark"))
    return response
  }

  private static String defaultErrorMsg() {
    return "{\"code\":1,\"message\":\"成功\",\"result\":{\"payStatus\":\"NOTPAY\",\"remark\":\"第三方系统超时\",\"result\":\"ACCEPT\"}}"
  }

  //映射合阔支付方式
  private static TransactionState mapTransactionState(String payStatus) {
    //SUCCESS: 支付成功
    //REFUND: 转入退款
    //NOTPAY: 未支付
    //CLOSED: 已关闭
    //PAYERROR: 支付失败(其他原因，如银行返回失败)
    //USERPAYING: 用户支付中
    //REVOKED: 已撤销
    switch (payStatus) {
      case "SUCCESS":
        TransactionState.SUCCESS
        break
      case "PAYERROR":
        TransactionState.FAILED
        break
      case "CHANGE":
        TransactionState.FAILED
        break
      case "REVOKED":
        TransactionState.CANCELED
        break
      case "CLOSED":
        TransactionState.CLOSED
        break
      case "REFUNDCLOSE":
        TransactionState.CLOSED
        break
      case "USERPAYING":
        TransactionState.PENDING
        break
      case "REFUND":
        TransactionState.REFUNDED
        break
      case "NOTPAY":
        TransactionState.WAITING
        break
      default:
        TransactionState.UNKNOWN
        break
    }
  }

  //映射合阔支付方式
  private static PayMethod mapPayMethod(String payChannel) {
    //0: 其他
    //10: 支付宝
    //20: 微信
    //30: 扫呗
    //40: 会员储值支付
    //50: 积分支付
    //60: 现金
    //70: 刷卡
    //80: 云闪付
    //90: 抖音
    switch (payChannel) {
      case "10":
        PayMethod.ALI_PAY
        break
      case "20":
        PayMethod.WX_PAY
        break
      case "40":
        PayMethod.YUNXI_PAY
        break
      case "80":
        PayMethod.QuickPass
        break
      case "510":
        PayMethod.CCB
        break
      default:
        PayMethod.OTHERS
        break
    }
  }

  // 退款查询
  private JSONObject refundQuery(String refundCode) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("refundCode", refundCode)
    bizParams.put("merchantCode", channel.getChannelAccessConfig().getMerchantId())
    bizParams.put("merchantAppId", channel.getChannelAccessConfig().getAppId())
    JSONObject resultJSON = doRequest("trade", "refundQuery", "refundQuery", bizParams, true)
    return resultJSON
  }

  // 退款查询
  private JSONObject cancelQuery(String payId) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("payId", payId)
    bizParams.put("merchantCode", channel.getChannelAccessConfig().getMerchantId())
    bizParams.put("merchantAppId", channel.getChannelAccessConfig().getAppId())
    JSONObject resultJSON = doRequest("trade", "payCancelQuery", "cancelQuery", bizParams, true)
    return resultJSON
  }

  // 支付查询
  private JSONObject payQuery(String payId, String payCode) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("payId", payId)
    bizParams.put("payCode", payCode)
    bizParams.put("merchantCode", channel.getChannelAccessConfig().getMerchantId())
    bizParams.put("merchantAppId", channel.getChannelAccessConfig().getAppId())
    JSONObject resultJSON = doRequest("trade", "payQuery", "query", bizParams, false)
    return resultJSON
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    JSONObject resultJSON = payQuery(request.getTpTransactionId(), request.getTransactionId())
    ChannelQueryResponse response = new ChannelQueryResponse()
    JSONObject result = resultJSON.getJSONObject("result")
    response.setTransactionState(mapTransactionState(result.getString("payStatus")))
    response.setTransactionId(result.getString("payCode"))
    response.setTpTransactionId(result.getString("payId"))
    response.setRealAmount(result.getBigDecimal("totalAmount"))
    response.setPayMethod(mapPayMethod(result.getString("payChannel")))
    // 获取会员id
    JSONObject userInfo = result.getJSONObject("userInfo")
    if (userInfo) {
      response.setPayer(userInfo.getString("openId"))
    }
    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("payId", request.getRelatedTPTransactionId())
    bizParams.put("refundCode", request.getTransactionId())
    bizParams.put("refundAmount", request.getAmount().divide(BigDecimal.valueOf(100)))
    bizParams.put("notifyUrl", getNotificationUrl())
    bizParams.put("merchantCode", channel.getChannelAccessConfig().getMerchantId())
    bizParams.put("merchantAppId", channel.getChannelAccessConfig().getAppId())
    JSONObject resultJSON = doRequest("trade", "refundOrder", "refund", bizParams, true)
    JSONObject result = resultJSON.getJSONObject("result")
    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionState(mapTransactionState(result.getString("refundStatus")))
    response.setTpTransactionId(result.getString("refundId"))

    //查询退款覆盖退款状态
    JSONObject payResultJSON = refundQuery(request.getTransactionId())
    JSONObject payResult = payResultJSON.getJSONObject("result")
    response.setTransactionState(mapTransactionState(payResult.getString("refundStatus")))
    return response
  }

  private static TransactionState mapCancelState(String cancelStatus) {
    switch (cancelStatus) {
      case "ACCEPT":
        TransactionState.PENDING
        break
      case "PROCESS":
        TransactionState.PENDING
        break
      case "SUCCESS":
        TransactionState.SUCCESS
        break
      case "FAIL":
        TransactionState.FAILED
        break
      default:
        TransactionState.UNKNOWN
        break
    }
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("payId", request.getRelatedTPTransactionId())
    bizParams.put("payType", 1000)
    bizParams.put("merchantCode", channel.getChannelAccessConfig().getMerchantId())
    bizParams.put("merchantAppId", channel.getChannelAccessConfig().getAppId())
    JSONObject resultJSON = doRequest("trade", "payCancel", "cancel", bizParams, true)
    JSONObject result = resultJSON.getJSONObject("result")
    ChannelCancelResponse response = new ChannelCancelResponse()
    response.setTransactionState(mapCancelState(result.getString("cancelStatus")))
    response.setTpTransactionId(result.getString("payId"))

    //查询退款覆盖退款状态
//    JSONObject payResultJSON = cancelQuery(request.getRelatedTPTransactionId())
//    JSONObject payResult = payResultJSON.getJSONObject("result")
//    response.setTransactionState(mapTransactionState(payResult.getString("cancelStatus")))
    return response
  }

  @Override
  ChannelNotificationResponse payNotify(HttpServletRequest request) {
    String callbackJSONStr = request.getParameter("payload")
    String method = request.getHeader("method")
    JSONObject callbackJSON = JSONObject.parseObject(callbackJSONStr)
    LoggerUtil.info("{0}.payNotify.{1} received message:{2} ", channel.getChannelAccessConfig().getChannelCode(), method, callbackJSONStr)
    // 获取回调方法
    ChannelNotificationResponse response = new ChannelNotificationResponse()
    String response2ThirdParty = "{\"code\":1,\"message\":\"成功\",\"result\":null}"
    response.setResponse(response2ThirdParty)
    // 退款通知
    if (method == REFUND_NOTIFY) {
      ChannelPayResponse payResponse = new ChannelQueryResponse()
      payResponse.setTransactionId(callbackJSON.getString("refundCode"))
      payResponse.setTpTransactionId(callbackJSON.getString("refundId"))
      payResponse.setRealAmount(callbackJSON.getBigDecimal("refundAmount"))
      payResponse.setTransactionState(mapTransactionState(callbackJSON.getString("refundStatus")))
      payResponse.setNotificationType(NotificationType.REFUND)
      response.setPayResponse(payResponse)
    } else if (method == PAY_NOTIFY) {
      ChannelPayResponse payResponse = new ChannelQueryResponse()
      payResponse.setTransactionId(callbackJSON.getString("payCode"))
      payResponse.setPayMethod(PayMethod.byCode(callbackJSON.getString("payChannel")))
      payResponse.setTpTransactionId(callbackJSON.getString("payId"))
      payResponse.setRealAmount(callbackJSON.getBigDecimal("totalAmount"))
      payResponse.setTransactionState(mapTransactionState(callbackJSON.getString("payStatus")))
      payResponse.setNotificationType(NotificationType.PAY)
      response.setPayResponse(payResponse)
    }

    // 设置上下文（出入报文）
    TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, callbackJSONStr)
    TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, response2ThirdParty)

    return response
  }

  private String getNotificationUrl() {
    String notificationUrl = channel.getChannelAccessConfig().getProperty("notification_url")
    if (StringUtils.isNotBlank(notificationUrl)) {
      String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
      String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
      String path = channel.getChannelCode() + "/" + partnerId + "/" + storeId
      return notificationUrl.endsWith("/") ? notificationUrl + path : notificationUrl + "/" + path
    }

    return null
  }

  @Override
  String getModuleName() {
    return "Payment"
  }
}
