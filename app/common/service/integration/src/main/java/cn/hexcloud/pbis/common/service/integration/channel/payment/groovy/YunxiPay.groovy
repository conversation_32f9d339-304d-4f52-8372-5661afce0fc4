package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.exception.CommonException
import cn.hexcloud.commons.log.LoggerUtil
import cn.hexcloud.commons.utils.DateUtil
import cn.hexcloud.commons.utils.HttpUtil
import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.*
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.PayMethod
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.enums.TransactionState
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.PaymentModule
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import cn.hexcloud.pbis.common.util.exception.ServiceError
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.springframework.util.DigestUtils

import javax.servlet.http.HttpServletRequest
import java.nio.charset.StandardCharsets
import java.sql.Timestamp

/**
 * @program: pbis* @author: miao* @create: 2021-12-27 14:35
 * */
class YunxiPay extends AbstractExternalChannelModule implements PaymentModule {
  private static final Map<String, String> API_VERSION_MAP
  private static final API_SUCCESS_CODE = 1
  static {
    API_VERSION_MAP = new HashMap<>()
    API_VERSION_MAP.put("query", "2.0")
  }

  YunxiPay(ExternalChannel channel) {
    super(channel)
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName()
  }

  @Override
  String getModuleName() {
    return "Payment"
  }

  String getSignature(Map<String, String> rawMessage) {
    String ts = rawMessage.get("ts")
    //拼接加密字符串
    StringBuffer b = new StringBuffer()
    b.append(channel.getChannelAccessConfig().getAccessKey())
        .append(channel.getChannelAccessConfig().getPrivateKey())
        .append(ts)
        .append(JSON.toJSON(rawMessage.get("msg")).toString())
    LoggerUtil.debug("{0}待加密参数{1}", channel.getChannelAccessConfig().getChannelCode(), b.toString())
    DigestUtils.md5DigestAsHex(b.toString().getBytes(StandardCharsets.UTF_8))
  }

  @Override
  ChannelCreateResponse create(ChannelCreateRequest request) {
    return null
  }

  /**
   * 获取动态吗
   * @param memberNo
   * @return
   */
  def String getMemberDynamicCode(String memberNo) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("memberCardNo", memberNo)
    JSONObject jsonData = doRequest("member", "queryDynamicCode", "queryDynamicCode", bizParams, false)
    JSONObject codeResult = jsonData.getJSONObject("result")
    return codeResult.getString("jBarCode")
  }

  @Override
  ChannelPayResponse pay(ChannelPayRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("memberCardNo", request.getMemberNo())
    bizParams.put("orderNo", request.getOrderNo())
    bizParams.put("other_id", request.getMemberNo())
    String storeCode = ServiceContext.getString(ContextKeyConstant.STORE_CODE)
    bizParams.put("storeCode", storeCode)
    if (request.getPayCode()) {
      // 卡号与payCode一致
      if (request.getPayCode().endsWith(request.getMemberNo())) {
        bizParams.put("wxConsumeNo", getMemberDynamicCode(request.getMemberNo()))
      } else {
        // 不一致默认为动态码
        bizParams.put("wxConsumeNo", request.getPayCode())
      }
    } else {
      bizParams.put("wxConsumeNo", getMemberDynamicCode(request.getMemberNo()))
    }
    bizParams.put("price", request.getAmount().divide(BigDecimal.valueOf(100)))
    bizParams.put("remark", "储值消费")
    JSONObject jsonData = doRequest("customer", "paidConsume", "pay", bizParams, true)
    ChannelPayResponse response = new ChannelPayResponse()
    response.setChannel(request.getChannel())
    response.setPayMethod(PayMethod.YUNXI_PAY)
    response.setTransactionId(request.getTransactionId())
    response.setTransactionState(TransactionState.SUCCESS)
    response.setRealAmount(request.getAmount())
    return response
  }

  @Override
  ChannelQueryResponse query(ChannelQueryRequest request) {
    // 云徙接口新增接口 5.10，查询储值消费流水
    // 如根据流水号查询到订单流水代表支付成功，如果为查询到数据则认为支付失败
    // 技术方案：https://www.tapd.cn/********/prong/stories/view/11********001012225
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    bizParams.put("orderNo", request.getOrderNo())
    JSONObject jsonData = doRequest("account", "checkOrderNo", "query", bizParams, false)
    ChannelQueryResponse response = new ChannelQueryResponse()
    response.setChannel(request.getChannel())
    response.setPayMethod(PayMethod.YUNXI_PAY)
    // 支付状态
    response.setTransactionState(TransactionState.SUCCESS)
    response.setTransactionId(request.getTransactionId())
    // 支付金额
    JSONObject jsonResult = jsonData.getJSONObject("result")
    if (jsonResult) {
      response.setRealAmount(jsonResult.getBigDecimal("tradeAmount"))
      response.setTransactionId(jsonResult.getString("orderNo"))
      response.setTpTransactionId(jsonResult.getString("tradeId"))
    } else {
      response.setTransactionState(TransactionState.FAILED)
    }
    return response
  }

  @Override
  ChannelRefundResponse refund(ChannelRefundRequest request) {
    // 请求参数
    Map<String, Object> bizParams = new HashMap<>()
    String storeCode = ServiceContext.getString(ContextKeyConstant.STORE_CODE)
    bizParams.put("storeCode", storeCode)
    bizParams.put("other_id", request.getMemberNo())
    bizParams.put("orderNo", request.getOrderNo())
    bizParams.put("refundOrderNo", request.getTransactionId())
    bizParams.put("mobile", request.getMobile())
    bizParams.put("price", request.getAmount().divide(BigDecimal.valueOf(100)))
    bizParams.put("remark", "储值回退")
    JSONObject jsonData = doRequest("customer", "paidRefund", "refund", bizParams, true)
    ChannelRefundResponse response = new ChannelRefundResponse()
    response.setTransactionId(request.getTransactionId())
    response.setTransactionState(TransactionState.SUCCESS)
    response.setRealAmount(request.getAmount())
    return response
  }

  @Override
  ChannelCancelResponse cancel(ChannelCancelRequest request) {
    return null
  }

  @Override
  ChannelNotificationResponse payNotify(HttpServletRequest request) {
    return null
  }

  private JSONObject doRequest(String model, String method, String method2, Map<String, Object> bizParams, boolean reserveData) {
    ChannelAccessConfig channelAccessConfig = channel.getChannelAccessConfig()
    //json
    String bodyJSON = JSON.toJSONString(bizParams)
    // 发起HTTP请求
    String methodFullName = getMethodFullName(method2)
    LoggerUtil.info("{0} is sending message: {1}.", methodFullName, bodyJSON)
    Timestamp reqTime = DateUtil.getNowTimeStamp()
    byte[] result = HttpUtil.doPost(channelAccessConfig.getGatewayUrl(), bodyJSON, getRequestHeader(model, method, bodyJSON, method2))
    Timestamp respTime = DateUtil.getNowTimeStamp()
    if (null == result) {
      LoggerUtil.error("{0} is failed, null result.", null, methodFullName)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, "Empty response.")
    }
    String resultJSONStr = new String(result, StandardCharsets.UTF_8)
    // 设置上下文（出入报文）
    if (reserveData) {
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_DATA, bodyJSON)
      TransactionLoggerContext.set(ContextKeyConstant.REQUEST_TIME, reqTime)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_DATA, resultJSONStr)
      TransactionLoggerContext.set(ContextKeyConstant.RESPONSE_TIME, respTime)
    }
    // 解析并返回结果
    JSONObject resultJSON = JSONObject.parseObject(resultJSONStr)
    LoggerUtil.info("{0} received message: {1}.", methodFullName, resultJSONStr)
    int errorCode = resultJSON.getIntValue("code")
    String errorMessage = resultJSON.getString("message")
    if (errorCode != API_SUCCESS_CODE) {
      // 请求失败
      LoggerUtil.error("{0} is failed, code: {1}, message: {2}.", null, methodFullName, errorCode, errorMessage)
      throw new CommonException(ServiceError.THIRD_PARTY_API_FAILED, errorMessage)
    }
    return resultJSON
  }

  private Map<String, String> getRequestHeader(String model, String method, String msg, String method2) {
    String ts = String.valueOf(System.currentTimeMillis())
    Map<String, String> headerMap = new HashMap<>()
    headerMap.put("appid", channel.getChannelAccessConfig().getAccessKey())
    headerMap.put("model", model)
    headerMap.put("method", method)
    headerMap.put("ts", ts)
    headerMap.put("msg", msg)
    String sign = getSignature(headerMap)
    headerMap.put("sign", sign)
    if (API_VERSION_MAP.containsKey(method2)) {
      headerMap.put("v", API_VERSION_MAP.get(method2))
      // 云徙测试环境参数，生产不传
      boolean isex = channel.channelAccessConfig.getProperties().containsKey("dtyunxi-saas-service-namespace")
      if (isex) {
        headerMap.put("dtyunxi-saas-service-namespace", channel.channelAccessConfig.getProperties().get("dtyunxi-saas-service-namespace"))
      }
    }
    return headerMap
  }

  private String getMethodFullName(String method) {
    return channel.getChannelCode() + "." + getModuleName() + "." + method
  }
}
