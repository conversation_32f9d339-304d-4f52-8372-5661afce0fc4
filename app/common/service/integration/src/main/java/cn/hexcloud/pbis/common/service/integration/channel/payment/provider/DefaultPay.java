package cn.hexcloud.pbis.common.service.integration.channel.payment.provider;

import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannel;
import cn.hexcloud.pbis.common.service.integration.channel.ChannelAccessSupportService;
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

/**
 * @ClassName DefaultPay.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/13 19:07:55
 */
@Service
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
public class DefaultPay extends AbstractExternalChannel implements PaymentChannel {

  @Override
  public String getChannelCode() {
    return channelAccessConfig.getChannelCode();
  }

  @Override
  public String getChannelName() {
    return channelAccessConfig.getChannelCode();
  }

  @Override
  public void init(ChannelAccessConfig channelAccessConfig) {
    this.channelAccessConfig = channelAccessConfig;
  }

  @Override
  public ChannelAccessConfig getChannelAccessConfig() {
    return channelAccessConfig;
  }

  @Override
  public ChannelAccessSupportService getChannelAccessSupportService() {
    return channelAccessSupportService;
  }

  @Override
  public PaymentModule getPaymentModule() {
    return new DefaultPaymentModule(this);
  }

}
