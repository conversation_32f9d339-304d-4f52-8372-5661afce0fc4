package cn.hexcloud.pbis.common.service.integration.channel.payment.provider;

import cn.hexcloud.pbis.common.service.integration.channel.AbstractExternalChannelModule;
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName DefaultPaymentModule.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2022/01/22 13:02:39
 */
public class DefaultPaymentModule extends AbstractExternalChannelModule implements PaymentModule {

  public DefaultPaymentModule(ExternalChannel channel) {
    super(channel);
  }

  @Override
  protected String getSignModuleName() {
    return this.getModuleName();
  }

  @Override
  public ChannelCreateResponse create(ChannelCreateRequest request) {
    return (ChannelCreateResponse) channel.doRequest(this.getModuleName(), "create", request);
  }

  @Override
  public ChannelPayResponse pay(ChannelPayRequest request) {
    return (ChannelPayResponse) channel.doRequest(this.getModuleName(), "pay", request);
  }

  @Override
  public ChannelQueryResponse query(ChannelQueryRequest request) {
    return (ChannelQueryResponse) channel.doRequest(this.getModuleName(), "query", request);
  }

  @Override
  public ChannelRefundResponse refund(ChannelRefundRequest request) {
    return (ChannelRefundResponse) channel.doRequest(this.getModuleName(), "refund", request);
  }

  @Override
  public ChannelCancelResponse cancel(ChannelCancelRequest request) {
    return (ChannelCancelResponse) channel.doRequest(this.getModuleName(), "cancel", request);
  }

  @Override
  public ChannelNotificationResponse payNotify(HttpServletRequest request) {
    return (ChannelNotificationResponse) channel.doRequest(this.getModuleName(), "payNotify", request);
  }

  @Override
  public String getSignature(Map<String, String> rawMessage) {
    return super.getSignature(rawMessage);
  }

  @Override
  public boolean isValidSignature(Map<String, String> unverifiedMessage) {
    return super.isValidSignature(unverifiedMessage);
  }
}
