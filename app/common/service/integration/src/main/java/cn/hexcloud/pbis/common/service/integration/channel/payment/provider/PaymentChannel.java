package cn.hexcloud.pbis.common.service.integration.channel.payment.provider;

import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;

public interface PaymentChannel extends ExternalChannel {

  PaymentModule getPaymentModule();

}