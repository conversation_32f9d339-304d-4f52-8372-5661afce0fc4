package cn.hexcloud.pbis.common.service.integration.channel.payment.provider;

import cn.hexcloud.commons.utils.SpringContextUtil;
import cn.hexcloud.pbis.common.service.integration.channel.ChannelHub;
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannel;
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannelHub;
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName PaymentProviderHub.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/14 18:29:51
 */
@Component
public class PaymentChannelHub implements ExternalChannelHub<PaymentChannel> {

  @Autowired
  private Map<String, PaymentChannel> paymentChannelMap;

  @Override
  public PaymentChannel on(String channelCode) {
    PaymentChannel paymentChannel = paymentChannelMap.get(channelCode);
    if (null == paymentChannel) {
      paymentChannel = SpringContextUtil.bean(DefaultPay.class);
    }

    return paymentChannel;
  }

  @Override
  public PaymentChannel on(String channelCode, ChannelAccessConfig channelAccessConfig) {
    PaymentChannel paymentChannel = this.on(channelCode);
    paymentChannel.init(channelAccessConfig);
    return paymentChannel;
  }
}
