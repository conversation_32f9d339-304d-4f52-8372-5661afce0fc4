package cn.hexcloud.pbis.common.service.integration.channel.payment.provider;

import cn.hexcloud.commons.exception.CommonException;
import cn.hexcloud.pbis.common.service.integration.channel.ExternalChannelModule;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCancelResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelQueryResponse;
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelRefundResponse;
import cn.hexcloud.pbis.common.util.exception.ServiceError;
import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName PaymentModule.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2022/01/22 12:59:15
 */
public interface PaymentModule extends ExternalChannelModule {

  default ChannelCreateResponse create(ChannelCreateRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "create");
  }

  default ChannelPayResponse pay(ChannelPayRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "pay");
  }

  default ChannelQueryResponse query(ChannelQueryRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "query");
  }

  default ChannelRefundResponse refund(ChannelRefundRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "refund");
  }

  default ChannelCancelResponse cancel(ChannelCancelRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "cancel");
  }

  default ChannelNotificationResponse payNotify(HttpServletRequest request) {
    throw new CommonException(ServiceError.UNSUPPORTED_OPERATION, "payNotify");
  }

  @Override
  default String getModuleName() {
    return "Payment";
  }

}
