// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

/**
 * Protobuf type {@code eticket_proto.Amount}
 */
public final class Amount extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:eticket_proto.Amount)
    AmountOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Amount.newBuilder() to construct.
  private Amount(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Amount() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Amount();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Amount(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 9: {

            taxAmount_ = input.readDouble();
            break;
          }
          case 17: {

            grossAmount_ = input.readDouble();
            break;
          }
          case 25: {

            netAmount_ = input.readDouble();
            break;
          }
          case 33: {

            payAmount_ = input.readDouble();
            break;
          }
          case 41: {

            discountAmount_ = input.readDouble();
            break;
          }
          case 49: {

            removezeroAmount_ = input.readDouble();
            break;
          }
          case 57: {

            rounding_ = input.readDouble();
            break;
          }
          case 65: {

            overflowAmount_ = input.readDouble();
            break;
          }
          case 73: {

            changeAmount_ = input.readDouble();
            break;
          }
          case 81: {

            serviceFee_ = input.readDouble();
            break;
          }
          case 89: {

            tip_ = input.readDouble();
            break;
          }
          case 97: {

            commission_ = input.readDouble();
            break;
          }
          case 105: {

            amount0_ = input.readDouble();
            break;
          }
          case 113: {

            amount1_ = input.readDouble();
            break;
          }
          case 121: {

            amount2_ = input.readDouble();
            break;
          }
          case 129: {

            amount3_ = input.readDouble();
            break;
          }
          case 137: {

            amount4_ = input.readDouble();
            break;
          }
          case 144: {

            taxIncluded_ = input.readBool();
            break;
          }
          case 157: {

            otherFee_ = input.readFloat();
            break;
          }
          case 165: {

            merchantDiscountAmount_ = input.readFloat();
            break;
          }
          case 173: {

            platformDiscountAmount_ = input.readFloat();
            break;
          }
          case 181: {

            projectedIncome_ = input.readFloat();
            break;
          }
          case 185: {

            receivable_ = input.readDouble();
            break;
          }
          case 193: {

            realAmount_ = input.readDouble();
            break;
          }
          case 201: {

            businessAmount_ = input.readDouble();
            break;
          }
          case 209: {

            expendAmount_ = input.readDouble();
            break;
          }
          case 217: {

            paymentTransferAmount_ = input.readDouble();
            break;
          }
          case 225: {

            discountTransferAmount_ = input.readDouble();
            break;
          }
          case 233: {

            storeDiscountAmount_ = input.readDouble();
            break;
          }
          case 241: {

            discountMerchantContribute_ = input.readDouble();
            break;
          }
          case 249: {

            discountPlatformContribute_ = input.readDouble();
            break;
          }
          case 257: {

            discountBuyerContribute_ = input.readDouble();
            break;
          }
          case 265: {

            discountOtherContribute_ = input.readDouble();
            break;
          }
          case 273: {

            payMerchantContribute_ = input.readDouble();
            break;
          }
          case 281: {

            payPlatformContribute_ = input.readDouble();
            break;
          }
          case 289: {

            payBuyerContribute_ = input.readDouble();
            break;
          }
          case 297: {

            payOtherContribute_ = input.readDouble();
            break;
          }
          case 305: {

            deliveryFee_ = input.readDouble();
            break;
          }
          case 313: {

            deliveryFeeForPlatform_ = input.readDouble();
            break;
          }
          case 321: {

            deliveryFeeForMerchant_ = input.readDouble();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Amount_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Amount_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.eticket.Amount.class, cn.hexcloud.pbis.common.service.integration.eticket.Amount.Builder.class);
  }

  public static final int TAXAMOUNT_FIELD_NUMBER = 1;
  private double taxAmount_;
  /**
   * <code>double taxAmount = 1;</code>
   * @return The taxAmount.
   */
  @java.lang.Override
  public double getTaxAmount() {
    return taxAmount_;
  }

  public static final int GROSS_AMOUNT_FIELD_NUMBER = 2;
  private double grossAmount_;
  /**
   * <code>double gross_amount = 2;</code>
   * @return The grossAmount.
   */
  @java.lang.Override
  public double getGrossAmount() {
    return grossAmount_;
  }

  public static final int NET_AMOUNT_FIELD_NUMBER = 3;
  private double netAmount_;
  /**
   * <code>double net_amount = 3;</code>
   * @return The netAmount.
   */
  @java.lang.Override
  public double getNetAmount() {
    return netAmount_;
  }

  public static final int PAY_AMOUNT_FIELD_NUMBER = 4;
  private double payAmount_;
  /**
   * <code>double pay_amount = 4;</code>
   * @return The payAmount.
   */
  @java.lang.Override
  public double getPayAmount() {
    return payAmount_;
  }

  public static final int DISCOUNT_AMOUNT_FIELD_NUMBER = 5;
  private double discountAmount_;
  /**
   * <code>double discount_amount = 5;</code>
   * @return The discountAmount.
   */
  @java.lang.Override
  public double getDiscountAmount() {
    return discountAmount_;
  }

  public static final int REMOVEZERO_AMOUNT_FIELD_NUMBER = 6;
  private double removezeroAmount_;
  /**
   * <code>double removezero_amount = 6;</code>
   * @return The removezeroAmount.
   */
  @java.lang.Override
  public double getRemovezeroAmount() {
    return removezeroAmount_;
  }

  public static final int ROUNDING_FIELD_NUMBER = 7;
  private double rounding_;
  /**
   * <code>double rounding = 7;</code>
   * @return The rounding.
   */
  @java.lang.Override
  public double getRounding() {
    return rounding_;
  }

  public static final int OVERFLOW_AMOUNT_FIELD_NUMBER = 8;
  private double overflowAmount_;
  /**
   * <code>double overflow_amount = 8;</code>
   * @return The overflowAmount.
   */
  @java.lang.Override
  public double getOverflowAmount() {
    return overflowAmount_;
  }

  public static final int CHANGEAMOUNT_FIELD_NUMBER = 9;
  private double changeAmount_;
  /**
   * <code>double changeAmount = 9;</code>
   * @return The changeAmount.
   */
  @java.lang.Override
  public double getChangeAmount() {
    return changeAmount_;
  }

  public static final int SERVICEFEE_FIELD_NUMBER = 10;
  private double serviceFee_;
  /**
   * <code>double serviceFee = 10;</code>
   * @return The serviceFee.
   */
  @java.lang.Override
  public double getServiceFee() {
    return serviceFee_;
  }

  public static final int TIP_FIELD_NUMBER = 11;
  private double tip_;
  /**
   * <code>double tip = 11;</code>
   * @return The tip.
   */
  @java.lang.Override
  public double getTip() {
    return tip_;
  }

  public static final int COMMISSION_FIELD_NUMBER = 12;
  private double commission_;
  /**
   * <code>double commission = 12;</code>
   * @return The commission.
   */
  @java.lang.Override
  public double getCommission() {
    return commission_;
  }

  public static final int AMOUNT_0_FIELD_NUMBER = 13;
  private double amount0_;
  /**
   * <code>double amount_0 = 13;</code>
   * @return The amount0.
   */
  @java.lang.Override
  public double getAmount0() {
    return amount0_;
  }

  public static final int AMOUNT_1_FIELD_NUMBER = 14;
  private double amount1_;
  /**
   * <code>double amount_1 = 14;</code>
   * @return The amount1.
   */
  @java.lang.Override
  public double getAmount1() {
    return amount1_;
  }

  public static final int AMOUNT_2_FIELD_NUMBER = 15;
  private double amount2_;
  /**
   * <code>double amount_2 = 15;</code>
   * @return The amount2.
   */
  @java.lang.Override
  public double getAmount2() {
    return amount2_;
  }

  public static final int AMOUNT_3_FIELD_NUMBER = 16;
  private double amount3_;
  /**
   * <code>double amount_3 = 16;</code>
   * @return The amount3.
   */
  @java.lang.Override
  public double getAmount3() {
    return amount3_;
  }

  public static final int AMOUNT_4_FIELD_NUMBER = 17;
  private double amount4_;
  /**
   * <code>double amount_4 = 17;</code>
   * @return The amount4.
   */
  @java.lang.Override
  public double getAmount4() {
    return amount4_;
  }

  public static final int TAXINCLUDED_FIELD_NUMBER = 18;
  private boolean taxIncluded_;
  /**
   * <pre>
   *价税合一
   * </pre>
   *
   * <code>bool taxIncluded = 18;</code>
   * @return The taxIncluded.
   */
  @java.lang.Override
  public boolean getTaxIncluded() {
    return taxIncluded_;
  }

  public static final int OTHERFEE_FIELD_NUMBER = 19;
  private float otherFee_;
  /**
   * <pre>
   *其他费用
   * </pre>
   *
   * <code>float otherFee = 19;</code>
   * @return The otherFee.
   */
  @java.lang.Override
  public float getOtherFee() {
    return otherFee_;
  }

  public static final int MERCHANT_DISCOUNT_AMOUNT_FIELD_NUMBER = 20;
  private float merchantDiscountAmount_;
  /**
   * <pre>
   *商家优惠承担
   * </pre>
   *
   * <code>float merchant_discount_amount = 20;</code>
   * @return The merchantDiscountAmount.
   */
  @java.lang.Override
  public float getMerchantDiscountAmount() {
    return merchantDiscountAmount_;
  }

  public static final int PLATFORM_DISCOUNT_AMOUNT_FIELD_NUMBER = 21;
  private float platformDiscountAmount_;
  /**
   * <pre>
   *活动平台优惠承担
   * </pre>
   *
   * <code>float platform_discount_amount = 21;</code>
   * @return The platformDiscountAmount.
   */
  @java.lang.Override
  public float getPlatformDiscountAmount() {
    return platformDiscountAmount_;
  }

  public static final int PROJECTED_INCOME_FIELD_NUMBER = 22;
  private float projectedIncome_;
  /**
   * <pre>
   *预计收入
   * </pre>
   *
   * <code>float projected_income = 22;</code>
   * @return The projectedIncome.
   */
  @java.lang.Override
  public float getProjectedIncome() {
    return projectedIncome_;
  }

  public static final int RECEIVABLE_FIELD_NUMBER = 23;
  private double receivable_;
  /**
   * <pre>
   *应付金额/应收金额
   * </pre>
   *
   * <code>double receivable = 23;</code>
   * @return The receivable.
   */
  @java.lang.Override
  public double getReceivable() {
    return receivable_;
  }

  public static final int REAL_AMOUNT_FIELD_NUMBER = 24;
  private double realAmount_;
  /**
   * <code>double real_amount = 24;</code>
   * @return The realAmount.
   */
  @java.lang.Override
  public double getRealAmount() {
    return realAmount_;
  }

  public static final int BUSINESS_AMOUNT_FIELD_NUMBER = 25;
  private double businessAmount_;
  /**
   * <code>double business_amount = 25;</code>
   * @return The businessAmount.
   */
  @java.lang.Override
  public double getBusinessAmount() {
    return businessAmount_;
  }

  public static final int EXPEND_AMOUNT_FIELD_NUMBER = 26;
  private double expendAmount_;
  /**
   * <code>double expend_amount = 26;</code>
   * @return The expendAmount.
   */
  @java.lang.Override
  public double getExpendAmount() {
    return expendAmount_;
  }

  public static final int PAYMENT_TRANSFER_AMOUNT_FIELD_NUMBER = 27;
  private double paymentTransferAmount_;
  /**
   * <code>double payment_transfer_amount = 27;</code>
   * @return The paymentTransferAmount.
   */
  @java.lang.Override
  public double getPaymentTransferAmount() {
    return paymentTransferAmount_;
  }

  public static final int DISCOUNT_TRANSFER_AMOUNT_FIELD_NUMBER = 28;
  private double discountTransferAmount_;
  /**
   * <code>double discount_transfer_amount = 28;</code>
   * @return The discountTransferAmount.
   */
  @java.lang.Override
  public double getDiscountTransferAmount() {
    return discountTransferAmount_;
  }

  public static final int STORE_DISCOUNT_AMOUNT_FIELD_NUMBER = 29;
  private double storeDiscountAmount_;
  /**
   * <code>double store_discount_amount = 29;</code>
   * @return The storeDiscountAmount.
   */
  @java.lang.Override
  public double getStoreDiscountAmount() {
    return storeDiscountAmount_;
  }

  public static final int DISCOUNT_MERCHANT_CONTRIBUTE_FIELD_NUMBER = 30;
  private double discountMerchantContribute_;
  /**
   * <code>double discount_merchant_contribute = 30;</code>
   * @return The discountMerchantContribute.
   */
  @java.lang.Override
  public double getDiscountMerchantContribute() {
    return discountMerchantContribute_;
  }

  public static final int DISCOUNT_PLATFORM_CONTRIBUTE_FIELD_NUMBER = 31;
  private double discountPlatformContribute_;
  /**
   * <code>double discount_platform_contribute = 31;</code>
   * @return The discountPlatformContribute.
   */
  @java.lang.Override
  public double getDiscountPlatformContribute() {
    return discountPlatformContribute_;
  }

  public static final int DISCOUNT_BUYER_CONTRIBUTE_FIELD_NUMBER = 32;
  private double discountBuyerContribute_;
  /**
   * <code>double discount_buyer_contribute = 32;</code>
   * @return The discountBuyerContribute.
   */
  @java.lang.Override
  public double getDiscountBuyerContribute() {
    return discountBuyerContribute_;
  }

  public static final int DISCOUNT_OTHER_CONTRIBUTE_FIELD_NUMBER = 33;
  private double discountOtherContribute_;
  /**
   * <code>double discount_other_contribute = 33;</code>
   * @return The discountOtherContribute.
   */
  @java.lang.Override
  public double getDiscountOtherContribute() {
    return discountOtherContribute_;
  }

  public static final int PAY_MERCHANT_CONTRIBUTE_FIELD_NUMBER = 34;
  private double payMerchantContribute_;
  /**
   * <code>double pay_merchant_contribute = 34;</code>
   * @return The payMerchantContribute.
   */
  @java.lang.Override
  public double getPayMerchantContribute() {
    return payMerchantContribute_;
  }

  public static final int PAY_PLATFORM_CONTRIBUTE_FIELD_NUMBER = 35;
  private double payPlatformContribute_;
  /**
   * <code>double pay_platform_contribute = 35;</code>
   * @return The payPlatformContribute.
   */
  @java.lang.Override
  public double getPayPlatformContribute() {
    return payPlatformContribute_;
  }

  public static final int PAY_BUYER_CONTRIBUTE_FIELD_NUMBER = 36;
  private double payBuyerContribute_;
  /**
   * <code>double pay_buyer_contribute = 36;</code>
   * @return The payBuyerContribute.
   */
  @java.lang.Override
  public double getPayBuyerContribute() {
    return payBuyerContribute_;
  }

  public static final int PAY_OTHER_CONTRIBUTE_FIELD_NUMBER = 37;
  private double payOtherContribute_;
  /**
   * <code>double pay_other_contribute = 37;</code>
   * @return The payOtherContribute.
   */
  @java.lang.Override
  public double getPayOtherContribute() {
    return payOtherContribute_;
  }

  public static final int DELIVERY_FEE_FIELD_NUMBER = 38;
  private double deliveryFee_;
  /**
   * <code>double delivery_fee = 38;</code>
   * @return The deliveryFee.
   */
  @java.lang.Override
  public double getDeliveryFee() {
    return deliveryFee_;
  }

  public static final int DELIVERY_FEE_FOR_PLATFORM_FIELD_NUMBER = 39;
  private double deliveryFeeForPlatform_;
  /**
   * <code>double delivery_fee_for_platform = 39;</code>
   * @return The deliveryFeeForPlatform.
   */
  @java.lang.Override
  public double getDeliveryFeeForPlatform() {
    return deliveryFeeForPlatform_;
  }

  public static final int DELIVERY_FEE_FOR_MERCHANT_FIELD_NUMBER = 40;
  private double deliveryFeeForMerchant_;
  /**
   * <code>double delivery_fee_for_merchant = 40;</code>
   * @return The deliveryFeeForMerchant.
   */
  @java.lang.Override
  public double getDeliveryFeeForMerchant() {
    return deliveryFeeForMerchant_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (taxAmount_ != 0D) {
      output.writeDouble(1, taxAmount_);
    }
    if (grossAmount_ != 0D) {
      output.writeDouble(2, grossAmount_);
    }
    if (netAmount_ != 0D) {
      output.writeDouble(3, netAmount_);
    }
    if (payAmount_ != 0D) {
      output.writeDouble(4, payAmount_);
    }
    if (discountAmount_ != 0D) {
      output.writeDouble(5, discountAmount_);
    }
    if (removezeroAmount_ != 0D) {
      output.writeDouble(6, removezeroAmount_);
    }
    if (rounding_ != 0D) {
      output.writeDouble(7, rounding_);
    }
    if (overflowAmount_ != 0D) {
      output.writeDouble(8, overflowAmount_);
    }
    if (changeAmount_ != 0D) {
      output.writeDouble(9, changeAmount_);
    }
    if (serviceFee_ != 0D) {
      output.writeDouble(10, serviceFee_);
    }
    if (tip_ != 0D) {
      output.writeDouble(11, tip_);
    }
    if (commission_ != 0D) {
      output.writeDouble(12, commission_);
    }
    if (amount0_ != 0D) {
      output.writeDouble(13, amount0_);
    }
    if (amount1_ != 0D) {
      output.writeDouble(14, amount1_);
    }
    if (amount2_ != 0D) {
      output.writeDouble(15, amount2_);
    }
    if (amount3_ != 0D) {
      output.writeDouble(16, amount3_);
    }
    if (amount4_ != 0D) {
      output.writeDouble(17, amount4_);
    }
    if (taxIncluded_ != false) {
      output.writeBool(18, taxIncluded_);
    }
    if (otherFee_ != 0F) {
      output.writeFloat(19, otherFee_);
    }
    if (merchantDiscountAmount_ != 0F) {
      output.writeFloat(20, merchantDiscountAmount_);
    }
    if (platformDiscountAmount_ != 0F) {
      output.writeFloat(21, platformDiscountAmount_);
    }
    if (projectedIncome_ != 0F) {
      output.writeFloat(22, projectedIncome_);
    }
    if (receivable_ != 0D) {
      output.writeDouble(23, receivable_);
    }
    if (realAmount_ != 0D) {
      output.writeDouble(24, realAmount_);
    }
    if (businessAmount_ != 0D) {
      output.writeDouble(25, businessAmount_);
    }
    if (expendAmount_ != 0D) {
      output.writeDouble(26, expendAmount_);
    }
    if (paymentTransferAmount_ != 0D) {
      output.writeDouble(27, paymentTransferAmount_);
    }
    if (discountTransferAmount_ != 0D) {
      output.writeDouble(28, discountTransferAmount_);
    }
    if (storeDiscountAmount_ != 0D) {
      output.writeDouble(29, storeDiscountAmount_);
    }
    if (discountMerchantContribute_ != 0D) {
      output.writeDouble(30, discountMerchantContribute_);
    }
    if (discountPlatformContribute_ != 0D) {
      output.writeDouble(31, discountPlatformContribute_);
    }
    if (discountBuyerContribute_ != 0D) {
      output.writeDouble(32, discountBuyerContribute_);
    }
    if (discountOtherContribute_ != 0D) {
      output.writeDouble(33, discountOtherContribute_);
    }
    if (payMerchantContribute_ != 0D) {
      output.writeDouble(34, payMerchantContribute_);
    }
    if (payPlatformContribute_ != 0D) {
      output.writeDouble(35, payPlatformContribute_);
    }
    if (payBuyerContribute_ != 0D) {
      output.writeDouble(36, payBuyerContribute_);
    }
    if (payOtherContribute_ != 0D) {
      output.writeDouble(37, payOtherContribute_);
    }
    if (deliveryFee_ != 0D) {
      output.writeDouble(38, deliveryFee_);
    }
    if (deliveryFeeForPlatform_ != 0D) {
      output.writeDouble(39, deliveryFeeForPlatform_);
    }
    if (deliveryFeeForMerchant_ != 0D) {
      output.writeDouble(40, deliveryFeeForMerchant_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (taxAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(1, taxAmount_);
    }
    if (grossAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(2, grossAmount_);
    }
    if (netAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(3, netAmount_);
    }
    if (payAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(4, payAmount_);
    }
    if (discountAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(5, discountAmount_);
    }
    if (removezeroAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(6, removezeroAmount_);
    }
    if (rounding_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(7, rounding_);
    }
    if (overflowAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(8, overflowAmount_);
    }
    if (changeAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(9, changeAmount_);
    }
    if (serviceFee_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(10, serviceFee_);
    }
    if (tip_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(11, tip_);
    }
    if (commission_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(12, commission_);
    }
    if (amount0_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(13, amount0_);
    }
    if (amount1_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(14, amount1_);
    }
    if (amount2_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(15, amount2_);
    }
    if (amount3_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(16, amount3_);
    }
    if (amount4_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(17, amount4_);
    }
    if (taxIncluded_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(18, taxIncluded_);
    }
    if (otherFee_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(19, otherFee_);
    }
    if (merchantDiscountAmount_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(20, merchantDiscountAmount_);
    }
    if (platformDiscountAmount_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(21, platformDiscountAmount_);
    }
    if (projectedIncome_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(22, projectedIncome_);
    }
    if (receivable_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(23, receivable_);
    }
    if (realAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(24, realAmount_);
    }
    if (businessAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(25, businessAmount_);
    }
    if (expendAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(26, expendAmount_);
    }
    if (paymentTransferAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(27, paymentTransferAmount_);
    }
    if (discountTransferAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(28, discountTransferAmount_);
    }
    if (storeDiscountAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(29, storeDiscountAmount_);
    }
    if (discountMerchantContribute_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(30, discountMerchantContribute_);
    }
    if (discountPlatformContribute_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(31, discountPlatformContribute_);
    }
    if (discountBuyerContribute_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(32, discountBuyerContribute_);
    }
    if (discountOtherContribute_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(33, discountOtherContribute_);
    }
    if (payMerchantContribute_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(34, payMerchantContribute_);
    }
    if (payPlatformContribute_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(35, payPlatformContribute_);
    }
    if (payBuyerContribute_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(36, payBuyerContribute_);
    }
    if (payOtherContribute_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(37, payOtherContribute_);
    }
    if (deliveryFee_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(38, deliveryFee_);
    }
    if (deliveryFeeForPlatform_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(39, deliveryFeeForPlatform_);
    }
    if (deliveryFeeForMerchant_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(40, deliveryFeeForMerchant_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.eticket.Amount)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.eticket.Amount other = (cn.hexcloud.pbis.common.service.integration.eticket.Amount) obj;

    if (java.lang.Double.doubleToLongBits(getTaxAmount())
        != java.lang.Double.doubleToLongBits(
            other.getTaxAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getGrossAmount())
        != java.lang.Double.doubleToLongBits(
            other.getGrossAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getNetAmount())
        != java.lang.Double.doubleToLongBits(
            other.getNetAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getPayAmount())
        != java.lang.Double.doubleToLongBits(
            other.getPayAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getDiscountAmount())
        != java.lang.Double.doubleToLongBits(
            other.getDiscountAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getRemovezeroAmount())
        != java.lang.Double.doubleToLongBits(
            other.getRemovezeroAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getRounding())
        != java.lang.Double.doubleToLongBits(
            other.getRounding())) return false;
    if (java.lang.Double.doubleToLongBits(getOverflowAmount())
        != java.lang.Double.doubleToLongBits(
            other.getOverflowAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getChangeAmount())
        != java.lang.Double.doubleToLongBits(
            other.getChangeAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getServiceFee())
        != java.lang.Double.doubleToLongBits(
            other.getServiceFee())) return false;
    if (java.lang.Double.doubleToLongBits(getTip())
        != java.lang.Double.doubleToLongBits(
            other.getTip())) return false;
    if (java.lang.Double.doubleToLongBits(getCommission())
        != java.lang.Double.doubleToLongBits(
            other.getCommission())) return false;
    if (java.lang.Double.doubleToLongBits(getAmount0())
        != java.lang.Double.doubleToLongBits(
            other.getAmount0())) return false;
    if (java.lang.Double.doubleToLongBits(getAmount1())
        != java.lang.Double.doubleToLongBits(
            other.getAmount1())) return false;
    if (java.lang.Double.doubleToLongBits(getAmount2())
        != java.lang.Double.doubleToLongBits(
            other.getAmount2())) return false;
    if (java.lang.Double.doubleToLongBits(getAmount3())
        != java.lang.Double.doubleToLongBits(
            other.getAmount3())) return false;
    if (java.lang.Double.doubleToLongBits(getAmount4())
        != java.lang.Double.doubleToLongBits(
            other.getAmount4())) return false;
    if (getTaxIncluded()
        != other.getTaxIncluded()) return false;
    if (java.lang.Float.floatToIntBits(getOtherFee())
        != java.lang.Float.floatToIntBits(
            other.getOtherFee())) return false;
    if (java.lang.Float.floatToIntBits(getMerchantDiscountAmount())
        != java.lang.Float.floatToIntBits(
            other.getMerchantDiscountAmount())) return false;
    if (java.lang.Float.floatToIntBits(getPlatformDiscountAmount())
        != java.lang.Float.floatToIntBits(
            other.getPlatformDiscountAmount())) return false;
    if (java.lang.Float.floatToIntBits(getProjectedIncome())
        != java.lang.Float.floatToIntBits(
            other.getProjectedIncome())) return false;
    if (java.lang.Double.doubleToLongBits(getReceivable())
        != java.lang.Double.doubleToLongBits(
            other.getReceivable())) return false;
    if (java.lang.Double.doubleToLongBits(getRealAmount())
        != java.lang.Double.doubleToLongBits(
            other.getRealAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getBusinessAmount())
        != java.lang.Double.doubleToLongBits(
            other.getBusinessAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getExpendAmount())
        != java.lang.Double.doubleToLongBits(
            other.getExpendAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getPaymentTransferAmount())
        != java.lang.Double.doubleToLongBits(
            other.getPaymentTransferAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getDiscountTransferAmount())
        != java.lang.Double.doubleToLongBits(
            other.getDiscountTransferAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getStoreDiscountAmount())
        != java.lang.Double.doubleToLongBits(
            other.getStoreDiscountAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getDiscountMerchantContribute())
        != java.lang.Double.doubleToLongBits(
            other.getDiscountMerchantContribute())) return false;
    if (java.lang.Double.doubleToLongBits(getDiscountPlatformContribute())
        != java.lang.Double.doubleToLongBits(
            other.getDiscountPlatformContribute())) return false;
    if (java.lang.Double.doubleToLongBits(getDiscountBuyerContribute())
        != java.lang.Double.doubleToLongBits(
            other.getDiscountBuyerContribute())) return false;
    if (java.lang.Double.doubleToLongBits(getDiscountOtherContribute())
        != java.lang.Double.doubleToLongBits(
            other.getDiscountOtherContribute())) return false;
    if (java.lang.Double.doubleToLongBits(getPayMerchantContribute())
        != java.lang.Double.doubleToLongBits(
            other.getPayMerchantContribute())) return false;
    if (java.lang.Double.doubleToLongBits(getPayPlatformContribute())
        != java.lang.Double.doubleToLongBits(
            other.getPayPlatformContribute())) return false;
    if (java.lang.Double.doubleToLongBits(getPayBuyerContribute())
        != java.lang.Double.doubleToLongBits(
            other.getPayBuyerContribute())) return false;
    if (java.lang.Double.doubleToLongBits(getPayOtherContribute())
        != java.lang.Double.doubleToLongBits(
            other.getPayOtherContribute())) return false;
    if (java.lang.Double.doubleToLongBits(getDeliveryFee())
        != java.lang.Double.doubleToLongBits(
            other.getDeliveryFee())) return false;
    if (java.lang.Double.doubleToLongBits(getDeliveryFeeForPlatform())
        != java.lang.Double.doubleToLongBits(
            other.getDeliveryFeeForPlatform())) return false;
    if (java.lang.Double.doubleToLongBits(getDeliveryFeeForMerchant())
        != java.lang.Double.doubleToLongBits(
            other.getDeliveryFeeForMerchant())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TAXAMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getTaxAmount()));
    hash = (37 * hash) + GROSS_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getGrossAmount()));
    hash = (37 * hash) + NET_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getNetAmount()));
    hash = (37 * hash) + PAY_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPayAmount()));
    hash = (37 * hash) + DISCOUNT_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getDiscountAmount()));
    hash = (37 * hash) + REMOVEZERO_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getRemovezeroAmount()));
    hash = (37 * hash) + ROUNDING_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getRounding()));
    hash = (37 * hash) + OVERFLOW_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getOverflowAmount()));
    hash = (37 * hash) + CHANGEAMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getChangeAmount()));
    hash = (37 * hash) + SERVICEFEE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getServiceFee()));
    hash = (37 * hash) + TIP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getTip()));
    hash = (37 * hash) + COMMISSION_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getCommission()));
    hash = (37 * hash) + AMOUNT_0_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getAmount0()));
    hash = (37 * hash) + AMOUNT_1_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getAmount1()));
    hash = (37 * hash) + AMOUNT_2_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getAmount2()));
    hash = (37 * hash) + AMOUNT_3_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getAmount3()));
    hash = (37 * hash) + AMOUNT_4_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getAmount4()));
    hash = (37 * hash) + TAXINCLUDED_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getTaxIncluded());
    hash = (37 * hash) + OTHERFEE_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getOtherFee());
    hash = (37 * hash) + MERCHANT_DISCOUNT_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getMerchantDiscountAmount());
    hash = (37 * hash) + PLATFORM_DISCOUNT_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getPlatformDiscountAmount());
    hash = (37 * hash) + PROJECTED_INCOME_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getProjectedIncome());
    hash = (37 * hash) + RECEIVABLE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getReceivable()));
    hash = (37 * hash) + REAL_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getRealAmount()));
    hash = (37 * hash) + BUSINESS_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getBusinessAmount()));
    hash = (37 * hash) + EXPEND_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getExpendAmount()));
    hash = (37 * hash) + PAYMENT_TRANSFER_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPaymentTransferAmount()));
    hash = (37 * hash) + DISCOUNT_TRANSFER_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getDiscountTransferAmount()));
    hash = (37 * hash) + STORE_DISCOUNT_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getStoreDiscountAmount()));
    hash = (37 * hash) + DISCOUNT_MERCHANT_CONTRIBUTE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getDiscountMerchantContribute()));
    hash = (37 * hash) + DISCOUNT_PLATFORM_CONTRIBUTE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getDiscountPlatformContribute()));
    hash = (37 * hash) + DISCOUNT_BUYER_CONTRIBUTE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getDiscountBuyerContribute()));
    hash = (37 * hash) + DISCOUNT_OTHER_CONTRIBUTE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getDiscountOtherContribute()));
    hash = (37 * hash) + PAY_MERCHANT_CONTRIBUTE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPayMerchantContribute()));
    hash = (37 * hash) + PAY_PLATFORM_CONTRIBUTE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPayPlatformContribute()));
    hash = (37 * hash) + PAY_BUYER_CONTRIBUTE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPayBuyerContribute()));
    hash = (37 * hash) + PAY_OTHER_CONTRIBUTE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPayOtherContribute()));
    hash = (37 * hash) + DELIVERY_FEE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getDeliveryFee()));
    hash = (37 * hash) + DELIVERY_FEE_FOR_PLATFORM_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getDeliveryFeeForPlatform()));
    hash = (37 * hash) + DELIVERY_FEE_FOR_MERCHANT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getDeliveryFeeForMerchant()));
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.Amount parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Amount parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Amount parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Amount parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Amount parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Amount parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Amount parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Amount parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Amount parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Amount parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Amount parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Amount parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.eticket.Amount prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code eticket_proto.Amount}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:eticket_proto.Amount)
      cn.hexcloud.pbis.common.service.integration.eticket.AmountOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Amount_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Amount_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.eticket.Amount.class, cn.hexcloud.pbis.common.service.integration.eticket.Amount.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.eticket.Amount.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      taxAmount_ = 0D;

      grossAmount_ = 0D;

      netAmount_ = 0D;

      payAmount_ = 0D;

      discountAmount_ = 0D;

      removezeroAmount_ = 0D;

      rounding_ = 0D;

      overflowAmount_ = 0D;

      changeAmount_ = 0D;

      serviceFee_ = 0D;

      tip_ = 0D;

      commission_ = 0D;

      amount0_ = 0D;

      amount1_ = 0D;

      amount2_ = 0D;

      amount3_ = 0D;

      amount4_ = 0D;

      taxIncluded_ = false;

      otherFee_ = 0F;

      merchantDiscountAmount_ = 0F;

      platformDiscountAmount_ = 0F;

      projectedIncome_ = 0F;

      receivable_ = 0D;

      realAmount_ = 0D;

      businessAmount_ = 0D;

      expendAmount_ = 0D;

      paymentTransferAmount_ = 0D;

      discountTransferAmount_ = 0D;

      storeDiscountAmount_ = 0D;

      discountMerchantContribute_ = 0D;

      discountPlatformContribute_ = 0D;

      discountBuyerContribute_ = 0D;

      discountOtherContribute_ = 0D;

      payMerchantContribute_ = 0D;

      payPlatformContribute_ = 0D;

      payBuyerContribute_ = 0D;

      payOtherContribute_ = 0D;

      deliveryFee_ = 0D;

      deliveryFeeForPlatform_ = 0D;

      deliveryFeeForMerchant_ = 0D;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Amount_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Amount getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.Amount.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Amount build() {
      cn.hexcloud.pbis.common.service.integration.eticket.Amount result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Amount buildPartial() {
      cn.hexcloud.pbis.common.service.integration.eticket.Amount result = new cn.hexcloud.pbis.common.service.integration.eticket.Amount(this);
      result.taxAmount_ = taxAmount_;
      result.grossAmount_ = grossAmount_;
      result.netAmount_ = netAmount_;
      result.payAmount_ = payAmount_;
      result.discountAmount_ = discountAmount_;
      result.removezeroAmount_ = removezeroAmount_;
      result.rounding_ = rounding_;
      result.overflowAmount_ = overflowAmount_;
      result.changeAmount_ = changeAmount_;
      result.serviceFee_ = serviceFee_;
      result.tip_ = tip_;
      result.commission_ = commission_;
      result.amount0_ = amount0_;
      result.amount1_ = amount1_;
      result.amount2_ = amount2_;
      result.amount3_ = amount3_;
      result.amount4_ = amount4_;
      result.taxIncluded_ = taxIncluded_;
      result.otherFee_ = otherFee_;
      result.merchantDiscountAmount_ = merchantDiscountAmount_;
      result.platformDiscountAmount_ = platformDiscountAmount_;
      result.projectedIncome_ = projectedIncome_;
      result.receivable_ = receivable_;
      result.realAmount_ = realAmount_;
      result.businessAmount_ = businessAmount_;
      result.expendAmount_ = expendAmount_;
      result.paymentTransferAmount_ = paymentTransferAmount_;
      result.discountTransferAmount_ = discountTransferAmount_;
      result.storeDiscountAmount_ = storeDiscountAmount_;
      result.discountMerchantContribute_ = discountMerchantContribute_;
      result.discountPlatformContribute_ = discountPlatformContribute_;
      result.discountBuyerContribute_ = discountBuyerContribute_;
      result.discountOtherContribute_ = discountOtherContribute_;
      result.payMerchantContribute_ = payMerchantContribute_;
      result.payPlatformContribute_ = payPlatformContribute_;
      result.payBuyerContribute_ = payBuyerContribute_;
      result.payOtherContribute_ = payOtherContribute_;
      result.deliveryFee_ = deliveryFee_;
      result.deliveryFeeForPlatform_ = deliveryFeeForPlatform_;
      result.deliveryFeeForMerchant_ = deliveryFeeForMerchant_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.eticket.Amount) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.eticket.Amount)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.eticket.Amount other) {
      if (other == cn.hexcloud.pbis.common.service.integration.eticket.Amount.getDefaultInstance()) return this;
      if (other.getTaxAmount() != 0D) {
        setTaxAmount(other.getTaxAmount());
      }
      if (other.getGrossAmount() != 0D) {
        setGrossAmount(other.getGrossAmount());
      }
      if (other.getNetAmount() != 0D) {
        setNetAmount(other.getNetAmount());
      }
      if (other.getPayAmount() != 0D) {
        setPayAmount(other.getPayAmount());
      }
      if (other.getDiscountAmount() != 0D) {
        setDiscountAmount(other.getDiscountAmount());
      }
      if (other.getRemovezeroAmount() != 0D) {
        setRemovezeroAmount(other.getRemovezeroAmount());
      }
      if (other.getRounding() != 0D) {
        setRounding(other.getRounding());
      }
      if (other.getOverflowAmount() != 0D) {
        setOverflowAmount(other.getOverflowAmount());
      }
      if (other.getChangeAmount() != 0D) {
        setChangeAmount(other.getChangeAmount());
      }
      if (other.getServiceFee() != 0D) {
        setServiceFee(other.getServiceFee());
      }
      if (other.getTip() != 0D) {
        setTip(other.getTip());
      }
      if (other.getCommission() != 0D) {
        setCommission(other.getCommission());
      }
      if (other.getAmount0() != 0D) {
        setAmount0(other.getAmount0());
      }
      if (other.getAmount1() != 0D) {
        setAmount1(other.getAmount1());
      }
      if (other.getAmount2() != 0D) {
        setAmount2(other.getAmount2());
      }
      if (other.getAmount3() != 0D) {
        setAmount3(other.getAmount3());
      }
      if (other.getAmount4() != 0D) {
        setAmount4(other.getAmount4());
      }
      if (other.getTaxIncluded() != false) {
        setTaxIncluded(other.getTaxIncluded());
      }
      if (other.getOtherFee() != 0F) {
        setOtherFee(other.getOtherFee());
      }
      if (other.getMerchantDiscountAmount() != 0F) {
        setMerchantDiscountAmount(other.getMerchantDiscountAmount());
      }
      if (other.getPlatformDiscountAmount() != 0F) {
        setPlatformDiscountAmount(other.getPlatformDiscountAmount());
      }
      if (other.getProjectedIncome() != 0F) {
        setProjectedIncome(other.getProjectedIncome());
      }
      if (other.getReceivable() != 0D) {
        setReceivable(other.getReceivable());
      }
      if (other.getRealAmount() != 0D) {
        setRealAmount(other.getRealAmount());
      }
      if (other.getBusinessAmount() != 0D) {
        setBusinessAmount(other.getBusinessAmount());
      }
      if (other.getExpendAmount() != 0D) {
        setExpendAmount(other.getExpendAmount());
      }
      if (other.getPaymentTransferAmount() != 0D) {
        setPaymentTransferAmount(other.getPaymentTransferAmount());
      }
      if (other.getDiscountTransferAmount() != 0D) {
        setDiscountTransferAmount(other.getDiscountTransferAmount());
      }
      if (other.getStoreDiscountAmount() != 0D) {
        setStoreDiscountAmount(other.getStoreDiscountAmount());
      }
      if (other.getDiscountMerchantContribute() != 0D) {
        setDiscountMerchantContribute(other.getDiscountMerchantContribute());
      }
      if (other.getDiscountPlatformContribute() != 0D) {
        setDiscountPlatformContribute(other.getDiscountPlatformContribute());
      }
      if (other.getDiscountBuyerContribute() != 0D) {
        setDiscountBuyerContribute(other.getDiscountBuyerContribute());
      }
      if (other.getDiscountOtherContribute() != 0D) {
        setDiscountOtherContribute(other.getDiscountOtherContribute());
      }
      if (other.getPayMerchantContribute() != 0D) {
        setPayMerchantContribute(other.getPayMerchantContribute());
      }
      if (other.getPayPlatformContribute() != 0D) {
        setPayPlatformContribute(other.getPayPlatformContribute());
      }
      if (other.getPayBuyerContribute() != 0D) {
        setPayBuyerContribute(other.getPayBuyerContribute());
      }
      if (other.getPayOtherContribute() != 0D) {
        setPayOtherContribute(other.getPayOtherContribute());
      }
      if (other.getDeliveryFee() != 0D) {
        setDeliveryFee(other.getDeliveryFee());
      }
      if (other.getDeliveryFeeForPlatform() != 0D) {
        setDeliveryFeeForPlatform(other.getDeliveryFeeForPlatform());
      }
      if (other.getDeliveryFeeForMerchant() != 0D) {
        setDeliveryFeeForMerchant(other.getDeliveryFeeForMerchant());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.eticket.Amount parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.eticket.Amount) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private double taxAmount_ ;
    /**
     * <code>double taxAmount = 1;</code>
     * @return The taxAmount.
     */
    @java.lang.Override
    public double getTaxAmount() {
      return taxAmount_;
    }
    /**
     * <code>double taxAmount = 1;</code>
     * @param value The taxAmount to set.
     * @return This builder for chaining.
     */
    public Builder setTaxAmount(double value) {
      
      taxAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double taxAmount = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearTaxAmount() {
      
      taxAmount_ = 0D;
      onChanged();
      return this;
    }

    private double grossAmount_ ;
    /**
     * <code>double gross_amount = 2;</code>
     * @return The grossAmount.
     */
    @java.lang.Override
    public double getGrossAmount() {
      return grossAmount_;
    }
    /**
     * <code>double gross_amount = 2;</code>
     * @param value The grossAmount to set.
     * @return This builder for chaining.
     */
    public Builder setGrossAmount(double value) {
      
      grossAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double gross_amount = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearGrossAmount() {
      
      grossAmount_ = 0D;
      onChanged();
      return this;
    }

    private double netAmount_ ;
    /**
     * <code>double net_amount = 3;</code>
     * @return The netAmount.
     */
    @java.lang.Override
    public double getNetAmount() {
      return netAmount_;
    }
    /**
     * <code>double net_amount = 3;</code>
     * @param value The netAmount to set.
     * @return This builder for chaining.
     */
    public Builder setNetAmount(double value) {
      
      netAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double net_amount = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearNetAmount() {
      
      netAmount_ = 0D;
      onChanged();
      return this;
    }

    private double payAmount_ ;
    /**
     * <code>double pay_amount = 4;</code>
     * @return The payAmount.
     */
    @java.lang.Override
    public double getPayAmount() {
      return payAmount_;
    }
    /**
     * <code>double pay_amount = 4;</code>
     * @param value The payAmount to set.
     * @return This builder for chaining.
     */
    public Builder setPayAmount(double value) {
      
      payAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double pay_amount = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayAmount() {
      
      payAmount_ = 0D;
      onChanged();
      return this;
    }

    private double discountAmount_ ;
    /**
     * <code>double discount_amount = 5;</code>
     * @return The discountAmount.
     */
    @java.lang.Override
    public double getDiscountAmount() {
      return discountAmount_;
    }
    /**
     * <code>double discount_amount = 5;</code>
     * @param value The discountAmount to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountAmount(double value) {
      
      discountAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double discount_amount = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountAmount() {
      
      discountAmount_ = 0D;
      onChanged();
      return this;
    }

    private double removezeroAmount_ ;
    /**
     * <code>double removezero_amount = 6;</code>
     * @return The removezeroAmount.
     */
    @java.lang.Override
    public double getRemovezeroAmount() {
      return removezeroAmount_;
    }
    /**
     * <code>double removezero_amount = 6;</code>
     * @param value The removezeroAmount to set.
     * @return This builder for chaining.
     */
    public Builder setRemovezeroAmount(double value) {
      
      removezeroAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double removezero_amount = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearRemovezeroAmount() {
      
      removezeroAmount_ = 0D;
      onChanged();
      return this;
    }

    private double rounding_ ;
    /**
     * <code>double rounding = 7;</code>
     * @return The rounding.
     */
    @java.lang.Override
    public double getRounding() {
      return rounding_;
    }
    /**
     * <code>double rounding = 7;</code>
     * @param value The rounding to set.
     * @return This builder for chaining.
     */
    public Builder setRounding(double value) {
      
      rounding_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double rounding = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearRounding() {
      
      rounding_ = 0D;
      onChanged();
      return this;
    }

    private double overflowAmount_ ;
    /**
     * <code>double overflow_amount = 8;</code>
     * @return The overflowAmount.
     */
    @java.lang.Override
    public double getOverflowAmount() {
      return overflowAmount_;
    }
    /**
     * <code>double overflow_amount = 8;</code>
     * @param value The overflowAmount to set.
     * @return This builder for chaining.
     */
    public Builder setOverflowAmount(double value) {
      
      overflowAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double overflow_amount = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearOverflowAmount() {
      
      overflowAmount_ = 0D;
      onChanged();
      return this;
    }

    private double changeAmount_ ;
    /**
     * <code>double changeAmount = 9;</code>
     * @return The changeAmount.
     */
    @java.lang.Override
    public double getChangeAmount() {
      return changeAmount_;
    }
    /**
     * <code>double changeAmount = 9;</code>
     * @param value The changeAmount to set.
     * @return This builder for chaining.
     */
    public Builder setChangeAmount(double value) {
      
      changeAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double changeAmount = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearChangeAmount() {
      
      changeAmount_ = 0D;
      onChanged();
      return this;
    }

    private double serviceFee_ ;
    /**
     * <code>double serviceFee = 10;</code>
     * @return The serviceFee.
     */
    @java.lang.Override
    public double getServiceFee() {
      return serviceFee_;
    }
    /**
     * <code>double serviceFee = 10;</code>
     * @param value The serviceFee to set.
     * @return This builder for chaining.
     */
    public Builder setServiceFee(double value) {
      
      serviceFee_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double serviceFee = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearServiceFee() {
      
      serviceFee_ = 0D;
      onChanged();
      return this;
    }

    private double tip_ ;
    /**
     * <code>double tip = 11;</code>
     * @return The tip.
     */
    @java.lang.Override
    public double getTip() {
      return tip_;
    }
    /**
     * <code>double tip = 11;</code>
     * @param value The tip to set.
     * @return This builder for chaining.
     */
    public Builder setTip(double value) {
      
      tip_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double tip = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearTip() {
      
      tip_ = 0D;
      onChanged();
      return this;
    }

    private double commission_ ;
    /**
     * <code>double commission = 12;</code>
     * @return The commission.
     */
    @java.lang.Override
    public double getCommission() {
      return commission_;
    }
    /**
     * <code>double commission = 12;</code>
     * @param value The commission to set.
     * @return This builder for chaining.
     */
    public Builder setCommission(double value) {
      
      commission_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double commission = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearCommission() {
      
      commission_ = 0D;
      onChanged();
      return this;
    }

    private double amount0_ ;
    /**
     * <code>double amount_0 = 13;</code>
     * @return The amount0.
     */
    @java.lang.Override
    public double getAmount0() {
      return amount0_;
    }
    /**
     * <code>double amount_0 = 13;</code>
     * @param value The amount0 to set.
     * @return This builder for chaining.
     */
    public Builder setAmount0(double value) {
      
      amount0_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double amount_0 = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmount0() {
      
      amount0_ = 0D;
      onChanged();
      return this;
    }

    private double amount1_ ;
    /**
     * <code>double amount_1 = 14;</code>
     * @return The amount1.
     */
    @java.lang.Override
    public double getAmount1() {
      return amount1_;
    }
    /**
     * <code>double amount_1 = 14;</code>
     * @param value The amount1 to set.
     * @return This builder for chaining.
     */
    public Builder setAmount1(double value) {
      
      amount1_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double amount_1 = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmount1() {
      
      amount1_ = 0D;
      onChanged();
      return this;
    }

    private double amount2_ ;
    /**
     * <code>double amount_2 = 15;</code>
     * @return The amount2.
     */
    @java.lang.Override
    public double getAmount2() {
      return amount2_;
    }
    /**
     * <code>double amount_2 = 15;</code>
     * @param value The amount2 to set.
     * @return This builder for chaining.
     */
    public Builder setAmount2(double value) {
      
      amount2_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double amount_2 = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmount2() {
      
      amount2_ = 0D;
      onChanged();
      return this;
    }

    private double amount3_ ;
    /**
     * <code>double amount_3 = 16;</code>
     * @return The amount3.
     */
    @java.lang.Override
    public double getAmount3() {
      return amount3_;
    }
    /**
     * <code>double amount_3 = 16;</code>
     * @param value The amount3 to set.
     * @return This builder for chaining.
     */
    public Builder setAmount3(double value) {
      
      amount3_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double amount_3 = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmount3() {
      
      amount3_ = 0D;
      onChanged();
      return this;
    }

    private double amount4_ ;
    /**
     * <code>double amount_4 = 17;</code>
     * @return The amount4.
     */
    @java.lang.Override
    public double getAmount4() {
      return amount4_;
    }
    /**
     * <code>double amount_4 = 17;</code>
     * @param value The amount4 to set.
     * @return This builder for chaining.
     */
    public Builder setAmount4(double value) {
      
      amount4_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double amount_4 = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmount4() {
      
      amount4_ = 0D;
      onChanged();
      return this;
    }

    private boolean taxIncluded_ ;
    /**
     * <pre>
     *价税合一
     * </pre>
     *
     * <code>bool taxIncluded = 18;</code>
     * @return The taxIncluded.
     */
    @java.lang.Override
    public boolean getTaxIncluded() {
      return taxIncluded_;
    }
    /**
     * <pre>
     *价税合一
     * </pre>
     *
     * <code>bool taxIncluded = 18;</code>
     * @param value The taxIncluded to set.
     * @return This builder for chaining.
     */
    public Builder setTaxIncluded(boolean value) {
      
      taxIncluded_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *价税合一
     * </pre>
     *
     * <code>bool taxIncluded = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearTaxIncluded() {
      
      taxIncluded_ = false;
      onChanged();
      return this;
    }

    private float otherFee_ ;
    /**
     * <pre>
     *其他费用
     * </pre>
     *
     * <code>float otherFee = 19;</code>
     * @return The otherFee.
     */
    @java.lang.Override
    public float getOtherFee() {
      return otherFee_;
    }
    /**
     * <pre>
     *其他费用
     * </pre>
     *
     * <code>float otherFee = 19;</code>
     * @param value The otherFee to set.
     * @return This builder for chaining.
     */
    public Builder setOtherFee(float value) {
      
      otherFee_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *其他费用
     * </pre>
     *
     * <code>float otherFee = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearOtherFee() {
      
      otherFee_ = 0F;
      onChanged();
      return this;
    }

    private float merchantDiscountAmount_ ;
    /**
     * <pre>
     *商家优惠承担
     * </pre>
     *
     * <code>float merchant_discount_amount = 20;</code>
     * @return The merchantDiscountAmount.
     */
    @java.lang.Override
    public float getMerchantDiscountAmount() {
      return merchantDiscountAmount_;
    }
    /**
     * <pre>
     *商家优惠承担
     * </pre>
     *
     * <code>float merchant_discount_amount = 20;</code>
     * @param value The merchantDiscountAmount to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantDiscountAmount(float value) {
      
      merchantDiscountAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商家优惠承担
     * </pre>
     *
     * <code>float merchant_discount_amount = 20;</code>
     * @return This builder for chaining.
     */
    public Builder clearMerchantDiscountAmount() {
      
      merchantDiscountAmount_ = 0F;
      onChanged();
      return this;
    }

    private float platformDiscountAmount_ ;
    /**
     * <pre>
     *活动平台优惠承担
     * </pre>
     *
     * <code>float platform_discount_amount = 21;</code>
     * @return The platformDiscountAmount.
     */
    @java.lang.Override
    public float getPlatformDiscountAmount() {
      return platformDiscountAmount_;
    }
    /**
     * <pre>
     *活动平台优惠承担
     * </pre>
     *
     * <code>float platform_discount_amount = 21;</code>
     * @param value The platformDiscountAmount to set.
     * @return This builder for chaining.
     */
    public Builder setPlatformDiscountAmount(float value) {
      
      platformDiscountAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *活动平台优惠承担
     * </pre>
     *
     * <code>float platform_discount_amount = 21;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlatformDiscountAmount() {
      
      platformDiscountAmount_ = 0F;
      onChanged();
      return this;
    }

    private float projectedIncome_ ;
    /**
     * <pre>
     *预计收入
     * </pre>
     *
     * <code>float projected_income = 22;</code>
     * @return The projectedIncome.
     */
    @java.lang.Override
    public float getProjectedIncome() {
      return projectedIncome_;
    }
    /**
     * <pre>
     *预计收入
     * </pre>
     *
     * <code>float projected_income = 22;</code>
     * @param value The projectedIncome to set.
     * @return This builder for chaining.
     */
    public Builder setProjectedIncome(float value) {
      
      projectedIncome_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *预计收入
     * </pre>
     *
     * <code>float projected_income = 22;</code>
     * @return This builder for chaining.
     */
    public Builder clearProjectedIncome() {
      
      projectedIncome_ = 0F;
      onChanged();
      return this;
    }

    private double receivable_ ;
    /**
     * <pre>
     *应付金额/应收金额
     * </pre>
     *
     * <code>double receivable = 23;</code>
     * @return The receivable.
     */
    @java.lang.Override
    public double getReceivable() {
      return receivable_;
    }
    /**
     * <pre>
     *应付金额/应收金额
     * </pre>
     *
     * <code>double receivable = 23;</code>
     * @param value The receivable to set.
     * @return This builder for chaining.
     */
    public Builder setReceivable(double value) {
      
      receivable_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *应付金额/应收金额
     * </pre>
     *
     * <code>double receivable = 23;</code>
     * @return This builder for chaining.
     */
    public Builder clearReceivable() {
      
      receivable_ = 0D;
      onChanged();
      return this;
    }

    private double realAmount_ ;
    /**
     * <code>double real_amount = 24;</code>
     * @return The realAmount.
     */
    @java.lang.Override
    public double getRealAmount() {
      return realAmount_;
    }
    /**
     * <code>double real_amount = 24;</code>
     * @param value The realAmount to set.
     * @return This builder for chaining.
     */
    public Builder setRealAmount(double value) {
      
      realAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double real_amount = 24;</code>
     * @return This builder for chaining.
     */
    public Builder clearRealAmount() {
      
      realAmount_ = 0D;
      onChanged();
      return this;
    }

    private double businessAmount_ ;
    /**
     * <code>double business_amount = 25;</code>
     * @return The businessAmount.
     */
    @java.lang.Override
    public double getBusinessAmount() {
      return businessAmount_;
    }
    /**
     * <code>double business_amount = 25;</code>
     * @param value The businessAmount to set.
     * @return This builder for chaining.
     */
    public Builder setBusinessAmount(double value) {
      
      businessAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double business_amount = 25;</code>
     * @return This builder for chaining.
     */
    public Builder clearBusinessAmount() {
      
      businessAmount_ = 0D;
      onChanged();
      return this;
    }

    private double expendAmount_ ;
    /**
     * <code>double expend_amount = 26;</code>
     * @return The expendAmount.
     */
    @java.lang.Override
    public double getExpendAmount() {
      return expendAmount_;
    }
    /**
     * <code>double expend_amount = 26;</code>
     * @param value The expendAmount to set.
     * @return This builder for chaining.
     */
    public Builder setExpendAmount(double value) {
      
      expendAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double expend_amount = 26;</code>
     * @return This builder for chaining.
     */
    public Builder clearExpendAmount() {
      
      expendAmount_ = 0D;
      onChanged();
      return this;
    }

    private double paymentTransferAmount_ ;
    /**
     * <code>double payment_transfer_amount = 27;</code>
     * @return The paymentTransferAmount.
     */
    @java.lang.Override
    public double getPaymentTransferAmount() {
      return paymentTransferAmount_;
    }
    /**
     * <code>double payment_transfer_amount = 27;</code>
     * @param value The paymentTransferAmount to set.
     * @return This builder for chaining.
     */
    public Builder setPaymentTransferAmount(double value) {
      
      paymentTransferAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double payment_transfer_amount = 27;</code>
     * @return This builder for chaining.
     */
    public Builder clearPaymentTransferAmount() {
      
      paymentTransferAmount_ = 0D;
      onChanged();
      return this;
    }

    private double discountTransferAmount_ ;
    /**
     * <code>double discount_transfer_amount = 28;</code>
     * @return The discountTransferAmount.
     */
    @java.lang.Override
    public double getDiscountTransferAmount() {
      return discountTransferAmount_;
    }
    /**
     * <code>double discount_transfer_amount = 28;</code>
     * @param value The discountTransferAmount to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountTransferAmount(double value) {
      
      discountTransferAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double discount_transfer_amount = 28;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountTransferAmount() {
      
      discountTransferAmount_ = 0D;
      onChanged();
      return this;
    }

    private double storeDiscountAmount_ ;
    /**
     * <code>double store_discount_amount = 29;</code>
     * @return The storeDiscountAmount.
     */
    @java.lang.Override
    public double getStoreDiscountAmount() {
      return storeDiscountAmount_;
    }
    /**
     * <code>double store_discount_amount = 29;</code>
     * @param value The storeDiscountAmount to set.
     * @return This builder for chaining.
     */
    public Builder setStoreDiscountAmount(double value) {
      
      storeDiscountAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double store_discount_amount = 29;</code>
     * @return This builder for chaining.
     */
    public Builder clearStoreDiscountAmount() {
      
      storeDiscountAmount_ = 0D;
      onChanged();
      return this;
    }

    private double discountMerchantContribute_ ;
    /**
     * <code>double discount_merchant_contribute = 30;</code>
     * @return The discountMerchantContribute.
     */
    @java.lang.Override
    public double getDiscountMerchantContribute() {
      return discountMerchantContribute_;
    }
    /**
     * <code>double discount_merchant_contribute = 30;</code>
     * @param value The discountMerchantContribute to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountMerchantContribute(double value) {
      
      discountMerchantContribute_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double discount_merchant_contribute = 30;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountMerchantContribute() {
      
      discountMerchantContribute_ = 0D;
      onChanged();
      return this;
    }

    private double discountPlatformContribute_ ;
    /**
     * <code>double discount_platform_contribute = 31;</code>
     * @return The discountPlatformContribute.
     */
    @java.lang.Override
    public double getDiscountPlatformContribute() {
      return discountPlatformContribute_;
    }
    /**
     * <code>double discount_platform_contribute = 31;</code>
     * @param value The discountPlatformContribute to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountPlatformContribute(double value) {
      
      discountPlatformContribute_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double discount_platform_contribute = 31;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountPlatformContribute() {
      
      discountPlatformContribute_ = 0D;
      onChanged();
      return this;
    }

    private double discountBuyerContribute_ ;
    /**
     * <code>double discount_buyer_contribute = 32;</code>
     * @return The discountBuyerContribute.
     */
    @java.lang.Override
    public double getDiscountBuyerContribute() {
      return discountBuyerContribute_;
    }
    /**
     * <code>double discount_buyer_contribute = 32;</code>
     * @param value The discountBuyerContribute to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountBuyerContribute(double value) {
      
      discountBuyerContribute_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double discount_buyer_contribute = 32;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountBuyerContribute() {
      
      discountBuyerContribute_ = 0D;
      onChanged();
      return this;
    }

    private double discountOtherContribute_ ;
    /**
     * <code>double discount_other_contribute = 33;</code>
     * @return The discountOtherContribute.
     */
    @java.lang.Override
    public double getDiscountOtherContribute() {
      return discountOtherContribute_;
    }
    /**
     * <code>double discount_other_contribute = 33;</code>
     * @param value The discountOtherContribute to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountOtherContribute(double value) {
      
      discountOtherContribute_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double discount_other_contribute = 33;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountOtherContribute() {
      
      discountOtherContribute_ = 0D;
      onChanged();
      return this;
    }

    private double payMerchantContribute_ ;
    /**
     * <code>double pay_merchant_contribute = 34;</code>
     * @return The payMerchantContribute.
     */
    @java.lang.Override
    public double getPayMerchantContribute() {
      return payMerchantContribute_;
    }
    /**
     * <code>double pay_merchant_contribute = 34;</code>
     * @param value The payMerchantContribute to set.
     * @return This builder for chaining.
     */
    public Builder setPayMerchantContribute(double value) {
      
      payMerchantContribute_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double pay_merchant_contribute = 34;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayMerchantContribute() {
      
      payMerchantContribute_ = 0D;
      onChanged();
      return this;
    }

    private double payPlatformContribute_ ;
    /**
     * <code>double pay_platform_contribute = 35;</code>
     * @return The payPlatformContribute.
     */
    @java.lang.Override
    public double getPayPlatformContribute() {
      return payPlatformContribute_;
    }
    /**
     * <code>double pay_platform_contribute = 35;</code>
     * @param value The payPlatformContribute to set.
     * @return This builder for chaining.
     */
    public Builder setPayPlatformContribute(double value) {
      
      payPlatformContribute_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double pay_platform_contribute = 35;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayPlatformContribute() {
      
      payPlatformContribute_ = 0D;
      onChanged();
      return this;
    }

    private double payBuyerContribute_ ;
    /**
     * <code>double pay_buyer_contribute = 36;</code>
     * @return The payBuyerContribute.
     */
    @java.lang.Override
    public double getPayBuyerContribute() {
      return payBuyerContribute_;
    }
    /**
     * <code>double pay_buyer_contribute = 36;</code>
     * @param value The payBuyerContribute to set.
     * @return This builder for chaining.
     */
    public Builder setPayBuyerContribute(double value) {
      
      payBuyerContribute_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double pay_buyer_contribute = 36;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayBuyerContribute() {
      
      payBuyerContribute_ = 0D;
      onChanged();
      return this;
    }

    private double payOtherContribute_ ;
    /**
     * <code>double pay_other_contribute = 37;</code>
     * @return The payOtherContribute.
     */
    @java.lang.Override
    public double getPayOtherContribute() {
      return payOtherContribute_;
    }
    /**
     * <code>double pay_other_contribute = 37;</code>
     * @param value The payOtherContribute to set.
     * @return This builder for chaining.
     */
    public Builder setPayOtherContribute(double value) {
      
      payOtherContribute_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double pay_other_contribute = 37;</code>
     * @return This builder for chaining.
     */
    public Builder clearPayOtherContribute() {
      
      payOtherContribute_ = 0D;
      onChanged();
      return this;
    }

    private double deliveryFee_ ;
    /**
     * <code>double delivery_fee = 38;</code>
     * @return The deliveryFee.
     */
    @java.lang.Override
    public double getDeliveryFee() {
      return deliveryFee_;
    }
    /**
     * <code>double delivery_fee = 38;</code>
     * @param value The deliveryFee to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryFee(double value) {
      
      deliveryFee_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double delivery_fee = 38;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeliveryFee() {
      
      deliveryFee_ = 0D;
      onChanged();
      return this;
    }

    private double deliveryFeeForPlatform_ ;
    /**
     * <code>double delivery_fee_for_platform = 39;</code>
     * @return The deliveryFeeForPlatform.
     */
    @java.lang.Override
    public double getDeliveryFeeForPlatform() {
      return deliveryFeeForPlatform_;
    }
    /**
     * <code>double delivery_fee_for_platform = 39;</code>
     * @param value The deliveryFeeForPlatform to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryFeeForPlatform(double value) {
      
      deliveryFeeForPlatform_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double delivery_fee_for_platform = 39;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeliveryFeeForPlatform() {
      
      deliveryFeeForPlatform_ = 0D;
      onChanged();
      return this;
    }

    private double deliveryFeeForMerchant_ ;
    /**
     * <code>double delivery_fee_for_merchant = 40;</code>
     * @return The deliveryFeeForMerchant.
     */
    @java.lang.Override
    public double getDeliveryFeeForMerchant() {
      return deliveryFeeForMerchant_;
    }
    /**
     * <code>double delivery_fee_for_merchant = 40;</code>
     * @param value The deliveryFeeForMerchant to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryFeeForMerchant(double value) {
      
      deliveryFeeForMerchant_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double delivery_fee_for_merchant = 40;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeliveryFeeForMerchant() {
      
      deliveryFeeForMerchant_ = 0D;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:eticket_proto.Amount)
  }

  // @@protoc_insertion_point(class_scope:eticket_proto.Amount)
  private static final cn.hexcloud.pbis.common.service.integration.eticket.Amount DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.eticket.Amount();
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.Amount getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Amount>
      PARSER = new com.google.protobuf.AbstractParser<Amount>() {
    @java.lang.Override
    public Amount parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Amount(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Amount> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Amount> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.Amount getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

