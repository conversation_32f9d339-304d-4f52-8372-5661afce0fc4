// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

public interface ChannelOrBuilder extends
    // @@protoc_insertion_point(interface_extends:eticket_proto.Channel)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string source = 1;</code>
   * @return The source.
   */
  java.lang.String getSource();
  /**
   * <code>string source = 1;</code>
   * @return The bytes for source.
   */
  com.google.protobuf.ByteString
      getSourceBytes();

  /**
   * <code>string deviceType = 2;</code>
   * @return The deviceType.
   */
  java.lang.String getDeviceType();
  /**
   * <code>string deviceType = 2;</code>
   * @return The bytes for deviceType.
   */
  com.google.protobuf.ByteString
      getDeviceTypeBytes();

  /**
   * <code>string orderType = 3;</code>
   * @return The orderType.
   */
  java.lang.String getOrderType();
  /**
   * <code>string orderType = 3;</code>
   * @return The bytes for orderType.
   */
  com.google.protobuf.ByteString
      getOrderTypeBytes();

  /**
   * <code>string deliveryType = 4;</code>
   * @return The deliveryType.
   */
  java.lang.String getDeliveryType();
  /**
   * <code>string deliveryType = 4;</code>
   * @return The bytes for deliveryType.
   */
  com.google.protobuf.ByteString
      getDeliveryTypeBytes();

  /**
   * <code>string tpName = 5;</code>
   * @return The tpName.
   */
  java.lang.String getTpName();
  /**
   * <code>string tpName = 5;</code>
   * @return The bytes for tpName.
   */
  com.google.protobuf.ByteString
      getTpNameBytes();

  /**
   * <pre>
   *渠道编码
   * </pre>
   *
   * <code>string code = 6;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <pre>
   *渠道编码
   * </pre>
   *
   * <code>string code = 6;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <pre>
   *渠道id
   * </pre>
   *
   * <code>string id = 7;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   *渠道id
   * </pre>
   *
   * <code>string id = 7;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   *第三方编码，内部有标准定义
   * </pre>
   *
   * <code>string mapping_code = 8;</code>
   * @return The mappingCode.
   */
  java.lang.String getMappingCode();
  /**
   * <pre>
   *第三方编码，内部有标准定义
   * </pre>
   *
   * <code>string mapping_code = 8;</code>
   * @return The bytes for mappingCode.
   */
  com.google.protobuf.ByteString
      getMappingCodeBytes();
}
