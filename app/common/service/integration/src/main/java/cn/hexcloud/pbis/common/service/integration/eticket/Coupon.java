// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

/**
 * Protobuf type {@code eticket_proto.Coupon}
 */
public final class Coupon extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:eticket_proto.Coupon)
    CouponOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Coupon.newBuilder() to construct.
  private Coupon(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Coupon() {
    id_ = "";
    name_ = "";
    code_ = "";
    sequenceId_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Coupon();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Coupon(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            isOnline_ = input.readBool();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            id_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            code_ = s;
            break;
          }
          case 40: {

            type_ = input.readInt64();
            break;
          }
          case 49: {

            parValue_ = input.readDouble();
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            sequenceId_ = s;
            break;
          }
          case 69: {

            price_ = input.readFloat();
            break;
          }
          case 77: {

            cost_ = input.readFloat();
            break;
          }
          case 85: {

            tpAllowance_ = input.readFloat();
            break;
          }
          case 93: {

            merchantAllowance_ = input.readFloat();
            break;
          }
          case 96: {

            hasInvoiced_ = input.readBool();
            break;
          }
          case 105: {

            transferAmount_ = input.readDouble();
            break;
          }
          case 113: {

            platformAllowance_ = input.readDouble();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Coupon_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Coupon_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.eticket.Coupon.class, cn.hexcloud.pbis.common.service.integration.eticket.Coupon.Builder.class);
  }

  public static final int IS_ONLINE_FIELD_NUMBER = 1;
  private boolean isOnline_;
  /**
   * <code>bool is_online = 1;</code>
   * @return The isOnline.
   */
  @java.lang.Override
  public boolean getIsOnline() {
    return isOnline_;
  }

  public static final int ID_FIELD_NUMBER = 2;
  private volatile java.lang.Object id_;
  /**
   * <code>string id = 2;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <code>string id = 2;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 3;
  private volatile java.lang.Object name_;
  /**
   * <code>string name = 3;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <code>string name = 3;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CODE_FIELD_NUMBER = 4;
  private volatile java.lang.Object code_;
  /**
   * <code>string code = 4;</code>
   * @return The code.
   */
  @java.lang.Override
  public java.lang.String getCode() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      code_ = s;
      return s;
    }
  }
  /**
   * <code>string code = 4;</code>
   * @return The bytes for code.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCodeBytes() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      code_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TYPE_FIELD_NUMBER = 5;
  private long type_;
  /**
   * <code>int64 type = 5;</code>
   * @return The type.
   */
  @java.lang.Override
  public long getType() {
    return type_;
  }

  public static final int PAR_VALUE_FIELD_NUMBER = 6;
  private double parValue_;
  /**
   * <code>double par_value = 6;</code>
   * @return The parValue.
   */
  @java.lang.Override
  public double getParValue() {
    return parValue_;
  }

  public static final int SEQUENCE_ID_FIELD_NUMBER = 7;
  private volatile java.lang.Object sequenceId_;
  /**
   * <code>string sequence_id = 7;</code>
   * @return The sequenceId.
   */
  @java.lang.Override
  public java.lang.String getSequenceId() {
    java.lang.Object ref = sequenceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      sequenceId_ = s;
      return s;
    }
  }
  /**
   * <code>string sequence_id = 7;</code>
   * @return The bytes for sequenceId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSequenceIdBytes() {
    java.lang.Object ref = sequenceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      sequenceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PRICE_FIELD_NUMBER = 8;
  private float price_;
  /**
   * <pre>
   *售价
   * </pre>
   *
   * <code>float price = 8;</code>
   * @return The price.
   */
  @java.lang.Override
  public float getPrice() {
    return price_;
  }

  public static final int COST_FIELD_NUMBER = 9;
  private float cost_;
  /**
   * <pre>
   *用户实际购买金额
   * </pre>
   *
   * <code>float cost = 9;</code>
   * @return The cost.
   */
  @java.lang.Override
  public float getCost() {
    return cost_;
  }

  public static final int TP_ALLOWANCE_FIELD_NUMBER = 10;
  private float tpAllowance_;
  /**
   * <pre>
   *第三方补贴金额
   * </pre>
   *
   * <code>float tp_allowance = 10;</code>
   * @return The tpAllowance.
   */
  @java.lang.Override
  public float getTpAllowance() {
    return tpAllowance_;
  }

  public static final int MERCHANT_ALLOWANCE_FIELD_NUMBER = 11;
  private float merchantAllowance_;
  /**
   * <pre>
   *商家补贴金额
   * </pre>
   *
   * <code>float merchant_allowance = 11;</code>
   * @return The merchantAllowance.
   */
  @java.lang.Override
  public float getMerchantAllowance() {
    return merchantAllowance_;
  }

  public static final int HAS_INVOICED_FIELD_NUMBER = 12;
  private boolean hasInvoiced_;
  /**
   * <pre>
   *已开发票
   * </pre>
   *
   * <code>bool has_invoiced = 12;</code>
   * @return The hasInvoiced.
   */
  @java.lang.Override
  public boolean getHasInvoiced() {
    return hasInvoiced_;
  }

  public static final int TRANSFER_AMOUNT_FIELD_NUMBER = 13;
  private double transferAmount_;
  /**
   * <pre>
   *折扣转支付金额
   * </pre>
   *
   * <code>double transfer_amount = 13;</code>
   * @return The transferAmount.
   */
  @java.lang.Override
  public double getTransferAmount() {
    return transferAmount_;
  }

  public static final int PLATFORM_ALLOWANCE_FIELD_NUMBER = 14;
  private double platformAllowance_;
  /**
   * <code>double platform_allowance = 14;</code>
   * @return The platformAllowance.
   */
  @java.lang.Override
  public double getPlatformAllowance() {
    return platformAllowance_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (isOnline_ != false) {
      output.writeBool(1, isOnline_);
    }
    if (!getIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, id_);
    }
    if (!getNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, name_);
    }
    if (!getCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, code_);
    }
    if (type_ != 0L) {
      output.writeInt64(5, type_);
    }
    if (parValue_ != 0D) {
      output.writeDouble(6, parValue_);
    }
    if (!getSequenceIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, sequenceId_);
    }
    if (price_ != 0F) {
      output.writeFloat(8, price_);
    }
    if (cost_ != 0F) {
      output.writeFloat(9, cost_);
    }
    if (tpAllowance_ != 0F) {
      output.writeFloat(10, tpAllowance_);
    }
    if (merchantAllowance_ != 0F) {
      output.writeFloat(11, merchantAllowance_);
    }
    if (hasInvoiced_ != false) {
      output.writeBool(12, hasInvoiced_);
    }
    if (transferAmount_ != 0D) {
      output.writeDouble(13, transferAmount_);
    }
    if (platformAllowance_ != 0D) {
      output.writeDouble(14, platformAllowance_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (isOnline_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(1, isOnline_);
    }
    if (!getIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, id_);
    }
    if (!getNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, name_);
    }
    if (!getCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, code_);
    }
    if (type_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, type_);
    }
    if (parValue_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(6, parValue_);
    }
    if (!getSequenceIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, sequenceId_);
    }
    if (price_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(8, price_);
    }
    if (cost_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(9, cost_);
    }
    if (tpAllowance_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(10, tpAllowance_);
    }
    if (merchantAllowance_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(11, merchantAllowance_);
    }
    if (hasInvoiced_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(12, hasInvoiced_);
    }
    if (transferAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(13, transferAmount_);
    }
    if (platformAllowance_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(14, platformAllowance_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.eticket.Coupon)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.eticket.Coupon other = (cn.hexcloud.pbis.common.service.integration.eticket.Coupon) obj;

    if (getIsOnline()
        != other.getIsOnline()) return false;
    if (!getId()
        .equals(other.getId())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (!getCode()
        .equals(other.getCode())) return false;
    if (getType()
        != other.getType()) return false;
    if (java.lang.Double.doubleToLongBits(getParValue())
        != java.lang.Double.doubleToLongBits(
            other.getParValue())) return false;
    if (!getSequenceId()
        .equals(other.getSequenceId())) return false;
    if (java.lang.Float.floatToIntBits(getPrice())
        != java.lang.Float.floatToIntBits(
            other.getPrice())) return false;
    if (java.lang.Float.floatToIntBits(getCost())
        != java.lang.Float.floatToIntBits(
            other.getCost())) return false;
    if (java.lang.Float.floatToIntBits(getTpAllowance())
        != java.lang.Float.floatToIntBits(
            other.getTpAllowance())) return false;
    if (java.lang.Float.floatToIntBits(getMerchantAllowance())
        != java.lang.Float.floatToIntBits(
            other.getMerchantAllowance())) return false;
    if (getHasInvoiced()
        != other.getHasInvoiced()) return false;
    if (java.lang.Double.doubleToLongBits(getTransferAmount())
        != java.lang.Double.doubleToLongBits(
            other.getTransferAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getPlatformAllowance())
        != java.lang.Double.doubleToLongBits(
            other.getPlatformAllowance())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + IS_ONLINE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIsOnline());
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCode().hashCode();
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getType());
    hash = (37 * hash) + PAR_VALUE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getParValue()));
    hash = (37 * hash) + SEQUENCE_ID_FIELD_NUMBER;
    hash = (53 * hash) + getSequenceId().hashCode();
    hash = (37 * hash) + PRICE_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getPrice());
    hash = (37 * hash) + COST_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getCost());
    hash = (37 * hash) + TP_ALLOWANCE_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getTpAllowance());
    hash = (37 * hash) + MERCHANT_ALLOWANCE_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getMerchantAllowance());
    hash = (37 * hash) + HAS_INVOICED_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getHasInvoiced());
    hash = (37 * hash) + TRANSFER_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getTransferAmount()));
    hash = (37 * hash) + PLATFORM_ALLOWANCE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPlatformAllowance()));
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.Coupon parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Coupon parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Coupon parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Coupon parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Coupon parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Coupon parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Coupon parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Coupon parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Coupon parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Coupon parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Coupon parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Coupon parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.eticket.Coupon prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code eticket_proto.Coupon}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:eticket_proto.Coupon)
      cn.hexcloud.pbis.common.service.integration.eticket.CouponOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Coupon_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Coupon_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.eticket.Coupon.class, cn.hexcloud.pbis.common.service.integration.eticket.Coupon.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.eticket.Coupon.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      isOnline_ = false;

      id_ = "";

      name_ = "";

      code_ = "";

      type_ = 0L;

      parValue_ = 0D;

      sequenceId_ = "";

      price_ = 0F;

      cost_ = 0F;

      tpAllowance_ = 0F;

      merchantAllowance_ = 0F;

      hasInvoiced_ = false;

      transferAmount_ = 0D;

      platformAllowance_ = 0D;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Coupon_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Coupon getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.Coupon.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Coupon build() {
      cn.hexcloud.pbis.common.service.integration.eticket.Coupon result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Coupon buildPartial() {
      cn.hexcloud.pbis.common.service.integration.eticket.Coupon result = new cn.hexcloud.pbis.common.service.integration.eticket.Coupon(this);
      result.isOnline_ = isOnline_;
      result.id_ = id_;
      result.name_ = name_;
      result.code_ = code_;
      result.type_ = type_;
      result.parValue_ = parValue_;
      result.sequenceId_ = sequenceId_;
      result.price_ = price_;
      result.cost_ = cost_;
      result.tpAllowance_ = tpAllowance_;
      result.merchantAllowance_ = merchantAllowance_;
      result.hasInvoiced_ = hasInvoiced_;
      result.transferAmount_ = transferAmount_;
      result.platformAllowance_ = platformAllowance_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.eticket.Coupon) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.eticket.Coupon)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.eticket.Coupon other) {
      if (other == cn.hexcloud.pbis.common.service.integration.eticket.Coupon.getDefaultInstance()) return this;
      if (other.getIsOnline() != false) {
        setIsOnline(other.getIsOnline());
      }
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (!other.getCode().isEmpty()) {
        code_ = other.code_;
        onChanged();
      }
      if (other.getType() != 0L) {
        setType(other.getType());
      }
      if (other.getParValue() != 0D) {
        setParValue(other.getParValue());
      }
      if (!other.getSequenceId().isEmpty()) {
        sequenceId_ = other.sequenceId_;
        onChanged();
      }
      if (other.getPrice() != 0F) {
        setPrice(other.getPrice());
      }
      if (other.getCost() != 0F) {
        setCost(other.getCost());
      }
      if (other.getTpAllowance() != 0F) {
        setTpAllowance(other.getTpAllowance());
      }
      if (other.getMerchantAllowance() != 0F) {
        setMerchantAllowance(other.getMerchantAllowance());
      }
      if (other.getHasInvoiced() != false) {
        setHasInvoiced(other.getHasInvoiced());
      }
      if (other.getTransferAmount() != 0D) {
        setTransferAmount(other.getTransferAmount());
      }
      if (other.getPlatformAllowance() != 0D) {
        setPlatformAllowance(other.getPlatformAllowance());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.eticket.Coupon parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.eticket.Coupon) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private boolean isOnline_ ;
    /**
     * <code>bool is_online = 1;</code>
     * @return The isOnline.
     */
    @java.lang.Override
    public boolean getIsOnline() {
      return isOnline_;
    }
    /**
     * <code>bool is_online = 1;</code>
     * @param value The isOnline to set.
     * @return This builder for chaining.
     */
    public Builder setIsOnline(boolean value) {
      
      isOnline_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>bool is_online = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsOnline() {
      
      isOnline_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object id_ = "";
    /**
     * <code>string id = 2;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string id = 2;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string id = 2;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      
      id_ = getDefaultInstance().getId();
      onChanged();
      return this;
    }
    /**
     * <code>string id = 2;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      id_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <code>string name = 3;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string name = 3;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string name = 3;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string name = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <code>string name = 3;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object code_ = "";
    /**
     * <code>string code = 4;</code>
     * @return The code.
     */
    public java.lang.String getCode() {
      java.lang.Object ref = code_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        code_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string code = 4;</code>
     * @return The bytes for code.
     */
    public com.google.protobuf.ByteString
        getCodeBytes() {
      java.lang.Object ref = code_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        code_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string code = 4;</code>
     * @param value The code to set.
     * @return This builder for chaining.
     */
    public Builder setCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      code_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string code = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearCode() {
      
      code_ = getDefaultInstance().getCode();
      onChanged();
      return this;
    }
    /**
     * <code>string code = 4;</code>
     * @param value The bytes for code to set.
     * @return This builder for chaining.
     */
    public Builder setCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      code_ = value;
      onChanged();
      return this;
    }

    private long type_ ;
    /**
     * <code>int64 type = 5;</code>
     * @return The type.
     */
    @java.lang.Override
    public long getType() {
      return type_;
    }
    /**
     * <code>int64 type = 5;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(long value) {
      
      type_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>int64 type = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      
      type_ = 0L;
      onChanged();
      return this;
    }

    private double parValue_ ;
    /**
     * <code>double par_value = 6;</code>
     * @return The parValue.
     */
    @java.lang.Override
    public double getParValue() {
      return parValue_;
    }
    /**
     * <code>double par_value = 6;</code>
     * @param value The parValue to set.
     * @return This builder for chaining.
     */
    public Builder setParValue(double value) {
      
      parValue_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double par_value = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearParValue() {
      
      parValue_ = 0D;
      onChanged();
      return this;
    }

    private java.lang.Object sequenceId_ = "";
    /**
     * <code>string sequence_id = 7;</code>
     * @return The sequenceId.
     */
    public java.lang.String getSequenceId() {
      java.lang.Object ref = sequenceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sequenceId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string sequence_id = 7;</code>
     * @return The bytes for sequenceId.
     */
    public com.google.protobuf.ByteString
        getSequenceIdBytes() {
      java.lang.Object ref = sequenceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sequenceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string sequence_id = 7;</code>
     * @param value The sequenceId to set.
     * @return This builder for chaining.
     */
    public Builder setSequenceId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      sequenceId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string sequence_id = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearSequenceId() {
      
      sequenceId_ = getDefaultInstance().getSequenceId();
      onChanged();
      return this;
    }
    /**
     * <code>string sequence_id = 7;</code>
     * @param value The bytes for sequenceId to set.
     * @return This builder for chaining.
     */
    public Builder setSequenceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      sequenceId_ = value;
      onChanged();
      return this;
    }

    private float price_ ;
    /**
     * <pre>
     *售价
     * </pre>
     *
     * <code>float price = 8;</code>
     * @return The price.
     */
    @java.lang.Override
    public float getPrice() {
      return price_;
    }
    /**
     * <pre>
     *售价
     * </pre>
     *
     * <code>float price = 8;</code>
     * @param value The price to set.
     * @return This builder for chaining.
     */
    public Builder setPrice(float value) {
      
      price_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *售价
     * </pre>
     *
     * <code>float price = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrice() {
      
      price_ = 0F;
      onChanged();
      return this;
    }

    private float cost_ ;
    /**
     * <pre>
     *用户实际购买金额
     * </pre>
     *
     * <code>float cost = 9;</code>
     * @return The cost.
     */
    @java.lang.Override
    public float getCost() {
      return cost_;
    }
    /**
     * <pre>
     *用户实际购买金额
     * </pre>
     *
     * <code>float cost = 9;</code>
     * @param value The cost to set.
     * @return This builder for chaining.
     */
    public Builder setCost(float value) {
      
      cost_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *用户实际购买金额
     * </pre>
     *
     * <code>float cost = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearCost() {
      
      cost_ = 0F;
      onChanged();
      return this;
    }

    private float tpAllowance_ ;
    /**
     * <pre>
     *第三方补贴金额
     * </pre>
     *
     * <code>float tp_allowance = 10;</code>
     * @return The tpAllowance.
     */
    @java.lang.Override
    public float getTpAllowance() {
      return tpAllowance_;
    }
    /**
     * <pre>
     *第三方补贴金额
     * </pre>
     *
     * <code>float tp_allowance = 10;</code>
     * @param value The tpAllowance to set.
     * @return This builder for chaining.
     */
    public Builder setTpAllowance(float value) {
      
      tpAllowance_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *第三方补贴金额
     * </pre>
     *
     * <code>float tp_allowance = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearTpAllowance() {
      
      tpAllowance_ = 0F;
      onChanged();
      return this;
    }

    private float merchantAllowance_ ;
    /**
     * <pre>
     *商家补贴金额
     * </pre>
     *
     * <code>float merchant_allowance = 11;</code>
     * @return The merchantAllowance.
     */
    @java.lang.Override
    public float getMerchantAllowance() {
      return merchantAllowance_;
    }
    /**
     * <pre>
     *商家补贴金额
     * </pre>
     *
     * <code>float merchant_allowance = 11;</code>
     * @param value The merchantAllowance to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantAllowance(float value) {
      
      merchantAllowance_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商家补贴金额
     * </pre>
     *
     * <code>float merchant_allowance = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearMerchantAllowance() {
      
      merchantAllowance_ = 0F;
      onChanged();
      return this;
    }

    private boolean hasInvoiced_ ;
    /**
     * <pre>
     *已开发票
     * </pre>
     *
     * <code>bool has_invoiced = 12;</code>
     * @return The hasInvoiced.
     */
    @java.lang.Override
    public boolean getHasInvoiced() {
      return hasInvoiced_;
    }
    /**
     * <pre>
     *已开发票
     * </pre>
     *
     * <code>bool has_invoiced = 12;</code>
     * @param value The hasInvoiced to set.
     * @return This builder for chaining.
     */
    public Builder setHasInvoiced(boolean value) {
      
      hasInvoiced_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *已开发票
     * </pre>
     *
     * <code>bool has_invoiced = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearHasInvoiced() {
      
      hasInvoiced_ = false;
      onChanged();
      return this;
    }

    private double transferAmount_ ;
    /**
     * <pre>
     *折扣转支付金额
     * </pre>
     *
     * <code>double transfer_amount = 13;</code>
     * @return The transferAmount.
     */
    @java.lang.Override
    public double getTransferAmount() {
      return transferAmount_;
    }
    /**
     * <pre>
     *折扣转支付金额
     * </pre>
     *
     * <code>double transfer_amount = 13;</code>
     * @param value The transferAmount to set.
     * @return This builder for chaining.
     */
    public Builder setTransferAmount(double value) {
      
      transferAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *折扣转支付金额
     * </pre>
     *
     * <code>double transfer_amount = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearTransferAmount() {
      
      transferAmount_ = 0D;
      onChanged();
      return this;
    }

    private double platformAllowance_ ;
    /**
     * <code>double platform_allowance = 14;</code>
     * @return The platformAllowance.
     */
    @java.lang.Override
    public double getPlatformAllowance() {
      return platformAllowance_;
    }
    /**
     * <code>double platform_allowance = 14;</code>
     * @param value The platformAllowance to set.
     * @return This builder for chaining.
     */
    public Builder setPlatformAllowance(double value) {
      
      platformAllowance_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double platform_allowance = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlatformAllowance() {
      
      platformAllowance_ = 0D;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:eticket_proto.Coupon)
  }

  // @@protoc_insertion_point(class_scope:eticket_proto.Coupon)
  private static final cn.hexcloud.pbis.common.service.integration.eticket.Coupon DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.eticket.Coupon();
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.Coupon getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Coupon>
      PARSER = new com.google.protobuf.AbstractParser<Coupon>() {
    @java.lang.Override
    public Coupon parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Coupon(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Coupon> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Coupon> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.Coupon getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

