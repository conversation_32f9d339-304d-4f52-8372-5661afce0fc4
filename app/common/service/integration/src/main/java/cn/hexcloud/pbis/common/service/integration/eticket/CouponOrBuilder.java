// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

public interface CouponOrBuilder extends
    // @@protoc_insertion_point(interface_extends:eticket_proto.Coupon)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool is_online = 1;</code>
   * @return The isOnline.
   */
  boolean getIsOnline();

  /**
   * <code>string id = 2;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <code>string id = 2;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <code>string name = 3;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <code>string name = 3;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <code>string code = 4;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <code>string code = 4;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <code>int64 type = 5;</code>
   * @return The type.
   */
  long getType();

  /**
   * <code>double par_value = 6;</code>
   * @return The parValue.
   */
  double getParValue();

  /**
   * <code>string sequence_id = 7;</code>
   * @return The sequenceId.
   */
  java.lang.String getSequenceId();
  /**
   * <code>string sequence_id = 7;</code>
   * @return The bytes for sequenceId.
   */
  com.google.protobuf.ByteString
      getSequenceIdBytes();

  /**
   * <pre>
   *售价
   * </pre>
   *
   * <code>float price = 8;</code>
   * @return The price.
   */
  float getPrice();

  /**
   * <pre>
   *用户实际购买金额
   * </pre>
   *
   * <code>float cost = 9;</code>
   * @return The cost.
   */
  float getCost();

  /**
   * <pre>
   *第三方补贴金额
   * </pre>
   *
   * <code>float tp_allowance = 10;</code>
   * @return The tpAllowance.
   */
  float getTpAllowance();

  /**
   * <pre>
   *商家补贴金额
   * </pre>
   *
   * <code>float merchant_allowance = 11;</code>
   * @return The merchantAllowance.
   */
  float getMerchantAllowance();

  /**
   * <pre>
   *已开发票
   * </pre>
   *
   * <code>bool has_invoiced = 12;</code>
   * @return The hasInvoiced.
   */
  boolean getHasInvoiced();

  /**
   * <pre>
   *折扣转支付金额
   * </pre>
   *
   * <code>double transfer_amount = 13;</code>
   * @return The transferAmount.
   */
  double getTransferAmount();

  /**
   * <code>double platform_allowance = 14;</code>
   * @return The platformAllowance.
   */
  double getPlatformAllowance();
}
