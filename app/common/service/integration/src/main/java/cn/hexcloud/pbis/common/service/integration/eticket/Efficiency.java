// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

/**
 * Protobuf type {@code eticket_proto.Efficiency}
 */
public final class Efficiency extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:eticket_proto.Efficiency)
    EfficiencyOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Efficiency.newBuilder() to construct.
  private Efficiency(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Efficiency() {
    confirmedTime_ = "";
    madeTime_ = "";
    assignedTime_ = "";
    arrivedTime_ = "";
    fetchedTime_ = "";
    deliveredTime_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Efficiency();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Efficiency(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            confirmedTime_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            madeTime_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            assignedTime_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            arrivedTime_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            fetchedTime_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            deliveredTime_ = s;
            break;
          }
          case 61: {

            makeSpan_ = input.readFloat();
            break;
          }
          case 69: {

            avgMakeSpan_ = input.readFloat();
            break;
          }
          case 77: {

            arriveSpan_ = input.readFloat();
            break;
          }
          case 85: {

            deliverSpan_ = input.readFloat();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Efficiency_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Efficiency_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.eticket.Efficiency.class, cn.hexcloud.pbis.common.service.integration.eticket.Efficiency.Builder.class);
  }

  public static final int CONFIRMED_TIME_FIELD_NUMBER = 1;
  private volatile java.lang.Object confirmedTime_;
  /**
   * <pre>
   *订单确认时间
   * </pre>
   *
   * <code>string confirmed_time = 1;</code>
   * @return The confirmedTime.
   */
  @java.lang.Override
  public java.lang.String getConfirmedTime() {
    java.lang.Object ref = confirmedTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      confirmedTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *订单确认时间
   * </pre>
   *
   * <code>string confirmed_time = 1;</code>
   * @return The bytes for confirmedTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getConfirmedTimeBytes() {
    java.lang.Object ref = confirmedTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      confirmedTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MADE_TIME_FIELD_NUMBER = 2;
  private volatile java.lang.Object madeTime_;
  /**
   * <pre>
   *制作完成时间
   * </pre>
   *
   * <code>string made_time = 2;</code>
   * @return The madeTime.
   */
  @java.lang.Override
  public java.lang.String getMadeTime() {
    java.lang.Object ref = madeTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      madeTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *制作完成时间
   * </pre>
   *
   * <code>string made_time = 2;</code>
   * @return The bytes for madeTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMadeTimeBytes() {
    java.lang.Object ref = madeTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      madeTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ASSIGNED_TIME_FIELD_NUMBER = 3;
  private volatile java.lang.Object assignedTime_;
  /**
   * <pre>
   *物流接单时间
   * </pre>
   *
   * <code>string assigned_time = 3;</code>
   * @return The assignedTime.
   */
  @java.lang.Override
  public java.lang.String getAssignedTime() {
    java.lang.Object ref = assignedTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      assignedTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *物流接单时间
   * </pre>
   *
   * <code>string assigned_time = 3;</code>
   * @return The bytes for assignedTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAssignedTimeBytes() {
    java.lang.Object ref = assignedTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      assignedTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ARRIVED_TIME_FIELD_NUMBER = 4;
  private volatile java.lang.Object arrivedTime_;
  /**
   * <pre>
   *骑手到店时间
   * </pre>
   *
   * <code>string arrived_time = 4;</code>
   * @return The arrivedTime.
   */
  @java.lang.Override
  public java.lang.String getArrivedTime() {
    java.lang.Object ref = arrivedTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      arrivedTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *骑手到店时间
   * </pre>
   *
   * <code>string arrived_time = 4;</code>
   * @return The bytes for arrivedTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getArrivedTimeBytes() {
    java.lang.Object ref = arrivedTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      arrivedTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int FETCHED_TIME_FIELD_NUMBER = 5;
  private volatile java.lang.Object fetchedTime_;
  /**
   * <pre>
   *骑手取餐时间
   * </pre>
   *
   * <code>string fetched_time = 5;</code>
   * @return The fetchedTime.
   */
  @java.lang.Override
  public java.lang.String getFetchedTime() {
    java.lang.Object ref = fetchedTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      fetchedTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *骑手取餐时间
   * </pre>
   *
   * <code>string fetched_time = 5;</code>
   * @return The bytes for fetchedTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getFetchedTimeBytes() {
    java.lang.Object ref = fetchedTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      fetchedTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DELIVERED_TIME_FIELD_NUMBER = 6;
  private volatile java.lang.Object deliveredTime_;
  /**
   * <pre>
   *骑手送达时间
   * </pre>
   *
   * <code>string delivered_time = 6;</code>
   * @return The deliveredTime.
   */
  @java.lang.Override
  public java.lang.String getDeliveredTime() {
    java.lang.Object ref = deliveredTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deliveredTime_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *骑手送达时间
   * </pre>
   *
   * <code>string delivered_time = 6;</code>
   * @return The bytes for deliveredTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDeliveredTimeBytes() {
    java.lang.Object ref = deliveredTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deliveredTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MAKE_SPAN_FIELD_NUMBER = 7;
  private float makeSpan_;
  /**
   * <pre>
   *制作时长
   * </pre>
   *
   * <code>float make_span = 7;</code>
   * @return The makeSpan.
   */
  @java.lang.Override
  public float getMakeSpan() {
    return makeSpan_;
  }

  public static final int AVG_MAKE_SPAN_FIELD_NUMBER = 8;
  private float avgMakeSpan_;
  /**
   * <pre>
   *平均每杯制作时长
   * </pre>
   *
   * <code>float avg_make_span = 8;</code>
   * @return The avgMakeSpan.
   */
  @java.lang.Override
  public float getAvgMakeSpan() {
    return avgMakeSpan_;
  }

  public static final int ARRIVE_SPAN_FIELD_NUMBER = 9;
  private float arriveSpan_;
  /**
   * <pre>
   *取餐时长
   * </pre>
   *
   * <code>float arrive_span = 9;</code>
   * @return The arriveSpan.
   */
  @java.lang.Override
  public float getArriveSpan() {
    return arriveSpan_;
  }

  public static final int DELIVER_SPAN_FIELD_NUMBER = 10;
  private float deliverSpan_;
  /**
   * <pre>
   *配送时长
   * </pre>
   *
   * <code>float deliver_span = 10;</code>
   * @return The deliverSpan.
   */
  @java.lang.Override
  public float getDeliverSpan() {
    return deliverSpan_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getConfirmedTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, confirmedTime_);
    }
    if (!getMadeTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, madeTime_);
    }
    if (!getAssignedTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, assignedTime_);
    }
    if (!getArrivedTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, arrivedTime_);
    }
    if (!getFetchedTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, fetchedTime_);
    }
    if (!getDeliveredTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, deliveredTime_);
    }
    if (makeSpan_ != 0F) {
      output.writeFloat(7, makeSpan_);
    }
    if (avgMakeSpan_ != 0F) {
      output.writeFloat(8, avgMakeSpan_);
    }
    if (arriveSpan_ != 0F) {
      output.writeFloat(9, arriveSpan_);
    }
    if (deliverSpan_ != 0F) {
      output.writeFloat(10, deliverSpan_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getConfirmedTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, confirmedTime_);
    }
    if (!getMadeTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, madeTime_);
    }
    if (!getAssignedTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, assignedTime_);
    }
    if (!getArrivedTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, arrivedTime_);
    }
    if (!getFetchedTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, fetchedTime_);
    }
    if (!getDeliveredTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, deliveredTime_);
    }
    if (makeSpan_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(7, makeSpan_);
    }
    if (avgMakeSpan_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(8, avgMakeSpan_);
    }
    if (arriveSpan_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(9, arriveSpan_);
    }
    if (deliverSpan_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(10, deliverSpan_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.eticket.Efficiency)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.eticket.Efficiency other = (cn.hexcloud.pbis.common.service.integration.eticket.Efficiency) obj;

    if (!getConfirmedTime()
        .equals(other.getConfirmedTime())) return false;
    if (!getMadeTime()
        .equals(other.getMadeTime())) return false;
    if (!getAssignedTime()
        .equals(other.getAssignedTime())) return false;
    if (!getArrivedTime()
        .equals(other.getArrivedTime())) return false;
    if (!getFetchedTime()
        .equals(other.getFetchedTime())) return false;
    if (!getDeliveredTime()
        .equals(other.getDeliveredTime())) return false;
    if (java.lang.Float.floatToIntBits(getMakeSpan())
        != java.lang.Float.floatToIntBits(
            other.getMakeSpan())) return false;
    if (java.lang.Float.floatToIntBits(getAvgMakeSpan())
        != java.lang.Float.floatToIntBits(
            other.getAvgMakeSpan())) return false;
    if (java.lang.Float.floatToIntBits(getArriveSpan())
        != java.lang.Float.floatToIntBits(
            other.getArriveSpan())) return false;
    if (java.lang.Float.floatToIntBits(getDeliverSpan())
        != java.lang.Float.floatToIntBits(
            other.getDeliverSpan())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CONFIRMED_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getConfirmedTime().hashCode();
    hash = (37 * hash) + MADE_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getMadeTime().hashCode();
    hash = (37 * hash) + ASSIGNED_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getAssignedTime().hashCode();
    hash = (37 * hash) + ARRIVED_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getArrivedTime().hashCode();
    hash = (37 * hash) + FETCHED_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getFetchedTime().hashCode();
    hash = (37 * hash) + DELIVERED_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getDeliveredTime().hashCode();
    hash = (37 * hash) + MAKE_SPAN_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getMakeSpan());
    hash = (37 * hash) + AVG_MAKE_SPAN_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getAvgMakeSpan());
    hash = (37 * hash) + ARRIVE_SPAN_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getArriveSpan());
    hash = (37 * hash) + DELIVER_SPAN_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getDeliverSpan());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.Efficiency parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Efficiency parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Efficiency parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Efficiency parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Efficiency parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Efficiency parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Efficiency parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Efficiency parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Efficiency parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Efficiency parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Efficiency parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Efficiency parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.eticket.Efficiency prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code eticket_proto.Efficiency}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:eticket_proto.Efficiency)
      cn.hexcloud.pbis.common.service.integration.eticket.EfficiencyOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Efficiency_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Efficiency_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.eticket.Efficiency.class, cn.hexcloud.pbis.common.service.integration.eticket.Efficiency.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.eticket.Efficiency.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      confirmedTime_ = "";

      madeTime_ = "";

      assignedTime_ = "";

      arrivedTime_ = "";

      fetchedTime_ = "";

      deliveredTime_ = "";

      makeSpan_ = 0F;

      avgMakeSpan_ = 0F;

      arriveSpan_ = 0F;

      deliverSpan_ = 0F;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Efficiency_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Efficiency getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.Efficiency.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Efficiency build() {
      cn.hexcloud.pbis.common.service.integration.eticket.Efficiency result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Efficiency buildPartial() {
      cn.hexcloud.pbis.common.service.integration.eticket.Efficiency result = new cn.hexcloud.pbis.common.service.integration.eticket.Efficiency(this);
      result.confirmedTime_ = confirmedTime_;
      result.madeTime_ = madeTime_;
      result.assignedTime_ = assignedTime_;
      result.arrivedTime_ = arrivedTime_;
      result.fetchedTime_ = fetchedTime_;
      result.deliveredTime_ = deliveredTime_;
      result.makeSpan_ = makeSpan_;
      result.avgMakeSpan_ = avgMakeSpan_;
      result.arriveSpan_ = arriveSpan_;
      result.deliverSpan_ = deliverSpan_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.eticket.Efficiency) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.eticket.Efficiency)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.eticket.Efficiency other) {
      if (other == cn.hexcloud.pbis.common.service.integration.eticket.Efficiency.getDefaultInstance()) return this;
      if (!other.getConfirmedTime().isEmpty()) {
        confirmedTime_ = other.confirmedTime_;
        onChanged();
      }
      if (!other.getMadeTime().isEmpty()) {
        madeTime_ = other.madeTime_;
        onChanged();
      }
      if (!other.getAssignedTime().isEmpty()) {
        assignedTime_ = other.assignedTime_;
        onChanged();
      }
      if (!other.getArrivedTime().isEmpty()) {
        arrivedTime_ = other.arrivedTime_;
        onChanged();
      }
      if (!other.getFetchedTime().isEmpty()) {
        fetchedTime_ = other.fetchedTime_;
        onChanged();
      }
      if (!other.getDeliveredTime().isEmpty()) {
        deliveredTime_ = other.deliveredTime_;
        onChanged();
      }
      if (other.getMakeSpan() != 0F) {
        setMakeSpan(other.getMakeSpan());
      }
      if (other.getAvgMakeSpan() != 0F) {
        setAvgMakeSpan(other.getAvgMakeSpan());
      }
      if (other.getArriveSpan() != 0F) {
        setArriveSpan(other.getArriveSpan());
      }
      if (other.getDeliverSpan() != 0F) {
        setDeliverSpan(other.getDeliverSpan());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.eticket.Efficiency parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.eticket.Efficiency) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object confirmedTime_ = "";
    /**
     * <pre>
     *订单确认时间
     * </pre>
     *
     * <code>string confirmed_time = 1;</code>
     * @return The confirmedTime.
     */
    public java.lang.String getConfirmedTime() {
      java.lang.Object ref = confirmedTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        confirmedTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *订单确认时间
     * </pre>
     *
     * <code>string confirmed_time = 1;</code>
     * @return The bytes for confirmedTime.
     */
    public com.google.protobuf.ByteString
        getConfirmedTimeBytes() {
      java.lang.Object ref = confirmedTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        confirmedTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *订单确认时间
     * </pre>
     *
     * <code>string confirmed_time = 1;</code>
     * @param value The confirmedTime to set.
     * @return This builder for chaining.
     */
    public Builder setConfirmedTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      confirmedTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单确认时间
     * </pre>
     *
     * <code>string confirmed_time = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearConfirmedTime() {
      
      confirmedTime_ = getDefaultInstance().getConfirmedTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *订单确认时间
     * </pre>
     *
     * <code>string confirmed_time = 1;</code>
     * @param value The bytes for confirmedTime to set.
     * @return This builder for chaining.
     */
    public Builder setConfirmedTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      confirmedTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object madeTime_ = "";
    /**
     * <pre>
     *制作完成时间
     * </pre>
     *
     * <code>string made_time = 2;</code>
     * @return The madeTime.
     */
    public java.lang.String getMadeTime() {
      java.lang.Object ref = madeTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        madeTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *制作完成时间
     * </pre>
     *
     * <code>string made_time = 2;</code>
     * @return The bytes for madeTime.
     */
    public com.google.protobuf.ByteString
        getMadeTimeBytes() {
      java.lang.Object ref = madeTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        madeTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *制作完成时间
     * </pre>
     *
     * <code>string made_time = 2;</code>
     * @param value The madeTime to set.
     * @return This builder for chaining.
     */
    public Builder setMadeTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      madeTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *制作完成时间
     * </pre>
     *
     * <code>string made_time = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMadeTime() {
      
      madeTime_ = getDefaultInstance().getMadeTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *制作完成时间
     * </pre>
     *
     * <code>string made_time = 2;</code>
     * @param value The bytes for madeTime to set.
     * @return This builder for chaining.
     */
    public Builder setMadeTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      madeTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object assignedTime_ = "";
    /**
     * <pre>
     *物流接单时间
     * </pre>
     *
     * <code>string assigned_time = 3;</code>
     * @return The assignedTime.
     */
    public java.lang.String getAssignedTime() {
      java.lang.Object ref = assignedTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        assignedTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *物流接单时间
     * </pre>
     *
     * <code>string assigned_time = 3;</code>
     * @return The bytes for assignedTime.
     */
    public com.google.protobuf.ByteString
        getAssignedTimeBytes() {
      java.lang.Object ref = assignedTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        assignedTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *物流接单时间
     * </pre>
     *
     * <code>string assigned_time = 3;</code>
     * @param value The assignedTime to set.
     * @return This builder for chaining.
     */
    public Builder setAssignedTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      assignedTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *物流接单时间
     * </pre>
     *
     * <code>string assigned_time = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearAssignedTime() {
      
      assignedTime_ = getDefaultInstance().getAssignedTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *物流接单时间
     * </pre>
     *
     * <code>string assigned_time = 3;</code>
     * @param value The bytes for assignedTime to set.
     * @return This builder for chaining.
     */
    public Builder setAssignedTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      assignedTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object arrivedTime_ = "";
    /**
     * <pre>
     *骑手到店时间
     * </pre>
     *
     * <code>string arrived_time = 4;</code>
     * @return The arrivedTime.
     */
    public java.lang.String getArrivedTime() {
      java.lang.Object ref = arrivedTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        arrivedTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *骑手到店时间
     * </pre>
     *
     * <code>string arrived_time = 4;</code>
     * @return The bytes for arrivedTime.
     */
    public com.google.protobuf.ByteString
        getArrivedTimeBytes() {
      java.lang.Object ref = arrivedTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        arrivedTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *骑手到店时间
     * </pre>
     *
     * <code>string arrived_time = 4;</code>
     * @param value The arrivedTime to set.
     * @return This builder for chaining.
     */
    public Builder setArrivedTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      arrivedTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *骑手到店时间
     * </pre>
     *
     * <code>string arrived_time = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearArrivedTime() {
      
      arrivedTime_ = getDefaultInstance().getArrivedTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *骑手到店时间
     * </pre>
     *
     * <code>string arrived_time = 4;</code>
     * @param value The bytes for arrivedTime to set.
     * @return This builder for chaining.
     */
    public Builder setArrivedTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      arrivedTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object fetchedTime_ = "";
    /**
     * <pre>
     *骑手取餐时间
     * </pre>
     *
     * <code>string fetched_time = 5;</code>
     * @return The fetchedTime.
     */
    public java.lang.String getFetchedTime() {
      java.lang.Object ref = fetchedTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        fetchedTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *骑手取餐时间
     * </pre>
     *
     * <code>string fetched_time = 5;</code>
     * @return The bytes for fetchedTime.
     */
    public com.google.protobuf.ByteString
        getFetchedTimeBytes() {
      java.lang.Object ref = fetchedTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fetchedTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *骑手取餐时间
     * </pre>
     *
     * <code>string fetched_time = 5;</code>
     * @param value The fetchedTime to set.
     * @return This builder for chaining.
     */
    public Builder setFetchedTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      fetchedTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *骑手取餐时间
     * </pre>
     *
     * <code>string fetched_time = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearFetchedTime() {
      
      fetchedTime_ = getDefaultInstance().getFetchedTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *骑手取餐时间
     * </pre>
     *
     * <code>string fetched_time = 5;</code>
     * @param value The bytes for fetchedTime to set.
     * @return This builder for chaining.
     */
    public Builder setFetchedTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      fetchedTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object deliveredTime_ = "";
    /**
     * <pre>
     *骑手送达时间
     * </pre>
     *
     * <code>string delivered_time = 6;</code>
     * @return The deliveredTime.
     */
    public java.lang.String getDeliveredTime() {
      java.lang.Object ref = deliveredTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deliveredTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *骑手送达时间
     * </pre>
     *
     * <code>string delivered_time = 6;</code>
     * @return The bytes for deliveredTime.
     */
    public com.google.protobuf.ByteString
        getDeliveredTimeBytes() {
      java.lang.Object ref = deliveredTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deliveredTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *骑手送达时间
     * </pre>
     *
     * <code>string delivered_time = 6;</code>
     * @param value The deliveredTime to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveredTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      deliveredTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *骑手送达时间
     * </pre>
     *
     * <code>string delivered_time = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeliveredTime() {
      
      deliveredTime_ = getDefaultInstance().getDeliveredTime();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *骑手送达时间
     * </pre>
     *
     * <code>string delivered_time = 6;</code>
     * @param value The bytes for deliveredTime to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveredTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      deliveredTime_ = value;
      onChanged();
      return this;
    }

    private float makeSpan_ ;
    /**
     * <pre>
     *制作时长
     * </pre>
     *
     * <code>float make_span = 7;</code>
     * @return The makeSpan.
     */
    @java.lang.Override
    public float getMakeSpan() {
      return makeSpan_;
    }
    /**
     * <pre>
     *制作时长
     * </pre>
     *
     * <code>float make_span = 7;</code>
     * @param value The makeSpan to set.
     * @return This builder for chaining.
     */
    public Builder setMakeSpan(float value) {
      
      makeSpan_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *制作时长
     * </pre>
     *
     * <code>float make_span = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearMakeSpan() {
      
      makeSpan_ = 0F;
      onChanged();
      return this;
    }

    private float avgMakeSpan_ ;
    /**
     * <pre>
     *平均每杯制作时长
     * </pre>
     *
     * <code>float avg_make_span = 8;</code>
     * @return The avgMakeSpan.
     */
    @java.lang.Override
    public float getAvgMakeSpan() {
      return avgMakeSpan_;
    }
    /**
     * <pre>
     *平均每杯制作时长
     * </pre>
     *
     * <code>float avg_make_span = 8;</code>
     * @param value The avgMakeSpan to set.
     * @return This builder for chaining.
     */
    public Builder setAvgMakeSpan(float value) {
      
      avgMakeSpan_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *平均每杯制作时长
     * </pre>
     *
     * <code>float avg_make_span = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearAvgMakeSpan() {
      
      avgMakeSpan_ = 0F;
      onChanged();
      return this;
    }

    private float arriveSpan_ ;
    /**
     * <pre>
     *取餐时长
     * </pre>
     *
     * <code>float arrive_span = 9;</code>
     * @return The arriveSpan.
     */
    @java.lang.Override
    public float getArriveSpan() {
      return arriveSpan_;
    }
    /**
     * <pre>
     *取餐时长
     * </pre>
     *
     * <code>float arrive_span = 9;</code>
     * @param value The arriveSpan to set.
     * @return This builder for chaining.
     */
    public Builder setArriveSpan(float value) {
      
      arriveSpan_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *取餐时长
     * </pre>
     *
     * <code>float arrive_span = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearArriveSpan() {
      
      arriveSpan_ = 0F;
      onChanged();
      return this;
    }

    private float deliverSpan_ ;
    /**
     * <pre>
     *配送时长
     * </pre>
     *
     * <code>float deliver_span = 10;</code>
     * @return The deliverSpan.
     */
    @java.lang.Override
    public float getDeliverSpan() {
      return deliverSpan_;
    }
    /**
     * <pre>
     *配送时长
     * </pre>
     *
     * <code>float deliver_span = 10;</code>
     * @param value The deliverSpan to set.
     * @return This builder for chaining.
     */
    public Builder setDeliverSpan(float value) {
      
      deliverSpan_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *配送时长
     * </pre>
     *
     * <code>float deliver_span = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeliverSpan() {
      
      deliverSpan_ = 0F;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:eticket_proto.Efficiency)
  }

  // @@protoc_insertion_point(class_scope:eticket_proto.Efficiency)
  private static final cn.hexcloud.pbis.common.service.integration.eticket.Efficiency DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.eticket.Efficiency();
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.Efficiency getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Efficiency>
      PARSER = new com.google.protobuf.AbstractParser<Efficiency>() {
    @java.lang.Override
    public Efficiency parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Efficiency(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Efficiency> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Efficiency> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.Efficiency getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

