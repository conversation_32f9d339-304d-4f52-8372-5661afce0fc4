package cn.hexcloud.pbis.common.service.integration.eticket;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.40.1)",
    comments = "Source: service.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class EticketGrpc {

  private EticketGrpc() {}

  public static final String SERVICE_NAME = "eticket_proto.Eticket";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest,
      cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketResponse> getUploadTicketMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "UploadTicket",
      requestType = cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest,
      cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketResponse> getUploadTicketMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest, cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketResponse> getUploadTicketMethod;
    if ((getUploadTicketMethod = EticketGrpc.getUploadTicketMethod) == null) {
      synchronized (EticketGrpc.class) {
        if ((getUploadTicketMethod = EticketGrpc.getUploadTicketMethod) == null) {
          EticketGrpc.getUploadTicketMethod = getUploadTicketMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest, cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "UploadTicket"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketResponse.getDefaultInstance()))
              .setSchemaDescriptor(new EticketMethodDescriptorSupplier("UploadTicket"))
              .build();
        }
      }
    }
    return getUploadTicketMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static EticketStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<EticketStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<EticketStub>() {
        @java.lang.Override
        public EticketStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new EticketStub(channel, callOptions);
        }
      };
    return EticketStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static EticketBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<EticketBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<EticketBlockingStub>() {
        @java.lang.Override
        public EticketBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new EticketBlockingStub(channel, callOptions);
        }
      };
    return EticketBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static EticketFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<EticketFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<EticketFutureStub>() {
        @java.lang.Override
        public EticketFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new EticketFutureStub(channel, callOptions);
        }
      };
    return EticketFutureStub.newStub(factory, channel);
  }

  /**
   */
  public static abstract class EticketImplBase implements io.grpc.BindableService {

    /**
     * <pre>
     * 电子小票订单上传
     * </pre>
     */
    public void uploadTicket(cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUploadTicketMethod(), responseObserver);
    }

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getUploadTicketMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest,
                cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketResponse>(
                  this, METHODID_UPLOAD_TICKET)))
          .build();
    }
  }

  /**
   */
  public static final class EticketStub extends io.grpc.stub.AbstractAsyncStub<EticketStub> {
    private EticketStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected EticketStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new EticketStub(channel, callOptions);
    }

    /**
     * <pre>
     * 电子小票订单上传
     * </pre>
     */
    public void uploadTicket(cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUploadTicketMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   */
  public static final class EticketBlockingStub extends io.grpc.stub.AbstractBlockingStub<EticketBlockingStub> {
    private EticketBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected EticketBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new EticketBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * 电子小票订单上传
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketResponse uploadTicket(cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUploadTicketMethod(), getCallOptions(), request);
    }
  }

  /**
   */
  public static final class EticketFutureStub extends io.grpc.stub.AbstractFutureStub<EticketFutureStub> {
    private EticketFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected EticketFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new EticketFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * 电子小票订单上传
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketResponse> uploadTicket(
        cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUploadTicketMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_UPLOAD_TICKET = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final EticketImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(EticketImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_UPLOAD_TICKET:
          serviceImpl.uploadTicket((cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class EticketBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    EticketBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.eticket.Service.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("Eticket");
    }
  }

  private static final class EticketFileDescriptorSupplier
      extends EticketBaseDescriptorSupplier {
    EticketFileDescriptorSupplier() {}
  }

  private static final class EticketMethodDescriptorSupplier
      extends EticketBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    EticketMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (EticketGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new EticketFileDescriptorSupplier())
              .addMethod(getUploadTicketMethod())
              .build();
        }
      }
    }
    return result;
  }
}
