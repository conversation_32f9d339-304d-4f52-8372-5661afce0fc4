// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

public interface FeeOrBuilder extends
    // @@protoc_insertion_point(interface_extends:eticket_proto.Fee)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string name = 1;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <code>string name = 1;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <code>double price = 2;</code>
   * @return The price.
   */
  double getPrice();

  /**
   * <code>int32 qty = 3;</code>
   * @return The qty.
   */
  int getQty();

  /**
   * <code>double amount = 4;</code>
   * @return The amount.
   */
  double getAmount();

  /**
   * <code>string type = 5;</code>
   * @return The type.
   */
  java.lang.String getType();
  /**
   * <code>string type = 5;</code>
   * @return The bytes for type.
   */
  com.google.protobuf.ByteString
      getTypeBytes();

  /**
   * <code>float discount_rate = 6;</code>
   * @return The discountRate.
   */
  float getDiscountRate();

  /**
   * <code>repeated .eticket_proto.Fee detail_fees = 7;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.integration.eticket.Fee> 
      getDetailFeesList();
  /**
   * <code>repeated .eticket_proto.Fee detail_fees = 7;</code>
   */
  cn.hexcloud.pbis.common.service.integration.eticket.Fee getDetailFees(int index);
  /**
   * <code>repeated .eticket_proto.Fee detail_fees = 7;</code>
   */
  int getDetailFeesCount();
  /**
   * <code>repeated .eticket_proto.Fee detail_fees = 7;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.integration.eticket.FeeOrBuilder> 
      getDetailFeesOrBuilderList();
  /**
   * <code>repeated .eticket_proto.Fee detail_fees = 7;</code>
   */
  cn.hexcloud.pbis.common.service.integration.eticket.FeeOrBuilder getDetailFeesOrBuilder(
      int index);
}
