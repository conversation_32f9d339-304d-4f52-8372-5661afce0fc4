// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

public interface OperatorOrBuilder extends
    // @@protoc_insertion_point(interface_extends:eticket_proto.Operator)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 收银员id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   * 收银员id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   *收银员姓名
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   *收银员姓名
   * </pre>
   *
   * <code>string name = 2;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   *收银员code
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <pre>
   *收银员code
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <pre>
   *登陆时间
   * </pre>
   *
   * <code>string login_time = 4;</code>
   * @return The loginTime.
   */
  java.lang.String getLoginTime();
  /**
   * <pre>
   *登陆时间
   * </pre>
   *
   * <code>string login_time = 4;</code>
   * @return The bytes for loginTime.
   */
  com.google.protobuf.ByteString
      getLoginTimeBytes();

  /**
   * <pre>
   *登陆id
   * </pre>
   *
   * <code>string loginId = 5;</code>
   * @return The loginId.
   */
  java.lang.String getLoginId();
  /**
   * <pre>
   *登陆id
   * </pre>
   *
   * <code>string loginId = 5;</code>
   * @return The bytes for loginId.
   */
  com.google.protobuf.ByteString
      getLoginIdBytes();
}
