// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

public interface PosOrBuilder extends
    // @@protoc_insertion_point(interface_extends:eticket_proto.Pos)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *pos id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   *pos id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   * pos 编码
   * </pre>
   *
   * <code>string code = 2;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <pre>
   * pos 编码
   * </pre>
   *
   * <code>string code = 2;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <pre>
   *pos名称
   * </pre>
   *
   * <code>string pos_name = 3;</code>
   * @return The posName.
   */
  java.lang.String getPosName();
  /**
   * <pre>
   *pos名称
   * </pre>
   *
   * <code>string pos_name = 3;</code>
   * @return The bytes for posName.
   */
  com.google.protobuf.ByteString
      getPosNameBytes();

  /**
   * <pre>
   * pos的设备id
   * </pre>
   *
   * <code>string device_id = 4;</code>
   * @return The deviceId.
   */
  java.lang.String getDeviceId();
  /**
   * <pre>
   * pos的设备id
   * </pre>
   *
   * <code>string device_id = 4;</code>
   * @return The bytes for deviceId.
   */
  com.google.protobuf.ByteString
      getDeviceIdBytes();

  /**
   * <pre>
   *pos设备编码
   * </pre>
   *
   * <code>string device_code = 5;</code>
   * @return The deviceCode.
   */
  java.lang.String getDeviceCode();
  /**
   * <pre>
   *pos设备编码
   * </pre>
   *
   * <code>string device_code = 5;</code>
   * @return The bytes for deviceCode.
   */
  com.google.protobuf.ByteString
      getDeviceCodeBytes();
}
