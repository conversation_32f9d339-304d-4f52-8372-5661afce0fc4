// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

/**
 * <pre>
 *促销
 * </pre>
 *
 * Protobuf type {@code eticket_proto.Promotion}
 */
public final class Promotion extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:eticket_proto.Promotion)
    PromotionOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Promotion.newBuilder() to construct.
  private Promotion(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Promotion() {
    products_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Promotion();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Promotion(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.Builder subBuilder = null;
            if (promotionInfo_ != null) {
              subBuilder = promotionInfo_.toBuilder();
            }
            promotionInfo_ = input.readMessage(cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(promotionInfo_);
              promotionInfo_ = subBuilder.buildPartial();
            }

            break;
          }
          case 18: {
            cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.Builder subBuilder = null;
            if (source_ != null) {
              subBuilder = source_.toBuilder();
            }
            source_ = input.readMessage(cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(source_);
              source_ = subBuilder.buildPartial();
            }

            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              products_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct>();
              mutable_bitField0_ |= 0x00000001;
            }
            products_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        products_ = java.util.Collections.unmodifiableList(products_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Promotion_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Promotion_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.eticket.Promotion.class, cn.hexcloud.pbis.common.service.integration.eticket.Promotion.Builder.class);
  }

  public static final int PROMOTIONINFO_FIELD_NUMBER = 1;
  private cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo promotionInfo_;
  /**
   * <code>.eticket_proto.PromotionInfo promotionInfo = 1;</code>
   * @return Whether the promotionInfo field is set.
   */
  @java.lang.Override
  public boolean hasPromotionInfo() {
    return promotionInfo_ != null;
  }
  /**
   * <code>.eticket_proto.PromotionInfo promotionInfo = 1;</code>
   * @return The promotionInfo.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo getPromotionInfo() {
    return promotionInfo_ == null ? cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.getDefaultInstance() : promotionInfo_;
  }
  /**
   * <code>.eticket_proto.PromotionInfo promotionInfo = 1;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfoOrBuilder getPromotionInfoOrBuilder() {
    return getPromotionInfo();
  }

  public static final int SOURCE_FIELD_NUMBER = 2;
  private cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource source_;
  /**
   * <code>.eticket_proto.PromotionSource source = 2;</code>
   * @return Whether the source field is set.
   */
  @java.lang.Override
  public boolean hasSource() {
    return source_ != null;
  }
  /**
   * <code>.eticket_proto.PromotionSource source = 2;</code>
   * @return The source.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource getSource() {
    return source_ == null ? cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.getDefaultInstance() : source_;
  }
  /**
   * <code>.eticket_proto.PromotionSource source = 2;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.PromotionSourceOrBuilder getSourceOrBuilder() {
    return getSource();
  }

  public static final int PRODUCTS_FIELD_NUMBER = 3;
  private java.util.List<cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct> products_;
  /**
   * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct> getProductsList() {
    return products_;
  }
  /**
   * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder> 
      getProductsOrBuilderList() {
    return products_;
  }
  /**
   * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
   */
  @java.lang.Override
  public int getProductsCount() {
    return products_.size();
  }
  /**
   * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct getProducts(int index) {
    return products_.get(index);
  }
  /**
   * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder getProductsOrBuilder(
      int index) {
    return products_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (promotionInfo_ != null) {
      output.writeMessage(1, getPromotionInfo());
    }
    if (source_ != null) {
      output.writeMessage(2, getSource());
    }
    for (int i = 0; i < products_.size(); i++) {
      output.writeMessage(3, products_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (promotionInfo_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getPromotionInfo());
    }
    if (source_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, getSource());
    }
    for (int i = 0; i < products_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, products_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.eticket.Promotion)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.eticket.Promotion other = (cn.hexcloud.pbis.common.service.integration.eticket.Promotion) obj;

    if (hasPromotionInfo() != other.hasPromotionInfo()) return false;
    if (hasPromotionInfo()) {
      if (!getPromotionInfo()
          .equals(other.getPromotionInfo())) return false;
    }
    if (hasSource() != other.hasSource()) return false;
    if (hasSource()) {
      if (!getSource()
          .equals(other.getSource())) return false;
    }
    if (!getProductsList()
        .equals(other.getProductsList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasPromotionInfo()) {
      hash = (37 * hash) + PROMOTIONINFO_FIELD_NUMBER;
      hash = (53 * hash) + getPromotionInfo().hashCode();
    }
    if (hasSource()) {
      hash = (37 * hash) + SOURCE_FIELD_NUMBER;
      hash = (53 * hash) + getSource().hashCode();
    }
    if (getProductsCount() > 0) {
      hash = (37 * hash) + PRODUCTS_FIELD_NUMBER;
      hash = (53 * hash) + getProductsList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.Promotion parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Promotion parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Promotion parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Promotion parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Promotion parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Promotion parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Promotion parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Promotion parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Promotion parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Promotion parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Promotion parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Promotion parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.eticket.Promotion prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *促销
   * </pre>
   *
   * Protobuf type {@code eticket_proto.Promotion}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:eticket_proto.Promotion)
      cn.hexcloud.pbis.common.service.integration.eticket.PromotionOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Promotion_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Promotion_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.eticket.Promotion.class, cn.hexcloud.pbis.common.service.integration.eticket.Promotion.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.eticket.Promotion.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getProductsFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (promotionInfoBuilder_ == null) {
        promotionInfo_ = null;
      } else {
        promotionInfo_ = null;
        promotionInfoBuilder_ = null;
      }
      if (sourceBuilder_ == null) {
        source_ = null;
      } else {
        source_ = null;
        sourceBuilder_ = null;
      }
      if (productsBuilder_ == null) {
        products_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        productsBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Promotion_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Promotion getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.Promotion.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Promotion build() {
      cn.hexcloud.pbis.common.service.integration.eticket.Promotion result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Promotion buildPartial() {
      cn.hexcloud.pbis.common.service.integration.eticket.Promotion result = new cn.hexcloud.pbis.common.service.integration.eticket.Promotion(this);
      int from_bitField0_ = bitField0_;
      if (promotionInfoBuilder_ == null) {
        result.promotionInfo_ = promotionInfo_;
      } else {
        result.promotionInfo_ = promotionInfoBuilder_.build();
      }
      if (sourceBuilder_ == null) {
        result.source_ = source_;
      } else {
        result.source_ = sourceBuilder_.build();
      }
      if (productsBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          products_ = java.util.Collections.unmodifiableList(products_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.products_ = products_;
      } else {
        result.products_ = productsBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.eticket.Promotion) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.eticket.Promotion)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.eticket.Promotion other) {
      if (other == cn.hexcloud.pbis.common.service.integration.eticket.Promotion.getDefaultInstance()) return this;
      if (other.hasPromotionInfo()) {
        mergePromotionInfo(other.getPromotionInfo());
      }
      if (other.hasSource()) {
        mergeSource(other.getSource());
      }
      if (productsBuilder_ == null) {
        if (!other.products_.isEmpty()) {
          if (products_.isEmpty()) {
            products_ = other.products_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureProductsIsMutable();
            products_.addAll(other.products_);
          }
          onChanged();
        }
      } else {
        if (!other.products_.isEmpty()) {
          if (productsBuilder_.isEmpty()) {
            productsBuilder_.dispose();
            productsBuilder_ = null;
            products_ = other.products_;
            bitField0_ = (bitField0_ & ~0x00000001);
            productsBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getProductsFieldBuilder() : null;
          } else {
            productsBuilder_.addAllMessages(other.products_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.eticket.Promotion parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.eticket.Promotion) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo promotionInfo_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo, cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.Builder, cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfoOrBuilder> promotionInfoBuilder_;
    /**
     * <code>.eticket_proto.PromotionInfo promotionInfo = 1;</code>
     * @return Whether the promotionInfo field is set.
     */
    public boolean hasPromotionInfo() {
      return promotionInfoBuilder_ != null || promotionInfo_ != null;
    }
    /**
     * <code>.eticket_proto.PromotionInfo promotionInfo = 1;</code>
     * @return The promotionInfo.
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo getPromotionInfo() {
      if (promotionInfoBuilder_ == null) {
        return promotionInfo_ == null ? cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.getDefaultInstance() : promotionInfo_;
      } else {
        return promotionInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>.eticket_proto.PromotionInfo promotionInfo = 1;</code>
     */
    public Builder setPromotionInfo(cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo value) {
      if (promotionInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        promotionInfo_ = value;
        onChanged();
      } else {
        promotionInfoBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <code>.eticket_proto.PromotionInfo promotionInfo = 1;</code>
     */
    public Builder setPromotionInfo(
        cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.Builder builderForValue) {
      if (promotionInfoBuilder_ == null) {
        promotionInfo_ = builderForValue.build();
        onChanged();
      } else {
        promotionInfoBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <code>.eticket_proto.PromotionInfo promotionInfo = 1;</code>
     */
    public Builder mergePromotionInfo(cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo value) {
      if (promotionInfoBuilder_ == null) {
        if (promotionInfo_ != null) {
          promotionInfo_ =
            cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.newBuilder(promotionInfo_).mergeFrom(value).buildPartial();
        } else {
          promotionInfo_ = value;
        }
        onChanged();
      } else {
        promotionInfoBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <code>.eticket_proto.PromotionInfo promotionInfo = 1;</code>
     */
    public Builder clearPromotionInfo() {
      if (promotionInfoBuilder_ == null) {
        promotionInfo_ = null;
        onChanged();
      } else {
        promotionInfo_ = null;
        promotionInfoBuilder_ = null;
      }

      return this;
    }
    /**
     * <code>.eticket_proto.PromotionInfo promotionInfo = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.Builder getPromotionInfoBuilder() {
      
      onChanged();
      return getPromotionInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>.eticket_proto.PromotionInfo promotionInfo = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfoOrBuilder getPromotionInfoOrBuilder() {
      if (promotionInfoBuilder_ != null) {
        return promotionInfoBuilder_.getMessageOrBuilder();
      } else {
        return promotionInfo_ == null ?
            cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.getDefaultInstance() : promotionInfo_;
      }
    }
    /**
     * <code>.eticket_proto.PromotionInfo promotionInfo = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo, cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.Builder, cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfoOrBuilder> 
        getPromotionInfoFieldBuilder() {
      if (promotionInfoBuilder_ == null) {
        promotionInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo, cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.Builder, cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfoOrBuilder>(
                getPromotionInfo(),
                getParentForChildren(),
                isClean());
        promotionInfo_ = null;
      }
      return promotionInfoBuilder_;
    }

    private cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource source_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource, cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.Builder, cn.hexcloud.pbis.common.service.integration.eticket.PromotionSourceOrBuilder> sourceBuilder_;
    /**
     * <code>.eticket_proto.PromotionSource source = 2;</code>
     * @return Whether the source field is set.
     */
    public boolean hasSource() {
      return sourceBuilder_ != null || source_ != null;
    }
    /**
     * <code>.eticket_proto.PromotionSource source = 2;</code>
     * @return The source.
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource getSource() {
      if (sourceBuilder_ == null) {
        return source_ == null ? cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.getDefaultInstance() : source_;
      } else {
        return sourceBuilder_.getMessage();
      }
    }
    /**
     * <code>.eticket_proto.PromotionSource source = 2;</code>
     */
    public Builder setSource(cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource value) {
      if (sourceBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        source_ = value;
        onChanged();
      } else {
        sourceBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <code>.eticket_proto.PromotionSource source = 2;</code>
     */
    public Builder setSource(
        cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.Builder builderForValue) {
      if (sourceBuilder_ == null) {
        source_ = builderForValue.build();
        onChanged();
      } else {
        sourceBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <code>.eticket_proto.PromotionSource source = 2;</code>
     */
    public Builder mergeSource(cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource value) {
      if (sourceBuilder_ == null) {
        if (source_ != null) {
          source_ =
            cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.newBuilder(source_).mergeFrom(value).buildPartial();
        } else {
          source_ = value;
        }
        onChanged();
      } else {
        sourceBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <code>.eticket_proto.PromotionSource source = 2;</code>
     */
    public Builder clearSource() {
      if (sourceBuilder_ == null) {
        source_ = null;
        onChanged();
      } else {
        source_ = null;
        sourceBuilder_ = null;
      }

      return this;
    }
    /**
     * <code>.eticket_proto.PromotionSource source = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.Builder getSourceBuilder() {
      
      onChanged();
      return getSourceFieldBuilder().getBuilder();
    }
    /**
     * <code>.eticket_proto.PromotionSource source = 2;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionSourceOrBuilder getSourceOrBuilder() {
      if (sourceBuilder_ != null) {
        return sourceBuilder_.getMessageOrBuilder();
      } else {
        return source_ == null ?
            cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.getDefaultInstance() : source_;
      }
    }
    /**
     * <code>.eticket_proto.PromotionSource source = 2;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource, cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.Builder, cn.hexcloud.pbis.common.service.integration.eticket.PromotionSourceOrBuilder> 
        getSourceFieldBuilder() {
      if (sourceBuilder_ == null) {
        sourceBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource, cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.Builder, cn.hexcloud.pbis.common.service.integration.eticket.PromotionSourceOrBuilder>(
                getSource(),
                getParentForChildren(),
                isClean());
        source_ = null;
      }
      return sourceBuilder_;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct> products_ =
      java.util.Collections.emptyList();
    private void ensureProductsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        products_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct>(products_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder> productsBuilder_;

    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct> getProductsList() {
      if (productsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(products_);
      } else {
        return productsBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public int getProductsCount() {
      if (productsBuilder_ == null) {
        return products_.size();
      } else {
        return productsBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct getProducts(int index) {
      if (productsBuilder_ == null) {
        return products_.get(index);
      } else {
        return productsBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public Builder setProducts(
        int index, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct value) {
      if (productsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureProductsIsMutable();
        products_.set(index, value);
        onChanged();
      } else {
        productsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public Builder setProducts(
        int index, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder builderForValue) {
      if (productsBuilder_ == null) {
        ensureProductsIsMutable();
        products_.set(index, builderForValue.build());
        onChanged();
      } else {
        productsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public Builder addProducts(cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct value) {
      if (productsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureProductsIsMutable();
        products_.add(value);
        onChanged();
      } else {
        productsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public Builder addProducts(
        int index, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct value) {
      if (productsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureProductsIsMutable();
        products_.add(index, value);
        onChanged();
      } else {
        productsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public Builder addProducts(
        cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder builderForValue) {
      if (productsBuilder_ == null) {
        ensureProductsIsMutable();
        products_.add(builderForValue.build());
        onChanged();
      } else {
        productsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public Builder addProducts(
        int index, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder builderForValue) {
      if (productsBuilder_ == null) {
        ensureProductsIsMutable();
        products_.add(index, builderForValue.build());
        onChanged();
      } else {
        productsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public Builder addAllProducts(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct> values) {
      if (productsBuilder_ == null) {
        ensureProductsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, products_);
        onChanged();
      } else {
        productsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public Builder clearProducts() {
      if (productsBuilder_ == null) {
        products_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        productsBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public Builder removeProducts(int index) {
      if (productsBuilder_ == null) {
        ensureProductsIsMutable();
        products_.remove(index);
        onChanged();
      } else {
        productsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder getProductsBuilder(
        int index) {
      return getProductsFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder getProductsOrBuilder(
        int index) {
      if (productsBuilder_ == null) {
        return products_.get(index);  } else {
        return productsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder> 
         getProductsOrBuilderList() {
      if (productsBuilder_ != null) {
        return productsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(products_);
      }
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder addProductsBuilder() {
      return getProductsFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.getDefaultInstance());
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder addProductsBuilder(
        int index) {
      return getProductsFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.getDefaultInstance());
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder> 
         getProductsBuilderList() {
      return getProductsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder> 
        getProductsFieldBuilder() {
      if (productsBuilder_ == null) {
        productsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder>(
                products_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        products_ = null;
      }
      return productsBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:eticket_proto.Promotion)
  }

  // @@protoc_insertion_point(class_scope:eticket_proto.Promotion)
  private static final cn.hexcloud.pbis.common.service.integration.eticket.Promotion DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.eticket.Promotion();
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.Promotion getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Promotion>
      PARSER = new com.google.protobuf.AbstractParser<Promotion>() {
    @java.lang.Override
    public Promotion parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Promotion(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Promotion> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Promotion> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.Promotion getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

