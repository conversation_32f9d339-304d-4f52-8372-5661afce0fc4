// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

/**
 * Protobuf type {@code eticket_proto.PromotionInfo}
 */
public final class PromotionInfo extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:eticket_proto.PromotionInfo)
    PromotionInfoOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PromotionInfo.newBuilder() to construct.
  private PromotionInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PromotionInfo() {
    type_ = "";
    discountType_ = "";
    name_ = "";
    promotionId_ = "";
    promotionCode_ = "";
    promotionType_ = "";
    ticketDisplay_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PromotionInfo();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private PromotionInfo(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            type_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            discountType_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            promotionId_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            promotionCode_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            promotionType_ = s;
            break;
          }
          case 56: {

            allowOverlap_ = input.readBool();
            break;
          }
          case 64: {

            triggerTimesCustom_ = input.readBool();
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            ticketDisplay_ = s;
            break;
          }
          case 81: {

            maxDiscount_ = input.readDouble();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_PromotionInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_PromotionInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.class, cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.Builder.class);
  }

  public static final int TYPE_FIELD_NUMBER = 1;
  private volatile java.lang.Object type_;
  /**
   * <code>string type = 1;</code>
   * @return The type.
   */
  @java.lang.Override
  public java.lang.String getType() {
    java.lang.Object ref = type_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      type_ = s;
      return s;
    }
  }
  /**
   * <code>string type = 1;</code>
   * @return The bytes for type.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTypeBytes() {
    java.lang.Object ref = type_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      type_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DISCOUNT_TYPE_FIELD_NUMBER = 2;
  private volatile java.lang.Object discountType_;
  /**
   * <code>string discount_type = 2;</code>
   * @return The discountType.
   */
  @java.lang.Override
  public java.lang.String getDiscountType() {
    java.lang.Object ref = discountType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      discountType_ = s;
      return s;
    }
  }
  /**
   * <code>string discount_type = 2;</code>
   * @return The bytes for discountType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDiscountTypeBytes() {
    java.lang.Object ref = discountType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      discountType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 3;
  private volatile java.lang.Object name_;
  /**
   * <code>string name = 3;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <code>string name = 3;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PROMOTION_ID_FIELD_NUMBER = 4;
  private volatile java.lang.Object promotionId_;
  /**
   * <code>string promotion_id = 4;</code>
   * @return The promotionId.
   */
  @java.lang.Override
  public java.lang.String getPromotionId() {
    java.lang.Object ref = promotionId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      promotionId_ = s;
      return s;
    }
  }
  /**
   * <code>string promotion_id = 4;</code>
   * @return The bytes for promotionId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPromotionIdBytes() {
    java.lang.Object ref = promotionId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      promotionId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PROMOTION_CODE_FIELD_NUMBER = 5;
  private volatile java.lang.Object promotionCode_;
  /**
   * <code>string promotion_code = 5;</code>
   * @return The promotionCode.
   */
  @java.lang.Override
  public java.lang.String getPromotionCode() {
    java.lang.Object ref = promotionCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      promotionCode_ = s;
      return s;
    }
  }
  /**
   * <code>string promotion_code = 5;</code>
   * @return The bytes for promotionCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPromotionCodeBytes() {
    java.lang.Object ref = promotionCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      promotionCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PROMOTION_TYPE_FIELD_NUMBER = 6;
  private volatile java.lang.Object promotionType_;
  /**
   * <code>string promotion_type = 6;</code>
   * @return The promotionType.
   */
  @java.lang.Override
  public java.lang.String getPromotionType() {
    java.lang.Object ref = promotionType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      promotionType_ = s;
      return s;
    }
  }
  /**
   * <code>string promotion_type = 6;</code>
   * @return The bytes for promotionType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPromotionTypeBytes() {
    java.lang.Object ref = promotionType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      promotionType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ALLOW_OVERLAP_FIELD_NUMBER = 7;
  private boolean allowOverlap_;
  /**
   * <code>bool allow_overlap = 7;</code>
   * @return The allowOverlap.
   */
  @java.lang.Override
  public boolean getAllowOverlap() {
    return allowOverlap_;
  }

  public static final int TRIGGER_TIMES_CUSTOM_FIELD_NUMBER = 8;
  private boolean triggerTimesCustom_;
  /**
   * <code>bool trigger_times_custom = 8;</code>
   * @return The triggerTimesCustom.
   */
  @java.lang.Override
  public boolean getTriggerTimesCustom() {
    return triggerTimesCustom_;
  }

  public static final int TICKET_DISPLAY_FIELD_NUMBER = 9;
  private volatile java.lang.Object ticketDisplay_;
  /**
   * <code>string ticket_display = 9;</code>
   * @return The ticketDisplay.
   */
  @java.lang.Override
  public java.lang.String getTicketDisplay() {
    java.lang.Object ref = ticketDisplay_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      ticketDisplay_ = s;
      return s;
    }
  }
  /**
   * <code>string ticket_display = 9;</code>
   * @return The bytes for ticketDisplay.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTicketDisplayBytes() {
    java.lang.Object ref = ticketDisplay_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      ticketDisplay_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MAX_DISCOUNT_FIELD_NUMBER = 10;
  private double maxDiscount_;
  /**
   * <code>double max_discount = 10;</code>
   * @return The maxDiscount.
   */
  @java.lang.Override
  public double getMaxDiscount() {
    return maxDiscount_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getTypeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, type_);
    }
    if (!getDiscountTypeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, discountType_);
    }
    if (!getNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, name_);
    }
    if (!getPromotionIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, promotionId_);
    }
    if (!getPromotionCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, promotionCode_);
    }
    if (!getPromotionTypeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, promotionType_);
    }
    if (allowOverlap_ != false) {
      output.writeBool(7, allowOverlap_);
    }
    if (triggerTimesCustom_ != false) {
      output.writeBool(8, triggerTimesCustom_);
    }
    if (!getTicketDisplayBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, ticketDisplay_);
    }
    if (maxDiscount_ != 0D) {
      output.writeDouble(10, maxDiscount_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getTypeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, type_);
    }
    if (!getDiscountTypeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, discountType_);
    }
    if (!getNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, name_);
    }
    if (!getPromotionIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, promotionId_);
    }
    if (!getPromotionCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, promotionCode_);
    }
    if (!getPromotionTypeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, promotionType_);
    }
    if (allowOverlap_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(7, allowOverlap_);
    }
    if (triggerTimesCustom_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(8, triggerTimesCustom_);
    }
    if (!getTicketDisplayBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, ticketDisplay_);
    }
    if (maxDiscount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(10, maxDiscount_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo other = (cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo) obj;

    if (!getType()
        .equals(other.getType())) return false;
    if (!getDiscountType()
        .equals(other.getDiscountType())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (!getPromotionId()
        .equals(other.getPromotionId())) return false;
    if (!getPromotionCode()
        .equals(other.getPromotionCode())) return false;
    if (!getPromotionType()
        .equals(other.getPromotionType())) return false;
    if (getAllowOverlap()
        != other.getAllowOverlap()) return false;
    if (getTriggerTimesCustom()
        != other.getTriggerTimesCustom()) return false;
    if (!getTicketDisplay()
        .equals(other.getTicketDisplay())) return false;
    if (java.lang.Double.doubleToLongBits(getMaxDiscount())
        != java.lang.Double.doubleToLongBits(
            other.getMaxDiscount())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getType().hashCode();
    hash = (37 * hash) + DISCOUNT_TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getDiscountType().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + PROMOTION_ID_FIELD_NUMBER;
    hash = (53 * hash) + getPromotionId().hashCode();
    hash = (37 * hash) + PROMOTION_CODE_FIELD_NUMBER;
    hash = (53 * hash) + getPromotionCode().hashCode();
    hash = (37 * hash) + PROMOTION_TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getPromotionType().hashCode();
    hash = (37 * hash) + ALLOW_OVERLAP_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getAllowOverlap());
    hash = (37 * hash) + TRIGGER_TIMES_CUSTOM_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getTriggerTimesCustom());
    hash = (37 * hash) + TICKET_DISPLAY_FIELD_NUMBER;
    hash = (53 * hash) + getTicketDisplay().hashCode();
    hash = (37 * hash) + MAX_DISCOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getMaxDiscount()));
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code eticket_proto.PromotionInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:eticket_proto.PromotionInfo)
      cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_PromotionInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_PromotionInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.class, cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      type_ = "";

      discountType_ = "";

      name_ = "";

      promotionId_ = "";

      promotionCode_ = "";

      promotionType_ = "";

      allowOverlap_ = false;

      triggerTimesCustom_ = false;

      ticketDisplay_ = "";

      maxDiscount_ = 0D;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_PromotionInfo_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo build() {
      cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo buildPartial() {
      cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo result = new cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo(this);
      result.type_ = type_;
      result.discountType_ = discountType_;
      result.name_ = name_;
      result.promotionId_ = promotionId_;
      result.promotionCode_ = promotionCode_;
      result.promotionType_ = promotionType_;
      result.allowOverlap_ = allowOverlap_;
      result.triggerTimesCustom_ = triggerTimesCustom_;
      result.ticketDisplay_ = ticketDisplay_;
      result.maxDiscount_ = maxDiscount_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo other) {
      if (other == cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo.getDefaultInstance()) return this;
      if (!other.getType().isEmpty()) {
        type_ = other.type_;
        onChanged();
      }
      if (!other.getDiscountType().isEmpty()) {
        discountType_ = other.discountType_;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (!other.getPromotionId().isEmpty()) {
        promotionId_ = other.promotionId_;
        onChanged();
      }
      if (!other.getPromotionCode().isEmpty()) {
        promotionCode_ = other.promotionCode_;
        onChanged();
      }
      if (!other.getPromotionType().isEmpty()) {
        promotionType_ = other.promotionType_;
        onChanged();
      }
      if (other.getAllowOverlap() != false) {
        setAllowOverlap(other.getAllowOverlap());
      }
      if (other.getTriggerTimesCustom() != false) {
        setTriggerTimesCustom(other.getTriggerTimesCustom());
      }
      if (!other.getTicketDisplay().isEmpty()) {
        ticketDisplay_ = other.ticketDisplay_;
        onChanged();
      }
      if (other.getMaxDiscount() != 0D) {
        setMaxDiscount(other.getMaxDiscount());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object type_ = "";
    /**
     * <code>string type = 1;</code>
     * @return The type.
     */
    public java.lang.String getType() {
      java.lang.Object ref = type_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        type_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string type = 1;</code>
     * @return The bytes for type.
     */
    public com.google.protobuf.ByteString
        getTypeBytes() {
      java.lang.Object ref = type_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        type_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string type = 1;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      type_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string type = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      
      type_ = getDefaultInstance().getType();
      onChanged();
      return this;
    }
    /**
     * <code>string type = 1;</code>
     * @param value The bytes for type to set.
     * @return This builder for chaining.
     */
    public Builder setTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      type_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object discountType_ = "";
    /**
     * <code>string discount_type = 2;</code>
     * @return The discountType.
     */
    public java.lang.String getDiscountType() {
      java.lang.Object ref = discountType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        discountType_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string discount_type = 2;</code>
     * @return The bytes for discountType.
     */
    public com.google.protobuf.ByteString
        getDiscountTypeBytes() {
      java.lang.Object ref = discountType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        discountType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string discount_type = 2;</code>
     * @param value The discountType to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountType(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      discountType_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string discount_type = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscountType() {
      
      discountType_ = getDefaultInstance().getDiscountType();
      onChanged();
      return this;
    }
    /**
     * <code>string discount_type = 2;</code>
     * @param value The bytes for discountType to set.
     * @return This builder for chaining.
     */
    public Builder setDiscountTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      discountType_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <code>string name = 3;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string name = 3;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string name = 3;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string name = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <code>string name = 3;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object promotionId_ = "";
    /**
     * <code>string promotion_id = 4;</code>
     * @return The promotionId.
     */
    public java.lang.String getPromotionId() {
      java.lang.Object ref = promotionId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        promotionId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string promotion_id = 4;</code>
     * @return The bytes for promotionId.
     */
    public com.google.protobuf.ByteString
        getPromotionIdBytes() {
      java.lang.Object ref = promotionId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        promotionId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string promotion_id = 4;</code>
     * @param value The promotionId to set.
     * @return This builder for chaining.
     */
    public Builder setPromotionId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      promotionId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string promotion_id = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearPromotionId() {
      
      promotionId_ = getDefaultInstance().getPromotionId();
      onChanged();
      return this;
    }
    /**
     * <code>string promotion_id = 4;</code>
     * @param value The bytes for promotionId to set.
     * @return This builder for chaining.
     */
    public Builder setPromotionIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      promotionId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object promotionCode_ = "";
    /**
     * <code>string promotion_code = 5;</code>
     * @return The promotionCode.
     */
    public java.lang.String getPromotionCode() {
      java.lang.Object ref = promotionCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        promotionCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string promotion_code = 5;</code>
     * @return The bytes for promotionCode.
     */
    public com.google.protobuf.ByteString
        getPromotionCodeBytes() {
      java.lang.Object ref = promotionCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        promotionCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string promotion_code = 5;</code>
     * @param value The promotionCode to set.
     * @return This builder for chaining.
     */
    public Builder setPromotionCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      promotionCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string promotion_code = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearPromotionCode() {
      
      promotionCode_ = getDefaultInstance().getPromotionCode();
      onChanged();
      return this;
    }
    /**
     * <code>string promotion_code = 5;</code>
     * @param value The bytes for promotionCode to set.
     * @return This builder for chaining.
     */
    public Builder setPromotionCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      promotionCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object promotionType_ = "";
    /**
     * <code>string promotion_type = 6;</code>
     * @return The promotionType.
     */
    public java.lang.String getPromotionType() {
      java.lang.Object ref = promotionType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        promotionType_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string promotion_type = 6;</code>
     * @return The bytes for promotionType.
     */
    public com.google.protobuf.ByteString
        getPromotionTypeBytes() {
      java.lang.Object ref = promotionType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        promotionType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string promotion_type = 6;</code>
     * @param value The promotionType to set.
     * @return This builder for chaining.
     */
    public Builder setPromotionType(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      promotionType_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string promotion_type = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearPromotionType() {
      
      promotionType_ = getDefaultInstance().getPromotionType();
      onChanged();
      return this;
    }
    /**
     * <code>string promotion_type = 6;</code>
     * @param value The bytes for promotionType to set.
     * @return This builder for chaining.
     */
    public Builder setPromotionTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      promotionType_ = value;
      onChanged();
      return this;
    }

    private boolean allowOverlap_ ;
    /**
     * <code>bool allow_overlap = 7;</code>
     * @return The allowOverlap.
     */
    @java.lang.Override
    public boolean getAllowOverlap() {
      return allowOverlap_;
    }
    /**
     * <code>bool allow_overlap = 7;</code>
     * @param value The allowOverlap to set.
     * @return This builder for chaining.
     */
    public Builder setAllowOverlap(boolean value) {
      
      allowOverlap_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>bool allow_overlap = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearAllowOverlap() {
      
      allowOverlap_ = false;
      onChanged();
      return this;
    }

    private boolean triggerTimesCustom_ ;
    /**
     * <code>bool trigger_times_custom = 8;</code>
     * @return The triggerTimesCustom.
     */
    @java.lang.Override
    public boolean getTriggerTimesCustom() {
      return triggerTimesCustom_;
    }
    /**
     * <code>bool trigger_times_custom = 8;</code>
     * @param value The triggerTimesCustom to set.
     * @return This builder for chaining.
     */
    public Builder setTriggerTimesCustom(boolean value) {
      
      triggerTimesCustom_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>bool trigger_times_custom = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearTriggerTimesCustom() {
      
      triggerTimesCustom_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object ticketDisplay_ = "";
    /**
     * <code>string ticket_display = 9;</code>
     * @return The ticketDisplay.
     */
    public java.lang.String getTicketDisplay() {
      java.lang.Object ref = ticketDisplay_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        ticketDisplay_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string ticket_display = 9;</code>
     * @return The bytes for ticketDisplay.
     */
    public com.google.protobuf.ByteString
        getTicketDisplayBytes() {
      java.lang.Object ref = ticketDisplay_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ticketDisplay_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string ticket_display = 9;</code>
     * @param value The ticketDisplay to set.
     * @return This builder for chaining.
     */
    public Builder setTicketDisplay(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      ticketDisplay_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string ticket_display = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearTicketDisplay() {
      
      ticketDisplay_ = getDefaultInstance().getTicketDisplay();
      onChanged();
      return this;
    }
    /**
     * <code>string ticket_display = 9;</code>
     * @param value The bytes for ticketDisplay to set.
     * @return This builder for chaining.
     */
    public Builder setTicketDisplayBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      ticketDisplay_ = value;
      onChanged();
      return this;
    }

    private double maxDiscount_ ;
    /**
     * <code>double max_discount = 10;</code>
     * @return The maxDiscount.
     */
    @java.lang.Override
    public double getMaxDiscount() {
      return maxDiscount_;
    }
    /**
     * <code>double max_discount = 10;</code>
     * @param value The maxDiscount to set.
     * @return This builder for chaining.
     */
    public Builder setMaxDiscount(double value) {
      
      maxDiscount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double max_discount = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearMaxDiscount() {
      
      maxDiscount_ = 0D;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:eticket_proto.PromotionInfo)
  }

  // @@protoc_insertion_point(class_scope:eticket_proto.PromotionInfo)
  private static final cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo();
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PromotionInfo>
      PARSER = new com.google.protobuf.AbstractParser<PromotionInfo>() {
    @java.lang.Override
    public PromotionInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new PromotionInfo(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<PromotionInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PromotionInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

