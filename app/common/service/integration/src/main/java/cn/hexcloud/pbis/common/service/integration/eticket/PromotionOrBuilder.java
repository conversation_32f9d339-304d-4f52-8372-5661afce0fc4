// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

public interface PromotionOrBuilder extends
    // @@protoc_insertion_point(interface_extends:eticket_proto.Promotion)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>.eticket_proto.PromotionInfo promotionInfo = 1;</code>
   * @return Whether the promotionInfo field is set.
   */
  boolean hasPromotionInfo();
  /**
   * <code>.eticket_proto.PromotionInfo promotionInfo = 1;</code>
   * @return The promotionInfo.
   */
  cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfo getPromotionInfo();
  /**
   * <code>.eticket_proto.PromotionInfo promotionInfo = 1;</code>
   */
  cn.hexcloud.pbis.common.service.integration.eticket.PromotionInfoOrBuilder getPromotionInfoOrBuilder();

  /**
   * <code>.eticket_proto.PromotionSource source = 2;</code>
   * @return Whether the source field is set.
   */
  boolean hasSource();
  /**
   * <code>.eticket_proto.PromotionSource source = 2;</code>
   * @return The source.
   */
  cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource getSource();
  /**
   * <code>.eticket_proto.PromotionSource source = 2;</code>
   */
  cn.hexcloud.pbis.common.service.integration.eticket.PromotionSourceOrBuilder getSourceOrBuilder();

  /**
   * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct> 
      getProductsList();
  /**
   * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
   */
  cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct getProducts(int index);
  /**
   * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
   */
  int getProductsCount();
  /**
   * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder> 
      getProductsOrBuilderList();
  /**
   * <code>repeated .eticket_proto.PromotionProduct products = 3;</code>
   */
  cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder getProductsOrBuilder(
      int index);
}
