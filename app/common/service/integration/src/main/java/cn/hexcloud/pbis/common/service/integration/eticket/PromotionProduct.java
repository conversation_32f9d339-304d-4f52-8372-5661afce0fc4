// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

/**
 * Protobuf type {@code eticket_proto.PromotionProduct}
 */
public final class PromotionProduct extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:eticket_proto.PromotionProduct)
    PromotionProductOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PromotionProduct.newBuilder() to construct.
  private PromotionProduct(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PromotionProduct() {
    keyId_ = "";
    accies_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    type_ = "";
    method_ = "";
    accessories_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PromotionProduct();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private PromotionProduct(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 9: {

            price_ = input.readDouble();
            break;
          }
          case 17: {

            amt_ = input.readDouble();
            break;
          }
          case 25: {

            accAmt_ = input.readDouble();
            break;
          }
          case 32: {

            qty_ = input.readInt32();
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            keyId_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              accies_ = new com.google.protobuf.LazyStringArrayList();
              mutable_bitField0_ |= 0x00000001;
            }
            accies_.add(s);
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            type_ = s;
            break;
          }
          case 65: {

            discount_ = input.readDouble();
            break;
          }
          case 73: {

            freeAmt_ = input.readDouble();
            break;
          }
          case 82: {
            java.lang.String s = input.readStringRequireUtf8();

            method_ = s;
            break;
          }
          case 90: {
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              accessories_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct>();
              mutable_bitField0_ |= 0x00000002;
            }
            accessories_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.parser(), extensionRegistry));
            break;
          }
          case 101: {

            weight_ = input.readFloat();
            break;
          }
          case 104: {

            hasWeight_ = input.readBool();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        accies_ = accies_.getUnmodifiableView();
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        accessories_ = java.util.Collections.unmodifiableList(accessories_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_PromotionProduct_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_PromotionProduct_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.class, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder.class);
  }

  public static final int PRICE_FIELD_NUMBER = 1;
  private double price_;
  /**
   * <code>double price = 1;</code>
   * @return The price.
   */
  @java.lang.Override
  public double getPrice() {
    return price_;
  }

  public static final int AMT_FIELD_NUMBER = 2;
  private double amt_;
  /**
   * <code>double amt = 2;</code>
   * @return The amt.
   */
  @java.lang.Override
  public double getAmt() {
    return amt_;
  }

  public static final int ACCAMT_FIELD_NUMBER = 3;
  private double accAmt_;
  /**
   * <code>double accAmt = 3;</code>
   * @return The accAmt.
   */
  @java.lang.Override
  public double getAccAmt() {
    return accAmt_;
  }

  public static final int QTY_FIELD_NUMBER = 4;
  private int qty_;
  /**
   * <code>int32 qty = 4;</code>
   * @return The qty.
   */
  @java.lang.Override
  public int getQty() {
    return qty_;
  }

  public static final int KEY_ID_FIELD_NUMBER = 5;
  private volatile java.lang.Object keyId_;
  /**
   * <code>string key_id = 5;</code>
   * @return The keyId.
   */
  @java.lang.Override
  public java.lang.String getKeyId() {
    java.lang.Object ref = keyId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      keyId_ = s;
      return s;
    }
  }
  /**
   * <code>string key_id = 5;</code>
   * @return The bytes for keyId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getKeyIdBytes() {
    java.lang.Object ref = keyId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      keyId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACCIES_FIELD_NUMBER = 6;
  private com.google.protobuf.LazyStringList accies_;
  /**
   * <code>repeated string accies = 6;</code>
   * @return A list containing the accies.
   */
  public com.google.protobuf.ProtocolStringList
      getAcciesList() {
    return accies_;
  }
  /**
   * <code>repeated string accies = 6;</code>
   * @return The count of accies.
   */
  public int getAcciesCount() {
    return accies_.size();
  }
  /**
   * <code>repeated string accies = 6;</code>
   * @param index The index of the element to return.
   * @return The accies at the given index.
   */
  public java.lang.String getAccies(int index) {
    return accies_.get(index);
  }
  /**
   * <code>repeated string accies = 6;</code>
   * @param index The index of the value to return.
   * @return The bytes of the accies at the given index.
   */
  public com.google.protobuf.ByteString
      getAcciesBytes(int index) {
    return accies_.getByteString(index);
  }

  public static final int TYPE_FIELD_NUMBER = 7;
  private volatile java.lang.Object type_;
  /**
   * <code>string type = 7;</code>
   * @return The type.
   */
  @java.lang.Override
  public java.lang.String getType() {
    java.lang.Object ref = type_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      type_ = s;
      return s;
    }
  }
  /**
   * <code>string type = 7;</code>
   * @return The bytes for type.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTypeBytes() {
    java.lang.Object ref = type_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      type_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DISCOUNT_FIELD_NUMBER = 8;
  private double discount_;
  /**
   * <code>double discount = 8;</code>
   * @return The discount.
   */
  @java.lang.Override
  public double getDiscount() {
    return discount_;
  }

  public static final int FREE_AMT_FIELD_NUMBER = 9;
  private double freeAmt_;
  /**
   * <code>double free_amt = 9;</code>
   * @return The freeAmt.
   */
  @java.lang.Override
  public double getFreeAmt() {
    return freeAmt_;
  }

  public static final int METHOD_FIELD_NUMBER = 10;
  private volatile java.lang.Object method_;
  /**
   * <code>string method = 10;</code>
   * @return The method.
   */
  @java.lang.Override
  public java.lang.String getMethod() {
    java.lang.Object ref = method_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      method_ = s;
      return s;
    }
  }
  /**
   * <code>string method = 10;</code>
   * @return The bytes for method.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMethodBytes() {
    java.lang.Object ref = method_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      method_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACCESSORIES_FIELD_NUMBER = 11;
  private java.util.List<cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct> accessories_;
  /**
   * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct> getAccessoriesList() {
    return accessories_;
  }
  /**
   * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder> 
      getAccessoriesOrBuilderList() {
    return accessories_;
  }
  /**
   * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
   */
  @java.lang.Override
  public int getAccessoriesCount() {
    return accessories_.size();
  }
  /**
   * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct getAccessories(int index) {
    return accessories_.get(index);
  }
  /**
   * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder getAccessoriesOrBuilder(
      int index) {
    return accessories_.get(index);
  }

  public static final int WEIGHT_FIELD_NUMBER = 12;
  private float weight_;
  /**
   * <pre>
   *重量(kg)
   * </pre>
   *
   * <code>float weight = 12;</code>
   * @return The weight.
   */
  @java.lang.Override
  public float getWeight() {
    return weight_;
  }

  public static final int HAS_WEIGHT_FIELD_NUMBER = 13;
  private boolean hasWeight_;
  /**
   * <pre>
   *是否称重商品
   * </pre>
   *
   * <code>bool has_weight = 13;</code>
   * @return The hasWeight.
   */
  @java.lang.Override
  public boolean getHasWeight() {
    return hasWeight_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (price_ != 0D) {
      output.writeDouble(1, price_);
    }
    if (amt_ != 0D) {
      output.writeDouble(2, amt_);
    }
    if (accAmt_ != 0D) {
      output.writeDouble(3, accAmt_);
    }
    if (qty_ != 0) {
      output.writeInt32(4, qty_);
    }
    if (!getKeyIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, keyId_);
    }
    for (int i = 0; i < accies_.size(); i++) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, accies_.getRaw(i));
    }
    if (!getTypeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, type_);
    }
    if (discount_ != 0D) {
      output.writeDouble(8, discount_);
    }
    if (freeAmt_ != 0D) {
      output.writeDouble(9, freeAmt_);
    }
    if (!getMethodBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, method_);
    }
    for (int i = 0; i < accessories_.size(); i++) {
      output.writeMessage(11, accessories_.get(i));
    }
    if (weight_ != 0F) {
      output.writeFloat(12, weight_);
    }
    if (hasWeight_ != false) {
      output.writeBool(13, hasWeight_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (price_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(1, price_);
    }
    if (amt_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(2, amt_);
    }
    if (accAmt_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(3, accAmt_);
    }
    if (qty_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, qty_);
    }
    if (!getKeyIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, keyId_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < accies_.size(); i++) {
        dataSize += computeStringSizeNoTag(accies_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getAcciesList().size();
    }
    if (!getTypeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, type_);
    }
    if (discount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(8, discount_);
    }
    if (freeAmt_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(9, freeAmt_);
    }
    if (!getMethodBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, method_);
    }
    for (int i = 0; i < accessories_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(11, accessories_.get(i));
    }
    if (weight_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(12, weight_);
    }
    if (hasWeight_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(13, hasWeight_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct other = (cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct) obj;

    if (java.lang.Double.doubleToLongBits(getPrice())
        != java.lang.Double.doubleToLongBits(
            other.getPrice())) return false;
    if (java.lang.Double.doubleToLongBits(getAmt())
        != java.lang.Double.doubleToLongBits(
            other.getAmt())) return false;
    if (java.lang.Double.doubleToLongBits(getAccAmt())
        != java.lang.Double.doubleToLongBits(
            other.getAccAmt())) return false;
    if (getQty()
        != other.getQty()) return false;
    if (!getKeyId()
        .equals(other.getKeyId())) return false;
    if (!getAcciesList()
        .equals(other.getAcciesList())) return false;
    if (!getType()
        .equals(other.getType())) return false;
    if (java.lang.Double.doubleToLongBits(getDiscount())
        != java.lang.Double.doubleToLongBits(
            other.getDiscount())) return false;
    if (java.lang.Double.doubleToLongBits(getFreeAmt())
        != java.lang.Double.doubleToLongBits(
            other.getFreeAmt())) return false;
    if (!getMethod()
        .equals(other.getMethod())) return false;
    if (!getAccessoriesList()
        .equals(other.getAccessoriesList())) return false;
    if (java.lang.Float.floatToIntBits(getWeight())
        != java.lang.Float.floatToIntBits(
            other.getWeight())) return false;
    if (getHasWeight()
        != other.getHasWeight()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + PRICE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPrice()));
    hash = (37 * hash) + AMT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getAmt()));
    hash = (37 * hash) + ACCAMT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getAccAmt()));
    hash = (37 * hash) + QTY_FIELD_NUMBER;
    hash = (53 * hash) + getQty();
    hash = (37 * hash) + KEY_ID_FIELD_NUMBER;
    hash = (53 * hash) + getKeyId().hashCode();
    if (getAcciesCount() > 0) {
      hash = (37 * hash) + ACCIES_FIELD_NUMBER;
      hash = (53 * hash) + getAcciesList().hashCode();
    }
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getType().hashCode();
    hash = (37 * hash) + DISCOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getDiscount()));
    hash = (37 * hash) + FREE_AMT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getFreeAmt()));
    hash = (37 * hash) + METHOD_FIELD_NUMBER;
    hash = (53 * hash) + getMethod().hashCode();
    if (getAccessoriesCount() > 0) {
      hash = (37 * hash) + ACCESSORIES_FIELD_NUMBER;
      hash = (53 * hash) + getAccessoriesList().hashCode();
    }
    hash = (37 * hash) + WEIGHT_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getWeight());
    hash = (37 * hash) + HAS_WEIGHT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getHasWeight());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code eticket_proto.PromotionProduct}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:eticket_proto.PromotionProduct)
      cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_PromotionProduct_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_PromotionProduct_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.class, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getAccessoriesFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      price_ = 0D;

      amt_ = 0D;

      accAmt_ = 0D;

      qty_ = 0;

      keyId_ = "";

      accies_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      bitField0_ = (bitField0_ & ~0x00000001);
      type_ = "";

      discount_ = 0D;

      freeAmt_ = 0D;

      method_ = "";

      if (accessoriesBuilder_ == null) {
        accessories_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
      } else {
        accessoriesBuilder_.clear();
      }
      weight_ = 0F;

      hasWeight_ = false;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_PromotionProduct_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct build() {
      cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct buildPartial() {
      cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct result = new cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct(this);
      int from_bitField0_ = bitField0_;
      result.price_ = price_;
      result.amt_ = amt_;
      result.accAmt_ = accAmt_;
      result.qty_ = qty_;
      result.keyId_ = keyId_;
      if (((bitField0_ & 0x00000001) != 0)) {
        accies_ = accies_.getUnmodifiableView();
        bitField0_ = (bitField0_ & ~0x00000001);
      }
      result.accies_ = accies_;
      result.type_ = type_;
      result.discount_ = discount_;
      result.freeAmt_ = freeAmt_;
      result.method_ = method_;
      if (accessoriesBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          accessories_ = java.util.Collections.unmodifiableList(accessories_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.accessories_ = accessories_;
      } else {
        result.accessories_ = accessoriesBuilder_.build();
      }
      result.weight_ = weight_;
      result.hasWeight_ = hasWeight_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct other) {
      if (other == cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.getDefaultInstance()) return this;
      if (other.getPrice() != 0D) {
        setPrice(other.getPrice());
      }
      if (other.getAmt() != 0D) {
        setAmt(other.getAmt());
      }
      if (other.getAccAmt() != 0D) {
        setAccAmt(other.getAccAmt());
      }
      if (other.getQty() != 0) {
        setQty(other.getQty());
      }
      if (!other.getKeyId().isEmpty()) {
        keyId_ = other.keyId_;
        onChanged();
      }
      if (!other.accies_.isEmpty()) {
        if (accies_.isEmpty()) {
          accies_ = other.accies_;
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          ensureAcciesIsMutable();
          accies_.addAll(other.accies_);
        }
        onChanged();
      }
      if (!other.getType().isEmpty()) {
        type_ = other.type_;
        onChanged();
      }
      if (other.getDiscount() != 0D) {
        setDiscount(other.getDiscount());
      }
      if (other.getFreeAmt() != 0D) {
        setFreeAmt(other.getFreeAmt());
      }
      if (!other.getMethod().isEmpty()) {
        method_ = other.method_;
        onChanged();
      }
      if (accessoriesBuilder_ == null) {
        if (!other.accessories_.isEmpty()) {
          if (accessories_.isEmpty()) {
            accessories_ = other.accessories_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureAccessoriesIsMutable();
            accessories_.addAll(other.accessories_);
          }
          onChanged();
        }
      } else {
        if (!other.accessories_.isEmpty()) {
          if (accessoriesBuilder_.isEmpty()) {
            accessoriesBuilder_.dispose();
            accessoriesBuilder_ = null;
            accessories_ = other.accessories_;
            bitField0_ = (bitField0_ & ~0x00000002);
            accessoriesBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getAccessoriesFieldBuilder() : null;
          } else {
            accessoriesBuilder_.addAllMessages(other.accessories_);
          }
        }
      }
      if (other.getWeight() != 0F) {
        setWeight(other.getWeight());
      }
      if (other.getHasWeight() != false) {
        setHasWeight(other.getHasWeight());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private double price_ ;
    /**
     * <code>double price = 1;</code>
     * @return The price.
     */
    @java.lang.Override
    public double getPrice() {
      return price_;
    }
    /**
     * <code>double price = 1;</code>
     * @param value The price to set.
     * @return This builder for chaining.
     */
    public Builder setPrice(double value) {
      
      price_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double price = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearPrice() {
      
      price_ = 0D;
      onChanged();
      return this;
    }

    private double amt_ ;
    /**
     * <code>double amt = 2;</code>
     * @return The amt.
     */
    @java.lang.Override
    public double getAmt() {
      return amt_;
    }
    /**
     * <code>double amt = 2;</code>
     * @param value The amt to set.
     * @return This builder for chaining.
     */
    public Builder setAmt(double value) {
      
      amt_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double amt = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearAmt() {
      
      amt_ = 0D;
      onChanged();
      return this;
    }

    private double accAmt_ ;
    /**
     * <code>double accAmt = 3;</code>
     * @return The accAmt.
     */
    @java.lang.Override
    public double getAccAmt() {
      return accAmt_;
    }
    /**
     * <code>double accAmt = 3;</code>
     * @param value The accAmt to set.
     * @return This builder for chaining.
     */
    public Builder setAccAmt(double value) {
      
      accAmt_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double accAmt = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearAccAmt() {
      
      accAmt_ = 0D;
      onChanged();
      return this;
    }

    private int qty_ ;
    /**
     * <code>int32 qty = 4;</code>
     * @return The qty.
     */
    @java.lang.Override
    public int getQty() {
      return qty_;
    }
    /**
     * <code>int32 qty = 4;</code>
     * @param value The qty to set.
     * @return This builder for chaining.
     */
    public Builder setQty(int value) {
      
      qty_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>int32 qty = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearQty() {
      
      qty_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object keyId_ = "";
    /**
     * <code>string key_id = 5;</code>
     * @return The keyId.
     */
    public java.lang.String getKeyId() {
      java.lang.Object ref = keyId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        keyId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string key_id = 5;</code>
     * @return The bytes for keyId.
     */
    public com.google.protobuf.ByteString
        getKeyIdBytes() {
      java.lang.Object ref = keyId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        keyId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string key_id = 5;</code>
     * @param value The keyId to set.
     * @return This builder for chaining.
     */
    public Builder setKeyId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      keyId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string key_id = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearKeyId() {
      
      keyId_ = getDefaultInstance().getKeyId();
      onChanged();
      return this;
    }
    /**
     * <code>string key_id = 5;</code>
     * @param value The bytes for keyId to set.
     * @return This builder for chaining.
     */
    public Builder setKeyIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      keyId_ = value;
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringList accies_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    private void ensureAcciesIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        accies_ = new com.google.protobuf.LazyStringArrayList(accies_);
        bitField0_ |= 0x00000001;
       }
    }
    /**
     * <code>repeated string accies = 6;</code>
     * @return A list containing the accies.
     */
    public com.google.protobuf.ProtocolStringList
        getAcciesList() {
      return accies_.getUnmodifiableView();
    }
    /**
     * <code>repeated string accies = 6;</code>
     * @return The count of accies.
     */
    public int getAcciesCount() {
      return accies_.size();
    }
    /**
     * <code>repeated string accies = 6;</code>
     * @param index The index of the element to return.
     * @return The accies at the given index.
     */
    public java.lang.String getAccies(int index) {
      return accies_.get(index);
    }
    /**
     * <code>repeated string accies = 6;</code>
     * @param index The index of the value to return.
     * @return The bytes of the accies at the given index.
     */
    public com.google.protobuf.ByteString
        getAcciesBytes(int index) {
      return accies_.getByteString(index);
    }
    /**
     * <code>repeated string accies = 6;</code>
     * @param index The index to set the value at.
     * @param value The accies to set.
     * @return This builder for chaining.
     */
    public Builder setAccies(
        int index, java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  ensureAcciesIsMutable();
      accies_.set(index, value);
      onChanged();
      return this;
    }
    /**
     * <code>repeated string accies = 6;</code>
     * @param value The accies to add.
     * @return This builder for chaining.
     */
    public Builder addAccies(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  ensureAcciesIsMutable();
      accies_.add(value);
      onChanged();
      return this;
    }
    /**
     * <code>repeated string accies = 6;</code>
     * @param values The accies to add.
     * @return This builder for chaining.
     */
    public Builder addAllAccies(
        java.lang.Iterable<java.lang.String> values) {
      ensureAcciesIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, accies_);
      onChanged();
      return this;
    }
    /**
     * <code>repeated string accies = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearAccies() {
      accies_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>repeated string accies = 6;</code>
     * @param value The bytes of the accies to add.
     * @return This builder for chaining.
     */
    public Builder addAcciesBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      ensureAcciesIsMutable();
      accies_.add(value);
      onChanged();
      return this;
    }

    private java.lang.Object type_ = "";
    /**
     * <code>string type = 7;</code>
     * @return The type.
     */
    public java.lang.String getType() {
      java.lang.Object ref = type_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        type_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string type = 7;</code>
     * @return The bytes for type.
     */
    public com.google.protobuf.ByteString
        getTypeBytes() {
      java.lang.Object ref = type_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        type_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string type = 7;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      type_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string type = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      
      type_ = getDefaultInstance().getType();
      onChanged();
      return this;
    }
    /**
     * <code>string type = 7;</code>
     * @param value The bytes for type to set.
     * @return This builder for chaining.
     */
    public Builder setTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      type_ = value;
      onChanged();
      return this;
    }

    private double discount_ ;
    /**
     * <code>double discount = 8;</code>
     * @return The discount.
     */
    @java.lang.Override
    public double getDiscount() {
      return discount_;
    }
    /**
     * <code>double discount = 8;</code>
     * @param value The discount to set.
     * @return This builder for chaining.
     */
    public Builder setDiscount(double value) {
      
      discount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double discount = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscount() {
      
      discount_ = 0D;
      onChanged();
      return this;
    }

    private double freeAmt_ ;
    /**
     * <code>double free_amt = 9;</code>
     * @return The freeAmt.
     */
    @java.lang.Override
    public double getFreeAmt() {
      return freeAmt_;
    }
    /**
     * <code>double free_amt = 9;</code>
     * @param value The freeAmt to set.
     * @return This builder for chaining.
     */
    public Builder setFreeAmt(double value) {
      
      freeAmt_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double free_amt = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearFreeAmt() {
      
      freeAmt_ = 0D;
      onChanged();
      return this;
    }

    private java.lang.Object method_ = "";
    /**
     * <code>string method = 10;</code>
     * @return The method.
     */
    public java.lang.String getMethod() {
      java.lang.Object ref = method_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        method_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string method = 10;</code>
     * @return The bytes for method.
     */
    public com.google.protobuf.ByteString
        getMethodBytes() {
      java.lang.Object ref = method_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        method_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string method = 10;</code>
     * @param value The method to set.
     * @return This builder for chaining.
     */
    public Builder setMethod(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      method_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string method = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearMethod() {
      
      method_ = getDefaultInstance().getMethod();
      onChanged();
      return this;
    }
    /**
     * <code>string method = 10;</code>
     * @param value The bytes for method to set.
     * @return This builder for chaining.
     */
    public Builder setMethodBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      method_ = value;
      onChanged();
      return this;
    }

    private java.util.List<cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct> accessories_ =
      java.util.Collections.emptyList();
    private void ensureAccessoriesIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        accessories_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct>(accessories_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder> accessoriesBuilder_;

    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct> getAccessoriesList() {
      if (accessoriesBuilder_ == null) {
        return java.util.Collections.unmodifiableList(accessories_);
      } else {
        return accessoriesBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public int getAccessoriesCount() {
      if (accessoriesBuilder_ == null) {
        return accessories_.size();
      } else {
        return accessoriesBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct getAccessories(int index) {
      if (accessoriesBuilder_ == null) {
        return accessories_.get(index);
      } else {
        return accessoriesBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public Builder setAccessories(
        int index, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct value) {
      if (accessoriesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAccessoriesIsMutable();
        accessories_.set(index, value);
        onChanged();
      } else {
        accessoriesBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public Builder setAccessories(
        int index, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder builderForValue) {
      if (accessoriesBuilder_ == null) {
        ensureAccessoriesIsMutable();
        accessories_.set(index, builderForValue.build());
        onChanged();
      } else {
        accessoriesBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public Builder addAccessories(cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct value) {
      if (accessoriesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAccessoriesIsMutable();
        accessories_.add(value);
        onChanged();
      } else {
        accessoriesBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public Builder addAccessories(
        int index, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct value) {
      if (accessoriesBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureAccessoriesIsMutable();
        accessories_.add(index, value);
        onChanged();
      } else {
        accessoriesBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public Builder addAccessories(
        cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder builderForValue) {
      if (accessoriesBuilder_ == null) {
        ensureAccessoriesIsMutable();
        accessories_.add(builderForValue.build());
        onChanged();
      } else {
        accessoriesBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public Builder addAccessories(
        int index, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder builderForValue) {
      if (accessoriesBuilder_ == null) {
        ensureAccessoriesIsMutable();
        accessories_.add(index, builderForValue.build());
        onChanged();
      } else {
        accessoriesBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public Builder addAllAccessories(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct> values) {
      if (accessoriesBuilder_ == null) {
        ensureAccessoriesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, accessories_);
        onChanged();
      } else {
        accessoriesBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public Builder clearAccessories() {
      if (accessoriesBuilder_ == null) {
        accessories_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        accessoriesBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public Builder removeAccessories(int index) {
      if (accessoriesBuilder_ == null) {
        ensureAccessoriesIsMutable();
        accessories_.remove(index);
        onChanged();
      } else {
        accessoriesBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder getAccessoriesBuilder(
        int index) {
      return getAccessoriesFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder getAccessoriesOrBuilder(
        int index) {
      if (accessoriesBuilder_ == null) {
        return accessories_.get(index);  } else {
        return accessoriesBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder> 
         getAccessoriesOrBuilderList() {
      if (accessoriesBuilder_ != null) {
        return accessoriesBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(accessories_);
      }
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder addAccessoriesBuilder() {
      return getAccessoriesFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.getDefaultInstance());
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder addAccessoriesBuilder(
        int index) {
      return getAccessoriesFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.getDefaultInstance());
    }
    /**
     * <code>repeated .eticket_proto.PromotionProduct accessories = 11;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder> 
         getAccessoriesBuilderList() {
      return getAccessoriesFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder> 
        getAccessoriesFieldBuilder() {
      if (accessoriesBuilder_ == null) {
        accessoriesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct.Builder, cn.hexcloud.pbis.common.service.integration.eticket.PromotionProductOrBuilder>(
                accessories_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        accessories_ = null;
      }
      return accessoriesBuilder_;
    }

    private float weight_ ;
    /**
     * <pre>
     *重量(kg)
     * </pre>
     *
     * <code>float weight = 12;</code>
     * @return The weight.
     */
    @java.lang.Override
    public float getWeight() {
      return weight_;
    }
    /**
     * <pre>
     *重量(kg)
     * </pre>
     *
     * <code>float weight = 12;</code>
     * @param value The weight to set.
     * @return This builder for chaining.
     */
    public Builder setWeight(float value) {
      
      weight_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *重量(kg)
     * </pre>
     *
     * <code>float weight = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearWeight() {
      
      weight_ = 0F;
      onChanged();
      return this;
    }

    private boolean hasWeight_ ;
    /**
     * <pre>
     *是否称重商品
     * </pre>
     *
     * <code>bool has_weight = 13;</code>
     * @return The hasWeight.
     */
    @java.lang.Override
    public boolean getHasWeight() {
      return hasWeight_;
    }
    /**
     * <pre>
     *是否称重商品
     * </pre>
     *
     * <code>bool has_weight = 13;</code>
     * @param value The hasWeight to set.
     * @return This builder for chaining.
     */
    public Builder setHasWeight(boolean value) {
      
      hasWeight_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *是否称重商品
     * </pre>
     *
     * <code>bool has_weight = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearHasWeight() {
      
      hasWeight_ = false;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:eticket_proto.PromotionProduct)
  }

  // @@protoc_insertion_point(class_scope:eticket_proto.PromotionProduct)
  private static final cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct();
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PromotionProduct>
      PARSER = new com.google.protobuf.AbstractParser<PromotionProduct>() {
    @java.lang.Override
    public PromotionProduct parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new PromotionProduct(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<PromotionProduct> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PromotionProduct> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.PromotionProduct getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

