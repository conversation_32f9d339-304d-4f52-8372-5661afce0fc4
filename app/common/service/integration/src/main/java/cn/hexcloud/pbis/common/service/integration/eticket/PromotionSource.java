// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

/**
 * Protobuf type {@code eticket_proto.PromotionSource}
 */
public final class PromotionSource extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:eticket_proto.PromotionSource)
    PromotionSourceOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PromotionSource.newBuilder() to construct.
  private PromotionSource(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PromotionSource() {
    fired_ = com.google.protobuf.LazyStringArrayList.EMPTY;
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PromotionSource();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private PromotionSource(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            trigger_ = input.readInt32();
            break;
          }
          case 17: {

            discount_ = input.readDouble();
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              fired_ = new com.google.protobuf.LazyStringArrayList();
              mutable_bitField0_ |= 0x00000001;
            }
            fired_.add(s);
            break;
          }
          case 33: {

            merchantDiscount_ = input.readDouble();
            break;
          }
          case 41: {

            platformDiscount_ = input.readDouble();
            break;
          }
          case 49: {

            storeDiscount_ = input.readDouble();
            break;
          }
          case 57: {

            cost_ = input.readDouble();
            break;
          }
          case 65: {

            tpAllowance_ = input.readDouble();
            break;
          }
          case 73: {

            merchantAllowance_ = input.readDouble();
            break;
          }
          case 81: {

            platformAllowance_ = input.readDouble();
            break;
          }
          case 89: {

            realAmount_ = input.readDouble();
            break;
          }
          case 97: {

            transferAmount_ = input.readDouble();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        fired_ = fired_.getUnmodifiableView();
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_PromotionSource_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_PromotionSource_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.class, cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.Builder.class);
  }

  public static final int TRIGGER_FIELD_NUMBER = 1;
  private int trigger_;
  /**
   * <code>int32 trigger = 1;</code>
   * @return The trigger.
   */
  @java.lang.Override
  public int getTrigger() {
    return trigger_;
  }

  public static final int DISCOUNT_FIELD_NUMBER = 2;
  private double discount_;
  /**
   * <code>double discount = 2;</code>
   * @return The discount.
   */
  @java.lang.Override
  public double getDiscount() {
    return discount_;
  }

  public static final int FIRED_FIELD_NUMBER = 3;
  private com.google.protobuf.LazyStringList fired_;
  /**
   * <code>repeated string fired = 3;</code>
   * @return A list containing the fired.
   */
  public com.google.protobuf.ProtocolStringList
      getFiredList() {
    return fired_;
  }
  /**
   * <code>repeated string fired = 3;</code>
   * @return The count of fired.
   */
  public int getFiredCount() {
    return fired_.size();
  }
  /**
   * <code>repeated string fired = 3;</code>
   * @param index The index of the element to return.
   * @return The fired at the given index.
   */
  public java.lang.String getFired(int index) {
    return fired_.get(index);
  }
  /**
   * <code>repeated string fired = 3;</code>
   * @param index The index of the value to return.
   * @return The bytes of the fired at the given index.
   */
  public com.google.protobuf.ByteString
      getFiredBytes(int index) {
    return fired_.getByteString(index);
  }

  public static final int MERCHANT_DISCOUNT_FIELD_NUMBER = 4;
  private double merchantDiscount_;
  /**
   * <code>double merchant_discount = 4;</code>
   * @return The merchantDiscount.
   */
  @java.lang.Override
  public double getMerchantDiscount() {
    return merchantDiscount_;
  }

  public static final int PLATFORM_DISCOUNT_FIELD_NUMBER = 5;
  private double platformDiscount_;
  /**
   * <code>double platform_discount = 5;</code>
   * @return The platformDiscount.
   */
  @java.lang.Override
  public double getPlatformDiscount() {
    return platformDiscount_;
  }

  public static final int STORE_DISCOUNT_FIELD_NUMBER = 6;
  private double storeDiscount_;
  /**
   * <code>double store_discount = 6;</code>
   * @return The storeDiscount.
   */
  @java.lang.Override
  public double getStoreDiscount() {
    return storeDiscount_;
  }

  public static final int COST_FIELD_NUMBER = 7;
  private double cost_;
  /**
   * <code>double cost = 7;</code>
   * @return The cost.
   */
  @java.lang.Override
  public double getCost() {
    return cost_;
  }

  public static final int TP_ALLOWANCE_FIELD_NUMBER = 8;
  private double tpAllowance_;
  /**
   * <code>double tp_allowance = 8;</code>
   * @return The tpAllowance.
   */
  @java.lang.Override
  public double getTpAllowance() {
    return tpAllowance_;
  }

  public static final int MERCHANT_ALLOWANCE_FIELD_NUMBER = 9;
  private double merchantAllowance_;
  /**
   * <code>double merchant_allowance = 9;</code>
   * @return The merchantAllowance.
   */
  @java.lang.Override
  public double getMerchantAllowance() {
    return merchantAllowance_;
  }

  public static final int PLATFORM_ALLOWANCE_FIELD_NUMBER = 10;
  private double platformAllowance_;
  /**
   * <code>double platform_allowance = 10;</code>
   * @return The platformAllowance.
   */
  @java.lang.Override
  public double getPlatformAllowance() {
    return platformAllowance_;
  }

  public static final int REAL_AMOUNT_FIELD_NUMBER = 11;
  private double realAmount_;
  /**
   * <code>double real_amount = 11;</code>
   * @return The realAmount.
   */
  @java.lang.Override
  public double getRealAmount() {
    return realAmount_;
  }

  public static final int TRANSFER_AMOUNT_FIELD_NUMBER = 12;
  private double transferAmount_;
  /**
   * <code>double transfer_amount = 12;</code>
   * @return The transferAmount.
   */
  @java.lang.Override
  public double getTransferAmount() {
    return transferAmount_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (trigger_ != 0) {
      output.writeInt32(1, trigger_);
    }
    if (discount_ != 0D) {
      output.writeDouble(2, discount_);
    }
    for (int i = 0; i < fired_.size(); i++) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, fired_.getRaw(i));
    }
    if (merchantDiscount_ != 0D) {
      output.writeDouble(4, merchantDiscount_);
    }
    if (platformDiscount_ != 0D) {
      output.writeDouble(5, platformDiscount_);
    }
    if (storeDiscount_ != 0D) {
      output.writeDouble(6, storeDiscount_);
    }
    if (cost_ != 0D) {
      output.writeDouble(7, cost_);
    }
    if (tpAllowance_ != 0D) {
      output.writeDouble(8, tpAllowance_);
    }
    if (merchantAllowance_ != 0D) {
      output.writeDouble(9, merchantAllowance_);
    }
    if (platformAllowance_ != 0D) {
      output.writeDouble(10, platformAllowance_);
    }
    if (realAmount_ != 0D) {
      output.writeDouble(11, realAmount_);
    }
    if (transferAmount_ != 0D) {
      output.writeDouble(12, transferAmount_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (trigger_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, trigger_);
    }
    if (discount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(2, discount_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < fired_.size(); i++) {
        dataSize += computeStringSizeNoTag(fired_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getFiredList().size();
    }
    if (merchantDiscount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(4, merchantDiscount_);
    }
    if (platformDiscount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(5, platformDiscount_);
    }
    if (storeDiscount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(6, storeDiscount_);
    }
    if (cost_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(7, cost_);
    }
    if (tpAllowance_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(8, tpAllowance_);
    }
    if (merchantAllowance_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(9, merchantAllowance_);
    }
    if (platformAllowance_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(10, platformAllowance_);
    }
    if (realAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(11, realAmount_);
    }
    if (transferAmount_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(12, transferAmount_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource other = (cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource) obj;

    if (getTrigger()
        != other.getTrigger()) return false;
    if (java.lang.Double.doubleToLongBits(getDiscount())
        != java.lang.Double.doubleToLongBits(
            other.getDiscount())) return false;
    if (!getFiredList()
        .equals(other.getFiredList())) return false;
    if (java.lang.Double.doubleToLongBits(getMerchantDiscount())
        != java.lang.Double.doubleToLongBits(
            other.getMerchantDiscount())) return false;
    if (java.lang.Double.doubleToLongBits(getPlatformDiscount())
        != java.lang.Double.doubleToLongBits(
            other.getPlatformDiscount())) return false;
    if (java.lang.Double.doubleToLongBits(getStoreDiscount())
        != java.lang.Double.doubleToLongBits(
            other.getStoreDiscount())) return false;
    if (java.lang.Double.doubleToLongBits(getCost())
        != java.lang.Double.doubleToLongBits(
            other.getCost())) return false;
    if (java.lang.Double.doubleToLongBits(getTpAllowance())
        != java.lang.Double.doubleToLongBits(
            other.getTpAllowance())) return false;
    if (java.lang.Double.doubleToLongBits(getMerchantAllowance())
        != java.lang.Double.doubleToLongBits(
            other.getMerchantAllowance())) return false;
    if (java.lang.Double.doubleToLongBits(getPlatformAllowance())
        != java.lang.Double.doubleToLongBits(
            other.getPlatformAllowance())) return false;
    if (java.lang.Double.doubleToLongBits(getRealAmount())
        != java.lang.Double.doubleToLongBits(
            other.getRealAmount())) return false;
    if (java.lang.Double.doubleToLongBits(getTransferAmount())
        != java.lang.Double.doubleToLongBits(
            other.getTransferAmount())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TRIGGER_FIELD_NUMBER;
    hash = (53 * hash) + getTrigger();
    hash = (37 * hash) + DISCOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getDiscount()));
    if (getFiredCount() > 0) {
      hash = (37 * hash) + FIRED_FIELD_NUMBER;
      hash = (53 * hash) + getFiredList().hashCode();
    }
    hash = (37 * hash) + MERCHANT_DISCOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getMerchantDiscount()));
    hash = (37 * hash) + PLATFORM_DISCOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPlatformDiscount()));
    hash = (37 * hash) + STORE_DISCOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getStoreDiscount()));
    hash = (37 * hash) + COST_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getCost()));
    hash = (37 * hash) + TP_ALLOWANCE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getTpAllowance()));
    hash = (37 * hash) + MERCHANT_ALLOWANCE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getMerchantAllowance()));
    hash = (37 * hash) + PLATFORM_ALLOWANCE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPlatformAllowance()));
    hash = (37 * hash) + REAL_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getRealAmount()));
    hash = (37 * hash) + TRANSFER_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getTransferAmount()));
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code eticket_proto.PromotionSource}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:eticket_proto.PromotionSource)
      cn.hexcloud.pbis.common.service.integration.eticket.PromotionSourceOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_PromotionSource_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_PromotionSource_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.class, cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      trigger_ = 0;

      discount_ = 0D;

      fired_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      bitField0_ = (bitField0_ & ~0x00000001);
      merchantDiscount_ = 0D;

      platformDiscount_ = 0D;

      storeDiscount_ = 0D;

      cost_ = 0D;

      tpAllowance_ = 0D;

      merchantAllowance_ = 0D;

      platformAllowance_ = 0D;

      realAmount_ = 0D;

      transferAmount_ = 0D;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_PromotionSource_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource build() {
      cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource buildPartial() {
      cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource result = new cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource(this);
      int from_bitField0_ = bitField0_;
      result.trigger_ = trigger_;
      result.discount_ = discount_;
      if (((bitField0_ & 0x00000001) != 0)) {
        fired_ = fired_.getUnmodifiableView();
        bitField0_ = (bitField0_ & ~0x00000001);
      }
      result.fired_ = fired_;
      result.merchantDiscount_ = merchantDiscount_;
      result.platformDiscount_ = platformDiscount_;
      result.storeDiscount_ = storeDiscount_;
      result.cost_ = cost_;
      result.tpAllowance_ = tpAllowance_;
      result.merchantAllowance_ = merchantAllowance_;
      result.platformAllowance_ = platformAllowance_;
      result.realAmount_ = realAmount_;
      result.transferAmount_ = transferAmount_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource other) {
      if (other == cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource.getDefaultInstance()) return this;
      if (other.getTrigger() != 0) {
        setTrigger(other.getTrigger());
      }
      if (other.getDiscount() != 0D) {
        setDiscount(other.getDiscount());
      }
      if (!other.fired_.isEmpty()) {
        if (fired_.isEmpty()) {
          fired_ = other.fired_;
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          ensureFiredIsMutable();
          fired_.addAll(other.fired_);
        }
        onChanged();
      }
      if (other.getMerchantDiscount() != 0D) {
        setMerchantDiscount(other.getMerchantDiscount());
      }
      if (other.getPlatformDiscount() != 0D) {
        setPlatformDiscount(other.getPlatformDiscount());
      }
      if (other.getStoreDiscount() != 0D) {
        setStoreDiscount(other.getStoreDiscount());
      }
      if (other.getCost() != 0D) {
        setCost(other.getCost());
      }
      if (other.getTpAllowance() != 0D) {
        setTpAllowance(other.getTpAllowance());
      }
      if (other.getMerchantAllowance() != 0D) {
        setMerchantAllowance(other.getMerchantAllowance());
      }
      if (other.getPlatformAllowance() != 0D) {
        setPlatformAllowance(other.getPlatformAllowance());
      }
      if (other.getRealAmount() != 0D) {
        setRealAmount(other.getRealAmount());
      }
      if (other.getTransferAmount() != 0D) {
        setTransferAmount(other.getTransferAmount());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private int trigger_ ;
    /**
     * <code>int32 trigger = 1;</code>
     * @return The trigger.
     */
    @java.lang.Override
    public int getTrigger() {
      return trigger_;
    }
    /**
     * <code>int32 trigger = 1;</code>
     * @param value The trigger to set.
     * @return This builder for chaining.
     */
    public Builder setTrigger(int value) {
      
      trigger_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>int32 trigger = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearTrigger() {
      
      trigger_ = 0;
      onChanged();
      return this;
    }

    private double discount_ ;
    /**
     * <code>double discount = 2;</code>
     * @return The discount.
     */
    @java.lang.Override
    public double getDiscount() {
      return discount_;
    }
    /**
     * <code>double discount = 2;</code>
     * @param value The discount to set.
     * @return This builder for chaining.
     */
    public Builder setDiscount(double value) {
      
      discount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double discount = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearDiscount() {
      
      discount_ = 0D;
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringList fired_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    private void ensureFiredIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        fired_ = new com.google.protobuf.LazyStringArrayList(fired_);
        bitField0_ |= 0x00000001;
       }
    }
    /**
     * <code>repeated string fired = 3;</code>
     * @return A list containing the fired.
     */
    public com.google.protobuf.ProtocolStringList
        getFiredList() {
      return fired_.getUnmodifiableView();
    }
    /**
     * <code>repeated string fired = 3;</code>
     * @return The count of fired.
     */
    public int getFiredCount() {
      return fired_.size();
    }
    /**
     * <code>repeated string fired = 3;</code>
     * @param index The index of the element to return.
     * @return The fired at the given index.
     */
    public java.lang.String getFired(int index) {
      return fired_.get(index);
    }
    /**
     * <code>repeated string fired = 3;</code>
     * @param index The index of the value to return.
     * @return The bytes of the fired at the given index.
     */
    public com.google.protobuf.ByteString
        getFiredBytes(int index) {
      return fired_.getByteString(index);
    }
    /**
     * <code>repeated string fired = 3;</code>
     * @param index The index to set the value at.
     * @param value The fired to set.
     * @return This builder for chaining.
     */
    public Builder setFired(
        int index, java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  ensureFiredIsMutable();
      fired_.set(index, value);
      onChanged();
      return this;
    }
    /**
     * <code>repeated string fired = 3;</code>
     * @param value The fired to add.
     * @return This builder for chaining.
     */
    public Builder addFired(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  ensureFiredIsMutable();
      fired_.add(value);
      onChanged();
      return this;
    }
    /**
     * <code>repeated string fired = 3;</code>
     * @param values The fired to add.
     * @return This builder for chaining.
     */
    public Builder addAllFired(
        java.lang.Iterable<java.lang.String> values) {
      ensureFiredIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, fired_);
      onChanged();
      return this;
    }
    /**
     * <code>repeated string fired = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearFired() {
      fired_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>repeated string fired = 3;</code>
     * @param value The bytes of the fired to add.
     * @return This builder for chaining.
     */
    public Builder addFiredBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      ensureFiredIsMutable();
      fired_.add(value);
      onChanged();
      return this;
    }

    private double merchantDiscount_ ;
    /**
     * <code>double merchant_discount = 4;</code>
     * @return The merchantDiscount.
     */
    @java.lang.Override
    public double getMerchantDiscount() {
      return merchantDiscount_;
    }
    /**
     * <code>double merchant_discount = 4;</code>
     * @param value The merchantDiscount to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantDiscount(double value) {
      
      merchantDiscount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double merchant_discount = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearMerchantDiscount() {
      
      merchantDiscount_ = 0D;
      onChanged();
      return this;
    }

    private double platformDiscount_ ;
    /**
     * <code>double platform_discount = 5;</code>
     * @return The platformDiscount.
     */
    @java.lang.Override
    public double getPlatformDiscount() {
      return platformDiscount_;
    }
    /**
     * <code>double platform_discount = 5;</code>
     * @param value The platformDiscount to set.
     * @return This builder for chaining.
     */
    public Builder setPlatformDiscount(double value) {
      
      platformDiscount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double platform_discount = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlatformDiscount() {
      
      platformDiscount_ = 0D;
      onChanged();
      return this;
    }

    private double storeDiscount_ ;
    /**
     * <code>double store_discount = 6;</code>
     * @return The storeDiscount.
     */
    @java.lang.Override
    public double getStoreDiscount() {
      return storeDiscount_;
    }
    /**
     * <code>double store_discount = 6;</code>
     * @param value The storeDiscount to set.
     * @return This builder for chaining.
     */
    public Builder setStoreDiscount(double value) {
      
      storeDiscount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double store_discount = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearStoreDiscount() {
      
      storeDiscount_ = 0D;
      onChanged();
      return this;
    }

    private double cost_ ;
    /**
     * <code>double cost = 7;</code>
     * @return The cost.
     */
    @java.lang.Override
    public double getCost() {
      return cost_;
    }
    /**
     * <code>double cost = 7;</code>
     * @param value The cost to set.
     * @return This builder for chaining.
     */
    public Builder setCost(double value) {
      
      cost_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double cost = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearCost() {
      
      cost_ = 0D;
      onChanged();
      return this;
    }

    private double tpAllowance_ ;
    /**
     * <code>double tp_allowance = 8;</code>
     * @return The tpAllowance.
     */
    @java.lang.Override
    public double getTpAllowance() {
      return tpAllowance_;
    }
    /**
     * <code>double tp_allowance = 8;</code>
     * @param value The tpAllowance to set.
     * @return This builder for chaining.
     */
    public Builder setTpAllowance(double value) {
      
      tpAllowance_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double tp_allowance = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearTpAllowance() {
      
      tpAllowance_ = 0D;
      onChanged();
      return this;
    }

    private double merchantAllowance_ ;
    /**
     * <code>double merchant_allowance = 9;</code>
     * @return The merchantAllowance.
     */
    @java.lang.Override
    public double getMerchantAllowance() {
      return merchantAllowance_;
    }
    /**
     * <code>double merchant_allowance = 9;</code>
     * @param value The merchantAllowance to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantAllowance(double value) {
      
      merchantAllowance_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double merchant_allowance = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearMerchantAllowance() {
      
      merchantAllowance_ = 0D;
      onChanged();
      return this;
    }

    private double platformAllowance_ ;
    /**
     * <code>double platform_allowance = 10;</code>
     * @return The platformAllowance.
     */
    @java.lang.Override
    public double getPlatformAllowance() {
      return platformAllowance_;
    }
    /**
     * <code>double platform_allowance = 10;</code>
     * @param value The platformAllowance to set.
     * @return This builder for chaining.
     */
    public Builder setPlatformAllowance(double value) {
      
      platformAllowance_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double platform_allowance = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlatformAllowance() {
      
      platformAllowance_ = 0D;
      onChanged();
      return this;
    }

    private double realAmount_ ;
    /**
     * <code>double real_amount = 11;</code>
     * @return The realAmount.
     */
    @java.lang.Override
    public double getRealAmount() {
      return realAmount_;
    }
    /**
     * <code>double real_amount = 11;</code>
     * @param value The realAmount to set.
     * @return This builder for chaining.
     */
    public Builder setRealAmount(double value) {
      
      realAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double real_amount = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearRealAmount() {
      
      realAmount_ = 0D;
      onChanged();
      return this;
    }

    private double transferAmount_ ;
    /**
     * <code>double transfer_amount = 12;</code>
     * @return The transferAmount.
     */
    @java.lang.Override
    public double getTransferAmount() {
      return transferAmount_;
    }
    /**
     * <code>double transfer_amount = 12;</code>
     * @param value The transferAmount to set.
     * @return This builder for chaining.
     */
    public Builder setTransferAmount(double value) {
      
      transferAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double transfer_amount = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearTransferAmount() {
      
      transferAmount_ = 0D;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:eticket_proto.PromotionSource)
  }

  // @@protoc_insertion_point(class_scope:eticket_proto.PromotionSource)
  private static final cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource();
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PromotionSource>
      PARSER = new com.google.protobuf.AbstractParser<PromotionSource>() {
    @java.lang.Override
    public PromotionSource parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new PromotionSource(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<PromotionSource> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PromotionSource> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.PromotionSource getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

