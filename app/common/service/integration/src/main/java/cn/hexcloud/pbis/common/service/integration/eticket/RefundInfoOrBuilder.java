// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

public interface RefundInfoOrBuilder extends
    // @@protoc_insertion_point(interface_extends:eticket_proto.RefundInfo)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 退单负单的ticketId，
   * </pre>
   *
   * <code>string refund_id = 1;</code>
   * @return The refundId.
   */
  java.lang.String getRefundId();
  /**
   * <pre>
   * 退单负单的ticketId，
   * </pre>
   *
   * <code>string refund_id = 1;</code>
   * @return The bytes for refundId.
   */
  com.google.protobuf.ByteString
      getRefundIdBytes();

  /**
   * <pre>
   * 退单负单的ticketId，
   * </pre>
   *
   * <code>string refund_no = 2;</code>
   * @return The refundNo.
   */
  java.lang.String getRefundNo();
  /**
   * <pre>
   * 退单负单的ticketId，
   * </pre>
   *
   * <code>string refund_no = 2;</code>
   * @return The bytes for refundNo.
   */
  com.google.protobuf.ByteString
      getRefundNoBytes();

  /**
   * <pre>
   *退单正单的ticketId
   * </pre>
   *
   * <code>string ref_ticket_id = 3;</code>
   * @return The refTicketId.
   */
  java.lang.String getRefTicketId();
  /**
   * <pre>
   *退单正单的ticketId
   * </pre>
   *
   * <code>string ref_ticket_id = 3;</code>
   * @return The bytes for refTicketId.
   */
  com.google.protobuf.ByteString
      getRefTicketIdBytes();

  /**
   * <pre>
   *退单正单的ticketNo
   * </pre>
   *
   * <code>string ref_ticket_no = 4;</code>
   * @return The refTicketNo.
   */
  java.lang.String getRefTicketNo();
  /**
   * <pre>
   *退单正单的ticketNo
   * </pre>
   *
   * <code>string ref_ticket_no = 4;</code>
   * @return The bytes for refTicketNo.
   */
  com.google.protobuf.ByteString
      getRefTicketNoBytes();

  /**
   * <pre>
   *退单原因
   * </pre>
   *
   * <code>string refund_reason = 5;</code>
   * @return The refundReason.
   */
  java.lang.String getRefundReason();
  /**
   * <pre>
   *退单原因
   * </pre>
   *
   * <code>string refund_reason = 5;</code>
   * @return The bytes for refundReason.
   */
  com.google.protobuf.ByteString
      getRefundReasonBytes();

  /**
   * <code>string refund_code = 6;</code>
   * @return The refundCode.
   */
  java.lang.String getRefundCode();
  /**
   * <code>string refund_code = 6;</code>
   * @return The bytes for refundCode.
   */
  com.google.protobuf.ByteString
      getRefundCodeBytes();

  /**
   * <pre>
   *退单方
   * </pre>
   *
   * <code>string refund_side = 7;</code>
   * @return The refundSide.
   */
  java.lang.String getRefundSide();
  /**
   * <pre>
   *退单方
   * </pre>
   *
   * <code>string refund_side = 7;</code>
   * @return The bytes for refundSide.
   */
  com.google.protobuf.ByteString
      getRefundSideBytes();
}
