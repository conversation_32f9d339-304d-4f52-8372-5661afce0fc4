// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: service.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

public final class Service {
  private Service() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_UploadTicketRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_UploadTicketRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_UploadTicketResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_UploadTicketResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rservice.proto\022\reticket_proto\032\014ticket.p" +
      "roto\"<\n\023UploadTicketRequest\022%\n\006ticket\030\001 " +
      "\001(\0132\025.eticket_proto.Ticket\":\n\024UploadTick" +
      "etResponse\022\017\n\007success\030\001 \001(\010\022\021\n\tticket_id" +
      "\030\002 \001(\t2d\n\007Eticket\022Y\n\014UploadTicket\022\".etic" +
      "ket_proto.UploadTicketRequest\032#.eticket_" +
      "proto.UploadTicketResponse\"\000B7\n3cn.hexcl" +
      "oud.pbis.common.service.integration.etic" +
      "ketP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.getDescriptor(),
        });
    internal_static_eticket_proto_UploadTicketRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_eticket_proto_UploadTicketRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_UploadTicketRequest_descriptor,
        new java.lang.String[] { "Ticket", });
    internal_static_eticket_proto_UploadTicketResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_eticket_proto_UploadTicketResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_UploadTicketResponse_descriptor,
        new java.lang.String[] { "Success", "TicketId", });
    cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
