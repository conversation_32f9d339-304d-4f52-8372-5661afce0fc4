// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

public interface SkuRemarkOrBuilder extends
    // @@protoc_insertion_point(interface_extends:eticket_proto.SkuRemark)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>.eticket_proto.SkuRemark.skuName name = 1;</code>
   * @return Whether the name field is set.
   */
  boolean hasName();
  /**
   * <code>.eticket_proto.SkuRemark.skuName name = 1;</code>
   * @return The name.
   */
  cn.hexcloud.pbis.common.service.integration.eticket.SkuRemark.skuName getName();
  /**
   * <code>.eticket_proto.SkuRemark.skuName name = 1;</code>
   */
  cn.hexcloud.pbis.common.service.integration.eticket.SkuRemark.skuNameOrBuilder getNameOrBuilder();

  /**
   * <code>.eticket_proto.SkuRemark.skuValue values = 2;</code>
   * @return Whether the values field is set.
   */
  boolean hasValues();
  /**
   * <code>.eticket_proto.SkuRemark.skuValue values = 2;</code>
   * @return The values.
   */
  cn.hexcloud.pbis.common.service.integration.eticket.SkuRemark.skuValue getValues();
  /**
   * <code>.eticket_proto.SkuRemark.skuValue values = 2;</code>
   */
  cn.hexcloud.pbis.common.service.integration.eticket.SkuRemark.skuValueOrBuilder getValuesOrBuilder();
}
