// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

/**
 * <pre>
 *门店信息
 * </pre>
 *
 * Protobuf type {@code eticket_proto.Store}
 */
public final class Store extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:eticket_proto.Store)
    StoreOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Store.newBuilder() to construct.
  private Store(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Store() {
    id_ = "";
    code_ = "";
    secondCode_ = "";
    companyId_ = "";
    partnerId_ = "";
    scopeId_ = "";
    branchId_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Store();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Store(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            id_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            code_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            secondCode_ = s;
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            companyId_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            partnerId_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            scopeId_ = s;
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            branchId_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Store_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Store_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.eticket.Store.class, cn.hexcloud.pbis.common.service.integration.eticket.Store.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  private volatile java.lang.Object id_;
  /**
   * <code>string id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CODE_FIELD_NUMBER = 2;
  private volatile java.lang.Object code_;
  /**
   * <code>string code = 2;</code>
   * @return The code.
   */
  @java.lang.Override
  public java.lang.String getCode() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      code_ = s;
      return s;
    }
  }
  /**
   * <code>string code = 2;</code>
   * @return The bytes for code.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCodeBytes() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      code_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SECONDCODE_FIELD_NUMBER = 3;
  private volatile java.lang.Object secondCode_;
  /**
   * <pre>
   *门店三方编码
   * </pre>
   *
   * <code>string secondCode = 3;</code>
   * @return The secondCode.
   */
  @java.lang.Override
  public java.lang.String getSecondCode() {
    java.lang.Object ref = secondCode_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      secondCode_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *门店三方编码
   * </pre>
   *
   * <code>string secondCode = 3;</code>
   * @return The bytes for secondCode.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSecondCodeBytes() {
    java.lang.Object ref = secondCode_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      secondCode_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int COMPANYID_FIELD_NUMBER = 4;
  private volatile java.lang.Object companyId_;
  /**
   * <pre>
   *门店三方id
   * </pre>
   *
   * <code>string companyId = 4;</code>
   * @return The companyId.
   */
  @java.lang.Override
  public java.lang.String getCompanyId() {
    java.lang.Object ref = companyId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      companyId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *门店三方id
   * </pre>
   *
   * <code>string companyId = 4;</code>
   * @return The bytes for companyId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCompanyIdBytes() {
    java.lang.Object ref = companyId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      companyId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PARTNERID_FIELD_NUMBER = 5;
  private volatile java.lang.Object partnerId_;
  /**
   * <pre>
   *租户id
   * </pre>
   *
   * <code>string partnerId = 5;</code>
   * @return The partnerId.
   */
  @java.lang.Override
  public java.lang.String getPartnerId() {
    java.lang.Object ref = partnerId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      partnerId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *租户id
   * </pre>
   *
   * <code>string partnerId = 5;</code>
   * @return The bytes for partnerId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPartnerIdBytes() {
    java.lang.Object ref = partnerId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      partnerId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SCOPEID_FIELD_NUMBER = 6;
  private volatile java.lang.Object scopeId_;
  /**
   * <code>string scopeId = 6;</code>
   * @return The scopeId.
   */
  @java.lang.Override
  public java.lang.String getScopeId() {
    java.lang.Object ref = scopeId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      scopeId_ = s;
      return s;
    }
  }
  /**
   * <code>string scopeId = 6;</code>
   * @return The bytes for scopeId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getScopeIdBytes() {
    java.lang.Object ref = scopeId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      scopeId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BRANCHID_FIELD_NUMBER = 7;
  private volatile java.lang.Object branchId_;
  /**
   * <code>string branchId = 7;</code>
   * @return The branchId.
   */
  @java.lang.Override
  public java.lang.String getBranchId() {
    java.lang.Object ref = branchId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      branchId_ = s;
      return s;
    }
  }
  /**
   * <code>string branchId = 7;</code>
   * @return The bytes for branchId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getBranchIdBytes() {
    java.lang.Object ref = branchId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      branchId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (!getCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, code_);
    }
    if (!getSecondCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, secondCode_);
    }
    if (!getCompanyIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, companyId_);
    }
    if (!getPartnerIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, partnerId_);
    }
    if (!getScopeIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, scopeId_);
    }
    if (!getBranchIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, branchId_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (!getCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, code_);
    }
    if (!getSecondCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, secondCode_);
    }
    if (!getCompanyIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, companyId_);
    }
    if (!getPartnerIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, partnerId_);
    }
    if (!getScopeIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, scopeId_);
    }
    if (!getBranchIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, branchId_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.eticket.Store)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.eticket.Store other = (cn.hexcloud.pbis.common.service.integration.eticket.Store) obj;

    if (!getId()
        .equals(other.getId())) return false;
    if (!getCode()
        .equals(other.getCode())) return false;
    if (!getSecondCode()
        .equals(other.getSecondCode())) return false;
    if (!getCompanyId()
        .equals(other.getCompanyId())) return false;
    if (!getPartnerId()
        .equals(other.getPartnerId())) return false;
    if (!getScopeId()
        .equals(other.getScopeId())) return false;
    if (!getBranchId()
        .equals(other.getBranchId())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCode().hashCode();
    hash = (37 * hash) + SECONDCODE_FIELD_NUMBER;
    hash = (53 * hash) + getSecondCode().hashCode();
    hash = (37 * hash) + COMPANYID_FIELD_NUMBER;
    hash = (53 * hash) + getCompanyId().hashCode();
    hash = (37 * hash) + PARTNERID_FIELD_NUMBER;
    hash = (53 * hash) + getPartnerId().hashCode();
    hash = (37 * hash) + SCOPEID_FIELD_NUMBER;
    hash = (53 * hash) + getScopeId().hashCode();
    hash = (37 * hash) + BRANCHID_FIELD_NUMBER;
    hash = (53 * hash) + getBranchId().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.Store parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Store parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Store parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Store parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Store parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Store parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Store parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Store parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Store parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Store parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Store parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Store parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.eticket.Store prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   *门店信息
   * </pre>
   *
   * Protobuf type {@code eticket_proto.Store}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:eticket_proto.Store)
      cn.hexcloud.pbis.common.service.integration.eticket.StoreOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Store_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Store_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.eticket.Store.class, cn.hexcloud.pbis.common.service.integration.eticket.Store.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.eticket.Store.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      id_ = "";

      code_ = "";

      secondCode_ = "";

      companyId_ = "";

      partnerId_ = "";

      scopeId_ = "";

      branchId_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Store_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Store getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.Store.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Store build() {
      cn.hexcloud.pbis.common.service.integration.eticket.Store result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Store buildPartial() {
      cn.hexcloud.pbis.common.service.integration.eticket.Store result = new cn.hexcloud.pbis.common.service.integration.eticket.Store(this);
      result.id_ = id_;
      result.code_ = code_;
      result.secondCode_ = secondCode_;
      result.companyId_ = companyId_;
      result.partnerId_ = partnerId_;
      result.scopeId_ = scopeId_;
      result.branchId_ = branchId_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.eticket.Store) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.eticket.Store)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.eticket.Store other) {
      if (other == cn.hexcloud.pbis.common.service.integration.eticket.Store.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        onChanged();
      }
      if (!other.getCode().isEmpty()) {
        code_ = other.code_;
        onChanged();
      }
      if (!other.getSecondCode().isEmpty()) {
        secondCode_ = other.secondCode_;
        onChanged();
      }
      if (!other.getCompanyId().isEmpty()) {
        companyId_ = other.companyId_;
        onChanged();
      }
      if (!other.getPartnerId().isEmpty()) {
        partnerId_ = other.partnerId_;
        onChanged();
      }
      if (!other.getScopeId().isEmpty()) {
        scopeId_ = other.scopeId_;
        onChanged();
      }
      if (!other.getBranchId().isEmpty()) {
        branchId_ = other.branchId_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.eticket.Store parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.eticket.Store) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object id_ = "";
    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      
      id_ = getDefaultInstance().getId();
      onChanged();
      return this;
    }
    /**
     * <code>string id = 1;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      id_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object code_ = "";
    /**
     * <code>string code = 2;</code>
     * @return The code.
     */
    public java.lang.String getCode() {
      java.lang.Object ref = code_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        code_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string code = 2;</code>
     * @return The bytes for code.
     */
    public com.google.protobuf.ByteString
        getCodeBytes() {
      java.lang.Object ref = code_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        code_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string code = 2;</code>
     * @param value The code to set.
     * @return This builder for chaining.
     */
    public Builder setCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      code_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string code = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearCode() {
      
      code_ = getDefaultInstance().getCode();
      onChanged();
      return this;
    }
    /**
     * <code>string code = 2;</code>
     * @param value The bytes for code to set.
     * @return This builder for chaining.
     */
    public Builder setCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      code_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object secondCode_ = "";
    /**
     * <pre>
     *门店三方编码
     * </pre>
     *
     * <code>string secondCode = 3;</code>
     * @return The secondCode.
     */
    public java.lang.String getSecondCode() {
      java.lang.Object ref = secondCode_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        secondCode_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *门店三方编码
     * </pre>
     *
     * <code>string secondCode = 3;</code>
     * @return The bytes for secondCode.
     */
    public com.google.protobuf.ByteString
        getSecondCodeBytes() {
      java.lang.Object ref = secondCode_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        secondCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *门店三方编码
     * </pre>
     *
     * <code>string secondCode = 3;</code>
     * @param value The secondCode to set.
     * @return This builder for chaining.
     */
    public Builder setSecondCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      secondCode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *门店三方编码
     * </pre>
     *
     * <code>string secondCode = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearSecondCode() {
      
      secondCode_ = getDefaultInstance().getSecondCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *门店三方编码
     * </pre>
     *
     * <code>string secondCode = 3;</code>
     * @param value The bytes for secondCode to set.
     * @return This builder for chaining.
     */
    public Builder setSecondCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      secondCode_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object companyId_ = "";
    /**
     * <pre>
     *门店三方id
     * </pre>
     *
     * <code>string companyId = 4;</code>
     * @return The companyId.
     */
    public java.lang.String getCompanyId() {
      java.lang.Object ref = companyId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        companyId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *门店三方id
     * </pre>
     *
     * <code>string companyId = 4;</code>
     * @return The bytes for companyId.
     */
    public com.google.protobuf.ByteString
        getCompanyIdBytes() {
      java.lang.Object ref = companyId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        companyId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *门店三方id
     * </pre>
     *
     * <code>string companyId = 4;</code>
     * @param value The companyId to set.
     * @return This builder for chaining.
     */
    public Builder setCompanyId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      companyId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *门店三方id
     * </pre>
     *
     * <code>string companyId = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearCompanyId() {
      
      companyId_ = getDefaultInstance().getCompanyId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *门店三方id
     * </pre>
     *
     * <code>string companyId = 4;</code>
     * @param value The bytes for companyId to set.
     * @return This builder for chaining.
     */
    public Builder setCompanyIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      companyId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object partnerId_ = "";
    /**
     * <pre>
     *租户id
     * </pre>
     *
     * <code>string partnerId = 5;</code>
     * @return The partnerId.
     */
    public java.lang.String getPartnerId() {
      java.lang.Object ref = partnerId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        partnerId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *租户id
     * </pre>
     *
     * <code>string partnerId = 5;</code>
     * @return The bytes for partnerId.
     */
    public com.google.protobuf.ByteString
        getPartnerIdBytes() {
      java.lang.Object ref = partnerId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        partnerId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *租户id
     * </pre>
     *
     * <code>string partnerId = 5;</code>
     * @param value The partnerId to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      partnerId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *租户id
     * </pre>
     *
     * <code>string partnerId = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearPartnerId() {
      
      partnerId_ = getDefaultInstance().getPartnerId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *租户id
     * </pre>
     *
     * <code>string partnerId = 5;</code>
     * @param value The bytes for partnerId to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      partnerId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object scopeId_ = "";
    /**
     * <code>string scopeId = 6;</code>
     * @return The scopeId.
     */
    public java.lang.String getScopeId() {
      java.lang.Object ref = scopeId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        scopeId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string scopeId = 6;</code>
     * @return The bytes for scopeId.
     */
    public com.google.protobuf.ByteString
        getScopeIdBytes() {
      java.lang.Object ref = scopeId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        scopeId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string scopeId = 6;</code>
     * @param value The scopeId to set.
     * @return This builder for chaining.
     */
    public Builder setScopeId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      scopeId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string scopeId = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearScopeId() {
      
      scopeId_ = getDefaultInstance().getScopeId();
      onChanged();
      return this;
    }
    /**
     * <code>string scopeId = 6;</code>
     * @param value The bytes for scopeId to set.
     * @return This builder for chaining.
     */
    public Builder setScopeIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      scopeId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object branchId_ = "";
    /**
     * <code>string branchId = 7;</code>
     * @return The branchId.
     */
    public java.lang.String getBranchId() {
      java.lang.Object ref = branchId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        branchId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string branchId = 7;</code>
     * @return The bytes for branchId.
     */
    public com.google.protobuf.ByteString
        getBranchIdBytes() {
      java.lang.Object ref = branchId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        branchId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string branchId = 7;</code>
     * @param value The branchId to set.
     * @return This builder for chaining.
     */
    public Builder setBranchId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      branchId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string branchId = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearBranchId() {
      
      branchId_ = getDefaultInstance().getBranchId();
      onChanged();
      return this;
    }
    /**
     * <code>string branchId = 7;</code>
     * @param value The bytes for branchId to set.
     * @return This builder for chaining.
     */
    public Builder setBranchIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      branchId_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:eticket_proto.Store)
  }

  // @@protoc_insertion_point(class_scope:eticket_proto.Store)
  private static final cn.hexcloud.pbis.common.service.integration.eticket.Store DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.eticket.Store();
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.Store getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Store>
      PARSER = new com.google.protobuf.AbstractParser<Store>() {
    @java.lang.Override
    public Store parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Store(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Store> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Store> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.Store getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

