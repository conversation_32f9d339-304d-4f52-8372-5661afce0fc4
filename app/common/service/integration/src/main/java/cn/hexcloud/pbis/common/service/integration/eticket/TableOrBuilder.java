// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

public interface TableOrBuilder extends
    // @@protoc_insertion_point(interface_extends:eticket_proto.Table)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *桌位id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   *桌位id
   * </pre>
   *
   * <code>string id = 1;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   *桌位分区id
   * </pre>
   *
   * <code>string zone_id = 2;</code>
   * @return The zoneId.
   */
  java.lang.String getZoneId();
  /**
   * <pre>
   *桌位分区id
   * </pre>
   *
   * <code>string zone_id = 2;</code>
   * @return The bytes for zoneId.
   */
  com.google.protobuf.ByteString
      getZoneIdBytes();

  /**
   * <pre>
   *桌位号
   * </pre>
   *
   * <code>string no = 3;</code>
   * @return The no.
   */
  java.lang.String getNo();
  /**
   * <pre>
   *桌位号
   * </pre>
   *
   * <code>string no = 3;</code>
   * @return The bytes for no.
   */
  com.google.protobuf.ByteString
      getNoBytes();

  /**
   * <pre>
   *桌位分区号
   * </pre>
   *
   * <code>string zoneNo = 4;</code>
   * @return The zoneNo.
   */
  java.lang.String getZoneNo();
  /**
   * <pre>
   *桌位分区号
   * </pre>
   *
   * <code>string zoneNo = 4;</code>
   * @return The bytes for zoneNo.
   */
  com.google.protobuf.ByteString
      getZoneNoBytes();

  /**
   * <pre>
   *桌位人数
   * </pre>
   *
   * <code>int32 people = 5;</code>
   * @return The people.
   */
  int getPeople();

  /**
   * <pre>
   *是否是临时桌位
   * </pre>
   *
   * <code>bool temporary = 6;</code>
   * @return The temporary.
   */
  boolean getTemporary();
}
