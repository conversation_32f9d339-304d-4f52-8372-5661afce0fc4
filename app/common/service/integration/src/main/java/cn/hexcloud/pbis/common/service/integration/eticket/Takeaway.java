// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

/**
 * Protobuf type {@code eticket_proto.Takeaway}
 */
public final class Takeaway extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:eticket_proto.Takeaway)
    TakeawayOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Takeaway.newBuilder() to construct.
  private Takeaway(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Takeaway() {
    orderMethod_ = "";
    tpOrderId_ = "";
    orderTime_ = "";
    deliverTime_ = "";
    description_ = "";
    consignee_ = "";
    deliveryPoiAddress_ = "";
    phoneList_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    tp_ = "";
    source_ = "";
    sourceOrderId_ = "";
    daySeq_ = "";
    deliveryName_ = "";
    invoiceTitle_ = "";
    waitingTime_ = "";
    deliveryTime_ = "";
    takeMealSn_ = "";
    partnerPlatformName_ = "";
    wxName_ = "";
    takeoutType_ = "";
    originalOrderNo_ = "";
    deliveryPhone_ = "";
    deliveryPlatform_ = "";
    invoiceType_ = "";
    invoiceTaxPayerId_ = "";
    invoiceEmail_ = "";
    invoiceProvider_ = "";
    invoiceAmount_ = "";
    invoiceUrl_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Takeaway();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Takeaway(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            orderMethod_ = s;
            break;
          }
          case 24: {

            isPaid_ = input.readBool();
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            tpOrderId_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            orderTime_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            deliverTime_ = s;
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            description_ = s;
            break;
          }
          case 66: {
            java.lang.String s = input.readStringRequireUtf8();

            consignee_ = s;
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            deliveryPoiAddress_ = s;
            break;
          }
          case 82: {
            java.lang.String s = input.readStringRequireUtf8();
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              phoneList_ = new com.google.protobuf.LazyStringArrayList();
              mutable_bitField0_ |= 0x00000001;
            }
            phoneList_.add(s);
            break;
          }
          case 90: {
            java.lang.String s = input.readStringRequireUtf8();

            tp_ = s;
            break;
          }
          case 98: {
            java.lang.String s = input.readStringRequireUtf8();

            source_ = s;
            break;
          }
          case 106: {
            java.lang.String s = input.readStringRequireUtf8();

            sourceOrderId_ = s;
            break;
          }
          case 114: {
            java.lang.String s = input.readStringRequireUtf8();

            daySeq_ = s;
            break;
          }
          case 120: {

            deliveryType_ = input.readInt32();
            break;
          }
          case 130: {
            java.lang.String s = input.readStringRequireUtf8();

            deliveryName_ = s;
            break;
          }
          case 138: {
            java.lang.String s = input.readStringRequireUtf8();

            invoiceTitle_ = s;
            break;
          }
          case 146: {
            java.lang.String s = input.readStringRequireUtf8();

            waitingTime_ = s;
            break;
          }
          case 152: {

            tablewareNum_ = input.readInt32();
            break;
          }
          case 161: {

            sendFee_ = input.readDouble();
            break;
          }
          case 169: {

            packageFee_ = input.readDouble();
            break;
          }
          case 178: {
            java.lang.String s = input.readStringRequireUtf8();

            deliveryTime_ = s;
            break;
          }
          case 186: {
            java.lang.String s = input.readStringRequireUtf8();

            takeMealSn_ = s;
            break;
          }
          case 192: {

            partnerPlatformId_ = input.readInt32();
            break;
          }
          case 202: {
            java.lang.String s = input.readStringRequireUtf8();

            partnerPlatformName_ = s;
            break;
          }
          case 210: {
            java.lang.String s = input.readStringRequireUtf8();

            wxName_ = s;
            break;
          }
          case 216: {

            isHighPriority_ = input.readBool();
            break;
          }
          case 226: {
            java.lang.String s = input.readStringRequireUtf8();

            takeoutType_ = s;
            break;
          }
          case 234: {
            java.lang.String s = input.readStringRequireUtf8();

            originalOrderNo_ = s;
            break;
          }
          case 245: {

            merchantSendFee_ = input.readFloat();
            break;
          }
          case 248: {

            selfDelivery_ = input.readBool();
            break;
          }
          case 258: {
            java.lang.String s = input.readStringRequireUtf8();

            deliveryPhone_ = s;
            break;
          }
          case 266: {
            java.lang.String s = input.readStringRequireUtf8();

            deliveryPlatform_ = s;
            break;
          }
          case 274: {
            java.lang.String s = input.readStringRequireUtf8();

            invoiceType_ = s;
            break;
          }
          case 282: {
            java.lang.String s = input.readStringRequireUtf8();

            invoiceTaxPayerId_ = s;
            break;
          }
          case 290: {
            java.lang.String s = input.readStringRequireUtf8();

            invoiceEmail_ = s;
            break;
          }
          case 301: {

            platformSendFee_ = input.readFloat();
            break;
          }
          case 309: {

            sendFeeForPlatform_ = input.readFloat();
            break;
          }
          case 317: {

            sendFeeForMerchant_ = input.readFloat();
            break;
          }
          case 322: {
            java.lang.String s = input.readStringRequireUtf8();

            invoiceProvider_ = s;
            break;
          }
          case 330: {
            java.lang.String s = input.readStringRequireUtf8();

            invoiceAmount_ = s;
            break;
          }
          case 338: {
            java.lang.String s = input.readStringRequireUtf8();

            invoiceUrl_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        phoneList_ = phoneList_.getUnmodifiableView();
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Takeaway_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Takeaway_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.eticket.Takeaway.class, cn.hexcloud.pbis.common.service.integration.eticket.Takeaway.Builder.class);
  }

  public static final int ORDER_METHOD_FIELD_NUMBER = 1;
  private volatile java.lang.Object orderMethod_;
  /**
   * <code>string order_method = 1;</code>
   * @return The orderMethod.
   */
  @java.lang.Override
  public java.lang.String getOrderMethod() {
    java.lang.Object ref = orderMethod_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      orderMethod_ = s;
      return s;
    }
  }
  /**
   * <code>string order_method = 1;</code>
   * @return The bytes for orderMethod.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOrderMethodBytes() {
    java.lang.Object ref = orderMethod_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      orderMethod_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IS_PAID_FIELD_NUMBER = 3;
  private boolean isPaid_;
  /**
   * <code>bool is_paid = 3;</code>
   * @return The isPaid.
   */
  @java.lang.Override
  public boolean getIsPaid() {
    return isPaid_;
  }

  public static final int TP_ORDER_ID_FIELD_NUMBER = 4;
  private volatile java.lang.Object tpOrderId_;
  /**
   * <code>string tp_order_id = 4;</code>
   * @return The tpOrderId.
   */
  @java.lang.Override
  public java.lang.String getTpOrderId() {
    java.lang.Object ref = tpOrderId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      tpOrderId_ = s;
      return s;
    }
  }
  /**
   * <code>string tp_order_id = 4;</code>
   * @return The bytes for tpOrderId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTpOrderIdBytes() {
    java.lang.Object ref = tpOrderId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      tpOrderId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ORDER_TIME_FIELD_NUMBER = 5;
  private volatile java.lang.Object orderTime_;
  /**
   * <code>string order_time = 5;</code>
   * @return The orderTime.
   */
  @java.lang.Override
  public java.lang.String getOrderTime() {
    java.lang.Object ref = orderTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      orderTime_ = s;
      return s;
    }
  }
  /**
   * <code>string order_time = 5;</code>
   * @return The bytes for orderTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOrderTimeBytes() {
    java.lang.Object ref = orderTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      orderTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DELIVER_TIME_FIELD_NUMBER = 6;
  private volatile java.lang.Object deliverTime_;
  /**
   * <code>string deliver_time = 6;</code>
   * @return The deliverTime.
   */
  @java.lang.Override
  public java.lang.String getDeliverTime() {
    java.lang.Object ref = deliverTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deliverTime_ = s;
      return s;
    }
  }
  /**
   * <code>string deliver_time = 6;</code>
   * @return The bytes for deliverTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDeliverTimeBytes() {
    java.lang.Object ref = deliverTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deliverTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DESCRIPTION_FIELD_NUMBER = 7;
  private volatile java.lang.Object description_;
  /**
   * <code>string description = 7;</code>
   * @return The description.
   */
  @java.lang.Override
  public java.lang.String getDescription() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      description_ = s;
      return s;
    }
  }
  /**
   * <code>string description = 7;</code>
   * @return The bytes for description.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDescriptionBytes() {
    java.lang.Object ref = description_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      description_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CONSIGNEE_FIELD_NUMBER = 8;
  private volatile java.lang.Object consignee_;
  /**
   * <code>string consignee = 8;</code>
   * @return The consignee.
   */
  @java.lang.Override
  public java.lang.String getConsignee() {
    java.lang.Object ref = consignee_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      consignee_ = s;
      return s;
    }
  }
  /**
   * <code>string consignee = 8;</code>
   * @return The bytes for consignee.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getConsigneeBytes() {
    java.lang.Object ref = consignee_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      consignee_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DELIVERY_POI_ADDRESS_FIELD_NUMBER = 9;
  private volatile java.lang.Object deliveryPoiAddress_;
  /**
   * <code>string delivery_poi_address = 9;</code>
   * @return The deliveryPoiAddress.
   */
  @java.lang.Override
  public java.lang.String getDeliveryPoiAddress() {
    java.lang.Object ref = deliveryPoiAddress_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deliveryPoiAddress_ = s;
      return s;
    }
  }
  /**
   * <code>string delivery_poi_address = 9;</code>
   * @return The bytes for deliveryPoiAddress.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDeliveryPoiAddressBytes() {
    java.lang.Object ref = deliveryPoiAddress_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deliveryPoiAddress_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PHONE_LIST_FIELD_NUMBER = 10;
  private com.google.protobuf.LazyStringList phoneList_;
  /**
   * <code>repeated string phone_list = 10;</code>
   * @return A list containing the phoneList.
   */
  public com.google.protobuf.ProtocolStringList
      getPhoneListList() {
    return phoneList_;
  }
  /**
   * <code>repeated string phone_list = 10;</code>
   * @return The count of phoneList.
   */
  public int getPhoneListCount() {
    return phoneList_.size();
  }
  /**
   * <code>repeated string phone_list = 10;</code>
   * @param index The index of the element to return.
   * @return The phoneList at the given index.
   */
  public java.lang.String getPhoneList(int index) {
    return phoneList_.get(index);
  }
  /**
   * <code>repeated string phone_list = 10;</code>
   * @param index The index of the value to return.
   * @return The bytes of the phoneList at the given index.
   */
  public com.google.protobuf.ByteString
      getPhoneListBytes(int index) {
    return phoneList_.getByteString(index);
  }

  public static final int TP_FIELD_NUMBER = 11;
  private volatile java.lang.Object tp_;
  /**
   * <code>string tp = 11;</code>
   * @return The tp.
   */
  @java.lang.Override
  public java.lang.String getTp() {
    java.lang.Object ref = tp_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      tp_ = s;
      return s;
    }
  }
  /**
   * <code>string tp = 11;</code>
   * @return The bytes for tp.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTpBytes() {
    java.lang.Object ref = tp_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      tp_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SOURCE_FIELD_NUMBER = 12;
  private volatile java.lang.Object source_;
  /**
   * <code>string source = 12;</code>
   * @return The source.
   */
  @java.lang.Override
  public java.lang.String getSource() {
    java.lang.Object ref = source_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      source_ = s;
      return s;
    }
  }
  /**
   * <code>string source = 12;</code>
   * @return The bytes for source.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSourceBytes() {
    java.lang.Object ref = source_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      source_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SOURCE_ORDER_ID_FIELD_NUMBER = 13;
  private volatile java.lang.Object sourceOrderId_;
  /**
   * <code>string source_order_id = 13;</code>
   * @return The sourceOrderId.
   */
  @java.lang.Override
  public java.lang.String getSourceOrderId() {
    java.lang.Object ref = sourceOrderId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      sourceOrderId_ = s;
      return s;
    }
  }
  /**
   * <code>string source_order_id = 13;</code>
   * @return The bytes for sourceOrderId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSourceOrderIdBytes() {
    java.lang.Object ref = sourceOrderId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      sourceOrderId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DAY_SEQ_FIELD_NUMBER = 14;
  private volatile java.lang.Object daySeq_;
  /**
   * <code>string day_seq = 14;</code>
   * @return The daySeq.
   */
  @java.lang.Override
  public java.lang.String getDaySeq() {
    java.lang.Object ref = daySeq_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      daySeq_ = s;
      return s;
    }
  }
  /**
   * <code>string day_seq = 14;</code>
   * @return The bytes for daySeq.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDaySeqBytes() {
    java.lang.Object ref = daySeq_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      daySeq_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DELIVERY_TYPE_FIELD_NUMBER = 15;
  private int deliveryType_;
  /**
   * <code>int32 delivery_type = 15;</code>
   * @return The deliveryType.
   */
  @java.lang.Override
  public int getDeliveryType() {
    return deliveryType_;
  }

  public static final int DELIVERY_NAME_FIELD_NUMBER = 16;
  private volatile java.lang.Object deliveryName_;
  /**
   * <code>string delivery_name = 16;</code>
   * @return The deliveryName.
   */
  @java.lang.Override
  public java.lang.String getDeliveryName() {
    java.lang.Object ref = deliveryName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deliveryName_ = s;
      return s;
    }
  }
  /**
   * <code>string delivery_name = 16;</code>
   * @return The bytes for deliveryName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDeliveryNameBytes() {
    java.lang.Object ref = deliveryName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deliveryName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INVOICE_TITLE_FIELD_NUMBER = 17;
  private volatile java.lang.Object invoiceTitle_;
  /**
   * <code>string invoice_title = 17;</code>
   * @return The invoiceTitle.
   */
  @java.lang.Override
  public java.lang.String getInvoiceTitle() {
    java.lang.Object ref = invoiceTitle_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      invoiceTitle_ = s;
      return s;
    }
  }
  /**
   * <code>string invoice_title = 17;</code>
   * @return The bytes for invoiceTitle.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getInvoiceTitleBytes() {
    java.lang.Object ref = invoiceTitle_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      invoiceTitle_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int WAITING_TIME_FIELD_NUMBER = 18;
  private volatile java.lang.Object waitingTime_;
  /**
   * <code>string waiting_time = 18;</code>
   * @return The waitingTime.
   */
  @java.lang.Override
  public java.lang.String getWaitingTime() {
    java.lang.Object ref = waitingTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      waitingTime_ = s;
      return s;
    }
  }
  /**
   * <code>string waiting_time = 18;</code>
   * @return The bytes for waitingTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getWaitingTimeBytes() {
    java.lang.Object ref = waitingTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      waitingTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TABLEWARE_NUM_FIELD_NUMBER = 19;
  private int tablewareNum_;
  /**
   * <code>int32 tableware_num = 19;</code>
   * @return The tablewareNum.
   */
  @java.lang.Override
  public int getTablewareNum() {
    return tablewareNum_;
  }

  public static final int SEND_FEE_FIELD_NUMBER = 20;
  private double sendFee_;
  /**
   * <code>double send_fee = 20;</code>
   * @return The sendFee.
   */
  @java.lang.Override
  public double getSendFee() {
    return sendFee_;
  }

  public static final int PACKAGE_FEE_FIELD_NUMBER = 21;
  private double packageFee_;
  /**
   * <code>double package_fee = 21;</code>
   * @return The packageFee.
   */
  @java.lang.Override
  public double getPackageFee() {
    return packageFee_;
  }

  public static final int DELIVERY_TIME_FIELD_NUMBER = 22;
  private volatile java.lang.Object deliveryTime_;
  /**
   * <code>string delivery_time = 22;</code>
   * @return The deliveryTime.
   */
  @java.lang.Override
  public java.lang.String getDeliveryTime() {
    java.lang.Object ref = deliveryTime_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deliveryTime_ = s;
      return s;
    }
  }
  /**
   * <code>string delivery_time = 22;</code>
   * @return The bytes for deliveryTime.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDeliveryTimeBytes() {
    java.lang.Object ref = deliveryTime_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deliveryTime_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TAKE_MEAL_SN_FIELD_NUMBER = 23;
  private volatile java.lang.Object takeMealSn_;
  /**
   * <code>string take_meal_sn = 23;</code>
   * @return The takeMealSn.
   */
  @java.lang.Override
  public java.lang.String getTakeMealSn() {
    java.lang.Object ref = takeMealSn_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      takeMealSn_ = s;
      return s;
    }
  }
  /**
   * <code>string take_meal_sn = 23;</code>
   * @return The bytes for takeMealSn.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTakeMealSnBytes() {
    java.lang.Object ref = takeMealSn_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      takeMealSn_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PARTNERPLATFORMID_FIELD_NUMBER = 24;
  private int partnerPlatformId_;
  /**
   * <code>int32 partnerPlatformId = 24;</code>
   * @return The partnerPlatformId.
   */
  @java.lang.Override
  public int getPartnerPlatformId() {
    return partnerPlatformId_;
  }

  public static final int PARTNERPLATFORMNAME_FIELD_NUMBER = 25;
  private volatile java.lang.Object partnerPlatformName_;
  /**
   * <code>string partnerPlatformName = 25;</code>
   * @return The partnerPlatformName.
   */
  @java.lang.Override
  public java.lang.String getPartnerPlatformName() {
    java.lang.Object ref = partnerPlatformName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      partnerPlatformName_ = s;
      return s;
    }
  }
  /**
   * <code>string partnerPlatformName = 25;</code>
   * @return The bytes for partnerPlatformName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPartnerPlatformNameBytes() {
    java.lang.Object ref = partnerPlatformName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      partnerPlatformName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int WXNAME_FIELD_NUMBER = 26;
  private volatile java.lang.Object wxName_;
  /**
   * <code>string wxName = 26;</code>
   * @return The wxName.
   */
  @java.lang.Override
  public java.lang.String getWxName() {
    java.lang.Object ref = wxName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      wxName_ = s;
      return s;
    }
  }
  /**
   * <code>string wxName = 26;</code>
   * @return The bytes for wxName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getWxNameBytes() {
    java.lang.Object ref = wxName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      wxName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ISHIGHPRIORITY_FIELD_NUMBER = 27;
  private boolean isHighPriority_;
  /**
   * <code>bool isHighPriority = 27;</code>
   * @return The isHighPriority.
   */
  @java.lang.Override
  public boolean getIsHighPriority() {
    return isHighPriority_;
  }

  public static final int TAKEOUTTYPE_FIELD_NUMBER = 28;
  private volatile java.lang.Object takeoutType_;
  /**
   * <pre>
   *外卖订单类型
   * </pre>
   *
   * <code>string takeoutType = 28;</code>
   * @return The takeoutType.
   */
  @java.lang.Override
  public java.lang.String getTakeoutType() {
    java.lang.Object ref = takeoutType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      takeoutType_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *外卖订单类型
   * </pre>
   *
   * <code>string takeoutType = 28;</code>
   * @return The bytes for takeoutType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTakeoutTypeBytes() {
    java.lang.Object ref = takeoutType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      takeoutType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ORIGINALORDERNO_FIELD_NUMBER = 29;
  private volatile java.lang.Object originalOrderNo_;
  /**
   * <pre>
   *部分退款时的外卖原单号
   * </pre>
   *
   * <code>string originalOrderNo = 29;</code>
   * @return The originalOrderNo.
   */
  @java.lang.Override
  public java.lang.String getOriginalOrderNo() {
    java.lang.Object ref = originalOrderNo_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      originalOrderNo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *部分退款时的外卖原单号
   * </pre>
   *
   * <code>string originalOrderNo = 29;</code>
   * @return The bytes for originalOrderNo.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOriginalOrderNoBytes() {
    java.lang.Object ref = originalOrderNo_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      originalOrderNo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MERCHANT_SEND_FEE_FIELD_NUMBER = 30;
  private float merchantSendFee_;
  /**
   * <pre>
   *商家替用户承担配送费
   * </pre>
   *
   * <code>float merchant_send_fee = 30;</code>
   * @return The merchantSendFee.
   */
  @java.lang.Override
  public float getMerchantSendFee() {
    return merchantSendFee_;
  }

  public static final int SELFDELIVERY_FIELD_NUMBER = 31;
  private boolean selfDelivery_;
  /**
   * <pre>
   *是否自配送
   * </pre>
   *
   * <code>bool selfDelivery = 31;</code>
   * @return The selfDelivery.
   */
  @java.lang.Override
  public boolean getSelfDelivery() {
    return selfDelivery_;
  }

  public static final int DELIVERY_PHONE_FIELD_NUMBER = 32;
  private volatile java.lang.Object deliveryPhone_;
  /**
   * <pre>
   *配送员电话
   * </pre>
   *
   * <code>string delivery_phone = 32;</code>
   * @return The deliveryPhone.
   */
  @java.lang.Override
  public java.lang.String getDeliveryPhone() {
    java.lang.Object ref = deliveryPhone_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deliveryPhone_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *配送员电话
   * </pre>
   *
   * <code>string delivery_phone = 32;</code>
   * @return The bytes for deliveryPhone.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDeliveryPhoneBytes() {
    java.lang.Object ref = deliveryPhone_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deliveryPhone_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DELIVERY_PLATFORM_FIELD_NUMBER = 33;
  private volatile java.lang.Object deliveryPlatform_;
  /**
   * <pre>
   *配送平台
   * </pre>
   *
   * <code>string delivery_platform = 33;</code>
   * @return The deliveryPlatform.
   */
  @java.lang.Override
  public java.lang.String getDeliveryPlatform() {
    java.lang.Object ref = deliveryPlatform_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      deliveryPlatform_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *配送平台
   * </pre>
   *
   * <code>string delivery_platform = 33;</code>
   * @return The bytes for deliveryPlatform.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDeliveryPlatformBytes() {
    java.lang.Object ref = deliveryPlatform_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      deliveryPlatform_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INVOICE_TYPE_FIELD_NUMBER = 34;
  private volatile java.lang.Object invoiceType_;
  /**
   * <pre>
   *发票类型
   * </pre>
   *
   * <code>string invoice_type = 34;</code>
   * @return The invoiceType.
   */
  @java.lang.Override
  public java.lang.String getInvoiceType() {
    java.lang.Object ref = invoiceType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      invoiceType_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *发票类型
   * </pre>
   *
   * <code>string invoice_type = 34;</code>
   * @return The bytes for invoiceType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getInvoiceTypeBytes() {
    java.lang.Object ref = invoiceType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      invoiceType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INVOICE_TAX_PAYER_ID_FIELD_NUMBER = 35;
  private volatile java.lang.Object invoiceTaxPayerId_;
  /**
   * <pre>
   *纳税人识别号
   * </pre>
   *
   * <code>string invoice_tax_payer_id = 35;</code>
   * @return The invoiceTaxPayerId.
   */
  @java.lang.Override
  public java.lang.String getInvoiceTaxPayerId() {
    java.lang.Object ref = invoiceTaxPayerId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      invoiceTaxPayerId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *纳税人识别号
   * </pre>
   *
   * <code>string invoice_tax_payer_id = 35;</code>
   * @return The bytes for invoiceTaxPayerId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getInvoiceTaxPayerIdBytes() {
    java.lang.Object ref = invoiceTaxPayerId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      invoiceTaxPayerId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INVOICE_EMAIL_FIELD_NUMBER = 36;
  private volatile java.lang.Object invoiceEmail_;
  /**
   * <pre>
   *用户取发票邮箱
   * </pre>
   *
   * <code>string invoice_email = 36;</code>
   * @return The invoiceEmail.
   */
  @java.lang.Override
  public java.lang.String getInvoiceEmail() {
    java.lang.Object ref = invoiceEmail_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      invoiceEmail_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *用户取发票邮箱
   * </pre>
   *
   * <code>string invoice_email = 36;</code>
   * @return The bytes for invoiceEmail.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getInvoiceEmailBytes() {
    java.lang.Object ref = invoiceEmail_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      invoiceEmail_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PLATFORM_SEND_FEE_FIELD_NUMBER = 37;
  private float platformSendFee_;
  /**
   * <pre>
   *平台承担配送费
   * </pre>
   *
   * <code>float platform_send_fee = 37;</code>
   * @return The platformSendFee.
   */
  @java.lang.Override
  public float getPlatformSendFee() {
    return platformSendFee_;
  }

  public static final int SEND_FEE_FOR_PLATFORM_FIELD_NUMBER = 38;
  private float sendFeeForPlatform_;
  /**
   * <code>float send_fee_for_platform = 38;</code>
   * @return The sendFeeForPlatform.
   */
  @java.lang.Override
  public float getSendFeeForPlatform() {
    return sendFeeForPlatform_;
  }

  public static final int SEND_FEE_FOR_MERCHANT_FIELD_NUMBER = 39;
  private float sendFeeForMerchant_;
  /**
   * <code>float send_fee_for_merchant = 39;</code>
   * @return The sendFeeForMerchant.
   */
  @java.lang.Override
  public float getSendFeeForMerchant() {
    return sendFeeForMerchant_;
  }

  public static final int INVOICE_PROVIDER_FIELD_NUMBER = 40;
  private volatile java.lang.Object invoiceProvider_;
  /**
   * <pre>
   * 开票供应商
   * </pre>
   *
   * <code>string invoice_provider = 40;</code>
   * @return The invoiceProvider.
   */
  @java.lang.Override
  public java.lang.String getInvoiceProvider() {
    java.lang.Object ref = invoiceProvider_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      invoiceProvider_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 开票供应商
   * </pre>
   *
   * <code>string invoice_provider = 40;</code>
   * @return The bytes for invoiceProvider.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getInvoiceProviderBytes() {
    java.lang.Object ref = invoiceProvider_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      invoiceProvider_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INVOICE_AMOUNT_FIELD_NUMBER = 41;
  private volatile java.lang.Object invoiceAmount_;
  /**
   * <pre>
   * 开票金额
   * </pre>
   *
   * <code>string invoice_amount = 41;</code>
   * @return The invoiceAmount.
   */
  @java.lang.Override
  public java.lang.String getInvoiceAmount() {
    java.lang.Object ref = invoiceAmount_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      invoiceAmount_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 开票金额
   * </pre>
   *
   * <code>string invoice_amount = 41;</code>
   * @return The bytes for invoiceAmount.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getInvoiceAmountBytes() {
    java.lang.Object ref = invoiceAmount_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      invoiceAmount_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INVOICE_URL_FIELD_NUMBER = 42;
  private volatile java.lang.Object invoiceUrl_;
  /**
   * <pre>
   * 开票链接
   * </pre>
   *
   * <code>string invoice_url = 42;</code>
   * @return The invoiceUrl.
   */
  @java.lang.Override
  public java.lang.String getInvoiceUrl() {
    java.lang.Object ref = invoiceUrl_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      invoiceUrl_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 开票链接
   * </pre>
   *
   * <code>string invoice_url = 42;</code>
   * @return The bytes for invoiceUrl.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getInvoiceUrlBytes() {
    java.lang.Object ref = invoiceUrl_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      invoiceUrl_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getOrderMethodBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, orderMethod_);
    }
    if (isPaid_ != false) {
      output.writeBool(3, isPaid_);
    }
    if (!getTpOrderIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, tpOrderId_);
    }
    if (!getOrderTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, orderTime_);
    }
    if (!getDeliverTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, deliverTime_);
    }
    if (!getDescriptionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, description_);
    }
    if (!getConsigneeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, consignee_);
    }
    if (!getDeliveryPoiAddressBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, deliveryPoiAddress_);
    }
    for (int i = 0; i < phoneList_.size(); i++) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, phoneList_.getRaw(i));
    }
    if (!getTpBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, tp_);
    }
    if (!getSourceBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 12, source_);
    }
    if (!getSourceOrderIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 13, sourceOrderId_);
    }
    if (!getDaySeqBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 14, daySeq_);
    }
    if (deliveryType_ != 0) {
      output.writeInt32(15, deliveryType_);
    }
    if (!getDeliveryNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 16, deliveryName_);
    }
    if (!getInvoiceTitleBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 17, invoiceTitle_);
    }
    if (!getWaitingTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 18, waitingTime_);
    }
    if (tablewareNum_ != 0) {
      output.writeInt32(19, tablewareNum_);
    }
    if (sendFee_ != 0D) {
      output.writeDouble(20, sendFee_);
    }
    if (packageFee_ != 0D) {
      output.writeDouble(21, packageFee_);
    }
    if (!getDeliveryTimeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 22, deliveryTime_);
    }
    if (!getTakeMealSnBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 23, takeMealSn_);
    }
    if (partnerPlatformId_ != 0) {
      output.writeInt32(24, partnerPlatformId_);
    }
    if (!getPartnerPlatformNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 25, partnerPlatformName_);
    }
    if (!getWxNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 26, wxName_);
    }
    if (isHighPriority_ != false) {
      output.writeBool(27, isHighPriority_);
    }
    if (!getTakeoutTypeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 28, takeoutType_);
    }
    if (!getOriginalOrderNoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 29, originalOrderNo_);
    }
    if (merchantSendFee_ != 0F) {
      output.writeFloat(30, merchantSendFee_);
    }
    if (selfDelivery_ != false) {
      output.writeBool(31, selfDelivery_);
    }
    if (!getDeliveryPhoneBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 32, deliveryPhone_);
    }
    if (!getDeliveryPlatformBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 33, deliveryPlatform_);
    }
    if (!getInvoiceTypeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 34, invoiceType_);
    }
    if (!getInvoiceTaxPayerIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 35, invoiceTaxPayerId_);
    }
    if (!getInvoiceEmailBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 36, invoiceEmail_);
    }
    if (platformSendFee_ != 0F) {
      output.writeFloat(37, platformSendFee_);
    }
    if (sendFeeForPlatform_ != 0F) {
      output.writeFloat(38, sendFeeForPlatform_);
    }
    if (sendFeeForMerchant_ != 0F) {
      output.writeFloat(39, sendFeeForMerchant_);
    }
    if (!getInvoiceProviderBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 40, invoiceProvider_);
    }
    if (!getInvoiceAmountBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 41, invoiceAmount_);
    }
    if (!getInvoiceUrlBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 42, invoiceUrl_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getOrderMethodBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, orderMethod_);
    }
    if (isPaid_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(3, isPaid_);
    }
    if (!getTpOrderIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, tpOrderId_);
    }
    if (!getOrderTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, orderTime_);
    }
    if (!getDeliverTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, deliverTime_);
    }
    if (!getDescriptionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, description_);
    }
    if (!getConsigneeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, consignee_);
    }
    if (!getDeliveryPoiAddressBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, deliveryPoiAddress_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < phoneList_.size(); i++) {
        dataSize += computeStringSizeNoTag(phoneList_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getPhoneListList().size();
    }
    if (!getTpBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, tp_);
    }
    if (!getSourceBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, source_);
    }
    if (!getSourceOrderIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, sourceOrderId_);
    }
    if (!getDaySeqBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, daySeq_);
    }
    if (deliveryType_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(15, deliveryType_);
    }
    if (!getDeliveryNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, deliveryName_);
    }
    if (!getInvoiceTitleBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, invoiceTitle_);
    }
    if (!getWaitingTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(18, waitingTime_);
    }
    if (tablewareNum_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(19, tablewareNum_);
    }
    if (sendFee_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(20, sendFee_);
    }
    if (packageFee_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(21, packageFee_);
    }
    if (!getDeliveryTimeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(22, deliveryTime_);
    }
    if (!getTakeMealSnBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(23, takeMealSn_);
    }
    if (partnerPlatformId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(24, partnerPlatformId_);
    }
    if (!getPartnerPlatformNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(25, partnerPlatformName_);
    }
    if (!getWxNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(26, wxName_);
    }
    if (isHighPriority_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(27, isHighPriority_);
    }
    if (!getTakeoutTypeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(28, takeoutType_);
    }
    if (!getOriginalOrderNoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(29, originalOrderNo_);
    }
    if (merchantSendFee_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(30, merchantSendFee_);
    }
    if (selfDelivery_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(31, selfDelivery_);
    }
    if (!getDeliveryPhoneBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(32, deliveryPhone_);
    }
    if (!getDeliveryPlatformBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(33, deliveryPlatform_);
    }
    if (!getInvoiceTypeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(34, invoiceType_);
    }
    if (!getInvoiceTaxPayerIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(35, invoiceTaxPayerId_);
    }
    if (!getInvoiceEmailBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(36, invoiceEmail_);
    }
    if (platformSendFee_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(37, platformSendFee_);
    }
    if (sendFeeForPlatform_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(38, sendFeeForPlatform_);
    }
    if (sendFeeForMerchant_ != 0F) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(39, sendFeeForMerchant_);
    }
    if (!getInvoiceProviderBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(40, invoiceProvider_);
    }
    if (!getInvoiceAmountBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(41, invoiceAmount_);
    }
    if (!getInvoiceUrlBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(42, invoiceUrl_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.eticket.Takeaway)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.eticket.Takeaway other = (cn.hexcloud.pbis.common.service.integration.eticket.Takeaway) obj;

    if (!getOrderMethod()
        .equals(other.getOrderMethod())) return false;
    if (getIsPaid()
        != other.getIsPaid()) return false;
    if (!getTpOrderId()
        .equals(other.getTpOrderId())) return false;
    if (!getOrderTime()
        .equals(other.getOrderTime())) return false;
    if (!getDeliverTime()
        .equals(other.getDeliverTime())) return false;
    if (!getDescription()
        .equals(other.getDescription())) return false;
    if (!getConsignee()
        .equals(other.getConsignee())) return false;
    if (!getDeliveryPoiAddress()
        .equals(other.getDeliveryPoiAddress())) return false;
    if (!getPhoneListList()
        .equals(other.getPhoneListList())) return false;
    if (!getTp()
        .equals(other.getTp())) return false;
    if (!getSource()
        .equals(other.getSource())) return false;
    if (!getSourceOrderId()
        .equals(other.getSourceOrderId())) return false;
    if (!getDaySeq()
        .equals(other.getDaySeq())) return false;
    if (getDeliveryType()
        != other.getDeliveryType()) return false;
    if (!getDeliveryName()
        .equals(other.getDeliveryName())) return false;
    if (!getInvoiceTitle()
        .equals(other.getInvoiceTitle())) return false;
    if (!getWaitingTime()
        .equals(other.getWaitingTime())) return false;
    if (getTablewareNum()
        != other.getTablewareNum()) return false;
    if (java.lang.Double.doubleToLongBits(getSendFee())
        != java.lang.Double.doubleToLongBits(
            other.getSendFee())) return false;
    if (java.lang.Double.doubleToLongBits(getPackageFee())
        != java.lang.Double.doubleToLongBits(
            other.getPackageFee())) return false;
    if (!getDeliveryTime()
        .equals(other.getDeliveryTime())) return false;
    if (!getTakeMealSn()
        .equals(other.getTakeMealSn())) return false;
    if (getPartnerPlatformId()
        != other.getPartnerPlatformId()) return false;
    if (!getPartnerPlatformName()
        .equals(other.getPartnerPlatformName())) return false;
    if (!getWxName()
        .equals(other.getWxName())) return false;
    if (getIsHighPriority()
        != other.getIsHighPriority()) return false;
    if (!getTakeoutType()
        .equals(other.getTakeoutType())) return false;
    if (!getOriginalOrderNo()
        .equals(other.getOriginalOrderNo())) return false;
    if (java.lang.Float.floatToIntBits(getMerchantSendFee())
        != java.lang.Float.floatToIntBits(
            other.getMerchantSendFee())) return false;
    if (getSelfDelivery()
        != other.getSelfDelivery()) return false;
    if (!getDeliveryPhone()
        .equals(other.getDeliveryPhone())) return false;
    if (!getDeliveryPlatform()
        .equals(other.getDeliveryPlatform())) return false;
    if (!getInvoiceType()
        .equals(other.getInvoiceType())) return false;
    if (!getInvoiceTaxPayerId()
        .equals(other.getInvoiceTaxPayerId())) return false;
    if (!getInvoiceEmail()
        .equals(other.getInvoiceEmail())) return false;
    if (java.lang.Float.floatToIntBits(getPlatformSendFee())
        != java.lang.Float.floatToIntBits(
            other.getPlatformSendFee())) return false;
    if (java.lang.Float.floatToIntBits(getSendFeeForPlatform())
        != java.lang.Float.floatToIntBits(
            other.getSendFeeForPlatform())) return false;
    if (java.lang.Float.floatToIntBits(getSendFeeForMerchant())
        != java.lang.Float.floatToIntBits(
            other.getSendFeeForMerchant())) return false;
    if (!getInvoiceProvider()
        .equals(other.getInvoiceProvider())) return false;
    if (!getInvoiceAmount()
        .equals(other.getInvoiceAmount())) return false;
    if (!getInvoiceUrl()
        .equals(other.getInvoiceUrl())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ORDER_METHOD_FIELD_NUMBER;
    hash = (53 * hash) + getOrderMethod().hashCode();
    hash = (37 * hash) + IS_PAID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIsPaid());
    hash = (37 * hash) + TP_ORDER_ID_FIELD_NUMBER;
    hash = (53 * hash) + getTpOrderId().hashCode();
    hash = (37 * hash) + ORDER_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getOrderTime().hashCode();
    hash = (37 * hash) + DELIVER_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getDeliverTime().hashCode();
    hash = (37 * hash) + DESCRIPTION_FIELD_NUMBER;
    hash = (53 * hash) + getDescription().hashCode();
    hash = (37 * hash) + CONSIGNEE_FIELD_NUMBER;
    hash = (53 * hash) + getConsignee().hashCode();
    hash = (37 * hash) + DELIVERY_POI_ADDRESS_FIELD_NUMBER;
    hash = (53 * hash) + getDeliveryPoiAddress().hashCode();
    if (getPhoneListCount() > 0) {
      hash = (37 * hash) + PHONE_LIST_FIELD_NUMBER;
      hash = (53 * hash) + getPhoneListList().hashCode();
    }
    hash = (37 * hash) + TP_FIELD_NUMBER;
    hash = (53 * hash) + getTp().hashCode();
    hash = (37 * hash) + SOURCE_FIELD_NUMBER;
    hash = (53 * hash) + getSource().hashCode();
    hash = (37 * hash) + SOURCE_ORDER_ID_FIELD_NUMBER;
    hash = (53 * hash) + getSourceOrderId().hashCode();
    hash = (37 * hash) + DAY_SEQ_FIELD_NUMBER;
    hash = (53 * hash) + getDaySeq().hashCode();
    hash = (37 * hash) + DELIVERY_TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getDeliveryType();
    hash = (37 * hash) + DELIVERY_NAME_FIELD_NUMBER;
    hash = (53 * hash) + getDeliveryName().hashCode();
    hash = (37 * hash) + INVOICE_TITLE_FIELD_NUMBER;
    hash = (53 * hash) + getInvoiceTitle().hashCode();
    hash = (37 * hash) + WAITING_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getWaitingTime().hashCode();
    hash = (37 * hash) + TABLEWARE_NUM_FIELD_NUMBER;
    hash = (53 * hash) + getTablewareNum();
    hash = (37 * hash) + SEND_FEE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getSendFee()));
    hash = (37 * hash) + PACKAGE_FEE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        java.lang.Double.doubleToLongBits(getPackageFee()));
    hash = (37 * hash) + DELIVERY_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getDeliveryTime().hashCode();
    hash = (37 * hash) + TAKE_MEAL_SN_FIELD_NUMBER;
    hash = (53 * hash) + getTakeMealSn().hashCode();
    hash = (37 * hash) + PARTNERPLATFORMID_FIELD_NUMBER;
    hash = (53 * hash) + getPartnerPlatformId();
    hash = (37 * hash) + PARTNERPLATFORMNAME_FIELD_NUMBER;
    hash = (53 * hash) + getPartnerPlatformName().hashCode();
    hash = (37 * hash) + WXNAME_FIELD_NUMBER;
    hash = (53 * hash) + getWxName().hashCode();
    hash = (37 * hash) + ISHIGHPRIORITY_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIsHighPriority());
    hash = (37 * hash) + TAKEOUTTYPE_FIELD_NUMBER;
    hash = (53 * hash) + getTakeoutType().hashCode();
    hash = (37 * hash) + ORIGINALORDERNO_FIELD_NUMBER;
    hash = (53 * hash) + getOriginalOrderNo().hashCode();
    hash = (37 * hash) + MERCHANT_SEND_FEE_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getMerchantSendFee());
    hash = (37 * hash) + SELFDELIVERY_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getSelfDelivery());
    hash = (37 * hash) + DELIVERY_PHONE_FIELD_NUMBER;
    hash = (53 * hash) + getDeliveryPhone().hashCode();
    hash = (37 * hash) + DELIVERY_PLATFORM_FIELD_NUMBER;
    hash = (53 * hash) + getDeliveryPlatform().hashCode();
    hash = (37 * hash) + INVOICE_TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getInvoiceType().hashCode();
    hash = (37 * hash) + INVOICE_TAX_PAYER_ID_FIELD_NUMBER;
    hash = (53 * hash) + getInvoiceTaxPayerId().hashCode();
    hash = (37 * hash) + INVOICE_EMAIL_FIELD_NUMBER;
    hash = (53 * hash) + getInvoiceEmail().hashCode();
    hash = (37 * hash) + PLATFORM_SEND_FEE_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getPlatformSendFee());
    hash = (37 * hash) + SEND_FEE_FOR_PLATFORM_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getSendFeeForPlatform());
    hash = (37 * hash) + SEND_FEE_FOR_MERCHANT_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getSendFeeForMerchant());
    hash = (37 * hash) + INVOICE_PROVIDER_FIELD_NUMBER;
    hash = (53 * hash) + getInvoiceProvider().hashCode();
    hash = (37 * hash) + INVOICE_AMOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getInvoiceAmount().hashCode();
    hash = (37 * hash) + INVOICE_URL_FIELD_NUMBER;
    hash = (53 * hash) + getInvoiceUrl().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.Takeaway parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Takeaway parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Takeaway parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Takeaway parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Takeaway parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Takeaway parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Takeaway parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Takeaway parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Takeaway parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Takeaway parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Takeaway parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.Takeaway parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.eticket.Takeaway prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code eticket_proto.Takeaway}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:eticket_proto.Takeaway)
      cn.hexcloud.pbis.common.service.integration.eticket.TakeawayOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Takeaway_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Takeaway_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.eticket.Takeaway.class, cn.hexcloud.pbis.common.service.integration.eticket.Takeaway.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.eticket.Takeaway.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      orderMethod_ = "";

      isPaid_ = false;

      tpOrderId_ = "";

      orderTime_ = "";

      deliverTime_ = "";

      description_ = "";

      consignee_ = "";

      deliveryPoiAddress_ = "";

      phoneList_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      bitField0_ = (bitField0_ & ~0x00000001);
      tp_ = "";

      source_ = "";

      sourceOrderId_ = "";

      daySeq_ = "";

      deliveryType_ = 0;

      deliveryName_ = "";

      invoiceTitle_ = "";

      waitingTime_ = "";

      tablewareNum_ = 0;

      sendFee_ = 0D;

      packageFee_ = 0D;

      deliveryTime_ = "";

      takeMealSn_ = "";

      partnerPlatformId_ = 0;

      partnerPlatformName_ = "";

      wxName_ = "";

      isHighPriority_ = false;

      takeoutType_ = "";

      originalOrderNo_ = "";

      merchantSendFee_ = 0F;

      selfDelivery_ = false;

      deliveryPhone_ = "";

      deliveryPlatform_ = "";

      invoiceType_ = "";

      invoiceTaxPayerId_ = "";

      invoiceEmail_ = "";

      platformSendFee_ = 0F;

      sendFeeForPlatform_ = 0F;

      sendFeeForMerchant_ = 0F;

      invoiceProvider_ = "";

      invoiceAmount_ = "";

      invoiceUrl_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.TicketOuterClass.internal_static_eticket_proto_Takeaway_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Takeaway getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.Takeaway.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Takeaway build() {
      cn.hexcloud.pbis.common.service.integration.eticket.Takeaway result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.Takeaway buildPartial() {
      cn.hexcloud.pbis.common.service.integration.eticket.Takeaway result = new cn.hexcloud.pbis.common.service.integration.eticket.Takeaway(this);
      int from_bitField0_ = bitField0_;
      result.orderMethod_ = orderMethod_;
      result.isPaid_ = isPaid_;
      result.tpOrderId_ = tpOrderId_;
      result.orderTime_ = orderTime_;
      result.deliverTime_ = deliverTime_;
      result.description_ = description_;
      result.consignee_ = consignee_;
      result.deliveryPoiAddress_ = deliveryPoiAddress_;
      if (((bitField0_ & 0x00000001) != 0)) {
        phoneList_ = phoneList_.getUnmodifiableView();
        bitField0_ = (bitField0_ & ~0x00000001);
      }
      result.phoneList_ = phoneList_;
      result.tp_ = tp_;
      result.source_ = source_;
      result.sourceOrderId_ = sourceOrderId_;
      result.daySeq_ = daySeq_;
      result.deliveryType_ = deliveryType_;
      result.deliveryName_ = deliveryName_;
      result.invoiceTitle_ = invoiceTitle_;
      result.waitingTime_ = waitingTime_;
      result.tablewareNum_ = tablewareNum_;
      result.sendFee_ = sendFee_;
      result.packageFee_ = packageFee_;
      result.deliveryTime_ = deliveryTime_;
      result.takeMealSn_ = takeMealSn_;
      result.partnerPlatformId_ = partnerPlatformId_;
      result.partnerPlatformName_ = partnerPlatformName_;
      result.wxName_ = wxName_;
      result.isHighPriority_ = isHighPriority_;
      result.takeoutType_ = takeoutType_;
      result.originalOrderNo_ = originalOrderNo_;
      result.merchantSendFee_ = merchantSendFee_;
      result.selfDelivery_ = selfDelivery_;
      result.deliveryPhone_ = deliveryPhone_;
      result.deliveryPlatform_ = deliveryPlatform_;
      result.invoiceType_ = invoiceType_;
      result.invoiceTaxPayerId_ = invoiceTaxPayerId_;
      result.invoiceEmail_ = invoiceEmail_;
      result.platformSendFee_ = platformSendFee_;
      result.sendFeeForPlatform_ = sendFeeForPlatform_;
      result.sendFeeForMerchant_ = sendFeeForMerchant_;
      result.invoiceProvider_ = invoiceProvider_;
      result.invoiceAmount_ = invoiceAmount_;
      result.invoiceUrl_ = invoiceUrl_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.eticket.Takeaway) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.eticket.Takeaway)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.eticket.Takeaway other) {
      if (other == cn.hexcloud.pbis.common.service.integration.eticket.Takeaway.getDefaultInstance()) return this;
      if (!other.getOrderMethod().isEmpty()) {
        orderMethod_ = other.orderMethod_;
        onChanged();
      }
      if (other.getIsPaid() != false) {
        setIsPaid(other.getIsPaid());
      }
      if (!other.getTpOrderId().isEmpty()) {
        tpOrderId_ = other.tpOrderId_;
        onChanged();
      }
      if (!other.getOrderTime().isEmpty()) {
        orderTime_ = other.orderTime_;
        onChanged();
      }
      if (!other.getDeliverTime().isEmpty()) {
        deliverTime_ = other.deliverTime_;
        onChanged();
      }
      if (!other.getDescription().isEmpty()) {
        description_ = other.description_;
        onChanged();
      }
      if (!other.getConsignee().isEmpty()) {
        consignee_ = other.consignee_;
        onChanged();
      }
      if (!other.getDeliveryPoiAddress().isEmpty()) {
        deliveryPoiAddress_ = other.deliveryPoiAddress_;
        onChanged();
      }
      if (!other.phoneList_.isEmpty()) {
        if (phoneList_.isEmpty()) {
          phoneList_ = other.phoneList_;
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          ensurePhoneListIsMutable();
          phoneList_.addAll(other.phoneList_);
        }
        onChanged();
      }
      if (!other.getTp().isEmpty()) {
        tp_ = other.tp_;
        onChanged();
      }
      if (!other.getSource().isEmpty()) {
        source_ = other.source_;
        onChanged();
      }
      if (!other.getSourceOrderId().isEmpty()) {
        sourceOrderId_ = other.sourceOrderId_;
        onChanged();
      }
      if (!other.getDaySeq().isEmpty()) {
        daySeq_ = other.daySeq_;
        onChanged();
      }
      if (other.getDeliveryType() != 0) {
        setDeliveryType(other.getDeliveryType());
      }
      if (!other.getDeliveryName().isEmpty()) {
        deliveryName_ = other.deliveryName_;
        onChanged();
      }
      if (!other.getInvoiceTitle().isEmpty()) {
        invoiceTitle_ = other.invoiceTitle_;
        onChanged();
      }
      if (!other.getWaitingTime().isEmpty()) {
        waitingTime_ = other.waitingTime_;
        onChanged();
      }
      if (other.getTablewareNum() != 0) {
        setTablewareNum(other.getTablewareNum());
      }
      if (other.getSendFee() != 0D) {
        setSendFee(other.getSendFee());
      }
      if (other.getPackageFee() != 0D) {
        setPackageFee(other.getPackageFee());
      }
      if (!other.getDeliveryTime().isEmpty()) {
        deliveryTime_ = other.deliveryTime_;
        onChanged();
      }
      if (!other.getTakeMealSn().isEmpty()) {
        takeMealSn_ = other.takeMealSn_;
        onChanged();
      }
      if (other.getPartnerPlatformId() != 0) {
        setPartnerPlatformId(other.getPartnerPlatformId());
      }
      if (!other.getPartnerPlatformName().isEmpty()) {
        partnerPlatformName_ = other.partnerPlatformName_;
        onChanged();
      }
      if (!other.getWxName().isEmpty()) {
        wxName_ = other.wxName_;
        onChanged();
      }
      if (other.getIsHighPriority() != false) {
        setIsHighPriority(other.getIsHighPriority());
      }
      if (!other.getTakeoutType().isEmpty()) {
        takeoutType_ = other.takeoutType_;
        onChanged();
      }
      if (!other.getOriginalOrderNo().isEmpty()) {
        originalOrderNo_ = other.originalOrderNo_;
        onChanged();
      }
      if (other.getMerchantSendFee() != 0F) {
        setMerchantSendFee(other.getMerchantSendFee());
      }
      if (other.getSelfDelivery() != false) {
        setSelfDelivery(other.getSelfDelivery());
      }
      if (!other.getDeliveryPhone().isEmpty()) {
        deliveryPhone_ = other.deliveryPhone_;
        onChanged();
      }
      if (!other.getDeliveryPlatform().isEmpty()) {
        deliveryPlatform_ = other.deliveryPlatform_;
        onChanged();
      }
      if (!other.getInvoiceType().isEmpty()) {
        invoiceType_ = other.invoiceType_;
        onChanged();
      }
      if (!other.getInvoiceTaxPayerId().isEmpty()) {
        invoiceTaxPayerId_ = other.invoiceTaxPayerId_;
        onChanged();
      }
      if (!other.getInvoiceEmail().isEmpty()) {
        invoiceEmail_ = other.invoiceEmail_;
        onChanged();
      }
      if (other.getPlatformSendFee() != 0F) {
        setPlatformSendFee(other.getPlatformSendFee());
      }
      if (other.getSendFeeForPlatform() != 0F) {
        setSendFeeForPlatform(other.getSendFeeForPlatform());
      }
      if (other.getSendFeeForMerchant() != 0F) {
        setSendFeeForMerchant(other.getSendFeeForMerchant());
      }
      if (!other.getInvoiceProvider().isEmpty()) {
        invoiceProvider_ = other.invoiceProvider_;
        onChanged();
      }
      if (!other.getInvoiceAmount().isEmpty()) {
        invoiceAmount_ = other.invoiceAmount_;
        onChanged();
      }
      if (!other.getInvoiceUrl().isEmpty()) {
        invoiceUrl_ = other.invoiceUrl_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.eticket.Takeaway parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.eticket.Takeaway) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object orderMethod_ = "";
    /**
     * <code>string order_method = 1;</code>
     * @return The orderMethod.
     */
    public java.lang.String getOrderMethod() {
      java.lang.Object ref = orderMethod_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        orderMethod_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string order_method = 1;</code>
     * @return The bytes for orderMethod.
     */
    public com.google.protobuf.ByteString
        getOrderMethodBytes() {
      java.lang.Object ref = orderMethod_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        orderMethod_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string order_method = 1;</code>
     * @param value The orderMethod to set.
     * @return This builder for chaining.
     */
    public Builder setOrderMethod(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      orderMethod_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string order_method = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearOrderMethod() {
      
      orderMethod_ = getDefaultInstance().getOrderMethod();
      onChanged();
      return this;
    }
    /**
     * <code>string order_method = 1;</code>
     * @param value The bytes for orderMethod to set.
     * @return This builder for chaining.
     */
    public Builder setOrderMethodBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      orderMethod_ = value;
      onChanged();
      return this;
    }

    private boolean isPaid_ ;
    /**
     * <code>bool is_paid = 3;</code>
     * @return The isPaid.
     */
    @java.lang.Override
    public boolean getIsPaid() {
      return isPaid_;
    }
    /**
     * <code>bool is_paid = 3;</code>
     * @param value The isPaid to set.
     * @return This builder for chaining.
     */
    public Builder setIsPaid(boolean value) {
      
      isPaid_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>bool is_paid = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsPaid() {
      
      isPaid_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object tpOrderId_ = "";
    /**
     * <code>string tp_order_id = 4;</code>
     * @return The tpOrderId.
     */
    public java.lang.String getTpOrderId() {
      java.lang.Object ref = tpOrderId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        tpOrderId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string tp_order_id = 4;</code>
     * @return The bytes for tpOrderId.
     */
    public com.google.protobuf.ByteString
        getTpOrderIdBytes() {
      java.lang.Object ref = tpOrderId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tpOrderId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string tp_order_id = 4;</code>
     * @param value The tpOrderId to set.
     * @return This builder for chaining.
     */
    public Builder setTpOrderId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      tpOrderId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string tp_order_id = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearTpOrderId() {
      
      tpOrderId_ = getDefaultInstance().getTpOrderId();
      onChanged();
      return this;
    }
    /**
     * <code>string tp_order_id = 4;</code>
     * @param value The bytes for tpOrderId to set.
     * @return This builder for chaining.
     */
    public Builder setTpOrderIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      tpOrderId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object orderTime_ = "";
    /**
     * <code>string order_time = 5;</code>
     * @return The orderTime.
     */
    public java.lang.String getOrderTime() {
      java.lang.Object ref = orderTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        orderTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string order_time = 5;</code>
     * @return The bytes for orderTime.
     */
    public com.google.protobuf.ByteString
        getOrderTimeBytes() {
      java.lang.Object ref = orderTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        orderTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string order_time = 5;</code>
     * @param value The orderTime to set.
     * @return This builder for chaining.
     */
    public Builder setOrderTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      orderTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string order_time = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearOrderTime() {
      
      orderTime_ = getDefaultInstance().getOrderTime();
      onChanged();
      return this;
    }
    /**
     * <code>string order_time = 5;</code>
     * @param value The bytes for orderTime to set.
     * @return This builder for chaining.
     */
    public Builder setOrderTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      orderTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object deliverTime_ = "";
    /**
     * <code>string deliver_time = 6;</code>
     * @return The deliverTime.
     */
    public java.lang.String getDeliverTime() {
      java.lang.Object ref = deliverTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deliverTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string deliver_time = 6;</code>
     * @return The bytes for deliverTime.
     */
    public com.google.protobuf.ByteString
        getDeliverTimeBytes() {
      java.lang.Object ref = deliverTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deliverTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string deliver_time = 6;</code>
     * @param value The deliverTime to set.
     * @return This builder for chaining.
     */
    public Builder setDeliverTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      deliverTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string deliver_time = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeliverTime() {
      
      deliverTime_ = getDefaultInstance().getDeliverTime();
      onChanged();
      return this;
    }
    /**
     * <code>string deliver_time = 6;</code>
     * @param value The bytes for deliverTime to set.
     * @return This builder for chaining.
     */
    public Builder setDeliverTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      deliverTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object description_ = "";
    /**
     * <code>string description = 7;</code>
     * @return The description.
     */
    public java.lang.String getDescription() {
      java.lang.Object ref = description_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        description_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string description = 7;</code>
     * @return The bytes for description.
     */
    public com.google.protobuf.ByteString
        getDescriptionBytes() {
      java.lang.Object ref = description_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        description_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string description = 7;</code>
     * @param value The description to set.
     * @return This builder for chaining.
     */
    public Builder setDescription(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      description_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string description = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearDescription() {
      
      description_ = getDefaultInstance().getDescription();
      onChanged();
      return this;
    }
    /**
     * <code>string description = 7;</code>
     * @param value The bytes for description to set.
     * @return This builder for chaining.
     */
    public Builder setDescriptionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      description_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object consignee_ = "";
    /**
     * <code>string consignee = 8;</code>
     * @return The consignee.
     */
    public java.lang.String getConsignee() {
      java.lang.Object ref = consignee_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        consignee_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string consignee = 8;</code>
     * @return The bytes for consignee.
     */
    public com.google.protobuf.ByteString
        getConsigneeBytes() {
      java.lang.Object ref = consignee_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        consignee_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string consignee = 8;</code>
     * @param value The consignee to set.
     * @return This builder for chaining.
     */
    public Builder setConsignee(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      consignee_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string consignee = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearConsignee() {
      
      consignee_ = getDefaultInstance().getConsignee();
      onChanged();
      return this;
    }
    /**
     * <code>string consignee = 8;</code>
     * @param value The bytes for consignee to set.
     * @return This builder for chaining.
     */
    public Builder setConsigneeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      consignee_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object deliveryPoiAddress_ = "";
    /**
     * <code>string delivery_poi_address = 9;</code>
     * @return The deliveryPoiAddress.
     */
    public java.lang.String getDeliveryPoiAddress() {
      java.lang.Object ref = deliveryPoiAddress_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deliveryPoiAddress_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string delivery_poi_address = 9;</code>
     * @return The bytes for deliveryPoiAddress.
     */
    public com.google.protobuf.ByteString
        getDeliveryPoiAddressBytes() {
      java.lang.Object ref = deliveryPoiAddress_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deliveryPoiAddress_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string delivery_poi_address = 9;</code>
     * @param value The deliveryPoiAddress to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryPoiAddress(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      deliveryPoiAddress_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string delivery_poi_address = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeliveryPoiAddress() {
      
      deliveryPoiAddress_ = getDefaultInstance().getDeliveryPoiAddress();
      onChanged();
      return this;
    }
    /**
     * <code>string delivery_poi_address = 9;</code>
     * @param value The bytes for deliveryPoiAddress to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryPoiAddressBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      deliveryPoiAddress_ = value;
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringList phoneList_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    private void ensurePhoneListIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        phoneList_ = new com.google.protobuf.LazyStringArrayList(phoneList_);
        bitField0_ |= 0x00000001;
       }
    }
    /**
     * <code>repeated string phone_list = 10;</code>
     * @return A list containing the phoneList.
     */
    public com.google.protobuf.ProtocolStringList
        getPhoneListList() {
      return phoneList_.getUnmodifiableView();
    }
    /**
     * <code>repeated string phone_list = 10;</code>
     * @return The count of phoneList.
     */
    public int getPhoneListCount() {
      return phoneList_.size();
    }
    /**
     * <code>repeated string phone_list = 10;</code>
     * @param index The index of the element to return.
     * @return The phoneList at the given index.
     */
    public java.lang.String getPhoneList(int index) {
      return phoneList_.get(index);
    }
    /**
     * <code>repeated string phone_list = 10;</code>
     * @param index The index of the value to return.
     * @return The bytes of the phoneList at the given index.
     */
    public com.google.protobuf.ByteString
        getPhoneListBytes(int index) {
      return phoneList_.getByteString(index);
    }
    /**
     * <code>repeated string phone_list = 10;</code>
     * @param index The index to set the value at.
     * @param value The phoneList to set.
     * @return This builder for chaining.
     */
    public Builder setPhoneList(
        int index, java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  ensurePhoneListIsMutable();
      phoneList_.set(index, value);
      onChanged();
      return this;
    }
    /**
     * <code>repeated string phone_list = 10;</code>
     * @param value The phoneList to add.
     * @return This builder for chaining.
     */
    public Builder addPhoneList(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  ensurePhoneListIsMutable();
      phoneList_.add(value);
      onChanged();
      return this;
    }
    /**
     * <code>repeated string phone_list = 10;</code>
     * @param values The phoneList to add.
     * @return This builder for chaining.
     */
    public Builder addAllPhoneList(
        java.lang.Iterable<java.lang.String> values) {
      ensurePhoneListIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, phoneList_);
      onChanged();
      return this;
    }
    /**
     * <code>repeated string phone_list = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearPhoneList() {
      phoneList_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>repeated string phone_list = 10;</code>
     * @param value The bytes of the phoneList to add.
     * @return This builder for chaining.
     */
    public Builder addPhoneListBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      ensurePhoneListIsMutable();
      phoneList_.add(value);
      onChanged();
      return this;
    }

    private java.lang.Object tp_ = "";
    /**
     * <code>string tp = 11;</code>
     * @return The tp.
     */
    public java.lang.String getTp() {
      java.lang.Object ref = tp_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        tp_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string tp = 11;</code>
     * @return The bytes for tp.
     */
    public com.google.protobuf.ByteString
        getTpBytes() {
      java.lang.Object ref = tp_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string tp = 11;</code>
     * @param value The tp to set.
     * @return This builder for chaining.
     */
    public Builder setTp(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      tp_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string tp = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearTp() {
      
      tp_ = getDefaultInstance().getTp();
      onChanged();
      return this;
    }
    /**
     * <code>string tp = 11;</code>
     * @param value The bytes for tp to set.
     * @return This builder for chaining.
     */
    public Builder setTpBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      tp_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object source_ = "";
    /**
     * <code>string source = 12;</code>
     * @return The source.
     */
    public java.lang.String getSource() {
      java.lang.Object ref = source_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        source_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string source = 12;</code>
     * @return The bytes for source.
     */
    public com.google.protobuf.ByteString
        getSourceBytes() {
      java.lang.Object ref = source_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        source_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string source = 12;</code>
     * @param value The source to set.
     * @return This builder for chaining.
     */
    public Builder setSource(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      source_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string source = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearSource() {
      
      source_ = getDefaultInstance().getSource();
      onChanged();
      return this;
    }
    /**
     * <code>string source = 12;</code>
     * @param value The bytes for source to set.
     * @return This builder for chaining.
     */
    public Builder setSourceBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      source_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object sourceOrderId_ = "";
    /**
     * <code>string source_order_id = 13;</code>
     * @return The sourceOrderId.
     */
    public java.lang.String getSourceOrderId() {
      java.lang.Object ref = sourceOrderId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sourceOrderId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string source_order_id = 13;</code>
     * @return The bytes for sourceOrderId.
     */
    public com.google.protobuf.ByteString
        getSourceOrderIdBytes() {
      java.lang.Object ref = sourceOrderId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sourceOrderId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string source_order_id = 13;</code>
     * @param value The sourceOrderId to set.
     * @return This builder for chaining.
     */
    public Builder setSourceOrderId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      sourceOrderId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string source_order_id = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearSourceOrderId() {
      
      sourceOrderId_ = getDefaultInstance().getSourceOrderId();
      onChanged();
      return this;
    }
    /**
     * <code>string source_order_id = 13;</code>
     * @param value The bytes for sourceOrderId to set.
     * @return This builder for chaining.
     */
    public Builder setSourceOrderIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      sourceOrderId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object daySeq_ = "";
    /**
     * <code>string day_seq = 14;</code>
     * @return The daySeq.
     */
    public java.lang.String getDaySeq() {
      java.lang.Object ref = daySeq_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        daySeq_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string day_seq = 14;</code>
     * @return The bytes for daySeq.
     */
    public com.google.protobuf.ByteString
        getDaySeqBytes() {
      java.lang.Object ref = daySeq_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        daySeq_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string day_seq = 14;</code>
     * @param value The daySeq to set.
     * @return This builder for chaining.
     */
    public Builder setDaySeq(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      daySeq_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string day_seq = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearDaySeq() {
      
      daySeq_ = getDefaultInstance().getDaySeq();
      onChanged();
      return this;
    }
    /**
     * <code>string day_seq = 14;</code>
     * @param value The bytes for daySeq to set.
     * @return This builder for chaining.
     */
    public Builder setDaySeqBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      daySeq_ = value;
      onChanged();
      return this;
    }

    private int deliveryType_ ;
    /**
     * <code>int32 delivery_type = 15;</code>
     * @return The deliveryType.
     */
    @java.lang.Override
    public int getDeliveryType() {
      return deliveryType_;
    }
    /**
     * <code>int32 delivery_type = 15;</code>
     * @param value The deliveryType to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryType(int value) {
      
      deliveryType_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>int32 delivery_type = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeliveryType() {
      
      deliveryType_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object deliveryName_ = "";
    /**
     * <code>string delivery_name = 16;</code>
     * @return The deliveryName.
     */
    public java.lang.String getDeliveryName() {
      java.lang.Object ref = deliveryName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deliveryName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string delivery_name = 16;</code>
     * @return The bytes for deliveryName.
     */
    public com.google.protobuf.ByteString
        getDeliveryNameBytes() {
      java.lang.Object ref = deliveryName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deliveryName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string delivery_name = 16;</code>
     * @param value The deliveryName to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      deliveryName_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string delivery_name = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeliveryName() {
      
      deliveryName_ = getDefaultInstance().getDeliveryName();
      onChanged();
      return this;
    }
    /**
     * <code>string delivery_name = 16;</code>
     * @param value The bytes for deliveryName to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      deliveryName_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object invoiceTitle_ = "";
    /**
     * <code>string invoice_title = 17;</code>
     * @return The invoiceTitle.
     */
    public java.lang.String getInvoiceTitle() {
      java.lang.Object ref = invoiceTitle_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        invoiceTitle_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string invoice_title = 17;</code>
     * @return The bytes for invoiceTitle.
     */
    public com.google.protobuf.ByteString
        getInvoiceTitleBytes() {
      java.lang.Object ref = invoiceTitle_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        invoiceTitle_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string invoice_title = 17;</code>
     * @param value The invoiceTitle to set.
     * @return This builder for chaining.
     */
    public Builder setInvoiceTitle(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      invoiceTitle_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string invoice_title = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearInvoiceTitle() {
      
      invoiceTitle_ = getDefaultInstance().getInvoiceTitle();
      onChanged();
      return this;
    }
    /**
     * <code>string invoice_title = 17;</code>
     * @param value The bytes for invoiceTitle to set.
     * @return This builder for chaining.
     */
    public Builder setInvoiceTitleBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      invoiceTitle_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object waitingTime_ = "";
    /**
     * <code>string waiting_time = 18;</code>
     * @return The waitingTime.
     */
    public java.lang.String getWaitingTime() {
      java.lang.Object ref = waitingTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        waitingTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string waiting_time = 18;</code>
     * @return The bytes for waitingTime.
     */
    public com.google.protobuf.ByteString
        getWaitingTimeBytes() {
      java.lang.Object ref = waitingTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        waitingTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string waiting_time = 18;</code>
     * @param value The waitingTime to set.
     * @return This builder for chaining.
     */
    public Builder setWaitingTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      waitingTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string waiting_time = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearWaitingTime() {
      
      waitingTime_ = getDefaultInstance().getWaitingTime();
      onChanged();
      return this;
    }
    /**
     * <code>string waiting_time = 18;</code>
     * @param value The bytes for waitingTime to set.
     * @return This builder for chaining.
     */
    public Builder setWaitingTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      waitingTime_ = value;
      onChanged();
      return this;
    }

    private int tablewareNum_ ;
    /**
     * <code>int32 tableware_num = 19;</code>
     * @return The tablewareNum.
     */
    @java.lang.Override
    public int getTablewareNum() {
      return tablewareNum_;
    }
    /**
     * <code>int32 tableware_num = 19;</code>
     * @param value The tablewareNum to set.
     * @return This builder for chaining.
     */
    public Builder setTablewareNum(int value) {
      
      tablewareNum_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>int32 tableware_num = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearTablewareNum() {
      
      tablewareNum_ = 0;
      onChanged();
      return this;
    }

    private double sendFee_ ;
    /**
     * <code>double send_fee = 20;</code>
     * @return The sendFee.
     */
    @java.lang.Override
    public double getSendFee() {
      return sendFee_;
    }
    /**
     * <code>double send_fee = 20;</code>
     * @param value The sendFee to set.
     * @return This builder for chaining.
     */
    public Builder setSendFee(double value) {
      
      sendFee_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double send_fee = 20;</code>
     * @return This builder for chaining.
     */
    public Builder clearSendFee() {
      
      sendFee_ = 0D;
      onChanged();
      return this;
    }

    private double packageFee_ ;
    /**
     * <code>double package_fee = 21;</code>
     * @return The packageFee.
     */
    @java.lang.Override
    public double getPackageFee() {
      return packageFee_;
    }
    /**
     * <code>double package_fee = 21;</code>
     * @param value The packageFee to set.
     * @return This builder for chaining.
     */
    public Builder setPackageFee(double value) {
      
      packageFee_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>double package_fee = 21;</code>
     * @return This builder for chaining.
     */
    public Builder clearPackageFee() {
      
      packageFee_ = 0D;
      onChanged();
      return this;
    }

    private java.lang.Object deliveryTime_ = "";
    /**
     * <code>string delivery_time = 22;</code>
     * @return The deliveryTime.
     */
    public java.lang.String getDeliveryTime() {
      java.lang.Object ref = deliveryTime_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deliveryTime_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string delivery_time = 22;</code>
     * @return The bytes for deliveryTime.
     */
    public com.google.protobuf.ByteString
        getDeliveryTimeBytes() {
      java.lang.Object ref = deliveryTime_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deliveryTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string delivery_time = 22;</code>
     * @param value The deliveryTime to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryTime(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      deliveryTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string delivery_time = 22;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeliveryTime() {
      
      deliveryTime_ = getDefaultInstance().getDeliveryTime();
      onChanged();
      return this;
    }
    /**
     * <code>string delivery_time = 22;</code>
     * @param value The bytes for deliveryTime to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryTimeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      deliveryTime_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object takeMealSn_ = "";
    /**
     * <code>string take_meal_sn = 23;</code>
     * @return The takeMealSn.
     */
    public java.lang.String getTakeMealSn() {
      java.lang.Object ref = takeMealSn_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        takeMealSn_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string take_meal_sn = 23;</code>
     * @return The bytes for takeMealSn.
     */
    public com.google.protobuf.ByteString
        getTakeMealSnBytes() {
      java.lang.Object ref = takeMealSn_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        takeMealSn_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string take_meal_sn = 23;</code>
     * @param value The takeMealSn to set.
     * @return This builder for chaining.
     */
    public Builder setTakeMealSn(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      takeMealSn_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string take_meal_sn = 23;</code>
     * @return This builder for chaining.
     */
    public Builder clearTakeMealSn() {
      
      takeMealSn_ = getDefaultInstance().getTakeMealSn();
      onChanged();
      return this;
    }
    /**
     * <code>string take_meal_sn = 23;</code>
     * @param value The bytes for takeMealSn to set.
     * @return This builder for chaining.
     */
    public Builder setTakeMealSnBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      takeMealSn_ = value;
      onChanged();
      return this;
    }

    private int partnerPlatformId_ ;
    /**
     * <code>int32 partnerPlatformId = 24;</code>
     * @return The partnerPlatformId.
     */
    @java.lang.Override
    public int getPartnerPlatformId() {
      return partnerPlatformId_;
    }
    /**
     * <code>int32 partnerPlatformId = 24;</code>
     * @param value The partnerPlatformId to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerPlatformId(int value) {
      
      partnerPlatformId_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>int32 partnerPlatformId = 24;</code>
     * @return This builder for chaining.
     */
    public Builder clearPartnerPlatformId() {
      
      partnerPlatformId_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object partnerPlatformName_ = "";
    /**
     * <code>string partnerPlatformName = 25;</code>
     * @return The partnerPlatformName.
     */
    public java.lang.String getPartnerPlatformName() {
      java.lang.Object ref = partnerPlatformName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        partnerPlatformName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string partnerPlatformName = 25;</code>
     * @return The bytes for partnerPlatformName.
     */
    public com.google.protobuf.ByteString
        getPartnerPlatformNameBytes() {
      java.lang.Object ref = partnerPlatformName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        partnerPlatformName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string partnerPlatformName = 25;</code>
     * @param value The partnerPlatformName to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerPlatformName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      partnerPlatformName_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string partnerPlatformName = 25;</code>
     * @return This builder for chaining.
     */
    public Builder clearPartnerPlatformName() {
      
      partnerPlatformName_ = getDefaultInstance().getPartnerPlatformName();
      onChanged();
      return this;
    }
    /**
     * <code>string partnerPlatformName = 25;</code>
     * @param value The bytes for partnerPlatformName to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerPlatformNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      partnerPlatformName_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object wxName_ = "";
    /**
     * <code>string wxName = 26;</code>
     * @return The wxName.
     */
    public java.lang.String getWxName() {
      java.lang.Object ref = wxName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        wxName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string wxName = 26;</code>
     * @return The bytes for wxName.
     */
    public com.google.protobuf.ByteString
        getWxNameBytes() {
      java.lang.Object ref = wxName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        wxName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string wxName = 26;</code>
     * @param value The wxName to set.
     * @return This builder for chaining.
     */
    public Builder setWxName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      wxName_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string wxName = 26;</code>
     * @return This builder for chaining.
     */
    public Builder clearWxName() {
      
      wxName_ = getDefaultInstance().getWxName();
      onChanged();
      return this;
    }
    /**
     * <code>string wxName = 26;</code>
     * @param value The bytes for wxName to set.
     * @return This builder for chaining.
     */
    public Builder setWxNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      wxName_ = value;
      onChanged();
      return this;
    }

    private boolean isHighPriority_ ;
    /**
     * <code>bool isHighPriority = 27;</code>
     * @return The isHighPriority.
     */
    @java.lang.Override
    public boolean getIsHighPriority() {
      return isHighPriority_;
    }
    /**
     * <code>bool isHighPriority = 27;</code>
     * @param value The isHighPriority to set.
     * @return This builder for chaining.
     */
    public Builder setIsHighPriority(boolean value) {
      
      isHighPriority_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>bool isHighPriority = 27;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsHighPriority() {
      
      isHighPriority_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object takeoutType_ = "";
    /**
     * <pre>
     *外卖订单类型
     * </pre>
     *
     * <code>string takeoutType = 28;</code>
     * @return The takeoutType.
     */
    public java.lang.String getTakeoutType() {
      java.lang.Object ref = takeoutType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        takeoutType_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *外卖订单类型
     * </pre>
     *
     * <code>string takeoutType = 28;</code>
     * @return The bytes for takeoutType.
     */
    public com.google.protobuf.ByteString
        getTakeoutTypeBytes() {
      java.lang.Object ref = takeoutType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        takeoutType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *外卖订单类型
     * </pre>
     *
     * <code>string takeoutType = 28;</code>
     * @param value The takeoutType to set.
     * @return This builder for chaining.
     */
    public Builder setTakeoutType(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      takeoutType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *外卖订单类型
     * </pre>
     *
     * <code>string takeoutType = 28;</code>
     * @return This builder for chaining.
     */
    public Builder clearTakeoutType() {
      
      takeoutType_ = getDefaultInstance().getTakeoutType();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *外卖订单类型
     * </pre>
     *
     * <code>string takeoutType = 28;</code>
     * @param value The bytes for takeoutType to set.
     * @return This builder for chaining.
     */
    public Builder setTakeoutTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      takeoutType_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object originalOrderNo_ = "";
    /**
     * <pre>
     *部分退款时的外卖原单号
     * </pre>
     *
     * <code>string originalOrderNo = 29;</code>
     * @return The originalOrderNo.
     */
    public java.lang.String getOriginalOrderNo() {
      java.lang.Object ref = originalOrderNo_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        originalOrderNo_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *部分退款时的外卖原单号
     * </pre>
     *
     * <code>string originalOrderNo = 29;</code>
     * @return The bytes for originalOrderNo.
     */
    public com.google.protobuf.ByteString
        getOriginalOrderNoBytes() {
      java.lang.Object ref = originalOrderNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        originalOrderNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *部分退款时的外卖原单号
     * </pre>
     *
     * <code>string originalOrderNo = 29;</code>
     * @param value The originalOrderNo to set.
     * @return This builder for chaining.
     */
    public Builder setOriginalOrderNo(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      originalOrderNo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *部分退款时的外卖原单号
     * </pre>
     *
     * <code>string originalOrderNo = 29;</code>
     * @return This builder for chaining.
     */
    public Builder clearOriginalOrderNo() {
      
      originalOrderNo_ = getDefaultInstance().getOriginalOrderNo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *部分退款时的外卖原单号
     * </pre>
     *
     * <code>string originalOrderNo = 29;</code>
     * @param value The bytes for originalOrderNo to set.
     * @return This builder for chaining.
     */
    public Builder setOriginalOrderNoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      originalOrderNo_ = value;
      onChanged();
      return this;
    }

    private float merchantSendFee_ ;
    /**
     * <pre>
     *商家替用户承担配送费
     * </pre>
     *
     * <code>float merchant_send_fee = 30;</code>
     * @return The merchantSendFee.
     */
    @java.lang.Override
    public float getMerchantSendFee() {
      return merchantSendFee_;
    }
    /**
     * <pre>
     *商家替用户承担配送费
     * </pre>
     *
     * <code>float merchant_send_fee = 30;</code>
     * @param value The merchantSendFee to set.
     * @return This builder for chaining.
     */
    public Builder setMerchantSendFee(float value) {
      
      merchantSendFee_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *商家替用户承担配送费
     * </pre>
     *
     * <code>float merchant_send_fee = 30;</code>
     * @return This builder for chaining.
     */
    public Builder clearMerchantSendFee() {
      
      merchantSendFee_ = 0F;
      onChanged();
      return this;
    }

    private boolean selfDelivery_ ;
    /**
     * <pre>
     *是否自配送
     * </pre>
     *
     * <code>bool selfDelivery = 31;</code>
     * @return The selfDelivery.
     */
    @java.lang.Override
    public boolean getSelfDelivery() {
      return selfDelivery_;
    }
    /**
     * <pre>
     *是否自配送
     * </pre>
     *
     * <code>bool selfDelivery = 31;</code>
     * @param value The selfDelivery to set.
     * @return This builder for chaining.
     */
    public Builder setSelfDelivery(boolean value) {
      
      selfDelivery_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *是否自配送
     * </pre>
     *
     * <code>bool selfDelivery = 31;</code>
     * @return This builder for chaining.
     */
    public Builder clearSelfDelivery() {
      
      selfDelivery_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object deliveryPhone_ = "";
    /**
     * <pre>
     *配送员电话
     * </pre>
     *
     * <code>string delivery_phone = 32;</code>
     * @return The deliveryPhone.
     */
    public java.lang.String getDeliveryPhone() {
      java.lang.Object ref = deliveryPhone_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deliveryPhone_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *配送员电话
     * </pre>
     *
     * <code>string delivery_phone = 32;</code>
     * @return The bytes for deliveryPhone.
     */
    public com.google.protobuf.ByteString
        getDeliveryPhoneBytes() {
      java.lang.Object ref = deliveryPhone_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deliveryPhone_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *配送员电话
     * </pre>
     *
     * <code>string delivery_phone = 32;</code>
     * @param value The deliveryPhone to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryPhone(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      deliveryPhone_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *配送员电话
     * </pre>
     *
     * <code>string delivery_phone = 32;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeliveryPhone() {
      
      deliveryPhone_ = getDefaultInstance().getDeliveryPhone();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *配送员电话
     * </pre>
     *
     * <code>string delivery_phone = 32;</code>
     * @param value The bytes for deliveryPhone to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryPhoneBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      deliveryPhone_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object deliveryPlatform_ = "";
    /**
     * <pre>
     *配送平台
     * </pre>
     *
     * <code>string delivery_platform = 33;</code>
     * @return The deliveryPlatform.
     */
    public java.lang.String getDeliveryPlatform() {
      java.lang.Object ref = deliveryPlatform_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        deliveryPlatform_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *配送平台
     * </pre>
     *
     * <code>string delivery_platform = 33;</code>
     * @return The bytes for deliveryPlatform.
     */
    public com.google.protobuf.ByteString
        getDeliveryPlatformBytes() {
      java.lang.Object ref = deliveryPlatform_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        deliveryPlatform_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *配送平台
     * </pre>
     *
     * <code>string delivery_platform = 33;</code>
     * @param value The deliveryPlatform to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryPlatform(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      deliveryPlatform_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *配送平台
     * </pre>
     *
     * <code>string delivery_platform = 33;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeliveryPlatform() {
      
      deliveryPlatform_ = getDefaultInstance().getDeliveryPlatform();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *配送平台
     * </pre>
     *
     * <code>string delivery_platform = 33;</code>
     * @param value The bytes for deliveryPlatform to set.
     * @return This builder for chaining.
     */
    public Builder setDeliveryPlatformBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      deliveryPlatform_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object invoiceType_ = "";
    /**
     * <pre>
     *发票类型
     * </pre>
     *
     * <code>string invoice_type = 34;</code>
     * @return The invoiceType.
     */
    public java.lang.String getInvoiceType() {
      java.lang.Object ref = invoiceType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        invoiceType_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *发票类型
     * </pre>
     *
     * <code>string invoice_type = 34;</code>
     * @return The bytes for invoiceType.
     */
    public com.google.protobuf.ByteString
        getInvoiceTypeBytes() {
      java.lang.Object ref = invoiceType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        invoiceType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *发票类型
     * </pre>
     *
     * <code>string invoice_type = 34;</code>
     * @param value The invoiceType to set.
     * @return This builder for chaining.
     */
    public Builder setInvoiceType(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      invoiceType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *发票类型
     * </pre>
     *
     * <code>string invoice_type = 34;</code>
     * @return This builder for chaining.
     */
    public Builder clearInvoiceType() {
      
      invoiceType_ = getDefaultInstance().getInvoiceType();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *发票类型
     * </pre>
     *
     * <code>string invoice_type = 34;</code>
     * @param value The bytes for invoiceType to set.
     * @return This builder for chaining.
     */
    public Builder setInvoiceTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      invoiceType_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object invoiceTaxPayerId_ = "";
    /**
     * <pre>
     *纳税人识别号
     * </pre>
     *
     * <code>string invoice_tax_payer_id = 35;</code>
     * @return The invoiceTaxPayerId.
     */
    public java.lang.String getInvoiceTaxPayerId() {
      java.lang.Object ref = invoiceTaxPayerId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        invoiceTaxPayerId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *纳税人识别号
     * </pre>
     *
     * <code>string invoice_tax_payer_id = 35;</code>
     * @return The bytes for invoiceTaxPayerId.
     */
    public com.google.protobuf.ByteString
        getInvoiceTaxPayerIdBytes() {
      java.lang.Object ref = invoiceTaxPayerId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        invoiceTaxPayerId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *纳税人识别号
     * </pre>
     *
     * <code>string invoice_tax_payer_id = 35;</code>
     * @param value The invoiceTaxPayerId to set.
     * @return This builder for chaining.
     */
    public Builder setInvoiceTaxPayerId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      invoiceTaxPayerId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *纳税人识别号
     * </pre>
     *
     * <code>string invoice_tax_payer_id = 35;</code>
     * @return This builder for chaining.
     */
    public Builder clearInvoiceTaxPayerId() {
      
      invoiceTaxPayerId_ = getDefaultInstance().getInvoiceTaxPayerId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *纳税人识别号
     * </pre>
     *
     * <code>string invoice_tax_payer_id = 35;</code>
     * @param value The bytes for invoiceTaxPayerId to set.
     * @return This builder for chaining.
     */
    public Builder setInvoiceTaxPayerIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      invoiceTaxPayerId_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object invoiceEmail_ = "";
    /**
     * <pre>
     *用户取发票邮箱
     * </pre>
     *
     * <code>string invoice_email = 36;</code>
     * @return The invoiceEmail.
     */
    public java.lang.String getInvoiceEmail() {
      java.lang.Object ref = invoiceEmail_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        invoiceEmail_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *用户取发票邮箱
     * </pre>
     *
     * <code>string invoice_email = 36;</code>
     * @return The bytes for invoiceEmail.
     */
    public com.google.protobuf.ByteString
        getInvoiceEmailBytes() {
      java.lang.Object ref = invoiceEmail_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        invoiceEmail_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *用户取发票邮箱
     * </pre>
     *
     * <code>string invoice_email = 36;</code>
     * @param value The invoiceEmail to set.
     * @return This builder for chaining.
     */
    public Builder setInvoiceEmail(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      invoiceEmail_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *用户取发票邮箱
     * </pre>
     *
     * <code>string invoice_email = 36;</code>
     * @return This builder for chaining.
     */
    public Builder clearInvoiceEmail() {
      
      invoiceEmail_ = getDefaultInstance().getInvoiceEmail();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *用户取发票邮箱
     * </pre>
     *
     * <code>string invoice_email = 36;</code>
     * @param value The bytes for invoiceEmail to set.
     * @return This builder for chaining.
     */
    public Builder setInvoiceEmailBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      invoiceEmail_ = value;
      onChanged();
      return this;
    }

    private float platformSendFee_ ;
    /**
     * <pre>
     *平台承担配送费
     * </pre>
     *
     * <code>float platform_send_fee = 37;</code>
     * @return The platformSendFee.
     */
    @java.lang.Override
    public float getPlatformSendFee() {
      return platformSendFee_;
    }
    /**
     * <pre>
     *平台承担配送费
     * </pre>
     *
     * <code>float platform_send_fee = 37;</code>
     * @param value The platformSendFee to set.
     * @return This builder for chaining.
     */
    public Builder setPlatformSendFee(float value) {
      
      platformSendFee_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *平台承担配送费
     * </pre>
     *
     * <code>float platform_send_fee = 37;</code>
     * @return This builder for chaining.
     */
    public Builder clearPlatformSendFee() {
      
      platformSendFee_ = 0F;
      onChanged();
      return this;
    }

    private float sendFeeForPlatform_ ;
    /**
     * <code>float send_fee_for_platform = 38;</code>
     * @return The sendFeeForPlatform.
     */
    @java.lang.Override
    public float getSendFeeForPlatform() {
      return sendFeeForPlatform_;
    }
    /**
     * <code>float send_fee_for_platform = 38;</code>
     * @param value The sendFeeForPlatform to set.
     * @return This builder for chaining.
     */
    public Builder setSendFeeForPlatform(float value) {
      
      sendFeeForPlatform_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>float send_fee_for_platform = 38;</code>
     * @return This builder for chaining.
     */
    public Builder clearSendFeeForPlatform() {
      
      sendFeeForPlatform_ = 0F;
      onChanged();
      return this;
    }

    private float sendFeeForMerchant_ ;
    /**
     * <code>float send_fee_for_merchant = 39;</code>
     * @return The sendFeeForMerchant.
     */
    @java.lang.Override
    public float getSendFeeForMerchant() {
      return sendFeeForMerchant_;
    }
    /**
     * <code>float send_fee_for_merchant = 39;</code>
     * @param value The sendFeeForMerchant to set.
     * @return This builder for chaining.
     */
    public Builder setSendFeeForMerchant(float value) {
      
      sendFeeForMerchant_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>float send_fee_for_merchant = 39;</code>
     * @return This builder for chaining.
     */
    public Builder clearSendFeeForMerchant() {
      
      sendFeeForMerchant_ = 0F;
      onChanged();
      return this;
    }

    private java.lang.Object invoiceProvider_ = "";
    /**
     * <pre>
     * 开票供应商
     * </pre>
     *
     * <code>string invoice_provider = 40;</code>
     * @return The invoiceProvider.
     */
    public java.lang.String getInvoiceProvider() {
      java.lang.Object ref = invoiceProvider_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        invoiceProvider_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 开票供应商
     * </pre>
     *
     * <code>string invoice_provider = 40;</code>
     * @return The bytes for invoiceProvider.
     */
    public com.google.protobuf.ByteString
        getInvoiceProviderBytes() {
      java.lang.Object ref = invoiceProvider_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        invoiceProvider_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 开票供应商
     * </pre>
     *
     * <code>string invoice_provider = 40;</code>
     * @param value The invoiceProvider to set.
     * @return This builder for chaining.
     */
    public Builder setInvoiceProvider(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      invoiceProvider_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 开票供应商
     * </pre>
     *
     * <code>string invoice_provider = 40;</code>
     * @return This builder for chaining.
     */
    public Builder clearInvoiceProvider() {
      
      invoiceProvider_ = getDefaultInstance().getInvoiceProvider();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 开票供应商
     * </pre>
     *
     * <code>string invoice_provider = 40;</code>
     * @param value The bytes for invoiceProvider to set.
     * @return This builder for chaining.
     */
    public Builder setInvoiceProviderBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      invoiceProvider_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object invoiceAmount_ = "";
    /**
     * <pre>
     * 开票金额
     * </pre>
     *
     * <code>string invoice_amount = 41;</code>
     * @return The invoiceAmount.
     */
    public java.lang.String getInvoiceAmount() {
      java.lang.Object ref = invoiceAmount_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        invoiceAmount_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 开票金额
     * </pre>
     *
     * <code>string invoice_amount = 41;</code>
     * @return The bytes for invoiceAmount.
     */
    public com.google.protobuf.ByteString
        getInvoiceAmountBytes() {
      java.lang.Object ref = invoiceAmount_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        invoiceAmount_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 开票金额
     * </pre>
     *
     * <code>string invoice_amount = 41;</code>
     * @param value The invoiceAmount to set.
     * @return This builder for chaining.
     */
    public Builder setInvoiceAmount(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      invoiceAmount_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 开票金额
     * </pre>
     *
     * <code>string invoice_amount = 41;</code>
     * @return This builder for chaining.
     */
    public Builder clearInvoiceAmount() {
      
      invoiceAmount_ = getDefaultInstance().getInvoiceAmount();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 开票金额
     * </pre>
     *
     * <code>string invoice_amount = 41;</code>
     * @param value The bytes for invoiceAmount to set.
     * @return This builder for chaining.
     */
    public Builder setInvoiceAmountBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      invoiceAmount_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object invoiceUrl_ = "";
    /**
     * <pre>
     * 开票链接
     * </pre>
     *
     * <code>string invoice_url = 42;</code>
     * @return The invoiceUrl.
     */
    public java.lang.String getInvoiceUrl() {
      java.lang.Object ref = invoiceUrl_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        invoiceUrl_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 开票链接
     * </pre>
     *
     * <code>string invoice_url = 42;</code>
     * @return The bytes for invoiceUrl.
     */
    public com.google.protobuf.ByteString
        getInvoiceUrlBytes() {
      java.lang.Object ref = invoiceUrl_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        invoiceUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 开票链接
     * </pre>
     *
     * <code>string invoice_url = 42;</code>
     * @param value The invoiceUrl to set.
     * @return This builder for chaining.
     */
    public Builder setInvoiceUrl(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      invoiceUrl_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 开票链接
     * </pre>
     *
     * <code>string invoice_url = 42;</code>
     * @return This builder for chaining.
     */
    public Builder clearInvoiceUrl() {
      
      invoiceUrl_ = getDefaultInstance().getInvoiceUrl();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 开票链接
     * </pre>
     *
     * <code>string invoice_url = 42;</code>
     * @param value The bytes for invoiceUrl to set.
     * @return This builder for chaining.
     */
    public Builder setInvoiceUrlBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      invoiceUrl_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:eticket_proto.Takeaway)
  }

  // @@protoc_insertion_point(class_scope:eticket_proto.Takeaway)
  private static final cn.hexcloud.pbis.common.service.integration.eticket.Takeaway DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.eticket.Takeaway();
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.Takeaway getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Takeaway>
      PARSER = new com.google.protobuf.AbstractParser<Takeaway>() {
    @java.lang.Override
    public Takeaway parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Takeaway(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Takeaway> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Takeaway> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.Takeaway getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

