// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

public interface TakeawayOrBuilder extends
    // @@protoc_insertion_point(interface_extends:eticket_proto.Takeaway)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string order_method = 1;</code>
   * @return The orderMethod.
   */
  java.lang.String getOrderMethod();
  /**
   * <code>string order_method = 1;</code>
   * @return The bytes for orderMethod.
   */
  com.google.protobuf.ByteString
      getOrderMethodBytes();

  /**
   * <code>bool is_paid = 3;</code>
   * @return The isPaid.
   */
  boolean getIsPaid();

  /**
   * <code>string tp_order_id = 4;</code>
   * @return The tpOrderId.
   */
  java.lang.String getTpOrderId();
  /**
   * <code>string tp_order_id = 4;</code>
   * @return The bytes for tpOrderId.
   */
  com.google.protobuf.ByteString
      getTpOrderIdBytes();

  /**
   * <code>string order_time = 5;</code>
   * @return The orderTime.
   */
  java.lang.String getOrderTime();
  /**
   * <code>string order_time = 5;</code>
   * @return The bytes for orderTime.
   */
  com.google.protobuf.ByteString
      getOrderTimeBytes();

  /**
   * <code>string deliver_time = 6;</code>
   * @return The deliverTime.
   */
  java.lang.String getDeliverTime();
  /**
   * <code>string deliver_time = 6;</code>
   * @return The bytes for deliverTime.
   */
  com.google.protobuf.ByteString
      getDeliverTimeBytes();

  /**
   * <code>string description = 7;</code>
   * @return The description.
   */
  java.lang.String getDescription();
  /**
   * <code>string description = 7;</code>
   * @return The bytes for description.
   */
  com.google.protobuf.ByteString
      getDescriptionBytes();

  /**
   * <code>string consignee = 8;</code>
   * @return The consignee.
   */
  java.lang.String getConsignee();
  /**
   * <code>string consignee = 8;</code>
   * @return The bytes for consignee.
   */
  com.google.protobuf.ByteString
      getConsigneeBytes();

  /**
   * <code>string delivery_poi_address = 9;</code>
   * @return The deliveryPoiAddress.
   */
  java.lang.String getDeliveryPoiAddress();
  /**
   * <code>string delivery_poi_address = 9;</code>
   * @return The bytes for deliveryPoiAddress.
   */
  com.google.protobuf.ByteString
      getDeliveryPoiAddressBytes();

  /**
   * <code>repeated string phone_list = 10;</code>
   * @return A list containing the phoneList.
   */
  java.util.List<java.lang.String>
      getPhoneListList();
  /**
   * <code>repeated string phone_list = 10;</code>
   * @return The count of phoneList.
   */
  int getPhoneListCount();
  /**
   * <code>repeated string phone_list = 10;</code>
   * @param index The index of the element to return.
   * @return The phoneList at the given index.
   */
  java.lang.String getPhoneList(int index);
  /**
   * <code>repeated string phone_list = 10;</code>
   * @param index The index of the value to return.
   * @return The bytes of the phoneList at the given index.
   */
  com.google.protobuf.ByteString
      getPhoneListBytes(int index);

  /**
   * <code>string tp = 11;</code>
   * @return The tp.
   */
  java.lang.String getTp();
  /**
   * <code>string tp = 11;</code>
   * @return The bytes for tp.
   */
  com.google.protobuf.ByteString
      getTpBytes();

  /**
   * <code>string source = 12;</code>
   * @return The source.
   */
  java.lang.String getSource();
  /**
   * <code>string source = 12;</code>
   * @return The bytes for source.
   */
  com.google.protobuf.ByteString
      getSourceBytes();

  /**
   * <code>string source_order_id = 13;</code>
   * @return The sourceOrderId.
   */
  java.lang.String getSourceOrderId();
  /**
   * <code>string source_order_id = 13;</code>
   * @return The bytes for sourceOrderId.
   */
  com.google.protobuf.ByteString
      getSourceOrderIdBytes();

  /**
   * <code>string day_seq = 14;</code>
   * @return The daySeq.
   */
  java.lang.String getDaySeq();
  /**
   * <code>string day_seq = 14;</code>
   * @return The bytes for daySeq.
   */
  com.google.protobuf.ByteString
      getDaySeqBytes();

  /**
   * <code>int32 delivery_type = 15;</code>
   * @return The deliveryType.
   */
  int getDeliveryType();

  /**
   * <code>string delivery_name = 16;</code>
   * @return The deliveryName.
   */
  java.lang.String getDeliveryName();
  /**
   * <code>string delivery_name = 16;</code>
   * @return The bytes for deliveryName.
   */
  com.google.protobuf.ByteString
      getDeliveryNameBytes();

  /**
   * <code>string invoice_title = 17;</code>
   * @return The invoiceTitle.
   */
  java.lang.String getInvoiceTitle();
  /**
   * <code>string invoice_title = 17;</code>
   * @return The bytes for invoiceTitle.
   */
  com.google.protobuf.ByteString
      getInvoiceTitleBytes();

  /**
   * <code>string waiting_time = 18;</code>
   * @return The waitingTime.
   */
  java.lang.String getWaitingTime();
  /**
   * <code>string waiting_time = 18;</code>
   * @return The bytes for waitingTime.
   */
  com.google.protobuf.ByteString
      getWaitingTimeBytes();

  /**
   * <code>int32 tableware_num = 19;</code>
   * @return The tablewareNum.
   */
  int getTablewareNum();

  /**
   * <code>double send_fee = 20;</code>
   * @return The sendFee.
   */
  double getSendFee();

  /**
   * <code>double package_fee = 21;</code>
   * @return The packageFee.
   */
  double getPackageFee();

  /**
   * <code>string delivery_time = 22;</code>
   * @return The deliveryTime.
   */
  java.lang.String getDeliveryTime();
  /**
   * <code>string delivery_time = 22;</code>
   * @return The bytes for deliveryTime.
   */
  com.google.protobuf.ByteString
      getDeliveryTimeBytes();

  /**
   * <code>string take_meal_sn = 23;</code>
   * @return The takeMealSn.
   */
  java.lang.String getTakeMealSn();
  /**
   * <code>string take_meal_sn = 23;</code>
   * @return The bytes for takeMealSn.
   */
  com.google.protobuf.ByteString
      getTakeMealSnBytes();

  /**
   * <code>int32 partnerPlatformId = 24;</code>
   * @return The partnerPlatformId.
   */
  int getPartnerPlatformId();

  /**
   * <code>string partnerPlatformName = 25;</code>
   * @return The partnerPlatformName.
   */
  java.lang.String getPartnerPlatformName();
  /**
   * <code>string partnerPlatformName = 25;</code>
   * @return The bytes for partnerPlatformName.
   */
  com.google.protobuf.ByteString
      getPartnerPlatformNameBytes();

  /**
   * <code>string wxName = 26;</code>
   * @return The wxName.
   */
  java.lang.String getWxName();
  /**
   * <code>string wxName = 26;</code>
   * @return The bytes for wxName.
   */
  com.google.protobuf.ByteString
      getWxNameBytes();

  /**
   * <code>bool isHighPriority = 27;</code>
   * @return The isHighPriority.
   */
  boolean getIsHighPriority();

  /**
   * <pre>
   *外卖订单类型
   * </pre>
   *
   * <code>string takeoutType = 28;</code>
   * @return The takeoutType.
   */
  java.lang.String getTakeoutType();
  /**
   * <pre>
   *外卖订单类型
   * </pre>
   *
   * <code>string takeoutType = 28;</code>
   * @return The bytes for takeoutType.
   */
  com.google.protobuf.ByteString
      getTakeoutTypeBytes();

  /**
   * <pre>
   *部分退款时的外卖原单号
   * </pre>
   *
   * <code>string originalOrderNo = 29;</code>
   * @return The originalOrderNo.
   */
  java.lang.String getOriginalOrderNo();
  /**
   * <pre>
   *部分退款时的外卖原单号
   * </pre>
   *
   * <code>string originalOrderNo = 29;</code>
   * @return The bytes for originalOrderNo.
   */
  com.google.protobuf.ByteString
      getOriginalOrderNoBytes();

  /**
   * <pre>
   *商家替用户承担配送费
   * </pre>
   *
   * <code>float merchant_send_fee = 30;</code>
   * @return The merchantSendFee.
   */
  float getMerchantSendFee();

  /**
   * <pre>
   *是否自配送
   * </pre>
   *
   * <code>bool selfDelivery = 31;</code>
   * @return The selfDelivery.
   */
  boolean getSelfDelivery();

  /**
   * <pre>
   *配送员电话
   * </pre>
   *
   * <code>string delivery_phone = 32;</code>
   * @return The deliveryPhone.
   */
  java.lang.String getDeliveryPhone();
  /**
   * <pre>
   *配送员电话
   * </pre>
   *
   * <code>string delivery_phone = 32;</code>
   * @return The bytes for deliveryPhone.
   */
  com.google.protobuf.ByteString
      getDeliveryPhoneBytes();

  /**
   * <pre>
   *配送平台
   * </pre>
   *
   * <code>string delivery_platform = 33;</code>
   * @return The deliveryPlatform.
   */
  java.lang.String getDeliveryPlatform();
  /**
   * <pre>
   *配送平台
   * </pre>
   *
   * <code>string delivery_platform = 33;</code>
   * @return The bytes for deliveryPlatform.
   */
  com.google.protobuf.ByteString
      getDeliveryPlatformBytes();

  /**
   * <pre>
   *发票类型
   * </pre>
   *
   * <code>string invoice_type = 34;</code>
   * @return The invoiceType.
   */
  java.lang.String getInvoiceType();
  /**
   * <pre>
   *发票类型
   * </pre>
   *
   * <code>string invoice_type = 34;</code>
   * @return The bytes for invoiceType.
   */
  com.google.protobuf.ByteString
      getInvoiceTypeBytes();

  /**
   * <pre>
   *纳税人识别号
   * </pre>
   *
   * <code>string invoice_tax_payer_id = 35;</code>
   * @return The invoiceTaxPayerId.
   */
  java.lang.String getInvoiceTaxPayerId();
  /**
   * <pre>
   *纳税人识别号
   * </pre>
   *
   * <code>string invoice_tax_payer_id = 35;</code>
   * @return The bytes for invoiceTaxPayerId.
   */
  com.google.protobuf.ByteString
      getInvoiceTaxPayerIdBytes();

  /**
   * <pre>
   *用户取发票邮箱
   * </pre>
   *
   * <code>string invoice_email = 36;</code>
   * @return The invoiceEmail.
   */
  java.lang.String getInvoiceEmail();
  /**
   * <pre>
   *用户取发票邮箱
   * </pre>
   *
   * <code>string invoice_email = 36;</code>
   * @return The bytes for invoiceEmail.
   */
  com.google.protobuf.ByteString
      getInvoiceEmailBytes();

  /**
   * <pre>
   *平台承担配送费
   * </pre>
   *
   * <code>float platform_send_fee = 37;</code>
   * @return The platformSendFee.
   */
  float getPlatformSendFee();

  /**
   * <code>float send_fee_for_platform = 38;</code>
   * @return The sendFeeForPlatform.
   */
  float getSendFeeForPlatform();

  /**
   * <code>float send_fee_for_merchant = 39;</code>
   * @return The sendFeeForMerchant.
   */
  float getSendFeeForMerchant();

  /**
   * <pre>
   * 开票供应商
   * </pre>
   *
   * <code>string invoice_provider = 40;</code>
   * @return The invoiceProvider.
   */
  java.lang.String getInvoiceProvider();
  /**
   * <pre>
   * 开票供应商
   * </pre>
   *
   * <code>string invoice_provider = 40;</code>
   * @return The bytes for invoiceProvider.
   */
  com.google.protobuf.ByteString
      getInvoiceProviderBytes();

  /**
   * <pre>
   * 开票金额
   * </pre>
   *
   * <code>string invoice_amount = 41;</code>
   * @return The invoiceAmount.
   */
  java.lang.String getInvoiceAmount();
  /**
   * <pre>
   * 开票金额
   * </pre>
   *
   * <code>string invoice_amount = 41;</code>
   * @return The bytes for invoiceAmount.
   */
  com.google.protobuf.ByteString
      getInvoiceAmountBytes();

  /**
   * <pre>
   * 开票链接
   * </pre>
   *
   * <code>string invoice_url = 42;</code>
   * @return The invoiceUrl.
   */
  java.lang.String getInvoiceUrl();
  /**
   * <pre>
   * 开票链接
   * </pre>
   *
   * <code>string invoice_url = 42;</code>
   * @return The bytes for invoiceUrl.
   */
  com.google.protobuf.ByteString
      getInvoiceUrlBytes();
}
