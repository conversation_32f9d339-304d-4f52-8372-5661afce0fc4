// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

public interface TaxOrBuilder extends
    // @@protoc_insertion_point(interface_extends:eticket_proto.Tax)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   *税金额
   * </pre>
   *
   * <code>double amount = 1;</code>
   * @return The amount.
   */
  double getAmount();

  /**
   * <pre>
   *纳税商品总额
   * </pre>
   *
   * <code>double subTotal = 2;</code>
   * @return The subTotal.
   */
  double getSubTotal();

  /**
   * <pre>
   *税种编码
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <pre>
   *税种编码
   * </pre>
   *
   * <code>string code = 3;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <pre>
   *税种名称
   * </pre>
   *
   * <code>string name = 4;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   *税种名称
   * </pre>
   *
   * <code>string name = 4;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   *税率
   * </pre>
   *
   * <code>double rate = 5;</code>
   * @return The rate.
   */
  double getRate();
}
