// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ticket.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

public final class TicketOuterClass {
  private TicketOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Ticket_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Ticket_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Efficiency_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Efficiency_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Store_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Store_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Pos_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Pos_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Amount_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Amount_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Operator_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Operator_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Table_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Table_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Channel_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Channel_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_RefundInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_RefundInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Product_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Product_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_SkuRemark_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_SkuRemark_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_SkuRemark_skuName_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_SkuRemark_skuName_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_SkuRemark_skuValue_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_SkuRemark_skuValue_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Tax_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Tax_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Payment_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Payment_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Promotion_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Promotion_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_PromotionInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_PromotionInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_PromotionSource_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_PromotionSource_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_PromotionProduct_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_PromotionProduct_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Member_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Member_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Coupon_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Coupon_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Fee_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Fee_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_eticket_proto_Takeaway_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_eticket_proto_Takeaway_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014ticket.proto\022\reticket_proto\"\345\010\n\006Ticket" +
      "\022\021\n\tticket_id\030\001 \001(\t\022\021\n\tticket_no\030\002 \001(\t\022\022" +
      "\n\nstart_time\030\003 \001(\t\022\020\n\010end_time\030\004 \001(\t\022\020\n\010" +
      "bus_date\030\005 \001(\t\022\037\n\003pos\030\006 \001(\0132\022.eticket_pr" +
      "oto.Pos\022)\n\010operator\030\007 \001(\0132\027.eticket_prot" +
      "o.Operator\022&\n\007amounts\030\010 \001(\0132\025.eticket_pr" +
      "oto.Amount\022\026\n\016takemealNumber\030\t \001(\t\022\013\n\003qt" +
      "y\030\n \001(\005\022\016\n\006status\030\013 \001(\t\022-\n\nrefundInfo\030\014 " +
      "\001(\0132\031.eticket_proto.RefundInfo\022\'\n\007channe" +
      "l\030\r \001(\0132\026.eticket_proto.Channel\022(\n\010produ" +
      "cts\030\016 \003(\0132\026.eticket_proto.Product\022(\n\010pay" +
      "ments\030\017 \003(\0132\026.eticket_proto.Payment\022,\n\np" +
      "romotions\030\020 \003(\0132\030.eticket_proto.Promotio" +
      "n\022&\n\007members\030\021 \003(\0132\025.eticket_proto.Membe" +
      "r\022#\n\005table\030\022 \001(\0132\024.eticket_proto.Table\022\016" +
      "\n\006people\030\023 \001(\005\022\017\n\007room_no\030\024 \001(\t\022\016\n\006remar" +
      "k\030\025 \001(\t\022\020\n\010house_ac\030\026 \001(\010\022\027\n\017order_time_" +
      "type\030\027 \001(\t\022\023\n\013shiftNumber\030\030 \001(\t\022#\n\007taxLi" +
      "st\030\031 \003(\0132\022.eticket_proto.Tax\022#\n\005store\030\032 " +
      "\001(\0132\024.eticket_proto.Store\022.\n\rtakeaway_in" +
      "fo\030\" \001(\0132\027.eticket_proto.Takeaway\022\021\n\ttic" +
      "ketUno\030# \001(\t\022&\n\007coupons\030$ \003(\0132\025.eticket_" +
      "proto.Coupon\022 \n\004fees\030% \003(\0132\022.eticket_pro" +
      "to.Fee\022\020\n\010timeZone\030& \001(\t\022\023\n\013upload_time\030" +
      "\' \001(\t\022\035\n\025discount_proportioned\030( \001(\010\022\026\n\016" +
      "transaction_no\030) \001(\t\022+\n\017fees_no_account\030" +
      "* \003(\0132\022.eticket_proto.Fee\022-\n\nefficiency\030" +
      "+ \001(\0132\031.eticket_proto.Efficiency\022\033\n\023pend" +
      "ing_sync_member\030, \001(\010\022\016\n\006weight\030- \001(\002\"\347\001" +
      "\n\nEfficiency\022\026\n\016confirmed_time\030\001 \001(\t\022\021\n\t" +
      "made_time\030\002 \001(\t\022\025\n\rassigned_time\030\003 \001(\t\022\024" +
      "\n\014arrived_time\030\004 \001(\t\022\024\n\014fetched_time\030\005 \001" +
      "(\t\022\026\n\016delivered_time\030\006 \001(\t\022\021\n\tmake_span\030" +
      "\007 \001(\002\022\025\n\ravg_make_span\030\010 \001(\002\022\023\n\013arrive_s" +
      "pan\030\t \001(\002\022\024\n\014deliver_span\030\n \001(\002\"~\n\005Store" +
      "\022\n\n\002id\030\001 \001(\t\022\014\n\004code\030\002 \001(\t\022\022\n\nsecondCode" +
      "\030\003 \001(\t\022\021\n\tcompanyId\030\004 \001(\t\022\021\n\tpartnerId\030\005" +
      " \001(\t\022\017\n\007scopeId\030\006 \001(\t\022\020\n\010branchId\030\007 \001(\t\"" +
      "Y\n\003Pos\022\n\n\002id\030\001 \001(\t\022\014\n\004code\030\002 \001(\t\022\020\n\010pos_" +
      "name\030\003 \001(\t\022\021\n\tdevice_id\030\004 \001(\t\022\023\n\013device_" +
      "code\030\005 \001(\t\"\211\010\n\006Amount\022\021\n\ttaxAmount\030\001 \001(\001" +
      "\022\024\n\014gross_amount\030\002 \001(\001\022\022\n\nnet_amount\030\003 \001" +
      "(\001\022\022\n\npay_amount\030\004 \001(\001\022\027\n\017discount_amoun" +
      "t\030\005 \001(\001\022\031\n\021removezero_amount\030\006 \001(\001\022\020\n\010ro" +
      "unding\030\007 \001(\001\022\027\n\017overflow_amount\030\010 \001(\001\022\024\n" +
      "\014changeAmount\030\t \001(\001\022\022\n\nserviceFee\030\n \001(\001\022" +
      "\013\n\003tip\030\013 \001(\001\022\022\n\ncommission\030\014 \001(\001\022\020\n\010amou" +
      "nt_0\030\r \001(\001\022\020\n\010amount_1\030\016 \001(\001\022\020\n\010amount_2" +
      "\030\017 \001(\001\022\020\n\010amount_3\030\020 \001(\001\022\020\n\010amount_4\030\021 \001" +
      "(\001\022\023\n\013taxIncluded\030\022 \001(\010\022\020\n\010otherFee\030\023 \001(" +
      "\002\022 \n\030merchant_discount_amount\030\024 \001(\002\022 \n\030p" +
      "latform_discount_amount\030\025 \001(\002\022\030\n\020project" +
      "ed_income\030\026 \001(\002\022\022\n\nreceivable\030\027 \001(\001\022\023\n\013r" +
      "eal_amount\030\030 \001(\001\022\027\n\017business_amount\030\031 \001(" +
      "\001\022\025\n\rexpend_amount\030\032 \001(\001\022\037\n\027payment_tran" +
      "sfer_amount\030\033 \001(\001\022 \n\030discount_transfer_a" +
      "mount\030\034 \001(\001\022\035\n\025store_discount_amount\030\035 \001" +
      "(\001\022$\n\034discount_merchant_contribute\030\036 \001(\001" +
      "\022$\n\034discount_platform_contribute\030\037 \001(\001\022!" +
      "\n\031discount_buyer_contribute\030  \001(\001\022!\n\031dis" +
      "count_other_contribute\030! \001(\001\022\037\n\027pay_merc" +
      "hant_contribute\030\" \001(\001\022\037\n\027pay_platform_co" +
      "ntribute\030# \001(\001\022\034\n\024pay_buyer_contribute\030$" +
      " \001(\001\022\034\n\024pay_other_contribute\030% \001(\001\022\024\n\014de" +
      "livery_fee\030& \001(\001\022!\n\031delivery_fee_for_pla" +
      "tform\030\' \001(\001\022!\n\031delivery_fee_for_merchant" +
      "\030( \001(\001\"W\n\010Operator\022\n\n\002id\030\001 \001(\t\022\014\n\004name\030\002" +
      " \001(\t\022\014\n\004code\030\003 \001(\t\022\022\n\nlogin_time\030\004 \001(\t\022\017" +
      "\n\007loginId\030\005 \001(\t\"c\n\005Table\022\n\n\002id\030\001 \001(\t\022\017\n\007" +
      "zone_id\030\002 \001(\t\022\n\n\002no\030\003 \001(\t\022\016\n\006zoneNo\030\004 \001(" +
      "\t\022\016\n\006people\030\005 \001(\005\022\021\n\ttemporary\030\006 \001(\010\"\226\001\n" +
      "\007Channel\022\016\n\006source\030\001 \001(\t\022\022\n\ndeviceType\030\002" +
      " \001(\t\022\021\n\torderType\030\003 \001(\t\022\024\n\014deliveryType\030" +
      "\004 \001(\t\022\016\n\006tpName\030\005 \001(\t\022\014\n\004code\030\006 \001(\t\022\n\n\002i" +
      "d\030\007 \001(\t\022\024\n\014mapping_code\030\010 \001(\t\"\241\001\n\nRefund" +
      "Info\022\021\n\trefund_id\030\001 \001(\t\022\021\n\trefund_no\030\002 \001" +
      "(\t\022\025\n\rref_ticket_id\030\003 \001(\t\022\025\n\rref_ticket_" +
      "no\030\004 \001(\t\022\025\n\rrefund_reason\030\005 \001(\t\022\023\n\013refun" +
      "d_code\030\006 \001(\t\022\023\n\013refund_side\030\007 \001(\t\"\210\004\n\007Pr" +
      "oduct\022\n\n\002id\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\014\n\004code\030" +
      "\003 \001(\t\022\016\n\006seq_id\030\004 \001(\003\022\r\n\005price\030\005 \001(\001\022\016\n\006" +
      "amount\030\006 \001(\001\022\013\n\003qty\030\007 \001(\005\022\027\n\017discount_am" +
      "ount\030\010 \001(\001\022\014\n\004type\030\t \001(\t\022+\n\013accessories\030" +
      "\n \003(\0132\026.eticket_proto.Product\022+\n\013combo_i" +
      "tems\030\013 \003(\0132\026.eticket_proto.Product\022\031\n\021op" +
      "eration_records\030\014 \001(\t\022+\n\tskuRemark\030\r \003(\013" +
      "2\030.eticket_proto.SkuRemark\022\016\n\006remark\030\016 \001" +
      "(\t\022\021\n\ttaxAmount\030\017 \001(\001\022\022\n\nsum_amount\030\020 \001(" +
      "\002\022\033\n\023sum_discount_amount\030\021 \001(\002\022\026\n\016sum_ne" +
      "t_amount\030\022 \001(\002\022\025\n\rhas_make_span\030\023 \001(\010\022\025\n" +
      "\ravg_make_span\030\024 \001(\002\022\022\n\nnet_amount\030\025 \001(\001" +
      "\022\016\n\006weight\030\026 \001(\002\022\022\n\nhas_weight\030\027 \001(\010\"\311\001\n" +
      "\tSkuRemark\022.\n\004name\030\001 \001(\0132 .eticket_proto" +
      ".SkuRemark.skuName\0221\n\006values\030\002 \001(\0132!.eti" +
      "cket_proto.SkuRemark.skuValue\0321\n\007skuName" +
      "\022\n\n\002id\030\001 \001(\t\022\014\n\004code\030\002 \001(\t\022\014\n\004name\030\003 \001(\t" +
      "\032&\n\010skuValue\022\014\n\004code\030\001 \001(\t\022\014\n\004name\030\002 \001(\t" +
      "\"Q\n\003Tax\022\016\n\006amount\030\001 \001(\001\022\020\n\010subTotal\030\002 \001(" +
      "\001\022\014\n\004code\030\003 \001(\t\022\014\n\004name\030\004 \001(\t\022\014\n\004rate\030\005 " +
      "\001(\001\"\271\003\n\007Payment\022\n\n\002id\030\001 \001(\t\022\016\n\006seq_id\030\002 " +
      "\001(\t\022\022\n\npay_amount\030\003 \001(\001\022\025\n\rrealPayAmount" +
      "\030\004 \001(\001\022\016\n\006change\030\005 \001(\001\022\020\n\010overflow\030\006 \001(\001" +
      "\022\020\n\010rounding\030\007 \001(\001\022\020\n\010pay_time\030\010 \001(\t\022\022\n\n" +
      "trans_code\030\t \001(\t\022\014\n\004name\030\n \001(\t\022\022\n\nreceiv" +
      "able\030\013 \001(\001\022\027\n\017tpTransactionNo\030\014 \001(\t\022\024\n\014t" +
      "p_allowance\030\r \001(\002\022\032\n\022merchant_allowance\030" +
      "\016 \001(\002\022\022\n\ntrans_name\030\017 \001(\t\022\r\n\005price\030\020 \001(\002" +
      "\022\014\n\004cost\030\021 \001(\002\022\023\n\013real_amount\030\022 \001(\002\022\024\n\014h" +
      "as_invoiced\030\023 \001(\010\022\027\n\017transfer_amount\030\024 \001" +
      "(\001\022\032\n\022platform_allowance\030\025 \001(\001\022\017\n\007payerN" +
      "o\030\026 \001(\t\"\243\001\n\tPromotion\0223\n\rpromotionInfo\030\001" +
      " \001(\0132\034.eticket_proto.PromotionInfo\022.\n\006so" +
      "urce\030\002 \001(\0132\036.eticket_proto.PromotionSour" +
      "ce\0221\n\010products\030\003 \003(\0132\037.eticket_proto.Pro" +
      "motionProduct\"\353\001\n\rPromotionInfo\022\014\n\004type\030" +
      "\001 \001(\t\022\025\n\rdiscount_type\030\002 \001(\t\022\014\n\004name\030\003 \001" +
      "(\t\022\024\n\014promotion_id\030\004 \001(\t\022\026\n\016promotion_co" +
      "de\030\005 \001(\t\022\026\n\016promotion_type\030\006 \001(\t\022\025\n\rallo" +
      "w_overlap\030\007 \001(\010\022\034\n\024trigger_times_custom\030" +
      "\010 \001(\010\022\026\n\016ticket_display\030\t \001(\t\022\024\n\014max_dis" +
      "count\030\n \001(\001\"\233\002\n\017PromotionSource\022\017\n\007trigg" +
      "er\030\001 \001(\005\022\020\n\010discount\030\002 \001(\001\022\r\n\005fired\030\003 \003(" +
      "\t\022\031\n\021merchant_discount\030\004 \001(\001\022\031\n\021platform" +
      "_discount\030\005 \001(\001\022\026\n\016store_discount\030\006 \001(\001\022" +
      "\014\n\004cost\030\007 \001(\001\022\024\n\014tp_allowance\030\010 \001(\001\022\032\n\022m" +
      "erchant_allowance\030\t \001(\001\022\032\n\022platform_allo" +
      "wance\030\n \001(\001\022\023\n\013real_amount\030\013 \001(\001\022\027\n\017tran" +
      "sfer_amount\030\014 \001(\001\"\207\002\n\020PromotionProduct\022\r" +
      "\n\005price\030\001 \001(\001\022\013\n\003amt\030\002 \001(\001\022\016\n\006accAmt\030\003 \001" +
      "(\001\022\013\n\003qty\030\004 \001(\005\022\016\n\006key_id\030\005 \001(\t\022\016\n\006accie" +
      "s\030\006 \003(\t\022\014\n\004type\030\007 \001(\t\022\020\n\010discount\030\010 \001(\001\022" +
      "\020\n\010free_amt\030\t \001(\001\022\016\n\006method\030\n \001(\t\0224\n\013acc" +
      "essories\030\013 \003(\0132\037.eticket_proto.Promotion" +
      "Product\022\016\n\006weight\030\014 \001(\002\022\022\n\nhas_weight\030\r " +
      "\001(\010\"\272\001\n\006Member\022\023\n\013member_code\030\001 \001(\t\022\016\n\006m" +
      "obile\030\002 \001(\t\022\014\n\004name\030\003 \001(\t\022\021\n\tgreetings\030\004" +
      " \001(\t\022\026\n\016balance_points\030\005 \001(\003\022\024\n\014total_po" +
      "ints\030\006 \001(\003\022\024\n\014order_points\030\007 \001(\003\022\022\n\ngrad" +
      "e_code\030\010 \001(\t\022\022\n\ngrade_name\030\t \001(\t\"\223\002\n\006Cou" +
      "pon\022\021\n\tis_online\030\001 \001(\010\022\n\n\002id\030\002 \001(\t\022\014\n\004na" +
      "me\030\003 \001(\t\022\014\n\004code\030\004 \001(\t\022\014\n\004type\030\005 \001(\003\022\021\n\t" +
      "par_value\030\006 \001(\001\022\023\n\013sequence_id\030\007 \001(\t\022\r\n\005" +
      "price\030\010 \001(\002\022\014\n\004cost\030\t \001(\002\022\024\n\014tp_allowanc" +
      "e\030\n \001(\002\022\032\n\022merchant_allowance\030\013 \001(\002\022\024\n\014h" +
      "as_invoiced\030\014 \001(\010\022\027\n\017transfer_amount\030\r \001" +
      "(\001\022\032\n\022platform_allowance\030\016 \001(\001\"\215\001\n\003Fee\022\014" +
      "\n\004name\030\001 \001(\t\022\r\n\005price\030\002 \001(\001\022\013\n\003qty\030\003 \001(\005" +
      "\022\016\n\006amount\030\004 \001(\001\022\014\n\004type\030\005 \001(\t\022\025\n\rdiscou" +
      "nt_rate\030\006 \001(\002\022\'\n\013detail_fees\030\007 \003(\0132\022.eti" +
      "cket_proto.Fee\"\263\007\n\010Takeaway\022\024\n\014order_met" +
      "hod\030\001 \001(\t\022\017\n\007is_paid\030\003 \001(\010\022\023\n\013tp_order_i" +
      "d\030\004 \001(\t\022\022\n\norder_time\030\005 \001(\t\022\024\n\014deliver_t" +
      "ime\030\006 \001(\t\022\023\n\013description\030\007 \001(\t\022\021\n\tconsig" +
      "nee\030\010 \001(\t\022\034\n\024delivery_poi_address\030\t \001(\t\022" +
      "\022\n\nphone_list\030\n \003(\t\022\n\n\002tp\030\013 \001(\t\022\016\n\006sourc" +
      "e\030\014 \001(\t\022\027\n\017source_order_id\030\r \001(\t\022\017\n\007day_" +
      "seq\030\016 \001(\t\022\025\n\rdelivery_type\030\017 \001(\005\022\025\n\rdeli" +
      "very_name\030\020 \001(\t\022\025\n\rinvoice_title\030\021 \001(\t\022\024" +
      "\n\014waiting_time\030\022 \001(\t\022\025\n\rtableware_num\030\023 " +
      "\001(\005\022\020\n\010send_fee\030\024 \001(\001\022\023\n\013package_fee\030\025 \001" +
      "(\001\022\025\n\rdelivery_time\030\026 \001(\t\022\024\n\014take_meal_s" +
      "n\030\027 \001(\t\022\031\n\021partnerPlatformId\030\030 \001(\005\022\033\n\023pa" +
      "rtnerPlatformName\030\031 \001(\t\022\016\n\006wxName\030\032 \001(\t\022" +
      "\026\n\016isHighPriority\030\033 \001(\010\022\023\n\013takeoutType\030\034" +
      " \001(\t\022\027\n\017originalOrderNo\030\035 \001(\t\022\031\n\021merchan" +
      "t_send_fee\030\036 \001(\002\022\024\n\014selfDelivery\030\037 \001(\010\022\026" +
      "\n\016delivery_phone\030  \001(\t\022\031\n\021delivery_platf" +
      "orm\030! \001(\t\022\024\n\014invoice_type\030\" \001(\t\022\034\n\024invoi" +
      "ce_tax_payer_id\030# \001(\t\022\025\n\rinvoice_email\030$" +
      " \001(\t\022\031\n\021platform_send_fee\030% \001(\002\022\035\n\025send_" +
      "fee_for_platform\030& \001(\002\022\035\n\025send_fee_for_m" +
      "erchant\030\' \001(\002\022\030\n\020invoice_provider\030( \001(\t\022" +
      "\026\n\016invoice_amount\030) \001(\t\022\023\n\013invoice_url\030*" +
      " \001(\tB7\n3cn.hexcloud.pbis.common.service." +
      "integration.eticketP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_eticket_proto_Ticket_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_eticket_proto_Ticket_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Ticket_descriptor,
        new java.lang.String[] { "TicketId", "TicketNo", "StartTime", "EndTime", "BusDate", "Pos", "Operator", "Amounts", "TakemealNumber", "Qty", "Status", "RefundInfo", "Channel", "Products", "Payments", "Promotions", "Members", "Table", "People", "RoomNo", "Remark", "HouseAc", "OrderTimeType", "ShiftNumber", "TaxList", "Store", "TakeawayInfo", "TicketUno", "Coupons", "Fees", "TimeZone", "UploadTime", "DiscountProportioned", "TransactionNo", "FeesNoAccount", "Efficiency", "PendingSyncMember", "Weight", });
    internal_static_eticket_proto_Efficiency_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_eticket_proto_Efficiency_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Efficiency_descriptor,
        new java.lang.String[] { "ConfirmedTime", "MadeTime", "AssignedTime", "ArrivedTime", "FetchedTime", "DeliveredTime", "MakeSpan", "AvgMakeSpan", "ArriveSpan", "DeliverSpan", });
    internal_static_eticket_proto_Store_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_eticket_proto_Store_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Store_descriptor,
        new java.lang.String[] { "Id", "Code", "SecondCode", "CompanyId", "PartnerId", "ScopeId", "BranchId", });
    internal_static_eticket_proto_Pos_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_eticket_proto_Pos_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Pos_descriptor,
        new java.lang.String[] { "Id", "Code", "PosName", "DeviceId", "DeviceCode", });
    internal_static_eticket_proto_Amount_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_eticket_proto_Amount_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Amount_descriptor,
        new java.lang.String[] { "TaxAmount", "GrossAmount", "NetAmount", "PayAmount", "DiscountAmount", "RemovezeroAmount", "Rounding", "OverflowAmount", "ChangeAmount", "ServiceFee", "Tip", "Commission", "Amount0", "Amount1", "Amount2", "Amount3", "Amount4", "TaxIncluded", "OtherFee", "MerchantDiscountAmount", "PlatformDiscountAmount", "ProjectedIncome", "Receivable", "RealAmount", "BusinessAmount", "ExpendAmount", "PaymentTransferAmount", "DiscountTransferAmount", "StoreDiscountAmount", "DiscountMerchantContribute", "DiscountPlatformContribute", "DiscountBuyerContribute", "DiscountOtherContribute", "PayMerchantContribute", "PayPlatformContribute", "PayBuyerContribute", "PayOtherContribute", "DeliveryFee", "DeliveryFeeForPlatform", "DeliveryFeeForMerchant", });
    internal_static_eticket_proto_Operator_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_eticket_proto_Operator_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Operator_descriptor,
        new java.lang.String[] { "Id", "Name", "Code", "LoginTime", "LoginId", });
    internal_static_eticket_proto_Table_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_eticket_proto_Table_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Table_descriptor,
        new java.lang.String[] { "Id", "ZoneId", "No", "ZoneNo", "People", "Temporary", });
    internal_static_eticket_proto_Channel_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_eticket_proto_Channel_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Channel_descriptor,
        new java.lang.String[] { "Source", "DeviceType", "OrderType", "DeliveryType", "TpName", "Code", "Id", "MappingCode", });
    internal_static_eticket_proto_RefundInfo_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_eticket_proto_RefundInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_RefundInfo_descriptor,
        new java.lang.String[] { "RefundId", "RefundNo", "RefTicketId", "RefTicketNo", "RefundReason", "RefundCode", "RefundSide", });
    internal_static_eticket_proto_Product_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_eticket_proto_Product_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Product_descriptor,
        new java.lang.String[] { "Id", "Name", "Code", "SeqId", "Price", "Amount", "Qty", "DiscountAmount", "Type", "Accessories", "ComboItems", "OperationRecords", "SkuRemark", "Remark", "TaxAmount", "SumAmount", "SumDiscountAmount", "SumNetAmount", "HasMakeSpan", "AvgMakeSpan", "NetAmount", "Weight", "HasWeight", });
    internal_static_eticket_proto_SkuRemark_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_eticket_proto_SkuRemark_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_SkuRemark_descriptor,
        new java.lang.String[] { "Name", "Values", });
    internal_static_eticket_proto_SkuRemark_skuName_descriptor =
      internal_static_eticket_proto_SkuRemark_descriptor.getNestedTypes().get(0);
    internal_static_eticket_proto_SkuRemark_skuName_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_SkuRemark_skuName_descriptor,
        new java.lang.String[] { "Id", "Code", "Name", });
    internal_static_eticket_proto_SkuRemark_skuValue_descriptor =
      internal_static_eticket_proto_SkuRemark_descriptor.getNestedTypes().get(1);
    internal_static_eticket_proto_SkuRemark_skuValue_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_SkuRemark_skuValue_descriptor,
        new java.lang.String[] { "Code", "Name", });
    internal_static_eticket_proto_Tax_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_eticket_proto_Tax_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Tax_descriptor,
        new java.lang.String[] { "Amount", "SubTotal", "Code", "Name", "Rate", });
    internal_static_eticket_proto_Payment_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_eticket_proto_Payment_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Payment_descriptor,
        new java.lang.String[] { "Id", "SeqId", "PayAmount", "RealPayAmount", "Change", "Overflow", "Rounding", "PayTime", "TransCode", "Name", "Receivable", "TpTransactionNo", "TpAllowance", "MerchantAllowance", "TransName", "Price", "Cost", "RealAmount", "HasInvoiced", "TransferAmount", "PlatformAllowance", "PayerNo", });
    internal_static_eticket_proto_Promotion_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_eticket_proto_Promotion_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Promotion_descriptor,
        new java.lang.String[] { "PromotionInfo", "Source", "Products", });
    internal_static_eticket_proto_PromotionInfo_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_eticket_proto_PromotionInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_PromotionInfo_descriptor,
        new java.lang.String[] { "Type", "DiscountType", "Name", "PromotionId", "PromotionCode", "PromotionType", "AllowOverlap", "TriggerTimesCustom", "TicketDisplay", "MaxDiscount", });
    internal_static_eticket_proto_PromotionSource_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_eticket_proto_PromotionSource_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_PromotionSource_descriptor,
        new java.lang.String[] { "Trigger", "Discount", "Fired", "MerchantDiscount", "PlatformDiscount", "StoreDiscount", "Cost", "TpAllowance", "MerchantAllowance", "PlatformAllowance", "RealAmount", "TransferAmount", });
    internal_static_eticket_proto_PromotionProduct_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_eticket_proto_PromotionProduct_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_PromotionProduct_descriptor,
        new java.lang.String[] { "Price", "Amt", "AccAmt", "Qty", "KeyId", "Accies", "Type", "Discount", "FreeAmt", "Method", "Accessories", "Weight", "HasWeight", });
    internal_static_eticket_proto_Member_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_eticket_proto_Member_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Member_descriptor,
        new java.lang.String[] { "MemberCode", "Mobile", "Name", "Greetings", "BalancePoints", "TotalPoints", "OrderPoints", "GradeCode", "GradeName", });
    internal_static_eticket_proto_Coupon_descriptor =
      getDescriptor().getMessageTypes().get(18);
    internal_static_eticket_proto_Coupon_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Coupon_descriptor,
        new java.lang.String[] { "IsOnline", "Id", "Name", "Code", "Type", "ParValue", "SequenceId", "Price", "Cost", "TpAllowance", "MerchantAllowance", "HasInvoiced", "TransferAmount", "PlatformAllowance", });
    internal_static_eticket_proto_Fee_descriptor =
      getDescriptor().getMessageTypes().get(19);
    internal_static_eticket_proto_Fee_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Fee_descriptor,
        new java.lang.String[] { "Name", "Price", "Qty", "Amount", "Type", "DiscountRate", "DetailFees", });
    internal_static_eticket_proto_Takeaway_descriptor =
      getDescriptor().getMessageTypes().get(20);
    internal_static_eticket_proto_Takeaway_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_eticket_proto_Takeaway_descriptor,
        new java.lang.String[] { "OrderMethod", "IsPaid", "TpOrderId", "OrderTime", "DeliverTime", "Description", "Consignee", "DeliveryPoiAddress", "PhoneList", "Tp", "Source", "SourceOrderId", "DaySeq", "DeliveryType", "DeliveryName", "InvoiceTitle", "WaitingTime", "TablewareNum", "SendFee", "PackageFee", "DeliveryTime", "TakeMealSn", "PartnerPlatformId", "PartnerPlatformName", "WxName", "IsHighPriority", "TakeoutType", "OriginalOrderNo", "MerchantSendFee", "SelfDelivery", "DeliveryPhone", "DeliveryPlatform", "InvoiceType", "InvoiceTaxPayerId", "InvoiceEmail", "PlatformSendFee", "SendFeeForPlatform", "SendFeeForMerchant", "InvoiceProvider", "InvoiceAmount", "InvoiceUrl", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
