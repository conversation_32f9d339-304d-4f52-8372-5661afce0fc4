// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: service.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

/**
 * Protobuf type {@code eticket_proto.UploadTicketRequest}
 */
public final class UploadTicketRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:eticket_proto.UploadTicketRequest)
    UploadTicketRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use UploadTicketRequest.newBuilder() to construct.
  private UploadTicketRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private UploadTicketRequest() {
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new UploadTicketRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private UploadTicketRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            cn.hexcloud.pbis.common.service.integration.eticket.Ticket.Builder subBuilder = null;
            if (ticket_ != null) {
              subBuilder = ticket_.toBuilder();
            }
            ticket_ = input.readMessage(cn.hexcloud.pbis.common.service.integration.eticket.Ticket.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(ticket_);
              ticket_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.eticket.Service.internal_static_eticket_proto_UploadTicketRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.eticket.Service.internal_static_eticket_proto_UploadTicketRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest.class, cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest.Builder.class);
  }

  public static final int TICKET_FIELD_NUMBER = 1;
  private cn.hexcloud.pbis.common.service.integration.eticket.Ticket ticket_;
  /**
   * <pre>
   * （必传）完整的ticket信息
   * </pre>
   *
   * <code>.eticket_proto.Ticket ticket = 1;</code>
   * @return Whether the ticket field is set.
   */
  @java.lang.Override
  public boolean hasTicket() {
    return ticket_ != null;
  }
  /**
   * <pre>
   * （必传）完整的ticket信息
   * </pre>
   *
   * <code>.eticket_proto.Ticket ticket = 1;</code>
   * @return The ticket.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.Ticket getTicket() {
    return ticket_ == null ? cn.hexcloud.pbis.common.service.integration.eticket.Ticket.getDefaultInstance() : ticket_;
  }
  /**
   * <pre>
   * （必传）完整的ticket信息
   * </pre>
   *
   * <code>.eticket_proto.Ticket ticket = 1;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.TicketOrBuilder getTicketOrBuilder() {
    return getTicket();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (ticket_ != null) {
      output.writeMessage(1, getTicket());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (ticket_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getTicket());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest other = (cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest) obj;

    if (hasTicket() != other.hasTicket()) return false;
    if (hasTicket()) {
      if (!getTicket()
          .equals(other.getTicket())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasTicket()) {
      hash = (37 * hash) + TICKET_FIELD_NUMBER;
      hash = (53 * hash) + getTicket().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code eticket_proto.UploadTicketRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:eticket_proto.UploadTicketRequest)
      cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.eticket.Service.internal_static_eticket_proto_UploadTicketRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.eticket.Service.internal_static_eticket_proto_UploadTicketRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest.class, cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (ticketBuilder_ == null) {
        ticket_ = null;
      } else {
        ticket_ = null;
        ticketBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.Service.internal_static_eticket_proto_UploadTicketRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest build() {
      cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest buildPartial() {
      cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest result = new cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest(this);
      if (ticketBuilder_ == null) {
        result.ticket_ = ticket_;
      } else {
        result.ticket_ = ticketBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest other) {
      if (other == cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest.getDefaultInstance()) return this;
      if (other.hasTicket()) {
        mergeTicket(other.getTicket());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private cn.hexcloud.pbis.common.service.integration.eticket.Ticket ticket_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.integration.eticket.Ticket, cn.hexcloud.pbis.common.service.integration.eticket.Ticket.Builder, cn.hexcloud.pbis.common.service.integration.eticket.TicketOrBuilder> ticketBuilder_;
    /**
     * <pre>
     * （必传）完整的ticket信息
     * </pre>
     *
     * <code>.eticket_proto.Ticket ticket = 1;</code>
     * @return Whether the ticket field is set.
     */
    public boolean hasTicket() {
      return ticketBuilder_ != null || ticket_ != null;
    }
    /**
     * <pre>
     * （必传）完整的ticket信息
     * </pre>
     *
     * <code>.eticket_proto.Ticket ticket = 1;</code>
     * @return The ticket.
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.Ticket getTicket() {
      if (ticketBuilder_ == null) {
        return ticket_ == null ? cn.hexcloud.pbis.common.service.integration.eticket.Ticket.getDefaultInstance() : ticket_;
      } else {
        return ticketBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * （必传）完整的ticket信息
     * </pre>
     *
     * <code>.eticket_proto.Ticket ticket = 1;</code>
     */
    public Builder setTicket(cn.hexcloud.pbis.common.service.integration.eticket.Ticket value) {
      if (ticketBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ticket_ = value;
        onChanged();
      } else {
        ticketBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * （必传）完整的ticket信息
     * </pre>
     *
     * <code>.eticket_proto.Ticket ticket = 1;</code>
     */
    public Builder setTicket(
        cn.hexcloud.pbis.common.service.integration.eticket.Ticket.Builder builderForValue) {
      if (ticketBuilder_ == null) {
        ticket_ = builderForValue.build();
        onChanged();
      } else {
        ticketBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * （必传）完整的ticket信息
     * </pre>
     *
     * <code>.eticket_proto.Ticket ticket = 1;</code>
     */
    public Builder mergeTicket(cn.hexcloud.pbis.common.service.integration.eticket.Ticket value) {
      if (ticketBuilder_ == null) {
        if (ticket_ != null) {
          ticket_ =
            cn.hexcloud.pbis.common.service.integration.eticket.Ticket.newBuilder(ticket_).mergeFrom(value).buildPartial();
        } else {
          ticket_ = value;
        }
        onChanged();
      } else {
        ticketBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * （必传）完整的ticket信息
     * </pre>
     *
     * <code>.eticket_proto.Ticket ticket = 1;</code>
     */
    public Builder clearTicket() {
      if (ticketBuilder_ == null) {
        ticket_ = null;
        onChanged();
      } else {
        ticket_ = null;
        ticketBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * （必传）完整的ticket信息
     * </pre>
     *
     * <code>.eticket_proto.Ticket ticket = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.Ticket.Builder getTicketBuilder() {
      
      onChanged();
      return getTicketFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * （必传）完整的ticket信息
     * </pre>
     *
     * <code>.eticket_proto.Ticket ticket = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.eticket.TicketOrBuilder getTicketOrBuilder() {
      if (ticketBuilder_ != null) {
        return ticketBuilder_.getMessageOrBuilder();
      } else {
        return ticket_ == null ?
            cn.hexcloud.pbis.common.service.integration.eticket.Ticket.getDefaultInstance() : ticket_;
      }
    }
    /**
     * <pre>
     * （必传）完整的ticket信息
     * </pre>
     *
     * <code>.eticket_proto.Ticket ticket = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.integration.eticket.Ticket, cn.hexcloud.pbis.common.service.integration.eticket.Ticket.Builder, cn.hexcloud.pbis.common.service.integration.eticket.TicketOrBuilder> 
        getTicketFieldBuilder() {
      if (ticketBuilder_ == null) {
        ticketBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.integration.eticket.Ticket, cn.hexcloud.pbis.common.service.integration.eticket.Ticket.Builder, cn.hexcloud.pbis.common.service.integration.eticket.TicketOrBuilder>(
                getTicket(),
                getParentForChildren(),
                isClean());
        ticket_ = null;
      }
      return ticketBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:eticket_proto.UploadTicketRequest)
  }

  // @@protoc_insertion_point(class_scope:eticket_proto.UploadTicketRequest)
  private static final cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest();
  }

  public static cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UploadTicketRequest>
      PARSER = new com.google.protobuf.AbstractParser<UploadTicketRequest>() {
    @java.lang.Override
    public UploadTicketRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new UploadTicketRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<UploadTicketRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UploadTicketRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

