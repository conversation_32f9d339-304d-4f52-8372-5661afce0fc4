// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: service.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

public interface UploadTicketRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:eticket_proto.UploadTicketRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）完整的ticket信息
   * </pre>
   *
   * <code>.eticket_proto.Ticket ticket = 1;</code>
   * @return Whether the ticket field is set.
   */
  boolean hasTicket();
  /**
   * <pre>
   * （必传）完整的ticket信息
   * </pre>
   *
   * <code>.eticket_proto.Ticket ticket = 1;</code>
   * @return The ticket.
   */
  cn.hexcloud.pbis.common.service.integration.eticket.Ticket getTicket();
  /**
   * <pre>
   * （必传）完整的ticket信息
   * </pre>
   *
   * <code>.eticket_proto.Ticket ticket = 1;</code>
   */
  cn.hexcloud.pbis.common.service.integration.eticket.TicketOrBuilder getTicketOrBuilder();
}
