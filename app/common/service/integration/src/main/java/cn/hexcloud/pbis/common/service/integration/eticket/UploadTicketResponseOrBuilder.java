// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: service.proto

package cn.hexcloud.pbis.common.service.integration.eticket;

public interface UploadTicketResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:eticket_proto.UploadTicketResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * （必传）是否上传成功
   * </pre>
   *
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string ticket_id = 2;</code>
   * @return The ticketId.
   */
  java.lang.String getTicketId();
  /**
   * <code>string ticket_id = 2;</code>
   * @return The bytes for ticketId.
   */
  com.google.protobuf.ByteString
      getTicketIdBytes();
}
