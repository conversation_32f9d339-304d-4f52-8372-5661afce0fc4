package cn.hexcloud.pbis.common.service.integration.eticket.client;

import cn.hexcloud.pbis.common.service.integration.eticket.EticketGrpc;
import cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketRequest;
import cn.hexcloud.pbis.common.service.integration.eticket.UploadTicketResponse;
import cn.hexcloud.pbis.common.util.config.PBISApplicationConfig;
import io.grpc.Channel;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import net.devh.boot.grpc.client.inject.GrpcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Classname EticketClient
 * @Description:
 * @Date 2022/1/124:23 下午
 * <AUTHOR>
 */
@Service
public class EticketClient {

  @Autowired
  private PBISApplicationConfig pbisApplicationConfig;

  @GrpcClient("EticketService")
  private Channel serviceChannel;

  private EticketGrpc.EticketBlockingStub eticketBlockingStub;

  @PostConstruct
  public void init() {
    this.eticketBlockingStub = EticketGrpc.newBlockingStub(serviceChannel);
  }


  /**
   * 电子小票上传
   *
   * @param
   */
  public UploadTicketResponse uploadTicket(UploadTicketRequest request) {
    UploadTicketResponse uploadTicketResponse = eticketBlockingStub
        .withDeadlineAfter(pbisApplicationConfig.getGrpcClientTimeout(), TimeUnit.MILLISECONDS)
        .uploadTicket(request);
    return uploadTicketResponse;
  }

}
