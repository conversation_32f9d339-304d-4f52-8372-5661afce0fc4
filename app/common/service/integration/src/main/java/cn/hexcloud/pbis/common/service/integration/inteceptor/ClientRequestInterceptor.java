package cn.hexcloud.pbis.common.service.integration.inteceptor;

import cn.hexcloud.pbis.common.util.GrpcExecutionLogUtil;
import cn.hexcloud.pbis.common.util.GrpcExecutionLogUtil.LogPrefix;
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant;
import cn.hexcloud.pbis.common.util.context.ServiceContext;
import com.google.protobuf.Message;
import io.grpc.CallOptions;
import io.grpc.Channel;
import io.grpc.ClientCall;
import io.grpc.ClientInterceptor;
import io.grpc.ForwardingClientCall;
import io.grpc.ForwardingClientCallListener;
import io.grpc.Metadata;
import io.grpc.MethodDescriptor;
import java.util.Objects;
import lombok.SneakyThrows;
import net.devh.boot.grpc.client.interceptor.GrpcGlobalClientInterceptor;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName RequestInterceptor.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/19 16:02:32
 */
@GrpcGlobalClientInterceptor
public class ClientRequestInterceptor implements ClientInterceptor {

  private static final Metadata.Key<String> PARTNER_ID_KEY = Metadata.Key.of("partner_id", Metadata.ASCII_STRING_MARSHALLER);
  private static final Metadata.Key<String> USER_ID_KEY = Metadata.Key.of("user_id", Metadata.ASCII_STRING_MARSHALLER);
  private static final Metadata.Key<String> SCOPE_ID_KEY = Metadata.Key.of("scope_id", Metadata.ASCII_STRING_MARSHALLER);
  private static final Metadata.Key<String> USER_INFO = Metadata.Key.of("user_info", Metadata.ASCII_STRING_MARSHALLER);

  @Autowired
  private GrpcExecutionLogUtil grpcExecutionLogUtil;

  @Override
  public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(MethodDescriptor<ReqT, RespT> methodDescriptor,
      CallOptions callOptions, Channel channel) {

    return new ForwardingClientCall.SimpleForwardingClientCall<ReqT, RespT>(
        channel.newCall(methodDescriptor, callOptions)) {

      final String methodName = methodDescriptor.getFullMethodName();

      Metadata metadata;

      @SneakyThrows
      @Override
      public void sendMessage(ReqT message) {
        // 打印请求日志
        grpcExecutionLogUtil.log(methodName, (Message) message, metadata, LogPrefix.CALL);
        super.sendMessage(message);
      }

      @Override
      public void start(Listener<RespT> responseListener, Metadata headers) {
        // 设置请求元数据参数
        metadata = headers;
        headers.put(PARTNER_ID_KEY, ServiceContext.getString(ContextKeyConstant.PARTNER_ID));
        headers.put(USER_ID_KEY, ServiceContext.getString(ContextKeyConstant.USER_ID));
        //headers.put(SCOPE_ID_KEY, ServiceContext.getString(ContextKeyConstant.SCOPE_ID));
        if (Objects.nonNull(ServiceContext.getString(ContextKeyConstant.USER_INFO))){
          headers.put(USER_INFO,ServiceContext.getString(ContextKeyConstant.USER_INFO));
        }
        super.start(
            new ForwardingClientCallListener.SimpleForwardingClientCallListener<RespT>(responseListener) {
              @SneakyThrows
              @Override
              public void onMessage(RespT message) {
                // 打印响应日志
                grpcExecutionLogUtil.log(methodName, (Message) message, null, LogPrefix.RETURN);
                super.onMessage(message);
              }
            }, headers);
      }
    };
  }
}
