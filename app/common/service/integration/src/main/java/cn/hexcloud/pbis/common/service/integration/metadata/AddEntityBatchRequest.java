// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

/**
 * Protobuf type {@code entity.AddEntityBatchRequest}
 */
public final class AddEntityBatchRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:entity.AddEntityBatchRequest)
    AddEntityBatchRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use AddEntityBatchRequest.newBuilder() to construct.
  private AddEntityBatchRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private AddEntityBatchRequest() {
    entitys_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new AddEntityBatchRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private AddEntityBatchRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              entitys_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest>();
              mutable_bitField0_ |= 0x00000001;
            }
            entitys_.add(
                input.readMessage(cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        entitys_ = java.util.Collections.unmodifiableList(entitys_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_AddEntityBatchRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_AddEntityBatchRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest.class, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest.Builder.class);
  }

  public static final int ENTITYS_FIELD_NUMBER = 1;
  private java.util.List<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest> entitys_;
  /**
   * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
   */
  @java.lang.Override
  public java.util.List<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest> getEntitysList() {
    return entitys_;
  }
  /**
   * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
   */
  @java.lang.Override
  public java.util.List<? extends cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequestOrBuilder> 
      getEntitysOrBuilderList() {
    return entitys_;
  }
  /**
   * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
   */
  @java.lang.Override
  public int getEntitysCount() {
    return entitys_.size();
  }
  /**
   * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest getEntitys(int index) {
    return entitys_.get(index);
  }
  /**
   * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequestOrBuilder getEntitysOrBuilder(
      int index) {
    return entitys_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < entitys_.size(); i++) {
      output.writeMessage(1, entitys_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    for (int i = 0; i < entitys_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, entitys_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest other = (cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest) obj;

    if (!getEntitysList()
        .equals(other.getEntitysList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getEntitysCount() > 0) {
      hash = (37 * hash) + ENTITYS_FIELD_NUMBER;
      hash = (53 * hash) + getEntitysList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code entity.AddEntityBatchRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:entity.AddEntityBatchRequest)
      cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_AddEntityBatchRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_AddEntityBatchRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest.class, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getEntitysFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      if (entitysBuilder_ == null) {
        entitys_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        entitysBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_AddEntityBatchRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest build() {
      cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest buildPartial() {
      cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest result = new cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest(this);
      int from_bitField0_ = bitField0_;
      if (entitysBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          entitys_ = java.util.Collections.unmodifiableList(entitys_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.entitys_ = entitys_;
      } else {
        result.entitys_ = entitysBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest other) {
      if (other == cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest.getDefaultInstance()) return this;
      if (entitysBuilder_ == null) {
        if (!other.entitys_.isEmpty()) {
          if (entitys_.isEmpty()) {
            entitys_ = other.entitys_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureEntitysIsMutable();
            entitys_.addAll(other.entitys_);
          }
          onChanged();
        }
      } else {
        if (!other.entitys_.isEmpty()) {
          if (entitysBuilder_.isEmpty()) {
            entitysBuilder_.dispose();
            entitysBuilder_ = null;
            entitys_ = other.entitys_;
            bitField0_ = (bitField0_ & ~0x00000001);
            entitysBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getEntitysFieldBuilder() : null;
          } else {
            entitysBuilder_.addAllMessages(other.entitys_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.util.List<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest> entitys_ =
      java.util.Collections.emptyList();
    private void ensureEntitysIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        entitys_ = new java.util.ArrayList<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest>(entitys_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.Builder, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequestOrBuilder> entitysBuilder_;

    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest> getEntitysList() {
      if (entitysBuilder_ == null) {
        return java.util.Collections.unmodifiableList(entitys_);
      } else {
        return entitysBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public int getEntitysCount() {
      if (entitysBuilder_ == null) {
        return entitys_.size();
      } else {
        return entitysBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest getEntitys(int index) {
      if (entitysBuilder_ == null) {
        return entitys_.get(index);
      } else {
        return entitysBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public Builder setEntitys(
        int index, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest value) {
      if (entitysBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEntitysIsMutable();
        entitys_.set(index, value);
        onChanged();
      } else {
        entitysBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public Builder setEntitys(
        int index, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.Builder builderForValue) {
      if (entitysBuilder_ == null) {
        ensureEntitysIsMutable();
        entitys_.set(index, builderForValue.build());
        onChanged();
      } else {
        entitysBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public Builder addEntitys(cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest value) {
      if (entitysBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEntitysIsMutable();
        entitys_.add(value);
        onChanged();
      } else {
        entitysBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public Builder addEntitys(
        int index, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest value) {
      if (entitysBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureEntitysIsMutable();
        entitys_.add(index, value);
        onChanged();
      } else {
        entitysBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public Builder addEntitys(
        cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.Builder builderForValue) {
      if (entitysBuilder_ == null) {
        ensureEntitysIsMutable();
        entitys_.add(builderForValue.build());
        onChanged();
      } else {
        entitysBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public Builder addEntitys(
        int index, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.Builder builderForValue) {
      if (entitysBuilder_ == null) {
        ensureEntitysIsMutable();
        entitys_.add(index, builderForValue.build());
        onChanged();
      } else {
        entitysBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public Builder addAllEntitys(
        java.lang.Iterable<? extends cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest> values) {
      if (entitysBuilder_ == null) {
        ensureEntitysIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, entitys_);
        onChanged();
      } else {
        entitysBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public Builder clearEntitys() {
      if (entitysBuilder_ == null) {
        entitys_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        entitysBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public Builder removeEntitys(int index) {
      if (entitysBuilder_ == null) {
        ensureEntitysIsMutable();
        entitys_.remove(index);
        onChanged();
      } else {
        entitysBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.Builder getEntitysBuilder(
        int index) {
      return getEntitysFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequestOrBuilder getEntitysOrBuilder(
        int index) {
      if (entitysBuilder_ == null) {
        return entitys_.get(index);  } else {
        return entitysBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public java.util.List<? extends cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequestOrBuilder> 
         getEntitysOrBuilderList() {
      if (entitysBuilder_ != null) {
        return entitysBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(entitys_);
      }
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.Builder addEntitysBuilder() {
      return getEntitysFieldBuilder().addBuilder(
          cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.getDefaultInstance());
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.Builder addEntitysBuilder(
        int index) {
      return getEntitysFieldBuilder().addBuilder(
          index, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.getDefaultInstance());
    }
    /**
     * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
     */
    public java.util.List<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.Builder> 
         getEntitysBuilderList() {
      return getEntitysFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.Builder, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequestOrBuilder> 
        getEntitysFieldBuilder() {
      if (entitysBuilder_ == null) {
        entitysBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.Builder, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequestOrBuilder>(
                entitys_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        entitys_ = null;
      }
      return entitysBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:entity.AddEntityBatchRequest)
  }

  // @@protoc_insertion_point(class_scope:entity.AddEntityBatchRequest)
  private static final cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest();
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AddEntityBatchRequest>
      PARSER = new com.google.protobuf.AbstractParser<AddEntityBatchRequest>() {
    @java.lang.Override
    public AddEntityBatchRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new AddEntityBatchRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<AddEntityBatchRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AddEntityBatchRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

