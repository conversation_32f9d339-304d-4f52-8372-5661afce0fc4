// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface AddEntityBatchRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.AddEntityBatchRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest> 
      getEntitysList();
  /**
   * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
   */
  cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest getEntitys(int index);
  /**
   * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
   */
  int getEntitysCount();
  /**
   * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequestOrBuilder> 
      getEntitysOrBuilderList();
  /**
   * <code>repeated .entity.AddEntityRequest entitys = 1;</code>
   */
  cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequestOrBuilder getEntitysOrBuilder(
      int index);
}
