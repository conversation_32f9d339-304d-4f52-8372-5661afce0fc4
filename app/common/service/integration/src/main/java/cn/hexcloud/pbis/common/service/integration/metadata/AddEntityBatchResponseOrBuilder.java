// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface AddEntityBatchResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.AddEntityBatchResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string message = 1;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 1;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>string code = 2;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <code>string code = 2;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <code>repeated uint64 error_ids = 3;</code>
   * @return A list containing the errorIds.
   */
  java.util.List<java.lang.Long> getErrorIdsList();
  /**
   * <code>repeated uint64 error_ids = 3;</code>
   * @return The count of errorIds.
   */
  int getErrorIdsCount();
  /**
   * <code>repeated uint64 error_ids = 3;</code>
   * @param index The index of the element to return.
   * @return The errorIds at the given index.
   */
  long getErrorIds(int index);
}
