// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

/**
 * Protobuf type {@code entity.AddEntityRequest}
 */
public final class AddEntityRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:entity.AddEntityRequest)
    AddEntityRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use AddEntityRequest.newBuilder() to construct.
  private AddEntityRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private AddEntityRequest() {
    schemaName_ = "";
    lan_ = "";
    id_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new AddEntityRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private AddEntityRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            schemaName_ = s;
            break;
          }
          case 16: {

            autoEnable_ = input.readBool();
            break;
          }
          case 26: {
            com.google.protobuf.Struct.Builder subBuilder = null;
            if (fields_ != null) {
              subBuilder = fields_.toBuilder();
            }
            fields_ = input.readMessage(com.google.protobuf.Struct.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(fields_);
              fields_ = subBuilder.buildPartial();
            }

            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            lan_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            id_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_AddEntityRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_AddEntityRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.class, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.Builder.class);
  }

  public static final int SCHEMA_NAME_FIELD_NUMBER = 1;
  private volatile java.lang.Object schemaName_;
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 1;</code>
   * @return The schemaName.
   */
  @java.lang.Override
  public java.lang.String getSchemaName() {
    java.lang.Object ref = schemaName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      schemaName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 1;</code>
   * @return The bytes for schemaName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSchemaNameBytes() {
    java.lang.Object ref = schemaName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      schemaName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AUTO_ENABLE_FIELD_NUMBER = 2;
  private boolean autoEnable_;
  /**
   * <pre>
   * 创建之后enable数据
   * </pre>
   *
   * <code>bool auto_enable = 2;</code>
   * @return The autoEnable.
   */
  @java.lang.Override
  public boolean getAutoEnable() {
    return autoEnable_;
  }

  public static final int FIELDS_FIELD_NUMBER = 3;
  private com.google.protobuf.Struct fields_;
  /**
   * <pre>
   * Entity字段json
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 3;</code>
   * @return Whether the fields field is set.
   */
  @java.lang.Override
  public boolean hasFields() {
    return fields_ != null;
  }
  /**
   * <pre>
   * Entity字段json
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 3;</code>
   * @return The fields.
   */
  @java.lang.Override
  public com.google.protobuf.Struct getFields() {
    return fields_ == null ? com.google.protobuf.Struct.getDefaultInstance() : fields_;
  }
  /**
   * <pre>
   * Entity字段json
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 3;</code>
   */
  @java.lang.Override
  public com.google.protobuf.StructOrBuilder getFieldsOrBuilder() {
    return getFields();
  }

  public static final int LAN_FIELD_NUMBER = 4;
  private volatile java.lang.Object lan_;
  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 4;</code>
   * @return The lan.
   */
  @java.lang.Override
  public java.lang.String getLan() {
    java.lang.Object ref = lan_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      lan_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 4;</code>
   * @return The bytes for lan.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLanBytes() {
    java.lang.Object ref = lan_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      lan_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ID_FIELD_NUMBER = 5;
  private volatile java.lang.Object id_;
  /**
   * <pre>
   *为了导入数据的参数
   * </pre>
   *
   * <code>string id = 5;</code>
   * @return The id.
   */
  @java.lang.Override
  public java.lang.String getId() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *为了导入数据的参数
   * </pre>
   *
   * <code>string id = 5;</code>
   * @return The bytes for id.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getIdBytes() {
    java.lang.Object ref = id_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getSchemaNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, schemaName_);
    }
    if (autoEnable_ != false) {
      output.writeBool(2, autoEnable_);
    }
    if (fields_ != null) {
      output.writeMessage(3, getFields());
    }
    if (!getLanBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, lan_);
    }
    if (!getIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, id_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getSchemaNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, schemaName_);
    }
    if (autoEnable_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(2, autoEnable_);
    }
    if (fields_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getFields());
    }
    if (!getLanBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, lan_);
    }
    if (!getIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, id_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest other = (cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest) obj;

    if (!getSchemaName()
        .equals(other.getSchemaName())) return false;
    if (getAutoEnable()
        != other.getAutoEnable()) return false;
    if (hasFields() != other.hasFields()) return false;
    if (hasFields()) {
      if (!getFields()
          .equals(other.getFields())) return false;
    }
    if (!getLan()
        .equals(other.getLan())) return false;
    if (!getId()
        .equals(other.getId())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SCHEMA_NAME_FIELD_NUMBER;
    hash = (53 * hash) + getSchemaName().hashCode();
    hash = (37 * hash) + AUTO_ENABLE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getAutoEnable());
    if (hasFields()) {
      hash = (37 * hash) + FIELDS_FIELD_NUMBER;
      hash = (53 * hash) + getFields().hashCode();
    }
    hash = (37 * hash) + LAN_FIELD_NUMBER;
    hash = (53 * hash) + getLan().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code entity.AddEntityRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:entity.AddEntityRequest)
      cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_AddEntityRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_AddEntityRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.class, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      schemaName_ = "";

      autoEnable_ = false;

      if (fieldsBuilder_ == null) {
        fields_ = null;
      } else {
        fields_ = null;
        fieldsBuilder_ = null;
      }
      lan_ = "";

      id_ = "";

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_AddEntityRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest build() {
      cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest buildPartial() {
      cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest result = new cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest(this);
      result.schemaName_ = schemaName_;
      result.autoEnable_ = autoEnable_;
      if (fieldsBuilder_ == null) {
        result.fields_ = fields_;
      } else {
        result.fields_ = fieldsBuilder_.build();
      }
      result.lan_ = lan_;
      result.id_ = id_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest other) {
      if (other == cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.getDefaultInstance()) return this;
      if (!other.getSchemaName().isEmpty()) {
        schemaName_ = other.schemaName_;
        onChanged();
      }
      if (other.getAutoEnable() != false) {
        setAutoEnable(other.getAutoEnable());
      }
      if (other.hasFields()) {
        mergeFields(other.getFields());
      }
      if (!other.getLan().isEmpty()) {
        lan_ = other.lan_;
        onChanged();
      }
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private java.lang.Object schemaName_ = "";
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 1;</code>
     * @return The schemaName.
     */
    public java.lang.String getSchemaName() {
      java.lang.Object ref = schemaName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        schemaName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 1;</code>
     * @return The bytes for schemaName.
     */
    public com.google.protobuf.ByteString
        getSchemaNameBytes() {
      java.lang.Object ref = schemaName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        schemaName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 1;</code>
     * @param value The schemaName to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      schemaName_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSchemaName() {
      
      schemaName_ = getDefaultInstance().getSchemaName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 1;</code>
     * @param value The bytes for schemaName to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      schemaName_ = value;
      onChanged();
      return this;
    }

    private boolean autoEnable_ ;
    /**
     * <pre>
     * 创建之后enable数据
     * </pre>
     *
     * <code>bool auto_enable = 2;</code>
     * @return The autoEnable.
     */
    @java.lang.Override
    public boolean getAutoEnable() {
      return autoEnable_;
    }
    /**
     * <pre>
     * 创建之后enable数据
     * </pre>
     *
     * <code>bool auto_enable = 2;</code>
     * @param value The autoEnable to set.
     * @return This builder for chaining.
     */
    public Builder setAutoEnable(boolean value) {
      
      autoEnable_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 创建之后enable数据
     * </pre>
     *
     * <code>bool auto_enable = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearAutoEnable() {
      
      autoEnable_ = false;
      onChanged();
      return this;
    }

    private com.google.protobuf.Struct fields_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> fieldsBuilder_;
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 3;</code>
     * @return Whether the fields field is set.
     */
    public boolean hasFields() {
      return fieldsBuilder_ != null || fields_ != null;
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 3;</code>
     * @return The fields.
     */
    public com.google.protobuf.Struct getFields() {
      if (fieldsBuilder_ == null) {
        return fields_ == null ? com.google.protobuf.Struct.getDefaultInstance() : fields_;
      } else {
        return fieldsBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 3;</code>
     */
    public Builder setFields(com.google.protobuf.Struct value) {
      if (fieldsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        fields_ = value;
        onChanged();
      } else {
        fieldsBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 3;</code>
     */
    public Builder setFields(
        com.google.protobuf.Struct.Builder builderForValue) {
      if (fieldsBuilder_ == null) {
        fields_ = builderForValue.build();
        onChanged();
      } else {
        fieldsBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 3;</code>
     */
    public Builder mergeFields(com.google.protobuf.Struct value) {
      if (fieldsBuilder_ == null) {
        if (fields_ != null) {
          fields_ =
            com.google.protobuf.Struct.newBuilder(fields_).mergeFrom(value).buildPartial();
        } else {
          fields_ = value;
        }
        onChanged();
      } else {
        fieldsBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 3;</code>
     */
    public Builder clearFields() {
      if (fieldsBuilder_ == null) {
        fields_ = null;
        onChanged();
      } else {
        fields_ = null;
        fieldsBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 3;</code>
     */
    public com.google.protobuf.Struct.Builder getFieldsBuilder() {
      
      onChanged();
      return getFieldsFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 3;</code>
     */
    public com.google.protobuf.StructOrBuilder getFieldsOrBuilder() {
      if (fieldsBuilder_ != null) {
        return fieldsBuilder_.getMessageOrBuilder();
      } else {
        return fields_ == null ?
            com.google.protobuf.Struct.getDefaultInstance() : fields_;
      }
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> 
        getFieldsFieldBuilder() {
      if (fieldsBuilder_ == null) {
        fieldsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder>(
                getFields(),
                getParentForChildren(),
                isClean());
        fields_ = null;
      }
      return fieldsBuilder_;
    }

    private java.lang.Object lan_ = "";
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 4;</code>
     * @return The lan.
     */
    public java.lang.String getLan() {
      java.lang.Object ref = lan_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        lan_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 4;</code>
     * @return The bytes for lan.
     */
    public com.google.protobuf.ByteString
        getLanBytes() {
      java.lang.Object ref = lan_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        lan_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 4;</code>
     * @param value The lan to set.
     * @return This builder for chaining.
     */
    public Builder setLan(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      lan_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearLan() {
      
      lan_ = getDefaultInstance().getLan();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 4;</code>
     * @param value The bytes for lan to set.
     * @return This builder for chaining.
     */
    public Builder setLanBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      lan_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object id_ = "";
    /**
     * <pre>
     *为了导入数据的参数
     * </pre>
     *
     * <code>string id = 5;</code>
     * @return The id.
     */
    public java.lang.String getId() {
      java.lang.Object ref = id_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     *为了导入数据的参数
     * </pre>
     *
     * <code>string id = 5;</code>
     * @return The bytes for id.
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      java.lang.Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *为了导入数据的参数
     * </pre>
     *
     * <code>string id = 5;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *为了导入数据的参数
     * </pre>
     *
     * <code>string id = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      
      id_ = getDefaultInstance().getId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *为了导入数据的参数
     * </pre>
     *
     * <code>string id = 5;</code>
     * @param value The bytes for id to set.
     * @return This builder for chaining.
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      id_ = value;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:entity.AddEntityRequest)
  }

  // @@protoc_insertion_point(class_scope:entity.AddEntityRequest)
  private static final cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest();
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<AddEntityRequest>
      PARSER = new com.google.protobuf.AbstractParser<AddEntityRequest>() {
    @java.lang.Override
    public AddEntityRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new AddEntityRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<AddEntityRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<AddEntityRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

