// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface AddEntityRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.AddEntityRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 1;</code>
   * @return The schemaName.
   */
  java.lang.String getSchemaName();
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 1;</code>
   * @return The bytes for schemaName.
   */
  com.google.protobuf.ByteString
      getSchemaNameBytes();

  /**
   * <pre>
   * 创建之后enable数据
   * </pre>
   *
   * <code>bool auto_enable = 2;</code>
   * @return The autoEnable.
   */
  boolean getAutoEnable();

  /**
   * <pre>
   * Entity字段json
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 3;</code>
   * @return Whether the fields field is set.
   */
  boolean hasFields();
  /**
   * <pre>
   * Entity字段json
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 3;</code>
   * @return The fields.
   */
  com.google.protobuf.Struct getFields();
  /**
   * <pre>
   * Entity字段json
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 3;</code>
   */
  com.google.protobuf.StructOrBuilder getFieldsOrBuilder();

  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 4;</code>
   * @return The lan.
   */
  java.lang.String getLan();
  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 4;</code>
   * @return The bytes for lan.
   */
  com.google.protobuf.ByteString
      getLanBytes();

  /**
   * <pre>
   *为了导入数据的参数
   * </pre>
   *
   * <code>string id = 5;</code>
   * @return The id.
   */
  java.lang.String getId();
  /**
   * <pre>
   *为了导入数据的参数
   * </pre>
   *
   * <code>string id = 5;</code>
   * @return The bytes for id.
   */
  com.google.protobuf.ByteString
      getIdBytes();
}
