// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

/**
 * Protobuf type {@code entity.CreateEntityTaskFromPendingChangesRequest}
 */
public final class CreateEntityTaskFromPendingChangesRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:entity.CreateEntityTaskFromPendingChangesRequest)
    CreateEntityTaskFromPendingChangesRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use CreateEntityTaskFromPendingChangesRequest.newBuilder() to construct.
  private CreateEntityTaskFromPendingChangesRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private CreateEntityTaskFromPendingChangesRequest() {
    schemaName_ = "";
    name_ = "";
    start_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new CreateEntityTaskFromPendingChangesRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private CreateEntityTaskFromPendingChangesRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            recordId_ = input.readUInt64();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            schemaName_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 32: {

            immediate_ = input.readBool();
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            start_ = s;
            break;
          }
          case 48: {

            autoApprove_ = input.readBool();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_CreateEntityTaskFromPendingChangesRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_CreateEntityTaskFromPendingChangesRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest.class, cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest.Builder.class);
  }

  public static final int RECORD_ID_FIELD_NUMBER = 1;
  private long recordId_;
  /**
   * <pre>
   * record_id
   * </pre>
   *
   * <code>uint64 record_id = 1;</code>
   * @return The recordId.
   */
  @java.lang.Override
  public long getRecordId() {
    return recordId_;
  }

  public static final int SCHEMA_NAME_FIELD_NUMBER = 2;
  private volatile java.lang.Object schemaName_;
  /**
   * <code>string schema_name = 2;</code>
   * @return The schemaName.
   */
  @java.lang.Override
  public java.lang.String getSchemaName() {
    java.lang.Object ref = schemaName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      schemaName_ = s;
      return s;
    }
  }
  /**
   * <code>string schema_name = 2;</code>
   * @return The bytes for schemaName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSchemaNameBytes() {
    java.lang.Object ref = schemaName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      schemaName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 3;
  private volatile java.lang.Object name_;
  /**
   * <pre>
   * task 名称
   * </pre>
   *
   * <code>string name = 3;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * task 名称
   * </pre>
   *
   * <code>string name = 3;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IMMEDIATE_FIELD_NUMBER = 4;
  private boolean immediate_;
  /**
   * <pre>
   * 是否立即执行
   * </pre>
   *
   * <code>bool immediate = 4;</code>
   * @return The immediate.
   */
  @java.lang.Override
  public boolean getImmediate() {
    return immediate_;
  }

  public static final int START_FIELD_NUMBER = 5;
  private volatile java.lang.Object start_;
  /**
   * <pre>
   * 开始执行时间
   * </pre>
   *
   * <code>string start = 5;</code>
   * @return The start.
   */
  @java.lang.Override
  public java.lang.String getStart() {
    java.lang.Object ref = start_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      start_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 开始执行时间
   * </pre>
   *
   * <code>string start = 5;</code>
   * @return The bytes for start.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStartBytes() {
    java.lang.Object ref = start_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      start_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AUTO_APPROVE_FIELD_NUMBER = 6;
  private boolean autoApprove_;
  /**
   * <pre>
   * 自动审核
   * </pre>
   *
   * <code>bool auto_approve = 6;</code>
   * @return The autoApprove.
   */
  @java.lang.Override
  public boolean getAutoApprove() {
    return autoApprove_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (recordId_ != 0L) {
      output.writeUInt64(1, recordId_);
    }
    if (!getSchemaNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, schemaName_);
    }
    if (!getNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, name_);
    }
    if (immediate_ != false) {
      output.writeBool(4, immediate_);
    }
    if (!getStartBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, start_);
    }
    if (autoApprove_ != false) {
      output.writeBool(6, autoApprove_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (recordId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, recordId_);
    }
    if (!getSchemaNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, schemaName_);
    }
    if (!getNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, name_);
    }
    if (immediate_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(4, immediate_);
    }
    if (!getStartBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, start_);
    }
    if (autoApprove_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(6, autoApprove_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest other = (cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest) obj;

    if (getRecordId()
        != other.getRecordId()) return false;
    if (!getSchemaName()
        .equals(other.getSchemaName())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (getImmediate()
        != other.getImmediate()) return false;
    if (!getStart()
        .equals(other.getStart())) return false;
    if (getAutoApprove()
        != other.getAutoApprove()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + RECORD_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getRecordId());
    hash = (37 * hash) + SCHEMA_NAME_FIELD_NUMBER;
    hash = (53 * hash) + getSchemaName().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + IMMEDIATE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getImmediate());
    hash = (37 * hash) + START_FIELD_NUMBER;
    hash = (53 * hash) + getStart().hashCode();
    hash = (37 * hash) + AUTO_APPROVE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getAutoApprove());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code entity.CreateEntityTaskFromPendingChangesRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:entity.CreateEntityTaskFromPendingChangesRequest)
      cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_CreateEntityTaskFromPendingChangesRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_CreateEntityTaskFromPendingChangesRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest.class, cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      recordId_ = 0L;

      schemaName_ = "";

      name_ = "";

      immediate_ = false;

      start_ = "";

      autoApprove_ = false;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_CreateEntityTaskFromPendingChangesRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest build() {
      cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest buildPartial() {
      cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest result = new cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest(this);
      result.recordId_ = recordId_;
      result.schemaName_ = schemaName_;
      result.name_ = name_;
      result.immediate_ = immediate_;
      result.start_ = start_;
      result.autoApprove_ = autoApprove_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest other) {
      if (other == cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest.getDefaultInstance()) return this;
      if (other.getRecordId() != 0L) {
        setRecordId(other.getRecordId());
      }
      if (!other.getSchemaName().isEmpty()) {
        schemaName_ = other.schemaName_;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (other.getImmediate() != false) {
        setImmediate(other.getImmediate());
      }
      if (!other.getStart().isEmpty()) {
        start_ = other.start_;
        onChanged();
      }
      if (other.getAutoApprove() != false) {
        setAutoApprove(other.getAutoApprove());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long recordId_ ;
    /**
     * <pre>
     * record_id
     * </pre>
     *
     * <code>uint64 record_id = 1;</code>
     * @return The recordId.
     */
    @java.lang.Override
    public long getRecordId() {
      return recordId_;
    }
    /**
     * <pre>
     * record_id
     * </pre>
     *
     * <code>uint64 record_id = 1;</code>
     * @param value The recordId to set.
     * @return This builder for chaining.
     */
    public Builder setRecordId(long value) {
      
      recordId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * record_id
     * </pre>
     *
     * <code>uint64 record_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRecordId() {
      
      recordId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object schemaName_ = "";
    /**
     * <code>string schema_name = 2;</code>
     * @return The schemaName.
     */
    public java.lang.String getSchemaName() {
      java.lang.Object ref = schemaName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        schemaName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string schema_name = 2;</code>
     * @return The bytes for schemaName.
     */
    public com.google.protobuf.ByteString
        getSchemaNameBytes() {
      java.lang.Object ref = schemaName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        schemaName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string schema_name = 2;</code>
     * @param value The schemaName to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      schemaName_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string schema_name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSchemaName() {
      
      schemaName_ = getDefaultInstance().getSchemaName();
      onChanged();
      return this;
    }
    /**
     * <code>string schema_name = 2;</code>
     * @param value The bytes for schemaName to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      schemaName_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <pre>
     * task 名称
     * </pre>
     *
     * <code>string name = 3;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * task 名称
     * </pre>
     *
     * <code>string name = 3;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * task 名称
     * </pre>
     *
     * <code>string name = 3;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * task 名称
     * </pre>
     *
     * <code>string name = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * task 名称
     * </pre>
     *
     * <code>string name = 3;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private boolean immediate_ ;
    /**
     * <pre>
     * 是否立即执行
     * </pre>
     *
     * <code>bool immediate = 4;</code>
     * @return The immediate.
     */
    @java.lang.Override
    public boolean getImmediate() {
      return immediate_;
    }
    /**
     * <pre>
     * 是否立即执行
     * </pre>
     *
     * <code>bool immediate = 4;</code>
     * @param value The immediate to set.
     * @return This builder for chaining.
     */
    public Builder setImmediate(boolean value) {
      
      immediate_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 是否立即执行
     * </pre>
     *
     * <code>bool immediate = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearImmediate() {
      
      immediate_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object start_ = "";
    /**
     * <pre>
     * 开始执行时间
     * </pre>
     *
     * <code>string start = 5;</code>
     * @return The start.
     */
    public java.lang.String getStart() {
      java.lang.Object ref = start_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        start_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 开始执行时间
     * </pre>
     *
     * <code>string start = 5;</code>
     * @return The bytes for start.
     */
    public com.google.protobuf.ByteString
        getStartBytes() {
      java.lang.Object ref = start_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        start_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 开始执行时间
     * </pre>
     *
     * <code>string start = 5;</code>
     * @param value The start to set.
     * @return This builder for chaining.
     */
    public Builder setStart(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      start_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 开始执行时间
     * </pre>
     *
     * <code>string start = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearStart() {
      
      start_ = getDefaultInstance().getStart();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 开始执行时间
     * </pre>
     *
     * <code>string start = 5;</code>
     * @param value The bytes for start to set.
     * @return This builder for chaining.
     */
    public Builder setStartBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      start_ = value;
      onChanged();
      return this;
    }

    private boolean autoApprove_ ;
    /**
     * <pre>
     * 自动审核
     * </pre>
     *
     * <code>bool auto_approve = 6;</code>
     * @return The autoApprove.
     */
    @java.lang.Override
    public boolean getAutoApprove() {
      return autoApprove_;
    }
    /**
     * <pre>
     * 自动审核
     * </pre>
     *
     * <code>bool auto_approve = 6;</code>
     * @param value The autoApprove to set.
     * @return This builder for chaining.
     */
    public Builder setAutoApprove(boolean value) {
      
      autoApprove_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 自动审核
     * </pre>
     *
     * <code>bool auto_approve = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearAutoApprove() {
      
      autoApprove_ = false;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:entity.CreateEntityTaskFromPendingChangesRequest)
  }

  // @@protoc_insertion_point(class_scope:entity.CreateEntityTaskFromPendingChangesRequest)
  private static final cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest();
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CreateEntityTaskFromPendingChangesRequest>
      PARSER = new com.google.protobuf.AbstractParser<CreateEntityTaskFromPendingChangesRequest>() {
    @java.lang.Override
    public CreateEntityTaskFromPendingChangesRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new CreateEntityTaskFromPendingChangesRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<CreateEntityTaskFromPendingChangesRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CreateEntityTaskFromPendingChangesRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

