// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface CreateEntityTaskFromPendingChangesRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.CreateEntityTaskFromPendingChangesRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * record_id
   * </pre>
   *
   * <code>uint64 record_id = 1;</code>
   * @return The recordId.
   */
  long getRecordId();

  /**
   * <code>string schema_name = 2;</code>
   * @return The schemaName.
   */
  java.lang.String getSchemaName();
  /**
   * <code>string schema_name = 2;</code>
   * @return The bytes for schemaName.
   */
  com.google.protobuf.ByteString
      getSchemaNameBytes();

  /**
   * <pre>
   * task 名称
   * </pre>
   *
   * <code>string name = 3;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   * task 名称
   * </pre>
   *
   * <code>string name = 3;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * 是否立即执行
   * </pre>
   *
   * <code>bool immediate = 4;</code>
   * @return The immediate.
   */
  boolean getImmediate();

  /**
   * <pre>
   * 开始执行时间
   * </pre>
   *
   * <code>string start = 5;</code>
   * @return The start.
   */
  java.lang.String getStart();
  /**
   * <pre>
   * 开始执行时间
   * </pre>
   *
   * <code>string start = 5;</code>
   * @return The bytes for start.
   */
  com.google.protobuf.ByteString
      getStartBytes();

  /**
   * <pre>
   * 自动审核
   * </pre>
   *
   * <code>bool auto_approve = 6;</code>
   * @return The autoApprove.
   */
  boolean getAutoApprove();
}
