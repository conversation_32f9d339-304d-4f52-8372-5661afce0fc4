// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

/**
 * Protobuf type {@code entity.Entity}
 */
public final class Entity extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:entity.Entity)
    EntityOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Entity.newBuilder() to construct.
  private Entity(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Entity() {
    schemaName_ = "";
    state_ = "";
    created_ = "";
    updated_ = "";
    createdBy_ = "";
    updatedBy_ = "";
    processStatus_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new Entity();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Entity(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            id_ = input.readUInt64();
            break;
          }
          case 16: {

            partnerId_ = input.readUInt64();
            break;
          }
          case 24: {

            scopeId_ = input.readUInt64();
            break;
          }
          case 32: {

            parentId_ = input.readUInt64();
            break;
          }
          case 40: {

            schemaId_ = input.readUInt64();
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            schemaName_ = s;
            break;
          }
          case 58: {
            java.lang.String s = input.readStringRequireUtf8();

            state_ = s;
            break;
          }
          case 66: {
            com.google.protobuf.Struct.Builder subBuilder = null;
            if (fields_ != null) {
              subBuilder = fields_.toBuilder();
            }
            fields_ = input.readMessage(com.google.protobuf.Struct.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(fields_);
              fields_ = subBuilder.buildPartial();
            }

            break;
          }
          case 74: {
            com.google.protobuf.Struct.Builder subBuilder = null;
            if (fieldsPending_ != null) {
              subBuilder = fieldsPending_.toBuilder();
            }
            fieldsPending_ = input.readMessage(com.google.protobuf.Struct.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(fieldsPending_);
              fieldsPending_ = subBuilder.buildPartial();
            }

            break;
          }
          case 82: {
            com.google.protobuf.Struct.Builder subBuilder = null;
            if (recordPending_ != null) {
              subBuilder = recordPending_.toBuilder();
            }
            recordPending_ = input.readMessage(com.google.protobuf.Struct.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(recordPending_);
              recordPending_ = subBuilder.buildPartial();
            }

            break;
          }
          case 88: {

            pending_ = input.readBool();
            break;
          }
          case 98: {
            java.lang.String s = input.readStringRequireUtf8();

            created_ = s;
            break;
          }
          case 106: {
            java.lang.String s = input.readStringRequireUtf8();

            updated_ = s;
            break;
          }
          case 114: {
            java.lang.String s = input.readStringRequireUtf8();

            createdBy_ = s;
            break;
          }
          case 122: {
            java.lang.String s = input.readStringRequireUtf8();

            updatedBy_ = s;
            break;
          }
          case 130: {
            java.lang.String s = input.readStringRequireUtf8();

            processStatus_ = s;
            break;
          }
          case 138: {
            cn.hexcloud.pbis.common.service.integration.metadata.Entity.Builder subBuilder = null;
            if (parent_ != null) {
              subBuilder = parent_.toBuilder();
            }
            parent_ = input.readMessage(cn.hexcloud.pbis.common.service.integration.metadata.Entity.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(parent_);
              parent_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_Entity_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_Entity_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.metadata.Entity.class, cn.hexcloud.pbis.common.service.integration.metadata.Entity.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  private long id_;
  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>uint64 id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public long getId() {
    return id_;
  }

  public static final int PARTNER_ID_FIELD_NUMBER = 2;
  private long partnerId_;
  /**
   * <pre>
   * 商户id
   * </pre>
   *
   * <code>uint64 partner_id = 2;</code>
   * @return The partnerId.
   */
  @java.lang.Override
  public long getPartnerId() {
    return partnerId_;
  }

  public static final int SCOPE_ID_FIELD_NUMBER = 3;
  private long scopeId_;
  /**
   * <pre>
   * scope_id
   * </pre>
   *
   * <code>uint64 scope_id = 3;</code>
   * @return The scopeId.
   */
  @java.lang.Override
  public long getScopeId() {
    return scopeId_;
  }

  public static final int PARENT_ID_FIELD_NUMBER = 4;
  private long parentId_;
  /**
   * <pre>
   * 父级id
   * </pre>
   *
   * <code>uint64 parent_id = 4;</code>
   * @return The parentId.
   */
  @java.lang.Override
  public long getParentId() {
    return parentId_;
  }

  public static final int SCHEMA_ID_FIELD_NUMBER = 5;
  private long schemaId_;
  /**
   * <pre>
   * schema id
   * </pre>
   *
   * <code>uint64 schema_id = 5;</code>
   * @return The schemaId.
   */
  @java.lang.Override
  public long getSchemaId() {
    return schemaId_;
  }

  public static final int SCHEMA_NAME_FIELD_NUMBER = 6;
  private volatile java.lang.Object schemaName_;
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 6;</code>
   * @return The schemaName.
   */
  @java.lang.Override
  public java.lang.String getSchemaName() {
    java.lang.Object ref = schemaName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      schemaName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 6;</code>
   * @return The bytes for schemaName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSchemaNameBytes() {
    java.lang.Object ref = schemaName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      schemaName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATE_FIELD_NUMBER = 7;
  private volatile java.lang.Object state_;
  /**
   * <pre>
   * 数据状态
   * </pre>
   *
   * <code>string state = 7;</code>
   * @return The state.
   */
  @java.lang.Override
  public java.lang.String getState() {
    java.lang.Object ref = state_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      state_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 数据状态
   * </pre>
   *
   * <code>string state = 7;</code>
   * @return The bytes for state.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStateBytes() {
    java.lang.Object ref = state_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      state_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int FIELDS_FIELD_NUMBER = 8;
  private com.google.protobuf.Struct fields_;
  /**
   * <pre>
   * 数据字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 8;</code>
   * @return Whether the fields field is set.
   */
  @java.lang.Override
  public boolean hasFields() {
    return fields_ != null;
  }
  /**
   * <pre>
   * 数据字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 8;</code>
   * @return The fields.
   */
  @java.lang.Override
  public com.google.protobuf.Struct getFields() {
    return fields_ == null ? com.google.protobuf.Struct.getDefaultInstance() : fields_;
  }
  /**
   * <pre>
   * 数据字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 8;</code>
   */
  @java.lang.Override
  public com.google.protobuf.StructOrBuilder getFieldsOrBuilder() {
    return getFields();
  }

  public static final int FIELDS_PENDING_FIELD_NUMBER = 9;
  private com.google.protobuf.Struct fieldsPending_;
  /**
   * <pre>
   *string fields = 8;
   * 被更新的字段内容(未被接受的更新)
   * string fields_pending = 9;
   * </pre>
   *
   * <code>.google.protobuf.Struct fields_pending = 9;</code>
   * @return Whether the fieldsPending field is set.
   */
  @java.lang.Override
  public boolean hasFieldsPending() {
    return fieldsPending_ != null;
  }
  /**
   * <pre>
   *string fields = 8;
   * 被更新的字段内容(未被接受的更新)
   * string fields_pending = 9;
   * </pre>
   *
   * <code>.google.protobuf.Struct fields_pending = 9;</code>
   * @return The fieldsPending.
   */
  @java.lang.Override
  public com.google.protobuf.Struct getFieldsPending() {
    return fieldsPending_ == null ? com.google.protobuf.Struct.getDefaultInstance() : fieldsPending_;
  }
  /**
   * <pre>
   *string fields = 8;
   * 被更新的字段内容(未被接受的更新)
   * string fields_pending = 9;
   * </pre>
   *
   * <code>.google.protobuf.Struct fields_pending = 9;</code>
   */
  @java.lang.Override
  public com.google.protobuf.StructOrBuilder getFieldsPendingOrBuilder() {
    return getFieldsPending();
  }

  public static final int RECORD_PENDING_FIELD_NUMBER = 10;
  private com.google.protobuf.Struct recordPending_;
  /**
   * <pre>
   * fields合并了fields_pending
   * string record_pending = 10;
   * </pre>
   *
   * <code>.google.protobuf.Struct record_pending = 10;</code>
   * @return Whether the recordPending field is set.
   */
  @java.lang.Override
  public boolean hasRecordPending() {
    return recordPending_ != null;
  }
  /**
   * <pre>
   * fields合并了fields_pending
   * string record_pending = 10;
   * </pre>
   *
   * <code>.google.protobuf.Struct record_pending = 10;</code>
   * @return The recordPending.
   */
  @java.lang.Override
  public com.google.protobuf.Struct getRecordPending() {
    return recordPending_ == null ? com.google.protobuf.Struct.getDefaultInstance() : recordPending_;
  }
  /**
   * <pre>
   * fields合并了fields_pending
   * string record_pending = 10;
   * </pre>
   *
   * <code>.google.protobuf.Struct record_pending = 10;</code>
   */
  @java.lang.Override
  public com.google.protobuf.StructOrBuilder getRecordPendingOrBuilder() {
    return getRecordPending();
  }

  public static final int PENDING_FIELD_NUMBER = 11;
  private boolean pending_;
  /**
   * <pre>
   * 是否有被更新字段
   * </pre>
   *
   * <code>bool pending = 11;</code>
   * @return The pending.
   */
  @java.lang.Override
  public boolean getPending() {
    return pending_;
  }

  public static final int CREATED_FIELD_NUMBER = 12;
  private volatile java.lang.Object created_;
  /**
   * <pre>
   * 创建时间
   * </pre>
   *
   * <code>string created = 12;</code>
   * @return The created.
   */
  @java.lang.Override
  public java.lang.String getCreated() {
    java.lang.Object ref = created_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      created_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 创建时间
   * </pre>
   *
   * <code>string created = 12;</code>
   * @return The bytes for created.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCreatedBytes() {
    java.lang.Object ref = created_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      created_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int UPDATED_FIELD_NUMBER = 13;
  private volatile java.lang.Object updated_;
  /**
   * <pre>
   * 最后一次修改时间
   * </pre>
   *
   * <code>string updated = 13;</code>
   * @return The updated.
   */
  @java.lang.Override
  public java.lang.String getUpdated() {
    java.lang.Object ref = updated_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      updated_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 最后一次修改时间
   * </pre>
   *
   * <code>string updated = 13;</code>
   * @return The bytes for updated.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUpdatedBytes() {
    java.lang.Object ref = updated_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      updated_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CREATED_BY_FIELD_NUMBER = 14;
  private volatile java.lang.Object createdBy_;
  /**
   * <pre>
   * 创建者
   * </pre>
   *
   * <code>string created_by = 14;</code>
   * @return The createdBy.
   */
  @java.lang.Override
  public java.lang.String getCreatedBy() {
    java.lang.Object ref = createdBy_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      createdBy_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 创建者
   * </pre>
   *
   * <code>string created_by = 14;</code>
   * @return The bytes for createdBy.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCreatedByBytes() {
    java.lang.Object ref = createdBy_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      createdBy_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int UPDATED_BY_FIELD_NUMBER = 15;
  private volatile java.lang.Object updatedBy_;
  /**
   * <pre>
   * 最后一次修改者
   * </pre>
   *
   * <code>string updated_by = 15;</code>
   * @return The updatedBy.
   */
  @java.lang.Override
  public java.lang.String getUpdatedBy() {
    java.lang.Object ref = updatedBy_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      updatedBy_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 最后一次修改者
   * </pre>
   *
   * <code>string updated_by = 15;</code>
   * @return The bytes for updatedBy.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUpdatedByBytes() {
    java.lang.Object ref = updatedBy_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      updatedBy_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PROCESS_STATUS_FIELD_NUMBER = 16;
  private volatile java.lang.Object processStatus_;
  /**
   * <pre>
   * 数据被批处理状态
   * </pre>
   *
   * <code>string process_status = 16;</code>
   * @return The processStatus.
   */
  @java.lang.Override
  public java.lang.String getProcessStatus() {
    java.lang.Object ref = processStatus_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      processStatus_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 数据被批处理状态
   * </pre>
   *
   * <code>string process_status = 16;</code>
   * @return The bytes for processStatus.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getProcessStatusBytes() {
    java.lang.Object ref = processStatus_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      processStatus_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PARENT_FIELD_NUMBER = 17;
  private cn.hexcloud.pbis.common.service.integration.metadata.Entity parent_;
  /**
   * <pre>
   * 父级节点
   * </pre>
   *
   * <code>.entity.Entity parent = 17;</code>
   * @return Whether the parent field is set.
   */
  @java.lang.Override
  public boolean hasParent() {
    return parent_ != null;
  }
  /**
   * <pre>
   * 父级节点
   * </pre>
   *
   * <code>.entity.Entity parent = 17;</code>
   * @return The parent.
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.metadata.Entity getParent() {
    return parent_ == null ? cn.hexcloud.pbis.common.service.integration.metadata.Entity.getDefaultInstance() : parent_;
  }
  /**
   * <pre>
   * 父级节点
   * </pre>
   *
   * <code>.entity.Entity parent = 17;</code>
   */
  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.metadata.EntityOrBuilder getParentOrBuilder() {
    return getParent();
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (id_ != 0L) {
      output.writeUInt64(1, id_);
    }
    if (partnerId_ != 0L) {
      output.writeUInt64(2, partnerId_);
    }
    if (scopeId_ != 0L) {
      output.writeUInt64(3, scopeId_);
    }
    if (parentId_ != 0L) {
      output.writeUInt64(4, parentId_);
    }
    if (schemaId_ != 0L) {
      output.writeUInt64(5, schemaId_);
    }
    if (!getSchemaNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, schemaName_);
    }
    if (!getStateBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, state_);
    }
    if (fields_ != null) {
      output.writeMessage(8, getFields());
    }
    if (fieldsPending_ != null) {
      output.writeMessage(9, getFieldsPending());
    }
    if (recordPending_ != null) {
      output.writeMessage(10, getRecordPending());
    }
    if (pending_ != false) {
      output.writeBool(11, pending_);
    }
    if (!getCreatedBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 12, created_);
    }
    if (!getUpdatedBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 13, updated_);
    }
    if (!getCreatedByBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 14, createdBy_);
    }
    if (!getUpdatedByBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 15, updatedBy_);
    }
    if (!getProcessStatusBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 16, processStatus_);
    }
    if (parent_ != null) {
      output.writeMessage(17, getParent());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (id_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, id_);
    }
    if (partnerId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(2, partnerId_);
    }
    if (scopeId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(3, scopeId_);
    }
    if (parentId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(4, parentId_);
    }
    if (schemaId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(5, schemaId_);
    }
    if (!getSchemaNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, schemaName_);
    }
    if (!getStateBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, state_);
    }
    if (fields_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, getFields());
    }
    if (fieldsPending_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(9, getFieldsPending());
    }
    if (recordPending_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(10, getRecordPending());
    }
    if (pending_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(11, pending_);
    }
    if (!getCreatedBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, created_);
    }
    if (!getUpdatedBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, updated_);
    }
    if (!getCreatedByBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, createdBy_);
    }
    if (!getUpdatedByBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, updatedBy_);
    }
    if (!getProcessStatusBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, processStatus_);
    }
    if (parent_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(17, getParent());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.metadata.Entity)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.metadata.Entity other = (cn.hexcloud.pbis.common.service.integration.metadata.Entity) obj;

    if (getId()
        != other.getId()) return false;
    if (getPartnerId()
        != other.getPartnerId()) return false;
    if (getScopeId()
        != other.getScopeId()) return false;
    if (getParentId()
        != other.getParentId()) return false;
    if (getSchemaId()
        != other.getSchemaId()) return false;
    if (!getSchemaName()
        .equals(other.getSchemaName())) return false;
    if (!getState()
        .equals(other.getState())) return false;
    if (hasFields() != other.hasFields()) return false;
    if (hasFields()) {
      if (!getFields()
          .equals(other.getFields())) return false;
    }
    if (hasFieldsPending() != other.hasFieldsPending()) return false;
    if (hasFieldsPending()) {
      if (!getFieldsPending()
          .equals(other.getFieldsPending())) return false;
    }
    if (hasRecordPending() != other.hasRecordPending()) return false;
    if (hasRecordPending()) {
      if (!getRecordPending()
          .equals(other.getRecordPending())) return false;
    }
    if (getPending()
        != other.getPending()) return false;
    if (!getCreated()
        .equals(other.getCreated())) return false;
    if (!getUpdated()
        .equals(other.getUpdated())) return false;
    if (!getCreatedBy()
        .equals(other.getCreatedBy())) return false;
    if (!getUpdatedBy()
        .equals(other.getUpdatedBy())) return false;
    if (!getProcessStatus()
        .equals(other.getProcessStatus())) return false;
    if (hasParent() != other.hasParent()) return false;
    if (hasParent()) {
      if (!getParent()
          .equals(other.getParent())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getId());
    hash = (37 * hash) + PARTNER_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getPartnerId());
    hash = (37 * hash) + SCOPE_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getScopeId());
    hash = (37 * hash) + PARENT_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getParentId());
    hash = (37 * hash) + SCHEMA_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getSchemaId());
    hash = (37 * hash) + SCHEMA_NAME_FIELD_NUMBER;
    hash = (53 * hash) + getSchemaName().hashCode();
    hash = (37 * hash) + STATE_FIELD_NUMBER;
    hash = (53 * hash) + getState().hashCode();
    if (hasFields()) {
      hash = (37 * hash) + FIELDS_FIELD_NUMBER;
      hash = (53 * hash) + getFields().hashCode();
    }
    if (hasFieldsPending()) {
      hash = (37 * hash) + FIELDS_PENDING_FIELD_NUMBER;
      hash = (53 * hash) + getFieldsPending().hashCode();
    }
    if (hasRecordPending()) {
      hash = (37 * hash) + RECORD_PENDING_FIELD_NUMBER;
      hash = (53 * hash) + getRecordPending().hashCode();
    }
    hash = (37 * hash) + PENDING_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getPending());
    hash = (37 * hash) + CREATED_FIELD_NUMBER;
    hash = (53 * hash) + getCreated().hashCode();
    hash = (37 * hash) + UPDATED_FIELD_NUMBER;
    hash = (53 * hash) + getUpdated().hashCode();
    hash = (37 * hash) + CREATED_BY_FIELD_NUMBER;
    hash = (53 * hash) + getCreatedBy().hashCode();
    hash = (37 * hash) + UPDATED_BY_FIELD_NUMBER;
    hash = (53 * hash) + getUpdatedBy().hashCode();
    hash = (37 * hash) + PROCESS_STATUS_FIELD_NUMBER;
    hash = (53 * hash) + getProcessStatus().hashCode();
    if (hasParent()) {
      hash = (37 * hash) + PARENT_FIELD_NUMBER;
      hash = (53 * hash) + getParent().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.Entity parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.Entity parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.Entity parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.Entity parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.Entity parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.Entity parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.Entity parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.Entity parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.Entity parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.Entity parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.Entity parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.Entity parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.metadata.Entity prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code entity.Entity}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:entity.Entity)
      cn.hexcloud.pbis.common.service.integration.metadata.EntityOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_Entity_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_Entity_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.metadata.Entity.class, cn.hexcloud.pbis.common.service.integration.metadata.Entity.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.metadata.Entity.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      id_ = 0L;

      partnerId_ = 0L;

      scopeId_ = 0L;

      parentId_ = 0L;

      schemaId_ = 0L;

      schemaName_ = "";

      state_ = "";

      if (fieldsBuilder_ == null) {
        fields_ = null;
      } else {
        fields_ = null;
        fieldsBuilder_ = null;
      }
      if (fieldsPendingBuilder_ == null) {
        fieldsPending_ = null;
      } else {
        fieldsPending_ = null;
        fieldsPendingBuilder_ = null;
      }
      if (recordPendingBuilder_ == null) {
        recordPending_ = null;
      } else {
        recordPending_ = null;
        recordPendingBuilder_ = null;
      }
      pending_ = false;

      created_ = "";

      updated_ = "";

      createdBy_ = "";

      updatedBy_ = "";

      processStatus_ = "";

      if (parentBuilder_ == null) {
        parent_ = null;
      } else {
        parent_ = null;
        parentBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_Entity_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.Entity getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Entity.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.Entity build() {
      cn.hexcloud.pbis.common.service.integration.metadata.Entity result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.Entity buildPartial() {
      cn.hexcloud.pbis.common.service.integration.metadata.Entity result = new cn.hexcloud.pbis.common.service.integration.metadata.Entity(this);
      result.id_ = id_;
      result.partnerId_ = partnerId_;
      result.scopeId_ = scopeId_;
      result.parentId_ = parentId_;
      result.schemaId_ = schemaId_;
      result.schemaName_ = schemaName_;
      result.state_ = state_;
      if (fieldsBuilder_ == null) {
        result.fields_ = fields_;
      } else {
        result.fields_ = fieldsBuilder_.build();
      }
      if (fieldsPendingBuilder_ == null) {
        result.fieldsPending_ = fieldsPending_;
      } else {
        result.fieldsPending_ = fieldsPendingBuilder_.build();
      }
      if (recordPendingBuilder_ == null) {
        result.recordPending_ = recordPending_;
      } else {
        result.recordPending_ = recordPendingBuilder_.build();
      }
      result.pending_ = pending_;
      result.created_ = created_;
      result.updated_ = updated_;
      result.createdBy_ = createdBy_;
      result.updatedBy_ = updatedBy_;
      result.processStatus_ = processStatus_;
      if (parentBuilder_ == null) {
        result.parent_ = parent_;
      } else {
        result.parent_ = parentBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.metadata.Entity) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.metadata.Entity)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.metadata.Entity other) {
      if (other == cn.hexcloud.pbis.common.service.integration.metadata.Entity.getDefaultInstance()) return this;
      if (other.getId() != 0L) {
        setId(other.getId());
      }
      if (other.getPartnerId() != 0L) {
        setPartnerId(other.getPartnerId());
      }
      if (other.getScopeId() != 0L) {
        setScopeId(other.getScopeId());
      }
      if (other.getParentId() != 0L) {
        setParentId(other.getParentId());
      }
      if (other.getSchemaId() != 0L) {
        setSchemaId(other.getSchemaId());
      }
      if (!other.getSchemaName().isEmpty()) {
        schemaName_ = other.schemaName_;
        onChanged();
      }
      if (!other.getState().isEmpty()) {
        state_ = other.state_;
        onChanged();
      }
      if (other.hasFields()) {
        mergeFields(other.getFields());
      }
      if (other.hasFieldsPending()) {
        mergeFieldsPending(other.getFieldsPending());
      }
      if (other.hasRecordPending()) {
        mergeRecordPending(other.getRecordPending());
      }
      if (other.getPending() != false) {
        setPending(other.getPending());
      }
      if (!other.getCreated().isEmpty()) {
        created_ = other.created_;
        onChanged();
      }
      if (!other.getUpdated().isEmpty()) {
        updated_ = other.updated_;
        onChanged();
      }
      if (!other.getCreatedBy().isEmpty()) {
        createdBy_ = other.createdBy_;
        onChanged();
      }
      if (!other.getUpdatedBy().isEmpty()) {
        updatedBy_ = other.updatedBy_;
        onChanged();
      }
      if (!other.getProcessStatus().isEmpty()) {
        processStatus_ = other.processStatus_;
        onChanged();
      }
      if (other.hasParent()) {
        mergeParent(other.getParent());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.metadata.Entity parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.metadata.Entity) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long id_ ;
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>uint64 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public long getId() {
      return id_;
    }
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>uint64 id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(long value) {
      
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>uint64 id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      
      id_ = 0L;
      onChanged();
      return this;
    }

    private long partnerId_ ;
    /**
     * <pre>
     * 商户id
     * </pre>
     *
     * <code>uint64 partner_id = 2;</code>
     * @return The partnerId.
     */
    @java.lang.Override
    public long getPartnerId() {
      return partnerId_;
    }
    /**
     * <pre>
     * 商户id
     * </pre>
     *
     * <code>uint64 partner_id = 2;</code>
     * @param value The partnerId to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerId(long value) {
      
      partnerId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 商户id
     * </pre>
     *
     * <code>uint64 partner_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearPartnerId() {
      
      partnerId_ = 0L;
      onChanged();
      return this;
    }

    private long scopeId_ ;
    /**
     * <pre>
     * scope_id
     * </pre>
     *
     * <code>uint64 scope_id = 3;</code>
     * @return The scopeId.
     */
    @java.lang.Override
    public long getScopeId() {
      return scopeId_;
    }
    /**
     * <pre>
     * scope_id
     * </pre>
     *
     * <code>uint64 scope_id = 3;</code>
     * @param value The scopeId to set.
     * @return This builder for chaining.
     */
    public Builder setScopeId(long value) {
      
      scopeId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * scope_id
     * </pre>
     *
     * <code>uint64 scope_id = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearScopeId() {
      
      scopeId_ = 0L;
      onChanged();
      return this;
    }

    private long parentId_ ;
    /**
     * <pre>
     * 父级id
     * </pre>
     *
     * <code>uint64 parent_id = 4;</code>
     * @return The parentId.
     */
    @java.lang.Override
    public long getParentId() {
      return parentId_;
    }
    /**
     * <pre>
     * 父级id
     * </pre>
     *
     * <code>uint64 parent_id = 4;</code>
     * @param value The parentId to set.
     * @return This builder for chaining.
     */
    public Builder setParentId(long value) {
      
      parentId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 父级id
     * </pre>
     *
     * <code>uint64 parent_id = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearParentId() {
      
      parentId_ = 0L;
      onChanged();
      return this;
    }

    private long schemaId_ ;
    /**
     * <pre>
     * schema id
     * </pre>
     *
     * <code>uint64 schema_id = 5;</code>
     * @return The schemaId.
     */
    @java.lang.Override
    public long getSchemaId() {
      return schemaId_;
    }
    /**
     * <pre>
     * schema id
     * </pre>
     *
     * <code>uint64 schema_id = 5;</code>
     * @param value The schemaId to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaId(long value) {
      
      schemaId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * schema id
     * </pre>
     *
     * <code>uint64 schema_id = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearSchemaId() {
      
      schemaId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object schemaName_ = "";
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 6;</code>
     * @return The schemaName.
     */
    public java.lang.String getSchemaName() {
      java.lang.Object ref = schemaName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        schemaName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 6;</code>
     * @return The bytes for schemaName.
     */
    public com.google.protobuf.ByteString
        getSchemaNameBytes() {
      java.lang.Object ref = schemaName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        schemaName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 6;</code>
     * @param value The schemaName to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      schemaName_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearSchemaName() {
      
      schemaName_ = getDefaultInstance().getSchemaName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 6;</code>
     * @param value The bytes for schemaName to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      schemaName_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object state_ = "";
    /**
     * <pre>
     * 数据状态
     * </pre>
     *
     * <code>string state = 7;</code>
     * @return The state.
     */
    public java.lang.String getState() {
      java.lang.Object ref = state_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        state_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 数据状态
     * </pre>
     *
     * <code>string state = 7;</code>
     * @return The bytes for state.
     */
    public com.google.protobuf.ByteString
        getStateBytes() {
      java.lang.Object ref = state_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        state_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 数据状态
     * </pre>
     *
     * <code>string state = 7;</code>
     * @param value The state to set.
     * @return This builder for chaining.
     */
    public Builder setState(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      state_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 数据状态
     * </pre>
     *
     * <code>string state = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearState() {
      
      state_ = getDefaultInstance().getState();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 数据状态
     * </pre>
     *
     * <code>string state = 7;</code>
     * @param value The bytes for state to set.
     * @return This builder for chaining.
     */
    public Builder setStateBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      state_ = value;
      onChanged();
      return this;
    }

    private com.google.protobuf.Struct fields_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> fieldsBuilder_;
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 8;</code>
     * @return Whether the fields field is set.
     */
    public boolean hasFields() {
      return fieldsBuilder_ != null || fields_ != null;
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 8;</code>
     * @return The fields.
     */
    public com.google.protobuf.Struct getFields() {
      if (fieldsBuilder_ == null) {
        return fields_ == null ? com.google.protobuf.Struct.getDefaultInstance() : fields_;
      } else {
        return fieldsBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 8;</code>
     */
    public Builder setFields(com.google.protobuf.Struct value) {
      if (fieldsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        fields_ = value;
        onChanged();
      } else {
        fieldsBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 8;</code>
     */
    public Builder setFields(
        com.google.protobuf.Struct.Builder builderForValue) {
      if (fieldsBuilder_ == null) {
        fields_ = builderForValue.build();
        onChanged();
      } else {
        fieldsBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 8;</code>
     */
    public Builder mergeFields(com.google.protobuf.Struct value) {
      if (fieldsBuilder_ == null) {
        if (fields_ != null) {
          fields_ =
            com.google.protobuf.Struct.newBuilder(fields_).mergeFrom(value).buildPartial();
        } else {
          fields_ = value;
        }
        onChanged();
      } else {
        fieldsBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 8;</code>
     */
    public Builder clearFields() {
      if (fieldsBuilder_ == null) {
        fields_ = null;
        onChanged();
      } else {
        fields_ = null;
        fieldsBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 8;</code>
     */
    public com.google.protobuf.Struct.Builder getFieldsBuilder() {
      
      onChanged();
      return getFieldsFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 8;</code>
     */
    public com.google.protobuf.StructOrBuilder getFieldsOrBuilder() {
      if (fieldsBuilder_ != null) {
        return fieldsBuilder_.getMessageOrBuilder();
      } else {
        return fields_ == null ?
            com.google.protobuf.Struct.getDefaultInstance() : fields_;
      }
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 8;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> 
        getFieldsFieldBuilder() {
      if (fieldsBuilder_ == null) {
        fieldsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder>(
                getFields(),
                getParentForChildren(),
                isClean());
        fields_ = null;
      }
      return fieldsBuilder_;
    }

    private com.google.protobuf.Struct fieldsPending_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> fieldsPendingBuilder_;
    /**
     * <pre>
     *string fields = 8;
     * 被更新的字段内容(未被接受的更新)
     * string fields_pending = 9;
     * </pre>
     *
     * <code>.google.protobuf.Struct fields_pending = 9;</code>
     * @return Whether the fieldsPending field is set.
     */
    public boolean hasFieldsPending() {
      return fieldsPendingBuilder_ != null || fieldsPending_ != null;
    }
    /**
     * <pre>
     *string fields = 8;
     * 被更新的字段内容(未被接受的更新)
     * string fields_pending = 9;
     * </pre>
     *
     * <code>.google.protobuf.Struct fields_pending = 9;</code>
     * @return The fieldsPending.
     */
    public com.google.protobuf.Struct getFieldsPending() {
      if (fieldsPendingBuilder_ == null) {
        return fieldsPending_ == null ? com.google.protobuf.Struct.getDefaultInstance() : fieldsPending_;
      } else {
        return fieldsPendingBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *string fields = 8;
     * 被更新的字段内容(未被接受的更新)
     * string fields_pending = 9;
     * </pre>
     *
     * <code>.google.protobuf.Struct fields_pending = 9;</code>
     */
    public Builder setFieldsPending(com.google.protobuf.Struct value) {
      if (fieldsPendingBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        fieldsPending_ = value;
        onChanged();
      } else {
        fieldsPendingBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *string fields = 8;
     * 被更新的字段内容(未被接受的更新)
     * string fields_pending = 9;
     * </pre>
     *
     * <code>.google.protobuf.Struct fields_pending = 9;</code>
     */
    public Builder setFieldsPending(
        com.google.protobuf.Struct.Builder builderForValue) {
      if (fieldsPendingBuilder_ == null) {
        fieldsPending_ = builderForValue.build();
        onChanged();
      } else {
        fieldsPendingBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *string fields = 8;
     * 被更新的字段内容(未被接受的更新)
     * string fields_pending = 9;
     * </pre>
     *
     * <code>.google.protobuf.Struct fields_pending = 9;</code>
     */
    public Builder mergeFieldsPending(com.google.protobuf.Struct value) {
      if (fieldsPendingBuilder_ == null) {
        if (fieldsPending_ != null) {
          fieldsPending_ =
            com.google.protobuf.Struct.newBuilder(fieldsPending_).mergeFrom(value).buildPartial();
        } else {
          fieldsPending_ = value;
        }
        onChanged();
      } else {
        fieldsPendingBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *string fields = 8;
     * 被更新的字段内容(未被接受的更新)
     * string fields_pending = 9;
     * </pre>
     *
     * <code>.google.protobuf.Struct fields_pending = 9;</code>
     */
    public Builder clearFieldsPending() {
      if (fieldsPendingBuilder_ == null) {
        fieldsPending_ = null;
        onChanged();
      } else {
        fieldsPending_ = null;
        fieldsPendingBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *string fields = 8;
     * 被更新的字段内容(未被接受的更新)
     * string fields_pending = 9;
     * </pre>
     *
     * <code>.google.protobuf.Struct fields_pending = 9;</code>
     */
    public com.google.protobuf.Struct.Builder getFieldsPendingBuilder() {
      
      onChanged();
      return getFieldsPendingFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *string fields = 8;
     * 被更新的字段内容(未被接受的更新)
     * string fields_pending = 9;
     * </pre>
     *
     * <code>.google.protobuf.Struct fields_pending = 9;</code>
     */
    public com.google.protobuf.StructOrBuilder getFieldsPendingOrBuilder() {
      if (fieldsPendingBuilder_ != null) {
        return fieldsPendingBuilder_.getMessageOrBuilder();
      } else {
        return fieldsPending_ == null ?
            com.google.protobuf.Struct.getDefaultInstance() : fieldsPending_;
      }
    }
    /**
     * <pre>
     *string fields = 8;
     * 被更新的字段内容(未被接受的更新)
     * string fields_pending = 9;
     * </pre>
     *
     * <code>.google.protobuf.Struct fields_pending = 9;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> 
        getFieldsPendingFieldBuilder() {
      if (fieldsPendingBuilder_ == null) {
        fieldsPendingBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder>(
                getFieldsPending(),
                getParentForChildren(),
                isClean());
        fieldsPending_ = null;
      }
      return fieldsPendingBuilder_;
    }

    private com.google.protobuf.Struct recordPending_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> recordPendingBuilder_;
    /**
     * <pre>
     * fields合并了fields_pending
     * string record_pending = 10;
     * </pre>
     *
     * <code>.google.protobuf.Struct record_pending = 10;</code>
     * @return Whether the recordPending field is set.
     */
    public boolean hasRecordPending() {
      return recordPendingBuilder_ != null || recordPending_ != null;
    }
    /**
     * <pre>
     * fields合并了fields_pending
     * string record_pending = 10;
     * </pre>
     *
     * <code>.google.protobuf.Struct record_pending = 10;</code>
     * @return The recordPending.
     */
    public com.google.protobuf.Struct getRecordPending() {
      if (recordPendingBuilder_ == null) {
        return recordPending_ == null ? com.google.protobuf.Struct.getDefaultInstance() : recordPending_;
      } else {
        return recordPendingBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * fields合并了fields_pending
     * string record_pending = 10;
     * </pre>
     *
     * <code>.google.protobuf.Struct record_pending = 10;</code>
     */
    public Builder setRecordPending(com.google.protobuf.Struct value) {
      if (recordPendingBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        recordPending_ = value;
        onChanged();
      } else {
        recordPendingBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * fields合并了fields_pending
     * string record_pending = 10;
     * </pre>
     *
     * <code>.google.protobuf.Struct record_pending = 10;</code>
     */
    public Builder setRecordPending(
        com.google.protobuf.Struct.Builder builderForValue) {
      if (recordPendingBuilder_ == null) {
        recordPending_ = builderForValue.build();
        onChanged();
      } else {
        recordPendingBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * fields合并了fields_pending
     * string record_pending = 10;
     * </pre>
     *
     * <code>.google.protobuf.Struct record_pending = 10;</code>
     */
    public Builder mergeRecordPending(com.google.protobuf.Struct value) {
      if (recordPendingBuilder_ == null) {
        if (recordPending_ != null) {
          recordPending_ =
            com.google.protobuf.Struct.newBuilder(recordPending_).mergeFrom(value).buildPartial();
        } else {
          recordPending_ = value;
        }
        onChanged();
      } else {
        recordPendingBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * fields合并了fields_pending
     * string record_pending = 10;
     * </pre>
     *
     * <code>.google.protobuf.Struct record_pending = 10;</code>
     */
    public Builder clearRecordPending() {
      if (recordPendingBuilder_ == null) {
        recordPending_ = null;
        onChanged();
      } else {
        recordPending_ = null;
        recordPendingBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * fields合并了fields_pending
     * string record_pending = 10;
     * </pre>
     *
     * <code>.google.protobuf.Struct record_pending = 10;</code>
     */
    public com.google.protobuf.Struct.Builder getRecordPendingBuilder() {
      
      onChanged();
      return getRecordPendingFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * fields合并了fields_pending
     * string record_pending = 10;
     * </pre>
     *
     * <code>.google.protobuf.Struct record_pending = 10;</code>
     */
    public com.google.protobuf.StructOrBuilder getRecordPendingOrBuilder() {
      if (recordPendingBuilder_ != null) {
        return recordPendingBuilder_.getMessageOrBuilder();
      } else {
        return recordPending_ == null ?
            com.google.protobuf.Struct.getDefaultInstance() : recordPending_;
      }
    }
    /**
     * <pre>
     * fields合并了fields_pending
     * string record_pending = 10;
     * </pre>
     *
     * <code>.google.protobuf.Struct record_pending = 10;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> 
        getRecordPendingFieldBuilder() {
      if (recordPendingBuilder_ == null) {
        recordPendingBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder>(
                getRecordPending(),
                getParentForChildren(),
                isClean());
        recordPending_ = null;
      }
      return recordPendingBuilder_;
    }

    private boolean pending_ ;
    /**
     * <pre>
     * 是否有被更新字段
     * </pre>
     *
     * <code>bool pending = 11;</code>
     * @return The pending.
     */
    @java.lang.Override
    public boolean getPending() {
      return pending_;
    }
    /**
     * <pre>
     * 是否有被更新字段
     * </pre>
     *
     * <code>bool pending = 11;</code>
     * @param value The pending to set.
     * @return This builder for chaining.
     */
    public Builder setPending(boolean value) {
      
      pending_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 是否有被更新字段
     * </pre>
     *
     * <code>bool pending = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearPending() {
      
      pending_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object created_ = "";
    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>string created = 12;</code>
     * @return The created.
     */
    public java.lang.String getCreated() {
      java.lang.Object ref = created_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        created_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>string created = 12;</code>
     * @return The bytes for created.
     */
    public com.google.protobuf.ByteString
        getCreatedBytes() {
      java.lang.Object ref = created_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        created_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>string created = 12;</code>
     * @param value The created to set.
     * @return This builder for chaining.
     */
    public Builder setCreated(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      created_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>string created = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearCreated() {
      
      created_ = getDefaultInstance().getCreated();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>string created = 12;</code>
     * @param value The bytes for created to set.
     * @return This builder for chaining.
     */
    public Builder setCreatedBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      created_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object updated_ = "";
    /**
     * <pre>
     * 最后一次修改时间
     * </pre>
     *
     * <code>string updated = 13;</code>
     * @return The updated.
     */
    public java.lang.String getUpdated() {
      java.lang.Object ref = updated_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        updated_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 最后一次修改时间
     * </pre>
     *
     * <code>string updated = 13;</code>
     * @return The bytes for updated.
     */
    public com.google.protobuf.ByteString
        getUpdatedBytes() {
      java.lang.Object ref = updated_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        updated_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 最后一次修改时间
     * </pre>
     *
     * <code>string updated = 13;</code>
     * @param value The updated to set.
     * @return This builder for chaining.
     */
    public Builder setUpdated(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      updated_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 最后一次修改时间
     * </pre>
     *
     * <code>string updated = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearUpdated() {
      
      updated_ = getDefaultInstance().getUpdated();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 最后一次修改时间
     * </pre>
     *
     * <code>string updated = 13;</code>
     * @param value The bytes for updated to set.
     * @return This builder for chaining.
     */
    public Builder setUpdatedBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      updated_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object createdBy_ = "";
    /**
     * <pre>
     * 创建者
     * </pre>
     *
     * <code>string created_by = 14;</code>
     * @return The createdBy.
     */
    public java.lang.String getCreatedBy() {
      java.lang.Object ref = createdBy_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        createdBy_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 创建者
     * </pre>
     *
     * <code>string created_by = 14;</code>
     * @return The bytes for createdBy.
     */
    public com.google.protobuf.ByteString
        getCreatedByBytes() {
      java.lang.Object ref = createdBy_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        createdBy_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 创建者
     * </pre>
     *
     * <code>string created_by = 14;</code>
     * @param value The createdBy to set.
     * @return This builder for chaining.
     */
    public Builder setCreatedBy(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      createdBy_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 创建者
     * </pre>
     *
     * <code>string created_by = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearCreatedBy() {
      
      createdBy_ = getDefaultInstance().getCreatedBy();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 创建者
     * </pre>
     *
     * <code>string created_by = 14;</code>
     * @param value The bytes for createdBy to set.
     * @return This builder for chaining.
     */
    public Builder setCreatedByBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      createdBy_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object updatedBy_ = "";
    /**
     * <pre>
     * 最后一次修改者
     * </pre>
     *
     * <code>string updated_by = 15;</code>
     * @return The updatedBy.
     */
    public java.lang.String getUpdatedBy() {
      java.lang.Object ref = updatedBy_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        updatedBy_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 最后一次修改者
     * </pre>
     *
     * <code>string updated_by = 15;</code>
     * @return The bytes for updatedBy.
     */
    public com.google.protobuf.ByteString
        getUpdatedByBytes() {
      java.lang.Object ref = updatedBy_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        updatedBy_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 最后一次修改者
     * </pre>
     *
     * <code>string updated_by = 15;</code>
     * @param value The updatedBy to set.
     * @return This builder for chaining.
     */
    public Builder setUpdatedBy(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      updatedBy_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 最后一次修改者
     * </pre>
     *
     * <code>string updated_by = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearUpdatedBy() {
      
      updatedBy_ = getDefaultInstance().getUpdatedBy();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 最后一次修改者
     * </pre>
     *
     * <code>string updated_by = 15;</code>
     * @param value The bytes for updatedBy to set.
     * @return This builder for chaining.
     */
    public Builder setUpdatedByBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      updatedBy_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object processStatus_ = "";
    /**
     * <pre>
     * 数据被批处理状态
     * </pre>
     *
     * <code>string process_status = 16;</code>
     * @return The processStatus.
     */
    public java.lang.String getProcessStatus() {
      java.lang.Object ref = processStatus_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        processStatus_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 数据被批处理状态
     * </pre>
     *
     * <code>string process_status = 16;</code>
     * @return The bytes for processStatus.
     */
    public com.google.protobuf.ByteString
        getProcessStatusBytes() {
      java.lang.Object ref = processStatus_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        processStatus_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 数据被批处理状态
     * </pre>
     *
     * <code>string process_status = 16;</code>
     * @param value The processStatus to set.
     * @return This builder for chaining.
     */
    public Builder setProcessStatus(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      processStatus_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 数据被批处理状态
     * </pre>
     *
     * <code>string process_status = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearProcessStatus() {
      
      processStatus_ = getDefaultInstance().getProcessStatus();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 数据被批处理状态
     * </pre>
     *
     * <code>string process_status = 16;</code>
     * @param value The bytes for processStatus to set.
     * @return This builder for chaining.
     */
    public Builder setProcessStatusBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      processStatus_ = value;
      onChanged();
      return this;
    }

    private cn.hexcloud.pbis.common.service.integration.metadata.Entity parent_;
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.integration.metadata.Entity, cn.hexcloud.pbis.common.service.integration.metadata.Entity.Builder, cn.hexcloud.pbis.common.service.integration.metadata.EntityOrBuilder> parentBuilder_;
    /**
     * <pre>
     * 父级节点
     * </pre>
     *
     * <code>.entity.Entity parent = 17;</code>
     * @return Whether the parent field is set.
     */
    public boolean hasParent() {
      return parentBuilder_ != null || parent_ != null;
    }
    /**
     * <pre>
     * 父级节点
     * </pre>
     *
     * <code>.entity.Entity parent = 17;</code>
     * @return The parent.
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.Entity getParent() {
      if (parentBuilder_ == null) {
        return parent_ == null ? cn.hexcloud.pbis.common.service.integration.metadata.Entity.getDefaultInstance() : parent_;
      } else {
        return parentBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 父级节点
     * </pre>
     *
     * <code>.entity.Entity parent = 17;</code>
     */
    public Builder setParent(cn.hexcloud.pbis.common.service.integration.metadata.Entity value) {
      if (parentBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        parent_ = value;
        onChanged();
      } else {
        parentBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 父级节点
     * </pre>
     *
     * <code>.entity.Entity parent = 17;</code>
     */
    public Builder setParent(
        cn.hexcloud.pbis.common.service.integration.metadata.Entity.Builder builderForValue) {
      if (parentBuilder_ == null) {
        parent_ = builderForValue.build();
        onChanged();
      } else {
        parentBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 父级节点
     * </pre>
     *
     * <code>.entity.Entity parent = 17;</code>
     */
    public Builder mergeParent(cn.hexcloud.pbis.common.service.integration.metadata.Entity value) {
      if (parentBuilder_ == null) {
        if (parent_ != null) {
          parent_ =
            cn.hexcloud.pbis.common.service.integration.metadata.Entity.newBuilder(parent_).mergeFrom(value).buildPartial();
        } else {
          parent_ = value;
        }
        onChanged();
      } else {
        parentBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 父级节点
     * </pre>
     *
     * <code>.entity.Entity parent = 17;</code>
     */
    public Builder clearParent() {
      if (parentBuilder_ == null) {
        parent_ = null;
        onChanged();
      } else {
        parent_ = null;
        parentBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 父级节点
     * </pre>
     *
     * <code>.entity.Entity parent = 17;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.Entity.Builder getParentBuilder() {
      
      onChanged();
      return getParentFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 父级节点
     * </pre>
     *
     * <code>.entity.Entity parent = 17;</code>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.EntityOrBuilder getParentOrBuilder() {
      if (parentBuilder_ != null) {
        return parentBuilder_.getMessageOrBuilder();
      } else {
        return parent_ == null ?
            cn.hexcloud.pbis.common.service.integration.metadata.Entity.getDefaultInstance() : parent_;
      }
    }
    /**
     * <pre>
     * 父级节点
     * </pre>
     *
     * <code>.entity.Entity parent = 17;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        cn.hexcloud.pbis.common.service.integration.metadata.Entity, cn.hexcloud.pbis.common.service.integration.metadata.Entity.Builder, cn.hexcloud.pbis.common.service.integration.metadata.EntityOrBuilder> 
        getParentFieldBuilder() {
      if (parentBuilder_ == null) {
        parentBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            cn.hexcloud.pbis.common.service.integration.metadata.Entity, cn.hexcloud.pbis.common.service.integration.metadata.Entity.Builder, cn.hexcloud.pbis.common.service.integration.metadata.EntityOrBuilder>(
                getParent(),
                getParentForChildren(),
                isClean());
        parent_ = null;
      }
      return parentBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:entity.Entity)
  }

  // @@protoc_insertion_point(class_scope:entity.Entity)
  private static final cn.hexcloud.pbis.common.service.integration.metadata.Entity DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.metadata.Entity();
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.Entity getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Entity>
      PARSER = new com.google.protobuf.AbstractParser<Entity>() {
    @java.lang.Override
    public Entity parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Entity(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Entity> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<Entity> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.metadata.Entity getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

