// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface EntityOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.Entity)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>uint64 id = 1;</code>
   * @return The id.
   */
  long getId();

  /**
   * <pre>
   * 商户id
   * </pre>
   *
   * <code>uint64 partner_id = 2;</code>
   * @return The partnerId.
   */
  long getPartnerId();

  /**
   * <pre>
   * scope_id
   * </pre>
   *
   * <code>uint64 scope_id = 3;</code>
   * @return The scopeId.
   */
  long getScopeId();

  /**
   * <pre>
   * 父级id
   * </pre>
   *
   * <code>uint64 parent_id = 4;</code>
   * @return The parentId.
   */
  long getParentId();

  /**
   * <pre>
   * schema id
   * </pre>
   *
   * <code>uint64 schema_id = 5;</code>
   * @return The schemaId.
   */
  long getSchemaId();

  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 6;</code>
   * @return The schemaName.
   */
  java.lang.String getSchemaName();
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 6;</code>
   * @return The bytes for schemaName.
   */
  com.google.protobuf.ByteString
      getSchemaNameBytes();

  /**
   * <pre>
   * 数据状态
   * </pre>
   *
   * <code>string state = 7;</code>
   * @return The state.
   */
  java.lang.String getState();
  /**
   * <pre>
   * 数据状态
   * </pre>
   *
   * <code>string state = 7;</code>
   * @return The bytes for state.
   */
  com.google.protobuf.ByteString
      getStateBytes();

  /**
   * <pre>
   * 数据字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 8;</code>
   * @return Whether the fields field is set.
   */
  boolean hasFields();
  /**
   * <pre>
   * 数据字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 8;</code>
   * @return The fields.
   */
  com.google.protobuf.Struct getFields();
  /**
   * <pre>
   * 数据字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 8;</code>
   */
  com.google.protobuf.StructOrBuilder getFieldsOrBuilder();

  /**
   * <pre>
   *string fields = 8;
   * 被更新的字段内容(未被接受的更新)
   * string fields_pending = 9;
   * </pre>
   *
   * <code>.google.protobuf.Struct fields_pending = 9;</code>
   * @return Whether the fieldsPending field is set.
   */
  boolean hasFieldsPending();
  /**
   * <pre>
   *string fields = 8;
   * 被更新的字段内容(未被接受的更新)
   * string fields_pending = 9;
   * </pre>
   *
   * <code>.google.protobuf.Struct fields_pending = 9;</code>
   * @return The fieldsPending.
   */
  com.google.protobuf.Struct getFieldsPending();
  /**
   * <pre>
   *string fields = 8;
   * 被更新的字段内容(未被接受的更新)
   * string fields_pending = 9;
   * </pre>
   *
   * <code>.google.protobuf.Struct fields_pending = 9;</code>
   */
  com.google.protobuf.StructOrBuilder getFieldsPendingOrBuilder();

  /**
   * <pre>
   * fields合并了fields_pending
   * string record_pending = 10;
   * </pre>
   *
   * <code>.google.protobuf.Struct record_pending = 10;</code>
   * @return Whether the recordPending field is set.
   */
  boolean hasRecordPending();
  /**
   * <pre>
   * fields合并了fields_pending
   * string record_pending = 10;
   * </pre>
   *
   * <code>.google.protobuf.Struct record_pending = 10;</code>
   * @return The recordPending.
   */
  com.google.protobuf.Struct getRecordPending();
  /**
   * <pre>
   * fields合并了fields_pending
   * string record_pending = 10;
   * </pre>
   *
   * <code>.google.protobuf.Struct record_pending = 10;</code>
   */
  com.google.protobuf.StructOrBuilder getRecordPendingOrBuilder();

  /**
   * <pre>
   * 是否有被更新字段
   * </pre>
   *
   * <code>bool pending = 11;</code>
   * @return The pending.
   */
  boolean getPending();

  /**
   * <pre>
   * 创建时间
   * </pre>
   *
   * <code>string created = 12;</code>
   * @return The created.
   */
  java.lang.String getCreated();
  /**
   * <pre>
   * 创建时间
   * </pre>
   *
   * <code>string created = 12;</code>
   * @return The bytes for created.
   */
  com.google.protobuf.ByteString
      getCreatedBytes();

  /**
   * <pre>
   * 最后一次修改时间
   * </pre>
   *
   * <code>string updated = 13;</code>
   * @return The updated.
   */
  java.lang.String getUpdated();
  /**
   * <pre>
   * 最后一次修改时间
   * </pre>
   *
   * <code>string updated = 13;</code>
   * @return The bytes for updated.
   */
  com.google.protobuf.ByteString
      getUpdatedBytes();

  /**
   * <pre>
   * 创建者
   * </pre>
   *
   * <code>string created_by = 14;</code>
   * @return The createdBy.
   */
  java.lang.String getCreatedBy();
  /**
   * <pre>
   * 创建者
   * </pre>
   *
   * <code>string created_by = 14;</code>
   * @return The bytes for createdBy.
   */
  com.google.protobuf.ByteString
      getCreatedByBytes();

  /**
   * <pre>
   * 最后一次修改者
   * </pre>
   *
   * <code>string updated_by = 15;</code>
   * @return The updatedBy.
   */
  java.lang.String getUpdatedBy();
  /**
   * <pre>
   * 最后一次修改者
   * </pre>
   *
   * <code>string updated_by = 15;</code>
   * @return The bytes for updatedBy.
   */
  com.google.protobuf.ByteString
      getUpdatedByBytes();

  /**
   * <pre>
   * 数据被批处理状态
   * </pre>
   *
   * <code>string process_status = 16;</code>
   * @return The processStatus.
   */
  java.lang.String getProcessStatus();
  /**
   * <pre>
   * 数据被批处理状态
   * </pre>
   *
   * <code>string process_status = 16;</code>
   * @return The bytes for processStatus.
   */
  com.google.protobuf.ByteString
      getProcessStatusBytes();

  /**
   * <pre>
   * 父级节点
   * </pre>
   *
   * <code>.entity.Entity parent = 17;</code>
   * @return Whether the parent field is set.
   */
  boolean hasParent();
  /**
   * <pre>
   * 父级节点
   * </pre>
   *
   * <code>.entity.Entity parent = 17;</code>
   * @return The parent.
   */
  cn.hexcloud.pbis.common.service.integration.metadata.Entity getParent();
  /**
   * <pre>
   * 父级节点
   * </pre>
   *
   * <code>.entity.Entity parent = 17;</code>
   */
  cn.hexcloud.pbis.common.service.integration.metadata.EntityOrBuilder getParentOrBuilder();
}
