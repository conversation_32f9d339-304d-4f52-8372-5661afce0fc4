package cn.hexcloud.pbis.common.service.integration.metadata;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 * <pre>
 * EntityService 用于维护主档Entity数据
 * </pre>
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.40.1)",
    comments = "Source: Metadata.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class EntityServiceGrpc {

  private EntityServiceGrpc() {}

  public static final String SERVICE_NAME = "entity.EntityService";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.Entity> getAddEntityMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "AddEntity",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.Entity.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.Entity> getAddEntityMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest, cn.hexcloud.pbis.common.service.integration.metadata.Entity> getAddEntityMethod;
    if ((getAddEntityMethod = EntityServiceGrpc.getAddEntityMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getAddEntityMethod = EntityServiceGrpc.getAddEntityMethod) == null) {
          EntityServiceGrpc.getAddEntityMethod = getAddEntityMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest, cn.hexcloud.pbis.common.service.integration.metadata.Entity>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "AddEntity"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.Entity.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("AddEntity"))
              .build();
        }
      }
    }
    return getAddEntityMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.Entity> getUpdateEntityMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "UpdateEntity",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.Entity.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.Entity> getUpdateEntityMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest, cn.hexcloud.pbis.common.service.integration.metadata.Entity> getUpdateEntityMethod;
    if ((getUpdateEntityMethod = EntityServiceGrpc.getUpdateEntityMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getUpdateEntityMethod = EntityServiceGrpc.getUpdateEntityMethod) == null) {
          EntityServiceGrpc.getUpdateEntityMethod = getUpdateEntityMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest, cn.hexcloud.pbis.common.service.integration.metadata.Entity>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "UpdateEntity"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.Entity.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("UpdateEntity"))
              .build();
        }
      }
    }
    return getUpdateEntityMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.SyncEntityRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.Entity> getSyncUpdateEntityMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SyncUpdateEntity",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.SyncEntityRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.Entity.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.SyncEntityRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.Entity> getSyncUpdateEntityMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.SyncEntityRequest, cn.hexcloud.pbis.common.service.integration.metadata.Entity> getSyncUpdateEntityMethod;
    if ((getSyncUpdateEntityMethod = EntityServiceGrpc.getSyncUpdateEntityMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getSyncUpdateEntityMethod = EntityServiceGrpc.getSyncUpdateEntityMethod) == null) {
          EntityServiceGrpc.getSyncUpdateEntityMethod = getSyncUpdateEntityMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.SyncEntityRequest, cn.hexcloud.pbis.common.service.integration.metadata.Entity>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SyncUpdateEntity"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.SyncEntityRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.Entity.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("SyncUpdateEntity"))
              .build();
        }
      }
    }
    return getSyncUpdateEntityMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.Entity> getGetEntityByIdMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "GetEntityById",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.Entity.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.Entity> getGetEntityByIdMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest, cn.hexcloud.pbis.common.service.integration.metadata.Entity> getGetEntityByIdMethod;
    if ((getGetEntityByIdMethod = EntityServiceGrpc.getGetEntityByIdMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getGetEntityByIdMethod = EntityServiceGrpc.getGetEntityByIdMethod) == null) {
          EntityServiceGrpc.getGetEntityByIdMethod = getGetEntityByIdMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest, cn.hexcloud.pbis.common.service.integration.metadata.Entity>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetEntityById"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.Entity.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("GetEntityById"))
              .build();
        }
      }
    }
    return getGetEntityByIdMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse> getListEntityMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "ListEntity",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse> getListEntityMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest, cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse> getListEntityMethod;
    if ((getListEntityMethod = EntityServiceGrpc.getListEntityMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getListEntityMethod = EntityServiceGrpc.getListEntityMethod) == null) {
          EntityServiceGrpc.getListEntityMethod = getListEntityMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest, cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "ListEntity"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("ListEntity"))
              .build();
        }
      }
    }
    return getListEntityMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityStateRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.Entity> getUpdateEntityStateMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "UpdateEntityState",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityStateRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.Entity.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityStateRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.Entity> getUpdateEntityStateMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityStateRequest, cn.hexcloud.pbis.common.service.integration.metadata.Entity> getUpdateEntityStateMethod;
    if ((getUpdateEntityStateMethod = EntityServiceGrpc.getUpdateEntityStateMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getUpdateEntityStateMethod = EntityServiceGrpc.getUpdateEntityStateMethod) == null) {
          EntityServiceGrpc.getUpdateEntityStateMethod = getUpdateEntityStateMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityStateRequest, cn.hexcloud.pbis.common.service.integration.metadata.Entity>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "UpdateEntityState"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityStateRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.Entity.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("UpdateEntityState"))
              .build();
        }
      }
    }
    return getUpdateEntityStateMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.ProcessEntityPendingChangesRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.Entity> getProcessEntityPendingChangesMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "ProcessEntityPendingChanges",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.ProcessEntityPendingChangesRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.Entity.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.ProcessEntityPendingChangesRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.Entity> getProcessEntityPendingChangesMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.ProcessEntityPendingChangesRequest, cn.hexcloud.pbis.common.service.integration.metadata.Entity> getProcessEntityPendingChangesMethod;
    if ((getProcessEntityPendingChangesMethod = EntityServiceGrpc.getProcessEntityPendingChangesMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getProcessEntityPendingChangesMethod = EntityServiceGrpc.getProcessEntityPendingChangesMethod) == null) {
          EntityServiceGrpc.getProcessEntityPendingChangesMethod = getProcessEntityPendingChangesMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.ProcessEntityPendingChangesRequest, cn.hexcloud.pbis.common.service.integration.metadata.Entity>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "ProcessEntityPendingChanges"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.ProcessEntityPendingChangesRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.Entity.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("ProcessEntityPendingChanges"))
              .build();
        }
      }
    }
    return getProcessEntityPendingChangesMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getCreateEntityTaskFromPendingChangesMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "CreateEntityTaskFromPendingChanges",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getCreateEntityTaskFromPendingChangesMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest, cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getCreateEntityTaskFromPendingChangesMethod;
    if ((getCreateEntityTaskFromPendingChangesMethod = EntityServiceGrpc.getCreateEntityTaskFromPendingChangesMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getCreateEntityTaskFromPendingChangesMethod = EntityServiceGrpc.getCreateEntityTaskFromPendingChangesMethod) == null) {
          EntityServiceGrpc.getCreateEntityTaskFromPendingChangesMethod = getCreateEntityTaskFromPendingChangesMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest, cn.hexcloud.pbis.common.service.integration.metadata.EntityTask>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "CreateEntityTaskFromPendingChanges"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("CreateEntityTaskFromPendingChanges"))
              .build();
        }
      }
    }
    return getCreateEntityTaskFromPendingChangesMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getUpdateEntityTaskMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "UpdateEntityTask",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getUpdateEntityTaskMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskRequest, cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getUpdateEntityTaskMethod;
    if ((getUpdateEntityTaskMethod = EntityServiceGrpc.getUpdateEntityTaskMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getUpdateEntityTaskMethod = EntityServiceGrpc.getUpdateEntityTaskMethod) == null) {
          EntityServiceGrpc.getUpdateEntityTaskMethod = getUpdateEntityTaskMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskRequest, cn.hexcloud.pbis.common.service.integration.metadata.EntityTask>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "UpdateEntityTask"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("UpdateEntityTask"))
              .build();
        }
      }
    }
    return getUpdateEntityTaskMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskStatusRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getUpdateEntityTaskStatusMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "UpdateEntityTaskStatus",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskStatusRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskStatusRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getUpdateEntityTaskStatusMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskStatusRequest, cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getUpdateEntityTaskStatusMethod;
    if ((getUpdateEntityTaskStatusMethod = EntityServiceGrpc.getUpdateEntityTaskStatusMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getUpdateEntityTaskStatusMethod = EntityServiceGrpc.getUpdateEntityTaskStatusMethod) == null) {
          EntityServiceGrpc.getUpdateEntityTaskStatusMethod = getUpdateEntityTaskStatusMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskStatusRequest, cn.hexcloud.pbis.common.service.integration.metadata.EntityTask>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "UpdateEntityTaskStatus"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskStatusRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("UpdateEntityTaskStatus"))
              .build();
        }
      }
    }
    return getUpdateEntityTaskStatusMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.DeleteEntityTaskRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getDeleteEntityTaskMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "DeleteEntityTask",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.DeleteEntityTaskRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.DeleteEntityTaskRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getDeleteEntityTaskMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.DeleteEntityTaskRequest, cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getDeleteEntityTaskMethod;
    if ((getDeleteEntityTaskMethod = EntityServiceGrpc.getDeleteEntityTaskMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getDeleteEntityTaskMethod = EntityServiceGrpc.getDeleteEntityTaskMethod) == null) {
          EntityServiceGrpc.getDeleteEntityTaskMethod = getDeleteEntityTaskMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.DeleteEntityTaskRequest, cn.hexcloud.pbis.common.service.integration.metadata.EntityTask>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "DeleteEntityTask"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.DeleteEntityTaskRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("DeleteEntityTask"))
              .build();
        }
      }
    }
    return getDeleteEntityTaskMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.GetEntityTaskByIdRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getGetEntityTaskByIdMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "GetEntityTaskById",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.GetEntityTaskByIdRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.GetEntityTaskByIdRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getGetEntityTaskByIdMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.GetEntityTaskByIdRequest, cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getGetEntityTaskByIdMethod;
    if ((getGetEntityTaskByIdMethod = EntityServiceGrpc.getGetEntityTaskByIdMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getGetEntityTaskByIdMethod = EntityServiceGrpc.getGetEntityTaskByIdMethod) == null) {
          EntityServiceGrpc.getGetEntityTaskByIdMethod = getGetEntityTaskByIdMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.GetEntityTaskByIdRequest, cn.hexcloud.pbis.common.service.integration.metadata.EntityTask>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetEntityTaskById"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.GetEntityTaskByIdRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("GetEntityTaskById"))
              .build();
        }
      }
    }
    return getGetEntityTaskByIdMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskResponse> getListEntityTaskMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "ListEntityTask",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskResponse> getListEntityTaskMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest, cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskResponse> getListEntityTaskMethod;
    if ((getListEntityTaskMethod = EntityServiceGrpc.getListEntityTaskMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getListEntityTaskMethod = EntityServiceGrpc.getListEntityTaskMethod) == null) {
          EntityServiceGrpc.getListEntityTaskMethod = getListEntityTaskMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest, cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "ListEntityTask"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskResponse.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("ListEntityTask"))
              .build();
        }
      }
    }
    return getListEntityTaskMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse> getGetChildrenEntityIdsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "GetChildrenEntityIds",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse> getGetChildrenEntityIdsMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsRequest, cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse> getGetChildrenEntityIdsMethod;
    if ((getGetChildrenEntityIdsMethod = EntityServiceGrpc.getGetChildrenEntityIdsMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getGetChildrenEntityIdsMethod = EntityServiceGrpc.getGetChildrenEntityIdsMethod) == null) {
          EntityServiceGrpc.getGetChildrenEntityIdsMethod = getGetChildrenEntityIdsMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsRequest, cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetChildrenEntityIds"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("GetChildrenEntityIds"))
              .build();
        }
      }
    }
    return getGetChildrenEntityIdsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse> getGetParentEntityIdsMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "GetParentEntityIds",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse> getGetParentEntityIdsMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsRequest, cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse> getGetParentEntityIdsMethod;
    if ((getGetParentEntityIdsMethod = EntityServiceGrpc.getGetParentEntityIdsMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getGetParentEntityIdsMethod = EntityServiceGrpc.getGetParentEntityIdsMethod) == null) {
          EntityServiceGrpc.getGetParentEntityIdsMethod = getGetParentEntityIdsMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsRequest, cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetParentEntityIds"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("GetParentEntityIds"))
              .build();
        }
      }
    }
    return getGetParentEntityIdsMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVResponse> getRefreshEntityMVMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "RefreshEntityMV",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVResponse> getRefreshEntityMVMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVRequest, cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVResponse> getRefreshEntityMVMethod;
    if ((getRefreshEntityMVMethod = EntityServiceGrpc.getRefreshEntityMVMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getRefreshEntityMVMethod = EntityServiceGrpc.getRefreshEntityMVMethod) == null) {
          EntityServiceGrpc.getRefreshEntityMVMethod = getRefreshEntityMVMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVRequest, cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "RefreshEntityMV"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVResponse.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("RefreshEntityMV"))
              .build();
        }
      }
    }
    return getRefreshEntityMVMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchResponse> getUpdateEntityBatchMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "UpdateEntityBatch",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchResponse> getUpdateEntityBatchMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchRequest, cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchResponse> getUpdateEntityBatchMethod;
    if ((getUpdateEntityBatchMethod = EntityServiceGrpc.getUpdateEntityBatchMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getUpdateEntityBatchMethod = EntityServiceGrpc.getUpdateEntityBatchMethod) == null) {
          EntityServiceGrpc.getUpdateEntityBatchMethod = getUpdateEntityBatchMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchRequest, cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "UpdateEntityBatch"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchResponse.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("UpdateEntityBatch"))
              .build();
        }
      }
    }
    return getUpdateEntityBatchMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchResponse> getAddEntityBatchMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "AddEntityBatch",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchResponse> getAddEntityBatchMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchResponse> getAddEntityBatchMethod;
    if ((getAddEntityBatchMethod = EntityServiceGrpc.getAddEntityBatchMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getAddEntityBatchMethod = EntityServiceGrpc.getAddEntityBatchMethod) == null) {
          EntityServiceGrpc.getAddEntityBatchMethod = getAddEntityBatchMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest, cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "AddEntityBatch"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchResponse.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("AddEntityBatch"))
              .build();
        }
      }
    }
    return getAddEntityBatchMethod;
  }

  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.SqlQueryRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse> getSqlQueryMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "SqlQuery",
      requestType = cn.hexcloud.pbis.common.service.integration.metadata.SqlQueryRequest.class,
      responseType = cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.SqlQueryRequest,
      cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse> getSqlQueryMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.metadata.SqlQueryRequest, cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse> getSqlQueryMethod;
    if ((getSqlQueryMethod = EntityServiceGrpc.getSqlQueryMethod) == null) {
      synchronized (EntityServiceGrpc.class) {
        if ((getSqlQueryMethod = EntityServiceGrpc.getSqlQueryMethod) == null) {
          EntityServiceGrpc.getSqlQueryMethod = getSqlQueryMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.metadata.SqlQueryRequest, cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "SqlQuery"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.SqlQueryRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse.getDefaultInstance()))
              .setSchemaDescriptor(new EntityServiceMethodDescriptorSupplier("SqlQuery"))
              .build();
        }
      }
    }
    return getSqlQueryMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static EntityServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<EntityServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<EntityServiceStub>() {
        @java.lang.Override
        public EntityServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new EntityServiceStub(channel, callOptions);
        }
      };
    return EntityServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static EntityServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<EntityServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<EntityServiceBlockingStub>() {
        @java.lang.Override
        public EntityServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new EntityServiceBlockingStub(channel, callOptions);
        }
      };
    return EntityServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static EntityServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<EntityServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<EntityServiceFutureStub>() {
        @java.lang.Override
        public EntityServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new EntityServiceFutureStub(channel, callOptions);
        }
      };
    return EntityServiceFutureStub.newStub(factory, channel);
  }

  /**
   * <pre>
   * EntityService 用于维护主档Entity数据
   * </pre>
   */
  public static abstract class EntityServiceImplBase implements io.grpc.BindableService {

    /**
     * <pre>
     * 添加Entity
     * </pre>
     */
    public void addEntity(cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getAddEntityMethod(), responseObserver);
    }

    /**
     * <pre>
     * 修改Entity
     * </pre>
     */
    public void updateEntity(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUpdateEntityMethod(), responseObserver);
    }

    /**
     * <pre>
     * 同步Entity
     * </pre>
     */
    public void syncUpdateEntity(cn.hexcloud.pbis.common.service.integration.metadata.SyncEntityRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSyncUpdateEntityMethod(), responseObserver);
    }

    /**
     * <pre>
     * 根据id获取Entity
     * </pre>
     */
    public void getEntityById(cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetEntityByIdMethod(), responseObserver);
    }

    /**
     * <pre>
     * 查询 Entity 列表
     * </pre>
     */
    public void listEntity(cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getListEntityMethod(), responseObserver);
    }

    /**
     * <pre>
     * 修改Entity数据状态
     * </pre>
     */
    public void updateEntityState(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityStateRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUpdateEntityStateMethod(), responseObserver);
    }

    /**
     * <pre>
     * Apply或者Cancel pending changes
     * </pre>
     */
    public void processEntityPendingChanges(cn.hexcloud.pbis.common.service.integration.metadata.ProcessEntityPendingChangesRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getProcessEntityPendingChangesMethod(), responseObserver);
    }

    /**
     * <pre>
     * 基于变更创建一个task
     * </pre>
     */
    public void createEntityTaskFromPendingChanges(cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getCreateEntityTaskFromPendingChangesMethod(), responseObserver);
    }

    /**
     * <pre>
     * 修改Entity Task
     * </pre>
     */
    public void updateEntityTask(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUpdateEntityTaskMethod(), responseObserver);
    }

    /**
     * <pre>
     * 修改Entity Task 状态
     * </pre>
     */
    public void updateEntityTaskStatus(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskStatusRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUpdateEntityTaskStatusMethod(), responseObserver);
    }

    /**
     * <pre>
     * 删除Entity Task 状态
     * </pre>
     */
    public void deleteEntityTask(cn.hexcloud.pbis.common.service.integration.metadata.DeleteEntityTaskRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getDeleteEntityTaskMethod(), responseObserver);
    }

    /**
     * <pre>
     * 根据id获取Entity Task
     * </pre>
     */
    public void getEntityTaskById(cn.hexcloud.pbis.common.service.integration.metadata.GetEntityTaskByIdRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetEntityTaskByIdMethod(), responseObserver);
    }

    /**
     * <pre>
     * 查询 Entity Task 列表
     * </pre>
     */
    public void listEntityTask(cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getListEntityTaskMethod(), responseObserver);
    }

    /**
     * <pre>
     * 根据id列表获取所有id的子节点以及子孙节点
     * </pre>
     */
    public void getChildrenEntityIds(cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetChildrenEntityIdsMethod(), responseObserver);
    }

    /**
     * <pre>
     * 根据id列表获取所有id的上级节点
     * </pre>
     */
    public void getParentEntityIds(cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetParentEntityIdsMethod(), responseObserver);
    }

    /**
     * <pre>
     * 刷新物化视图
     * </pre>
     */
    public void refreshEntityMV(cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getRefreshEntityMVMethod(), responseObserver);
    }

    /**
     * <pre>
     * 修改EntityBatch
     * </pre>
     */
    public void updateEntityBatch(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getUpdateEntityBatchMethod(), responseObserver);
    }

    /**
     * <pre>
     * 添加EntityBatch
     * </pre>
     */
    public void addEntityBatch(cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getAddEntityBatchMethod(), responseObserver);
    }

    /**
     * <pre>
     * 查询 Entity 列表
     * </pre>
     */
    public void sqlQuery(cn.hexcloud.pbis.common.service.integration.metadata.SqlQueryRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSqlQueryMethod(), responseObserver);
    }

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getAddEntityMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.Entity>(
                  this, METHODID_ADD_ENTITY)))
          .addMethod(
            getUpdateEntityMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.Entity>(
                  this, METHODID_UPDATE_ENTITY)))
          .addMethod(
            getSyncUpdateEntityMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.SyncEntityRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.Entity>(
                  this, METHODID_SYNC_UPDATE_ENTITY)))
          .addMethod(
            getGetEntityByIdMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.Entity>(
                  this, METHODID_GET_ENTITY_BY_ID)))
          .addMethod(
            getListEntityMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse>(
                  this, METHODID_LIST_ENTITY)))
          .addMethod(
            getUpdateEntityStateMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityStateRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.Entity>(
                  this, METHODID_UPDATE_ENTITY_STATE)))
          .addMethod(
            getProcessEntityPendingChangesMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.ProcessEntityPendingChangesRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.Entity>(
                  this, METHODID_PROCESS_ENTITY_PENDING_CHANGES)))
          .addMethod(
            getCreateEntityTaskFromPendingChangesMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.EntityTask>(
                  this, METHODID_CREATE_ENTITY_TASK_FROM_PENDING_CHANGES)))
          .addMethod(
            getUpdateEntityTaskMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.EntityTask>(
                  this, METHODID_UPDATE_ENTITY_TASK)))
          .addMethod(
            getUpdateEntityTaskStatusMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskStatusRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.EntityTask>(
                  this, METHODID_UPDATE_ENTITY_TASK_STATUS)))
          .addMethod(
            getDeleteEntityTaskMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.DeleteEntityTaskRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.EntityTask>(
                  this, METHODID_DELETE_ENTITY_TASK)))
          .addMethod(
            getGetEntityTaskByIdMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.GetEntityTaskByIdRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.EntityTask>(
                  this, METHODID_GET_ENTITY_TASK_BY_ID)))
          .addMethod(
            getListEntityTaskMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskResponse>(
                  this, METHODID_LIST_ENTITY_TASK)))
          .addMethod(
            getGetChildrenEntityIdsMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse>(
                  this, METHODID_GET_CHILDREN_ENTITY_IDS)))
          .addMethod(
            getGetParentEntityIdsMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse>(
                  this, METHODID_GET_PARENT_ENTITY_IDS)))
          .addMethod(
            getRefreshEntityMVMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVResponse>(
                  this, METHODID_REFRESH_ENTITY_MV)))
          .addMethod(
            getUpdateEntityBatchMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchResponse>(
                  this, METHODID_UPDATE_ENTITY_BATCH)))
          .addMethod(
            getAddEntityBatchMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchResponse>(
                  this, METHODID_ADD_ENTITY_BATCH)))
          .addMethod(
            getSqlQueryMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.metadata.SqlQueryRequest,
                cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse>(
                  this, METHODID_SQL_QUERY)))
          .build();
    }
  }

  /**
   * <pre>
   * EntityService 用于维护主档Entity数据
   * </pre>
   */
  public static final class EntityServiceStub extends io.grpc.stub.AbstractAsyncStub<EntityServiceStub> {
    private EntityServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected EntityServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new EntityServiceStub(channel, callOptions);
    }

    /**
     * <pre>
     * 添加Entity
     * </pre>
     */
    public void addEntity(cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getAddEntityMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 修改Entity
     * </pre>
     */
    public void updateEntity(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUpdateEntityMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 同步Entity
     * </pre>
     */
    public void syncUpdateEntity(cn.hexcloud.pbis.common.service.integration.metadata.SyncEntityRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSyncUpdateEntityMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 根据id获取Entity
     * </pre>
     */
    public void getEntityById(cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetEntityByIdMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 查询 Entity 列表
     * </pre>
     */
    public void listEntity(cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getListEntityMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 修改Entity数据状态
     * </pre>
     */
    public void updateEntityState(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityStateRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUpdateEntityStateMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * Apply或者Cancel pending changes
     * </pre>
     */
    public void processEntityPendingChanges(cn.hexcloud.pbis.common.service.integration.metadata.ProcessEntityPendingChangesRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getProcessEntityPendingChangesMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 基于变更创建一个task
     * </pre>
     */
    public void createEntityTaskFromPendingChanges(cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getCreateEntityTaskFromPendingChangesMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 修改Entity Task
     * </pre>
     */
    public void updateEntityTask(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUpdateEntityTaskMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 修改Entity Task 状态
     * </pre>
     */
    public void updateEntityTaskStatus(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskStatusRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUpdateEntityTaskStatusMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 删除Entity Task 状态
     * </pre>
     */
    public void deleteEntityTask(cn.hexcloud.pbis.common.service.integration.metadata.DeleteEntityTaskRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getDeleteEntityTaskMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 根据id获取Entity Task
     * </pre>
     */
    public void getEntityTaskById(cn.hexcloud.pbis.common.service.integration.metadata.GetEntityTaskByIdRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetEntityTaskByIdMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 查询 Entity Task 列表
     * </pre>
     */
    public void listEntityTask(cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getListEntityTaskMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 根据id列表获取所有id的子节点以及子孙节点
     * </pre>
     */
    public void getChildrenEntityIds(cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetChildrenEntityIdsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 根据id列表获取所有id的上级节点
     * </pre>
     */
    public void getParentEntityIds(cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetParentEntityIdsMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 刷新物化视图
     * </pre>
     */
    public void refreshEntityMV(cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getRefreshEntityMVMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 修改EntityBatch
     * </pre>
     */
    public void updateEntityBatch(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getUpdateEntityBatchMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 添加EntityBatch
     * </pre>
     */
    public void addEntityBatch(cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getAddEntityBatchMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     * 查询 Entity 列表
     * </pre>
     */
    public void sqlQuery(cn.hexcloud.pbis.common.service.integration.metadata.SqlQueryRequest request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSqlQueryMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * <pre>
   * EntityService 用于维护主档Entity数据
   * </pre>
   */
  public static final class EntityServiceBlockingStub extends io.grpc.stub.AbstractBlockingStub<EntityServiceBlockingStub> {
    private EntityServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected EntityServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new EntityServiceBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * 添加Entity
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.Entity addEntity(cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getAddEntityMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 修改Entity
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.Entity updateEntity(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUpdateEntityMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 同步Entity
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.Entity syncUpdateEntity(cn.hexcloud.pbis.common.service.integration.metadata.SyncEntityRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSyncUpdateEntityMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 根据id获取Entity
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.Entity getEntityById(cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetEntityByIdMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 查询 Entity 列表
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse listEntity(cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getListEntityMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 修改Entity数据状态
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.Entity updateEntityState(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityStateRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUpdateEntityStateMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * Apply或者Cancel pending changes
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.Entity processEntityPendingChanges(cn.hexcloud.pbis.common.service.integration.metadata.ProcessEntityPendingChangesRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getProcessEntityPendingChangesMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 基于变更创建一个task
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.EntityTask createEntityTaskFromPendingChanges(cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getCreateEntityTaskFromPendingChangesMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 修改Entity Task
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.EntityTask updateEntityTask(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUpdateEntityTaskMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 修改Entity Task 状态
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.EntityTask updateEntityTaskStatus(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskStatusRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUpdateEntityTaskStatusMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 删除Entity Task 状态
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.EntityTask deleteEntityTask(cn.hexcloud.pbis.common.service.integration.metadata.DeleteEntityTaskRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getDeleteEntityTaskMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 根据id获取Entity Task
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.EntityTask getEntityTaskById(cn.hexcloud.pbis.common.service.integration.metadata.GetEntityTaskByIdRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetEntityTaskByIdMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 查询 Entity Task 列表
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskResponse listEntityTask(cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getListEntityTaskMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 根据id列表获取所有id的子节点以及子孙节点
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse getChildrenEntityIds(cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetChildrenEntityIdsMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 根据id列表获取所有id的上级节点
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse getParentEntityIds(cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetParentEntityIdsMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 刷新物化视图
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVResponse refreshEntityMV(cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getRefreshEntityMVMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 修改EntityBatch
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchResponse updateEntityBatch(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getUpdateEntityBatchMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 添加EntityBatch
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchResponse addEntityBatch(cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getAddEntityBatchMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     * 查询 Entity 列表
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse sqlQuery(cn.hexcloud.pbis.common.service.integration.metadata.SqlQueryRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSqlQueryMethod(), getCallOptions(), request);
    }
  }

  /**
   * <pre>
   * EntityService 用于维护主档Entity数据
   * </pre>
   */
  public static final class EntityServiceFutureStub extends io.grpc.stub.AbstractFutureStub<EntityServiceFutureStub> {
    private EntityServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected EntityServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new EntityServiceFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * 添加Entity
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.Entity> addEntity(
        cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getAddEntityMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 修改Entity
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.Entity> updateEntity(
        cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUpdateEntityMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 同步Entity
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.Entity> syncUpdateEntity(
        cn.hexcloud.pbis.common.service.integration.metadata.SyncEntityRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSyncUpdateEntityMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 根据id获取Entity
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.Entity> getEntityById(
        cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetEntityByIdMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 查询 Entity 列表
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse> listEntity(
        cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getListEntityMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 修改Entity数据状态
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.Entity> updateEntityState(
        cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityStateRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUpdateEntityStateMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * Apply或者Cancel pending changes
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.Entity> processEntityPendingChanges(
        cn.hexcloud.pbis.common.service.integration.metadata.ProcessEntityPendingChangesRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getProcessEntityPendingChangesMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 基于变更创建一个task
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> createEntityTaskFromPendingChanges(
        cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getCreateEntityTaskFromPendingChangesMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 修改Entity Task
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> updateEntityTask(
        cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUpdateEntityTaskMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 修改Entity Task 状态
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> updateEntityTaskStatus(
        cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskStatusRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUpdateEntityTaskStatusMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 删除Entity Task 状态
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> deleteEntityTask(
        cn.hexcloud.pbis.common.service.integration.metadata.DeleteEntityTaskRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getDeleteEntityTaskMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 根据id获取Entity Task
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> getEntityTaskById(
        cn.hexcloud.pbis.common.service.integration.metadata.GetEntityTaskByIdRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetEntityTaskByIdMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 查询 Entity Task 列表
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskResponse> listEntityTask(
        cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getListEntityTaskMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 根据id列表获取所有id的子节点以及子孙节点
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse> getChildrenEntityIds(
        cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetChildrenEntityIdsMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 根据id列表获取所有id的上级节点
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse> getParentEntityIds(
        cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetParentEntityIdsMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 刷新物化视图
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVResponse> refreshEntityMV(
        cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getRefreshEntityMVMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 修改EntityBatch
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchResponse> updateEntityBatch(
        cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getUpdateEntityBatchMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 添加EntityBatch
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchResponse> addEntityBatch(
        cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getAddEntityBatchMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     * 查询 Entity 列表
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse> sqlQuery(
        cn.hexcloud.pbis.common.service.integration.metadata.SqlQueryRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSqlQueryMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_ADD_ENTITY = 0;
  private static final int METHODID_UPDATE_ENTITY = 1;
  private static final int METHODID_SYNC_UPDATE_ENTITY = 2;
  private static final int METHODID_GET_ENTITY_BY_ID = 3;
  private static final int METHODID_LIST_ENTITY = 4;
  private static final int METHODID_UPDATE_ENTITY_STATE = 5;
  private static final int METHODID_PROCESS_ENTITY_PENDING_CHANGES = 6;
  private static final int METHODID_CREATE_ENTITY_TASK_FROM_PENDING_CHANGES = 7;
  private static final int METHODID_UPDATE_ENTITY_TASK = 8;
  private static final int METHODID_UPDATE_ENTITY_TASK_STATUS = 9;
  private static final int METHODID_DELETE_ENTITY_TASK = 10;
  private static final int METHODID_GET_ENTITY_TASK_BY_ID = 11;
  private static final int METHODID_LIST_ENTITY_TASK = 12;
  private static final int METHODID_GET_CHILDREN_ENTITY_IDS = 13;
  private static final int METHODID_GET_PARENT_ENTITY_IDS = 14;
  private static final int METHODID_REFRESH_ENTITY_MV = 15;
  private static final int METHODID_UPDATE_ENTITY_BATCH = 16;
  private static final int METHODID_ADD_ENTITY_BATCH = 17;
  private static final int METHODID_SQL_QUERY = 18;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final EntityServiceImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(EntityServiceImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_ADD_ENTITY:
          serviceImpl.addEntity((cn.hexcloud.pbis.common.service.integration.metadata.AddEntityRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity>) responseObserver);
          break;
        case METHODID_UPDATE_ENTITY:
          serviceImpl.updateEntity((cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity>) responseObserver);
          break;
        case METHODID_SYNC_UPDATE_ENTITY:
          serviceImpl.syncUpdateEntity((cn.hexcloud.pbis.common.service.integration.metadata.SyncEntityRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity>) responseObserver);
          break;
        case METHODID_GET_ENTITY_BY_ID:
          serviceImpl.getEntityById((cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity>) responseObserver);
          break;
        case METHODID_LIST_ENTITY:
          serviceImpl.listEntity((cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse>) responseObserver);
          break;
        case METHODID_UPDATE_ENTITY_STATE:
          serviceImpl.updateEntityState((cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityStateRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity>) responseObserver);
          break;
        case METHODID_PROCESS_ENTITY_PENDING_CHANGES:
          serviceImpl.processEntityPendingChanges((cn.hexcloud.pbis.common.service.integration.metadata.ProcessEntityPendingChangesRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.Entity>) responseObserver);
          break;
        case METHODID_CREATE_ENTITY_TASK_FROM_PENDING_CHANGES:
          serviceImpl.createEntityTaskFromPendingChanges((cn.hexcloud.pbis.common.service.integration.metadata.CreateEntityTaskFromPendingChangesRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask>) responseObserver);
          break;
        case METHODID_UPDATE_ENTITY_TASK:
          serviceImpl.updateEntityTask((cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask>) responseObserver);
          break;
        case METHODID_UPDATE_ENTITY_TASK_STATUS:
          serviceImpl.updateEntityTaskStatus((cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityTaskStatusRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask>) responseObserver);
          break;
        case METHODID_DELETE_ENTITY_TASK:
          serviceImpl.deleteEntityTask((cn.hexcloud.pbis.common.service.integration.metadata.DeleteEntityTaskRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask>) responseObserver);
          break;
        case METHODID_GET_ENTITY_TASK_BY_ID:
          serviceImpl.getEntityTaskById((cn.hexcloud.pbis.common.service.integration.metadata.GetEntityTaskByIdRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask>) responseObserver);
          break;
        case METHODID_LIST_ENTITY_TASK:
          serviceImpl.listEntityTask((cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskResponse>) responseObserver);
          break;
        case METHODID_GET_CHILDREN_ENTITY_IDS:
          serviceImpl.getChildrenEntityIds((cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse>) responseObserver);
          break;
        case METHODID_GET_PARENT_ENTITY_IDS:
          serviceImpl.getParentEntityIds((cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse>) responseObserver);
          break;
        case METHODID_REFRESH_ENTITY_MV:
          serviceImpl.refreshEntityMV((cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.RefreshEntityMVResponse>) responseObserver);
          break;
        case METHODID_UPDATE_ENTITY_BATCH:
          serviceImpl.updateEntityBatch((cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityBatchResponse>) responseObserver);
          break;
        case METHODID_ADD_ENTITY_BATCH:
          serviceImpl.addEntityBatch((cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.AddEntityBatchResponse>) responseObserver);
          break;
        case METHODID_SQL_QUERY:
          serviceImpl.sqlQuery((cn.hexcloud.pbis.common.service.integration.metadata.SqlQueryRequest) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.metadata.ListEntityResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class EntityServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    EntityServiceBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("EntityService");
    }
  }

  private static final class EntityServiceFileDescriptorSupplier
      extends EntityServiceBaseDescriptorSupplier {
    EntityServiceFileDescriptorSupplier() {}
  }

  private static final class EntityServiceMethodDescriptorSupplier
      extends EntityServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    EntityServiceMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (EntityServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new EntityServiceFileDescriptorSupplier())
              .addMethod(getAddEntityMethod())
              .addMethod(getUpdateEntityMethod())
              .addMethod(getSyncUpdateEntityMethod())
              .addMethod(getGetEntityByIdMethod())
              .addMethod(getListEntityMethod())
              .addMethod(getUpdateEntityStateMethod())
              .addMethod(getProcessEntityPendingChangesMethod())
              .addMethod(getCreateEntityTaskFromPendingChangesMethod())
              .addMethod(getUpdateEntityTaskMethod())
              .addMethod(getUpdateEntityTaskStatusMethod())
              .addMethod(getDeleteEntityTaskMethod())
              .addMethod(getGetEntityTaskByIdMethod())
              .addMethod(getListEntityTaskMethod())
              .addMethod(getGetChildrenEntityIdsMethod())
              .addMethod(getGetParentEntityIdsMethod())
              .addMethod(getRefreshEntityMVMethod())
              .addMethod(getUpdateEntityBatchMethod())
              .addMethod(getAddEntityBatchMethod())
              .addMethod(getSqlQueryMethod())
              .build();
        }
      }
    }
    return result;
  }
}
