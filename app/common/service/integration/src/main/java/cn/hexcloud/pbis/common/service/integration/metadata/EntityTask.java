// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

/**
 * Protobuf type {@code entity.EntityTask}
 */
public final class EntityTask extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:entity.EntityTask)
    EntityTaskOrBuilder {
private static final long serialVersionUID = 0L;
  // Use EntityTask.newBuilder() to construct.
  private EntityTask(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private EntityTask() {
    schemaType_ = "";
    name_ = "";
    status_ = "";
    processStatus_ = "";
    action_ = "";
    start_ = "";
    lastStart_ = "";
    lastEnd_ = "";
    created_ = "";
    updated_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new EntityTask();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private EntityTask(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            id_ = input.readUInt64();
            break;
          }
          case 16: {

            partnerId_ = input.readUInt64();
            break;
          }
          case 24: {

            scopeId_ = input.readUInt64();
            break;
          }
          case 32: {

            jobId_ = input.readUInt64();
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            schemaType_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            name_ = s;
            break;
          }
          case 56: {

            recordId_ = input.readUInt64();
            break;
          }
          case 66: {
            com.google.protobuf.Struct.Builder subBuilder = null;
            if (content_ != null) {
              subBuilder = content_.toBuilder();
            }
            content_ = input.readMessage(com.google.protobuf.Struct.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(content_);
              content_ = subBuilder.buildPartial();
            }

            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            status_ = s;
            break;
          }
          case 82: {
            java.lang.String s = input.readStringRequireUtf8();

            processStatus_ = s;
            break;
          }
          case 90: {
            java.lang.String s = input.readStringRequireUtf8();

            action_ = s;
            break;
          }
          case 96: {

            immediate_ = input.readBool();
            break;
          }
          case 106: {
            java.lang.String s = input.readStringRequireUtf8();

            start_ = s;
            break;
          }
          case 114: {
            java.lang.String s = input.readStringRequireUtf8();

            lastStart_ = s;
            break;
          }
          case 122: {
            java.lang.String s = input.readStringRequireUtf8();

            lastEnd_ = s;
            break;
          }
          case 128: {

            retry_ = input.readInt32();
            break;
          }
          case 138: {
            java.lang.String s = input.readStringRequireUtf8();

            created_ = s;
            break;
          }
          case 146: {
            java.lang.String s = input.readStringRequireUtf8();

            updated_ = s;
            break;
          }
          case 152: {

            createdBy_ = input.readUInt64();
            break;
          }
          case 160: {

            updatedBy_ = input.readUInt64();
            break;
          }
          case 170: {
            com.google.protobuf.Struct.Builder subBuilder = null;
            if (contentFrom_ != null) {
              subBuilder = contentFrom_.toBuilder();
            }
            contentFrom_ = input.readMessage(com.google.protobuf.Struct.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(contentFrom_);
              contentFrom_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_EntityTask_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_EntityTask_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.class, cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  private long id_;
  /**
   * <pre>
   * task id
   * </pre>
   *
   * <code>uint64 id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public long getId() {
    return id_;
  }

  public static final int PARTNER_ID_FIELD_NUMBER = 2;
  private long partnerId_;
  /**
   * <pre>
   * 商户id
   * </pre>
   *
   * <code>uint64 partner_id = 2;</code>
   * @return The partnerId.
   */
  @java.lang.Override
  public long getPartnerId() {
    return partnerId_;
  }

  public static final int SCOPE_ID_FIELD_NUMBER = 3;
  private long scopeId_;
  /**
   * <pre>
   * scope_id
   * </pre>
   *
   * <code>uint64 scope_id = 3;</code>
   * @return The scopeId.
   */
  @java.lang.Override
  public long getScopeId() {
    return scopeId_;
  }

  public static final int JOB_ID_FIELD_NUMBER = 4;
  private long jobId_;
  /**
   * <pre>
   * job id
   * </pre>
   *
   * <code>uint64 job_id = 4;</code>
   * @return The jobId.
   */
  @java.lang.Override
  public long getJobId() {
    return jobId_;
  }

  public static final int SCHEMA_TYPE_FIELD_NUMBER = 5;
  private volatile java.lang.Object schemaType_;
  /**
   * <pre>
   * schema type(entity, relation)
   * </pre>
   *
   * <code>string schema_type = 5;</code>
   * @return The schemaType.
   */
  @java.lang.Override
  public java.lang.String getSchemaType() {
    java.lang.Object ref = schemaType_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      schemaType_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * schema type(entity, relation)
   * </pre>
   *
   * <code>string schema_type = 5;</code>
   * @return The bytes for schemaType.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSchemaTypeBytes() {
    java.lang.Object ref = schemaType_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      schemaType_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NAME_FIELD_NUMBER = 6;
  private volatile java.lang.Object name_;
  /**
   * <pre>
   * task 名称
   * </pre>
   *
   * <code>string name = 6;</code>
   * @return The name.
   */
  @java.lang.Override
  public java.lang.String getName() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      name_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * task 名称
   * </pre>
   *
   * <code>string name = 6;</code>
   * @return The bytes for name.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNameBytes() {
    java.lang.Object ref = name_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      name_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RECORD_ID_FIELD_NUMBER = 7;
  private long recordId_;
  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>uint64 record_id = 7;</code>
   * @return The recordId.
   */
  @java.lang.Override
  public long getRecordId() {
    return recordId_;
  }

  public static final int CONTENT_FIELD_NUMBER = 8;
  private com.google.protobuf.Struct content_;
  /**
   * <pre>
   * 数据字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct content = 8;</code>
   * @return Whether the content field is set.
   */
  @java.lang.Override
  public boolean hasContent() {
    return content_ != null;
  }
  /**
   * <pre>
   * 数据字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct content = 8;</code>
   * @return The content.
   */
  @java.lang.Override
  public com.google.protobuf.Struct getContent() {
    return content_ == null ? com.google.protobuf.Struct.getDefaultInstance() : content_;
  }
  /**
   * <pre>
   * 数据字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct content = 8;</code>
   */
  @java.lang.Override
  public com.google.protobuf.StructOrBuilder getContentOrBuilder() {
    return getContent();
  }

  public static final int CONTENT_FROM_FIELD_NUMBER = 21;
  private com.google.protobuf.Struct contentFrom_;
  /**
   * <pre>
   *数据原先的字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct content_from = 21;</code>
   * @return Whether the contentFrom field is set.
   */
  @java.lang.Override
  public boolean hasContentFrom() {
    return contentFrom_ != null;
  }
  /**
   * <pre>
   *数据原先的字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct content_from = 21;</code>
   * @return The contentFrom.
   */
  @java.lang.Override
  public com.google.protobuf.Struct getContentFrom() {
    return contentFrom_ == null ? com.google.protobuf.Struct.getDefaultInstance() : contentFrom_;
  }
  /**
   * <pre>
   *数据原先的字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct content_from = 21;</code>
   */
  @java.lang.Override
  public com.google.protobuf.StructOrBuilder getContentFromOrBuilder() {
    return getContentFrom();
  }

  public static final int STATUS_FIELD_NUMBER = 9;
  private volatile java.lang.Object status_;
  /**
   * <pre>
   * task 状态
   * </pre>
   *
   * <code>string status = 9;</code>
   * @return The status.
   */
  @java.lang.Override
  public java.lang.String getStatus() {
    java.lang.Object ref = status_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      status_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * task 状态
   * </pre>
   *
   * <code>string status = 9;</code>
   * @return The bytes for status.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStatusBytes() {
    java.lang.Object ref = status_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      status_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PROCESS_STATUS_FIELD_NUMBER = 10;
  private volatile java.lang.Object processStatus_;
  /**
   * <pre>
   * 数据被批处理状态
   * </pre>
   *
   * <code>string process_status = 10;</code>
   * @return The processStatus.
   */
  @java.lang.Override
  public java.lang.String getProcessStatus() {
    java.lang.Object ref = processStatus_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      processStatus_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 数据被批处理状态
   * </pre>
   *
   * <code>string process_status = 10;</code>
   * @return The bytes for processStatus.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getProcessStatusBytes() {
    java.lang.Object ref = processStatus_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      processStatus_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ACTION_FIELD_NUMBER = 11;
  private volatile java.lang.Object action_;
  /**
   * <pre>
   * task动作
   * </pre>
   *
   * <code>string action = 11;</code>
   * @return The action.
   */
  @java.lang.Override
  public java.lang.String getAction() {
    java.lang.Object ref = action_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      action_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * task动作
   * </pre>
   *
   * <code>string action = 11;</code>
   * @return The bytes for action.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getActionBytes() {
    java.lang.Object ref = action_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      action_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IMMEDIATE_FIELD_NUMBER = 12;
  private boolean immediate_;
  /**
   * <pre>
   * 是否立即执行
   * </pre>
   *
   * <code>bool immediate = 12;</code>
   * @return The immediate.
   */
  @java.lang.Override
  public boolean getImmediate() {
    return immediate_;
  }

  public static final int START_FIELD_NUMBER = 13;
  private volatile java.lang.Object start_;
  /**
   * <pre>
   * 开始执行时间
   * </pre>
   *
   * <code>string start = 13;</code>
   * @return The start.
   */
  @java.lang.Override
  public java.lang.String getStart() {
    java.lang.Object ref = start_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      start_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 开始执行时间
   * </pre>
   *
   * <code>string start = 13;</code>
   * @return The bytes for start.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStartBytes() {
    java.lang.Object ref = start_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      start_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LAST_START_FIELD_NUMBER = 14;
  private volatile java.lang.Object lastStart_;
  /**
   * <pre>
   * 最后一次开始执行时间
   * </pre>
   *
   * <code>string last_start = 14;</code>
   * @return The lastStart.
   */
  @java.lang.Override
  public java.lang.String getLastStart() {
    java.lang.Object ref = lastStart_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      lastStart_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 最后一次开始执行时间
   * </pre>
   *
   * <code>string last_start = 14;</code>
   * @return The bytes for lastStart.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLastStartBytes() {
    java.lang.Object ref = lastStart_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      lastStart_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LAST_END_FIELD_NUMBER = 15;
  private volatile java.lang.Object lastEnd_;
  /**
   * <pre>
   * 最后一次结束时间
   * </pre>
   *
   * <code>string last_end = 15;</code>
   * @return The lastEnd.
   */
  @java.lang.Override
  public java.lang.String getLastEnd() {
    java.lang.Object ref = lastEnd_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      lastEnd_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 最后一次结束时间
   * </pre>
   *
   * <code>string last_end = 15;</code>
   * @return The bytes for lastEnd.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLastEndBytes() {
    java.lang.Object ref = lastEnd_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      lastEnd_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RETRY_FIELD_NUMBER = 16;
  private int retry_;
  /**
   * <pre>
   * 重试次数
   * </pre>
   *
   * <code>int32 retry = 16;</code>
   * @return The retry.
   */
  @java.lang.Override
  public int getRetry() {
    return retry_;
  }

  public static final int CREATED_FIELD_NUMBER = 17;
  private volatile java.lang.Object created_;
  /**
   * <pre>
   * 创建时间
   * </pre>
   *
   * <code>string created = 17;</code>
   * @return The created.
   */
  @java.lang.Override
  public java.lang.String getCreated() {
    java.lang.Object ref = created_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      created_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 创建时间
   * </pre>
   *
   * <code>string created = 17;</code>
   * @return The bytes for created.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCreatedBytes() {
    java.lang.Object ref = created_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      created_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int UPDATED_FIELD_NUMBER = 18;
  private volatile java.lang.Object updated_;
  /**
   * <pre>
   * 最后一次修改时间
   * </pre>
   *
   * <code>string updated = 18;</code>
   * @return The updated.
   */
  @java.lang.Override
  public java.lang.String getUpdated() {
    java.lang.Object ref = updated_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      updated_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 最后一次修改时间
   * </pre>
   *
   * <code>string updated = 18;</code>
   * @return The bytes for updated.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUpdatedBytes() {
    java.lang.Object ref = updated_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      updated_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CREATED_BY_FIELD_NUMBER = 19;
  private long createdBy_;
  /**
   * <pre>
   * 创建者
   * </pre>
   *
   * <code>uint64 created_by = 19;</code>
   * @return The createdBy.
   */
  @java.lang.Override
  public long getCreatedBy() {
    return createdBy_;
  }

  public static final int UPDATED_BY_FIELD_NUMBER = 20;
  private long updatedBy_;
  /**
   * <pre>
   * 最后一次修改者
   * </pre>
   *
   * <code>uint64 updated_by = 20;</code>
   * @return The updatedBy.
   */
  @java.lang.Override
  public long getUpdatedBy() {
    return updatedBy_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (id_ != 0L) {
      output.writeUInt64(1, id_);
    }
    if (partnerId_ != 0L) {
      output.writeUInt64(2, partnerId_);
    }
    if (scopeId_ != 0L) {
      output.writeUInt64(3, scopeId_);
    }
    if (jobId_ != 0L) {
      output.writeUInt64(4, jobId_);
    }
    if (!getSchemaTypeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, schemaType_);
    }
    if (!getNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, name_);
    }
    if (recordId_ != 0L) {
      output.writeUInt64(7, recordId_);
    }
    if (content_ != null) {
      output.writeMessage(8, getContent());
    }
    if (!getStatusBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, status_);
    }
    if (!getProcessStatusBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, processStatus_);
    }
    if (!getActionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, action_);
    }
    if (immediate_ != false) {
      output.writeBool(12, immediate_);
    }
    if (!getStartBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 13, start_);
    }
    if (!getLastStartBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 14, lastStart_);
    }
    if (!getLastEndBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 15, lastEnd_);
    }
    if (retry_ != 0) {
      output.writeInt32(16, retry_);
    }
    if (!getCreatedBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 17, created_);
    }
    if (!getUpdatedBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 18, updated_);
    }
    if (createdBy_ != 0L) {
      output.writeUInt64(19, createdBy_);
    }
    if (updatedBy_ != 0L) {
      output.writeUInt64(20, updatedBy_);
    }
    if (contentFrom_ != null) {
      output.writeMessage(21, getContentFrom());
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (id_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, id_);
    }
    if (partnerId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(2, partnerId_);
    }
    if (scopeId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(3, scopeId_);
    }
    if (jobId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(4, jobId_);
    }
    if (!getSchemaTypeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, schemaType_);
    }
    if (!getNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, name_);
    }
    if (recordId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(7, recordId_);
    }
    if (content_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, getContent());
    }
    if (!getStatusBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, status_);
    }
    if (!getProcessStatusBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, processStatus_);
    }
    if (!getActionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, action_);
    }
    if (immediate_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(12, immediate_);
    }
    if (!getStartBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, start_);
    }
    if (!getLastStartBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, lastStart_);
    }
    if (!getLastEndBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, lastEnd_);
    }
    if (retry_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(16, retry_);
    }
    if (!getCreatedBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, created_);
    }
    if (!getUpdatedBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(18, updated_);
    }
    if (createdBy_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(19, createdBy_);
    }
    if (updatedBy_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(20, updatedBy_);
    }
    if (contentFrom_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(21, getContentFrom());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.metadata.EntityTask)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.metadata.EntityTask other = (cn.hexcloud.pbis.common.service.integration.metadata.EntityTask) obj;

    if (getId()
        != other.getId()) return false;
    if (getPartnerId()
        != other.getPartnerId()) return false;
    if (getScopeId()
        != other.getScopeId()) return false;
    if (getJobId()
        != other.getJobId()) return false;
    if (!getSchemaType()
        .equals(other.getSchemaType())) return false;
    if (!getName()
        .equals(other.getName())) return false;
    if (getRecordId()
        != other.getRecordId()) return false;
    if (hasContent() != other.hasContent()) return false;
    if (hasContent()) {
      if (!getContent()
          .equals(other.getContent())) return false;
    }
    if (hasContentFrom() != other.hasContentFrom()) return false;
    if (hasContentFrom()) {
      if (!getContentFrom()
          .equals(other.getContentFrom())) return false;
    }
    if (!getStatus()
        .equals(other.getStatus())) return false;
    if (!getProcessStatus()
        .equals(other.getProcessStatus())) return false;
    if (!getAction()
        .equals(other.getAction())) return false;
    if (getImmediate()
        != other.getImmediate()) return false;
    if (!getStart()
        .equals(other.getStart())) return false;
    if (!getLastStart()
        .equals(other.getLastStart())) return false;
    if (!getLastEnd()
        .equals(other.getLastEnd())) return false;
    if (getRetry()
        != other.getRetry()) return false;
    if (!getCreated()
        .equals(other.getCreated())) return false;
    if (!getUpdated()
        .equals(other.getUpdated())) return false;
    if (getCreatedBy()
        != other.getCreatedBy()) return false;
    if (getUpdatedBy()
        != other.getUpdatedBy()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getId());
    hash = (37 * hash) + PARTNER_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getPartnerId());
    hash = (37 * hash) + SCOPE_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getScopeId());
    hash = (37 * hash) + JOB_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getJobId());
    hash = (37 * hash) + SCHEMA_TYPE_FIELD_NUMBER;
    hash = (53 * hash) + getSchemaType().hashCode();
    hash = (37 * hash) + NAME_FIELD_NUMBER;
    hash = (53 * hash) + getName().hashCode();
    hash = (37 * hash) + RECORD_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getRecordId());
    if (hasContent()) {
      hash = (37 * hash) + CONTENT_FIELD_NUMBER;
      hash = (53 * hash) + getContent().hashCode();
    }
    if (hasContentFrom()) {
      hash = (37 * hash) + CONTENT_FROM_FIELD_NUMBER;
      hash = (53 * hash) + getContentFrom().hashCode();
    }
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + getStatus().hashCode();
    hash = (37 * hash) + PROCESS_STATUS_FIELD_NUMBER;
    hash = (53 * hash) + getProcessStatus().hashCode();
    hash = (37 * hash) + ACTION_FIELD_NUMBER;
    hash = (53 * hash) + getAction().hashCode();
    hash = (37 * hash) + IMMEDIATE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getImmediate());
    hash = (37 * hash) + START_FIELD_NUMBER;
    hash = (53 * hash) + getStart().hashCode();
    hash = (37 * hash) + LAST_START_FIELD_NUMBER;
    hash = (53 * hash) + getLastStart().hashCode();
    hash = (37 * hash) + LAST_END_FIELD_NUMBER;
    hash = (53 * hash) + getLastEnd().hashCode();
    hash = (37 * hash) + RETRY_FIELD_NUMBER;
    hash = (53 * hash) + getRetry();
    hash = (37 * hash) + CREATED_FIELD_NUMBER;
    hash = (53 * hash) + getCreated().hashCode();
    hash = (37 * hash) + UPDATED_FIELD_NUMBER;
    hash = (53 * hash) + getUpdated().hashCode();
    hash = (37 * hash) + CREATED_BY_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getCreatedBy());
    hash = (37 * hash) + UPDATED_BY_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getUpdatedBy());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.EntityTask parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.EntityTask parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.EntityTask parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.EntityTask parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.EntityTask parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.EntityTask parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.EntityTask parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.EntityTask parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.EntityTask parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.EntityTask parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.EntityTask parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.EntityTask parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.metadata.EntityTask prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code entity.EntityTask}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:entity.EntityTask)
      cn.hexcloud.pbis.common.service.integration.metadata.EntityTaskOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_EntityTask_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_EntityTask_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.class, cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      id_ = 0L;

      partnerId_ = 0L;

      scopeId_ = 0L;

      jobId_ = 0L;

      schemaType_ = "";

      name_ = "";

      recordId_ = 0L;

      if (contentBuilder_ == null) {
        content_ = null;
      } else {
        content_ = null;
        contentBuilder_ = null;
      }
      if (contentFromBuilder_ == null) {
        contentFrom_ = null;
      } else {
        contentFrom_ = null;
        contentFromBuilder_ = null;
      }
      status_ = "";

      processStatus_ = "";

      action_ = "";

      immediate_ = false;

      start_ = "";

      lastStart_ = "";

      lastEnd_ = "";

      retry_ = 0;

      created_ = "";

      updated_ = "";

      createdBy_ = 0L;

      updatedBy_ = 0L;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_EntityTask_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.EntityTask getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.EntityTask build() {
      cn.hexcloud.pbis.common.service.integration.metadata.EntityTask result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.EntityTask buildPartial() {
      cn.hexcloud.pbis.common.service.integration.metadata.EntityTask result = new cn.hexcloud.pbis.common.service.integration.metadata.EntityTask(this);
      result.id_ = id_;
      result.partnerId_ = partnerId_;
      result.scopeId_ = scopeId_;
      result.jobId_ = jobId_;
      result.schemaType_ = schemaType_;
      result.name_ = name_;
      result.recordId_ = recordId_;
      if (contentBuilder_ == null) {
        result.content_ = content_;
      } else {
        result.content_ = contentBuilder_.build();
      }
      if (contentFromBuilder_ == null) {
        result.contentFrom_ = contentFrom_;
      } else {
        result.contentFrom_ = contentFromBuilder_.build();
      }
      result.status_ = status_;
      result.processStatus_ = processStatus_;
      result.action_ = action_;
      result.immediate_ = immediate_;
      result.start_ = start_;
      result.lastStart_ = lastStart_;
      result.lastEnd_ = lastEnd_;
      result.retry_ = retry_;
      result.created_ = created_;
      result.updated_ = updated_;
      result.createdBy_ = createdBy_;
      result.updatedBy_ = updatedBy_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.metadata.EntityTask) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.metadata.EntityTask)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.metadata.EntityTask other) {
      if (other == cn.hexcloud.pbis.common.service.integration.metadata.EntityTask.getDefaultInstance()) return this;
      if (other.getId() != 0L) {
        setId(other.getId());
      }
      if (other.getPartnerId() != 0L) {
        setPartnerId(other.getPartnerId());
      }
      if (other.getScopeId() != 0L) {
        setScopeId(other.getScopeId());
      }
      if (other.getJobId() != 0L) {
        setJobId(other.getJobId());
      }
      if (!other.getSchemaType().isEmpty()) {
        schemaType_ = other.schemaType_;
        onChanged();
      }
      if (!other.getName().isEmpty()) {
        name_ = other.name_;
        onChanged();
      }
      if (other.getRecordId() != 0L) {
        setRecordId(other.getRecordId());
      }
      if (other.hasContent()) {
        mergeContent(other.getContent());
      }
      if (other.hasContentFrom()) {
        mergeContentFrom(other.getContentFrom());
      }
      if (!other.getStatus().isEmpty()) {
        status_ = other.status_;
        onChanged();
      }
      if (!other.getProcessStatus().isEmpty()) {
        processStatus_ = other.processStatus_;
        onChanged();
      }
      if (!other.getAction().isEmpty()) {
        action_ = other.action_;
        onChanged();
      }
      if (other.getImmediate() != false) {
        setImmediate(other.getImmediate());
      }
      if (!other.getStart().isEmpty()) {
        start_ = other.start_;
        onChanged();
      }
      if (!other.getLastStart().isEmpty()) {
        lastStart_ = other.lastStart_;
        onChanged();
      }
      if (!other.getLastEnd().isEmpty()) {
        lastEnd_ = other.lastEnd_;
        onChanged();
      }
      if (other.getRetry() != 0) {
        setRetry(other.getRetry());
      }
      if (!other.getCreated().isEmpty()) {
        created_ = other.created_;
        onChanged();
      }
      if (!other.getUpdated().isEmpty()) {
        updated_ = other.updated_;
        onChanged();
      }
      if (other.getCreatedBy() != 0L) {
        setCreatedBy(other.getCreatedBy());
      }
      if (other.getUpdatedBy() != 0L) {
        setUpdatedBy(other.getUpdatedBy());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.metadata.EntityTask parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.metadata.EntityTask) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long id_ ;
    /**
     * <pre>
     * task id
     * </pre>
     *
     * <code>uint64 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public long getId() {
      return id_;
    }
    /**
     * <pre>
     * task id
     * </pre>
     *
     * <code>uint64 id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(long value) {
      
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * task id
     * </pre>
     *
     * <code>uint64 id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      
      id_ = 0L;
      onChanged();
      return this;
    }

    private long partnerId_ ;
    /**
     * <pre>
     * 商户id
     * </pre>
     *
     * <code>uint64 partner_id = 2;</code>
     * @return The partnerId.
     */
    @java.lang.Override
    public long getPartnerId() {
      return partnerId_;
    }
    /**
     * <pre>
     * 商户id
     * </pre>
     *
     * <code>uint64 partner_id = 2;</code>
     * @param value The partnerId to set.
     * @return This builder for chaining.
     */
    public Builder setPartnerId(long value) {
      
      partnerId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 商户id
     * </pre>
     *
     * <code>uint64 partner_id = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearPartnerId() {
      
      partnerId_ = 0L;
      onChanged();
      return this;
    }

    private long scopeId_ ;
    /**
     * <pre>
     * scope_id
     * </pre>
     *
     * <code>uint64 scope_id = 3;</code>
     * @return The scopeId.
     */
    @java.lang.Override
    public long getScopeId() {
      return scopeId_;
    }
    /**
     * <pre>
     * scope_id
     * </pre>
     *
     * <code>uint64 scope_id = 3;</code>
     * @param value The scopeId to set.
     * @return This builder for chaining.
     */
    public Builder setScopeId(long value) {
      
      scopeId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * scope_id
     * </pre>
     *
     * <code>uint64 scope_id = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearScopeId() {
      
      scopeId_ = 0L;
      onChanged();
      return this;
    }

    private long jobId_ ;
    /**
     * <pre>
     * job id
     * </pre>
     *
     * <code>uint64 job_id = 4;</code>
     * @return The jobId.
     */
    @java.lang.Override
    public long getJobId() {
      return jobId_;
    }
    /**
     * <pre>
     * job id
     * </pre>
     *
     * <code>uint64 job_id = 4;</code>
     * @param value The jobId to set.
     * @return This builder for chaining.
     */
    public Builder setJobId(long value) {
      
      jobId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * job id
     * </pre>
     *
     * <code>uint64 job_id = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearJobId() {
      
      jobId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object schemaType_ = "";
    /**
     * <pre>
     * schema type(entity, relation)
     * </pre>
     *
     * <code>string schema_type = 5;</code>
     * @return The schemaType.
     */
    public java.lang.String getSchemaType() {
      java.lang.Object ref = schemaType_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        schemaType_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * schema type(entity, relation)
     * </pre>
     *
     * <code>string schema_type = 5;</code>
     * @return The bytes for schemaType.
     */
    public com.google.protobuf.ByteString
        getSchemaTypeBytes() {
      java.lang.Object ref = schemaType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        schemaType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * schema type(entity, relation)
     * </pre>
     *
     * <code>string schema_type = 5;</code>
     * @param value The schemaType to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaType(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      schemaType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * schema type(entity, relation)
     * </pre>
     *
     * <code>string schema_type = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearSchemaType() {
      
      schemaType_ = getDefaultInstance().getSchemaType();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * schema type(entity, relation)
     * </pre>
     *
     * <code>string schema_type = 5;</code>
     * @param value The bytes for schemaType to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaTypeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      schemaType_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object name_ = "";
    /**
     * <pre>
     * task 名称
     * </pre>
     *
     * <code>string name = 6;</code>
     * @return The name.
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * task 名称
     * </pre>
     *
     * <code>string name = 6;</code>
     * @return The bytes for name.
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * task 名称
     * </pre>
     *
     * <code>string name = 6;</code>
     * @param value The name to set.
     * @return This builder for chaining.
     */
    public Builder setName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      name_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * task 名称
     * </pre>
     *
     * <code>string name = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearName() {
      
      name_ = getDefaultInstance().getName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * task 名称
     * </pre>
     *
     * <code>string name = 6;</code>
     * @param value The bytes for name to set.
     * @return This builder for chaining.
     */
    public Builder setNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      name_ = value;
      onChanged();
      return this;
    }

    private long recordId_ ;
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>uint64 record_id = 7;</code>
     * @return The recordId.
     */
    @java.lang.Override
    public long getRecordId() {
      return recordId_;
    }
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>uint64 record_id = 7;</code>
     * @param value The recordId to set.
     * @return This builder for chaining.
     */
    public Builder setRecordId(long value) {
      
      recordId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>uint64 record_id = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearRecordId() {
      
      recordId_ = 0L;
      onChanged();
      return this;
    }

    private com.google.protobuf.Struct content_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> contentBuilder_;
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content = 8;</code>
     * @return Whether the content field is set.
     */
    public boolean hasContent() {
      return contentBuilder_ != null || content_ != null;
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content = 8;</code>
     * @return The content.
     */
    public com.google.protobuf.Struct getContent() {
      if (contentBuilder_ == null) {
        return content_ == null ? com.google.protobuf.Struct.getDefaultInstance() : content_;
      } else {
        return contentBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content = 8;</code>
     */
    public Builder setContent(com.google.protobuf.Struct value) {
      if (contentBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        content_ = value;
        onChanged();
      } else {
        contentBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content = 8;</code>
     */
    public Builder setContent(
        com.google.protobuf.Struct.Builder builderForValue) {
      if (contentBuilder_ == null) {
        content_ = builderForValue.build();
        onChanged();
      } else {
        contentBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content = 8;</code>
     */
    public Builder mergeContent(com.google.protobuf.Struct value) {
      if (contentBuilder_ == null) {
        if (content_ != null) {
          content_ =
            com.google.protobuf.Struct.newBuilder(content_).mergeFrom(value).buildPartial();
        } else {
          content_ = value;
        }
        onChanged();
      } else {
        contentBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content = 8;</code>
     */
    public Builder clearContent() {
      if (contentBuilder_ == null) {
        content_ = null;
        onChanged();
      } else {
        content_ = null;
        contentBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content = 8;</code>
     */
    public com.google.protobuf.Struct.Builder getContentBuilder() {
      
      onChanged();
      return getContentFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content = 8;</code>
     */
    public com.google.protobuf.StructOrBuilder getContentOrBuilder() {
      if (contentBuilder_ != null) {
        return contentBuilder_.getMessageOrBuilder();
      } else {
        return content_ == null ?
            com.google.protobuf.Struct.getDefaultInstance() : content_;
      }
    }
    /**
     * <pre>
     * 数据字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content = 8;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> 
        getContentFieldBuilder() {
      if (contentBuilder_ == null) {
        contentBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder>(
                getContent(),
                getParentForChildren(),
                isClean());
        content_ = null;
      }
      return contentBuilder_;
    }

    private com.google.protobuf.Struct contentFrom_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> contentFromBuilder_;
    /**
     * <pre>
     *数据原先的字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content_from = 21;</code>
     * @return Whether the contentFrom field is set.
     */
    public boolean hasContentFrom() {
      return contentFromBuilder_ != null || contentFrom_ != null;
    }
    /**
     * <pre>
     *数据原先的字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content_from = 21;</code>
     * @return The contentFrom.
     */
    public com.google.protobuf.Struct getContentFrom() {
      if (contentFromBuilder_ == null) {
        return contentFrom_ == null ? com.google.protobuf.Struct.getDefaultInstance() : contentFrom_;
      } else {
        return contentFromBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     *数据原先的字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content_from = 21;</code>
     */
    public Builder setContentFrom(com.google.protobuf.Struct value) {
      if (contentFromBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        contentFrom_ = value;
        onChanged();
      } else {
        contentFromBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     *数据原先的字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content_from = 21;</code>
     */
    public Builder setContentFrom(
        com.google.protobuf.Struct.Builder builderForValue) {
      if (contentFromBuilder_ == null) {
        contentFrom_ = builderForValue.build();
        onChanged();
      } else {
        contentFromBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     *数据原先的字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content_from = 21;</code>
     */
    public Builder mergeContentFrom(com.google.protobuf.Struct value) {
      if (contentFromBuilder_ == null) {
        if (contentFrom_ != null) {
          contentFrom_ =
            com.google.protobuf.Struct.newBuilder(contentFrom_).mergeFrom(value).buildPartial();
        } else {
          contentFrom_ = value;
        }
        onChanged();
      } else {
        contentFromBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     *数据原先的字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content_from = 21;</code>
     */
    public Builder clearContentFrom() {
      if (contentFromBuilder_ == null) {
        contentFrom_ = null;
        onChanged();
      } else {
        contentFrom_ = null;
        contentFromBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     *数据原先的字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content_from = 21;</code>
     */
    public com.google.protobuf.Struct.Builder getContentFromBuilder() {
      
      onChanged();
      return getContentFromFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     *数据原先的字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content_from = 21;</code>
     */
    public com.google.protobuf.StructOrBuilder getContentFromOrBuilder() {
      if (contentFromBuilder_ != null) {
        return contentFromBuilder_.getMessageOrBuilder();
      } else {
        return contentFrom_ == null ?
            com.google.protobuf.Struct.getDefaultInstance() : contentFrom_;
      }
    }
    /**
     * <pre>
     *数据原先的字段内容
     * </pre>
     *
     * <code>.google.protobuf.Struct content_from = 21;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> 
        getContentFromFieldBuilder() {
      if (contentFromBuilder_ == null) {
        contentFromBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder>(
                getContentFrom(),
                getParentForChildren(),
                isClean());
        contentFrom_ = null;
      }
      return contentFromBuilder_;
    }

    private java.lang.Object status_ = "";
    /**
     * <pre>
     * task 状态
     * </pre>
     *
     * <code>string status = 9;</code>
     * @return The status.
     */
    public java.lang.String getStatus() {
      java.lang.Object ref = status_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        status_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * task 状态
     * </pre>
     *
     * <code>string status = 9;</code>
     * @return The bytes for status.
     */
    public com.google.protobuf.ByteString
        getStatusBytes() {
      java.lang.Object ref = status_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        status_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * task 状态
     * </pre>
     *
     * <code>string status = 9;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      status_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * task 状态
     * </pre>
     *
     * <code>string status = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      
      status_ = getDefaultInstance().getStatus();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * task 状态
     * </pre>
     *
     * <code>string status = 9;</code>
     * @param value The bytes for status to set.
     * @return This builder for chaining.
     */
    public Builder setStatusBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      status_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object processStatus_ = "";
    /**
     * <pre>
     * 数据被批处理状态
     * </pre>
     *
     * <code>string process_status = 10;</code>
     * @return The processStatus.
     */
    public java.lang.String getProcessStatus() {
      java.lang.Object ref = processStatus_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        processStatus_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 数据被批处理状态
     * </pre>
     *
     * <code>string process_status = 10;</code>
     * @return The bytes for processStatus.
     */
    public com.google.protobuf.ByteString
        getProcessStatusBytes() {
      java.lang.Object ref = processStatus_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        processStatus_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 数据被批处理状态
     * </pre>
     *
     * <code>string process_status = 10;</code>
     * @param value The processStatus to set.
     * @return This builder for chaining.
     */
    public Builder setProcessStatus(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      processStatus_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 数据被批处理状态
     * </pre>
     *
     * <code>string process_status = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearProcessStatus() {
      
      processStatus_ = getDefaultInstance().getProcessStatus();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 数据被批处理状态
     * </pre>
     *
     * <code>string process_status = 10;</code>
     * @param value The bytes for processStatus to set.
     * @return This builder for chaining.
     */
    public Builder setProcessStatusBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      processStatus_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object action_ = "";
    /**
     * <pre>
     * task动作
     * </pre>
     *
     * <code>string action = 11;</code>
     * @return The action.
     */
    public java.lang.String getAction() {
      java.lang.Object ref = action_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        action_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * task动作
     * </pre>
     *
     * <code>string action = 11;</code>
     * @return The bytes for action.
     */
    public com.google.protobuf.ByteString
        getActionBytes() {
      java.lang.Object ref = action_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        action_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * task动作
     * </pre>
     *
     * <code>string action = 11;</code>
     * @param value The action to set.
     * @return This builder for chaining.
     */
    public Builder setAction(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      action_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * task动作
     * </pre>
     *
     * <code>string action = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearAction() {
      
      action_ = getDefaultInstance().getAction();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * task动作
     * </pre>
     *
     * <code>string action = 11;</code>
     * @param value The bytes for action to set.
     * @return This builder for chaining.
     */
    public Builder setActionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      action_ = value;
      onChanged();
      return this;
    }

    private boolean immediate_ ;
    /**
     * <pre>
     * 是否立即执行
     * </pre>
     *
     * <code>bool immediate = 12;</code>
     * @return The immediate.
     */
    @java.lang.Override
    public boolean getImmediate() {
      return immediate_;
    }
    /**
     * <pre>
     * 是否立即执行
     * </pre>
     *
     * <code>bool immediate = 12;</code>
     * @param value The immediate to set.
     * @return This builder for chaining.
     */
    public Builder setImmediate(boolean value) {
      
      immediate_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 是否立即执行
     * </pre>
     *
     * <code>bool immediate = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearImmediate() {
      
      immediate_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object start_ = "";
    /**
     * <pre>
     * 开始执行时间
     * </pre>
     *
     * <code>string start = 13;</code>
     * @return The start.
     */
    public java.lang.String getStart() {
      java.lang.Object ref = start_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        start_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 开始执行时间
     * </pre>
     *
     * <code>string start = 13;</code>
     * @return The bytes for start.
     */
    public com.google.protobuf.ByteString
        getStartBytes() {
      java.lang.Object ref = start_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        start_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 开始执行时间
     * </pre>
     *
     * <code>string start = 13;</code>
     * @param value The start to set.
     * @return This builder for chaining.
     */
    public Builder setStart(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      start_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 开始执行时间
     * </pre>
     *
     * <code>string start = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearStart() {
      
      start_ = getDefaultInstance().getStart();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 开始执行时间
     * </pre>
     *
     * <code>string start = 13;</code>
     * @param value The bytes for start to set.
     * @return This builder for chaining.
     */
    public Builder setStartBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      start_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object lastStart_ = "";
    /**
     * <pre>
     * 最后一次开始执行时间
     * </pre>
     *
     * <code>string last_start = 14;</code>
     * @return The lastStart.
     */
    public java.lang.String getLastStart() {
      java.lang.Object ref = lastStart_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        lastStart_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 最后一次开始执行时间
     * </pre>
     *
     * <code>string last_start = 14;</code>
     * @return The bytes for lastStart.
     */
    public com.google.protobuf.ByteString
        getLastStartBytes() {
      java.lang.Object ref = lastStart_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        lastStart_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 最后一次开始执行时间
     * </pre>
     *
     * <code>string last_start = 14;</code>
     * @param value The lastStart to set.
     * @return This builder for chaining.
     */
    public Builder setLastStart(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      lastStart_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 最后一次开始执行时间
     * </pre>
     *
     * <code>string last_start = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearLastStart() {
      
      lastStart_ = getDefaultInstance().getLastStart();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 最后一次开始执行时间
     * </pre>
     *
     * <code>string last_start = 14;</code>
     * @param value The bytes for lastStart to set.
     * @return This builder for chaining.
     */
    public Builder setLastStartBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      lastStart_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object lastEnd_ = "";
    /**
     * <pre>
     * 最后一次结束时间
     * </pre>
     *
     * <code>string last_end = 15;</code>
     * @return The lastEnd.
     */
    public java.lang.String getLastEnd() {
      java.lang.Object ref = lastEnd_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        lastEnd_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 最后一次结束时间
     * </pre>
     *
     * <code>string last_end = 15;</code>
     * @return The bytes for lastEnd.
     */
    public com.google.protobuf.ByteString
        getLastEndBytes() {
      java.lang.Object ref = lastEnd_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        lastEnd_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 最后一次结束时间
     * </pre>
     *
     * <code>string last_end = 15;</code>
     * @param value The lastEnd to set.
     * @return This builder for chaining.
     */
    public Builder setLastEnd(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      lastEnd_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 最后一次结束时间
     * </pre>
     *
     * <code>string last_end = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearLastEnd() {
      
      lastEnd_ = getDefaultInstance().getLastEnd();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 最后一次结束时间
     * </pre>
     *
     * <code>string last_end = 15;</code>
     * @param value The bytes for lastEnd to set.
     * @return This builder for chaining.
     */
    public Builder setLastEndBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      lastEnd_ = value;
      onChanged();
      return this;
    }

    private int retry_ ;
    /**
     * <pre>
     * 重试次数
     * </pre>
     *
     * <code>int32 retry = 16;</code>
     * @return The retry.
     */
    @java.lang.Override
    public int getRetry() {
      return retry_;
    }
    /**
     * <pre>
     * 重试次数
     * </pre>
     *
     * <code>int32 retry = 16;</code>
     * @param value The retry to set.
     * @return This builder for chaining.
     */
    public Builder setRetry(int value) {
      
      retry_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 重试次数
     * </pre>
     *
     * <code>int32 retry = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearRetry() {
      
      retry_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object created_ = "";
    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>string created = 17;</code>
     * @return The created.
     */
    public java.lang.String getCreated() {
      java.lang.Object ref = created_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        created_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>string created = 17;</code>
     * @return The bytes for created.
     */
    public com.google.protobuf.ByteString
        getCreatedBytes() {
      java.lang.Object ref = created_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        created_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>string created = 17;</code>
     * @param value The created to set.
     * @return This builder for chaining.
     */
    public Builder setCreated(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      created_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>string created = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearCreated() {
      
      created_ = getDefaultInstance().getCreated();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 创建时间
     * </pre>
     *
     * <code>string created = 17;</code>
     * @param value The bytes for created to set.
     * @return This builder for chaining.
     */
    public Builder setCreatedBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      created_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object updated_ = "";
    /**
     * <pre>
     * 最后一次修改时间
     * </pre>
     *
     * <code>string updated = 18;</code>
     * @return The updated.
     */
    public java.lang.String getUpdated() {
      java.lang.Object ref = updated_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        updated_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 最后一次修改时间
     * </pre>
     *
     * <code>string updated = 18;</code>
     * @return The bytes for updated.
     */
    public com.google.protobuf.ByteString
        getUpdatedBytes() {
      java.lang.Object ref = updated_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        updated_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 最后一次修改时间
     * </pre>
     *
     * <code>string updated = 18;</code>
     * @param value The updated to set.
     * @return This builder for chaining.
     */
    public Builder setUpdated(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      updated_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 最后一次修改时间
     * </pre>
     *
     * <code>string updated = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearUpdated() {
      
      updated_ = getDefaultInstance().getUpdated();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 最后一次修改时间
     * </pre>
     *
     * <code>string updated = 18;</code>
     * @param value The bytes for updated to set.
     * @return This builder for chaining.
     */
    public Builder setUpdatedBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      updated_ = value;
      onChanged();
      return this;
    }

    private long createdBy_ ;
    /**
     * <pre>
     * 创建者
     * </pre>
     *
     * <code>uint64 created_by = 19;</code>
     * @return The createdBy.
     */
    @java.lang.Override
    public long getCreatedBy() {
      return createdBy_;
    }
    /**
     * <pre>
     * 创建者
     * </pre>
     *
     * <code>uint64 created_by = 19;</code>
     * @param value The createdBy to set.
     * @return This builder for chaining.
     */
    public Builder setCreatedBy(long value) {
      
      createdBy_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 创建者
     * </pre>
     *
     * <code>uint64 created_by = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearCreatedBy() {
      
      createdBy_ = 0L;
      onChanged();
      return this;
    }

    private long updatedBy_ ;
    /**
     * <pre>
     * 最后一次修改者
     * </pre>
     *
     * <code>uint64 updated_by = 20;</code>
     * @return The updatedBy.
     */
    @java.lang.Override
    public long getUpdatedBy() {
      return updatedBy_;
    }
    /**
     * <pre>
     * 最后一次修改者
     * </pre>
     *
     * <code>uint64 updated_by = 20;</code>
     * @param value The updatedBy to set.
     * @return This builder for chaining.
     */
    public Builder setUpdatedBy(long value) {
      
      updatedBy_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 最后一次修改者
     * </pre>
     *
     * <code>uint64 updated_by = 20;</code>
     * @return This builder for chaining.
     */
    public Builder clearUpdatedBy() {
      
      updatedBy_ = 0L;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:entity.EntityTask)
  }

  // @@protoc_insertion_point(class_scope:entity.EntityTask)
  private static final cn.hexcloud.pbis.common.service.integration.metadata.EntityTask DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.metadata.EntityTask();
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.EntityTask getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<EntityTask>
      PARSER = new com.google.protobuf.AbstractParser<EntityTask>() {
    @java.lang.Override
    public EntityTask parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new EntityTask(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<EntityTask> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<EntityTask> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.metadata.EntityTask getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

