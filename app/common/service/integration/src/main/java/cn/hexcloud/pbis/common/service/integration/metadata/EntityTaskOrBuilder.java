// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface EntityTaskOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.EntityTask)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * task id
   * </pre>
   *
   * <code>uint64 id = 1;</code>
   * @return The id.
   */
  long getId();

  /**
   * <pre>
   * 商户id
   * </pre>
   *
   * <code>uint64 partner_id = 2;</code>
   * @return The partnerId.
   */
  long getPartnerId();

  /**
   * <pre>
   * scope_id
   * </pre>
   *
   * <code>uint64 scope_id = 3;</code>
   * @return The scopeId.
   */
  long getScopeId();

  /**
   * <pre>
   * job id
   * </pre>
   *
   * <code>uint64 job_id = 4;</code>
   * @return The jobId.
   */
  long getJobId();

  /**
   * <pre>
   * schema type(entity, relation)
   * </pre>
   *
   * <code>string schema_type = 5;</code>
   * @return The schemaType.
   */
  java.lang.String getSchemaType();
  /**
   * <pre>
   * schema type(entity, relation)
   * </pre>
   *
   * <code>string schema_type = 5;</code>
   * @return The bytes for schemaType.
   */
  com.google.protobuf.ByteString
      getSchemaTypeBytes();

  /**
   * <pre>
   * task 名称
   * </pre>
   *
   * <code>string name = 6;</code>
   * @return The name.
   */
  java.lang.String getName();
  /**
   * <pre>
   * task 名称
   * </pre>
   *
   * <code>string name = 6;</code>
   * @return The bytes for name.
   */
  com.google.protobuf.ByteString
      getNameBytes();

  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>uint64 record_id = 7;</code>
   * @return The recordId.
   */
  long getRecordId();

  /**
   * <pre>
   * 数据字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct content = 8;</code>
   * @return Whether the content field is set.
   */
  boolean hasContent();
  /**
   * <pre>
   * 数据字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct content = 8;</code>
   * @return The content.
   */
  com.google.protobuf.Struct getContent();
  /**
   * <pre>
   * 数据字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct content = 8;</code>
   */
  com.google.protobuf.StructOrBuilder getContentOrBuilder();

  /**
   * <pre>
   *数据原先的字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct content_from = 21;</code>
   * @return Whether the contentFrom field is set.
   */
  boolean hasContentFrom();
  /**
   * <pre>
   *数据原先的字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct content_from = 21;</code>
   * @return The contentFrom.
   */
  com.google.protobuf.Struct getContentFrom();
  /**
   * <pre>
   *数据原先的字段内容
   * </pre>
   *
   * <code>.google.protobuf.Struct content_from = 21;</code>
   */
  com.google.protobuf.StructOrBuilder getContentFromOrBuilder();

  /**
   * <pre>
   * task 状态
   * </pre>
   *
   * <code>string status = 9;</code>
   * @return The status.
   */
  java.lang.String getStatus();
  /**
   * <pre>
   * task 状态
   * </pre>
   *
   * <code>string status = 9;</code>
   * @return The bytes for status.
   */
  com.google.protobuf.ByteString
      getStatusBytes();

  /**
   * <pre>
   * 数据被批处理状态
   * </pre>
   *
   * <code>string process_status = 10;</code>
   * @return The processStatus.
   */
  java.lang.String getProcessStatus();
  /**
   * <pre>
   * 数据被批处理状态
   * </pre>
   *
   * <code>string process_status = 10;</code>
   * @return The bytes for processStatus.
   */
  com.google.protobuf.ByteString
      getProcessStatusBytes();

  /**
   * <pre>
   * task动作
   * </pre>
   *
   * <code>string action = 11;</code>
   * @return The action.
   */
  java.lang.String getAction();
  /**
   * <pre>
   * task动作
   * </pre>
   *
   * <code>string action = 11;</code>
   * @return The bytes for action.
   */
  com.google.protobuf.ByteString
      getActionBytes();

  /**
   * <pre>
   * 是否立即执行
   * </pre>
   *
   * <code>bool immediate = 12;</code>
   * @return The immediate.
   */
  boolean getImmediate();

  /**
   * <pre>
   * 开始执行时间
   * </pre>
   *
   * <code>string start = 13;</code>
   * @return The start.
   */
  java.lang.String getStart();
  /**
   * <pre>
   * 开始执行时间
   * </pre>
   *
   * <code>string start = 13;</code>
   * @return The bytes for start.
   */
  com.google.protobuf.ByteString
      getStartBytes();

  /**
   * <pre>
   * 最后一次开始执行时间
   * </pre>
   *
   * <code>string last_start = 14;</code>
   * @return The lastStart.
   */
  java.lang.String getLastStart();
  /**
   * <pre>
   * 最后一次开始执行时间
   * </pre>
   *
   * <code>string last_start = 14;</code>
   * @return The bytes for lastStart.
   */
  com.google.protobuf.ByteString
      getLastStartBytes();

  /**
   * <pre>
   * 最后一次结束时间
   * </pre>
   *
   * <code>string last_end = 15;</code>
   * @return The lastEnd.
   */
  java.lang.String getLastEnd();
  /**
   * <pre>
   * 最后一次结束时间
   * </pre>
   *
   * <code>string last_end = 15;</code>
   * @return The bytes for lastEnd.
   */
  com.google.protobuf.ByteString
      getLastEndBytes();

  /**
   * <pre>
   * 重试次数
   * </pre>
   *
   * <code>int32 retry = 16;</code>
   * @return The retry.
   */
  int getRetry();

  /**
   * <pre>
   * 创建时间
   * </pre>
   *
   * <code>string created = 17;</code>
   * @return The created.
   */
  java.lang.String getCreated();
  /**
   * <pre>
   * 创建时间
   * </pre>
   *
   * <code>string created = 17;</code>
   * @return The bytes for created.
   */
  com.google.protobuf.ByteString
      getCreatedBytes();

  /**
   * <pre>
   * 最后一次修改时间
   * </pre>
   *
   * <code>string updated = 18;</code>
   * @return The updated.
   */
  java.lang.String getUpdated();
  /**
   * <pre>
   * 最后一次修改时间
   * </pre>
   *
   * <code>string updated = 18;</code>
   * @return The bytes for updated.
   */
  com.google.protobuf.ByteString
      getUpdatedBytes();

  /**
   * <pre>
   * 创建者
   * </pre>
   *
   * <code>uint64 created_by = 19;</code>
   * @return The createdBy.
   */
  long getCreatedBy();

  /**
   * <pre>
   * 最后一次修改者
   * </pre>
   *
   * <code>uint64 updated_by = 20;</code>
   * @return The updatedBy.
   */
  long getUpdatedBy();
}
