// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface GetChildrenEntityIdsRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.GetChildrenEntityIdsRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 1;</code>
   * @return The schemaName.
   */
  java.lang.String getSchemaName();
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 1;</code>
   * @return The bytes for schemaName.
   */
  com.google.protobuf.ByteString
      getSchemaNameBytes();

  /**
   * <pre>
   * 按id列表查询
   * </pre>
   *
   * <code>repeated uint64 ids = 2;</code>
   * @return A list containing the ids.
   */
  java.util.List<java.lang.Long> getIdsList();
  /**
   * <pre>
   * 按id列表查询
   * </pre>
   *
   * <code>repeated uint64 ids = 2;</code>
   * @return The count of ids.
   */
  int getIdsCount();
  /**
   * <pre>
   * 按id列表查询
   * </pre>
   *
   * <code>repeated uint64 ids = 2;</code>
   * @param index The index of the element to return.
   * @return The ids at the given index.
   */
  long getIds(int index);
}
