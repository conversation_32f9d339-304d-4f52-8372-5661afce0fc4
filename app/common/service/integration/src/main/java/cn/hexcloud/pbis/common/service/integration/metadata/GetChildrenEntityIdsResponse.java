// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

/**
 * Protobuf type {@code entity.GetChildrenEntityIdsResponse}
 */
public final class GetChildrenEntityIdsResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:entity.GetChildrenEntityIdsResponse)
    GetChildrenEntityIdsResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use GetChildrenEntityIdsResponse.newBuilder() to construct.
  private GetChildrenEntityIdsResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private GetChildrenEntityIdsResponse() {
    childrenIds_ = emptyLongList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new GetChildrenEntityIdsResponse();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private GetChildrenEntityIdsResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              childrenIds_ = newLongList();
              mutable_bitField0_ |= 0x00000001;
            }
            childrenIds_.addLong(input.readUInt64());
            break;
          }
          case 10: {
            int length = input.readRawVarint32();
            int limit = input.pushLimit(length);
            if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
              childrenIds_ = newLongList();
              mutable_bitField0_ |= 0x00000001;
            }
            while (input.getBytesUntilLimit() > 0) {
              childrenIds_.addLong(input.readUInt64());
            }
            input.popLimit(limit);
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        childrenIds_.makeImmutable(); // C
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_GetChildrenEntityIdsResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_GetChildrenEntityIdsResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse.class, cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse.Builder.class);
  }

  public static final int CHILDREN_IDS_FIELD_NUMBER = 1;
  private com.google.protobuf.Internal.LongList childrenIds_;
  /**
   * <code>repeated uint64 children_ids = 1;</code>
   * @return A list containing the childrenIds.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getChildrenIdsList() {
    return childrenIds_;
  }
  /**
   * <code>repeated uint64 children_ids = 1;</code>
   * @return The count of childrenIds.
   */
  public int getChildrenIdsCount() {
    return childrenIds_.size();
  }
  /**
   * <code>repeated uint64 children_ids = 1;</code>
   * @param index The index of the element to return.
   * @return The childrenIds at the given index.
   */
  public long getChildrenIds(int index) {
    return childrenIds_.getLong(index);
  }
  private int childrenIdsMemoizedSerializedSize = -1;

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    getSerializedSize();
    if (getChildrenIdsList().size() > 0) {
      output.writeUInt32NoTag(10);
      output.writeUInt32NoTag(childrenIdsMemoizedSerializedSize);
    }
    for (int i = 0; i < childrenIds_.size(); i++) {
      output.writeUInt64NoTag(childrenIds_.getLong(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    {
      int dataSize = 0;
      for (int i = 0; i < childrenIds_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeUInt64SizeNoTag(childrenIds_.getLong(i));
      }
      size += dataSize;
      if (!getChildrenIdsList().isEmpty()) {
        size += 1;
        size += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(dataSize);
      }
      childrenIdsMemoizedSerializedSize = dataSize;
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse other = (cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse) obj;

    if (!getChildrenIdsList()
        .equals(other.getChildrenIdsList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getChildrenIdsCount() > 0) {
      hash = (37 * hash) + CHILDREN_IDS_FIELD_NUMBER;
      hash = (53 * hash) + getChildrenIdsList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code entity.GetChildrenEntityIdsResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:entity.GetChildrenEntityIdsResponse)
      cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_GetChildrenEntityIdsResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_GetChildrenEntityIdsResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse.class, cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      childrenIds_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000001);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_GetChildrenEntityIdsResponse_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse build() {
      cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse buildPartial() {
      cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse result = new cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse(this);
      int from_bitField0_ = bitField0_;
      if (((bitField0_ & 0x00000001) != 0)) {
        childrenIds_.makeImmutable();
        bitField0_ = (bitField0_ & ~0x00000001);
      }
      result.childrenIds_ = childrenIds_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse other) {
      if (other == cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse.getDefaultInstance()) return this;
      if (!other.childrenIds_.isEmpty()) {
        if (childrenIds_.isEmpty()) {
          childrenIds_ = other.childrenIds_;
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          ensureChildrenIdsIsMutable();
          childrenIds_.addAll(other.childrenIds_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private com.google.protobuf.Internal.LongList childrenIds_ = emptyLongList();
    private void ensureChildrenIdsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        childrenIds_ = mutableCopy(childrenIds_);
        bitField0_ |= 0x00000001;
       }
    }
    /**
     * <code>repeated uint64 children_ids = 1;</code>
     * @return A list containing the childrenIds.
     */
    public java.util.List<java.lang.Long>
        getChildrenIdsList() {
      return ((bitField0_ & 0x00000001) != 0) ?
               java.util.Collections.unmodifiableList(childrenIds_) : childrenIds_;
    }
    /**
     * <code>repeated uint64 children_ids = 1;</code>
     * @return The count of childrenIds.
     */
    public int getChildrenIdsCount() {
      return childrenIds_.size();
    }
    /**
     * <code>repeated uint64 children_ids = 1;</code>
     * @param index The index of the element to return.
     * @return The childrenIds at the given index.
     */
    public long getChildrenIds(int index) {
      return childrenIds_.getLong(index);
    }
    /**
     * <code>repeated uint64 children_ids = 1;</code>
     * @param index The index to set the value at.
     * @param value The childrenIds to set.
     * @return This builder for chaining.
     */
    public Builder setChildrenIds(
        int index, long value) {
      ensureChildrenIdsIsMutable();
      childrenIds_.setLong(index, value);
      onChanged();
      return this;
    }
    /**
     * <code>repeated uint64 children_ids = 1;</code>
     * @param value The childrenIds to add.
     * @return This builder for chaining.
     */
    public Builder addChildrenIds(long value) {
      ensureChildrenIdsIsMutable();
      childrenIds_.addLong(value);
      onChanged();
      return this;
    }
    /**
     * <code>repeated uint64 children_ids = 1;</code>
     * @param values The childrenIds to add.
     * @return This builder for chaining.
     */
    public Builder addAllChildrenIds(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureChildrenIdsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, childrenIds_);
      onChanged();
      return this;
    }
    /**
     * <code>repeated uint64 children_ids = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearChildrenIds() {
      childrenIds_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:entity.GetChildrenEntityIdsResponse)
  }

  // @@protoc_insertion_point(class_scope:entity.GetChildrenEntityIdsResponse)
  private static final cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse();
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GetChildrenEntityIdsResponse>
      PARSER = new com.google.protobuf.AbstractParser<GetChildrenEntityIdsResponse>() {
    @java.lang.Override
    public GetChildrenEntityIdsResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new GetChildrenEntityIdsResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<GetChildrenEntityIdsResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GetChildrenEntityIdsResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.metadata.GetChildrenEntityIdsResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

