// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface GetChildrenEntityIdsResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.GetChildrenEntityIdsResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated uint64 children_ids = 1;</code>
   * @return A list containing the childrenIds.
   */
  java.util.List<java.lang.Long> getChildrenIdsList();
  /**
   * <code>repeated uint64 children_ids = 1;</code>
   * @return The count of childrenIds.
   */
  int getChildrenIdsCount();
  /**
   * <code>repeated uint64 children_ids = 1;</code>
   * @param index The index of the element to return.
   * @return The childrenIds at the given index.
   */
  long getChildrenIds(int index);
}
