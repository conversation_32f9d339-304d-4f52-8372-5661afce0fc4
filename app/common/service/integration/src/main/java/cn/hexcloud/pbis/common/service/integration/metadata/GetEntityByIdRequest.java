// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

/**
 * Protobuf type {@code entity.GetEntityByIdRequest}
 */
public final class GetEntityByIdRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:entity.GetEntityByIdRequest)
    GetEntityByIdRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use GetEntityByIdRequest.newBuilder() to construct.
  private GetEntityByIdRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private GetEntityByIdRequest() {
    schemaName_ = "";
    code_ = "";
    relation_ = "";
    returnFields_ = "";
    lan_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new GetEntityByIdRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private GetEntityByIdRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            id_ = input.readUInt64();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            schemaName_ = s;
            break;
          }
          case 24: {

            includeState_ = input.readBool();
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            code_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            relation_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            returnFields_ = s;
            break;
          }
          case 56: {

            includePendingChanges_ = input.readBool();
            break;
          }
          case 64: {

            includePendingRecord_ = input.readBool();
            break;
          }
          case 72: {

            includeParents_ = input.readBool();
            break;
          }
          case 82: {
            java.lang.String s = input.readStringRequireUtf8();

            lan_ = s;
            break;
          }
          case 88: {

            includeAllLocalizations_ = input.readBool();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_GetEntityByIdRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_GetEntityByIdRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest.class, cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  private long id_;
  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>uint64 id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public long getId() {
    return id_;
  }

  public static final int SCHEMA_NAME_FIELD_NUMBER = 2;
  private volatile java.lang.Object schemaName_;
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 2;</code>
   * @return The schemaName.
   */
  @java.lang.Override
  public java.lang.String getSchemaName() {
    java.lang.Object ref = schemaName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      schemaName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 2;</code>
   * @return The bytes for schemaName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSchemaNameBytes() {
    java.lang.Object ref = schemaName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      schemaName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INCLUDE_STATE_FIELD_NUMBER = 3;
  private boolean includeState_;
  /**
   * <pre>
   * 是否包含数据状态
   * </pre>
   *
   * <code>bool include_state = 3;</code>
   * @return The includeState.
   */
  @java.lang.Override
  public boolean getIncludeState() {
    return includeState_;
  }

  public static final int CODE_FIELD_NUMBER = 4;
  private volatile java.lang.Object code_;
  /**
   * <pre>
   * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string code = 4;</code>
   * @return The code.
   */
  @java.lang.Override
  public java.lang.String getCode() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      code_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string code = 4;</code>
   * @return The bytes for code.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCodeBytes() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      code_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RELATION_FIELD_NUMBER = 5;
  private volatile java.lang.Object relation_;
  /**
   * <pre>
   * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string relation = 5;</code>
   * @return The relation.
   */
  @java.lang.Override
  public java.lang.String getRelation() {
    java.lang.Object ref = relation_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      relation_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string relation = 5;</code>
   * @return The bytes for relation.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRelationBytes() {
    java.lang.Object ref = relation_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      relation_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RETURN_FIELDS_FIELD_NUMBER = 6;
  private volatile java.lang.Object returnFields_;
  /**
   * <pre>
   * 除code和relation之外需要返回的字段, 多个以逗号隔开
   * </pre>
   *
   * <code>string return_fields = 6;</code>
   * @return The returnFields.
   */
  @java.lang.Override
  public java.lang.String getReturnFields() {
    java.lang.Object ref = returnFields_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      returnFields_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 除code和relation之外需要返回的字段, 多个以逗号隔开
   * </pre>
   *
   * <code>string return_fields = 6;</code>
   * @return The bytes for returnFields.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getReturnFieldsBytes() {
    java.lang.Object ref = returnFields_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      returnFields_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INCLUDE_PENDING_CHANGES_FIELD_NUMBER = 7;
  private boolean includePendingChanges_;
  /**
   * <pre>
   * include_pending_changes=true时, 如果相关记录包含pending的修改属性,
   * 则会获取修改属性attach到返回记录中pengding_changes字段
   * </pre>
   *
   * <code>bool include_pending_changes = 7;</code>
   * @return The includePendingChanges.
   */
  @java.lang.Override
  public boolean getIncludePendingChanges() {
    return includePendingChanges_;
  }

  public static final int INCLUDE_PENDING_RECORD_FIELD_NUMBER = 8;
  private boolean includePendingRecord_;
  /**
   * <pre>
   * include_pending_record=true时, 如果相关记录包含pending的修改属性,
   * 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的pending_record字段
   * </pre>
   *
   * <code>bool include_pending_record = 8;</code>
   * @return The includePendingRecord.
   */
  @java.lang.Override
  public boolean getIncludePendingRecord() {
    return includePendingRecord_;
  }

  public static final int INCLUDE_PARENTS_FIELD_NUMBER = 9;
  private boolean includeParents_;
  /**
   * <pre>
   * include_parents=true返回所有父级节点
   * </pre>
   *
   * <code>bool include_parents = 9;</code>
   * @return The includeParents.
   */
  @java.lang.Override
  public boolean getIncludeParents() {
    return includeParents_;
  }

  public static final int LAN_FIELD_NUMBER = 10;
  private volatile java.lang.Object lan_;
  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 10;</code>
   * @return The lan.
   */
  @java.lang.Override
  public java.lang.String getLan() {
    java.lang.Object ref = lan_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      lan_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 10;</code>
   * @return The bytes for lan.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLanBytes() {
    java.lang.Object ref = lan_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      lan_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INCLUDE_ALL_LOCALIZATIONS_FIELD_NUMBER = 11;
  private boolean includeAllLocalizations_;
  /**
   * <pre>
   * 是否包含所有本地化信息
   * </pre>
   *
   * <code>bool include_all_localizations = 11;</code>
   * @return The includeAllLocalizations.
   */
  @java.lang.Override
  public boolean getIncludeAllLocalizations() {
    return includeAllLocalizations_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (id_ != 0L) {
      output.writeUInt64(1, id_);
    }
    if (!getSchemaNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, schemaName_);
    }
    if (includeState_ != false) {
      output.writeBool(3, includeState_);
    }
    if (!getCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, code_);
    }
    if (!getRelationBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, relation_);
    }
    if (!getReturnFieldsBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, returnFields_);
    }
    if (includePendingChanges_ != false) {
      output.writeBool(7, includePendingChanges_);
    }
    if (includePendingRecord_ != false) {
      output.writeBool(8, includePendingRecord_);
    }
    if (includeParents_ != false) {
      output.writeBool(9, includeParents_);
    }
    if (!getLanBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, lan_);
    }
    if (includeAllLocalizations_ != false) {
      output.writeBool(11, includeAllLocalizations_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (id_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, id_);
    }
    if (!getSchemaNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, schemaName_);
    }
    if (includeState_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(3, includeState_);
    }
    if (!getCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, code_);
    }
    if (!getRelationBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, relation_);
    }
    if (!getReturnFieldsBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, returnFields_);
    }
    if (includePendingChanges_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(7, includePendingChanges_);
    }
    if (includePendingRecord_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(8, includePendingRecord_);
    }
    if (includeParents_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(9, includeParents_);
    }
    if (!getLanBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, lan_);
    }
    if (includeAllLocalizations_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(11, includeAllLocalizations_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest other = (cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest) obj;

    if (getId()
        != other.getId()) return false;
    if (!getSchemaName()
        .equals(other.getSchemaName())) return false;
    if (getIncludeState()
        != other.getIncludeState()) return false;
    if (!getCode()
        .equals(other.getCode())) return false;
    if (!getRelation()
        .equals(other.getRelation())) return false;
    if (!getReturnFields()
        .equals(other.getReturnFields())) return false;
    if (getIncludePendingChanges()
        != other.getIncludePendingChanges()) return false;
    if (getIncludePendingRecord()
        != other.getIncludePendingRecord()) return false;
    if (getIncludeParents()
        != other.getIncludeParents()) return false;
    if (!getLan()
        .equals(other.getLan())) return false;
    if (getIncludeAllLocalizations()
        != other.getIncludeAllLocalizations()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getId());
    hash = (37 * hash) + SCHEMA_NAME_FIELD_NUMBER;
    hash = (53 * hash) + getSchemaName().hashCode();
    hash = (37 * hash) + INCLUDE_STATE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIncludeState());
    hash = (37 * hash) + CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCode().hashCode();
    hash = (37 * hash) + RELATION_FIELD_NUMBER;
    hash = (53 * hash) + getRelation().hashCode();
    hash = (37 * hash) + RETURN_FIELDS_FIELD_NUMBER;
    hash = (53 * hash) + getReturnFields().hashCode();
    hash = (37 * hash) + INCLUDE_PENDING_CHANGES_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIncludePendingChanges());
    hash = (37 * hash) + INCLUDE_PENDING_RECORD_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIncludePendingRecord());
    hash = (37 * hash) + INCLUDE_PARENTS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIncludeParents());
    hash = (37 * hash) + LAN_FIELD_NUMBER;
    hash = (53 * hash) + getLan().hashCode();
    hash = (37 * hash) + INCLUDE_ALL_LOCALIZATIONS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIncludeAllLocalizations());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code entity.GetEntityByIdRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:entity.GetEntityByIdRequest)
      cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_GetEntityByIdRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_GetEntityByIdRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest.class, cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      id_ = 0L;

      schemaName_ = "";

      includeState_ = false;

      code_ = "";

      relation_ = "";

      returnFields_ = "";

      includePendingChanges_ = false;

      includePendingRecord_ = false;

      includeParents_ = false;

      lan_ = "";

      includeAllLocalizations_ = false;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_GetEntityByIdRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest build() {
      cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest buildPartial() {
      cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest result = new cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest(this);
      result.id_ = id_;
      result.schemaName_ = schemaName_;
      result.includeState_ = includeState_;
      result.code_ = code_;
      result.relation_ = relation_;
      result.returnFields_ = returnFields_;
      result.includePendingChanges_ = includePendingChanges_;
      result.includePendingRecord_ = includePendingRecord_;
      result.includeParents_ = includeParents_;
      result.lan_ = lan_;
      result.includeAllLocalizations_ = includeAllLocalizations_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest other) {
      if (other == cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest.getDefaultInstance()) return this;
      if (other.getId() != 0L) {
        setId(other.getId());
      }
      if (!other.getSchemaName().isEmpty()) {
        schemaName_ = other.schemaName_;
        onChanged();
      }
      if (other.getIncludeState() != false) {
        setIncludeState(other.getIncludeState());
      }
      if (!other.getCode().isEmpty()) {
        code_ = other.code_;
        onChanged();
      }
      if (!other.getRelation().isEmpty()) {
        relation_ = other.relation_;
        onChanged();
      }
      if (!other.getReturnFields().isEmpty()) {
        returnFields_ = other.returnFields_;
        onChanged();
      }
      if (other.getIncludePendingChanges() != false) {
        setIncludePendingChanges(other.getIncludePendingChanges());
      }
      if (other.getIncludePendingRecord() != false) {
        setIncludePendingRecord(other.getIncludePendingRecord());
      }
      if (other.getIncludeParents() != false) {
        setIncludeParents(other.getIncludeParents());
      }
      if (!other.getLan().isEmpty()) {
        lan_ = other.lan_;
        onChanged();
      }
      if (other.getIncludeAllLocalizations() != false) {
        setIncludeAllLocalizations(other.getIncludeAllLocalizations());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long id_ ;
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>uint64 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public long getId() {
      return id_;
    }
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>uint64 id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(long value) {
      
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>uint64 id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      
      id_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object schemaName_ = "";
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 2;</code>
     * @return The schemaName.
     */
    public java.lang.String getSchemaName() {
      java.lang.Object ref = schemaName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        schemaName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 2;</code>
     * @return The bytes for schemaName.
     */
    public com.google.protobuf.ByteString
        getSchemaNameBytes() {
      java.lang.Object ref = schemaName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        schemaName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 2;</code>
     * @param value The schemaName to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      schemaName_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSchemaName() {
      
      schemaName_ = getDefaultInstance().getSchemaName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 2;</code>
     * @param value The bytes for schemaName to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      schemaName_ = value;
      onChanged();
      return this;
    }

    private boolean includeState_ ;
    /**
     * <pre>
     * 是否包含数据状态
     * </pre>
     *
     * <code>bool include_state = 3;</code>
     * @return The includeState.
     */
    @java.lang.Override
    public boolean getIncludeState() {
      return includeState_;
    }
    /**
     * <pre>
     * 是否包含数据状态
     * </pre>
     *
     * <code>bool include_state = 3;</code>
     * @param value The includeState to set.
     * @return This builder for chaining.
     */
    public Builder setIncludeState(boolean value) {
      
      includeState_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 是否包含数据状态
     * </pre>
     *
     * <code>bool include_state = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearIncludeState() {
      
      includeState_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object code_ = "";
    /**
     * <pre>
     * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string code = 4;</code>
     * @return The code.
     */
    public java.lang.String getCode() {
      java.lang.Object ref = code_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        code_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string code = 4;</code>
     * @return The bytes for code.
     */
    public com.google.protobuf.ByteString
        getCodeBytes() {
      java.lang.Object ref = code_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        code_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string code = 4;</code>
     * @param value The code to set.
     * @return This builder for chaining.
     */
    public Builder setCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      code_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string code = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearCode() {
      
      code_ = getDefaultInstance().getCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string code = 4;</code>
     * @param value The bytes for code to set.
     * @return This builder for chaining.
     */
    public Builder setCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      code_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object relation_ = "";
    /**
     * <pre>
     * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string relation = 5;</code>
     * @return The relation.
     */
    public java.lang.String getRelation() {
      java.lang.Object ref = relation_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        relation_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string relation = 5;</code>
     * @return The bytes for relation.
     */
    public com.google.protobuf.ByteString
        getRelationBytes() {
      java.lang.Object ref = relation_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        relation_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string relation = 5;</code>
     * @param value The relation to set.
     * @return This builder for chaining.
     */
    public Builder setRelation(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      relation_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string relation = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearRelation() {
      
      relation_ = getDefaultInstance().getRelation();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string relation = 5;</code>
     * @param value The bytes for relation to set.
     * @return This builder for chaining.
     */
    public Builder setRelationBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      relation_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object returnFields_ = "";
    /**
     * <pre>
     * 除code和relation之外需要返回的字段, 多个以逗号隔开
     * </pre>
     *
     * <code>string return_fields = 6;</code>
     * @return The returnFields.
     */
    public java.lang.String getReturnFields() {
      java.lang.Object ref = returnFields_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        returnFields_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 除code和relation之外需要返回的字段, 多个以逗号隔开
     * </pre>
     *
     * <code>string return_fields = 6;</code>
     * @return The bytes for returnFields.
     */
    public com.google.protobuf.ByteString
        getReturnFieldsBytes() {
      java.lang.Object ref = returnFields_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        returnFields_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 除code和relation之外需要返回的字段, 多个以逗号隔开
     * </pre>
     *
     * <code>string return_fields = 6;</code>
     * @param value The returnFields to set.
     * @return This builder for chaining.
     */
    public Builder setReturnFields(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      returnFields_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 除code和relation之外需要返回的字段, 多个以逗号隔开
     * </pre>
     *
     * <code>string return_fields = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearReturnFields() {
      
      returnFields_ = getDefaultInstance().getReturnFields();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 除code和relation之外需要返回的字段, 多个以逗号隔开
     * </pre>
     *
     * <code>string return_fields = 6;</code>
     * @param value The bytes for returnFields to set.
     * @return This builder for chaining.
     */
    public Builder setReturnFieldsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      returnFields_ = value;
      onChanged();
      return this;
    }

    private boolean includePendingChanges_ ;
    /**
     * <pre>
     * include_pending_changes=true时, 如果相关记录包含pending的修改属性,
     * 则会获取修改属性attach到返回记录中pengding_changes字段
     * </pre>
     *
     * <code>bool include_pending_changes = 7;</code>
     * @return The includePendingChanges.
     */
    @java.lang.Override
    public boolean getIncludePendingChanges() {
      return includePendingChanges_;
    }
    /**
     * <pre>
     * include_pending_changes=true时, 如果相关记录包含pending的修改属性,
     * 则会获取修改属性attach到返回记录中pengding_changes字段
     * </pre>
     *
     * <code>bool include_pending_changes = 7;</code>
     * @param value The includePendingChanges to set.
     * @return This builder for chaining.
     */
    public Builder setIncludePendingChanges(boolean value) {
      
      includePendingChanges_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * include_pending_changes=true时, 如果相关记录包含pending的修改属性,
     * 则会获取修改属性attach到返回记录中pengding_changes字段
     * </pre>
     *
     * <code>bool include_pending_changes = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearIncludePendingChanges() {
      
      includePendingChanges_ = false;
      onChanged();
      return this;
    }

    private boolean includePendingRecord_ ;
    /**
     * <pre>
     * include_pending_record=true时, 如果相关记录包含pending的修改属性,
     * 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的pending_record字段
     * </pre>
     *
     * <code>bool include_pending_record = 8;</code>
     * @return The includePendingRecord.
     */
    @java.lang.Override
    public boolean getIncludePendingRecord() {
      return includePendingRecord_;
    }
    /**
     * <pre>
     * include_pending_record=true时, 如果相关记录包含pending的修改属性,
     * 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的pending_record字段
     * </pre>
     *
     * <code>bool include_pending_record = 8;</code>
     * @param value The includePendingRecord to set.
     * @return This builder for chaining.
     */
    public Builder setIncludePendingRecord(boolean value) {
      
      includePendingRecord_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * include_pending_record=true时, 如果相关记录包含pending的修改属性,
     * 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的pending_record字段
     * </pre>
     *
     * <code>bool include_pending_record = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearIncludePendingRecord() {
      
      includePendingRecord_ = false;
      onChanged();
      return this;
    }

    private boolean includeParents_ ;
    /**
     * <pre>
     * include_parents=true返回所有父级节点
     * </pre>
     *
     * <code>bool include_parents = 9;</code>
     * @return The includeParents.
     */
    @java.lang.Override
    public boolean getIncludeParents() {
      return includeParents_;
    }
    /**
     * <pre>
     * include_parents=true返回所有父级节点
     * </pre>
     *
     * <code>bool include_parents = 9;</code>
     * @param value The includeParents to set.
     * @return This builder for chaining.
     */
    public Builder setIncludeParents(boolean value) {
      
      includeParents_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * include_parents=true返回所有父级节点
     * </pre>
     *
     * <code>bool include_parents = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearIncludeParents() {
      
      includeParents_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object lan_ = "";
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 10;</code>
     * @return The lan.
     */
    public java.lang.String getLan() {
      java.lang.Object ref = lan_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        lan_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 10;</code>
     * @return The bytes for lan.
     */
    public com.google.protobuf.ByteString
        getLanBytes() {
      java.lang.Object ref = lan_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        lan_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 10;</code>
     * @param value The lan to set.
     * @return This builder for chaining.
     */
    public Builder setLan(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      lan_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearLan() {
      
      lan_ = getDefaultInstance().getLan();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 10;</code>
     * @param value The bytes for lan to set.
     * @return This builder for chaining.
     */
    public Builder setLanBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      lan_ = value;
      onChanged();
      return this;
    }

    private boolean includeAllLocalizations_ ;
    /**
     * <pre>
     * 是否包含所有本地化信息
     * </pre>
     *
     * <code>bool include_all_localizations = 11;</code>
     * @return The includeAllLocalizations.
     */
    @java.lang.Override
    public boolean getIncludeAllLocalizations() {
      return includeAllLocalizations_;
    }
    /**
     * <pre>
     * 是否包含所有本地化信息
     * </pre>
     *
     * <code>bool include_all_localizations = 11;</code>
     * @param value The includeAllLocalizations to set.
     * @return This builder for chaining.
     */
    public Builder setIncludeAllLocalizations(boolean value) {
      
      includeAllLocalizations_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 是否包含所有本地化信息
     * </pre>
     *
     * <code>bool include_all_localizations = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearIncludeAllLocalizations() {
      
      includeAllLocalizations_ = false;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:entity.GetEntityByIdRequest)
  }

  // @@protoc_insertion_point(class_scope:entity.GetEntityByIdRequest)
  private static final cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest();
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GetEntityByIdRequest>
      PARSER = new com.google.protobuf.AbstractParser<GetEntityByIdRequest>() {
    @java.lang.Override
    public GetEntityByIdRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new GetEntityByIdRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<GetEntityByIdRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GetEntityByIdRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.metadata.GetEntityByIdRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

