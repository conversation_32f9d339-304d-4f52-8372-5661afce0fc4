// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface GetEntityByIdRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.GetEntityByIdRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>uint64 id = 1;</code>
   * @return The id.
   */
  long getId();

  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 2;</code>
   * @return The schemaName.
   */
  java.lang.String getSchemaName();
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 2;</code>
   * @return The bytes for schemaName.
   */
  com.google.protobuf.ByteString
      getSchemaNameBytes();

  /**
   * <pre>
   * 是否包含数据状态
   * </pre>
   *
   * <code>bool include_state = 3;</code>
   * @return The includeState.
   */
  boolean getIncludeState();

  /**
   * <pre>
   * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string code = 4;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <pre>
   * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string code = 4;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <pre>
   * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string relation = 5;</code>
   * @return The relation.
   */
  java.lang.String getRelation();
  /**
   * <pre>
   * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string relation = 5;</code>
   * @return The bytes for relation.
   */
  com.google.protobuf.ByteString
      getRelationBytes();

  /**
   * <pre>
   * 除code和relation之外需要返回的字段, 多个以逗号隔开
   * </pre>
   *
   * <code>string return_fields = 6;</code>
   * @return The returnFields.
   */
  java.lang.String getReturnFields();
  /**
   * <pre>
   * 除code和relation之外需要返回的字段, 多个以逗号隔开
   * </pre>
   *
   * <code>string return_fields = 6;</code>
   * @return The bytes for returnFields.
   */
  com.google.protobuf.ByteString
      getReturnFieldsBytes();

  /**
   * <pre>
   * include_pending_changes=true时, 如果相关记录包含pending的修改属性,
   * 则会获取修改属性attach到返回记录中pengding_changes字段
   * </pre>
   *
   * <code>bool include_pending_changes = 7;</code>
   * @return The includePendingChanges.
   */
  boolean getIncludePendingChanges();

  /**
   * <pre>
   * include_pending_record=true时, 如果相关记录包含pending的修改属性,
   * 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的pending_record字段
   * </pre>
   *
   * <code>bool include_pending_record = 8;</code>
   * @return The includePendingRecord.
   */
  boolean getIncludePendingRecord();

  /**
   * <pre>
   * include_parents=true返回所有父级节点
   * </pre>
   *
   * <code>bool include_parents = 9;</code>
   * @return The includeParents.
   */
  boolean getIncludeParents();

  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 10;</code>
   * @return The lan.
   */
  java.lang.String getLan();
  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 10;</code>
   * @return The bytes for lan.
   */
  com.google.protobuf.ByteString
      getLanBytes();

  /**
   * <pre>
   * 是否包含所有本地化信息
   * </pre>
   *
   * <code>bool include_all_localizations = 11;</code>
   * @return The includeAllLocalizations.
   */
  boolean getIncludeAllLocalizations();
}
