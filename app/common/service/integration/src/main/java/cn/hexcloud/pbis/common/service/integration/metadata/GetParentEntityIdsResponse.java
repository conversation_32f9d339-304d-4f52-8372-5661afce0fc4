// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

/**
 * Protobuf type {@code entity.GetParentEntityIdsResponse}
 */
public final class GetParentEntityIdsResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:entity.GetParentEntityIdsResponse)
    GetParentEntityIdsResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use GetParentEntityIdsResponse.newBuilder() to construct.
  private GetParentEntityIdsResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private GetParentEntityIdsResponse() {
    parentIds_ = emptyLongList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new GetParentEntityIdsResponse();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private GetParentEntityIdsResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              parentIds_ = newLongList();
              mutable_bitField0_ |= 0x00000001;
            }
            parentIds_.addLong(input.readUInt64());
            break;
          }
          case 10: {
            int length = input.readRawVarint32();
            int limit = input.pushLimit(length);
            if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
              parentIds_ = newLongList();
              mutable_bitField0_ |= 0x00000001;
            }
            while (input.getBytesUntilLimit() > 0) {
              parentIds_.addLong(input.readUInt64());
            }
            input.popLimit(limit);
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        parentIds_.makeImmutable(); // C
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_GetParentEntityIdsResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_GetParentEntityIdsResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse.class, cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse.Builder.class);
  }

  public static final int PARENT_IDS_FIELD_NUMBER = 1;
  private com.google.protobuf.Internal.LongList parentIds_;
  /**
   * <code>repeated uint64 parent_ids = 1;</code>
   * @return A list containing the parentIds.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getParentIdsList() {
    return parentIds_;
  }
  /**
   * <code>repeated uint64 parent_ids = 1;</code>
   * @return The count of parentIds.
   */
  public int getParentIdsCount() {
    return parentIds_.size();
  }
  /**
   * <code>repeated uint64 parent_ids = 1;</code>
   * @param index The index of the element to return.
   * @return The parentIds at the given index.
   */
  public long getParentIds(int index) {
    return parentIds_.getLong(index);
  }
  private int parentIdsMemoizedSerializedSize = -1;

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    getSerializedSize();
    if (getParentIdsList().size() > 0) {
      output.writeUInt32NoTag(10);
      output.writeUInt32NoTag(parentIdsMemoizedSerializedSize);
    }
    for (int i = 0; i < parentIds_.size(); i++) {
      output.writeUInt64NoTag(parentIds_.getLong(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    {
      int dataSize = 0;
      for (int i = 0; i < parentIds_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeUInt64SizeNoTag(parentIds_.getLong(i));
      }
      size += dataSize;
      if (!getParentIdsList().isEmpty()) {
        size += 1;
        size += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(dataSize);
      }
      parentIdsMemoizedSerializedSize = dataSize;
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse other = (cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse) obj;

    if (!getParentIdsList()
        .equals(other.getParentIdsList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getParentIdsCount() > 0) {
      hash = (37 * hash) + PARENT_IDS_FIELD_NUMBER;
      hash = (53 * hash) + getParentIdsList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code entity.GetParentEntityIdsResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:entity.GetParentEntityIdsResponse)
      cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_GetParentEntityIdsResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_GetParentEntityIdsResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse.class, cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      parentIds_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000001);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_GetParentEntityIdsResponse_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse build() {
      cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse buildPartial() {
      cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse result = new cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse(this);
      int from_bitField0_ = bitField0_;
      if (((bitField0_ & 0x00000001) != 0)) {
        parentIds_.makeImmutable();
        bitField0_ = (bitField0_ & ~0x00000001);
      }
      result.parentIds_ = parentIds_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse other) {
      if (other == cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse.getDefaultInstance()) return this;
      if (!other.parentIds_.isEmpty()) {
        if (parentIds_.isEmpty()) {
          parentIds_ = other.parentIds_;
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          ensureParentIdsIsMutable();
          parentIds_.addAll(other.parentIds_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private com.google.protobuf.Internal.LongList parentIds_ = emptyLongList();
    private void ensureParentIdsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        parentIds_ = mutableCopy(parentIds_);
        bitField0_ |= 0x00000001;
       }
    }
    /**
     * <code>repeated uint64 parent_ids = 1;</code>
     * @return A list containing the parentIds.
     */
    public java.util.List<java.lang.Long>
        getParentIdsList() {
      return ((bitField0_ & 0x00000001) != 0) ?
               java.util.Collections.unmodifiableList(parentIds_) : parentIds_;
    }
    /**
     * <code>repeated uint64 parent_ids = 1;</code>
     * @return The count of parentIds.
     */
    public int getParentIdsCount() {
      return parentIds_.size();
    }
    /**
     * <code>repeated uint64 parent_ids = 1;</code>
     * @param index The index of the element to return.
     * @return The parentIds at the given index.
     */
    public long getParentIds(int index) {
      return parentIds_.getLong(index);
    }
    /**
     * <code>repeated uint64 parent_ids = 1;</code>
     * @param index The index to set the value at.
     * @param value The parentIds to set.
     * @return This builder for chaining.
     */
    public Builder setParentIds(
        int index, long value) {
      ensureParentIdsIsMutable();
      parentIds_.setLong(index, value);
      onChanged();
      return this;
    }
    /**
     * <code>repeated uint64 parent_ids = 1;</code>
     * @param value The parentIds to add.
     * @return This builder for chaining.
     */
    public Builder addParentIds(long value) {
      ensureParentIdsIsMutable();
      parentIds_.addLong(value);
      onChanged();
      return this;
    }
    /**
     * <code>repeated uint64 parent_ids = 1;</code>
     * @param values The parentIds to add.
     * @return This builder for chaining.
     */
    public Builder addAllParentIds(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureParentIdsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, parentIds_);
      onChanged();
      return this;
    }
    /**
     * <code>repeated uint64 parent_ids = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearParentIds() {
      parentIds_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:entity.GetParentEntityIdsResponse)
  }

  // @@protoc_insertion_point(class_scope:entity.GetParentEntityIdsResponse)
  private static final cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse();
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<GetParentEntityIdsResponse>
      PARSER = new com.google.protobuf.AbstractParser<GetParentEntityIdsResponse>() {
    @java.lang.Override
    public GetParentEntityIdsResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new GetParentEntityIdsResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<GetParentEntityIdsResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<GetParentEntityIdsResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.metadata.GetParentEntityIdsResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

