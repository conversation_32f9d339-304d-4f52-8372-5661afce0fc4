// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface GetParentEntityIdsResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.GetParentEntityIdsResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated uint64 parent_ids = 1;</code>
   * @return A list containing the parentIds.
   */
  java.util.List<java.lang.Long> getParentIdsList();
  /**
   * <code>repeated uint64 parent_ids = 1;</code>
   * @return The count of parentIds.
   */
  int getParentIdsCount();
  /**
   * <code>repeated uint64 parent_ids = 1;</code>
   * @param index The index of the element to return.
   * @return The parentIds at the given index.
   */
  long getParentIds(int index);
}
