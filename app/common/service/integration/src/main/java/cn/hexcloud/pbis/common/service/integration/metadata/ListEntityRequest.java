// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

/**
 * Protobuf type {@code entity.ListEntityRequest}
 */
public final class ListEntityRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:entity.ListEntityRequest)
    ListEntityRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ListEntityRequest.newBuilder() to construct.
  private ListEntityRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ListEntityRequest() {
    schemaName_ = "";
    state_ = "";
    code_ = "";
    relation_ = "";
    returnFields_ = "";
    sort_ = "";
    order_ = "";
    search_ = "";
    searchFields_ = "";
    ids_ = emptyLongList();
    lan_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ListEntityRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ListEntityRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            java.lang.String s = input.readStringRequireUtf8();

            schemaName_ = s;
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            state_ = s;
            break;
          }
          case 24: {

            includeState_ = input.readBool();
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();

            code_ = s;
            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            relation_ = s;
            break;
          }
          case 50: {
            java.lang.String s = input.readStringRequireUtf8();

            returnFields_ = s;
            break;
          }
          case 56: {

            includePendingChanges_ = input.readBool();
            break;
          }
          case 64: {

            includePendingRecord_ = input.readBool();
            break;
          }
          case 72: {

            includeParents_ = input.readBool();
            break;
          }
          case 80: {

            limit_ = input.readInt32();
            break;
          }
          case 88: {

            offset_ = input.readInt32();
            break;
          }
          case 98: {
            java.lang.String s = input.readStringRequireUtf8();

            sort_ = s;
            break;
          }
          case 106: {
            java.lang.String s = input.readStringRequireUtf8();

            order_ = s;
            break;
          }
          case 112: {

            includeTotal_ = input.readBool();
            break;
          }
          case 122: {
            java.lang.String s = input.readStringRequireUtf8();

            search_ = s;
            break;
          }
          case 130: {
            java.lang.String s = input.readStringRequireUtf8();

            searchFields_ = s;
            break;
          }
          case 136: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              ids_ = newLongList();
              mutable_bitField0_ |= 0x00000001;
            }
            ids_.addLong(input.readUInt64());
            break;
          }
          case 138: {
            int length = input.readRawVarint32();
            int limit = input.pushLimit(length);
            if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
              ids_ = newLongList();
              mutable_bitField0_ |= 0x00000001;
            }
            while (input.getBytesUntilLimit() > 0) {
              ids_.addLong(input.readUInt64());
            }
            input.popLimit(limit);
            break;
          }
          case 146: {
            com.google.protobuf.Struct.Builder subBuilder = null;
            if (filters_ != null) {
              subBuilder = filters_.toBuilder();
            }
            filters_ = input.readMessage(com.google.protobuf.Struct.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(filters_);
              filters_ = subBuilder.buildPartial();
            }

            break;
          }
          case 154: {
            com.google.protobuf.Struct.Builder subBuilder = null;
            if (relationFilters_ != null) {
              subBuilder = relationFilters_.toBuilder();
            }
            relationFilters_ = input.readMessage(com.google.protobuf.Struct.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(relationFilters_);
              relationFilters_ = subBuilder.buildPartial();
            }

            break;
          }
          case 162: {
            java.lang.String s = input.readStringRequireUtf8();

            lan_ = s;
            break;
          }
          case 168: {

            includeAllLocalizations_ = input.readBool();
            break;
          }
          case 176: {

            isRequest_ = input.readBool();
            break;
          }
          case 184: {

            getCommon_ = input.readBool();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        ids_.makeImmutable(); // C
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_ListEntityRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_ListEntityRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest.class, cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest.Builder.class);
  }

  public static final int SCHEMA_NAME_FIELD_NUMBER = 1;
  private volatile java.lang.Object schemaName_;
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 1;</code>
   * @return The schemaName.
   */
  @java.lang.Override
  public java.lang.String getSchemaName() {
    java.lang.Object ref = schemaName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      schemaName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 1;</code>
   * @return The bytes for schemaName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSchemaNameBytes() {
    java.lang.Object ref = schemaName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      schemaName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATE_FIELD_NUMBER = 2;
  private volatile java.lang.Object state_;
  /**
   * <pre>
   * 指定要返回相关数据状态的记录, 默认只返回enabled, 多个状态用逗号隔开，
   * 如state=draft,disabled; state=all时返回所有数据状态的记录
   * </pre>
   *
   * <code>string state = 2;</code>
   * @return The state.
   */
  @java.lang.Override
  public java.lang.String getState() {
    java.lang.Object ref = state_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      state_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 指定要返回相关数据状态的记录, 默认只返回enabled, 多个状态用逗号隔开，
   * 如state=draft,disabled; state=all时返回所有数据状态的记录
   * </pre>
   *
   * <code>string state = 2;</code>
   * @return The bytes for state.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStateBytes() {
    java.lang.Object ref = state_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      state_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INCLUDE_STATE_FIELD_NUMBER = 3;
  private boolean includeState_;
  /**
   * <pre>
   * 是否包含数据状态
   * </pre>
   *
   * <code>bool include_state = 3;</code>
   * @return The includeState.
   */
  @java.lang.Override
  public boolean getIncludeState() {
    return includeState_;
  }

  public static final int CODE_FIELD_NUMBER = 4;
  private volatile java.lang.Object code_;
  /**
   * <pre>
   * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string code = 4;</code>
   * @return The code.
   */
  @java.lang.Override
  public java.lang.String getCode() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      code_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string code = 4;</code>
   * @return The bytes for code.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCodeBytes() {
    java.lang.Object ref = code_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      code_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RELATION_FIELD_NUMBER = 5;
  private volatile java.lang.Object relation_;
  /**
   * <pre>
   * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string relation = 5;</code>
   * @return The relation.
   */
  @java.lang.Override
  public java.lang.String getRelation() {
    java.lang.Object ref = relation_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      relation_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string relation = 5;</code>
   * @return The bytes for relation.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRelationBytes() {
    java.lang.Object ref = relation_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      relation_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RETURN_FIELDS_FIELD_NUMBER = 6;
  private volatile java.lang.Object returnFields_;
  /**
   * <pre>
   * 除code和relation之外需要返回的字段, 多个以逗号隔开
   * </pre>
   *
   * <code>string return_fields = 6;</code>
   * @return The returnFields.
   */
  @java.lang.Override
  public java.lang.String getReturnFields() {
    java.lang.Object ref = returnFields_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      returnFields_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 除code和relation之外需要返回的字段, 多个以逗号隔开
   * </pre>
   *
   * <code>string return_fields = 6;</code>
   * @return The bytes for returnFields.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getReturnFieldsBytes() {
    java.lang.Object ref = returnFields_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      returnFields_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INCLUDE_PENDING_CHANGES_FIELD_NUMBER = 7;
  private boolean includePendingChanges_;
  /**
   * <pre>
   * include_pending_changes=true时, 如果相关记录包含pending的修改属性,
   * 则会获取修改属性attach到返回记录中fields_pending字段
   * </pre>
   *
   * <code>bool include_pending_changes = 7;</code>
   * @return The includePendingChanges.
   */
  @java.lang.Override
  public boolean getIncludePendingChanges() {
    return includePendingChanges_;
  }

  public static final int INCLUDE_PENDING_RECORD_FIELD_NUMBER = 8;
  private boolean includePendingRecord_;
  /**
   * <pre>
   * include_pending_record=true时, 如果相关记录包含pending的修改属性,
   * 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的record_pending字段
   * </pre>
   *
   * <code>bool include_pending_record = 8;</code>
   * @return The includePendingRecord.
   */
  @java.lang.Override
  public boolean getIncludePendingRecord() {
    return includePendingRecord_;
  }

  public static final int INCLUDE_PARENTS_FIELD_NUMBER = 9;
  private boolean includeParents_;
  /**
   * <pre>
   * include_parents=true返回所有父级节点
   * </pre>
   *
   * <code>bool include_parents = 9;</code>
   * @return The includeParents.
   */
  @java.lang.Override
  public boolean getIncludeParents() {
    return includeParents_;
  }

  public static final int LIMIT_FIELD_NUMBER = 10;
  private int limit_;
  /**
   * <pre>
   * 分页大小
   * </pre>
   *
   * <code>int32 limit = 10;</code>
   * @return The limit.
   */
  @java.lang.Override
  public int getLimit() {
    return limit_;
  }

  public static final int OFFSET_FIELD_NUMBER = 11;
  private int offset_;
  /**
   * <pre>
   * 跳过行数
   * </pre>
   *
   * <code>int32 offset = 11;</code>
   * @return The offset.
   */
  @java.lang.Override
  public int getOffset() {
    return offset_;
  }

  public static final int SORT_FIELD_NUMBER = 12;
  private volatile java.lang.Object sort_;
  /**
   * <pre>
   * 排序字段
   * </pre>
   *
   * <code>string sort = 12;</code>
   * @return The sort.
   */
  @java.lang.Override
  public java.lang.String getSort() {
    java.lang.Object ref = sort_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      sort_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 排序字段
   * </pre>
   *
   * <code>string sort = 12;</code>
   * @return The bytes for sort.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSortBytes() {
    java.lang.Object ref = sort_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      sort_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int ORDER_FIELD_NUMBER = 13;
  private volatile java.lang.Object order_;
  /**
   * <pre>
   * 排序顺序
   * </pre>
   *
   * <code>string order = 13;</code>
   * @return The order.
   */
  @java.lang.Override
  public java.lang.String getOrder() {
    java.lang.Object ref = order_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      order_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 排序顺序
   * </pre>
   *
   * <code>string order = 13;</code>
   * @return The bytes for order.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getOrderBytes() {
    java.lang.Object ref = order_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      order_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INCLUDE_TOTAL_FIELD_NUMBER = 14;
  private boolean includeTotal_;
  /**
   * <pre>
   * 返回总条数
   * </pre>
   *
   * <code>bool include_total = 14;</code>
   * @return The includeTotal.
   */
  @java.lang.Override
  public boolean getIncludeTotal() {
    return includeTotal_;
  }

  public static final int SEARCH_FIELD_NUMBER = 15;
  private volatile java.lang.Object search_;
  /**
   * <pre>
   * 要模糊查询的字符串
   * </pre>
   *
   * <code>string search = 15;</code>
   * @return The search.
   */
  @java.lang.Override
  public java.lang.String getSearch() {
    java.lang.Object ref = search_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      search_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 要模糊查询的字符串
   * </pre>
   *
   * <code>string search = 15;</code>
   * @return The bytes for search.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSearchBytes() {
    java.lang.Object ref = search_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      search_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SEARCH_FIELDS_FIELD_NUMBER = 16;
  private volatile java.lang.Object searchFields_;
  /**
   * <pre>
   * 要查询的字段, 多个逗号隔开;
   * 如果传入relation.[relation name].[field], 则会顺带搜索关联的数据,
   * 例: 搜索store数据时, search_fields=relation.branch.name则会搜索管理区域名称,
   * 找到匹配的管理区域, 然后找到关联到这些管理区域以及所有下级区域的门店
   * </pre>
   *
   * <code>string search_fields = 16;</code>
   * @return The searchFields.
   */
  @java.lang.Override
  public java.lang.String getSearchFields() {
    java.lang.Object ref = searchFields_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      searchFields_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 要查询的字段, 多个逗号隔开;
   * 如果传入relation.[relation name].[field], 则会顺带搜索关联的数据,
   * 例: 搜索store数据时, search_fields=relation.branch.name则会搜索管理区域名称,
   * 找到匹配的管理区域, 然后找到关联到这些管理区域以及所有下级区域的门店
   * </pre>
   *
   * <code>string search_fields = 16;</code>
   * @return The bytes for searchFields.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSearchFieldsBytes() {
    java.lang.Object ref = searchFields_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      searchFields_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IDS_FIELD_NUMBER = 17;
  private com.google.protobuf.Internal.LongList ids_;
  /**
   * <pre>
   * 按id列表查询
   * </pre>
   *
   * <code>repeated uint64 ids = 17;</code>
   * @return A list containing the ids.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getIdsList() {
    return ids_;
  }
  /**
   * <pre>
   * 按id列表查询
   * </pre>
   *
   * <code>repeated uint64 ids = 17;</code>
   * @return The count of ids.
   */
  public int getIdsCount() {
    return ids_.size();
  }
  /**
   * <pre>
   * 按id列表查询
   * </pre>
   *
   * <code>repeated uint64 ids = 17;</code>
   * @param index The index of the element to return.
   * @return The ids at the given index.
   */
  public long getIds(int index) {
    return ids_.getLong(index);
  }
  private int idsMemoizedSerializedSize = -1;

  public static final int FILTERS_FIELD_NUMBER = 18;
  private com.google.protobuf.Struct filters_;
  /**
   * <pre>
   * 按字段过滤
   * </pre>
   *
   * <code>.google.protobuf.Struct filters = 18;</code>
   * @return Whether the filters field is set.
   */
  @java.lang.Override
  public boolean hasFilters() {
    return filters_ != null;
  }
  /**
   * <pre>
   * 按字段过滤
   * </pre>
   *
   * <code>.google.protobuf.Struct filters = 18;</code>
   * @return The filters.
   */
  @java.lang.Override
  public com.google.protobuf.Struct getFilters() {
    return filters_ == null ? com.google.protobuf.Struct.getDefaultInstance() : filters_;
  }
  /**
   * <pre>
   * 按字段过滤
   * </pre>
   *
   * <code>.google.protobuf.Struct filters = 18;</code>
   */
  @java.lang.Override
  public com.google.protobuf.StructOrBuilder getFiltersOrBuilder() {
    return getFilters();
  }

  public static final int RELATION_FILTERS_FIELD_NUMBER = 19;
  private com.google.protobuf.Struct relationFilters_;
  /**
   * <pre>
   * 按关系深层次递归过滤(获取包含下级节点的数据)
   * </pre>
   *
   * <code>.google.protobuf.Struct relation_filters = 19;</code>
   * @return Whether the relationFilters field is set.
   */
  @java.lang.Override
  public boolean hasRelationFilters() {
    return relationFilters_ != null;
  }
  /**
   * <pre>
   * 按关系深层次递归过滤(获取包含下级节点的数据)
   * </pre>
   *
   * <code>.google.protobuf.Struct relation_filters = 19;</code>
   * @return The relationFilters.
   */
  @java.lang.Override
  public com.google.protobuf.Struct getRelationFilters() {
    return relationFilters_ == null ? com.google.protobuf.Struct.getDefaultInstance() : relationFilters_;
  }
  /**
   * <pre>
   * 按关系深层次递归过滤(获取包含下级节点的数据)
   * </pre>
   *
   * <code>.google.protobuf.Struct relation_filters = 19;</code>
   */
  @java.lang.Override
  public com.google.protobuf.StructOrBuilder getRelationFiltersOrBuilder() {
    return getRelationFilters();
  }

  public static final int LAN_FIELD_NUMBER = 20;
  private volatile java.lang.Object lan_;
  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 20;</code>
   * @return The lan.
   */
  @java.lang.Override
  public java.lang.String getLan() {
    java.lang.Object ref = lan_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      lan_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 20;</code>
   * @return The bytes for lan.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLanBytes() {
    java.lang.Object ref = lan_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      lan_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INCLUDE_ALL_LOCALIZATIONS_FIELD_NUMBER = 21;
  private boolean includeAllLocalizations_;
  /**
   * <pre>
   * 是否包含所有本地化信息
   * </pre>
   *
   * <code>bool include_all_localizations = 21;</code>
   * @return The includeAllLocalizations.
   */
  @java.lang.Override
  public boolean getIncludeAllLocalizations() {
    return includeAllLocalizations_;
  }

  public static final int IS_REQUEST_FIELD_NUMBER = 22;
  private boolean isRequest_;
  /**
   * <pre>
   * 返回 state=DRAFT 或 state=ENABLED且有pending record的记录
   * </pre>
   *
   * <code>bool is_request = 22;</code>
   * @return The isRequest.
   */
  @java.lang.Override
  public boolean getIsRequest() {
    return isRequest_;
  }

  public static final int GET_COMMON_FIELD_NUMBER = 23;
  private boolean getCommon_;
  /**
   * <pre>
   * 多租户下公共数据
   * </pre>
   *
   * <code>bool get_common = 23;</code>
   * @return The getCommon.
   */
  @java.lang.Override
  public boolean getGetCommon() {
    return getCommon_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    getSerializedSize();
    if (!getSchemaNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, schemaName_);
    }
    if (!getStateBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, state_);
    }
    if (includeState_ != false) {
      output.writeBool(3, includeState_);
    }
    if (!getCodeBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, code_);
    }
    if (!getRelationBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, relation_);
    }
    if (!getReturnFieldsBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, returnFields_);
    }
    if (includePendingChanges_ != false) {
      output.writeBool(7, includePendingChanges_);
    }
    if (includePendingRecord_ != false) {
      output.writeBool(8, includePendingRecord_);
    }
    if (includeParents_ != false) {
      output.writeBool(9, includeParents_);
    }
    if (limit_ != 0) {
      output.writeInt32(10, limit_);
    }
    if (offset_ != 0) {
      output.writeInt32(11, offset_);
    }
    if (!getSortBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 12, sort_);
    }
    if (!getOrderBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 13, order_);
    }
    if (includeTotal_ != false) {
      output.writeBool(14, includeTotal_);
    }
    if (!getSearchBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 15, search_);
    }
    if (!getSearchFieldsBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 16, searchFields_);
    }
    if (getIdsList().size() > 0) {
      output.writeUInt32NoTag(138);
      output.writeUInt32NoTag(idsMemoizedSerializedSize);
    }
    for (int i = 0; i < ids_.size(); i++) {
      output.writeUInt64NoTag(ids_.getLong(i));
    }
    if (filters_ != null) {
      output.writeMessage(18, getFilters());
    }
    if (relationFilters_ != null) {
      output.writeMessage(19, getRelationFilters());
    }
    if (!getLanBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 20, lan_);
    }
    if (includeAllLocalizations_ != false) {
      output.writeBool(21, includeAllLocalizations_);
    }
    if (isRequest_ != false) {
      output.writeBool(22, isRequest_);
    }
    if (getCommon_ != false) {
      output.writeBool(23, getCommon_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getSchemaNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, schemaName_);
    }
    if (!getStateBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, state_);
    }
    if (includeState_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(3, includeState_);
    }
    if (!getCodeBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, code_);
    }
    if (!getRelationBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, relation_);
    }
    if (!getReturnFieldsBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, returnFields_);
    }
    if (includePendingChanges_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(7, includePendingChanges_);
    }
    if (includePendingRecord_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(8, includePendingRecord_);
    }
    if (includeParents_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(9, includeParents_);
    }
    if (limit_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(10, limit_);
    }
    if (offset_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(11, offset_);
    }
    if (!getSortBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, sort_);
    }
    if (!getOrderBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, order_);
    }
    if (includeTotal_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(14, includeTotal_);
    }
    if (!getSearchBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, search_);
    }
    if (!getSearchFieldsBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, searchFields_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < ids_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeUInt64SizeNoTag(ids_.getLong(i));
      }
      size += dataSize;
      if (!getIdsList().isEmpty()) {
        size += 2;
        size += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(dataSize);
      }
      idsMemoizedSerializedSize = dataSize;
    }
    if (filters_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(18, getFilters());
    }
    if (relationFilters_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(19, getRelationFilters());
    }
    if (!getLanBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(20, lan_);
    }
    if (includeAllLocalizations_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(21, includeAllLocalizations_);
    }
    if (isRequest_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(22, isRequest_);
    }
    if (getCommon_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(23, getCommon_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest other = (cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest) obj;

    if (!getSchemaName()
        .equals(other.getSchemaName())) return false;
    if (!getState()
        .equals(other.getState())) return false;
    if (getIncludeState()
        != other.getIncludeState()) return false;
    if (!getCode()
        .equals(other.getCode())) return false;
    if (!getRelation()
        .equals(other.getRelation())) return false;
    if (!getReturnFields()
        .equals(other.getReturnFields())) return false;
    if (getIncludePendingChanges()
        != other.getIncludePendingChanges()) return false;
    if (getIncludePendingRecord()
        != other.getIncludePendingRecord()) return false;
    if (getIncludeParents()
        != other.getIncludeParents()) return false;
    if (getLimit()
        != other.getLimit()) return false;
    if (getOffset()
        != other.getOffset()) return false;
    if (!getSort()
        .equals(other.getSort())) return false;
    if (!getOrder()
        .equals(other.getOrder())) return false;
    if (getIncludeTotal()
        != other.getIncludeTotal()) return false;
    if (!getSearch()
        .equals(other.getSearch())) return false;
    if (!getSearchFields()
        .equals(other.getSearchFields())) return false;
    if (!getIdsList()
        .equals(other.getIdsList())) return false;
    if (hasFilters() != other.hasFilters()) return false;
    if (hasFilters()) {
      if (!getFilters()
          .equals(other.getFilters())) return false;
    }
    if (hasRelationFilters() != other.hasRelationFilters()) return false;
    if (hasRelationFilters()) {
      if (!getRelationFilters()
          .equals(other.getRelationFilters())) return false;
    }
    if (!getLan()
        .equals(other.getLan())) return false;
    if (getIncludeAllLocalizations()
        != other.getIncludeAllLocalizations()) return false;
    if (getIsRequest()
        != other.getIsRequest()) return false;
    if (getGetCommon()
        != other.getGetCommon()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SCHEMA_NAME_FIELD_NUMBER;
    hash = (53 * hash) + getSchemaName().hashCode();
    hash = (37 * hash) + STATE_FIELD_NUMBER;
    hash = (53 * hash) + getState().hashCode();
    hash = (37 * hash) + INCLUDE_STATE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIncludeState());
    hash = (37 * hash) + CODE_FIELD_NUMBER;
    hash = (53 * hash) + getCode().hashCode();
    hash = (37 * hash) + RELATION_FIELD_NUMBER;
    hash = (53 * hash) + getRelation().hashCode();
    hash = (37 * hash) + RETURN_FIELDS_FIELD_NUMBER;
    hash = (53 * hash) + getReturnFields().hashCode();
    hash = (37 * hash) + INCLUDE_PENDING_CHANGES_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIncludePendingChanges());
    hash = (37 * hash) + INCLUDE_PENDING_RECORD_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIncludePendingRecord());
    hash = (37 * hash) + INCLUDE_PARENTS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIncludeParents());
    hash = (37 * hash) + LIMIT_FIELD_NUMBER;
    hash = (53 * hash) + getLimit();
    hash = (37 * hash) + OFFSET_FIELD_NUMBER;
    hash = (53 * hash) + getOffset();
    hash = (37 * hash) + SORT_FIELD_NUMBER;
    hash = (53 * hash) + getSort().hashCode();
    hash = (37 * hash) + ORDER_FIELD_NUMBER;
    hash = (53 * hash) + getOrder().hashCode();
    hash = (37 * hash) + INCLUDE_TOTAL_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIncludeTotal());
    hash = (37 * hash) + SEARCH_FIELD_NUMBER;
    hash = (53 * hash) + getSearch().hashCode();
    hash = (37 * hash) + SEARCH_FIELDS_FIELD_NUMBER;
    hash = (53 * hash) + getSearchFields().hashCode();
    if (getIdsCount() > 0) {
      hash = (37 * hash) + IDS_FIELD_NUMBER;
      hash = (53 * hash) + getIdsList().hashCode();
    }
    if (hasFilters()) {
      hash = (37 * hash) + FILTERS_FIELD_NUMBER;
      hash = (53 * hash) + getFilters().hashCode();
    }
    if (hasRelationFilters()) {
      hash = (37 * hash) + RELATION_FILTERS_FIELD_NUMBER;
      hash = (53 * hash) + getRelationFilters().hashCode();
    }
    hash = (37 * hash) + LAN_FIELD_NUMBER;
    hash = (53 * hash) + getLan().hashCode();
    hash = (37 * hash) + INCLUDE_ALL_LOCALIZATIONS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIncludeAllLocalizations());
    hash = (37 * hash) + IS_REQUEST_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIsRequest());
    hash = (37 * hash) + GET_COMMON_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getGetCommon());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code entity.ListEntityRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:entity.ListEntityRequest)
      cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_ListEntityRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_ListEntityRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest.class, cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      schemaName_ = "";

      state_ = "";

      includeState_ = false;

      code_ = "";

      relation_ = "";

      returnFields_ = "";

      includePendingChanges_ = false;

      includePendingRecord_ = false;

      includeParents_ = false;

      limit_ = 0;

      offset_ = 0;

      sort_ = "";

      order_ = "";

      includeTotal_ = false;

      search_ = "";

      searchFields_ = "";

      ids_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000001);
      if (filtersBuilder_ == null) {
        filters_ = null;
      } else {
        filters_ = null;
        filtersBuilder_ = null;
      }
      if (relationFiltersBuilder_ == null) {
        relationFilters_ = null;
      } else {
        relationFilters_ = null;
        relationFiltersBuilder_ = null;
      }
      lan_ = "";

      includeAllLocalizations_ = false;

      isRequest_ = false;

      getCommon_ = false;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_ListEntityRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest build() {
      cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest buildPartial() {
      cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest result = new cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest(this);
      int from_bitField0_ = bitField0_;
      result.schemaName_ = schemaName_;
      result.state_ = state_;
      result.includeState_ = includeState_;
      result.code_ = code_;
      result.relation_ = relation_;
      result.returnFields_ = returnFields_;
      result.includePendingChanges_ = includePendingChanges_;
      result.includePendingRecord_ = includePendingRecord_;
      result.includeParents_ = includeParents_;
      result.limit_ = limit_;
      result.offset_ = offset_;
      result.sort_ = sort_;
      result.order_ = order_;
      result.includeTotal_ = includeTotal_;
      result.search_ = search_;
      result.searchFields_ = searchFields_;
      if (((bitField0_ & 0x00000001) != 0)) {
        ids_.makeImmutable();
        bitField0_ = (bitField0_ & ~0x00000001);
      }
      result.ids_ = ids_;
      if (filtersBuilder_ == null) {
        result.filters_ = filters_;
      } else {
        result.filters_ = filtersBuilder_.build();
      }
      if (relationFiltersBuilder_ == null) {
        result.relationFilters_ = relationFilters_;
      } else {
        result.relationFilters_ = relationFiltersBuilder_.build();
      }
      result.lan_ = lan_;
      result.includeAllLocalizations_ = includeAllLocalizations_;
      result.isRequest_ = isRequest_;
      result.getCommon_ = getCommon_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest other) {
      if (other == cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest.getDefaultInstance()) return this;
      if (!other.getSchemaName().isEmpty()) {
        schemaName_ = other.schemaName_;
        onChanged();
      }
      if (!other.getState().isEmpty()) {
        state_ = other.state_;
        onChanged();
      }
      if (other.getIncludeState() != false) {
        setIncludeState(other.getIncludeState());
      }
      if (!other.getCode().isEmpty()) {
        code_ = other.code_;
        onChanged();
      }
      if (!other.getRelation().isEmpty()) {
        relation_ = other.relation_;
        onChanged();
      }
      if (!other.getReturnFields().isEmpty()) {
        returnFields_ = other.returnFields_;
        onChanged();
      }
      if (other.getIncludePendingChanges() != false) {
        setIncludePendingChanges(other.getIncludePendingChanges());
      }
      if (other.getIncludePendingRecord() != false) {
        setIncludePendingRecord(other.getIncludePendingRecord());
      }
      if (other.getIncludeParents() != false) {
        setIncludeParents(other.getIncludeParents());
      }
      if (other.getLimit() != 0) {
        setLimit(other.getLimit());
      }
      if (other.getOffset() != 0) {
        setOffset(other.getOffset());
      }
      if (!other.getSort().isEmpty()) {
        sort_ = other.sort_;
        onChanged();
      }
      if (!other.getOrder().isEmpty()) {
        order_ = other.order_;
        onChanged();
      }
      if (other.getIncludeTotal() != false) {
        setIncludeTotal(other.getIncludeTotal());
      }
      if (!other.getSearch().isEmpty()) {
        search_ = other.search_;
        onChanged();
      }
      if (!other.getSearchFields().isEmpty()) {
        searchFields_ = other.searchFields_;
        onChanged();
      }
      if (!other.ids_.isEmpty()) {
        if (ids_.isEmpty()) {
          ids_ = other.ids_;
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          ensureIdsIsMutable();
          ids_.addAll(other.ids_);
        }
        onChanged();
      }
      if (other.hasFilters()) {
        mergeFilters(other.getFilters());
      }
      if (other.hasRelationFilters()) {
        mergeRelationFilters(other.getRelationFilters());
      }
      if (!other.getLan().isEmpty()) {
        lan_ = other.lan_;
        onChanged();
      }
      if (other.getIncludeAllLocalizations() != false) {
        setIncludeAllLocalizations(other.getIncludeAllLocalizations());
      }
      if (other.getIsRequest() != false) {
        setIsRequest(other.getIsRequest());
      }
      if (other.getGetCommon() != false) {
        setGetCommon(other.getGetCommon());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.lang.Object schemaName_ = "";
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 1;</code>
     * @return The schemaName.
     */
    public java.lang.String getSchemaName() {
      java.lang.Object ref = schemaName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        schemaName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 1;</code>
     * @return The bytes for schemaName.
     */
    public com.google.protobuf.ByteString
        getSchemaNameBytes() {
      java.lang.Object ref = schemaName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        schemaName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 1;</code>
     * @param value The schemaName to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      schemaName_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSchemaName() {
      
      schemaName_ = getDefaultInstance().getSchemaName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 1;</code>
     * @param value The bytes for schemaName to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      schemaName_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object state_ = "";
    /**
     * <pre>
     * 指定要返回相关数据状态的记录, 默认只返回enabled, 多个状态用逗号隔开，
     * 如state=draft,disabled; state=all时返回所有数据状态的记录
     * </pre>
     *
     * <code>string state = 2;</code>
     * @return The state.
     */
    public java.lang.String getState() {
      java.lang.Object ref = state_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        state_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 指定要返回相关数据状态的记录, 默认只返回enabled, 多个状态用逗号隔开，
     * 如state=draft,disabled; state=all时返回所有数据状态的记录
     * </pre>
     *
     * <code>string state = 2;</code>
     * @return The bytes for state.
     */
    public com.google.protobuf.ByteString
        getStateBytes() {
      java.lang.Object ref = state_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        state_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 指定要返回相关数据状态的记录, 默认只返回enabled, 多个状态用逗号隔开，
     * 如state=draft,disabled; state=all时返回所有数据状态的记录
     * </pre>
     *
     * <code>string state = 2;</code>
     * @param value The state to set.
     * @return This builder for chaining.
     */
    public Builder setState(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      state_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 指定要返回相关数据状态的记录, 默认只返回enabled, 多个状态用逗号隔开，
     * 如state=draft,disabled; state=all时返回所有数据状态的记录
     * </pre>
     *
     * <code>string state = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearState() {
      
      state_ = getDefaultInstance().getState();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 指定要返回相关数据状态的记录, 默认只返回enabled, 多个状态用逗号隔开，
     * 如state=draft,disabled; state=all时返回所有数据状态的记录
     * </pre>
     *
     * <code>string state = 2;</code>
     * @param value The bytes for state to set.
     * @return This builder for chaining.
     */
    public Builder setStateBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      state_ = value;
      onChanged();
      return this;
    }

    private boolean includeState_ ;
    /**
     * <pre>
     * 是否包含数据状态
     * </pre>
     *
     * <code>bool include_state = 3;</code>
     * @return The includeState.
     */
    @java.lang.Override
    public boolean getIncludeState() {
      return includeState_;
    }
    /**
     * <pre>
     * 是否包含数据状态
     * </pre>
     *
     * <code>bool include_state = 3;</code>
     * @param value The includeState to set.
     * @return This builder for chaining.
     */
    public Builder setIncludeState(boolean value) {
      
      includeState_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 是否包含数据状态
     * </pre>
     *
     * <code>bool include_state = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearIncludeState() {
      
      includeState_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object code_ = "";
    /**
     * <pre>
     * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string code = 4;</code>
     * @return The code.
     */
    public java.lang.String getCode() {
      java.lang.Object ref = code_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        code_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string code = 4;</code>
     * @return The bytes for code.
     */
    public com.google.protobuf.ByteString
        getCodeBytes() {
      java.lang.Object ref = code_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        code_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string code = 4;</code>
     * @param value The code to set.
     * @return This builder for chaining.
     */
    public Builder setCode(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      code_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string code = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearCode() {
      
      code_ = getDefaultInstance().getCode();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string code = 4;</code>
     * @param value The bytes for code to set.
     * @return This builder for chaining.
     */
    public Builder setCodeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      code_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object relation_ = "";
    /**
     * <pre>
     * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string relation = 5;</code>
     * @return The relation.
     */
    public java.lang.String getRelation() {
      java.lang.Object ref = relation_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        relation_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string relation = 5;</code>
     * @return The bytes for relation.
     */
    public com.google.protobuf.ByteString
        getRelationBytes() {
      java.lang.Object ref = relation_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        relation_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string relation = 5;</code>
     * @param value The relation to set.
     * @return This builder for chaining.
     */
    public Builder setRelation(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      relation_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string relation = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearRelation() {
      
      relation_ = getDefaultInstance().getRelation();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
     * </pre>
     *
     * <code>string relation = 5;</code>
     * @param value The bytes for relation to set.
     * @return This builder for chaining.
     */
    public Builder setRelationBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      relation_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object returnFields_ = "";
    /**
     * <pre>
     * 除code和relation之外需要返回的字段, 多个以逗号隔开
     * </pre>
     *
     * <code>string return_fields = 6;</code>
     * @return The returnFields.
     */
    public java.lang.String getReturnFields() {
      java.lang.Object ref = returnFields_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        returnFields_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 除code和relation之外需要返回的字段, 多个以逗号隔开
     * </pre>
     *
     * <code>string return_fields = 6;</code>
     * @return The bytes for returnFields.
     */
    public com.google.protobuf.ByteString
        getReturnFieldsBytes() {
      java.lang.Object ref = returnFields_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        returnFields_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 除code和relation之外需要返回的字段, 多个以逗号隔开
     * </pre>
     *
     * <code>string return_fields = 6;</code>
     * @param value The returnFields to set.
     * @return This builder for chaining.
     */
    public Builder setReturnFields(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      returnFields_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 除code和relation之外需要返回的字段, 多个以逗号隔开
     * </pre>
     *
     * <code>string return_fields = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearReturnFields() {
      
      returnFields_ = getDefaultInstance().getReturnFields();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 除code和relation之外需要返回的字段, 多个以逗号隔开
     * </pre>
     *
     * <code>string return_fields = 6;</code>
     * @param value The bytes for returnFields to set.
     * @return This builder for chaining.
     */
    public Builder setReturnFieldsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      returnFields_ = value;
      onChanged();
      return this;
    }

    private boolean includePendingChanges_ ;
    /**
     * <pre>
     * include_pending_changes=true时, 如果相关记录包含pending的修改属性,
     * 则会获取修改属性attach到返回记录中fields_pending字段
     * </pre>
     *
     * <code>bool include_pending_changes = 7;</code>
     * @return The includePendingChanges.
     */
    @java.lang.Override
    public boolean getIncludePendingChanges() {
      return includePendingChanges_;
    }
    /**
     * <pre>
     * include_pending_changes=true时, 如果相关记录包含pending的修改属性,
     * 则会获取修改属性attach到返回记录中fields_pending字段
     * </pre>
     *
     * <code>bool include_pending_changes = 7;</code>
     * @param value The includePendingChanges to set.
     * @return This builder for chaining.
     */
    public Builder setIncludePendingChanges(boolean value) {
      
      includePendingChanges_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * include_pending_changes=true时, 如果相关记录包含pending的修改属性,
     * 则会获取修改属性attach到返回记录中fields_pending字段
     * </pre>
     *
     * <code>bool include_pending_changes = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearIncludePendingChanges() {
      
      includePendingChanges_ = false;
      onChanged();
      return this;
    }

    private boolean includePendingRecord_ ;
    /**
     * <pre>
     * include_pending_record=true时, 如果相关记录包含pending的修改属性,
     * 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的record_pending字段
     * </pre>
     *
     * <code>bool include_pending_record = 8;</code>
     * @return The includePendingRecord.
     */
    @java.lang.Override
    public boolean getIncludePendingRecord() {
      return includePendingRecord_;
    }
    /**
     * <pre>
     * include_pending_record=true时, 如果相关记录包含pending的修改属性,
     * 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的record_pending字段
     * </pre>
     *
     * <code>bool include_pending_record = 8;</code>
     * @param value The includePendingRecord to set.
     * @return This builder for chaining.
     */
    public Builder setIncludePendingRecord(boolean value) {
      
      includePendingRecord_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * include_pending_record=true时, 如果相关记录包含pending的修改属性,
     * 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的record_pending字段
     * </pre>
     *
     * <code>bool include_pending_record = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearIncludePendingRecord() {
      
      includePendingRecord_ = false;
      onChanged();
      return this;
    }

    private boolean includeParents_ ;
    /**
     * <pre>
     * include_parents=true返回所有父级节点
     * </pre>
     *
     * <code>bool include_parents = 9;</code>
     * @return The includeParents.
     */
    @java.lang.Override
    public boolean getIncludeParents() {
      return includeParents_;
    }
    /**
     * <pre>
     * include_parents=true返回所有父级节点
     * </pre>
     *
     * <code>bool include_parents = 9;</code>
     * @param value The includeParents to set.
     * @return This builder for chaining.
     */
    public Builder setIncludeParents(boolean value) {
      
      includeParents_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * include_parents=true返回所有父级节点
     * </pre>
     *
     * <code>bool include_parents = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearIncludeParents() {
      
      includeParents_ = false;
      onChanged();
      return this;
    }

    private int limit_ ;
    /**
     * <pre>
     * 分页大小
     * </pre>
     *
     * <code>int32 limit = 10;</code>
     * @return The limit.
     */
    @java.lang.Override
    public int getLimit() {
      return limit_;
    }
    /**
     * <pre>
     * 分页大小
     * </pre>
     *
     * <code>int32 limit = 10;</code>
     * @param value The limit to set.
     * @return This builder for chaining.
     */
    public Builder setLimit(int value) {
      
      limit_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 分页大小
     * </pre>
     *
     * <code>int32 limit = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearLimit() {
      
      limit_ = 0;
      onChanged();
      return this;
    }

    private int offset_ ;
    /**
     * <pre>
     * 跳过行数
     * </pre>
     *
     * <code>int32 offset = 11;</code>
     * @return The offset.
     */
    @java.lang.Override
    public int getOffset() {
      return offset_;
    }
    /**
     * <pre>
     * 跳过行数
     * </pre>
     *
     * <code>int32 offset = 11;</code>
     * @param value The offset to set.
     * @return This builder for chaining.
     */
    public Builder setOffset(int value) {
      
      offset_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 跳过行数
     * </pre>
     *
     * <code>int32 offset = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearOffset() {
      
      offset_ = 0;
      onChanged();
      return this;
    }

    private java.lang.Object sort_ = "";
    /**
     * <pre>
     * 排序字段
     * </pre>
     *
     * <code>string sort = 12;</code>
     * @return The sort.
     */
    public java.lang.String getSort() {
      java.lang.Object ref = sort_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sort_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 排序字段
     * </pre>
     *
     * <code>string sort = 12;</code>
     * @return The bytes for sort.
     */
    public com.google.protobuf.ByteString
        getSortBytes() {
      java.lang.Object ref = sort_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sort_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 排序字段
     * </pre>
     *
     * <code>string sort = 12;</code>
     * @param value The sort to set.
     * @return This builder for chaining.
     */
    public Builder setSort(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      sort_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 排序字段
     * </pre>
     *
     * <code>string sort = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearSort() {
      
      sort_ = getDefaultInstance().getSort();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 排序字段
     * </pre>
     *
     * <code>string sort = 12;</code>
     * @param value The bytes for sort to set.
     * @return This builder for chaining.
     */
    public Builder setSortBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      sort_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object order_ = "";
    /**
     * <pre>
     * 排序顺序
     * </pre>
     *
     * <code>string order = 13;</code>
     * @return The order.
     */
    public java.lang.String getOrder() {
      java.lang.Object ref = order_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        order_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 排序顺序
     * </pre>
     *
     * <code>string order = 13;</code>
     * @return The bytes for order.
     */
    public com.google.protobuf.ByteString
        getOrderBytes() {
      java.lang.Object ref = order_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        order_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 排序顺序
     * </pre>
     *
     * <code>string order = 13;</code>
     * @param value The order to set.
     * @return This builder for chaining.
     */
    public Builder setOrder(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      order_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 排序顺序
     * </pre>
     *
     * <code>string order = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearOrder() {
      
      order_ = getDefaultInstance().getOrder();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 排序顺序
     * </pre>
     *
     * <code>string order = 13;</code>
     * @param value The bytes for order to set.
     * @return This builder for chaining.
     */
    public Builder setOrderBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      order_ = value;
      onChanged();
      return this;
    }

    private boolean includeTotal_ ;
    /**
     * <pre>
     * 返回总条数
     * </pre>
     *
     * <code>bool include_total = 14;</code>
     * @return The includeTotal.
     */
    @java.lang.Override
    public boolean getIncludeTotal() {
      return includeTotal_;
    }
    /**
     * <pre>
     * 返回总条数
     * </pre>
     *
     * <code>bool include_total = 14;</code>
     * @param value The includeTotal to set.
     * @return This builder for chaining.
     */
    public Builder setIncludeTotal(boolean value) {
      
      includeTotal_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 返回总条数
     * </pre>
     *
     * <code>bool include_total = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearIncludeTotal() {
      
      includeTotal_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object search_ = "";
    /**
     * <pre>
     * 要模糊查询的字符串
     * </pre>
     *
     * <code>string search = 15;</code>
     * @return The search.
     */
    public java.lang.String getSearch() {
      java.lang.Object ref = search_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        search_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 要模糊查询的字符串
     * </pre>
     *
     * <code>string search = 15;</code>
     * @return The bytes for search.
     */
    public com.google.protobuf.ByteString
        getSearchBytes() {
      java.lang.Object ref = search_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        search_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 要模糊查询的字符串
     * </pre>
     *
     * <code>string search = 15;</code>
     * @param value The search to set.
     * @return This builder for chaining.
     */
    public Builder setSearch(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      search_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 要模糊查询的字符串
     * </pre>
     *
     * <code>string search = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearSearch() {
      
      search_ = getDefaultInstance().getSearch();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 要模糊查询的字符串
     * </pre>
     *
     * <code>string search = 15;</code>
     * @param value The bytes for search to set.
     * @return This builder for chaining.
     */
    public Builder setSearchBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      search_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object searchFields_ = "";
    /**
     * <pre>
     * 要查询的字段, 多个逗号隔开;
     * 如果传入relation.[relation name].[field], 则会顺带搜索关联的数据,
     * 例: 搜索store数据时, search_fields=relation.branch.name则会搜索管理区域名称,
     * 找到匹配的管理区域, 然后找到关联到这些管理区域以及所有下级区域的门店
     * </pre>
     *
     * <code>string search_fields = 16;</code>
     * @return The searchFields.
     */
    public java.lang.String getSearchFields() {
      java.lang.Object ref = searchFields_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        searchFields_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 要查询的字段, 多个逗号隔开;
     * 如果传入relation.[relation name].[field], 则会顺带搜索关联的数据,
     * 例: 搜索store数据时, search_fields=relation.branch.name则会搜索管理区域名称,
     * 找到匹配的管理区域, 然后找到关联到这些管理区域以及所有下级区域的门店
     * </pre>
     *
     * <code>string search_fields = 16;</code>
     * @return The bytes for searchFields.
     */
    public com.google.protobuf.ByteString
        getSearchFieldsBytes() {
      java.lang.Object ref = searchFields_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        searchFields_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 要查询的字段, 多个逗号隔开;
     * 如果传入relation.[relation name].[field], 则会顺带搜索关联的数据,
     * 例: 搜索store数据时, search_fields=relation.branch.name则会搜索管理区域名称,
     * 找到匹配的管理区域, 然后找到关联到这些管理区域以及所有下级区域的门店
     * </pre>
     *
     * <code>string search_fields = 16;</code>
     * @param value The searchFields to set.
     * @return This builder for chaining.
     */
    public Builder setSearchFields(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      searchFields_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 要查询的字段, 多个逗号隔开;
     * 如果传入relation.[relation name].[field], 则会顺带搜索关联的数据,
     * 例: 搜索store数据时, search_fields=relation.branch.name则会搜索管理区域名称,
     * 找到匹配的管理区域, 然后找到关联到这些管理区域以及所有下级区域的门店
     * </pre>
     *
     * <code>string search_fields = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearSearchFields() {
      
      searchFields_ = getDefaultInstance().getSearchFields();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 要查询的字段, 多个逗号隔开;
     * 如果传入relation.[relation name].[field], 则会顺带搜索关联的数据,
     * 例: 搜索store数据时, search_fields=relation.branch.name则会搜索管理区域名称,
     * 找到匹配的管理区域, 然后找到关联到这些管理区域以及所有下级区域的门店
     * </pre>
     *
     * <code>string search_fields = 16;</code>
     * @param value The bytes for searchFields to set.
     * @return This builder for chaining.
     */
    public Builder setSearchFieldsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      searchFields_ = value;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.LongList ids_ = emptyLongList();
    private void ensureIdsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        ids_ = mutableCopy(ids_);
        bitField0_ |= 0x00000001;
       }
    }
    /**
     * <pre>
     * 按id列表查询
     * </pre>
     *
     * <code>repeated uint64 ids = 17;</code>
     * @return A list containing the ids.
     */
    public java.util.List<java.lang.Long>
        getIdsList() {
      return ((bitField0_ & 0x00000001) != 0) ?
               java.util.Collections.unmodifiableList(ids_) : ids_;
    }
    /**
     * <pre>
     * 按id列表查询
     * </pre>
     *
     * <code>repeated uint64 ids = 17;</code>
     * @return The count of ids.
     */
    public int getIdsCount() {
      return ids_.size();
    }
    /**
     * <pre>
     * 按id列表查询
     * </pre>
     *
     * <code>repeated uint64 ids = 17;</code>
     * @param index The index of the element to return.
     * @return The ids at the given index.
     */
    public long getIds(int index) {
      return ids_.getLong(index);
    }
    /**
     * <pre>
     * 按id列表查询
     * </pre>
     *
     * <code>repeated uint64 ids = 17;</code>
     * @param index The index to set the value at.
     * @param value The ids to set.
     * @return This builder for chaining.
     */
    public Builder setIds(
        int index, long value) {
      ensureIdsIsMutable();
      ids_.setLong(index, value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 按id列表查询
     * </pre>
     *
     * <code>repeated uint64 ids = 17;</code>
     * @param value The ids to add.
     * @return This builder for chaining.
     */
    public Builder addIds(long value) {
      ensureIdsIsMutable();
      ids_.addLong(value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 按id列表查询
     * </pre>
     *
     * <code>repeated uint64 ids = 17;</code>
     * @param values The ids to add.
     * @return This builder for chaining.
     */
    public Builder addAllIds(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureIdsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, ids_);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 按id列表查询
     * </pre>
     *
     * <code>repeated uint64 ids = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearIds() {
      ids_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }

    private com.google.protobuf.Struct filters_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> filtersBuilder_;
    /**
     * <pre>
     * 按字段过滤
     * </pre>
     *
     * <code>.google.protobuf.Struct filters = 18;</code>
     * @return Whether the filters field is set.
     */
    public boolean hasFilters() {
      return filtersBuilder_ != null || filters_ != null;
    }
    /**
     * <pre>
     * 按字段过滤
     * </pre>
     *
     * <code>.google.protobuf.Struct filters = 18;</code>
     * @return The filters.
     */
    public com.google.protobuf.Struct getFilters() {
      if (filtersBuilder_ == null) {
        return filters_ == null ? com.google.protobuf.Struct.getDefaultInstance() : filters_;
      } else {
        return filtersBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 按字段过滤
     * </pre>
     *
     * <code>.google.protobuf.Struct filters = 18;</code>
     */
    public Builder setFilters(com.google.protobuf.Struct value) {
      if (filtersBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        filters_ = value;
        onChanged();
      } else {
        filtersBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 按字段过滤
     * </pre>
     *
     * <code>.google.protobuf.Struct filters = 18;</code>
     */
    public Builder setFilters(
        com.google.protobuf.Struct.Builder builderForValue) {
      if (filtersBuilder_ == null) {
        filters_ = builderForValue.build();
        onChanged();
      } else {
        filtersBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 按字段过滤
     * </pre>
     *
     * <code>.google.protobuf.Struct filters = 18;</code>
     */
    public Builder mergeFilters(com.google.protobuf.Struct value) {
      if (filtersBuilder_ == null) {
        if (filters_ != null) {
          filters_ =
            com.google.protobuf.Struct.newBuilder(filters_).mergeFrom(value).buildPartial();
        } else {
          filters_ = value;
        }
        onChanged();
      } else {
        filtersBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 按字段过滤
     * </pre>
     *
     * <code>.google.protobuf.Struct filters = 18;</code>
     */
    public Builder clearFilters() {
      if (filtersBuilder_ == null) {
        filters_ = null;
        onChanged();
      } else {
        filters_ = null;
        filtersBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 按字段过滤
     * </pre>
     *
     * <code>.google.protobuf.Struct filters = 18;</code>
     */
    public com.google.protobuf.Struct.Builder getFiltersBuilder() {
      
      onChanged();
      return getFiltersFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 按字段过滤
     * </pre>
     *
     * <code>.google.protobuf.Struct filters = 18;</code>
     */
    public com.google.protobuf.StructOrBuilder getFiltersOrBuilder() {
      if (filtersBuilder_ != null) {
        return filtersBuilder_.getMessageOrBuilder();
      } else {
        return filters_ == null ?
            com.google.protobuf.Struct.getDefaultInstance() : filters_;
      }
    }
    /**
     * <pre>
     * 按字段过滤
     * </pre>
     *
     * <code>.google.protobuf.Struct filters = 18;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> 
        getFiltersFieldBuilder() {
      if (filtersBuilder_ == null) {
        filtersBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder>(
                getFilters(),
                getParentForChildren(),
                isClean());
        filters_ = null;
      }
      return filtersBuilder_;
    }

    private com.google.protobuf.Struct relationFilters_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> relationFiltersBuilder_;
    /**
     * <pre>
     * 按关系深层次递归过滤(获取包含下级节点的数据)
     * </pre>
     *
     * <code>.google.protobuf.Struct relation_filters = 19;</code>
     * @return Whether the relationFilters field is set.
     */
    public boolean hasRelationFilters() {
      return relationFiltersBuilder_ != null || relationFilters_ != null;
    }
    /**
     * <pre>
     * 按关系深层次递归过滤(获取包含下级节点的数据)
     * </pre>
     *
     * <code>.google.protobuf.Struct relation_filters = 19;</code>
     * @return The relationFilters.
     */
    public com.google.protobuf.Struct getRelationFilters() {
      if (relationFiltersBuilder_ == null) {
        return relationFilters_ == null ? com.google.protobuf.Struct.getDefaultInstance() : relationFilters_;
      } else {
        return relationFiltersBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 按关系深层次递归过滤(获取包含下级节点的数据)
     * </pre>
     *
     * <code>.google.protobuf.Struct relation_filters = 19;</code>
     */
    public Builder setRelationFilters(com.google.protobuf.Struct value) {
      if (relationFiltersBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        relationFilters_ = value;
        onChanged();
      } else {
        relationFiltersBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 按关系深层次递归过滤(获取包含下级节点的数据)
     * </pre>
     *
     * <code>.google.protobuf.Struct relation_filters = 19;</code>
     */
    public Builder setRelationFilters(
        com.google.protobuf.Struct.Builder builderForValue) {
      if (relationFiltersBuilder_ == null) {
        relationFilters_ = builderForValue.build();
        onChanged();
      } else {
        relationFiltersBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 按关系深层次递归过滤(获取包含下级节点的数据)
     * </pre>
     *
     * <code>.google.protobuf.Struct relation_filters = 19;</code>
     */
    public Builder mergeRelationFilters(com.google.protobuf.Struct value) {
      if (relationFiltersBuilder_ == null) {
        if (relationFilters_ != null) {
          relationFilters_ =
            com.google.protobuf.Struct.newBuilder(relationFilters_).mergeFrom(value).buildPartial();
        } else {
          relationFilters_ = value;
        }
        onChanged();
      } else {
        relationFiltersBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 按关系深层次递归过滤(获取包含下级节点的数据)
     * </pre>
     *
     * <code>.google.protobuf.Struct relation_filters = 19;</code>
     */
    public Builder clearRelationFilters() {
      if (relationFiltersBuilder_ == null) {
        relationFilters_ = null;
        onChanged();
      } else {
        relationFilters_ = null;
        relationFiltersBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 按关系深层次递归过滤(获取包含下级节点的数据)
     * </pre>
     *
     * <code>.google.protobuf.Struct relation_filters = 19;</code>
     */
    public com.google.protobuf.Struct.Builder getRelationFiltersBuilder() {
      
      onChanged();
      return getRelationFiltersFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 按关系深层次递归过滤(获取包含下级节点的数据)
     * </pre>
     *
     * <code>.google.protobuf.Struct relation_filters = 19;</code>
     */
    public com.google.protobuf.StructOrBuilder getRelationFiltersOrBuilder() {
      if (relationFiltersBuilder_ != null) {
        return relationFiltersBuilder_.getMessageOrBuilder();
      } else {
        return relationFilters_ == null ?
            com.google.protobuf.Struct.getDefaultInstance() : relationFilters_;
      }
    }
    /**
     * <pre>
     * 按关系深层次递归过滤(获取包含下级节点的数据)
     * </pre>
     *
     * <code>.google.protobuf.Struct relation_filters = 19;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> 
        getRelationFiltersFieldBuilder() {
      if (relationFiltersBuilder_ == null) {
        relationFiltersBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder>(
                getRelationFilters(),
                getParentForChildren(),
                isClean());
        relationFilters_ = null;
      }
      return relationFiltersBuilder_;
    }

    private java.lang.Object lan_ = "";
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 20;</code>
     * @return The lan.
     */
    public java.lang.String getLan() {
      java.lang.Object ref = lan_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        lan_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 20;</code>
     * @return The bytes for lan.
     */
    public com.google.protobuf.ByteString
        getLanBytes() {
      java.lang.Object ref = lan_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        lan_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 20;</code>
     * @param value The lan to set.
     * @return This builder for chaining.
     */
    public Builder setLan(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      lan_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 20;</code>
     * @return This builder for chaining.
     */
    public Builder clearLan() {
      
      lan_ = getDefaultInstance().getLan();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 20;</code>
     * @param value The bytes for lan to set.
     * @return This builder for chaining.
     */
    public Builder setLanBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      lan_ = value;
      onChanged();
      return this;
    }

    private boolean includeAllLocalizations_ ;
    /**
     * <pre>
     * 是否包含所有本地化信息
     * </pre>
     *
     * <code>bool include_all_localizations = 21;</code>
     * @return The includeAllLocalizations.
     */
    @java.lang.Override
    public boolean getIncludeAllLocalizations() {
      return includeAllLocalizations_;
    }
    /**
     * <pre>
     * 是否包含所有本地化信息
     * </pre>
     *
     * <code>bool include_all_localizations = 21;</code>
     * @param value The includeAllLocalizations to set.
     * @return This builder for chaining.
     */
    public Builder setIncludeAllLocalizations(boolean value) {
      
      includeAllLocalizations_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 是否包含所有本地化信息
     * </pre>
     *
     * <code>bool include_all_localizations = 21;</code>
     * @return This builder for chaining.
     */
    public Builder clearIncludeAllLocalizations() {
      
      includeAllLocalizations_ = false;
      onChanged();
      return this;
    }

    private boolean isRequest_ ;
    /**
     * <pre>
     * 返回 state=DRAFT 或 state=ENABLED且有pending record的记录
     * </pre>
     *
     * <code>bool is_request = 22;</code>
     * @return The isRequest.
     */
    @java.lang.Override
    public boolean getIsRequest() {
      return isRequest_;
    }
    /**
     * <pre>
     * 返回 state=DRAFT 或 state=ENABLED且有pending record的记录
     * </pre>
     *
     * <code>bool is_request = 22;</code>
     * @param value The isRequest to set.
     * @return This builder for chaining.
     */
    public Builder setIsRequest(boolean value) {
      
      isRequest_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 返回 state=DRAFT 或 state=ENABLED且有pending record的记录
     * </pre>
     *
     * <code>bool is_request = 22;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsRequest() {
      
      isRequest_ = false;
      onChanged();
      return this;
    }

    private boolean getCommon_ ;
    /**
     * <pre>
     * 多租户下公共数据
     * </pre>
     *
     * <code>bool get_common = 23;</code>
     * @return The getCommon.
     */
    @java.lang.Override
    public boolean getGetCommon() {
      return getCommon_;
    }
    /**
     * <pre>
     * 多租户下公共数据
     * </pre>
     *
     * <code>bool get_common = 23;</code>
     * @param value The getCommon to set.
     * @return This builder for chaining.
     */
    public Builder setGetCommon(boolean value) {
      
      getCommon_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 多租户下公共数据
     * </pre>
     *
     * <code>bool get_common = 23;</code>
     * @return This builder for chaining.
     */
    public Builder clearGetCommon() {
      
      getCommon_ = false;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:entity.ListEntityRequest)
  }

  // @@protoc_insertion_point(class_scope:entity.ListEntityRequest)
  private static final cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest();
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ListEntityRequest>
      PARSER = new com.google.protobuf.AbstractParser<ListEntityRequest>() {
    @java.lang.Override
    public ListEntityRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ListEntityRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ListEntityRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ListEntityRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.metadata.ListEntityRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

