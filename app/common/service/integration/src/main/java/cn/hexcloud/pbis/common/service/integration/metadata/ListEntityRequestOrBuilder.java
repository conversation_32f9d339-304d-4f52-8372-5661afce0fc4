// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface ListEntityRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.ListEntityRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 1;</code>
   * @return The schemaName.
   */
  java.lang.String getSchemaName();
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 1;</code>
   * @return The bytes for schemaName.
   */
  com.google.protobuf.ByteString
      getSchemaNameBytes();

  /**
   * <pre>
   * 指定要返回相关数据状态的记录, 默认只返回enabled, 多个状态用逗号隔开，
   * 如state=draft,disabled; state=all时返回所有数据状态的记录
   * </pre>
   *
   * <code>string state = 2;</code>
   * @return The state.
   */
  java.lang.String getState();
  /**
   * <pre>
   * 指定要返回相关数据状态的记录, 默认只返回enabled, 多个状态用逗号隔开，
   * 如state=draft,disabled; state=all时返回所有数据状态的记录
   * </pre>
   *
   * <code>string state = 2;</code>
   * @return The bytes for state.
   */
  com.google.protobuf.ByteString
      getStateBytes();

  /**
   * <pre>
   * 是否包含数据状态
   * </pre>
   *
   * <code>bool include_state = 3;</code>
   * @return The includeState.
   */
  boolean getIncludeState();

  /**
   * <pre>
   * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string code = 4;</code>
   * @return The code.
   */
  java.lang.String getCode();
  /**
   * <pre>
   * 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string code = 4;</code>
   * @return The bytes for code.
   */
  com.google.protobuf.ByteString
      getCodeBytes();

  /**
   * <pre>
   * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string relation = 5;</code>
   * @return The relation.
   */
  java.lang.String getRelation();
  /**
   * <pre>
   * 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
   * </pre>
   *
   * <code>string relation = 5;</code>
   * @return The bytes for relation.
   */
  com.google.protobuf.ByteString
      getRelationBytes();

  /**
   * <pre>
   * 除code和relation之外需要返回的字段, 多个以逗号隔开
   * </pre>
   *
   * <code>string return_fields = 6;</code>
   * @return The returnFields.
   */
  java.lang.String getReturnFields();
  /**
   * <pre>
   * 除code和relation之外需要返回的字段, 多个以逗号隔开
   * </pre>
   *
   * <code>string return_fields = 6;</code>
   * @return The bytes for returnFields.
   */
  com.google.protobuf.ByteString
      getReturnFieldsBytes();

  /**
   * <pre>
   * include_pending_changes=true时, 如果相关记录包含pending的修改属性,
   * 则会获取修改属性attach到返回记录中fields_pending字段
   * </pre>
   *
   * <code>bool include_pending_changes = 7;</code>
   * @return The includePendingChanges.
   */
  boolean getIncludePendingChanges();

  /**
   * <pre>
   * include_pending_record=true时, 如果相关记录包含pending的修改属性,
   * 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的record_pending字段
   * </pre>
   *
   * <code>bool include_pending_record = 8;</code>
   * @return The includePendingRecord.
   */
  boolean getIncludePendingRecord();

  /**
   * <pre>
   * include_parents=true返回所有父级节点
   * </pre>
   *
   * <code>bool include_parents = 9;</code>
   * @return The includeParents.
   */
  boolean getIncludeParents();

  /**
   * <pre>
   * 分页大小
   * </pre>
   *
   * <code>int32 limit = 10;</code>
   * @return The limit.
   */
  int getLimit();

  /**
   * <pre>
   * 跳过行数
   * </pre>
   *
   * <code>int32 offset = 11;</code>
   * @return The offset.
   */
  int getOffset();

  /**
   * <pre>
   * 排序字段
   * </pre>
   *
   * <code>string sort = 12;</code>
   * @return The sort.
   */
  java.lang.String getSort();
  /**
   * <pre>
   * 排序字段
   * </pre>
   *
   * <code>string sort = 12;</code>
   * @return The bytes for sort.
   */
  com.google.protobuf.ByteString
      getSortBytes();

  /**
   * <pre>
   * 排序顺序
   * </pre>
   *
   * <code>string order = 13;</code>
   * @return The order.
   */
  java.lang.String getOrder();
  /**
   * <pre>
   * 排序顺序
   * </pre>
   *
   * <code>string order = 13;</code>
   * @return The bytes for order.
   */
  com.google.protobuf.ByteString
      getOrderBytes();

  /**
   * <pre>
   * 返回总条数
   * </pre>
   *
   * <code>bool include_total = 14;</code>
   * @return The includeTotal.
   */
  boolean getIncludeTotal();

  /**
   * <pre>
   * 要模糊查询的字符串
   * </pre>
   *
   * <code>string search = 15;</code>
   * @return The search.
   */
  java.lang.String getSearch();
  /**
   * <pre>
   * 要模糊查询的字符串
   * </pre>
   *
   * <code>string search = 15;</code>
   * @return The bytes for search.
   */
  com.google.protobuf.ByteString
      getSearchBytes();

  /**
   * <pre>
   * 要查询的字段, 多个逗号隔开;
   * 如果传入relation.[relation name].[field], 则会顺带搜索关联的数据,
   * 例: 搜索store数据时, search_fields=relation.branch.name则会搜索管理区域名称,
   * 找到匹配的管理区域, 然后找到关联到这些管理区域以及所有下级区域的门店
   * </pre>
   *
   * <code>string search_fields = 16;</code>
   * @return The searchFields.
   */
  java.lang.String getSearchFields();
  /**
   * <pre>
   * 要查询的字段, 多个逗号隔开;
   * 如果传入relation.[relation name].[field], 则会顺带搜索关联的数据,
   * 例: 搜索store数据时, search_fields=relation.branch.name则会搜索管理区域名称,
   * 找到匹配的管理区域, 然后找到关联到这些管理区域以及所有下级区域的门店
   * </pre>
   *
   * <code>string search_fields = 16;</code>
   * @return The bytes for searchFields.
   */
  com.google.protobuf.ByteString
      getSearchFieldsBytes();

  /**
   * <pre>
   * 按id列表查询
   * </pre>
   *
   * <code>repeated uint64 ids = 17;</code>
   * @return A list containing the ids.
   */
  java.util.List<java.lang.Long> getIdsList();
  /**
   * <pre>
   * 按id列表查询
   * </pre>
   *
   * <code>repeated uint64 ids = 17;</code>
   * @return The count of ids.
   */
  int getIdsCount();
  /**
   * <pre>
   * 按id列表查询
   * </pre>
   *
   * <code>repeated uint64 ids = 17;</code>
   * @param index The index of the element to return.
   * @return The ids at the given index.
   */
  long getIds(int index);

  /**
   * <pre>
   * 按字段过滤
   * </pre>
   *
   * <code>.google.protobuf.Struct filters = 18;</code>
   * @return Whether the filters field is set.
   */
  boolean hasFilters();
  /**
   * <pre>
   * 按字段过滤
   * </pre>
   *
   * <code>.google.protobuf.Struct filters = 18;</code>
   * @return The filters.
   */
  com.google.protobuf.Struct getFilters();
  /**
   * <pre>
   * 按字段过滤
   * </pre>
   *
   * <code>.google.protobuf.Struct filters = 18;</code>
   */
  com.google.protobuf.StructOrBuilder getFiltersOrBuilder();

  /**
   * <pre>
   * 按关系深层次递归过滤(获取包含下级节点的数据)
   * </pre>
   *
   * <code>.google.protobuf.Struct relation_filters = 19;</code>
   * @return Whether the relationFilters field is set.
   */
  boolean hasRelationFilters();
  /**
   * <pre>
   * 按关系深层次递归过滤(获取包含下级节点的数据)
   * </pre>
   *
   * <code>.google.protobuf.Struct relation_filters = 19;</code>
   * @return The relationFilters.
   */
  com.google.protobuf.Struct getRelationFilters();
  /**
   * <pre>
   * 按关系深层次递归过滤(获取包含下级节点的数据)
   * </pre>
   *
   * <code>.google.protobuf.Struct relation_filters = 19;</code>
   */
  com.google.protobuf.StructOrBuilder getRelationFiltersOrBuilder();

  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 20;</code>
   * @return The lan.
   */
  java.lang.String getLan();
  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 20;</code>
   * @return The bytes for lan.
   */
  com.google.protobuf.ByteString
      getLanBytes();

  /**
   * <pre>
   * 是否包含所有本地化信息
   * </pre>
   *
   * <code>bool include_all_localizations = 21;</code>
   * @return The includeAllLocalizations.
   */
  boolean getIncludeAllLocalizations();

  /**
   * <pre>
   * 返回 state=DRAFT 或 state=ENABLED且有pending record的记录
   * </pre>
   *
   * <code>bool is_request = 22;</code>
   * @return The isRequest.
   */
  boolean getIsRequest();

  /**
   * <pre>
   * 多租户下公共数据
   * </pre>
   *
   * <code>bool get_common = 23;</code>
   * @return The getCommon.
   */
  boolean getGetCommon();
}
