// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface ListEntityResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.ListEntityResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .entity.Entity rows = 1;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.integration.metadata.Entity> 
      getRowsList();
  /**
   * <code>repeated .entity.Entity rows = 1;</code>
   */
  cn.hexcloud.pbis.common.service.integration.metadata.Entity getRows(int index);
  /**
   * <code>repeated .entity.Entity rows = 1;</code>
   */
  int getRowsCount();
  /**
   * <code>repeated .entity.Entity rows = 1;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.integration.metadata.EntityOrBuilder> 
      getRowsOrBuilderList();
  /**
   * <code>repeated .entity.Entity rows = 1;</code>
   */
  cn.hexcloud.pbis.common.service.integration.metadata.EntityOrBuilder getRowsOrBuilder(
      int index);

  /**
   * <code>int32 total = 2;</code>
   * @return The total.
   */
  int getTotal();
}
