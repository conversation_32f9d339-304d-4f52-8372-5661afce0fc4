// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

/**
 * Protobuf type {@code entity.ListEntityTaskRequest}
 */
public final class ListEntityTaskRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:entity.ListEntityTaskRequest)
    ListEntityTaskRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ListEntityTaskRequest.newBuilder() to construct.
  private ListEntityTaskRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ListEntityTaskRequest() {
    recordIds_ = emptyLongList();
    schemaName_ = "";
    status_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    processStatus_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    search_ = "";
    searchFields_ = "";
    ids_ = emptyLongList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new ListEntityTaskRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ListEntityTaskRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              recordIds_ = newLongList();
              mutable_bitField0_ |= 0x00000001;
            }
            recordIds_.addLong(input.readUInt64());
            break;
          }
          case 10: {
            int length = input.readRawVarint32();
            int limit = input.pushLimit(length);
            if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
              recordIds_ = newLongList();
              mutable_bitField0_ |= 0x00000001;
            }
            while (input.getBytesUntilLimit() > 0) {
              recordIds_.addLong(input.readUInt64());
            }
            input.popLimit(limit);
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            schemaName_ = s;
            break;
          }
          case 26: {
            java.lang.String s = input.readStringRequireUtf8();
            if (!((mutable_bitField0_ & 0x00000002) != 0)) {
              status_ = new com.google.protobuf.LazyStringArrayList();
              mutable_bitField0_ |= 0x00000002;
            }
            status_.add(s);
            break;
          }
          case 34: {
            java.lang.String s = input.readStringRequireUtf8();
            if (!((mutable_bitField0_ & 0x00000004) != 0)) {
              processStatus_ = new com.google.protobuf.LazyStringArrayList();
              mutable_bitField0_ |= 0x00000004;
            }
            processStatus_.add(s);
            break;
          }
          case 40: {

            limit_ = input.readInt32();
            break;
          }
          case 48: {

            offset_ = input.readInt32();
            break;
          }
          case 56: {

            includeTotal_ = input.readBool();
            break;
          }
          case 66: {
            java.lang.String s = input.readStringRequireUtf8();

            search_ = s;
            break;
          }
          case 74: {
            java.lang.String s = input.readStringRequireUtf8();

            searchFields_ = s;
            break;
          }
          case 80: {
            if (!((mutable_bitField0_ & 0x00000008) != 0)) {
              ids_ = newLongList();
              mutable_bitField0_ |= 0x00000008;
            }
            ids_.addLong(input.readUInt64());
            break;
          }
          case 82: {
            int length = input.readRawVarint32();
            int limit = input.pushLimit(length);
            if (!((mutable_bitField0_ & 0x00000008) != 0) && input.getBytesUntilLimit() > 0) {
              ids_ = newLongList();
              mutable_bitField0_ |= 0x00000008;
            }
            while (input.getBytesUntilLimit() > 0) {
              ids_.addLong(input.readUInt64());
            }
            input.popLimit(limit);
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        recordIds_.makeImmutable(); // C
      }
      if (((mutable_bitField0_ & 0x00000002) != 0)) {
        status_ = status_.getUnmodifiableView();
      }
      if (((mutable_bitField0_ & 0x00000004) != 0)) {
        processStatus_ = processStatus_.getUnmodifiableView();
      }
      if (((mutable_bitField0_ & 0x00000008) != 0)) {
        ids_.makeImmutable(); // C
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_ListEntityTaskRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_ListEntityTaskRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest.class, cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest.Builder.class);
  }

  public static final int RECORD_IDS_FIELD_NUMBER = 1;
  private com.google.protobuf.Internal.LongList recordIds_;
  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>repeated uint64 record_ids = 1;</code>
   * @return A list containing the recordIds.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getRecordIdsList() {
    return recordIds_;
  }
  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>repeated uint64 record_ids = 1;</code>
   * @return The count of recordIds.
   */
  public int getRecordIdsCount() {
    return recordIds_.size();
  }
  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>repeated uint64 record_ids = 1;</code>
   * @param index The index of the element to return.
   * @return The recordIds at the given index.
   */
  public long getRecordIds(int index) {
    return recordIds_.getLong(index);
  }
  private int recordIdsMemoizedSerializedSize = -1;

  public static final int SCHEMA_NAME_FIELD_NUMBER = 2;
  private volatile java.lang.Object schemaName_;
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 2;</code>
   * @return The schemaName.
   */
  @java.lang.Override
  public java.lang.String getSchemaName() {
    java.lang.Object ref = schemaName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      schemaName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 2;</code>
   * @return The bytes for schemaName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSchemaNameBytes() {
    java.lang.Object ref = schemaName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      schemaName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int STATUS_FIELD_NUMBER = 3;
  private com.google.protobuf.LazyStringList status_;
  /**
   * <pre>
   * task 状态
   * </pre>
   *
   * <code>repeated string status = 3;</code>
   * @return A list containing the status.
   */
  public com.google.protobuf.ProtocolStringList
      getStatusList() {
    return status_;
  }
  /**
   * <pre>
   * task 状态
   * </pre>
   *
   * <code>repeated string status = 3;</code>
   * @return The count of status.
   */
  public int getStatusCount() {
    return status_.size();
  }
  /**
   * <pre>
   * task 状态
   * </pre>
   *
   * <code>repeated string status = 3;</code>
   * @param index The index of the element to return.
   * @return The status at the given index.
   */
  public java.lang.String getStatus(int index) {
    return status_.get(index);
  }
  /**
   * <pre>
   * task 状态
   * </pre>
   *
   * <code>repeated string status = 3;</code>
   * @param index The index of the value to return.
   * @return The bytes of the status at the given index.
   */
  public com.google.protobuf.ByteString
      getStatusBytes(int index) {
    return status_.getByteString(index);
  }

  public static final int PROCESS_STATUS_FIELD_NUMBER = 4;
  private com.google.protobuf.LazyStringList processStatus_;
  /**
   * <pre>
   * 批处理状态
   * </pre>
   *
   * <code>repeated string process_status = 4;</code>
   * @return A list containing the processStatus.
   */
  public com.google.protobuf.ProtocolStringList
      getProcessStatusList() {
    return processStatus_;
  }
  /**
   * <pre>
   * 批处理状态
   * </pre>
   *
   * <code>repeated string process_status = 4;</code>
   * @return The count of processStatus.
   */
  public int getProcessStatusCount() {
    return processStatus_.size();
  }
  /**
   * <pre>
   * 批处理状态
   * </pre>
   *
   * <code>repeated string process_status = 4;</code>
   * @param index The index of the element to return.
   * @return The processStatus at the given index.
   */
  public java.lang.String getProcessStatus(int index) {
    return processStatus_.get(index);
  }
  /**
   * <pre>
   * 批处理状态
   * </pre>
   *
   * <code>repeated string process_status = 4;</code>
   * @param index The index of the value to return.
   * @return The bytes of the processStatus at the given index.
   */
  public com.google.protobuf.ByteString
      getProcessStatusBytes(int index) {
    return processStatus_.getByteString(index);
  }

  public static final int LIMIT_FIELD_NUMBER = 5;
  private int limit_;
  /**
   * <pre>
   * 分页大小
   * </pre>
   *
   * <code>int32 limit = 5;</code>
   * @return The limit.
   */
  @java.lang.Override
  public int getLimit() {
    return limit_;
  }

  public static final int OFFSET_FIELD_NUMBER = 6;
  private int offset_;
  /**
   * <pre>
   * 跳过行数
   * </pre>
   *
   * <code>int32 offset = 6;</code>
   * @return The offset.
   */
  @java.lang.Override
  public int getOffset() {
    return offset_;
  }

  public static final int INCLUDE_TOTAL_FIELD_NUMBER = 7;
  private boolean includeTotal_;
  /**
   * <pre>
   * 返回总条数
   * </pre>
   *
   * <code>bool include_total = 7;</code>
   * @return The includeTotal.
   */
  @java.lang.Override
  public boolean getIncludeTotal() {
    return includeTotal_;
  }

  public static final int SEARCH_FIELD_NUMBER = 8;
  private volatile java.lang.Object search_;
  /**
   * <pre>
   * 要模糊查询的字符串
   * </pre>
   *
   * <code>string search = 8;</code>
   * @return The search.
   */
  @java.lang.Override
  public java.lang.String getSearch() {
    java.lang.Object ref = search_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      search_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 要模糊查询的字符串
   * </pre>
   *
   * <code>string search = 8;</code>
   * @return The bytes for search.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSearchBytes() {
    java.lang.Object ref = search_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      search_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SEARCH_FIELDS_FIELD_NUMBER = 9;
  private volatile java.lang.Object searchFields_;
  /**
   * <pre>
   * 要查询的字段, 多个逗号隔开;
   * </pre>
   *
   * <code>string search_fields = 9;</code>
   * @return The searchFields.
   */
  @java.lang.Override
  public java.lang.String getSearchFields() {
    java.lang.Object ref = searchFields_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      searchFields_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 要查询的字段, 多个逗号隔开;
   * </pre>
   *
   * <code>string search_fields = 9;</code>
   * @return The bytes for searchFields.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSearchFieldsBytes() {
    java.lang.Object ref = searchFields_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      searchFields_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IDS_FIELD_NUMBER = 10;
  private com.google.protobuf.Internal.LongList ids_;
  /**
   * <pre>
   * 按id列表查询
   * </pre>
   *
   * <code>repeated uint64 ids = 10;</code>
   * @return A list containing the ids.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getIdsList() {
    return ids_;
  }
  /**
   * <pre>
   * 按id列表查询
   * </pre>
   *
   * <code>repeated uint64 ids = 10;</code>
   * @return The count of ids.
   */
  public int getIdsCount() {
    return ids_.size();
  }
  /**
   * <pre>
   * 按id列表查询
   * </pre>
   *
   * <code>repeated uint64 ids = 10;</code>
   * @param index The index of the element to return.
   * @return The ids at the given index.
   */
  public long getIds(int index) {
    return ids_.getLong(index);
  }
  private int idsMemoizedSerializedSize = -1;

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    getSerializedSize();
    if (getRecordIdsList().size() > 0) {
      output.writeUInt32NoTag(10);
      output.writeUInt32NoTag(recordIdsMemoizedSerializedSize);
    }
    for (int i = 0; i < recordIds_.size(); i++) {
      output.writeUInt64NoTag(recordIds_.getLong(i));
    }
    if (!getSchemaNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, schemaName_);
    }
    for (int i = 0; i < status_.size(); i++) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, status_.getRaw(i));
    }
    for (int i = 0; i < processStatus_.size(); i++) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, processStatus_.getRaw(i));
    }
    if (limit_ != 0) {
      output.writeInt32(5, limit_);
    }
    if (offset_ != 0) {
      output.writeInt32(6, offset_);
    }
    if (includeTotal_ != false) {
      output.writeBool(7, includeTotal_);
    }
    if (!getSearchBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, search_);
    }
    if (!getSearchFieldsBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, searchFields_);
    }
    if (getIdsList().size() > 0) {
      output.writeUInt32NoTag(82);
      output.writeUInt32NoTag(idsMemoizedSerializedSize);
    }
    for (int i = 0; i < ids_.size(); i++) {
      output.writeUInt64NoTag(ids_.getLong(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    {
      int dataSize = 0;
      for (int i = 0; i < recordIds_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeUInt64SizeNoTag(recordIds_.getLong(i));
      }
      size += dataSize;
      if (!getRecordIdsList().isEmpty()) {
        size += 1;
        size += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(dataSize);
      }
      recordIdsMemoizedSerializedSize = dataSize;
    }
    if (!getSchemaNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, schemaName_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < status_.size(); i++) {
        dataSize += computeStringSizeNoTag(status_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getStatusList().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < processStatus_.size(); i++) {
        dataSize += computeStringSizeNoTag(processStatus_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getProcessStatusList().size();
    }
    if (limit_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, limit_);
    }
    if (offset_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, offset_);
    }
    if (includeTotal_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(7, includeTotal_);
    }
    if (!getSearchBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, search_);
    }
    if (!getSearchFieldsBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, searchFields_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < ids_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeUInt64SizeNoTag(ids_.getLong(i));
      }
      size += dataSize;
      if (!getIdsList().isEmpty()) {
        size += 1;
        size += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(dataSize);
      }
      idsMemoizedSerializedSize = dataSize;
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest other = (cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest) obj;

    if (!getRecordIdsList()
        .equals(other.getRecordIdsList())) return false;
    if (!getSchemaName()
        .equals(other.getSchemaName())) return false;
    if (!getStatusList()
        .equals(other.getStatusList())) return false;
    if (!getProcessStatusList()
        .equals(other.getProcessStatusList())) return false;
    if (getLimit()
        != other.getLimit()) return false;
    if (getOffset()
        != other.getOffset()) return false;
    if (getIncludeTotal()
        != other.getIncludeTotal()) return false;
    if (!getSearch()
        .equals(other.getSearch())) return false;
    if (!getSearchFields()
        .equals(other.getSearchFields())) return false;
    if (!getIdsList()
        .equals(other.getIdsList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getRecordIdsCount() > 0) {
      hash = (37 * hash) + RECORD_IDS_FIELD_NUMBER;
      hash = (53 * hash) + getRecordIdsList().hashCode();
    }
    hash = (37 * hash) + SCHEMA_NAME_FIELD_NUMBER;
    hash = (53 * hash) + getSchemaName().hashCode();
    if (getStatusCount() > 0) {
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatusList().hashCode();
    }
    if (getProcessStatusCount() > 0) {
      hash = (37 * hash) + PROCESS_STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getProcessStatusList().hashCode();
    }
    hash = (37 * hash) + LIMIT_FIELD_NUMBER;
    hash = (53 * hash) + getLimit();
    hash = (37 * hash) + OFFSET_FIELD_NUMBER;
    hash = (53 * hash) + getOffset();
    hash = (37 * hash) + INCLUDE_TOTAL_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIncludeTotal());
    hash = (37 * hash) + SEARCH_FIELD_NUMBER;
    hash = (53 * hash) + getSearch().hashCode();
    hash = (37 * hash) + SEARCH_FIELDS_FIELD_NUMBER;
    hash = (53 * hash) + getSearchFields().hashCode();
    if (getIdsCount() > 0) {
      hash = (37 * hash) + IDS_FIELD_NUMBER;
      hash = (53 * hash) + getIdsList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code entity.ListEntityTaskRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:entity.ListEntityTaskRequest)
      cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_ListEntityTaskRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_ListEntityTaskRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest.class, cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      recordIds_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000001);
      schemaName_ = "";

      status_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      bitField0_ = (bitField0_ & ~0x00000002);
      processStatus_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      bitField0_ = (bitField0_ & ~0x00000004);
      limit_ = 0;

      offset_ = 0;

      includeTotal_ = false;

      search_ = "";

      searchFields_ = "";

      ids_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000008);
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_ListEntityTaskRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest build() {
      cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest buildPartial() {
      cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest result = new cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest(this);
      int from_bitField0_ = bitField0_;
      if (((bitField0_ & 0x00000001) != 0)) {
        recordIds_.makeImmutable();
        bitField0_ = (bitField0_ & ~0x00000001);
      }
      result.recordIds_ = recordIds_;
      result.schemaName_ = schemaName_;
      if (((bitField0_ & 0x00000002) != 0)) {
        status_ = status_.getUnmodifiableView();
        bitField0_ = (bitField0_ & ~0x00000002);
      }
      result.status_ = status_;
      if (((bitField0_ & 0x00000004) != 0)) {
        processStatus_ = processStatus_.getUnmodifiableView();
        bitField0_ = (bitField0_ & ~0x00000004);
      }
      result.processStatus_ = processStatus_;
      result.limit_ = limit_;
      result.offset_ = offset_;
      result.includeTotal_ = includeTotal_;
      result.search_ = search_;
      result.searchFields_ = searchFields_;
      if (((bitField0_ & 0x00000008) != 0)) {
        ids_.makeImmutable();
        bitField0_ = (bitField0_ & ~0x00000008);
      }
      result.ids_ = ids_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest other) {
      if (other == cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest.getDefaultInstance()) return this;
      if (!other.recordIds_.isEmpty()) {
        if (recordIds_.isEmpty()) {
          recordIds_ = other.recordIds_;
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          ensureRecordIdsIsMutable();
          recordIds_.addAll(other.recordIds_);
        }
        onChanged();
      }
      if (!other.getSchemaName().isEmpty()) {
        schemaName_ = other.schemaName_;
        onChanged();
      }
      if (!other.status_.isEmpty()) {
        if (status_.isEmpty()) {
          status_ = other.status_;
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          ensureStatusIsMutable();
          status_.addAll(other.status_);
        }
        onChanged();
      }
      if (!other.processStatus_.isEmpty()) {
        if (processStatus_.isEmpty()) {
          processStatus_ = other.processStatus_;
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          ensureProcessStatusIsMutable();
          processStatus_.addAll(other.processStatus_);
        }
        onChanged();
      }
      if (other.getLimit() != 0) {
        setLimit(other.getLimit());
      }
      if (other.getOffset() != 0) {
        setOffset(other.getOffset());
      }
      if (other.getIncludeTotal() != false) {
        setIncludeTotal(other.getIncludeTotal());
      }
      if (!other.getSearch().isEmpty()) {
        search_ = other.search_;
        onChanged();
      }
      if (!other.getSearchFields().isEmpty()) {
        searchFields_ = other.searchFields_;
        onChanged();
      }
      if (!other.ids_.isEmpty()) {
        if (ids_.isEmpty()) {
          ids_ = other.ids_;
          bitField0_ = (bitField0_ & ~0x00000008);
        } else {
          ensureIdsIsMutable();
          ids_.addAll(other.ids_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private com.google.protobuf.Internal.LongList recordIds_ = emptyLongList();
    private void ensureRecordIdsIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        recordIds_ = mutableCopy(recordIds_);
        bitField0_ |= 0x00000001;
       }
    }
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>repeated uint64 record_ids = 1;</code>
     * @return A list containing the recordIds.
     */
    public java.util.List<java.lang.Long>
        getRecordIdsList() {
      return ((bitField0_ & 0x00000001) != 0) ?
               java.util.Collections.unmodifiableList(recordIds_) : recordIds_;
    }
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>repeated uint64 record_ids = 1;</code>
     * @return The count of recordIds.
     */
    public int getRecordIdsCount() {
      return recordIds_.size();
    }
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>repeated uint64 record_ids = 1;</code>
     * @param index The index of the element to return.
     * @return The recordIds at the given index.
     */
    public long getRecordIds(int index) {
      return recordIds_.getLong(index);
    }
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>repeated uint64 record_ids = 1;</code>
     * @param index The index to set the value at.
     * @param value The recordIds to set.
     * @return This builder for chaining.
     */
    public Builder setRecordIds(
        int index, long value) {
      ensureRecordIdsIsMutable();
      recordIds_.setLong(index, value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>repeated uint64 record_ids = 1;</code>
     * @param value The recordIds to add.
     * @return This builder for chaining.
     */
    public Builder addRecordIds(long value) {
      ensureRecordIdsIsMutable();
      recordIds_.addLong(value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>repeated uint64 record_ids = 1;</code>
     * @param values The recordIds to add.
     * @return This builder for chaining.
     */
    public Builder addAllRecordIds(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureRecordIdsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, recordIds_);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>repeated uint64 record_ids = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRecordIds() {
      recordIds_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }

    private java.lang.Object schemaName_ = "";
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 2;</code>
     * @return The schemaName.
     */
    public java.lang.String getSchemaName() {
      java.lang.Object ref = schemaName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        schemaName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 2;</code>
     * @return The bytes for schemaName.
     */
    public com.google.protobuf.ByteString
        getSchemaNameBytes() {
      java.lang.Object ref = schemaName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        schemaName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 2;</code>
     * @param value The schemaName to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      schemaName_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSchemaName() {
      
      schemaName_ = getDefaultInstance().getSchemaName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 2;</code>
     * @param value The bytes for schemaName to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      schemaName_ = value;
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringList status_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    private void ensureStatusIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        status_ = new com.google.protobuf.LazyStringArrayList(status_);
        bitField0_ |= 0x00000002;
       }
    }
    /**
     * <pre>
     * task 状态
     * </pre>
     *
     * <code>repeated string status = 3;</code>
     * @return A list containing the status.
     */
    public com.google.protobuf.ProtocolStringList
        getStatusList() {
      return status_.getUnmodifiableView();
    }
    /**
     * <pre>
     * task 状态
     * </pre>
     *
     * <code>repeated string status = 3;</code>
     * @return The count of status.
     */
    public int getStatusCount() {
      return status_.size();
    }
    /**
     * <pre>
     * task 状态
     * </pre>
     *
     * <code>repeated string status = 3;</code>
     * @param index The index of the element to return.
     * @return The status at the given index.
     */
    public java.lang.String getStatus(int index) {
      return status_.get(index);
    }
    /**
     * <pre>
     * task 状态
     * </pre>
     *
     * <code>repeated string status = 3;</code>
     * @param index The index of the value to return.
     * @return The bytes of the status at the given index.
     */
    public com.google.protobuf.ByteString
        getStatusBytes(int index) {
      return status_.getByteString(index);
    }
    /**
     * <pre>
     * task 状态
     * </pre>
     *
     * <code>repeated string status = 3;</code>
     * @param index The index to set the value at.
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(
        int index, java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  ensureStatusIsMutable();
      status_.set(index, value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * task 状态
     * </pre>
     *
     * <code>repeated string status = 3;</code>
     * @param value The status to add.
     * @return This builder for chaining.
     */
    public Builder addStatus(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  ensureStatusIsMutable();
      status_.add(value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * task 状态
     * </pre>
     *
     * <code>repeated string status = 3;</code>
     * @param values The status to add.
     * @return This builder for chaining.
     */
    public Builder addAllStatus(
        java.lang.Iterable<java.lang.String> values) {
      ensureStatusIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, status_);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * task 状态
     * </pre>
     *
     * <code>repeated string status = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      status_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * task 状态
     * </pre>
     *
     * <code>repeated string status = 3;</code>
     * @param value The bytes of the status to add.
     * @return This builder for chaining.
     */
    public Builder addStatusBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      ensureStatusIsMutable();
      status_.add(value);
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringList processStatus_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    private void ensureProcessStatusIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        processStatus_ = new com.google.protobuf.LazyStringArrayList(processStatus_);
        bitField0_ |= 0x00000004;
       }
    }
    /**
     * <pre>
     * 批处理状态
     * </pre>
     *
     * <code>repeated string process_status = 4;</code>
     * @return A list containing the processStatus.
     */
    public com.google.protobuf.ProtocolStringList
        getProcessStatusList() {
      return processStatus_.getUnmodifiableView();
    }
    /**
     * <pre>
     * 批处理状态
     * </pre>
     *
     * <code>repeated string process_status = 4;</code>
     * @return The count of processStatus.
     */
    public int getProcessStatusCount() {
      return processStatus_.size();
    }
    /**
     * <pre>
     * 批处理状态
     * </pre>
     *
     * <code>repeated string process_status = 4;</code>
     * @param index The index of the element to return.
     * @return The processStatus at the given index.
     */
    public java.lang.String getProcessStatus(int index) {
      return processStatus_.get(index);
    }
    /**
     * <pre>
     * 批处理状态
     * </pre>
     *
     * <code>repeated string process_status = 4;</code>
     * @param index The index of the value to return.
     * @return The bytes of the processStatus at the given index.
     */
    public com.google.protobuf.ByteString
        getProcessStatusBytes(int index) {
      return processStatus_.getByteString(index);
    }
    /**
     * <pre>
     * 批处理状态
     * </pre>
     *
     * <code>repeated string process_status = 4;</code>
     * @param index The index to set the value at.
     * @param value The processStatus to set.
     * @return This builder for chaining.
     */
    public Builder setProcessStatus(
        int index, java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  ensureProcessStatusIsMutable();
      processStatus_.set(index, value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 批处理状态
     * </pre>
     *
     * <code>repeated string process_status = 4;</code>
     * @param value The processStatus to add.
     * @return This builder for chaining.
     */
    public Builder addProcessStatus(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  ensureProcessStatusIsMutable();
      processStatus_.add(value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 批处理状态
     * </pre>
     *
     * <code>repeated string process_status = 4;</code>
     * @param values The processStatus to add.
     * @return This builder for chaining.
     */
    public Builder addAllProcessStatus(
        java.lang.Iterable<java.lang.String> values) {
      ensureProcessStatusIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, processStatus_);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 批处理状态
     * </pre>
     *
     * <code>repeated string process_status = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearProcessStatus() {
      processStatus_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 批处理状态
     * </pre>
     *
     * <code>repeated string process_status = 4;</code>
     * @param value The bytes of the processStatus to add.
     * @return This builder for chaining.
     */
    public Builder addProcessStatusBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      ensureProcessStatusIsMutable();
      processStatus_.add(value);
      onChanged();
      return this;
    }

    private int limit_ ;
    /**
     * <pre>
     * 分页大小
     * </pre>
     *
     * <code>int32 limit = 5;</code>
     * @return The limit.
     */
    @java.lang.Override
    public int getLimit() {
      return limit_;
    }
    /**
     * <pre>
     * 分页大小
     * </pre>
     *
     * <code>int32 limit = 5;</code>
     * @param value The limit to set.
     * @return This builder for chaining.
     */
    public Builder setLimit(int value) {
      
      limit_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 分页大小
     * </pre>
     *
     * <code>int32 limit = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearLimit() {
      
      limit_ = 0;
      onChanged();
      return this;
    }

    private int offset_ ;
    /**
     * <pre>
     * 跳过行数
     * </pre>
     *
     * <code>int32 offset = 6;</code>
     * @return The offset.
     */
    @java.lang.Override
    public int getOffset() {
      return offset_;
    }
    /**
     * <pre>
     * 跳过行数
     * </pre>
     *
     * <code>int32 offset = 6;</code>
     * @param value The offset to set.
     * @return This builder for chaining.
     */
    public Builder setOffset(int value) {
      
      offset_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 跳过行数
     * </pre>
     *
     * <code>int32 offset = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearOffset() {
      
      offset_ = 0;
      onChanged();
      return this;
    }

    private boolean includeTotal_ ;
    /**
     * <pre>
     * 返回总条数
     * </pre>
     *
     * <code>bool include_total = 7;</code>
     * @return The includeTotal.
     */
    @java.lang.Override
    public boolean getIncludeTotal() {
      return includeTotal_;
    }
    /**
     * <pre>
     * 返回总条数
     * </pre>
     *
     * <code>bool include_total = 7;</code>
     * @param value The includeTotal to set.
     * @return This builder for chaining.
     */
    public Builder setIncludeTotal(boolean value) {
      
      includeTotal_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 返回总条数
     * </pre>
     *
     * <code>bool include_total = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearIncludeTotal() {
      
      includeTotal_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object search_ = "";
    /**
     * <pre>
     * 要模糊查询的字符串
     * </pre>
     *
     * <code>string search = 8;</code>
     * @return The search.
     */
    public java.lang.String getSearch() {
      java.lang.Object ref = search_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        search_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 要模糊查询的字符串
     * </pre>
     *
     * <code>string search = 8;</code>
     * @return The bytes for search.
     */
    public com.google.protobuf.ByteString
        getSearchBytes() {
      java.lang.Object ref = search_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        search_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 要模糊查询的字符串
     * </pre>
     *
     * <code>string search = 8;</code>
     * @param value The search to set.
     * @return This builder for chaining.
     */
    public Builder setSearch(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      search_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 要模糊查询的字符串
     * </pre>
     *
     * <code>string search = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearSearch() {
      
      search_ = getDefaultInstance().getSearch();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 要模糊查询的字符串
     * </pre>
     *
     * <code>string search = 8;</code>
     * @param value The bytes for search to set.
     * @return This builder for chaining.
     */
    public Builder setSearchBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      search_ = value;
      onChanged();
      return this;
    }

    private java.lang.Object searchFields_ = "";
    /**
     * <pre>
     * 要查询的字段, 多个逗号隔开;
     * </pre>
     *
     * <code>string search_fields = 9;</code>
     * @return The searchFields.
     */
    public java.lang.String getSearchFields() {
      java.lang.Object ref = searchFields_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        searchFields_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 要查询的字段, 多个逗号隔开;
     * </pre>
     *
     * <code>string search_fields = 9;</code>
     * @return The bytes for searchFields.
     */
    public com.google.protobuf.ByteString
        getSearchFieldsBytes() {
      java.lang.Object ref = searchFields_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        searchFields_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 要查询的字段, 多个逗号隔开;
     * </pre>
     *
     * <code>string search_fields = 9;</code>
     * @param value The searchFields to set.
     * @return This builder for chaining.
     */
    public Builder setSearchFields(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      searchFields_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 要查询的字段, 多个逗号隔开;
     * </pre>
     *
     * <code>string search_fields = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearSearchFields() {
      
      searchFields_ = getDefaultInstance().getSearchFields();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 要查询的字段, 多个逗号隔开;
     * </pre>
     *
     * <code>string search_fields = 9;</code>
     * @param value The bytes for searchFields to set.
     * @return This builder for chaining.
     */
    public Builder setSearchFieldsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      searchFields_ = value;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.LongList ids_ = emptyLongList();
    private void ensureIdsIsMutable() {
      if (!((bitField0_ & 0x00000008) != 0)) {
        ids_ = mutableCopy(ids_);
        bitField0_ |= 0x00000008;
       }
    }
    /**
     * <pre>
     * 按id列表查询
     * </pre>
     *
     * <code>repeated uint64 ids = 10;</code>
     * @return A list containing the ids.
     */
    public java.util.List<java.lang.Long>
        getIdsList() {
      return ((bitField0_ & 0x00000008) != 0) ?
               java.util.Collections.unmodifiableList(ids_) : ids_;
    }
    /**
     * <pre>
     * 按id列表查询
     * </pre>
     *
     * <code>repeated uint64 ids = 10;</code>
     * @return The count of ids.
     */
    public int getIdsCount() {
      return ids_.size();
    }
    /**
     * <pre>
     * 按id列表查询
     * </pre>
     *
     * <code>repeated uint64 ids = 10;</code>
     * @param index The index of the element to return.
     * @return The ids at the given index.
     */
    public long getIds(int index) {
      return ids_.getLong(index);
    }
    /**
     * <pre>
     * 按id列表查询
     * </pre>
     *
     * <code>repeated uint64 ids = 10;</code>
     * @param index The index to set the value at.
     * @param value The ids to set.
     * @return This builder for chaining.
     */
    public Builder setIds(
        int index, long value) {
      ensureIdsIsMutable();
      ids_.setLong(index, value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 按id列表查询
     * </pre>
     *
     * <code>repeated uint64 ids = 10;</code>
     * @param value The ids to add.
     * @return This builder for chaining.
     */
    public Builder addIds(long value) {
      ensureIdsIsMutable();
      ids_.addLong(value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 按id列表查询
     * </pre>
     *
     * <code>repeated uint64 ids = 10;</code>
     * @param values The ids to add.
     * @return This builder for chaining.
     */
    public Builder addAllIds(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureIdsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, ids_);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 按id列表查询
     * </pre>
     *
     * <code>repeated uint64 ids = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearIds() {
      ids_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:entity.ListEntityTaskRequest)
  }

  // @@protoc_insertion_point(class_scope:entity.ListEntityTaskRequest)
  private static final cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest();
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ListEntityTaskRequest>
      PARSER = new com.google.protobuf.AbstractParser<ListEntityTaskRequest>() {
    @java.lang.Override
    public ListEntityTaskRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new ListEntityTaskRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<ListEntityTaskRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ListEntityTaskRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.metadata.ListEntityTaskRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

