// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface ListEntityTaskRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.ListEntityTaskRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>repeated uint64 record_ids = 1;</code>
   * @return A list containing the recordIds.
   */
  java.util.List<java.lang.Long> getRecordIdsList();
  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>repeated uint64 record_ids = 1;</code>
   * @return The count of recordIds.
   */
  int getRecordIdsCount();
  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>repeated uint64 record_ids = 1;</code>
   * @param index The index of the element to return.
   * @return The recordIds at the given index.
   */
  long getRecordIds(int index);

  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 2;</code>
   * @return The schemaName.
   */
  java.lang.String getSchemaName();
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 2;</code>
   * @return The bytes for schemaName.
   */
  com.google.protobuf.ByteString
      getSchemaNameBytes();

  /**
   * <pre>
   * task 状态
   * </pre>
   *
   * <code>repeated string status = 3;</code>
   * @return A list containing the status.
   */
  java.util.List<java.lang.String>
      getStatusList();
  /**
   * <pre>
   * task 状态
   * </pre>
   *
   * <code>repeated string status = 3;</code>
   * @return The count of status.
   */
  int getStatusCount();
  /**
   * <pre>
   * task 状态
   * </pre>
   *
   * <code>repeated string status = 3;</code>
   * @param index The index of the element to return.
   * @return The status at the given index.
   */
  java.lang.String getStatus(int index);
  /**
   * <pre>
   * task 状态
   * </pre>
   *
   * <code>repeated string status = 3;</code>
   * @param index The index of the value to return.
   * @return The bytes of the status at the given index.
   */
  com.google.protobuf.ByteString
      getStatusBytes(int index);

  /**
   * <pre>
   * 批处理状态
   * </pre>
   *
   * <code>repeated string process_status = 4;</code>
   * @return A list containing the processStatus.
   */
  java.util.List<java.lang.String>
      getProcessStatusList();
  /**
   * <pre>
   * 批处理状态
   * </pre>
   *
   * <code>repeated string process_status = 4;</code>
   * @return The count of processStatus.
   */
  int getProcessStatusCount();
  /**
   * <pre>
   * 批处理状态
   * </pre>
   *
   * <code>repeated string process_status = 4;</code>
   * @param index The index of the element to return.
   * @return The processStatus at the given index.
   */
  java.lang.String getProcessStatus(int index);
  /**
   * <pre>
   * 批处理状态
   * </pre>
   *
   * <code>repeated string process_status = 4;</code>
   * @param index The index of the value to return.
   * @return The bytes of the processStatus at the given index.
   */
  com.google.protobuf.ByteString
      getProcessStatusBytes(int index);

  /**
   * <pre>
   * 分页大小
   * </pre>
   *
   * <code>int32 limit = 5;</code>
   * @return The limit.
   */
  int getLimit();

  /**
   * <pre>
   * 跳过行数
   * </pre>
   *
   * <code>int32 offset = 6;</code>
   * @return The offset.
   */
  int getOffset();

  /**
   * <pre>
   * 返回总条数
   * </pre>
   *
   * <code>bool include_total = 7;</code>
   * @return The includeTotal.
   */
  boolean getIncludeTotal();

  /**
   * <pre>
   * 要模糊查询的字符串
   * </pre>
   *
   * <code>string search = 8;</code>
   * @return The search.
   */
  java.lang.String getSearch();
  /**
   * <pre>
   * 要模糊查询的字符串
   * </pre>
   *
   * <code>string search = 8;</code>
   * @return The bytes for search.
   */
  com.google.protobuf.ByteString
      getSearchBytes();

  /**
   * <pre>
   * 要查询的字段, 多个逗号隔开;
   * </pre>
   *
   * <code>string search_fields = 9;</code>
   * @return The searchFields.
   */
  java.lang.String getSearchFields();
  /**
   * <pre>
   * 要查询的字段, 多个逗号隔开;
   * </pre>
   *
   * <code>string search_fields = 9;</code>
   * @return The bytes for searchFields.
   */
  com.google.protobuf.ByteString
      getSearchFieldsBytes();

  /**
   * <pre>
   * 按id列表查询
   * </pre>
   *
   * <code>repeated uint64 ids = 10;</code>
   * @return A list containing the ids.
   */
  java.util.List<java.lang.Long> getIdsList();
  /**
   * <pre>
   * 按id列表查询
   * </pre>
   *
   * <code>repeated uint64 ids = 10;</code>
   * @return The count of ids.
   */
  int getIdsCount();
  /**
   * <pre>
   * 按id列表查询
   * </pre>
   *
   * <code>repeated uint64 ids = 10;</code>
   * @param index The index of the element to return.
   * @return The ids at the given index.
   */
  long getIds(int index);
}
