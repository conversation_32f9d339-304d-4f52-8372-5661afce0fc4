// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface ListEntityTaskResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.ListEntityTaskResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .entity.EntityTask rows = 1;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.integration.metadata.EntityTask> 
      getRowsList();
  /**
   * <code>repeated .entity.EntityTask rows = 1;</code>
   */
  cn.hexcloud.pbis.common.service.integration.metadata.EntityTask getRows(int index);
  /**
   * <code>repeated .entity.EntityTask rows = 1;</code>
   */
  int getRowsCount();
  /**
   * <code>repeated .entity.EntityTask rows = 1;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.integration.metadata.EntityTaskOrBuilder> 
      getRowsOrBuilderList();
  /**
   * <code>repeated .entity.EntityTask rows = 1;</code>
   */
  cn.hexcloud.pbis.common.service.integration.metadata.EntityTaskOrBuilder getRowsOrBuilder(
      int index);

  /**
   * <code>int32 total = 2;</code>
   * @return The total.
   */
  int getTotal();
}
