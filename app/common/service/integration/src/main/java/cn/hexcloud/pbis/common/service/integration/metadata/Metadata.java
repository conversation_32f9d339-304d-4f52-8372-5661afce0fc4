// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public final class Metadata {
  private Metadata() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_AddEntityRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_AddEntityRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_SyncEntityRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_SyncEntityRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_UpdateEntityRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_UpdateEntityRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_GetEntityByIdRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_GetEntityByIdRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_ListEntityRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_ListEntityRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_UpdateEntityStateRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_UpdateEntityStateRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_ProcessEntityPendingChangesRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_ProcessEntityPendingChangesRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_ListEntityResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_ListEntityResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_Entity_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_Entity_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_DefaultResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_DefaultResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_CreateEntityTaskFromPendingChangesRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_CreateEntityTaskFromPendingChangesRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_GetEntityTaskByIdRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_GetEntityTaskByIdRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_ListEntityTaskRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_ListEntityTaskRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_UpdateEntityTaskRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_UpdateEntityTaskRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_DeleteEntityTaskRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_DeleteEntityTaskRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_UpdateEntityTaskStatusRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_UpdateEntityTaskStatusRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_RunEntityTaskRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_RunEntityTaskRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_ListEntityTaskResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_ListEntityTaskResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_EntityTask_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_EntityTask_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_GetChildrenEntityIdsRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_GetChildrenEntityIdsRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_GetChildrenEntityIdsResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_GetChildrenEntityIdsResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_GetParentEntityIdsRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_GetParentEntityIdsRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_GetParentEntityIdsResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_GetParentEntityIdsResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_RefreshEntityMVRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_RefreshEntityMVRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_RefreshEntityMVResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_RefreshEntityMVResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_UpdateEntityBatchRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_UpdateEntityBatchRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_UpdateEntityBatchResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_UpdateEntityBatchResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_AddEntityBatchRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_AddEntityBatchRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_AddEntityBatchResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_AddEntityBatchResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_entity_SqlQueryRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_entity_SqlQueryRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016Metadata.proto\022\006entity\032\034google/api/ann" +
      "otations.proto\032\034google/protobuf/struct.p" +
      "roto\"~\n\020AddEntityRequest\022\023\n\013schema_name\030" +
      "\001 \001(\t\022\023\n\013auto_enable\030\002 \001(\010\022\'\n\006fields\030\003 \001" +
      "(\0132\027.google.protobuf.Struct\022\013\n\003lan\030\004 \001(\t" +
      "\022\n\n\002id\030\005 \001(\t\"\223\001\n\021SyncEntityRequest\022\n\n\002id" +
      "\030\001 \001(\004\022\023\n\013schema_name\030\002 \001(\t\022\022\n\nauto_appl" +
      "y\030\003 \001(\010\022\023\n\013auto_enable\030\004 \001(\010\022\'\n\006fields\030\005" +
      " \001(\0132\027.google.protobuf.Struct\022\013\n\003lan\030\006 \001" +
      "(\t\"\252\001\n\023UpdateEntityRequest\022\n\n\002id\030\001 \001(\004\022\023" +
      "\n\013schema_name\030\002 \001(\t\022\022\n\nauto_apply\030\003 \001(\010\022" +
      "\'\n\006fields\030\004 \001(\0132\027.google.protobuf.Struct" +
      "\022\013\n\003lan\030\005 \001(\t\022\021\n\tis_merged\030\006 \001(\010\022\025\n\rappl" +
      "y_current\030\007 \001(\010\"\217\002\n\024GetEntityByIdRequest" +
      "\022\n\n\002id\030\001 \001(\004\022\023\n\013schema_name\030\002 \001(\t\022\025\n\rinc" +
      "lude_state\030\003 \001(\010\022\014\n\004code\030\004 \001(\t\022\020\n\010relati" +
      "on\030\005 \001(\t\022\025\n\rreturn_fields\030\006 \001(\t\022\037\n\027inclu" +
      "de_pending_changes\030\007 \001(\010\022\036\n\026include_pend" +
      "ing_record\030\010 \001(\010\022\027\n\017include_parents\030\t \001(" +
      "\010\022\013\n\003lan\030\n \001(\t\022!\n\031include_all_localizati" +
      "ons\030\013 \001(\010\"\233\004\n\021ListEntityRequest\022\023\n\013schem" +
      "a_name\030\001 \001(\t\022\r\n\005state\030\002 \001(\t\022\025\n\rinclude_s" +
      "tate\030\003 \001(\010\022\014\n\004code\030\004 \001(\t\022\020\n\010relation\030\005 \001" +
      "(\t\022\025\n\rreturn_fields\030\006 \001(\t\022\037\n\027include_pen" +
      "ding_changes\030\007 \001(\010\022\036\n\026include_pending_re" +
      "cord\030\010 \001(\010\022\027\n\017include_parents\030\t \001(\010\022\r\n\005l" +
      "imit\030\n \001(\005\022\016\n\006offset\030\013 \001(\005\022\014\n\004sort\030\014 \001(\t" +
      "\022\r\n\005order\030\r \001(\t\022\025\n\rinclude_total\030\016 \001(\010\022\016" +
      "\n\006search\030\017 \001(\t\022\025\n\rsearch_fields\030\020 \001(\t\022\013\n" +
      "\003ids\030\021 \003(\004\022(\n\007filters\030\022 \001(\0132\027.google.pro" +
      "tobuf.Struct\0221\n\020relation_filters\030\023 \001(\0132\027" +
      ".google.protobuf.Struct\022\013\n\003lan\030\024 \001(\t\022!\n\031" +
      "include_all_localizations\030\025 \001(\010\022\022\n\nis_re" +
      "quest\030\026 \001(\010\022\022\n\nget_common\030\027 \001(\010\"W\n\030Updat" +
      "eEntityStateRequest\022\n\n\002id\030\001 \001(\004\022\023\n\013schem" +
      "a_name\030\002 \001(\t\022\r\n\005state\030\003 \001(\t\022\013\n\003lan\030\004 \001(\t" +
      "\"U\n\"ProcessEntityPendingChangesRequest\022\n" +
      "\n\002id\030\001 \001(\004\022\023\n\013schema_name\030\002 \001(\t\022\016\n\006actio" +
      "n\030\003 \001(\t\"A\n\022ListEntityResponse\022\034\n\004rows\030\001 " +
      "\003(\0132\016.entity.Entity\022\r\n\005total\030\002 \001(\005\"\242\003\n\006E" +
      "ntity\022\n\n\002id\030\001 \001(\004\022\022\n\npartner_id\030\002 \001(\004\022\020\n" +
      "\010scope_id\030\003 \001(\004\022\021\n\tparent_id\030\004 \001(\004\022\021\n\tsc" +
      "hema_id\030\005 \001(\004\022\023\n\013schema_name\030\006 \001(\t\022\r\n\005st" +
      "ate\030\007 \001(\t\022\'\n\006fields\030\010 \001(\0132\027.google.proto" +
      "buf.Struct\022/\n\016fields_pending\030\t \001(\0132\027.goo" +
      "gle.protobuf.Struct\022/\n\016record_pending\030\n " +
      "\001(\0132\027.google.protobuf.Struct\022\017\n\007pending\030" +
      "\013 \001(\010\022\017\n\007created\030\014 \001(\t\022\017\n\007updated\030\r \001(\t\022" +
      "\022\n\ncreated_by\030\016 \001(\t\022\022\n\nupdated_by\030\017 \001(\t\022" +
      "\026\n\016process_status\030\020 \001(\t\022\036\n\006parent\030\021 \001(\0132" +
      "\016.entity.Entity\"!\n\017DefaultResponse\022\016\n\006re" +
      "sult\030\001 \001(\010\"\231\001\n)CreateEntityTaskFromPendi" +
      "ngChangesRequest\022\021\n\trecord_id\030\001 \001(\004\022\023\n\013s" +
      "chema_name\030\002 \001(\t\022\014\n\004name\030\003 \001(\t\022\021\n\timmedi" +
      "ate\030\004 \001(\010\022\r\n\005start\030\005 \001(\t\022\024\n\014auto_approve" +
      "\030\006 \001(\010\";\n\030GetEntityTaskByIdRequest\022\n\n\002id" +
      "\030\001 \001(\004\022\023\n\013schema_name\030\002 \001(\t\"\322\001\n\025ListEnti" +
      "tyTaskRequest\022\022\n\nrecord_ids\030\001 \003(\004\022\023\n\013sch" +
      "ema_name\030\002 \001(\t\022\016\n\006status\030\003 \003(\t\022\026\n\016proces" +
      "s_status\030\004 \003(\t\022\r\n\005limit\030\005 \001(\005\022\016\n\006offset\030" +
      "\006 \001(\005\022\025\n\rinclude_total\030\007 \001(\010\022\016\n\006search\030\010" +
      " \001(\t\022\025\n\rsearch_fields\030\t \001(\t\022\013\n\003ids\030\n \003(\004" +
      "\"c\n\027UpdateEntityTaskRequest\022\n\n\002id\030\001 \001(\004\022" +
      "\023\n\013schema_name\030\002 \001(\t\022\'\n\006fields\030\003 \001(\0132\027.g" +
      "oogle.protobuf.Struct\":\n\027DeleteEntityTas" +
      "kRequest\022\n\n\002id\030\001 \001(\004\022\023\n\013schema_name\030\002 \001(" +
      "\t\"P\n\035UpdateEntityTaskStatusRequest\022\n\n\002id" +
      "\030\001 \001(\004\022\023\n\013schema_name\030\002 \001(\t\022\016\n\006status\030\003 " +
      "\001(\t\"7\n\024RunEntityTaskRequest\022\n\n\002id\030\001 \001(\004\022" +
      "\023\n\013schema_name\030\002 \001(\t\"I\n\026ListEntityTaskRe" +
      "sponse\022 \n\004rows\030\001 \003(\0132\022.entity.EntityTask" +
      "\022\r\n\005total\030\002 \001(\005\"\266\003\n\nEntityTask\022\n\n\002id\030\001 \001" +
      "(\004\022\022\n\npartner_id\030\002 \001(\004\022\020\n\010scope_id\030\003 \001(\004" +
      "\022\016\n\006job_id\030\004 \001(\004\022\023\n\013schema_type\030\005 \001(\t\022\014\n" +
      "\004name\030\006 \001(\t\022\021\n\trecord_id\030\007 \001(\004\022(\n\007conten" +
      "t\030\010 \001(\0132\027.google.protobuf.Struct\022-\n\014cont" +
      "ent_from\030\025 \001(\0132\027.google.protobuf.Struct\022" +
      "\016\n\006status\030\t \001(\t\022\026\n\016process_status\030\n \001(\t\022" +
      "\016\n\006action\030\013 \001(\t\022\021\n\timmediate\030\014 \001(\010\022\r\n\005st" +
      "art\030\r \001(\t\022\022\n\nlast_start\030\016 \001(\t\022\020\n\010last_en" +
      "d\030\017 \001(\t\022\r\n\005retry\030\020 \001(\005\022\017\n\007created\030\021 \001(\t\022" +
      "\017\n\007updated\030\022 \001(\t\022\022\n\ncreated_by\030\023 \001(\004\022\022\n\n" +
      "updated_by\030\024 \001(\004\"?\n\033GetChildrenEntityIds" +
      "Request\022\023\n\013schema_name\030\001 \001(\t\022\013\n\003ids\030\002 \003(" +
      "\004\"4\n\034GetChildrenEntityIdsResponse\022\024\n\014chi" +
      "ldren_ids\030\001 \003(\004\"=\n\031GetParentEntityIdsReq" +
      "uest\022\023\n\013schema_name\030\001 \001(\t\022\013\n\003ids\030\002 \003(\004\"0" +
      "\n\032GetParentEntityIdsResponse\022\022\n\nparent_i" +
      "ds\030\001 \003(\004\"-\n\026RefreshEntityMVRequest\022\023\n\013sc" +
      "hema_name\030\001 \001(\t\")\n\027RefreshEntityMVRespon" +
      "se\022\016\n\006result\030\001 \001(\t\"H\n\030UpdateEntityBatchR" +
      "equest\022,\n\007entitys\030\001 \003(\0132\033.entity.UpdateE" +
      "ntityRequest\"M\n\031UpdateEntityBatchRespons" +
      "e\022\017\n\007message\030\001 \001(\t\022\014\n\004code\030\002 \001(\t\022\021\n\terro" +
      "r_ids\030\003 \003(\004\"B\n\025AddEntityBatchRequest\022)\n\007" +
      "entitys\030\001 \003(\0132\030.entity.AddEntityRequest\"" +
      "J\n\026AddEntityBatchResponse\022\017\n\007message\030\001 \001" +
      "(\t\022\014\n\004code\030\002 \001(\t\022\021\n\terror_ids\030\003 \003(\004\"\036\n\017S" +
      "qlQueryRequest\022\013\n\003sql\030\001 \001(\t2\255\024\n\rEntitySe" +
      "rvice\022k\n\tAddEntity\022\030.entity.AddEntityReq" +
      "uest\032\016.entity.Entity\"4\202\323\344\223\002.\")/api/v2/me" +
      "tadata/entity/{schema_name}/add:\001*\022t\n\014Up" +
      "dateEntity\022\033.entity.UpdateEntityRequest\032" +
      "\016.entity.Entity\"7\202\323\344\223\0021\",/api/v2/metadat" +
      "a/entity/{schema_name}/update:\001*\022{\n\020Sync" +
      "UpdateEntity\022\031.entity.SyncEntityRequest\032" +
      "\016.entity.Entity\"<\202\323\344\223\0026\"1/api/v2/metadat" +
      "a/entity/{schema_name}/sync/update:\001*\022w\n" +
      "\rGetEntityById\022\034.entity.GetEntityByIdReq" +
      "uest\032\016.entity.Entity\"8\202\323\344\223\0022\0220/api/v2/me" +
      "tadata/entity/{schema_name}/by/id/{id}\022{" +
      "\n\nListEntity\022\031.entity.ListEntityRequest\032" +
      "\032.entity.ListEntityResponse\"6\202\323\344\223\0020\"+/ap" +
      "i/v2/metadata/entity/{schema_name}/query" +
      ":\001*\022\204\001\n\021UpdateEntityState\022 .entity.Updat" +
      "eEntityStateRequest\032\016.entity.Entity\"=\202\323\344" +
      "\223\0027\"2/api/v2/metadata/entity/{schema_nam" +
      "e}/state/update:\001*\022\234\001\n\033ProcessEntityPend" +
      "ingChanges\022*.entity.ProcessEntityPending" +
      "ChangesRequest\032\016.entity.Entity\"A\202\323\344\223\002;\"6" +
      "/api/v2/metadata/entity/{schema_name}/ch" +
      "anges/{action}:\001*\022\255\001\n\"CreateEntityTaskFr" +
      "omPendingChanges\0221.entity.CreateEntityTa" +
      "skFromPendingChangesRequest\032\022.entity.Ent" +
      "ityTask\"@\202\323\344\223\002:\"5/api/v2/metadata/entity" +
      "/{schema_name}/changes/to/task:\001*\022\205\001\n\020Up" +
      "dateEntityTask\022\037.entity.UpdateEntityTask" +
      "Request\032\022.entity.EntityTask\"<\202\323\344\223\0026\"1/ap" +
      "i/v2/metadata/entity/{schema_name}/task/" +
      "update:\001*\022\230\001\n\026UpdateEntityTaskStatus\022%.e" +
      "ntity.UpdateEntityTaskStatusRequest\032\022.en" +
      "tity.EntityTask\"C\202\323\344\223\002=\"8/api/v2/metadat" +
      "a/entity/{schema_name}/task/update/statu" +
      "s:\001*\022\206\001\n\020DeleteEntityTask\022\037.entity.Delet" +
      "eEntityTaskRequest\032\022.entity.EntityTask\"=" +
      "\202\323\344\223\0027*5/api/v2/metadata/entity/{schema_" +
      "name}/task/by/id/{id}\022\210\001\n\021GetEntityTaskB" +
      "yId\022 .entity.GetEntityTaskByIdRequest\032\022." +
      "entity.EntityTask\"=\202\323\344\223\0027\0225/api/v2/metad" +
      "ata/entity/{schema_name}/task/by/id/{id}" +
      "\022\214\001\n\016ListEntityTask\022\035.entity.ListEntityT" +
      "askRequest\032\036.entity.ListEntityTaskRespon" +
      "se\";\202\323\344\223\0025\"0/api/v2/metadata/entity/{sch" +
      "ema_name}/task/query:\001*\022\237\001\n\024GetChildrenE" +
      "ntityIds\022#.entity.GetChildrenEntityIdsRe" +
      "quest\032$.entity.GetChildrenEntityIdsRespo" +
      "nse\"<\202\323\344\223\0026\"1/api/v2/metadata/entity/{sc" +
      "hema_name}/childrenids:\001*\022\227\001\n\022GetParentE" +
      "ntityIds\022!.entity.GetParentEntityIdsRequ" +
      "est\032\".entity.GetParentEntityIdsResponse\"" +
      ":\202\323\344\223\0024\"//api/v2/metadata/entity/{schema" +
      "_name}/parentids:\001*\022t\n\017RefreshEntityMV\022\036" +
      ".entity.RefreshEntityMVRequest\032\037.entity." +
      "RefreshEntityMVResponse\" \202\323\344\223\002\032\"\030/api/v2" +
      "/metadata/refresh\022\211\001\n\021UpdateEntityBatch\022" +
      " .entity.UpdateEntityBatchRequest\032!.enti" +
      "ty.UpdateEntityBatchResponse\"/\202\323\344\223\002)\"$/a" +
      "pi/v2/metadata/entity/update/batch:\001*\022}\n" +
      "\016AddEntityBatch\022\035.entity.AddEntityBatchR" +
      "equest\032\036.entity.AddEntityBatchResponse\"," +
      "\202\323\344\223\002&\"!/api/v2/metadata/entity/add/batc" +
      "h:\001*\022m\n\010SqlQuery\022\027.entity.SqlQueryReques" +
      "t\032\032.entity.ListEntityResponse\",\202\323\344\223\002&\"!/" +
      "api/v2/metadata/entity/query/sql:\001*B8\n4c" +
      "n.hexcloud.pbis.common.service.integrati" +
      "on.metadataP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.api.AnnotationsProto.getDescriptor(),
          com.google.protobuf.StructProto.getDescriptor(),
        });
    internal_static_entity_AddEntityRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_entity_AddEntityRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_AddEntityRequest_descriptor,
        new java.lang.String[] { "SchemaName", "AutoEnable", "Fields", "Lan", "Id", });
    internal_static_entity_SyncEntityRequest_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_entity_SyncEntityRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_SyncEntityRequest_descriptor,
        new java.lang.String[] { "Id", "SchemaName", "AutoApply", "AutoEnable", "Fields", "Lan", });
    internal_static_entity_UpdateEntityRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_entity_UpdateEntityRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_UpdateEntityRequest_descriptor,
        new java.lang.String[] { "Id", "SchemaName", "AutoApply", "Fields", "Lan", "IsMerged", "ApplyCurrent", });
    internal_static_entity_GetEntityByIdRequest_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_entity_GetEntityByIdRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_GetEntityByIdRequest_descriptor,
        new java.lang.String[] { "Id", "SchemaName", "IncludeState", "Code", "Relation", "ReturnFields", "IncludePendingChanges", "IncludePendingRecord", "IncludeParents", "Lan", "IncludeAllLocalizations", });
    internal_static_entity_ListEntityRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_entity_ListEntityRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_ListEntityRequest_descriptor,
        new java.lang.String[] { "SchemaName", "State", "IncludeState", "Code", "Relation", "ReturnFields", "IncludePendingChanges", "IncludePendingRecord", "IncludeParents", "Limit", "Offset", "Sort", "Order", "IncludeTotal", "Search", "SearchFields", "Ids", "Filters", "RelationFilters", "Lan", "IncludeAllLocalizations", "IsRequest", "GetCommon", });
    internal_static_entity_UpdateEntityStateRequest_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_entity_UpdateEntityStateRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_UpdateEntityStateRequest_descriptor,
        new java.lang.String[] { "Id", "SchemaName", "State", "Lan", });
    internal_static_entity_ProcessEntityPendingChangesRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_entity_ProcessEntityPendingChangesRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_ProcessEntityPendingChangesRequest_descriptor,
        new java.lang.String[] { "Id", "SchemaName", "Action", });
    internal_static_entity_ListEntityResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_entity_ListEntityResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_ListEntityResponse_descriptor,
        new java.lang.String[] { "Rows", "Total", });
    internal_static_entity_Entity_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_entity_Entity_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_Entity_descriptor,
        new java.lang.String[] { "Id", "PartnerId", "ScopeId", "ParentId", "SchemaId", "SchemaName", "State", "Fields", "FieldsPending", "RecordPending", "Pending", "Created", "Updated", "CreatedBy", "UpdatedBy", "ProcessStatus", "Parent", });
    internal_static_entity_DefaultResponse_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_entity_DefaultResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_DefaultResponse_descriptor,
        new java.lang.String[] { "Result", });
    internal_static_entity_CreateEntityTaskFromPendingChangesRequest_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_entity_CreateEntityTaskFromPendingChangesRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_CreateEntityTaskFromPendingChangesRequest_descriptor,
        new java.lang.String[] { "RecordId", "SchemaName", "Name", "Immediate", "Start", "AutoApprove", });
    internal_static_entity_GetEntityTaskByIdRequest_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_entity_GetEntityTaskByIdRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_GetEntityTaskByIdRequest_descriptor,
        new java.lang.String[] { "Id", "SchemaName", });
    internal_static_entity_ListEntityTaskRequest_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_entity_ListEntityTaskRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_ListEntityTaskRequest_descriptor,
        new java.lang.String[] { "RecordIds", "SchemaName", "Status", "ProcessStatus", "Limit", "Offset", "IncludeTotal", "Search", "SearchFields", "Ids", });
    internal_static_entity_UpdateEntityTaskRequest_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_entity_UpdateEntityTaskRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_UpdateEntityTaskRequest_descriptor,
        new java.lang.String[] { "Id", "SchemaName", "Fields", });
    internal_static_entity_DeleteEntityTaskRequest_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_entity_DeleteEntityTaskRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_DeleteEntityTaskRequest_descriptor,
        new java.lang.String[] { "Id", "SchemaName", });
    internal_static_entity_UpdateEntityTaskStatusRequest_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_entity_UpdateEntityTaskStatusRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_UpdateEntityTaskStatusRequest_descriptor,
        new java.lang.String[] { "Id", "SchemaName", "Status", });
    internal_static_entity_RunEntityTaskRequest_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_entity_RunEntityTaskRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_RunEntityTaskRequest_descriptor,
        new java.lang.String[] { "Id", "SchemaName", });
    internal_static_entity_ListEntityTaskResponse_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_entity_ListEntityTaskResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_ListEntityTaskResponse_descriptor,
        new java.lang.String[] { "Rows", "Total", });
    internal_static_entity_EntityTask_descriptor =
      getDescriptor().getMessageTypes().get(18);
    internal_static_entity_EntityTask_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_EntityTask_descriptor,
        new java.lang.String[] { "Id", "PartnerId", "ScopeId", "JobId", "SchemaType", "Name", "RecordId", "Content", "ContentFrom", "Status", "ProcessStatus", "Action", "Immediate", "Start", "LastStart", "LastEnd", "Retry", "Created", "Updated", "CreatedBy", "UpdatedBy", });
    internal_static_entity_GetChildrenEntityIdsRequest_descriptor =
      getDescriptor().getMessageTypes().get(19);
    internal_static_entity_GetChildrenEntityIdsRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_GetChildrenEntityIdsRequest_descriptor,
        new java.lang.String[] { "SchemaName", "Ids", });
    internal_static_entity_GetChildrenEntityIdsResponse_descriptor =
      getDescriptor().getMessageTypes().get(20);
    internal_static_entity_GetChildrenEntityIdsResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_GetChildrenEntityIdsResponse_descriptor,
        new java.lang.String[] { "ChildrenIds", });
    internal_static_entity_GetParentEntityIdsRequest_descriptor =
      getDescriptor().getMessageTypes().get(21);
    internal_static_entity_GetParentEntityIdsRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_GetParentEntityIdsRequest_descriptor,
        new java.lang.String[] { "SchemaName", "Ids", });
    internal_static_entity_GetParentEntityIdsResponse_descriptor =
      getDescriptor().getMessageTypes().get(22);
    internal_static_entity_GetParentEntityIdsResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_GetParentEntityIdsResponse_descriptor,
        new java.lang.String[] { "ParentIds", });
    internal_static_entity_RefreshEntityMVRequest_descriptor =
      getDescriptor().getMessageTypes().get(23);
    internal_static_entity_RefreshEntityMVRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_RefreshEntityMVRequest_descriptor,
        new java.lang.String[] { "SchemaName", });
    internal_static_entity_RefreshEntityMVResponse_descriptor =
      getDescriptor().getMessageTypes().get(24);
    internal_static_entity_RefreshEntityMVResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_RefreshEntityMVResponse_descriptor,
        new java.lang.String[] { "Result", });
    internal_static_entity_UpdateEntityBatchRequest_descriptor =
      getDescriptor().getMessageTypes().get(25);
    internal_static_entity_UpdateEntityBatchRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_UpdateEntityBatchRequest_descriptor,
        new java.lang.String[] { "Entitys", });
    internal_static_entity_UpdateEntityBatchResponse_descriptor =
      getDescriptor().getMessageTypes().get(26);
    internal_static_entity_UpdateEntityBatchResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_UpdateEntityBatchResponse_descriptor,
        new java.lang.String[] { "Message", "Code", "ErrorIds", });
    internal_static_entity_AddEntityBatchRequest_descriptor =
      getDescriptor().getMessageTypes().get(27);
    internal_static_entity_AddEntityBatchRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_AddEntityBatchRequest_descriptor,
        new java.lang.String[] { "Entitys", });
    internal_static_entity_AddEntityBatchResponse_descriptor =
      getDescriptor().getMessageTypes().get(28);
    internal_static_entity_AddEntityBatchResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_AddEntityBatchResponse_descriptor,
        new java.lang.String[] { "Message", "Code", "ErrorIds", });
    internal_static_entity_SqlQueryRequest_descriptor =
      getDescriptor().getMessageTypes().get(29);
    internal_static_entity_SqlQueryRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_entity_SqlQueryRequest_descriptor,
        new java.lang.String[] { "Sql", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(com.google.api.AnnotationsProto.http);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    com.google.api.AnnotationsProto.getDescriptor();
    com.google.protobuf.StructProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
