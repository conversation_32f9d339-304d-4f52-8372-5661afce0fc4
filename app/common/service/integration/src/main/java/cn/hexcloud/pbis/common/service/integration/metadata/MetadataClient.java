package cn.hexcloud.pbis.common.service.integration.metadata;

import cn.hexcloud.pbis.common.util.config.PBISApplicationConfig;
import io.grpc.Channel;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import net.devh.boot.grpc.client.inject.GrpcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName MetadataClient.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/19 12:34:12
 */
@Service
public class MetadataClient {

  @GrpcClient("MetadataService")
  private Channel serviceChannel;

  @Autowired
  private PBISApplicationConfig pbisApplicationConfig;

  private EntityServiceGrpc.EntityServiceBlockingStub serviceBlockingStub;

  @PostConstruct
  public void init() {
    this.serviceBlockingStub = EntityServiceGrpc.newBlockingStub(serviceChannel);
  }

  public Entity getMetadata(GetEntityByIdRequest request) {
    return serviceBlockingStub
        .withDeadlineAfter(pbisApplicationConfig.getGrpcClientTimeout(), TimeUnit.MILLISECONDS)
        .getEntityById(request);
  }

  public List<Entity> listMetadata(ListEntityRequest request) {
    ListEntityResponse response = serviceBlockingStub
        .withDeadlineAfter(pbisApplicationConfig.getGrpcClientTimeout(), TimeUnit.MILLISECONDS)
        .listEntity(request);
    return response.getRowsList();
  }

}
