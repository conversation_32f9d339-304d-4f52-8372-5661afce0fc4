// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface RefreshEntityMVRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.RefreshEntityMVRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string schema_name = 1;</code>
   * @return The schemaName.
   */
  java.lang.String getSchemaName();
  /**
   * <code>string schema_name = 1;</code>
   * @return The bytes for schemaName.
   */
  com.google.protobuf.ByteString
      getSchemaNameBytes();
}
