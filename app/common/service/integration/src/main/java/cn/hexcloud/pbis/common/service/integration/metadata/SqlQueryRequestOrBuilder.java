// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface SqlQueryRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.SqlQueryRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string sql = 1;</code>
   * @return The sql.
   */
  java.lang.String getSql();
  /**
   * <code>string sql = 1;</code>
   * @return The bytes for sql.
   */
  com.google.protobuf.ByteString
      getSqlBytes();
}
