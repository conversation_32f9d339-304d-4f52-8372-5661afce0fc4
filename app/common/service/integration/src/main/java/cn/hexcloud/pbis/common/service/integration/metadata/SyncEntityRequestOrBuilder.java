// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface SyncEntityRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.SyncEntityRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>uint64 id = 1;</code>
   * @return The id.
   */
  long getId();

  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 2;</code>
   * @return The schemaName.
   */
  java.lang.String getSchemaName();
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 2;</code>
   * @return The bytes for schemaName.
   */
  com.google.protobuf.ByteString
      getSchemaNameBytes();

  /**
   * <pre>
   * 对数据状态是ENABLED的数据自动apply change
   * </pre>
   *
   * <code>bool auto_apply = 3;</code>
   * @return The autoApply.
   */
  boolean getAutoApply();

  /**
   * <pre>
   * 创建之后enable数据
   * </pre>
   *
   * <code>bool auto_enable = 4;</code>
   * @return The autoEnable.
   */
  boolean getAutoEnable();

  /**
   * <pre>
   * Entity字段json
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 5;</code>
   * @return Whether the fields field is set.
   */
  boolean hasFields();
  /**
   * <pre>
   * Entity字段json
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 5;</code>
   * @return The fields.
   */
  com.google.protobuf.Struct getFields();
  /**
   * <pre>
   * Entity字段json
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 5;</code>
   */
  com.google.protobuf.StructOrBuilder getFieldsOrBuilder();

  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 6;</code>
   * @return The lan.
   */
  java.lang.String getLan();
  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 6;</code>
   * @return The bytes for lan.
   */
  com.google.protobuf.ByteString
      getLanBytes();
}
