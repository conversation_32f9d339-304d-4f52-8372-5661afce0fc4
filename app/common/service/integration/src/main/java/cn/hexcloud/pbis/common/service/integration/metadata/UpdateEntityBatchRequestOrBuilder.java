// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface UpdateEntityBatchRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.UpdateEntityBatchRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>repeated .entity.UpdateEntityRequest entitys = 1;</code>
   */
  java.util.List<cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest> 
      getEntitysList();
  /**
   * <code>repeated .entity.UpdateEntityRequest entitys = 1;</code>
   */
  cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest getEntitys(int index);
  /**
   * <code>repeated .entity.UpdateEntityRequest entitys = 1;</code>
   */
  int getEntitysCount();
  /**
   * <code>repeated .entity.UpdateEntityRequest entitys = 1;</code>
   */
  java.util.List<? extends cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequestOrBuilder> 
      getEntitysOrBuilderList();
  /**
   * <code>repeated .entity.UpdateEntityRequest entitys = 1;</code>
   */
  cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequestOrBuilder getEntitysOrBuilder(
      int index);
}
