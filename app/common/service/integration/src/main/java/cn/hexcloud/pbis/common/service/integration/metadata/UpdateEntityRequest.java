// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

/**
 * Protobuf type {@code entity.UpdateEntityRequest}
 */
public final class UpdateEntityRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:entity.UpdateEntityRequest)
    UpdateEntityRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use UpdateEntityRequest.newBuilder() to construct.
  private UpdateEntityRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private UpdateEntityRequest() {
    schemaName_ = "";
    lan_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new UpdateEntityRequest();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private UpdateEntityRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            id_ = input.readUInt64();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            schemaName_ = s;
            break;
          }
          case 24: {

            autoApply_ = input.readBool();
            break;
          }
          case 34: {
            com.google.protobuf.Struct.Builder subBuilder = null;
            if (fields_ != null) {
              subBuilder = fields_.toBuilder();
            }
            fields_ = input.readMessage(com.google.protobuf.Struct.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(fields_);
              fields_ = subBuilder.buildPartial();
            }

            break;
          }
          case 42: {
            java.lang.String s = input.readStringRequireUtf8();

            lan_ = s;
            break;
          }
          case 48: {

            isMerged_ = input.readBool();
            break;
          }
          case 56: {

            applyCurrent_ = input.readBool();
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_UpdateEntityRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_UpdateEntityRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest.class, cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest.Builder.class);
  }

  public static final int ID_FIELD_NUMBER = 1;
  private long id_;
  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>uint64 id = 1;</code>
   * @return The id.
   */
  @java.lang.Override
  public long getId() {
    return id_;
  }

  public static final int SCHEMA_NAME_FIELD_NUMBER = 2;
  private volatile java.lang.Object schemaName_;
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 2;</code>
   * @return The schemaName.
   */
  @java.lang.Override
  public java.lang.String getSchemaName() {
    java.lang.Object ref = schemaName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      schemaName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 2;</code>
   * @return The bytes for schemaName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSchemaNameBytes() {
    java.lang.Object ref = schemaName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      schemaName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AUTO_APPLY_FIELD_NUMBER = 3;
  private boolean autoApply_;
  /**
   * <pre>
   * 对数据状态是ENABLED的数据自动apply change
   * </pre>
   *
   * <code>bool auto_apply = 3;</code>
   * @return The autoApply.
   */
  @java.lang.Override
  public boolean getAutoApply() {
    return autoApply_;
  }

  public static final int FIELDS_FIELD_NUMBER = 4;
  private com.google.protobuf.Struct fields_;
  /**
   * <pre>
   * Entity字段json
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 4;</code>
   * @return Whether the fields field is set.
   */
  @java.lang.Override
  public boolean hasFields() {
    return fields_ != null;
  }
  /**
   * <pre>
   * Entity字段json
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 4;</code>
   * @return The fields.
   */
  @java.lang.Override
  public com.google.protobuf.Struct getFields() {
    return fields_ == null ? com.google.protobuf.Struct.getDefaultInstance() : fields_;
  }
  /**
   * <pre>
   * Entity字段json
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 4;</code>
   */
  @java.lang.Override
  public com.google.protobuf.StructOrBuilder getFieldsOrBuilder() {
    return getFields();
  }

  public static final int LAN_FIELD_NUMBER = 5;
  private volatile java.lang.Object lan_;
  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 5;</code>
   * @return The lan.
   */
  @java.lang.Override
  public java.lang.String getLan() {
    java.lang.Object ref = lan_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      lan_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 5;</code>
   * @return The bytes for lan.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getLanBytes() {
    java.lang.Object ref = lan_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      lan_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IS_MERGED_FIELD_NUMBER = 6;
  private boolean isMerged_;
  /**
   * <pre>
   * merged
   * </pre>
   *
   * <code>bool is_merged = 6;</code>
   * @return The isMerged.
   */
  @java.lang.Override
  public boolean getIsMerged() {
    return isMerged_;
  }

  public static final int APPLY_CURRENT_FIELD_NUMBER = 7;
  private boolean applyCurrent_;
  /**
   * <code>bool apply_current = 7;</code>
   * @return The applyCurrent.
   */
  @java.lang.Override
  public boolean getApplyCurrent() {
    return applyCurrent_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (id_ != 0L) {
      output.writeUInt64(1, id_);
    }
    if (!getSchemaNameBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, schemaName_);
    }
    if (autoApply_ != false) {
      output.writeBool(3, autoApply_);
    }
    if (fields_ != null) {
      output.writeMessage(4, getFields());
    }
    if (!getLanBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, lan_);
    }
    if (isMerged_ != false) {
      output.writeBool(6, isMerged_);
    }
    if (applyCurrent_ != false) {
      output.writeBool(7, applyCurrent_);
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (id_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, id_);
    }
    if (!getSchemaNameBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, schemaName_);
    }
    if (autoApply_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(3, autoApply_);
    }
    if (fields_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getFields());
    }
    if (!getLanBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, lan_);
    }
    if (isMerged_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(6, isMerged_);
    }
    if (applyCurrent_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(7, applyCurrent_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest)) {
      return super.equals(obj);
    }
    cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest other = (cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest) obj;

    if (getId()
        != other.getId()) return false;
    if (!getSchemaName()
        .equals(other.getSchemaName())) return false;
    if (getAutoApply()
        != other.getAutoApply()) return false;
    if (hasFields() != other.hasFields()) return false;
    if (hasFields()) {
      if (!getFields()
          .equals(other.getFields())) return false;
    }
    if (!getLan()
        .equals(other.getLan())) return false;
    if (getIsMerged()
        != other.getIsMerged()) return false;
    if (getApplyCurrent()
        != other.getApplyCurrent()) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getId());
    hash = (37 * hash) + SCHEMA_NAME_FIELD_NUMBER;
    hash = (53 * hash) + getSchemaName().hashCode();
    hash = (37 * hash) + AUTO_APPLY_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getAutoApply());
    if (hasFields()) {
      hash = (37 * hash) + FIELDS_FIELD_NUMBER;
      hash = (53 * hash) + getFields().hashCode();
    }
    hash = (37 * hash) + LAN_FIELD_NUMBER;
    hash = (53 * hash) + getLan().hashCode();
    hash = (37 * hash) + IS_MERGED_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getIsMerged());
    hash = (37 * hash) + APPLY_CURRENT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getApplyCurrent());
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code entity.UpdateEntityRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:entity.UpdateEntityRequest)
      cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_UpdateEntityRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_UpdateEntityRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest.class, cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest.Builder.class);
    }

    // Construct using cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      id_ = 0L;

      schemaName_ = "";

      autoApply_ = false;

      if (fieldsBuilder_ == null) {
        fields_ = null;
      } else {
        fields_ = null;
        fieldsBuilder_ = null;
      }
      lan_ = "";

      isMerged_ = false;

      applyCurrent_ = false;

      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.Metadata.internal_static_entity_UpdateEntityRequest_descriptor;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest getDefaultInstanceForType() {
      return cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest.getDefaultInstance();
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest build() {
      cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest buildPartial() {
      cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest result = new cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest(this);
      result.id_ = id_;
      result.schemaName_ = schemaName_;
      result.autoApply_ = autoApply_;
      if (fieldsBuilder_ == null) {
        result.fields_ = fields_;
      } else {
        result.fields_ = fieldsBuilder_.build();
      }
      result.lan_ = lan_;
      result.isMerged_ = isMerged_;
      result.applyCurrent_ = applyCurrent_;
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest) {
        return mergeFrom((cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest other) {
      if (other == cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest.getDefaultInstance()) return this;
      if (other.getId() != 0L) {
        setId(other.getId());
      }
      if (!other.getSchemaName().isEmpty()) {
        schemaName_ = other.schemaName_;
        onChanged();
      }
      if (other.getAutoApply() != false) {
        setAutoApply(other.getAutoApply());
      }
      if (other.hasFields()) {
        mergeFields(other.getFields());
      }
      if (!other.getLan().isEmpty()) {
        lan_ = other.lan_;
        onChanged();
      }
      if (other.getIsMerged() != false) {
        setIsMerged(other.getIsMerged());
      }
      if (other.getApplyCurrent() != false) {
        setApplyCurrent(other.getApplyCurrent());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private long id_ ;
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>uint64 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public long getId() {
      return id_;
    }
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>uint64 id = 1;</code>
     * @param value The id to set.
     * @return This builder for chaining.
     */
    public Builder setId(long value) {
      
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 数据id
     * </pre>
     *
     * <code>uint64 id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearId() {
      
      id_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object schemaName_ = "";
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 2;</code>
     * @return The schemaName.
     */
    public java.lang.String getSchemaName() {
      java.lang.Object ref = schemaName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        schemaName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 2;</code>
     * @return The bytes for schemaName.
     */
    public com.google.protobuf.ByteString
        getSchemaNameBytes() {
      java.lang.Object ref = schemaName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        schemaName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 2;</code>
     * @param value The schemaName to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaName(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      schemaName_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSchemaName() {
      
      schemaName_ = getDefaultInstance().getSchemaName();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * schema 名称
     * </pre>
     *
     * <code>string schema_name = 2;</code>
     * @param value The bytes for schemaName to set.
     * @return This builder for chaining.
     */
    public Builder setSchemaNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      schemaName_ = value;
      onChanged();
      return this;
    }

    private boolean autoApply_ ;
    /**
     * <pre>
     * 对数据状态是ENABLED的数据自动apply change
     * </pre>
     *
     * <code>bool auto_apply = 3;</code>
     * @return The autoApply.
     */
    @java.lang.Override
    public boolean getAutoApply() {
      return autoApply_;
    }
    /**
     * <pre>
     * 对数据状态是ENABLED的数据自动apply change
     * </pre>
     *
     * <code>bool auto_apply = 3;</code>
     * @param value The autoApply to set.
     * @return This builder for chaining.
     */
    public Builder setAutoApply(boolean value) {
      
      autoApply_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 对数据状态是ENABLED的数据自动apply change
     * </pre>
     *
     * <code>bool auto_apply = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearAutoApply() {
      
      autoApply_ = false;
      onChanged();
      return this;
    }

    private com.google.protobuf.Struct fields_;
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> fieldsBuilder_;
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 4;</code>
     * @return Whether the fields field is set.
     */
    public boolean hasFields() {
      return fieldsBuilder_ != null || fields_ != null;
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 4;</code>
     * @return The fields.
     */
    public com.google.protobuf.Struct getFields() {
      if (fieldsBuilder_ == null) {
        return fields_ == null ? com.google.protobuf.Struct.getDefaultInstance() : fields_;
      } else {
        return fieldsBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 4;</code>
     */
    public Builder setFields(com.google.protobuf.Struct value) {
      if (fieldsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        fields_ = value;
        onChanged();
      } else {
        fieldsBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 4;</code>
     */
    public Builder setFields(
        com.google.protobuf.Struct.Builder builderForValue) {
      if (fieldsBuilder_ == null) {
        fields_ = builderForValue.build();
        onChanged();
      } else {
        fieldsBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 4;</code>
     */
    public Builder mergeFields(com.google.protobuf.Struct value) {
      if (fieldsBuilder_ == null) {
        if (fields_ != null) {
          fields_ =
            com.google.protobuf.Struct.newBuilder(fields_).mergeFrom(value).buildPartial();
        } else {
          fields_ = value;
        }
        onChanged();
      } else {
        fieldsBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 4;</code>
     */
    public Builder clearFields() {
      if (fieldsBuilder_ == null) {
        fields_ = null;
        onChanged();
      } else {
        fields_ = null;
        fieldsBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 4;</code>
     */
    public com.google.protobuf.Struct.Builder getFieldsBuilder() {
      
      onChanged();
      return getFieldsFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 4;</code>
     */
    public com.google.protobuf.StructOrBuilder getFieldsOrBuilder() {
      if (fieldsBuilder_ != null) {
        return fieldsBuilder_.getMessageOrBuilder();
      } else {
        return fields_ == null ?
            com.google.protobuf.Struct.getDefaultInstance() : fields_;
      }
    }
    /**
     * <pre>
     * Entity字段json
     * </pre>
     *
     * <code>.google.protobuf.Struct fields = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder> 
        getFieldsFieldBuilder() {
      if (fieldsBuilder_ == null) {
        fieldsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
            com.google.protobuf.Struct, com.google.protobuf.Struct.Builder, com.google.protobuf.StructOrBuilder>(
                getFields(),
                getParentForChildren(),
                isClean());
        fields_ = null;
      }
      return fieldsBuilder_;
    }

    private java.lang.Object lan_ = "";
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 5;</code>
     * @return The lan.
     */
    public java.lang.String getLan() {
      java.lang.Object ref = lan_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        lan_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 5;</code>
     * @return The bytes for lan.
     */
    public com.google.protobuf.ByteString
        getLanBytes() {
      java.lang.Object ref = lan_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        lan_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 5;</code>
     * @param value The lan to set.
     * @return This builder for chaining.
     */
    public Builder setLan(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      lan_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearLan() {
      
      lan_ = getDefaultInstance().getLan();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前使用的语言
     * </pre>
     *
     * <code>string lan = 5;</code>
     * @param value The bytes for lan to set.
     * @return This builder for chaining.
     */
    public Builder setLanBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      lan_ = value;
      onChanged();
      return this;
    }

    private boolean isMerged_ ;
    /**
     * <pre>
     * merged
     * </pre>
     *
     * <code>bool is_merged = 6;</code>
     * @return The isMerged.
     */
    @java.lang.Override
    public boolean getIsMerged() {
      return isMerged_;
    }
    /**
     * <pre>
     * merged
     * </pre>
     *
     * <code>bool is_merged = 6;</code>
     * @param value The isMerged to set.
     * @return This builder for chaining.
     */
    public Builder setIsMerged(boolean value) {
      
      isMerged_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * merged
     * </pre>
     *
     * <code>bool is_merged = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearIsMerged() {
      
      isMerged_ = false;
      onChanged();
      return this;
    }

    private boolean applyCurrent_ ;
    /**
     * <code>bool apply_current = 7;</code>
     * @return The applyCurrent.
     */
    @java.lang.Override
    public boolean getApplyCurrent() {
      return applyCurrent_;
    }
    /**
     * <code>bool apply_current = 7;</code>
     * @param value The applyCurrent to set.
     * @return This builder for chaining.
     */
    public Builder setApplyCurrent(boolean value) {
      
      applyCurrent_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>bool apply_current = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearApplyCurrent() {
      
      applyCurrent_ = false;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:entity.UpdateEntityRequest)
  }

  // @@protoc_insertion_point(class_scope:entity.UpdateEntityRequest)
  private static final cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest();
  }

  public static cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UpdateEntityRequest>
      PARSER = new com.google.protobuf.AbstractParser<UpdateEntityRequest>() {
    @java.lang.Override
    public UpdateEntityRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new UpdateEntityRequest(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<UpdateEntityRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<UpdateEntityRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public cn.hexcloud.pbis.common.service.integration.metadata.UpdateEntityRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

