// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface UpdateEntityStateRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.UpdateEntityStateRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>uint64 id = 1;</code>
   * @return The id.
   */
  long getId();

  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 2;</code>
   * @return The schemaName.
   */
  java.lang.String getSchemaName();
  /**
   * <pre>
   * schema 名称
   * </pre>
   *
   * <code>string schema_name = 2;</code>
   * @return The bytes for schemaName.
   */
  com.google.protobuf.ByteString
      getSchemaNameBytes();

  /**
   * <pre>
   * state
   * </pre>
   *
   * <code>string state = 3;</code>
   * @return The state.
   */
  java.lang.String getState();
  /**
   * <pre>
   * state
   * </pre>
   *
   * <code>string state = 3;</code>
   * @return The bytes for state.
   */
  com.google.protobuf.ByteString
      getStateBytes();

  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 4;</code>
   * @return The lan.
   */
  java.lang.String getLan();
  /**
   * <pre>
   * 当前使用的语言
   * </pre>
   *
   * <code>string lan = 4;</code>
   * @return The bytes for lan.
   */
  com.google.protobuf.ByteString
      getLanBytes();
}
