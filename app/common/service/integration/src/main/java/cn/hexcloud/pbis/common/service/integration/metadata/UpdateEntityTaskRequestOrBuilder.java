// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Metadata.proto

package cn.hexcloud.pbis.common.service.integration.metadata;

public interface UpdateEntityTaskRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:entity.UpdateEntityTaskRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 数据id
   * </pre>
   *
   * <code>uint64 id = 1;</code>
   * @return The id.
   */
  long getId();

  /**
   * <code>string schema_name = 2;</code>
   * @return The schemaName.
   */
  java.lang.String getSchemaName();
  /**
   * <code>string schema_name = 2;</code>
   * @return The bytes for schemaName.
   */
  com.google.protobuf.ByteString
      getSchemaNameBytes();

  /**
   * <pre>
   * task 字段
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 3;</code>
   * @return Whether the fields field is set.
   */
  boolean hasFields();
  /**
   * <pre>
   * task 字段
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 3;</code>
   * @return The fields.
   */
  com.google.protobuf.Struct getFields();
  /**
   * <pre>
   * task 字段
   * </pre>
   *
   * <code>.google.protobuf.Struct fields = 3;</code>
   */
  com.google.protobuf.StructOrBuilder getFieldsOrBuilder();
}
