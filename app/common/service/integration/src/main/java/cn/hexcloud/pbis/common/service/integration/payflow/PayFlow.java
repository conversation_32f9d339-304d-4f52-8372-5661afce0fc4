// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PayFlow.proto

package cn.hexcloud.pbis.common.service.integration.payflow;

public final class PayFlow {
  private PayFlow() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface NotifyReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:payflow.NotifyReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * (必传) 业务系统订单号
     * </pre>
     *
     * <code>string payment_ticket_id = 1;</code>
     * @return The paymentTicketId.
     */
    java.lang.String getPaymentTicketId();
    /**
     * <pre>
     * (必传) 业务系统订单号
     * </pre>
     *
     * <code>string payment_ticket_id = 1;</code>
     * @return The bytes for paymentTicketId.
     */
    com.google.protobuf.ByteString
        getPaymentTicketIdBytes();

    /**
     * <pre>
     * (非必传) 第三方支付订单号，比如 微信订单号、支付宝订单号
     * </pre>
     *
     * <code>string tp_transaction_no = 2;</code>
     * @return The tpTransactionNo.
     */
    java.lang.String getTpTransactionNo();
    /**
     * <pre>
     * (非必传) 第三方支付订单号，比如 微信订单号、支付宝订单号
     * </pre>
     *
     * <code>string tp_transaction_no = 2;</code>
     * @return The bytes for tpTransactionNo.
     */
    com.google.protobuf.ByteString
        getTpTransactionNoBytes();

    /**
     * <pre>
     * (必传) 支付到账、退款到账状态。取 pbis的 transactionState 的值，理论上应只有 SUCCESS, REFUNDED
     * </pre>
     *
     * <code>string status = 3;</code>
     * @return The status.
     */
    java.lang.String getStatus();
    /**
     * <pre>
     * (必传) 支付到账、退款到账状态。取 pbis的 transactionState 的值，理论上应只有 SUCCESS, REFUNDED
     * </pre>
     *
     * <code>string status = 3;</code>
     * @return The bytes for status.
     */
    com.google.protobuf.ByteString
        getStatusBytes();

    /**
     * <pre>
     * (必传) 行为操作
     * </pre>
     *
     * <code>.payflow.NotifyReq.ActionType action_type = 4;</code>
     * @return The enum numeric value on the wire for actionType.
     */
    int getActionTypeValue();
    /**
     * <pre>
     * (必传) 行为操作
     * </pre>
     *
     * <code>.payflow.NotifyReq.ActionType action_type = 4;</code>
     * @return The actionType.
     */
    cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.ActionType getActionType();

    /**
     * <pre>
     * (非必填) 真实支付方式
     * </pre>
     *
     * <code>string payMethod = 5;</code>
     * @return The payMethod.
     */
    java.lang.String getPayMethod();
    /**
     * <pre>
     * (非必填) 真实支付方式
     * </pre>
     *
     * <code>string payMethod = 5;</code>
     * @return The bytes for payMethod.
     */
    com.google.protobuf.ByteString
        getPayMethodBytes();

    /**
     * <pre>
     *  (非必填) 扩展参数 json 字符串
     * </pre>
     *
     * <code>string extendedParams = 6;</code>
     * @return The extendedParams.
     */
    java.lang.String getExtendedParams();
    /**
     * <pre>
     *  (非必填) 扩展参数 json 字符串
     * </pre>
     *
     * <code>string extendedParams = 6;</code>
     * @return The bytes for extendedParams.
     */
    com.google.protobuf.ByteString
        getExtendedParamsBytes();
  }
  /**
   * Protobuf type {@code payflow.NotifyReq}
   */
  public static final class NotifyReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:payflow.NotifyReq)
      NotifyReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use NotifyReq.newBuilder() to construct.
    private NotifyReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private NotifyReq() {
      paymentTicketId_ = "";
      tpTransactionNo_ = "";
      status_ = "";
      actionType_ = 0;
      payMethod_ = "";
      extendedParams_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new NotifyReq();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private NotifyReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              paymentTicketId_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              tpTransactionNo_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              status_ = s;
              break;
            }
            case 32: {
              int rawValue = input.readEnum();

              actionType_ = rawValue;
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();

              payMethod_ = s;
              break;
            }
            case 50: {
              java.lang.String s = input.readStringRequireUtf8();

              extendedParams_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.internal_static_payflow_NotifyReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.internal_static_payflow_NotifyReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.class, cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.Builder.class);
    }

    /**
     * Protobuf enum {@code payflow.NotifyReq.ActionType}
     */
    public enum ActionType
        implements com.google.protobuf.ProtocolMessageEnum {
      /**
       * <pre>
       * 支付标识
       * </pre>
       *
       * <code>PAY = 0;</code>
       */
      PAY(0),
      /**
       * <pre>
       * 退款标识
       * </pre>
       *
       * <code>REFUND = 1;</code>
       */
      REFUND(1),
      UNRECOGNIZED(-1),
      ;

      /**
       * <pre>
       * 支付标识
       * </pre>
       *
       * <code>PAY = 0;</code>
       */
      public static final int PAY_VALUE = 0;
      /**
       * <pre>
       * 退款标识
       * </pre>
       *
       * <code>REFUND = 1;</code>
       */
      public static final int REFUND_VALUE = 1;


      public final int getNumber() {
        if (this == UNRECOGNIZED) {
          throw new java.lang.IllegalArgumentException(
              "Can't get the number of an unknown enum value.");
        }
        return value;
      }

      /**
       * @param value The numeric wire value of the corresponding enum entry.
       * @return The enum associated with the given numeric wire value.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static ActionType valueOf(int value) {
        return forNumber(value);
      }

      /**
       * @param value The numeric wire value of the corresponding enum entry.
       * @return The enum associated with the given numeric wire value.
       */
      public static ActionType forNumber(int value) {
        switch (value) {
          case 0: return PAY;
          case 1: return REFUND;
          default: return null;
        }
      }

      public static com.google.protobuf.Internal.EnumLiteMap<ActionType>
          internalGetValueMap() {
        return internalValueMap;
      }
      private static final com.google.protobuf.Internal.EnumLiteMap<
          ActionType> internalValueMap =
            new com.google.protobuf.Internal.EnumLiteMap<ActionType>() {
              public ActionType findValueByNumber(int number) {
                return ActionType.forNumber(number);
              }
            };

      public final com.google.protobuf.Descriptors.EnumValueDescriptor
          getValueDescriptor() {
        if (this == UNRECOGNIZED) {
          throw new java.lang.IllegalStateException(
              "Can't get the descriptor of an unrecognized enum value.");
        }
        return getDescriptor().getValues().get(ordinal());
      }
      public final com.google.protobuf.Descriptors.EnumDescriptor
          getDescriptorForType() {
        return getDescriptor();
      }
      public static final com.google.protobuf.Descriptors.EnumDescriptor
          getDescriptor() {
        return cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.getDescriptor().getEnumTypes().get(0);
      }

      private static final ActionType[] VALUES = values();

      public static ActionType valueOf(
          com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
        if (desc.getType() != getDescriptor()) {
          throw new java.lang.IllegalArgumentException(
            "EnumValueDescriptor is not for this type.");
        }
        if (desc.getIndex() == -1) {
          return UNRECOGNIZED;
        }
        return VALUES[desc.getIndex()];
      }

      private final int value;

      private ActionType(int value) {
        this.value = value;
      }

      // @@protoc_insertion_point(enum_scope:payflow.NotifyReq.ActionType)
    }

    public static final int PAYMENT_TICKET_ID_FIELD_NUMBER = 1;
    private volatile java.lang.Object paymentTicketId_;
    /**
     * <pre>
     * (必传) 业务系统订单号
     * </pre>
     *
     * <code>string payment_ticket_id = 1;</code>
     * @return The paymentTicketId.
     */
    @java.lang.Override
    public java.lang.String getPaymentTicketId() {
      java.lang.Object ref = paymentTicketId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        paymentTicketId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * (必传) 业务系统订单号
     * </pre>
     *
     * <code>string payment_ticket_id = 1;</code>
     * @return The bytes for paymentTicketId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPaymentTicketIdBytes() {
      java.lang.Object ref = paymentTicketId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        paymentTicketId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TP_TRANSACTION_NO_FIELD_NUMBER = 2;
    private volatile java.lang.Object tpTransactionNo_;
    /**
     * <pre>
     * (非必传) 第三方支付订单号，比如 微信订单号、支付宝订单号
     * </pre>
     *
     * <code>string tp_transaction_no = 2;</code>
     * @return The tpTransactionNo.
     */
    @java.lang.Override
    public java.lang.String getTpTransactionNo() {
      java.lang.Object ref = tpTransactionNo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        tpTransactionNo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * (非必传) 第三方支付订单号，比如 微信订单号、支付宝订单号
     * </pre>
     *
     * <code>string tp_transaction_no = 2;</code>
     * @return The bytes for tpTransactionNo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTpTransactionNoBytes() {
      java.lang.Object ref = tpTransactionNo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        tpTransactionNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int STATUS_FIELD_NUMBER = 3;
    private volatile java.lang.Object status_;
    /**
     * <pre>
     * (必传) 支付到账、退款到账状态。取 pbis的 transactionState 的值，理论上应只有 SUCCESS, REFUNDED
     * </pre>
     *
     * <code>string status = 3;</code>
     * @return The status.
     */
    @java.lang.Override
    public java.lang.String getStatus() {
      java.lang.Object ref = status_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        status_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * (必传) 支付到账、退款到账状态。取 pbis的 transactionState 的值，理论上应只有 SUCCESS, REFUNDED
     * </pre>
     *
     * <code>string status = 3;</code>
     * @return The bytes for status.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getStatusBytes() {
      java.lang.Object ref = status_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        status_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ACTION_TYPE_FIELD_NUMBER = 4;
    private int actionType_;
    /**
     * <pre>
     * (必传) 行为操作
     * </pre>
     *
     * <code>.payflow.NotifyReq.ActionType action_type = 4;</code>
     * @return The enum numeric value on the wire for actionType.
     */
    @java.lang.Override public int getActionTypeValue() {
      return actionType_;
    }
    /**
     * <pre>
     * (必传) 行为操作
     * </pre>
     *
     * <code>.payflow.NotifyReq.ActionType action_type = 4;</code>
     * @return The actionType.
     */
    @java.lang.Override public cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.ActionType getActionType() {
      @SuppressWarnings("deprecation")
      cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.ActionType result = cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.ActionType.valueOf(actionType_);
      return result == null ? cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.ActionType.UNRECOGNIZED : result;
    }

    public static final int PAYMETHOD_FIELD_NUMBER = 5;
    private volatile java.lang.Object payMethod_;
    /**
     * <pre>
     * (非必填) 真实支付方式
     * </pre>
     *
     * <code>string payMethod = 5;</code>
     * @return The payMethod.
     */
    @java.lang.Override
    public java.lang.String getPayMethod() {
      java.lang.Object ref = payMethod_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        payMethod_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * (非必填) 真实支付方式
     * </pre>
     *
     * <code>string payMethod = 5;</code>
     * @return The bytes for payMethod.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPayMethodBytes() {
      java.lang.Object ref = payMethod_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payMethod_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EXTENDEDPARAMS_FIELD_NUMBER = 6;
    private volatile java.lang.Object extendedParams_;
    /**
     * <pre>
     *  (非必填) 扩展参数 json 字符串
     * </pre>
     *
     * <code>string extendedParams = 6;</code>
     * @return The extendedParams.
     */
    @java.lang.Override
    public java.lang.String getExtendedParams() {
      java.lang.Object ref = extendedParams_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        extendedParams_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *  (非必填) 扩展参数 json 字符串
     * </pre>
     *
     * <code>string extendedParams = 6;</code>
     * @return The bytes for extendedParams.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getExtendedParamsBytes() {
      java.lang.Object ref = extendedParams_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extendedParams_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getPaymentTicketIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, paymentTicketId_);
      }
      if (!getTpTransactionNoBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, tpTransactionNo_);
      }
      if (!getStatusBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, status_);
      }
      if (actionType_ != cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.ActionType.PAY.getNumber()) {
        output.writeEnum(4, actionType_);
      }
      if (!getPayMethodBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, payMethod_);
      }
      if (!getExtendedParamsBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, extendedParams_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getPaymentTicketIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, paymentTicketId_);
      }
      if (!getTpTransactionNoBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, tpTransactionNo_);
      }
      if (!getStatusBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, status_);
      }
      if (actionType_ != cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.ActionType.PAY.getNumber()) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(4, actionType_);
      }
      if (!getPayMethodBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, payMethod_);
      }
      if (!getExtendedParamsBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, extendedParams_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq)) {
        return super.equals(obj);
      }
      cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq other = (cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq) obj;

      if (!getPaymentTicketId()
          .equals(other.getPaymentTicketId())) return false;
      if (!getTpTransactionNo()
          .equals(other.getTpTransactionNo())) return false;
      if (!getStatus()
          .equals(other.getStatus())) return false;
      if (actionType_ != other.actionType_) return false;
      if (!getPayMethod()
          .equals(other.getPayMethod())) return false;
      if (!getExtendedParams()
          .equals(other.getExtendedParams())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + PAYMENT_TICKET_ID_FIELD_NUMBER;
      hash = (53 * hash) + getPaymentTicketId().hashCode();
      hash = (37 * hash) + TP_TRANSACTION_NO_FIELD_NUMBER;
      hash = (53 * hash) + getTpTransactionNo().hashCode();
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus().hashCode();
      hash = (37 * hash) + ACTION_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + actionType_;
      hash = (37 * hash) + PAYMETHOD_FIELD_NUMBER;
      hash = (53 * hash) + getPayMethod().hashCode();
      hash = (37 * hash) + EXTENDEDPARAMS_FIELD_NUMBER;
      hash = (53 * hash) + getExtendedParams().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code payflow.NotifyReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:payflow.NotifyReq)
        cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.internal_static_payflow_NotifyReq_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.internal_static_payflow_NotifyReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.class, cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.Builder.class);
      }

      // Construct using cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        paymentTicketId_ = "";

        tpTransactionNo_ = "";

        status_ = "";

        actionType_ = 0;

        payMethod_ = "";

        extendedParams_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.internal_static_payflow_NotifyReq_descriptor;
      }

      @java.lang.Override
      public cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq getDefaultInstanceForType() {
        return cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.getDefaultInstance();
      }

      @java.lang.Override
      public cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq build() {
        cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq buildPartial() {
        cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq result = new cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq(this);
        result.paymentTicketId_ = paymentTicketId_;
        result.tpTransactionNo_ = tpTransactionNo_;
        result.status_ = status_;
        result.actionType_ = actionType_;
        result.payMethod_ = payMethod_;
        result.extendedParams_ = extendedParams_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq) {
          return mergeFrom((cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq other) {
        if (other == cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.getDefaultInstance()) return this;
        if (!other.getPaymentTicketId().isEmpty()) {
          paymentTicketId_ = other.paymentTicketId_;
          onChanged();
        }
        if (!other.getTpTransactionNo().isEmpty()) {
          tpTransactionNo_ = other.tpTransactionNo_;
          onChanged();
        }
        if (!other.getStatus().isEmpty()) {
          status_ = other.status_;
          onChanged();
        }
        if (other.actionType_ != 0) {
          setActionTypeValue(other.getActionTypeValue());
        }
        if (!other.getPayMethod().isEmpty()) {
          payMethod_ = other.payMethod_;
          onChanged();
        }
        if (!other.getExtendedParams().isEmpty()) {
          extendedParams_ = other.extendedParams_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object paymentTicketId_ = "";
      /**
       * <pre>
       * (必传) 业务系统订单号
       * </pre>
       *
       * <code>string payment_ticket_id = 1;</code>
       * @return The paymentTicketId.
       */
      public java.lang.String getPaymentTicketId() {
        java.lang.Object ref = paymentTicketId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          paymentTicketId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * (必传) 业务系统订单号
       * </pre>
       *
       * <code>string payment_ticket_id = 1;</code>
       * @return The bytes for paymentTicketId.
       */
      public com.google.protobuf.ByteString
          getPaymentTicketIdBytes() {
        java.lang.Object ref = paymentTicketId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          paymentTicketId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * (必传) 业务系统订单号
       * </pre>
       *
       * <code>string payment_ticket_id = 1;</code>
       * @param value The paymentTicketId to set.
       * @return This builder for chaining.
       */
      public Builder setPaymentTicketId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        paymentTicketId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * (必传) 业务系统订单号
       * </pre>
       *
       * <code>string payment_ticket_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPaymentTicketId() {
        
        paymentTicketId_ = getDefaultInstance().getPaymentTicketId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * (必传) 业务系统订单号
       * </pre>
       *
       * <code>string payment_ticket_id = 1;</code>
       * @param value The bytes for paymentTicketId to set.
       * @return This builder for chaining.
       */
      public Builder setPaymentTicketIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        paymentTicketId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object tpTransactionNo_ = "";
      /**
       * <pre>
       * (非必传) 第三方支付订单号，比如 微信订单号、支付宝订单号
       * </pre>
       *
       * <code>string tp_transaction_no = 2;</code>
       * @return The tpTransactionNo.
       */
      public java.lang.String getTpTransactionNo() {
        java.lang.Object ref = tpTransactionNo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          tpTransactionNo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * (非必传) 第三方支付订单号，比如 微信订单号、支付宝订单号
       * </pre>
       *
       * <code>string tp_transaction_no = 2;</code>
       * @return The bytes for tpTransactionNo.
       */
      public com.google.protobuf.ByteString
          getTpTransactionNoBytes() {
        java.lang.Object ref = tpTransactionNo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          tpTransactionNo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * (非必传) 第三方支付订单号，比如 微信订单号、支付宝订单号
       * </pre>
       *
       * <code>string tp_transaction_no = 2;</code>
       * @param value The tpTransactionNo to set.
       * @return This builder for chaining.
       */
      public Builder setTpTransactionNo(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        tpTransactionNo_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * (非必传) 第三方支付订单号，比如 微信订单号、支付宝订单号
       * </pre>
       *
       * <code>string tp_transaction_no = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTpTransactionNo() {
        
        tpTransactionNo_ = getDefaultInstance().getTpTransactionNo();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * (非必传) 第三方支付订单号，比如 微信订单号、支付宝订单号
       * </pre>
       *
       * <code>string tp_transaction_no = 2;</code>
       * @param value The bytes for tpTransactionNo to set.
       * @return This builder for chaining.
       */
      public Builder setTpTransactionNoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        tpTransactionNo_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object status_ = "";
      /**
       * <pre>
       * (必传) 支付到账、退款到账状态。取 pbis的 transactionState 的值，理论上应只有 SUCCESS, REFUNDED
       * </pre>
       *
       * <code>string status = 3;</code>
       * @return The status.
       */
      public java.lang.String getStatus() {
        java.lang.Object ref = status_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          status_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * (必传) 支付到账、退款到账状态。取 pbis的 transactionState 的值，理论上应只有 SUCCESS, REFUNDED
       * </pre>
       *
       * <code>string status = 3;</code>
       * @return The bytes for status.
       */
      public com.google.protobuf.ByteString
          getStatusBytes() {
        java.lang.Object ref = status_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          status_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * (必传) 支付到账、退款到账状态。取 pbis的 transactionState 的值，理论上应只有 SUCCESS, REFUNDED
       * </pre>
       *
       * <code>string status = 3;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * (必传) 支付到账、退款到账状态。取 pbis的 transactionState 的值，理论上应只有 SUCCESS, REFUNDED
       * </pre>
       *
       * <code>string status = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        
        status_ = getDefaultInstance().getStatus();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * (必传) 支付到账、退款到账状态。取 pbis的 transactionState 的值，理论上应只有 SUCCESS, REFUNDED
       * </pre>
       *
       * <code>string status = 3;</code>
       * @param value The bytes for status to set.
       * @return This builder for chaining.
       */
      public Builder setStatusBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        status_ = value;
        onChanged();
        return this;
      }

      private int actionType_ = 0;
      /**
       * <pre>
       * (必传) 行为操作
       * </pre>
       *
       * <code>.payflow.NotifyReq.ActionType action_type = 4;</code>
       * @return The enum numeric value on the wire for actionType.
       */
      @java.lang.Override public int getActionTypeValue() {
        return actionType_;
      }
      /**
       * <pre>
       * (必传) 行为操作
       * </pre>
       *
       * <code>.payflow.NotifyReq.ActionType action_type = 4;</code>
       * @param value The enum numeric value on the wire for actionType to set.
       * @return This builder for chaining.
       */
      public Builder setActionTypeValue(int value) {
        
        actionType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * (必传) 行为操作
       * </pre>
       *
       * <code>.payflow.NotifyReq.ActionType action_type = 4;</code>
       * @return The actionType.
       */
      @java.lang.Override
      public cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.ActionType getActionType() {
        @SuppressWarnings("deprecation")
        cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.ActionType result = cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.ActionType.valueOf(actionType_);
        return result == null ? cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.ActionType.UNRECOGNIZED : result;
      }
      /**
       * <pre>
       * (必传) 行为操作
       * </pre>
       *
       * <code>.payflow.NotifyReq.ActionType action_type = 4;</code>
       * @param value The actionType to set.
       * @return This builder for chaining.
       */
      public Builder setActionType(cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.ActionType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        
        actionType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * (必传) 行为操作
       * </pre>
       *
       * <code>.payflow.NotifyReq.ActionType action_type = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearActionType() {
        
        actionType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object payMethod_ = "";
      /**
       * <pre>
       * (非必填) 真实支付方式
       * </pre>
       *
       * <code>string payMethod = 5;</code>
       * @return The payMethod.
       */
      public java.lang.String getPayMethod() {
        java.lang.Object ref = payMethod_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          payMethod_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * (非必填) 真实支付方式
       * </pre>
       *
       * <code>string payMethod = 5;</code>
       * @return The bytes for payMethod.
       */
      public com.google.protobuf.ByteString
          getPayMethodBytes() {
        java.lang.Object ref = payMethod_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          payMethod_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * (非必填) 真实支付方式
       * </pre>
       *
       * <code>string payMethod = 5;</code>
       * @param value The payMethod to set.
       * @return This builder for chaining.
       */
      public Builder setPayMethod(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        payMethod_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * (非必填) 真实支付方式
       * </pre>
       *
       * <code>string payMethod = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearPayMethod() {
        
        payMethod_ = getDefaultInstance().getPayMethod();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * (非必填) 真实支付方式
       * </pre>
       *
       * <code>string payMethod = 5;</code>
       * @param value The bytes for payMethod to set.
       * @return This builder for chaining.
       */
      public Builder setPayMethodBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        payMethod_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object extendedParams_ = "";
      /**
       * <pre>
       *  (非必填) 扩展参数 json 字符串
       * </pre>
       *
       * <code>string extendedParams = 6;</code>
       * @return The extendedParams.
       */
      public java.lang.String getExtendedParams() {
        java.lang.Object ref = extendedParams_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          extendedParams_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *  (非必填) 扩展参数 json 字符串
       * </pre>
       *
       * <code>string extendedParams = 6;</code>
       * @return The bytes for extendedParams.
       */
      public com.google.protobuf.ByteString
          getExtendedParamsBytes() {
        java.lang.Object ref = extendedParams_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          extendedParams_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *  (非必填) 扩展参数 json 字符串
       * </pre>
       *
       * <code>string extendedParams = 6;</code>
       * @param value The extendedParams to set.
       * @return This builder for chaining.
       */
      public Builder setExtendedParams(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        extendedParams_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *  (非必填) 扩展参数 json 字符串
       * </pre>
       *
       * <code>string extendedParams = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtendedParams() {
        
        extendedParams_ = getDefaultInstance().getExtendedParams();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *  (非必填) 扩展参数 json 字符串
       * </pre>
       *
       * <code>string extendedParams = 6;</code>
       * @param value The bytes for extendedParams to set.
       * @return This builder for chaining.
       */
      public Builder setExtendedParamsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        extendedParams_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:payflow.NotifyReq)
    }

    // @@protoc_insertion_point(class_scope:payflow.NotifyReq)
    private static final cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq();
    }

    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<NotifyReq>
        PARSER = new com.google.protobuf.AbstractParser<NotifyReq>() {
      @java.lang.Override
      public NotifyReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new NotifyReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<NotifyReq> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<NotifyReq> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface NotifyResOrBuilder extends
      // @@protoc_insertion_point(interface_extends:payflow.NotifyRes)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code payflow.NotifyRes}
   */
  public static final class NotifyRes extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:payflow.NotifyRes)
      NotifyResOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use NotifyRes.newBuilder() to construct.
    private NotifyRes(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private NotifyRes() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new NotifyRes();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private NotifyRes(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.internal_static_payflow_NotifyRes_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.internal_static_payflow_NotifyRes_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes.class, cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes)) {
        return super.equals(obj);
      }
      cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes other = (cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code payflow.NotifyRes}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:payflow.NotifyRes)
        cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyResOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.internal_static_payflow_NotifyRes_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.internal_static_payflow_NotifyRes_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes.class, cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes.Builder.class);
      }

      // Construct using cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.internal_static_payflow_NotifyRes_descriptor;
      }

      @java.lang.Override
      public cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes getDefaultInstanceForType() {
        return cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes.getDefaultInstance();
      }

      @java.lang.Override
      public cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes build() {
        cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes buildPartial() {
        cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes result = new cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes) {
          return mergeFrom((cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes other) {
        if (other == cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:payflow.NotifyRes)
    }

    // @@protoc_insertion_point(class_scope:payflow.NotifyRes)
    private static final cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes();
    }

    public static cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<NotifyRes>
        PARSER = new com.google.protobuf.AbstractParser<NotifyRes>() {
      @java.lang.Override
      public NotifyRes parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new NotifyRes(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<NotifyRes> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<NotifyRes> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_payflow_NotifyReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_payflow_NotifyReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_payflow_NotifyRes_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_payflow_NotifyRes_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rPayFlow.proto\022\007payflow\"\323\001\n\tNotifyReq\022\031" +
      "\n\021payment_ticket_id\030\001 \001(\t\022\031\n\021tp_transact" +
      "ion_no\030\002 \001(\t\022\016\n\006status\030\003 \001(\t\0222\n\013action_t" +
      "ype\030\004 \001(\0162\035.payflow.NotifyReq.ActionType" +
      "\022\021\n\tpayMethod\030\005 \001(\t\022\026\n\016extendedParams\030\006 " +
      "\001(\t\"!\n\nActionType\022\007\n\003PAY\020\000\022\n\n\006REFUND\020\001\"\013" +
      "\n\tNotifyRes2=\n\007Payflow\0222\n\006Notify\022\022.payfl" +
      "ow.NotifyReq\032\022.payflow.NotifyRes\"\000B@\n3cn" +
      ".hexcloud.pbis.common.service.integratio" +
      "n.payflowZ\t.;payflowb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_payflow_NotifyReq_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_payflow_NotifyReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_payflow_NotifyReq_descriptor,
        new java.lang.String[] { "PaymentTicketId", "TpTransactionNo", "Status", "ActionType", "PayMethod", "ExtendedParams", });
    internal_static_payflow_NotifyRes_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_payflow_NotifyRes_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_payflow_NotifyRes_descriptor,
        new java.lang.String[] { });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
