package cn.hexcloud.pbis.common.service.integration.payflow;

import cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq;
import cn.hexcloud.pbis.common.service.integration.payflow.PayflowGrpc.PayflowBlockingStub;
import cn.hexcloud.pbis.common.util.config.PBISApplicationConfig;
import io.grpc.Channel;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import net.devh.boot.grpc.client.inject.GrpcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName PayFlowClient.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/21 18:03:29
 */
@Service
public class PayFlowClient {

  @GrpcClient("PayFlowService")
  private Channel serviceChannel;

  @Autowired
  private PBISApplicationConfig pbisApplicationConfig;

  PayflowBlockingStub serviceBlockingStub;

  @PostConstruct
  public void init() {
    this.serviceBlockingStub = PayflowGrpc.newBlockingStub(serviceChannel);
  }

  public void payNotify(NotifyReq notifyReq) {
    serviceBlockingStub
        .withDeadlineAfter(pbisApplicationConfig.getGrpcClientTimeout(), TimeUnit.MILLISECONDS)
        .notify(notifyReq);
  }

}
