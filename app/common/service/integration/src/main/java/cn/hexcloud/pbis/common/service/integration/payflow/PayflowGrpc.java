package cn.hexcloud.pbis.common.service.integration.payflow;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.40.1)",
    comments = "Source: PayFlow.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class PayflowGrpc {

  private PayflowGrpc() {}

  public static final String SERVICE_NAME = "payflow.Payflow";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq,
      cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes> getNotifyMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "Notify",
      requestType = cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.class,
      responseType = cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq,
      cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes> getNotifyMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq, cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes> getNotifyMethod;
    if ((getNotifyMethod = PayflowGrpc.getNotifyMethod) == null) {
      synchronized (PayflowGrpc.class) {
        if ((getNotifyMethod = PayflowGrpc.getNotifyMethod) == null) {
          PayflowGrpc.getNotifyMethod = getNotifyMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq, cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "Notify"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes.getDefaultInstance()))
              .setSchemaDescriptor(new PayflowMethodDescriptorSupplier("Notify"))
              .build();
        }
      }
    }
    return getNotifyMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static PayflowStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<PayflowStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<PayflowStub>() {
        @java.lang.Override
        public PayflowStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new PayflowStub(channel, callOptions);
        }
      };
    return PayflowStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static PayflowBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<PayflowBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<PayflowBlockingStub>() {
        @java.lang.Override
        public PayflowBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new PayflowBlockingStub(channel, callOptions);
        }
      };
    return PayflowBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static PayflowFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<PayflowFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<PayflowFutureStub>() {
        @java.lang.Override
        public PayflowFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new PayflowFutureStub(channel, callOptions);
        }
      };
    return PayflowFutureStub.newStub(factory, channel);
  }

  /**
   */
  public static abstract class PayflowImplBase implements io.grpc.BindableService {

    /**
     * <pre>
     * NotifyPay 支付结果通知
     * </pre>
     */
    public void notify(cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getNotifyMethod(), responseObserver);
    }

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getNotifyMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq,
                cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes>(
                  this, METHODID_NOTIFY)))
          .build();
    }
  }

  /**
   */
  public static final class PayflowStub extends io.grpc.stub.AbstractAsyncStub<PayflowStub> {
    private PayflowStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected PayflowStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new PayflowStub(channel, callOptions);
    }

    /**
     * <pre>
     * NotifyPay 支付结果通知
     * </pre>
     */
    public void notify(cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq request,
        io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getNotifyMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   */
  public static final class PayflowBlockingStub extends io.grpc.stub.AbstractBlockingStub<PayflowBlockingStub> {
    private PayflowBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected PayflowBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new PayflowBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * NotifyPay 支付结果通知
     * </pre>
     */
    public cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes notify(cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getNotifyMethod(), getCallOptions(), request);
    }
  }

  /**
   */
  public static final class PayflowFutureStub extends io.grpc.stub.AbstractFutureStub<PayflowFutureStub> {
    private PayflowFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected PayflowFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new PayflowFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * NotifyPay 支付结果通知
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes> notify(
        cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getNotifyMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_NOTIFY = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final PayflowImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(PayflowImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_NOTIFY:
          serviceImpl.notify((cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyReq) request,
              (io.grpc.stub.StreamObserver<cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.NotifyRes>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class PayflowBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    PayflowBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.payflow.PayFlow.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("Payflow");
    }
  }

  private static final class PayflowFileDescriptorSupplier
      extends PayflowBaseDescriptorSupplier {
    PayflowFileDescriptorSupplier() {}
  }

  private static final class PayflowMethodDescriptorSupplier
      extends PayflowBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    PayflowMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (PayflowGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new PayflowFileDescriptorSupplier())
              .addMethod(getNotifyMethod())
              .build();
        }
      }
    }
    return result;
  }
}
