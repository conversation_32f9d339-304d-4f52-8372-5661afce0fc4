package cn.hexcloud.pbis.common.service.integration.script;

import cn.hexcloud.commons.exception.CommonException;
import cn.hexcloud.commons.trace.util.TraceUtil;
import cn.hexcloud.commons.utils.RedisUtil;
import cn.hexcloud.pbis.common.dal.entity.TransactionScriptEntity;
import cn.hexcloud.pbis.common.dal.entity.TransactionScriptEntityExample;
import cn.hexcloud.pbis.common.dal.entity.TransactionScriptEntityExample.Criteria;
import cn.hexcloud.pbis.common.dal.mapper.TransactionScriptEntityMapper;
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelScriptConfig;
import cn.hexcloud.pbis.common.util.GroovyUtil;
import cn.hexcloud.pbis.common.util.exception.ServiceError;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @ClassName GroovyScriptManager.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/20 11:14:10
 */
@Component
public class GroovyScriptManager implements ScriptManager {

  @Resource
  private TransactionScriptEntityMapper transactionScriptEntityMapper;

  @Autowired
  private ChannelScriptConfig channelScriptConfig;

  @Override
  public String getScript(String channel, String scriptKey) {
    if (StringUtils.isEmpty(channel)) {
      throw new CommonException(ServiceError.INVALID_PARAM, "channel");
    }
    if (StringUtils.isEmpty(scriptKey)) {
      throw new CommonException(ServiceError.INVALID_PARAM, "scriptKey");
    }

    // 尝试从缓存中获取script
    String scriptVersion = channelScriptConfig.getScriptVersion(scriptKey);
    String redisKey = "PBIS-SCRIPTS:" + scriptKey.toUpperCase() + ":" + scriptVersion;

    Map<String, String> eventAttrs = CollectionUtils.newHashMap(2);
    eventAttrs.put("cache_script_key", redisKey);
    TraceUtil.currSpan().addEvent("cacheScriptEnd", eventAttrs);

    // 从redis获取取脚本
    String scriptText = RedisUtil.StringOps.get(redisKey);

    eventAttrs.clear();
    eventAttrs.put("load_redis_cache_script", redisKey);
    TraceUtil.currSpan().addEvent("loadRedisCacheScriptEnd", eventAttrs);

    if (StringUtils.isEmpty(scriptText)) {
      // 缓存中未取到script，尝试从数据库中获取
      TransactionScriptEntityExample example = new TransactionScriptEntityExample();
      Criteria criteria = example.createCriteria();
      criteria.andChannelCodeEqualTo(channel).andScriptKeyEqualTo(scriptKey).andIsDeletedEqualTo(false);
      if ("LATEST".equalsIgnoreCase(scriptVersion)) {
        example.setOrderByClause("script_version desc");
      } else {
        criteria.andScriptVersionEqualTo(Integer.parseInt(scriptVersion));
      }
      List<TransactionScriptEntity> entities = transactionScriptEntityMapper.selectByExample(example);
      if (CollectionUtils.isEmpty(entities)) {
        throw new CommonException(ServiceError.SCRIPT_NOT_FOUND, channel, scriptKey);
      }
      scriptText = entities.get(0).getScriptText();

      // 设置script到缓存
      RedisUtil.StringOps.set(redisKey, scriptText);
    }

    return scriptText;
  }

  @Override
  public Object invoke(String scriptText, String method, Object[] constructorArgs, Object[] args) {
    return GroovyUtil.invoke(scriptText, method, constructorArgs, args);
  }
}
