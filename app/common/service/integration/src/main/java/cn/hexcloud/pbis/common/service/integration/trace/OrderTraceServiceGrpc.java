package cn.hexcloud.pbis.common.service.integration.trace;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 * <pre>
 ** 订单追踪服务 
 * </pre>
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.40.1)",
    comments = "Source: OrderTraceService.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class OrderTraceServiceGrpc {

  private OrderTraceServiceGrpc() {}

  public static final String SERVICE_NAME = "cn.hexcloud.m82.log.pos.grpc.OrderTraceService";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq,
      com.google.protobuf.Empty> getTraceOrderMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "traceOrder",
      requestType = cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq.class,
      responseType = com.google.protobuf.Empty.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq,
      com.google.protobuf.Empty> getTraceOrderMethod() {
    io.grpc.MethodDescriptor<cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq, com.google.protobuf.Empty> getTraceOrderMethod;
    if ((getTraceOrderMethod = OrderTraceServiceGrpc.getTraceOrderMethod) == null) {
      synchronized (OrderTraceServiceGrpc.class) {
        if ((getTraceOrderMethod = OrderTraceServiceGrpc.getTraceOrderMethod) == null) {
          OrderTraceServiceGrpc.getTraceOrderMethod = getTraceOrderMethod =
              io.grpc.MethodDescriptor.<cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq, com.google.protobuf.Empty>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "traceOrder"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.google.protobuf.Empty.getDefaultInstance()))
              .setSchemaDescriptor(new OrderTraceServiceMethodDescriptorSupplier("traceOrder"))
              .build();
        }
      }
    }
    return getTraceOrderMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static OrderTraceServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<OrderTraceServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<OrderTraceServiceStub>() {
        @java.lang.Override
        public OrderTraceServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new OrderTraceServiceStub(channel, callOptions);
        }
      };
    return OrderTraceServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static OrderTraceServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<OrderTraceServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<OrderTraceServiceBlockingStub>() {
        @java.lang.Override
        public OrderTraceServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new OrderTraceServiceBlockingStub(channel, callOptions);
        }
      };
    return OrderTraceServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static OrderTraceServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<OrderTraceServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<OrderTraceServiceFutureStub>() {
        @java.lang.Override
        public OrderTraceServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new OrderTraceServiceFutureStub(channel, callOptions);
        }
      };
    return OrderTraceServiceFutureStub.newStub(factory, channel);
  }

  /**
   * <pre>
   ** 订单追踪服务 
   * </pre>
   */
  public static abstract class OrderTraceServiceImplBase implements io.grpc.BindableService {

    /**
     * <pre>
     ** 记录订单行为 
     * </pre>
     */
    public void traceOrder(cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq request,
        io.grpc.stub.StreamObserver<com.google.protobuf.Empty> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getTraceOrderMethod(), responseObserver);
    }

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getTraceOrderMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq,
                com.google.protobuf.Empty>(
                  this, METHODID_TRACE_ORDER)))
          .build();
    }
  }

  /**
   * <pre>
   ** 订单追踪服务 
   * </pre>
   */
  public static final class OrderTraceServiceStub extends io.grpc.stub.AbstractAsyncStub<OrderTraceServiceStub> {
    private OrderTraceServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected OrderTraceServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new OrderTraceServiceStub(channel, callOptions);
    }

    /**
     * <pre>
     ** 记录订单行为 
     * </pre>
     */
    public void traceOrder(cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq request,
        io.grpc.stub.StreamObserver<com.google.protobuf.Empty> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getTraceOrderMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * <pre>
   ** 订单追踪服务 
   * </pre>
   */
  public static final class OrderTraceServiceBlockingStub extends io.grpc.stub.AbstractBlockingStub<OrderTraceServiceBlockingStub> {
    private OrderTraceServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected OrderTraceServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new OrderTraceServiceBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     ** 记录订单行为 
     * </pre>
     */
    public com.google.protobuf.Empty traceOrder(cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getTraceOrderMethod(), getCallOptions(), request);
    }
  }

  /**
   * <pre>
   ** 订单追踪服务 
   * </pre>
   */
  public static final class OrderTraceServiceFutureStub extends io.grpc.stub.AbstractFutureStub<OrderTraceServiceFutureStub> {
    private OrderTraceServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected OrderTraceServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new OrderTraceServiceFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     ** 记录订单行为 
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.google.protobuf.Empty> traceOrder(
        cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getTraceOrderMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_TRACE_ORDER = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final OrderTraceServiceImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(OrderTraceServiceImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_TRACE_ORDER:
          serviceImpl.traceOrder((cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq) request,
              (io.grpc.stub.StreamObserver<com.google.protobuf.Empty>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class OrderTraceServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    OrderTraceServiceBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("OrderTraceService");
    }
  }

  private static final class OrderTraceServiceFileDescriptorSupplier
      extends OrderTraceServiceBaseDescriptorSupplier {
    OrderTraceServiceFileDescriptorSupplier() {}
  }

  private static final class OrderTraceServiceMethodDescriptorSupplier
      extends OrderTraceServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    OrderTraceServiceMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (OrderTraceServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new OrderTraceServiceFileDescriptorSupplier())
              .addMethod(getTraceOrderMethod())
              .build();
        }
      }
    }
    return result;
  }
}
