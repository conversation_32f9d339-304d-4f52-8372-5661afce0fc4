// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: OrderTraceService.proto

package cn.hexcloud.pbis.common.service.integration.trace;

public final class OrderTraceServiceProto {
  private OrderTraceServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface TraceOrderReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:cn.hexcloud.m82.log.pos.grpc.TraceOrderReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     ** 订单号 
     * </pre>
     *
     * <code>string orderNo = 1;</code>
     * @return The orderNo.
     */
    java.lang.String getOrderNo();
    /**
     * <pre>
     ** 订单号 
     * </pre>
     *
     * <code>string orderNo = 1;</code>
     * @return The bytes for orderNo.
     */
    com.google.protobuf.ByteString
        getOrderNoBytes();

    /**
     * <pre>
     ** 业务动作，如：pos-下单、pos-打印小票、printkit-打印小票、posservice-外卖接单 
     * </pre>
     *
     * <code>string action = 2;</code>
     * @return The action.
     */
    java.lang.String getAction();
    /**
     * <pre>
     ** 业务动作，如：pos-下单、pos-打印小票、printkit-打印小票、posservice-外卖接单 
     * </pre>
     *
     * <code>string action = 2;</code>
     * @return The bytes for action.
     */
    com.google.protobuf.ByteString
        getActionBytes();

    /**
     * <pre>
     ** 业务动作-状态，枚举：NORMAL, ABNORMAL 
     * </pre>
     *
     * <code>string actionStatus = 3;</code>
     * @return The actionStatus.
     */
    java.lang.String getActionStatus();
    /**
     * <pre>
     ** 业务动作-状态，枚举：NORMAL, ABNORMAL 
     * </pre>
     *
     * <code>string actionStatus = 3;</code>
     * @return The bytes for actionStatus.
     */
    com.google.protobuf.ByteString
        getActionStatusBytes();

    /**
     * <pre>
     ** 业务信息或者报错信息 
     * </pre>
     *
     * <code>string content = 4;</code>
     * @return The content.
     */
    java.lang.String getContent();
    /**
     * <pre>
     ** 业务信息或者报错信息 
     * </pre>
     *
     * <code>string content = 4;</code>
     * @return The bytes for content.
     */
    com.google.protobuf.ByteString
        getContentBytes();

    /**
     * <pre>
     ** 发生时间，毫秒级，13位数字，如：1640966400000 
     * </pre>
     *
     * <code>string startTimestamp = 5;</code>
     * @return The startTimestamp.
     */
    java.lang.String getStartTimestamp();
    /**
     * <pre>
     ** 发生时间，毫秒级，13位数字，如：1640966400000 
     * </pre>
     *
     * <code>string startTimestamp = 5;</code>
     * @return The bytes for startTimestamp.
     */
    com.google.protobuf.ByteString
        getStartTimestampBytes();

    /**
     * <code>int64 partnerId = 6;</code>
     * @return The partnerId.
     */
    long getPartnerId();

    /**
     * <code>string partnerName = 7;</code>
     * @return The partnerName.
     */
    java.lang.String getPartnerName();
    /**
     * <code>string partnerName = 7;</code>
     * @return The bytes for partnerName.
     */
    com.google.protobuf.ByteString
        getPartnerNameBytes();

    /**
     * <code>string storeId = 8;</code>
     * @return The storeId.
     */
    java.lang.String getStoreId();
    /**
     * <code>string storeId = 8;</code>
     * @return The bytes for storeId.
     */
    com.google.protobuf.ByteString
        getStoreIdBytes();

    /**
     * <code>string storeName = 9;</code>
     * @return The storeName.
     */
    java.lang.String getStoreName();
    /**
     * <code>string storeName = 9;</code>
     * @return The bytes for storeName.
     */
    com.google.protobuf.ByteString
        getStoreNameBytes();

    /**
     * <code>string clientType = 10;</code>
     * @return The clientType.
     */
    java.lang.String getClientType();
    /**
     * <code>string clientType = 10;</code>
     * @return The bytes for clientType.
     */
    com.google.protobuf.ByteString
        getClientTypeBytes();

    /**
     * <code>string takeNo = 11;</code>
     * @return The takeNo.
     */
    java.lang.String getTakeNo();
    /**
     * <code>string takeNo = 11;</code>
     * @return The bytes for takeNo.
     */
    com.google.protobuf.ByteString
        getTakeNoBytes();
  }
  /**
   * Protobuf type {@code cn.hexcloud.m82.log.pos.grpc.TraceOrderReq}
   */
  public static final class TraceOrderReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:cn.hexcloud.m82.log.pos.grpc.TraceOrderReq)
      TraceOrderReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TraceOrderReq.newBuilder() to construct.
    private TraceOrderReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TraceOrderReq() {
      orderNo_ = "";
      action_ = "";
      actionStatus_ = "";
      content_ = "";
      startTimestamp_ = "";
      partnerName_ = "";
      storeId_ = "";
      storeName_ = "";
      clientType_ = "";
      takeNo_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TraceOrderReq();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private TraceOrderReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              orderNo_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              action_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              actionStatus_ = s;
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              content_ = s;
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();

              startTimestamp_ = s;
              break;
            }
            case 48: {

              partnerId_ = input.readInt64();
              break;
            }
            case 58: {
              java.lang.String s = input.readStringRequireUtf8();

              partnerName_ = s;
              break;
            }
            case 66: {
              java.lang.String s = input.readStringRequireUtf8();

              storeId_ = s;
              break;
            }
            case 74: {
              java.lang.String s = input.readStringRequireUtf8();

              storeName_ = s;
              break;
            }
            case 82: {
              java.lang.String s = input.readStringRequireUtf8();

              clientType_ = s;
              break;
            }
            case 90: {
              java.lang.String s = input.readStringRequireUtf8();

              takeNo_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.internal_static_cn_hexcloud_m82_log_pos_grpc_TraceOrderReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.internal_static_cn_hexcloud_m82_log_pos_grpc_TraceOrderReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq.class, cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq.Builder.class);
    }

    public static final int ORDERNO_FIELD_NUMBER = 1;
    private volatile java.lang.Object orderNo_;
    /**
     * <pre>
     ** 订单号 
     * </pre>
     *
     * <code>string orderNo = 1;</code>
     * @return The orderNo.
     */
    @java.lang.Override
    public java.lang.String getOrderNo() {
      java.lang.Object ref = orderNo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        orderNo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 订单号 
     * </pre>
     *
     * <code>string orderNo = 1;</code>
     * @return The bytes for orderNo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOrderNoBytes() {
      java.lang.Object ref = orderNo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        orderNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ACTION_FIELD_NUMBER = 2;
    private volatile java.lang.Object action_;
    /**
     * <pre>
     ** 业务动作，如：pos-下单、pos-打印小票、printkit-打印小票、posservice-外卖接单 
     * </pre>
     *
     * <code>string action = 2;</code>
     * @return The action.
     */
    @java.lang.Override
    public java.lang.String getAction() {
      java.lang.Object ref = action_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        action_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 业务动作，如：pos-下单、pos-打印小票、printkit-打印小票、posservice-外卖接单 
     * </pre>
     *
     * <code>string action = 2;</code>
     * @return The bytes for action.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getActionBytes() {
      java.lang.Object ref = action_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        action_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ACTIONSTATUS_FIELD_NUMBER = 3;
    private volatile java.lang.Object actionStatus_;
    /**
     * <pre>
     ** 业务动作-状态，枚举：NORMAL, ABNORMAL 
     * </pre>
     *
     * <code>string actionStatus = 3;</code>
     * @return The actionStatus.
     */
    @java.lang.Override
    public java.lang.String getActionStatus() {
      java.lang.Object ref = actionStatus_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        actionStatus_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 业务动作-状态，枚举：NORMAL, ABNORMAL 
     * </pre>
     *
     * <code>string actionStatus = 3;</code>
     * @return The bytes for actionStatus.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getActionStatusBytes() {
      java.lang.Object ref = actionStatus_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        actionStatus_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CONTENT_FIELD_NUMBER = 4;
    private volatile java.lang.Object content_;
    /**
     * <pre>
     ** 业务信息或者报错信息 
     * </pre>
     *
     * <code>string content = 4;</code>
     * @return The content.
     */
    @java.lang.Override
    public java.lang.String getContent() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        content_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 业务信息或者报错信息 
     * </pre>
     *
     * <code>string content = 4;</code>
     * @return The bytes for content.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getContentBytes() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        content_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int STARTTIMESTAMP_FIELD_NUMBER = 5;
    private volatile java.lang.Object startTimestamp_;
    /**
     * <pre>
     ** 发生时间，毫秒级，13位数字，如：1640966400000 
     * </pre>
     *
     * <code>string startTimestamp = 5;</code>
     * @return The startTimestamp.
     */
    @java.lang.Override
    public java.lang.String getStartTimestamp() {
      java.lang.Object ref = startTimestamp_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        startTimestamp_ = s;
        return s;
      }
    }
    /**
     * <pre>
     ** 发生时间，毫秒级，13位数字，如：1640966400000 
     * </pre>
     *
     * <code>string startTimestamp = 5;</code>
     * @return The bytes for startTimestamp.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getStartTimestampBytes() {
      java.lang.Object ref = startTimestamp_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        startTimestamp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PARTNERID_FIELD_NUMBER = 6;
    private long partnerId_;
    /**
     * <code>int64 partnerId = 6;</code>
     * @return The partnerId.
     */
    @java.lang.Override
    public long getPartnerId() {
      return partnerId_;
    }

    public static final int PARTNERNAME_FIELD_NUMBER = 7;
    private volatile java.lang.Object partnerName_;
    /**
     * <code>string partnerName = 7;</code>
     * @return The partnerName.
     */
    @java.lang.Override
    public java.lang.String getPartnerName() {
      java.lang.Object ref = partnerName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        partnerName_ = s;
        return s;
      }
    }
    /**
     * <code>string partnerName = 7;</code>
     * @return The bytes for partnerName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPartnerNameBytes() {
      java.lang.Object ref = partnerName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        partnerName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int STOREID_FIELD_NUMBER = 8;
    private volatile java.lang.Object storeId_;
    /**
     * <code>string storeId = 8;</code>
     * @return The storeId.
     */
    @java.lang.Override
    public java.lang.String getStoreId() {
      java.lang.Object ref = storeId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        storeId_ = s;
        return s;
      }
    }
    /**
     * <code>string storeId = 8;</code>
     * @return The bytes for storeId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getStoreIdBytes() {
      java.lang.Object ref = storeId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        storeId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int STORENAME_FIELD_NUMBER = 9;
    private volatile java.lang.Object storeName_;
    /**
     * <code>string storeName = 9;</code>
     * @return The storeName.
     */
    @java.lang.Override
    public java.lang.String getStoreName() {
      java.lang.Object ref = storeName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        storeName_ = s;
        return s;
      }
    }
    /**
     * <code>string storeName = 9;</code>
     * @return The bytes for storeName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getStoreNameBytes() {
      java.lang.Object ref = storeName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        storeName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CLIENTTYPE_FIELD_NUMBER = 10;
    private volatile java.lang.Object clientType_;
    /**
     * <code>string clientType = 10;</code>
     * @return The clientType.
     */
    @java.lang.Override
    public java.lang.String getClientType() {
      java.lang.Object ref = clientType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        clientType_ = s;
        return s;
      }
    }
    /**
     * <code>string clientType = 10;</code>
     * @return The bytes for clientType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClientTypeBytes() {
      java.lang.Object ref = clientType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clientType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TAKENO_FIELD_NUMBER = 11;
    private volatile java.lang.Object takeNo_;
    /**
     * <code>string takeNo = 11;</code>
     * @return The takeNo.
     */
    @java.lang.Override
    public java.lang.String getTakeNo() {
      java.lang.Object ref = takeNo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        takeNo_ = s;
        return s;
      }
    }
    /**
     * <code>string takeNo = 11;</code>
     * @return The bytes for takeNo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTakeNoBytes() {
      java.lang.Object ref = takeNo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        takeNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getOrderNoBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, orderNo_);
      }
      if (!getActionBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, action_);
      }
      if (!getActionStatusBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, actionStatus_);
      }
      if (!getContentBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, content_);
      }
      if (!getStartTimestampBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, startTimestamp_);
      }
      if (partnerId_ != 0L) {
        output.writeInt64(6, partnerId_);
      }
      if (!getPartnerNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, partnerName_);
      }
      if (!getStoreIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, storeId_);
      }
      if (!getStoreNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, storeName_);
      }
      if (!getClientTypeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, clientType_);
      }
      if (!getTakeNoBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, takeNo_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getOrderNoBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, orderNo_);
      }
      if (!getActionBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, action_);
      }
      if (!getActionStatusBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, actionStatus_);
      }
      if (!getContentBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, content_);
      }
      if (!getStartTimestampBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, startTimestamp_);
      }
      if (partnerId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, partnerId_);
      }
      if (!getPartnerNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, partnerName_);
      }
      if (!getStoreIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, storeId_);
      }
      if (!getStoreNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, storeName_);
      }
      if (!getClientTypeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, clientType_);
      }
      if (!getTakeNoBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, takeNo_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq)) {
        return super.equals(obj);
      }
      cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq other = (cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq) obj;

      if (!getOrderNo()
          .equals(other.getOrderNo())) return false;
      if (!getAction()
          .equals(other.getAction())) return false;
      if (!getActionStatus()
          .equals(other.getActionStatus())) return false;
      if (!getContent()
          .equals(other.getContent())) return false;
      if (!getStartTimestamp()
          .equals(other.getStartTimestamp())) return false;
      if (getPartnerId()
          != other.getPartnerId()) return false;
      if (!getPartnerName()
          .equals(other.getPartnerName())) return false;
      if (!getStoreId()
          .equals(other.getStoreId())) return false;
      if (!getStoreName()
          .equals(other.getStoreName())) return false;
      if (!getClientType()
          .equals(other.getClientType())) return false;
      if (!getTakeNo()
          .equals(other.getTakeNo())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ORDERNO_FIELD_NUMBER;
      hash = (53 * hash) + getOrderNo().hashCode();
      hash = (37 * hash) + ACTION_FIELD_NUMBER;
      hash = (53 * hash) + getAction().hashCode();
      hash = (37 * hash) + ACTIONSTATUS_FIELD_NUMBER;
      hash = (53 * hash) + getActionStatus().hashCode();
      hash = (37 * hash) + CONTENT_FIELD_NUMBER;
      hash = (53 * hash) + getContent().hashCode();
      hash = (37 * hash) + STARTTIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + getStartTimestamp().hashCode();
      hash = (37 * hash) + PARTNERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getPartnerId());
      hash = (37 * hash) + PARTNERNAME_FIELD_NUMBER;
      hash = (53 * hash) + getPartnerName().hashCode();
      hash = (37 * hash) + STOREID_FIELD_NUMBER;
      hash = (53 * hash) + getStoreId().hashCode();
      hash = (37 * hash) + STORENAME_FIELD_NUMBER;
      hash = (53 * hash) + getStoreName().hashCode();
      hash = (37 * hash) + CLIENTTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getClientType().hashCode();
      hash = (37 * hash) + TAKENO_FIELD_NUMBER;
      hash = (53 * hash) + getTakeNo().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code cn.hexcloud.m82.log.pos.grpc.TraceOrderReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:cn.hexcloud.m82.log.pos.grpc.TraceOrderReq)
        cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.internal_static_cn_hexcloud_m82_log_pos_grpc_TraceOrderReq_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.internal_static_cn_hexcloud_m82_log_pos_grpc_TraceOrderReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq.class, cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq.Builder.class);
      }

      // Construct using cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        orderNo_ = "";

        action_ = "";

        actionStatus_ = "";

        content_ = "";

        startTimestamp_ = "";

        partnerId_ = 0L;

        partnerName_ = "";

        storeId_ = "";

        storeName_ = "";

        clientType_ = "";

        takeNo_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.internal_static_cn_hexcloud_m82_log_pos_grpc_TraceOrderReq_descriptor;
      }

      @java.lang.Override
      public cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq getDefaultInstanceForType() {
        return cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq.getDefaultInstance();
      }

      @java.lang.Override
      public cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq build() {
        cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq buildPartial() {
        cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq result = new cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq(this);
        result.orderNo_ = orderNo_;
        result.action_ = action_;
        result.actionStatus_ = actionStatus_;
        result.content_ = content_;
        result.startTimestamp_ = startTimestamp_;
        result.partnerId_ = partnerId_;
        result.partnerName_ = partnerName_;
        result.storeId_ = storeId_;
        result.storeName_ = storeName_;
        result.clientType_ = clientType_;
        result.takeNo_ = takeNo_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq) {
          return mergeFrom((cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq other) {
        if (other == cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq.getDefaultInstance()) return this;
        if (!other.getOrderNo().isEmpty()) {
          orderNo_ = other.orderNo_;
          onChanged();
        }
        if (!other.getAction().isEmpty()) {
          action_ = other.action_;
          onChanged();
        }
        if (!other.getActionStatus().isEmpty()) {
          actionStatus_ = other.actionStatus_;
          onChanged();
        }
        if (!other.getContent().isEmpty()) {
          content_ = other.content_;
          onChanged();
        }
        if (!other.getStartTimestamp().isEmpty()) {
          startTimestamp_ = other.startTimestamp_;
          onChanged();
        }
        if (other.getPartnerId() != 0L) {
          setPartnerId(other.getPartnerId());
        }
        if (!other.getPartnerName().isEmpty()) {
          partnerName_ = other.partnerName_;
          onChanged();
        }
        if (!other.getStoreId().isEmpty()) {
          storeId_ = other.storeId_;
          onChanged();
        }
        if (!other.getStoreName().isEmpty()) {
          storeName_ = other.storeName_;
          onChanged();
        }
        if (!other.getClientType().isEmpty()) {
          clientType_ = other.clientType_;
          onChanged();
        }
        if (!other.getTakeNo().isEmpty()) {
          takeNo_ = other.takeNo_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object orderNo_ = "";
      /**
       * <pre>
       ** 订单号 
       * </pre>
       *
       * <code>string orderNo = 1;</code>
       * @return The orderNo.
       */
      public java.lang.String getOrderNo() {
        java.lang.Object ref = orderNo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          orderNo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 订单号 
       * </pre>
       *
       * <code>string orderNo = 1;</code>
       * @return The bytes for orderNo.
       */
      public com.google.protobuf.ByteString
          getOrderNoBytes() {
        java.lang.Object ref = orderNo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          orderNo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 订单号 
       * </pre>
       *
       * <code>string orderNo = 1;</code>
       * @param value The orderNo to set.
       * @return This builder for chaining.
       */
      public Builder setOrderNo(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        orderNo_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 订单号 
       * </pre>
       *
       * <code>string orderNo = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOrderNo() {
        
        orderNo_ = getDefaultInstance().getOrderNo();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 订单号 
       * </pre>
       *
       * <code>string orderNo = 1;</code>
       * @param value The bytes for orderNo to set.
       * @return This builder for chaining.
       */
      public Builder setOrderNoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        orderNo_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object action_ = "";
      /**
       * <pre>
       ** 业务动作，如：pos-下单、pos-打印小票、printkit-打印小票、posservice-外卖接单 
       * </pre>
       *
       * <code>string action = 2;</code>
       * @return The action.
       */
      public java.lang.String getAction() {
        java.lang.Object ref = action_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          action_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 业务动作，如：pos-下单、pos-打印小票、printkit-打印小票、posservice-外卖接单 
       * </pre>
       *
       * <code>string action = 2;</code>
       * @return The bytes for action.
       */
      public com.google.protobuf.ByteString
          getActionBytes() {
        java.lang.Object ref = action_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          action_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 业务动作，如：pos-下单、pos-打印小票、printkit-打印小票、posservice-外卖接单 
       * </pre>
       *
       * <code>string action = 2;</code>
       * @param value The action to set.
       * @return This builder for chaining.
       */
      public Builder setAction(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        action_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 业务动作，如：pos-下单、pos-打印小票、printkit-打印小票、posservice-外卖接单 
       * </pre>
       *
       * <code>string action = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAction() {
        
        action_ = getDefaultInstance().getAction();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 业务动作，如：pos-下单、pos-打印小票、printkit-打印小票、posservice-外卖接单 
       * </pre>
       *
       * <code>string action = 2;</code>
       * @param value The bytes for action to set.
       * @return This builder for chaining.
       */
      public Builder setActionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        action_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object actionStatus_ = "";
      /**
       * <pre>
       ** 业务动作-状态，枚举：NORMAL, ABNORMAL 
       * </pre>
       *
       * <code>string actionStatus = 3;</code>
       * @return The actionStatus.
       */
      public java.lang.String getActionStatus() {
        java.lang.Object ref = actionStatus_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          actionStatus_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 业务动作-状态，枚举：NORMAL, ABNORMAL 
       * </pre>
       *
       * <code>string actionStatus = 3;</code>
       * @return The bytes for actionStatus.
       */
      public com.google.protobuf.ByteString
          getActionStatusBytes() {
        java.lang.Object ref = actionStatus_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          actionStatus_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 业务动作-状态，枚举：NORMAL, ABNORMAL 
       * </pre>
       *
       * <code>string actionStatus = 3;</code>
       * @param value The actionStatus to set.
       * @return This builder for chaining.
       */
      public Builder setActionStatus(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        actionStatus_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 业务动作-状态，枚举：NORMAL, ABNORMAL 
       * </pre>
       *
       * <code>string actionStatus = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearActionStatus() {
        
        actionStatus_ = getDefaultInstance().getActionStatus();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 业务动作-状态，枚举：NORMAL, ABNORMAL 
       * </pre>
       *
       * <code>string actionStatus = 3;</code>
       * @param value The bytes for actionStatus to set.
       * @return This builder for chaining.
       */
      public Builder setActionStatusBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        actionStatus_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object content_ = "";
      /**
       * <pre>
       ** 业务信息或者报错信息 
       * </pre>
       *
       * <code>string content = 4;</code>
       * @return The content.
       */
      public java.lang.String getContent() {
        java.lang.Object ref = content_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          content_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 业务信息或者报错信息 
       * </pre>
       *
       * <code>string content = 4;</code>
       * @return The bytes for content.
       */
      public com.google.protobuf.ByteString
          getContentBytes() {
        java.lang.Object ref = content_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          content_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 业务信息或者报错信息 
       * </pre>
       *
       * <code>string content = 4;</code>
       * @param value The content to set.
       * @return This builder for chaining.
       */
      public Builder setContent(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        content_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 业务信息或者报错信息 
       * </pre>
       *
       * <code>string content = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearContent() {
        
        content_ = getDefaultInstance().getContent();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 业务信息或者报错信息 
       * </pre>
       *
       * <code>string content = 4;</code>
       * @param value The bytes for content to set.
       * @return This builder for chaining.
       */
      public Builder setContentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        content_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object startTimestamp_ = "";
      /**
       * <pre>
       ** 发生时间，毫秒级，13位数字，如：1640966400000 
       * </pre>
       *
       * <code>string startTimestamp = 5;</code>
       * @return The startTimestamp.
       */
      public java.lang.String getStartTimestamp() {
        java.lang.Object ref = startTimestamp_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          startTimestamp_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       ** 发生时间，毫秒级，13位数字，如：1640966400000 
       * </pre>
       *
       * <code>string startTimestamp = 5;</code>
       * @return The bytes for startTimestamp.
       */
      public com.google.protobuf.ByteString
          getStartTimestampBytes() {
        java.lang.Object ref = startTimestamp_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          startTimestamp_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       ** 发生时间，毫秒级，13位数字，如：1640966400000 
       * </pre>
       *
       * <code>string startTimestamp = 5;</code>
       * @param value The startTimestamp to set.
       * @return This builder for chaining.
       */
      public Builder setStartTimestamp(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        startTimestamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 发生时间，毫秒级，13位数字，如：1640966400000 
       * </pre>
       *
       * <code>string startTimestamp = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearStartTimestamp() {
        
        startTimestamp_ = getDefaultInstance().getStartTimestamp();
        onChanged();
        return this;
      }
      /**
       * <pre>
       ** 发生时间，毫秒级，13位数字，如：1640966400000 
       * </pre>
       *
       * <code>string startTimestamp = 5;</code>
       * @param value The bytes for startTimestamp to set.
       * @return This builder for chaining.
       */
      public Builder setStartTimestampBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        startTimestamp_ = value;
        onChanged();
        return this;
      }

      private long partnerId_ ;
      /**
       * <code>int64 partnerId = 6;</code>
       * @return The partnerId.
       */
      @java.lang.Override
      public long getPartnerId() {
        return partnerId_;
      }
      /**
       * <code>int64 partnerId = 6;</code>
       * @param value The partnerId to set.
       * @return This builder for chaining.
       */
      public Builder setPartnerId(long value) {
        
        partnerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 partnerId = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartnerId() {
        
        partnerId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object partnerName_ = "";
      /**
       * <code>string partnerName = 7;</code>
       * @return The partnerName.
       */
      public java.lang.String getPartnerName() {
        java.lang.Object ref = partnerName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          partnerName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string partnerName = 7;</code>
       * @return The bytes for partnerName.
       */
      public com.google.protobuf.ByteString
          getPartnerNameBytes() {
        java.lang.Object ref = partnerName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          partnerName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string partnerName = 7;</code>
       * @param value The partnerName to set.
       * @return This builder for chaining.
       */
      public Builder setPartnerName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        partnerName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string partnerName = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartnerName() {
        
        partnerName_ = getDefaultInstance().getPartnerName();
        onChanged();
        return this;
      }
      /**
       * <code>string partnerName = 7;</code>
       * @param value The bytes for partnerName to set.
       * @return This builder for chaining.
       */
      public Builder setPartnerNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        partnerName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object storeId_ = "";
      /**
       * <code>string storeId = 8;</code>
       * @return The storeId.
       */
      public java.lang.String getStoreId() {
        java.lang.Object ref = storeId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          storeId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string storeId = 8;</code>
       * @return The bytes for storeId.
       */
      public com.google.protobuf.ByteString
          getStoreIdBytes() {
        java.lang.Object ref = storeId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          storeId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string storeId = 8;</code>
       * @param value The storeId to set.
       * @return This builder for chaining.
       */
      public Builder setStoreId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        storeId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string storeId = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearStoreId() {
        
        storeId_ = getDefaultInstance().getStoreId();
        onChanged();
        return this;
      }
      /**
       * <code>string storeId = 8;</code>
       * @param value The bytes for storeId to set.
       * @return This builder for chaining.
       */
      public Builder setStoreIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        storeId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object storeName_ = "";
      /**
       * <code>string storeName = 9;</code>
       * @return The storeName.
       */
      public java.lang.String getStoreName() {
        java.lang.Object ref = storeName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          storeName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string storeName = 9;</code>
       * @return The bytes for storeName.
       */
      public com.google.protobuf.ByteString
          getStoreNameBytes() {
        java.lang.Object ref = storeName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          storeName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string storeName = 9;</code>
       * @param value The storeName to set.
       * @return This builder for chaining.
       */
      public Builder setStoreName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        storeName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string storeName = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearStoreName() {
        
        storeName_ = getDefaultInstance().getStoreName();
        onChanged();
        return this;
      }
      /**
       * <code>string storeName = 9;</code>
       * @param value The bytes for storeName to set.
       * @return This builder for chaining.
       */
      public Builder setStoreNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        storeName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object clientType_ = "";
      /**
       * <code>string clientType = 10;</code>
       * @return The clientType.
       */
      public java.lang.String getClientType() {
        java.lang.Object ref = clientType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          clientType_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string clientType = 10;</code>
       * @return The bytes for clientType.
       */
      public com.google.protobuf.ByteString
          getClientTypeBytes() {
        java.lang.Object ref = clientType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clientType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string clientType = 10;</code>
       * @param value The clientType to set.
       * @return This builder for chaining.
       */
      public Builder setClientType(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        clientType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string clientType = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearClientType() {
        
        clientType_ = getDefaultInstance().getClientType();
        onChanged();
        return this;
      }
      /**
       * <code>string clientType = 10;</code>
       * @param value The bytes for clientType to set.
       * @return This builder for chaining.
       */
      public Builder setClientTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        clientType_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object takeNo_ = "";
      /**
       * <code>string takeNo = 11;</code>
       * @return The takeNo.
       */
      public java.lang.String getTakeNo() {
        java.lang.Object ref = takeNo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          takeNo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string takeNo = 11;</code>
       * @return The bytes for takeNo.
       */
      public com.google.protobuf.ByteString
          getTakeNoBytes() {
        java.lang.Object ref = takeNo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          takeNo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string takeNo = 11;</code>
       * @param value The takeNo to set.
       * @return This builder for chaining.
       */
      public Builder setTakeNo(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        takeNo_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string takeNo = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearTakeNo() {
        
        takeNo_ = getDefaultInstance().getTakeNo();
        onChanged();
        return this;
      }
      /**
       * <code>string takeNo = 11;</code>
       * @param value The bytes for takeNo to set.
       * @return This builder for chaining.
       */
      public Builder setTakeNoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        takeNo_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:cn.hexcloud.m82.log.pos.grpc.TraceOrderReq)
    }

    // @@protoc_insertion_point(class_scope:cn.hexcloud.m82.log.pos.grpc.TraceOrderReq)
    private static final cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq();
    }

    public static cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TraceOrderReq>
        PARSER = new com.google.protobuf.AbstractParser<TraceOrderReq>() {
      @java.lang.Override
      public TraceOrderReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new TraceOrderReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<TraceOrderReq> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TraceOrderReq> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public cn.hexcloud.pbis.common.service.integration.trace.OrderTraceServiceProto.TraceOrderReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_cn_hexcloud_m82_log_pos_grpc_TraceOrderReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_cn_hexcloud_m82_log_pos_grpc_TraceOrderReq_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\027OrderTraceService.proto\022\034cn.hexcloud.m" +
      "82.log.pos.grpc\032\033google/protobuf/empty.p" +
      "roto\032\034google/api/annotations.proto\"\337\001\n\rT" +
      "raceOrderReq\022\017\n\007orderNo\030\001 \001(\t\022\016\n\006action\030" +
      "\002 \001(\t\022\024\n\014actionStatus\030\003 \001(\t\022\017\n\007content\030\004" +
      " \001(\t\022\026\n\016startTimestamp\030\005 \001(\t\022\021\n\tpartnerI" +
      "d\030\006 \001(\003\022\023\n\013partnerName\030\007 \001(\t\022\017\n\007storeId\030" +
      "\010 \001(\t\022\021\n\tstoreName\030\t \001(\t\022\022\n\nclientType\030\n" +
      " \001(\t\022\016\n\006takeNo\030\013 \001(\t2h\n\021OrderTraceServic" +
      "e\022S\n\ntraceOrder\022+.cn.hexcloud.m82.log.po" +
      "s.grpc.TraceOrderReq\032\026.google.protobuf.E" +
      "mpty\"\000BM\n1cn.hexcloud.pbis.common.servic" +
      "e.integration.traceB\026OrderTraceServicePr" +
      "otoP\000b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.EmptyProto.getDescriptor(),
          com.google.api.AnnotationsProto.getDescriptor(),
        });
    internal_static_cn_hexcloud_m82_log_pos_grpc_TraceOrderReq_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_cn_hexcloud_m82_log_pos_grpc_TraceOrderReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_cn_hexcloud_m82_log_pos_grpc_TraceOrderReq_descriptor,
        new java.lang.String[] { "OrderNo", "Action", "ActionStatus", "Content", "StartTimestamp", "PartnerId", "PartnerName", "StoreId", "StoreName", "ClientType", "TakeNo", });
    com.google.protobuf.EmptyProto.getDescriptor();
    com.google.api.AnnotationsProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
