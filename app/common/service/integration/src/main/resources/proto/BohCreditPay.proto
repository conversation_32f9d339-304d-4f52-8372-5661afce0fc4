syntax = "proto3";
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
package credit_pay;

option java_multiple_files = true;
option java_package = "cn.hexcloud.pbis.common.service.integration.boh";
option java_outer_classname = "BohCreditPayProto";


service CreditPay {
  // 健康检查
  rpc Ping(PingRequest) returns(PingResponse);

  // 创建信用付
  rpc CreateCreditPay(CreateCreditPayRequest) returns(CommonResponse){
    option (google.api.http) = {
      post: "/api/v1/credit-pay/create-credit-pay"
    };
  };

  // 修改信用付
  rpc UpdateCreditPay(UpdateCreditPayRequest) returns(CommonResponse){
    option (google.api.http) = {
      post: "/api/v1/credit-pay/update-credit-pay"
    };
  };

  // 关闭/开启信用付
  rpc CloseCreditPay(CloseCreditPayRequest) returns(CommonResponse){
    option (google.api.http) = {
      post: "/api/v1/credit-pay/close-credit-pay"
    };
  };

  // 信用付账户列表
  rpc GetCreditPayList(GetCreditPayListRequest) returns(GetCreditPayListResponse){
    option (google.api.http) = {
      get: "/api/v1/credit-pay/get-credit-pay-list"
    };
  };

  // 信用付账户详情
  rpc GetCreditPayDetail(GetCreditPayDetailRequest) returns(GetCreditPayDetailResponse){
    option (google.api.http) = {
      get: "/api/v1/credit-pay/get-credit-pay-detail"
    };
  };

  // 创建支付关系
  rpc CreatePayRelation(CreatePayRelationRequest) returns(CommonResponse){
    option (google.api.http) = {
      post: "/api/v1/credit-pay/create-pay-relation"
    };
  };

  // 获取支付关系列表
  rpc GetPayRelationList(GetPayRelationListRequest) returns(GetPayRelationListResponse){
    option (google.api.http) = {
      post: "/api/v1/credit-pay/get-pay-relation-list"
    };
  };

  // 获取支付关系详情
  rpc GetPayRelationDetail(GetPayRelationDetailRequest) returns(GetPayRelationDetailResponse){
    option (google.api.http) = {
      post: "/api/v1/credit-pay/get-pay-relation-detail"
    };
  };

  // 移除支付关系
  rpc DeletePayRelation(DeletePayRelationRequest) returns(CommonResponse){
    option (google.api.http) = {
      post: "/api/v1/credit-pay/delete-pay-relation"
    };
  };

  // 关闭/开启支付关系
  rpc ChangePayRelationStatus(ChangePayRelationStatusRequest) returns(CommonResponse){
    option (google.api.http) = {
      post: "/api/v1/credit-pay/change-pay-relation-status"
    };
  };

  // 获取日志数据
  rpc GetLogList(GetLogListRequest) returns(GetLogListResponse){
    option (google.api.http) = {
      post: "/api/v1/credit-pay/get-log-list"
    };
  };

  // 支付回调 - 微信等线上支付
  rpc PayNotify(PayNotifyRequest) returns(CommonResponse) {
    option (google.api.http) = {
      post: "/api/v1/credit-pay/pay-notify"
      body: "*"
    };
  }

  // 支付结果查询
  rpc QueryPayStatus(QueryPayStatusRequest) returns(QueryPayStatusResponse) {
    option (google.api.http) = {
      post: "/api/v1/credit-pay/query-pay-status"
      body: "*"
    };
  }

  // 获取支付参数
  rpc GetPayParams(GetPayParamsRequest) returns(GetPayParamsResponse) {
    option (google.api.http) = {
      post: "/api/v1/credit-pay/get-pay-params"
      body: "*"
    };
  }

  // 强制使用信用付支付 - 仅提供后台使用
  rpc ForcePay(ForcePayRequest) returns(CommonResponse) {
    option (google.api.http) = {
      post: "/api/v1/credit-pay/force-pay"
      body: "*"
    };
  }

  // 获取支付方式列表
  rpc GetChannels(GetChannelsRequest) returns(GetChannelsResponse) {
    option (google.api.http) = {
      post: "/api/v1/credit-pay/get-channels"
      body: "*"
    };
  }

  // 获取代金券列表
  rpc GetVouchers(GetVouchersRequest) returns(GetVouchersResponse) {
    option (google.api.http) = {
      post: "/api/v1/credit-pay/get-vouchers"
      body: "*"
    };
  }

  // 信用付支付
  rpc Pay(PayRequest) returns(CommonResponse) {
    option (google.api.http) = {
      post: "/api/v1/credit-pay/pay"
      body: "*"
    };
  }

  // 代金券支付
  rpc VoucherPay(VoucherPayRequest) returns(CommonResponse) {
    option (google.api.http) = {
      post: "/api/v1/credit-pay/voucher-pay"
      body: "*"
    };
  }

  // 退款 - 不支持原路退, 代金券退代金券, 其他退信用付
  rpc Refund(RefundRequest) returns(CommonResponse) {
    option (google.api.http) = {
      post: "/api/v1/credit-pay/refund"
      body: "*"
    };
  }

  // 获取交易流水
  rpc GetTransactionLogList(GetTransactionLogListRequest) returns(GetTransactionLogListResponse) {
    option (google.api.http) = {
      post: "/api/v1/credit-pay/get-transaction-log-list"
      body: "*"
    };
  }
}

message PingRequest {
  string ping = 1;
}

message PingResponse {
  string pong = 1;
}

// 创建信用付
message CreateCreditPayRequest {
  // 授信主体公司id, 必传
  uint64 company_id = 1;
  // 授信方式枚举, 必传
  CreditWayType credit_way = 2;
  // 被授信方id, 必传
  repeated uint64 branch_ids = 3;
  // 授信额度, 单位分, 必传
  int64 limit = 4;
  // 是否允许超额支付, 必传
  bool is_over = 5;
  // 超额支付额度上限, 单位分, is_over为true时必传
  int64 max_over_limit = 6;
  // 备注, 必传
  string remark = 7;
  // 状态
  CreditStatus status = 8;
}

// 编辑信用付
message UpdateCreditPayRequest {
  // 信用付id, 必传
  uint64 credit_pay_id = 1;
  // 增加授信额度, 单位分, 如果要变更额度, increase/reduce 必须二选一, 否则都为0
  uint64 increase = 2;
  // 减少授信额度, 单位分, 如果要变更额度, increase/reduce 必须二选一, 否则都为0
  uint64 reduce = 3;
  // 是否允许超额支付, 必传
  bool is_over = 4;
  // 超额支付上限, 必传
  int64 max_over_limit = 5;
  // 备注, 可选
  string remark = 6;
  // 状态
  CreditStatus status = 7;
}

// 关闭/开启信用付
message CloseCreditPayRequest {
  // 信用付id, 必传
  repeated uint64 credit_pay_ids = 1;
  // 状态, 必传
  CreditStatus status = 2;
}

// 获取信用付列表
message GetCreditPayListRequest {
  // 授信方式, 必传
  CreditWayType credit_way = 1;
  // 被授信方id, 可选
  repeated uint64 branch_ids = 2;
  // 授信主体公司id, 可选
  repeated uint64 company_ids = 3;
  // 状态, 可选
  CreditStatus status = 4;
  // 分页
  Page page = 5;
}

// 信用付列表响应
message GetCreditPayListResponse {
  // 列表数据
  repeated CreditPayList rows = 1;
  // 总页数
  uint64 total = 2;
}

// 信用付列表数据
message CreditPayList {
  // 信用付id
  uint64 credit_pay_id = 1;
  // 被授信方组织id, 目前是门店/加盟商
  uint64 branch_id = 2;
  // 被授信方组织code
  string branch_code = 3;
  // 被授信方名称
  string branch_name = 4;
  // 授信主体公司id
  uint64 company_id = 5;
  // 授信主体名称
  string company_name = 6;
  // 可用额度
  int64 limit = 7;
  // 是否允许超额支付
  bool is_over = 8;
  // 超额支付额度
  int64 max_over_limit = 9;
  // 更新人id
  uint64  updated_by = 10;
  // 创建人id
  uint64  created_by = 11;
  // 创建人
  string created_name = 12;
  // 更新人;
  string updated_name = 13;
  // 状态
  CreditStatus status = 14;
  // 创建时间
  google.protobuf.Timestamp created = 15;
  // 更新时间
  google.protobuf.Timestamp updated = 16;
}

// 获取信用付详情
message GetCreditPayDetailRequest {
  // 信用付id
  uint64 credit_pay_id = 1;
}

// 信用付详情
message GetCreditPayDetailResponse {
  // 信用付id
  uint64 credit_pay_id = 1;
  // 被授信方组织id, 目前是门店/加盟商
  uint64 branch_id = 2;
  // 被授信方组织code
  string branch_code = 3;
  // 被授信方名称
  string branch_name = 4;
  // 授信主体公司id
  uint64 company_id = 5;
  // 授信主体名称
  string company_name = 6;
  // 当前额度
  int64 limit = 7;
  // 是否允许超额支付
  bool is_over = 8;
  // 超额支付额度
  int64 max_over_limit = 9;
  // 备注
  string remark = 10;
  // 更新人
  uint64  updated_by = 11;
  // 创建人
  uint64  created_by = 12;
  // 状态
  CreditStatus status = 13;
  // 授信方式枚举
  CreditWayType credit_way = 14;
  // 创建时间
  google.protobuf.Timestamp created = 15;
  // 更新时间
  google.protobuf.Timestamp updated = 16;
}

// 创建支付关系
message CreatePayRelationRequest {
  // 支付公司id
  uint64 CompanyId = 1;
  // 支付关系状态
  PayRelationStatus status = 2;
  // 门店id
  repeated uint64 store_ids = 3;
}

// 获取支付关系列表
message GetPayRelationListRequest {
  // 地理区域id
  uint64 geo_region_id = 1;
  // 管理区域id
  uint64 branch_region_id = 2;
  // 门店id
  repeated uint64 store_ids = 3;
  // 公司id
  repeated uint64 company_ids = 4;
  // 支付关系状态
  PayRelationStatus status = 5;
  // 分页
  Page page = 6;
}

// 支付关系列表
message GetPayRelationListResponse {
  // 列表数据
  repeated GetPayRelationDetailResponse rows = 1;
  // 总页数
  uint64 total = 2;
}

// 支付关系列表
message GetPayRelationDetailResponse {
  // 支付关系id
  uint64 relation_id = 1;
  // 门店id
  uint64 store_id = 2;
  // 门店code
  string store_code = 3;
  // 门店名称
  string store_name = 4;
  // 授信主体公司id
  uint64 company_id = 5;
  // 授信主体名称
  string company_name = 6;
  // 更新人
  uint64  updated_by = 7;
  // 更新人名称
  string  updated_name = 8;
  // 创建人
  uint64  created_by = 9;
  // 创建人名称
  string  created_name = 10;
  // 状态
  PayRelationStatus status = 11;
  // 创建时间
  google.protobuf.Timestamp created = 12;
  // 更新时间
  google.protobuf.Timestamp updated = 13;
}

// 获取支付关系详情
message GetPayRelationDetailRequest {
  // 支付关系id
  uint64 relation_id = 1;
}

// 移除支付关系
message DeletePayRelationRequest {
  // 支付关系id
  repeated uint64 relation_id = 1;
}

// 关闭/开启信用付
message ChangePayRelationStatusRequest {
  // 信用付id, 必传
  repeated uint64 relation_ids = 1;
  // 状态, 必传
  PayRelationStatus status = 2;
}

// 获取日志列表
message GetLogListRequest {
  // 业务名称, 必传
  // credit_pay 信用付
  // pay_relation 支付关系
  string bus_name = 1;
  // 数据id, 必传
  uint64 data_id = 2;
  // 分页
  Page page = 3;
}

// 日志列表
message GetLogListResponse {
  // 列表数据
  repeated LogList rows = 1;
  // 总页数
  uint64 total = 2;
}

// 日志列表数据
message LogList {
  // 日志id
  uint64 log_id = 1;
  // 日志详情
  // 字段含义详见 https://gitlab.hexcloud.cn/saas-boh/credit-pay/blob/master/doc/saas_boh_credit_pay.sql
  repeated LogInfo log_data = 2;
  // 操作类型
  LogOperationType operation_type = 3;
  // 操作人id
  uint64 updated_by = 4;
  // 操作人名称
  string updated_name = 5;
  // 操作时间
  google.protobuf.Timestamp updated = 6;
}

// 日志详情
message LogInfo {
  // 字段名称
  string field_name = 1;
  // 更新前的值
  string old_val = 2;
  // 更新后的值
  string new_val = 3;
  // 是否变更
  bool is_change = 4;
}

// 支付回调
message PayNotifyRequest {
  // 业务系统订单号
  string order_no = 1;
  // 第三方支付订单号，比如 微信订单号、支付宝订单号
  string tp_transaction_no = 2;
  // 支付状态   PAID：支付成功，REFUNDED：退款成功，REF_FAIL:退款失败
  string pay_status = 3;
  // 附加信息，如果有失败的信息，可通过该字段带回。
  string message = 4;
  // 支付渠道
  string pay_channel = 5;
  // 支付人id
  string pay_user_id = 6;
  // 支付人名称
  string pay_user_name = 7;
}

// 信用付支付
message PayRequest {
  // 业务系统订单号
  string order_no = 1;
}

// 支付结果查询
message QueryPayStatusRequest {
  //（必传）渠道编码，CreditPay BOH信用付 WXMPay 微信小程序 Cash 现金
  string channel = 1;
  //（必传）支付时传给第三方接口的唯一标识id
  string transaction_id = 2;
  // 必传）订单号
  string order_no = 3;
}

// 支付结果
message QueryPayStatusResponse {
  // 异常编码 - 0为成功
  string error_code = 1;
  // 异常描述 - 成功时为success
  string error_message = 2;
  // （必传）交易状态
  string transaction_state = 3;
  // （必传）实际发生金额
  double real_amount = 4;
  // （必传）支付流水号
  string tp_transaction_id = 5;
  // （必传）支付渠道
  string pay_channel = 6;
}

// 获取支付参数请求
message GetPayParamsRequest {
  // 订单号
  string order_no = 1;
  // 支付渠道, 全渠道定义
  string channel = 2;
}

// 支付参数响应
message GetPayParamsResponse {
  // 错误码
  string error_code = 1;
  // 错误信息
  string error_message = 2;
  //支付ticket_id
  string ticket_id = 3;
  // 支付状态 WAITING， PAYING   等
  string status = 4;
  // 预下单第三方订单号（微信、支付宝）
  string prepay_id = 5;
  // 预支付交易客户端唤起支付签名
  string prepay_sign = 6;
  // （可选）第三方流水号
  string tp_transaction_id = 7;
  // （可选）预支付交易扩展字段
  map<string, string> pack_str = 8;
  // 签名算法
  string sign_type = 9;
}

// 强制使用信用付支付
message ForcePayRequest {
  // 业务系统订单号
  string order_no = 1;
}

// 获取支付列表
message GetChannelsRequest {
  // （必传）业务操作
  string action = 1;
  // （必传）门店id
  uint64 store_id = 2;
  // 必传）平台code
  PlatformType platform = 3;
  // （可选）渠道绑定关系查询请求对象
  ListBindingSection list_binding_section = 4;
}

// 渠道绑定关系查询对象
message ListBindingSection {
  // （可选）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送，多个渠道代码使用","分隔
  string channel_category = 1;
  // （可选）渠道是否启用
  string enabled = 2;
  // （可选）渠道标签，多个以","分割
  string channel_labels = 3;
  //（可选）渠道业务代码，多个以","分割
  string business_code = 4;
}

// 渠道签约授权响应信息
message GetChannelsResponse {
  // （必传）异常编码
  string error_code = 1;
  // （必传）异常信息
  string error_message = 2;
  // （可选）渠道绑定关系信息
  BindingListSection binding_list_section = 3;
}

// 渠道绑定关系查询结果
message BindingListSection {
  // （必传）渠道绑定关系信息
  repeated BindingItem binding_item = 1;
}

// 渠道绑定关系信息
message BindingItem {
  //（必传）渠道代码
  string channel_code = 1;
  //（必传）渠道名称
  string channel_name = 2;
  //（必传）渠道业务代码，","分割
  string business_code = 3;
  //（必传）渠道性质，KA 品牌商；ISV 服务商
  string channel_type = 4;
  //（必传）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送
  string channel_category = 5;
  //（可选）渠道二级分类，AM 小程序&会员；COUPON 卡券
  string channel_sub_category = 6;
  //（可选）渠道标签，逗号分割
  string channel_labels = 7;
  //（必传）是否启用
  string enabled = 8;
}

// 获取代金券列表
message GetVouchersRequest {
  // 门店id
  repeated uint64 store_id = 1;
  // 订货类型id
  repeated uint64 order_type_id = 2;
  // 代金券状态
  repeated VoucherStatus voucher_status = 3;
  // 分页
  Page page = 4;
}

// 代金券列表
message GetVouchersResponse {
  // 代金券数据
  repeated VouchersDetail rows = 1;
  // 总页数
  uint64 total = 2;
}

// 代金券详情
message VouchersDetail {
  // 代金券id
  uint64 id = 1;
  // 租户id
  uint64 partner_id = 2;
  // 门店id
  uint64 store_id = 3;
  // 代金券活动id
  uint64 voucher_id = 4;
  // 代金券活动code
  string voucher_code = 5;
  // 代金券名称
  string voucher_name = 6;
  // 开始时间
  google.protobuf.Timestamp start_date = 7;
  // 结束时间
  google.protobuf.Timestamp end_date = 8;
  // 支持的订货类型
  repeated uint64 order_type_ids = 9;
  // 额度
  string limit = 10;
  // 备注
  string remark = 11;
  // 代金券状态
  VoucherStatus voucher_status = 12;
  // 更新人
  uint64 updated_by = 13;
  // 创建人
  uint64 created_by = 14;
  // 创建人名称
  string created_name = 15;
  // 更新人名称
  string updated_name = 16;
  // 创建时间
  google.protobuf.Timestamp created = 17;
  // 更新时间
  google.protobuf.Timestamp updated = 18;
}

// 代金券支付
message VoucherPayRequest {
  // 业务系统订单号
  string order_no = 1;
  // 代金券id
  string voucher_id = 2;
}

// 发起退款
message RefundRequest {
  // 退款单id
  uint64 refund_id = 1;
}

// 获取交易流水
message GetTransactionLogListRequest {
  // 账户id
  uint64 account_id = 1;
  // 账户类型:
  //    CreditPay BOH信用付
  //    Voucher   代金券
  string account_type = 2;
  // 交易流水状态:
  //    Waiting 待处理
  //    Success 成功
  //    Failed  失败
  string status = 3;
  // 分页
  Page page = 4;
}

// 交易流水列表
message GetTransactionLogListResponse {
  // 交易流水
  repeated TransactionLogDetail rows = 1;
  // 总页数
  uint64 total = 2;
}

// 交易流水详情
message TransactionLogDetail {
  // 变动账户id
  string transaction_account = 1;
  // 单号id
  string order_no = 2;
  // 单据变动金额
  string sum_price = 3;
  // 实际变动金额
  string transaction_amount = 4;
  // 当前剩余额度
  string remain_amount = 5;
  // 交易时间
  google.protobuf.Timestamp transaction_time = 6;
  // 交易流水状态:
  //    Waiting 待处理
  //    Success 成功
  //    Failed  失败
  string status = 7;
  // 交易类型:
  //    PAY     支付
  //    REFUND  退款
  //    DEPOSIT 充值
  string transaction_type = 8;
}

message CommonResponse {
  uint32 code = 1;
  string message = 2;
}

// 授信方式枚举
enum CreditWayType {
  // 初始值, 无意义
  CreditWayInit = 0;
  // 门店
  CreditWayStore = 1;
  // 加盟商
  CreditWayFranchisee = 2;
}

// 信用付状态枚举
enum CreditStatus {
  // 初始值, 无意义
  CreditStatusInit = 0;
  // 开启
  CreditStatusOpen = 1;
  // 关闭
  CreditStatusClosed = 2;
}

// 支付关系状态枚举
enum PayRelationStatus {
  // 初始值, 无意义
  PayRelationStatusInit = 0;
  // 开启
  PayRelationStatusOpen = 1;
  // 关闭
  PayRelationStatusClosed = 2;
}

// 日志业务类型枚举
enum LogOperationType {
  // 初始值, 无意义
  LogOperationTypeInit = 0;
  // 创建
  LogOperationTypeCreate = 1;
  // 更新
  LogOperationTypeUpdate = 2;
}

// 平台类型枚举
enum PlatformType {
  // 初始值, 无意义
  PlatformTypeInit = 0;
  // 创建
  WXMiniProgram = 1;
}

// 代金券状态枚举
enum VoucherStatus {
  // 未开始
  NotStart = 0;
  // 活动中
  UnderWay = 1;
  // 已过期
  Expired = 3;
}

message Page {
  string order = 1;
  string sort = 2;
  int32 limit = 3;
  int32 offset = 4;
}
