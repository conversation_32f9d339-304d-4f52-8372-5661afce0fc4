syntax = "proto3";
import "google/api/annotations.proto";
package calculate;

option java_multiple_files = true;
option java_package = "cn.hexcloud.pbis.common.service.integration.m82";
option java_outer_classname = "CalculateProto";


//M82卡券/算价接口
//算价接口入参
message DiscountCalculateIn {
  //门店code
  string storeCode = 1;
  //订单总价
  string subTotal = 2;
  //商品列表
  repeated GoodsInfo lines = 3;
  //适用场景
  int32 scene = 4;
  //适用渠道
  int32 channel = 5;
  //用户id
  int64 userId = 6;
  //打包费
  string packageFee = 7;
  //配送费
  string deliveryFee = 8;
  //优惠信息
  repeated DiscountInfo discs = 9;
}

//优惠信息
message DiscountInfo {
  //促销id
  string promotion_id = 1;
  //1优惠券2优惠活动
  int32 promotion_type = 2;
}

//商品信息
message GoodsInfo {
  //商品单价
  string price = 1;
  //商品总价（不含加料）
  string amt = 2;
  //加料总价
  string accAmt = 3;
  //商品数量
  int32 qty = 4;
  //商品id
  string key_id = 5;
  //商品分类id数组
  repeated string categories = 6;
  //加料商品
  repeated ChargeGoods accies = 7;
  //商品信息扩展字段
  string shop_id = 8;

}

//加料商品信息
message ChargeGoods {
  //商品单价
  string price = 1;
  //加料商品总价
  string amt = 2;
  //商品数量
  int32 qty = 3;
  //商品id
  string key_id = 4;
  //扩展字段，无业务相关
  string shop_id = 5;
}

//优惠算价输出
message DiscountCalculateOut {
  //金额折扣
  repeated PriceDiscount discount = 1;
  //总优惠信息
  TotalDiscount summary = 2;
  //核销标识
  bool verifyFlag = 3;
  //失败原因
  string failReason = 4;
  // 打包费信息
  PackageFee packageFee = 5;
  // 配送费信息
  DeliveryFee deliveryFee = 6;
}


message DeliveryFee {
  string deliveryTotal = 1;//配送费总金额
  string deliveryDiscount = 2;//配送费折扣金额
}

message PackageFee {
  string packageTotal = 1;//打包费总金额
  string packageDiscount = 2;//打包费折扣金额
}

//总优惠信息
message TotalDiscount {
  //订单总金额
  string subTotal = 1;
  //折扣后金额
  string grantTotal = 2;
  //折扣总金额
  string discount = 3;
}

//批量总优惠信息
message BatchTotalDiscount {
  //订单总金额
  string subTotal = 1;
  //折扣后金额
  string grantTotal = 2;
  //折扣总金额
  string discount = 3;
  //优惠券id
  int64 userCouponId = 4;
}

//金额折扣
message PriceDiscount {
  //折扣类型名称
  string type = 1;
  //折扣类型
  string discount_type = 2;
  //折扣金额
  string discount = 3;
  //促销名称
  string name = 4;
  //促销id
  string promotion_id = 5;
  //促销编号
  string promotion_code = 6;
  //促销类型（NORMAL:普通促销，MEMBER:会员促销，COUPON:卡券促销）
  string promotion_type = 7;
  //是否允许折上折
  bool allow_overlap = 8;
  //显示在小票上的促销名称
  string ticket_display = 9;
  //是否允许多次
  bool trigger_times_custom = 10;
  //参与折扣的商品
  repeated string fired = 11;
  //分摊信息
  repeated ProportionInfo product = 12;
  // 优惠券模版ID
  string promotion_template_id = 13;
}

//加料分摊信息
message ChargeInfo {
  string key_id = 1;//商品id
  string price = 3;//商品价格
  int32  qty = 4; //商品数量
  string amt = 5; //折扣后总价
  string price_discount = 7;//商品折扣金额
  int32  discount_goods_num = 8;//参与优惠的数量
  string shop_id = 9; //回传字段无业务逻辑
}

//分摊信息
message ProportionInfo {
  //商品id
  string key_id = 1;
  //换购商品优惠方式（AMOUNT:金额折扣，PERCENT:百分比折扣，PRICE:固定价格）
  string method = 2;
  //商品价格
  string price = 3;
  //商品数量
  int32 qty = 4;
  //折扣后总价
  string amt = 5;
  //换购商品折扣（根据优惠方式，数值代表的意义不同，AMOUNT:折扣金额，PERCENT:折扣百分比，PRICE:固定价格)
  string discount = 6;
  //商品折扣金额
  string price_discount = 7;
  //参与优惠的数量
  //分摊信息
  repeated ChargeInfo charge_info = 9;
  //商品信息扩展字段-回传
  string shop_id = 10;
}

//权益核销接口入参
message UsePromotionsIn {
  //订单号
  string orderNo = 1;
  //门店code
  string storeCode = 2;
  //订单总价
  string subTotal = 3;
  //商品列表
  repeated GoodsInfo lines = 4;
  //适用场景
  int32 scene = 5;
  //适用渠道
  int32 channel = 6;
  //用户id
  int64 userId = 7;
  //打包费
  string packageFee = 8;
  //配送费
  string deliveryFee = 9;
  //优惠信息
  repeated DiscountInfo discs = 10;
}

//权益核销接口入参
message UsePromotionsOut {
  //金额折扣
  repeated PriceDiscount discount = 1;
  //总优惠信息
  TotalDiscount summary = 2;
  //核销标识
  bool verifyFlag = 3;
  //失败原因
  string failReason = 4;
  // 打包费信息
  PackageFee packageFee = 5;
  // 配送费信息
  DeliveryFee deliveryFee = 6;
}

//权益逆向入参
message BackPromotionsIn {
  //订单号
  string orderNo = 1;
  //订单状态 // 1 订单取消 2订单退款
  int32 orderStatus = 2;
}

//权益逆向出参
message BackPromotionsOut {
  //逆向标识
  bool reverseFlag = 1;
  //失败原因
  string failReason = 2;
}

service Calculate {

  //优惠算价
  rpc DiscountCalculate(DiscountCalculateIn) returns (DiscountCalculateOut){
    option(google.api.http) = {
      post:"/api/v1/calculation/calculate/discountCalculate"
      body:"*"
    };
  };

  //权益核销接口
  rpc UsePromotions(UsePromotionsIn) returns (UsePromotionsOut){
    option(google.api.http) = {
      post:"/api/v1/calculation/calculate/UsePromotions"
      body:"*"
    };
  }

  //权益逆向接口
  rpc BackPromotions(BackPromotionsIn) returns (BackPromotionsOut){
    option(google.api.http) = {
      post:"/api/v1/calculation/calculate/backPromotions"
      body:"*"
    };
  }
}