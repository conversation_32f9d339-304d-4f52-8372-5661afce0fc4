syntax = "proto3";

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/rpc/status.proto";

option java_multiple_files = true;
option java_package = "cn.hexcloud.pbis.common.service.integration.m82";
option java_outer_classname = "M82CouponProto";

package coupon;

service RedCouponInnerService {

  rpc RedCouponStatusUpdate (RedCouponStatusUpdateReq) returns (google.rpc.Status) {
    option (google.api.http) = {
      post: "/api/v1/coupon/redCoupon/redCouponStatusUpdate"
      body: "*"
    };
  }

  rpc RedCouponOrderDetail (RedCouponOrderDetailReq) returns (RedCouponOrderDetailResp) {
    option (google.api.http) = {
      post: "/api/v1/coupon/redCoupon/redCouponOrderDetail"
      body: "*"
    };
  }
}

message RedCouponOrderDetailReq {
  /*合作方id*/
  string partner_id = 1;
  /*券号*/
  string coupon_no = 2;
  /*门店 code*/
  string store_code = 3;
}

message RedCouponOrderDetailResp {
  /*内部订单号*/
  string order_id = 1;
  /*三方订单号*/
  string third_order_id = 2;
  /*商品 code*/
  string good_code = 3;
  /*是否可用 */
  bool can_use = 4;
  /*不可用原因*/
  string reason = 5;
}

message RedCouponStatusUpdateReq {
  repeated RedCouponStatusInfo red_coupon_status_info = 1;
}

message RedCouponStatusInfo {
  /*券码*/
  string coupon_no = 1;
  /*1 核销 2 反核销*/
  int32 status = 2;
}