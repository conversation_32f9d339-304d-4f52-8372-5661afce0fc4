syntax = "proto3";

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";

option java_multiple_files = false;
option java_package = "cn.hexcloud.pbis.common.service.integration.trace";
option java_outer_classname = "OrderTraceServiceProto";

/** package路径不能改 */
package cn.hexcloud.m82.log.pos.grpc;

/** 订单追踪服务 */
service OrderTraceService {

    /** 记录订单行为 */
    rpc traceOrder (TraceOrderReq) returns (google.protobuf.Empty) {}
}


message TraceOrderReq {
    /** 订单号 */
    string orderNo = 1;
    /** 业务动作，如：pos-下单、pos-打印小票、printkit-打印小票、posservice-外卖接单 */
    string action = 2;
    /** 业务动作-状态，枚举：NORMAL, ABNORMAL */
    string actionStatus = 3;
    /** 业务信息或者报错信息 */
    string content = 4;
    /** 发生时间，毫秒级，13位数字，如：1640966400000 */
    string startTimestamp = 5;

    int64 partnerId = 6;

    string partnerName = 7;

    string storeId = 8;

    string storeName = 9;

    string clientType = 10;

    string takeNo = 11;
}


