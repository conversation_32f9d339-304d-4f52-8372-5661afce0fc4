syntax = "proto3";
package payflow;

option go_package = ".;payflow";
option java_package = "cn.hexcloud.pbis.common.service.integration.payflow";

service Payflow {
  // NotifyPay 支付结果通知
  rpc Notify(NotifyReq) returns (NotifyRes) {}
}

message NotifyReq {
  // (必传) 业务系统订单号
  string payment_ticket_id = 1;
  // (非必传) 第三方支付订单号，比如 微信订单号、支付宝订单号
  string tp_transaction_no = 2;
  // (必传) 支付到账、退款到账状态。取 pbis的 transactionState 的值，理论上应只有 SUCCESS, REFUNDED
  string status = 3;
  enum ActionType {
    PAY = 0; // 支付标识
    REFUND = 1; // 退款标识
  }
  // (必传) 行为操作
  ActionType action_type = 4;
  // (非必填) 真实支付方式
  string payMethod = 5;
  //  (非必填) 扩展参数 json 字符串
  string extendedParams = 6;
}

message NotifyRes {}


