syntax = "proto3";

import "google/api/annotations.proto";
import public "google/protobuf/timestamp.proto";

package auth;

option java_package = "cn.hexcloud.pbis.common.service.integration.auth";
option java_multiple_files = true;

service HwsAuth {
  // CreateActions 批量创建权限动作
  rpc CreateActions(CreateActionsRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/auth/actions"
    };
  }

  // CreateAction 创建权限动作
  rpc CreateAction(CreateActionRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/auth/action"
    };
  }

  // UpdateAction 更新权限动作
  rpc UpdateAction(UpdateActionsRequest) returns (CommonResponse){
    option (google.api.http) = {
      put:"/api/v1/auth/action"
    };
  }

  // QueryActions 查询action - web
  rpc QueryActions(QueryActionsRequest) returns (QueryActionsResponse){
    option (google.api.http) = {
      post:"/api/v1/auth/action/list"
    };
  }

  // DeleteAction 删除action
  rpc DeleteAction(DeleteActionRequest) returns (CommonResponse) {
    option (google.api.http) = {
      delete:"/api/v1/auth/action/{id}"
    };
  }

  // CreateRole 创建角色
  rpc CreateRole(SaveRoleRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/auth/role"
    };
  }

  // UpdateRole 更新角色
  rpc UpdateRole(SaveRoleRequest) returns (CommonResponse){
    option (google.api.http) = {
      put:"/api/v1/auth/role"
    };
  }

  // QueryRole 角色查询 - web
  rpc QueryRole(RoleQueryRequest) returns (RolesResponse){
    option (google.api.http) = {
      get:"/api/v1/auth/role"
    };
  }

  // DeleteRole 删除角色
  rpc DeleteRole(DelRoleRequest) returns (CommonResponse){
    option (google.api.http) = {
      delete:"/api/v1/auth/role/{id}"
    };
  }

  // Auth 认证授权请求
  rpc Auth(AuthRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/auth",
    };
  }

  // 创建超管
  rpc CreateAdmin(CreateAdminRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/auth/admin"
    };
  }

  // Grant 授权 - web
  rpc Grant(PolicyRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/auth/grant"
    };
  }

  // BatchGrant 批量授权
  rpc BatchGrant(BatchGrantRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/auth/batch-grant"
    };
  }

  // Revoke 吊销授权 - web
  rpc Revoke(PolicyRevokeRequest) returns (CommonResponse){
    option (google.api.http) = {
      delete:"/api/v1/auth/grant/{id}"
    };
  }

  // UpdatePolicyResources 修改subject可访问资源列表
  rpc UpdatePolicyResources(UpdatePolicyResourcesRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/auth/grant/{id}/resources"
    };
  }

  // DeletePolicyResources 删除subject可访问资源列表
  rpc DeletePolicyResources(DeletePolicyResourcesRequest) returns (CommonResponse){
    option (google.api.http) = {
      delete:"/api/v1/auth/grant/{id}/resources"
    };
  }

  // BatchDeletePolicyResources 批量删除subject可访问资源列表
  rpc BatchDeletePolicyResources(BatchDeletePolicyResourcesRequest) returns (CommonResponse){
    option (google.api.http) = {
      delete:"/api/v1/auth/grant/{id}/batch-resources"
    };
  }

  // QueryPolicy 查询授权 - web
  rpc QueryPolicy(PolicyQueryRequest) returns (PolicyQueryResponse){
    option (google.api.http) = {
      post:"/api/v1/auth/grant/list"
    };
  }

  // GetAdmin 获取超管 - web
  rpc GetAdmin(GetAdminRequest) returns (GetAdminResponse){
    option (google.api.http) = {
      get:"/api/v1/auth/admin"
    };
  }

  // CheckOwner 检查是否分配过owner
  rpc CheckOwner(CheckOwnerRequest) returns (CheckOwnerResponse){
    option (google.api.http) = {
      get:"/api/v1/auth/check-owner"
    };
  }

  // TODO:: CleanCache清除缓存
  rpc CleanCache(CleanCacheRequest) returns (CommonResponse){
    option (google.api.http) = {
      get:"/api/v1/auth/grant"
    };
  }

  // SubjectRoles 获取subject角色权限 - web
  rpc SubjectRoles(SubjectRoleRequest) returns (SubjectRoleResponse){
    option (google.api.http) = {
      get:"/api/v1/auth/subject-roles"
    };
  }

  // CreateOwner 创建owner - web
  rpc CreateOwner(SaveOwnerRequest) returns (OwnerResponse){
    option (google.api.http) = {
      post:"/api/v1/auth/owner"
    };
  }

  // CreateOwners 批量创建owner.目前业务要求一个domain只有一个owner, 所以这个接口会覆写之前创建的owner
  rpc CreateOwners(CreateOwnersRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/auth/owners"
    };
  }

  // UpdateOwner 修改owner - web
  rpc UpdateOwner(SaveOwnerRequest) returns (OwnerResponse){
    option (google.api.http) = {
      put:"/api/v1/auth/owner"
    };
  }

  // QueryOwner owner查询 - web
  rpc QueryOwner(OwnerQueryRequest) returns (OwnersResponse){
    option (google.api.http) = {
      get:"/api/v1/auth/owner"
    };
  }

  // DeleteOwner 删除owner - web
  rpc DeleteOwner(DelOwnerRequest) returns (CommonResponse){
    option (google.api.http) = {
      delete:"/api/v1/auth/owner/{id}"
    };
  }

  // QuerySubjectResource 查询对象可访问资源
  rpc QuerySubjectResource(QuerySubjectResourceRequest) returns (SubjectResourceResponse){
    option (google.api.http) = {
      get:"/api/v1/auth/subject-resources"
    };
  }
  // QueryGroup 查询分组
  rpc QueryGroup(GroupQueryRequest) returns (GroupResponse){
    option (google.api.http) = {
      get:"/api/v1/auth/group"
    };
  }
  // Grant 分组授权
  rpc GroupGrant(BatchGroupGrantRequest) returns (CommonResponse){
    option (google.api.http) = {
       post:"/api/v1/auth/group/batch-grant"
    };
  }
  // 查询角色组tree
  rpc RolesTree(RolesTreeRequest) returns (RolesTreeResponse){
    option (google.api.http) = {
      get:"/api/v1/auth/roles-tree"
    };
  }
  // 查询用户授权记录
  rpc SubjectPolicies(PolicyQueryRequest) returns (RolesTreeResponse){
    option (google.api.http) = {
      post:"/api/v1/auth/subject-policies"
    };
  }
  // 复制权限
  rpc CopySubjectPolicy(CopyPolicyRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/auth/copy-grant"
    };
  }
  // 检查当前操作人是否有复制该用户的权限
  rpc CheckCopySubjectPolicy(CheckCopyPolicyRequest) returns (CommonResponse){
    option (google.api.http) = {
      post:"/api/v1/auth/check-copy"
    };
  }
}

message CommonResponse{
  CommonStatus status = 1;  // 响应状态
}

message CreateActionsRequest {
  repeated Action actions = 1;  // 权限list
  Scope scope = 2;  // 所属域
  int64 operator = 3; // 操作人
}

message CreateActionRequest {
  Action actions = 1; // 权限list
  Scope scope = 2;  // 所属scope
  int64 operator = 3; // 操作人
}

message UpdateActionsRequest {
  Action actions = 1; // 权限list
  Scope scope = 2;  // 所属scope
  uint64 operator = 3; // 操作人
}

message DeleteActionRequest {
  uint64 id = 1; // action id
}

message QueryActionsRequest {
  repeated uint64 id = 1; // 查询指定id
  int32 category = 2; // 权限动作分类: 1 api 2 ui
  repeated string code = 3; // 权限动作code
  repeated string domain = 4;  // 产品场景

  int32 limit = 5; // 分页大小
  int32 offset = 6; // 跳过行数
  string sort = 7; // 排序字段
  string order = 8; // 排序顺序

  string searchName = 9; // 名称模糊查询
  string searchCode = 10; // code模糊查询
  string searchDomain = 11; // domain模糊查询
  int32 status = 12; // 状态
}

message QueryActionsResponse {
  repeated Action rows = 1;
  int32 total = 2;
}

message SaveRoleRequest {
  uint64 id = 1;  // 角色id, create时不需要
  string name = 2;  // 角色名称
  string description = 3;  // 描述
  int64  operator = 4; // 操作人

  AdminType    isAdmin = 6; // 角色类型
  RoleCategory category = 7; // 角色类型
  RecordStatus status = 8; // 记录状态
  RoleActions  actions = 9;  // 权限列表
  Scope        scope = 10; // 所属域
  RoleExtra    extra = 11; // 角色拓展信息
}

message SaveRoleResponse {
  CommonStatus status = 1;  // 响应状态

  int64 roleId = 2; // 角色id
}

message SaveRolesRequest{
  repeated SaveRoleRequest role = 1;  // 角色list

  int64 operator = 4; // 操作人
}

message RoleQueryRequest {
  repeated uint64 id = 1;    // role_id
  string name = 2;  // 角色名称
  bool   detail = 3; // 是否携带详细信息

  AdminType    isAdmin = 4; // 是否超级管理员
  RoleCategory category = 5; // 角色类型

  uint64 partnerId = 6;  // 租户ID
  string domain = 7;  // 产品场景

  int32 limit = 8; // 分页大小
  int32 offset = 9; // 跳过行数
  string sort = 10; // 排序字段
  string order = 11; // 排序顺序
}

message RolesResponse {
  repeated RoleInfo rows = 1;  // 角色详情

  int32 total = 2;
}

message DelRoleRequest {
  uint64 roleId = 1; // 角色id
}

message CleanCacheRequest {
  Scope scope = 1;
}

message AuthRequest {
  string  subject = 1;  // 访问对象, 由访问类型 + : + 访问者id组成, e.g. "user_id:1111"
  string  action = 2;  // 操作code domain.code
  string  resource = 3;  // 资源范围, 由资源schema + : + 资源id组成, e.g. "STORE:2222"
  Scope   scope = 4;  // 所属域
}

message CreateAdminRequest {
  Subject subject = 1; // 访问实体
  Scope   scope   = 2; // 域
  uint64 operator = 3; // 操作人
}

message PolicyRequest {
  repeated Subject subjects = 1; // 访问实体

  bool isFullResource = 2; // 是否可访问全部资源

  repeated Resource resources = 3;  // 资源范围

  Scope scope = 4; // 域

  uint64 roleId   = 5; // 角色id
  uint64 operator = 6; // 操作人
}

message BatchGrantRequest {
  repeated Subject subjects = 1; // 访问实体
  repeated BatchRole roles = 2; // 授权角色
  uint64 operator = 3; // 操作人
  GrantCategory category = 4; // 复制权限类型
}

message GetAdminRequest {}

message CheckOwnerRequest {}

message GetAdminResponse {
  Subject subjects = 1;
}

message CheckOwnerResponse {
  bool isHaveOwner = 1;
}

message PolicyQueryRequest {
  uint64 id = 1;  // 授权记录id

  repeated uint64 roleId = 2; // 角色id

  bool    detail = 3; // 是否携带详情(角色action)
  uint64  partnerId = 4;  // 租户ID
  string  domain = 5;  // 产品场景

  repeated Subject subject = 6; // 访问对象类型, 目前只有user_id

  string search = 7; // 筛选的key, 目前为account

  int32 limit = 8; // 分页大小
  int32 offset = 9; // 跳过行数
  string sort = 10; // 排序字段
  string order = 11; // 排序顺序
  bool user_filter = 12; // 根据用户所能看到用户列表过滤
}

message PolicyQueryResponse {
  repeated PolicyInfo rows = 1; // 授权记录详情

  int32 total = 2; // 总条数
}

// 撤销授权
message PolicyRevokeRequest {
  repeated int64 Id = 1; // 授权记录id
  uint64 operator = 2; // 操作人
}

// 修改资源授权
message UpdatePolicyResourcesRequest {
  uint64 roleId = 1; // 角色id
  bool isFullResource = 2; // 是否可访问全部资源
  repeated Subject subjects = 3; // 访问资源对象
  repeated Resource resources = 4; // 可访问资源list
}

// 批量删除资源授权
message DeletePolicyResourcesRequest {
  uint64 roleId = 1; // 角色id
  repeated Subject subjects = 2; // 访问资源对象
  repeated Resource resources = 3; // 要删除的资源list
}

// 批量删除资源授权
message BatchDeletePolicyResourcesRequest {
  repeated Subject subjects = 1; // 访问资源对象
  repeated BatchRole roles = 2; // 要删除的资源list
}

// 查询subject角色权限
message SubjectRoleRequest {
  Subject subject = 1; // subject, 不传通过token获取用户, 传了则以传的为准
  uint64  partnerId = 2;  // 租户ID, 不传通过token获取, 传了则以传的为准
  repeated string domain = 3;  // 产品场景, 可选

  bool  detail = 4; // 是否携带角色权限详情, 可选, 默认不带
  int32 actionsCategory = 5; // 权限动作分类: 1 api 2 ui, 仅在detail为true时生效, 默认全部 --- 目前没有用, 不分api权限和ui权限
  bool resourceDetail = 6; // 是否转换资源schema类型, 会将地区、公司等schema转换为门店、仓库
}

// subject角色权限
message SubjectRoleResponse {
  Subject subject = 1; // 访问实体, e.g. "user_id:111"

  repeated string ownerDomain = 2; // 是owner的domain list

  map<string, SubjectRoleInfo> roleInfo = 5;  // 角色信息, 结构为map<domain>SubjectRoleInfo
}

message SaveOwnerRequest {
  uint64 id = 1; // id, 更新时使用

  Subject subjects = 2; // 访问实体

  Scope scope = 4;  // 所属域

  uint64 operator = 5; // 操作人
}

message OwnerResponse {
  uint64  id = 1;  // 授权记录id

  Subject subjects = 2; // 访问实体, e.g. ["user_id:1111", "user_id:2222"]

  Scope scope = 4;  // 所属域

  RecordStatus status = 5;  // 记录状态

  uint64 createBy = 6; // 创建人
  uint64 updateBy = 7; // 更新人

  google.protobuf.Timestamp created = 8;  // 创建时间
  google.protobuf.Timestamp updated = 9;  // 更新时间
}

message CreateOwnersRequest {
  Subject subjects = 1; // 访问实体

  repeated Scope scopes = 2;  // 所属域

  uint64 operator = 3; // 操作人
}

message OwnerQueryRequest {
  uint64  partnerId = 1;  // 租户ID
  string domain = 2;  // 产品场景
  Subject subject = 3; // 访问对象

  int32 limit = 4; // 分页大小
  int32 offset = 5; // 跳过行数
  string sort = 6; // 排序字段
  string order = 7; // 排序顺序
}

message OwnersResponse {
  repeated OwnerInfo rows = 1; // owner记录详情

  int32  total = 2; // 总条数, 请求时不需要
}

// 撤销授权
message DelOwnerRequest {
  repeated OwnerRevoke owner = 1; // 授权对象

  uint64 operator = 2; // 操作人
}

// 查询访问对象可操作资源
message QuerySubjectResourceRequest {
  string subject = 1;
  string resourceSchema = 2;

  Scope scope = 3;
}

// 对象可操作资源
message SubjectResourceResponse {
  bool full_access = 1; // 是否有全部权限
  string schemaName = 2;
  repeated uint64 ids = 3; // 如果full_access=false，则只有相关schema ids的权限
}

message OwnerRevoke {
  oneof del {
    uint64  Id = 1; // 授权记录id
    Subject subject = 2; // 访问对象
  }
}

message CommonStatus{
  int32  code = 1; // 状态码
  string message = 2; // msg
  string detail = 3; // 状态详情
}

//分页
message Pagination {
  int32  offset = 1;
  int32  limit = 2;
  string order = 3;
  string sort = 4;
  int32  total = 5; // 总条数, 请求时不需要
}

message RoleActions {
  repeated uint64 actionsId = 2; // 允许的动作id, 为0则为全部允许
}

// 角色信息详情
message SubjectRoleInfo {
  repeated RoleInfo roleInfo = 3;
}

// 角色信息详情
message RoleInfo {
  uint64 id = 1;    // role_id
  string name = 2;  // 角色名称
  string description = 3;  // 角色描述

  AdminType isAdmin = 4;  // 是否超级管理员
  RecordStatus status = 5;  // 状态

  uint64 createBy = 6;  // 创建人
  uint64 updateBy = 7;  // 更新人

  Scope scope = 8;

  repeated Action actions = 9; // 权限列表

  bool isFullResource = 10; // 是否可访问全部资源
  repeated Resource resource = 11; // 访问资源, 组成格式: schemaName:id, e.g. ["STORE:1111", "STORE:2222"]

  google.protobuf.Timestamp created = 12;  // 创建时间
  google.protobuf.Timestamp updated = 13;  // 更新时间

  RoleExtra extra = 14; // 角色拓展信息
}

message Scope {
  uint64 partnerId = 1;  // 租户ID
  string domain = 2;  // 产品场景
}

message Action {
  uint64 id = 1; // actionId
  string name = 2;  // action名称
  string code = 3;  // action code, 全局唯一
  string domain = 4; // action所属domain
  int32 category = 5; // 类别: 1 api 2 ui
  uint64 createBy = 6; // 创建人
  uint64 updateBy = 7; // 更新人

  RecordStatus status = 8;  // 状态

  google.protobuf.Timestamp created = 9;  // 创建时间
  google.protobuf.Timestamp updated = 10;  // 更新时间
}

message PolicyInfo {
  uint64  id = 1;  // 授权记录id

  Subject subjects = 2; // 访问实体, e.g. ["user_id:1111", "user_id:2222"]

  bool isFullResource = 3; // 是否可访问全部资源

  repeated Resource resource = 4; // 访问资源, e.g. ["branch:1111", "branch:2222"]

  Scope scope = 5;

  RoleInfo roleInfo = 6;  // 角色信息
  RecordStatus status = 7;  // 记录状态

  uint64 createBy = 8; // 创建人
  uint64 updateBy = 9; // 更新人

  google.protobuf.Timestamp created = 10;  // 创建时间
  google.protobuf.Timestamp updated = 11;  // 更新时间
}

message OwnerInfo {
  uint64  id = 1;  // 授权记录id

  Subject subjects = 2; // 访问实体, e.g. ["user_id:1111", "user_id:2222"]

  Scope scope = 4;  // 所属域

  RecordStatus status = 5;  // 记录状态

  uint64 createBy = 6; // 创建人
  uint64 updateBy = 7; // 更新人

  google.protobuf.Timestamp created = 8;  // 创建时间
  google.protobuf.Timestamp updated = 9;  // 更新时间
}

message Subject {
  string subjectType = 1; // 访问对象类型, 目前只有user_id
  uint64 subjectId = 2; // 访问对象id
}

message Resource {
  string resourceType = 1; // 资源对象类型,schemaName
  uint64 resourceId = 2; // 资源对象类型id
  string resourceName = 3; // 资源名称
  uint64 resourceGeoRegionId = 4; // 资源所属地理区域
}

// 角色拓展信息
message RoleExtra {
  repeated string resourceSchema = 1; // 允许配置的资源schema
}

message BatchRole {
  uint64 roleId       = 1; // 角色id
  bool isFullResource = 2; // 是否可访问全部资源
  repeated Resource resources = 3;  // 资源范围
}

// 记录状态
enum RecordStatus {
  Unavailable = 0;  // 不可用
  Available = 1;  // 可用
}

// 记录状态
enum AdminType {
  No = 0;  // 是admin
  Yes = 1;  // 不是admin
}

// 角色类型
enum RoleCategory {
  INIT = 0;  // 初始值, 无意义
  GlobalRole = 1;  // 全局角色
  PartnerRole = 2;  // 租户角色
}

// 分组查询
message GroupQueryRequest {
  repeated uint64 id = 1; // group_id
  string name = 2;  // 分组名称
  bool   detail = 3; // 是否携带详细信息
  uint64 partnerId = 4;  // 租户ID
  int32 limit = 5; // 分页大小
  int32 offset = 6; // 跳过行数
  string sort = 7; // 排序字段
  string order = 8; // 排序顺序
}

message GroupResponse {
  repeated GroupInfo rows = 1;  // 分组

  int32 total = 2;
}

// 分组信息详情
message GroupInfo {
  uint64 id = 1;    // group_id
  string name = 2;  // 分组名称
  string description = 3;  // 角色描述
  RecordStatus status = 4;  // 状态
  uint64 createBy = 5;  // 创建人
  uint64 updateBy = 6;  // 更新人
  repeated RoleInfo roles = 7; // 角色列表
  google.protobuf.Timestamp created = 8;  // 创建时间
  google.protobuf.Timestamp updated = 9;  // 更新时间
  Scope scope = 10;  // 所属scope
}

// 分组批量授权
message BatchGroupGrantRequest {
  repeated Subject subjects = 1; // 访问实体
  repeated BatchRole roles = 2; // 授权角色
  uint64 operator = 3; // 操作人
  repeated GroupInfo groups = 4; // 授权分组
}

message RolesTreeRequest {
  string roleName = 1;  // 产品场景
  int32 limit = 2; // 分页大小
  int32 offset = 3; // 跳过行数
  string sort = 4; // 排序字段
  string order = 5; // 排序顺序
  uint64 partnerId = 6;  // 租户ID, 不传通过token获取, 传了则以传的为准
  bool   detail = 7; // 是否携带详细信息
}

message RolesTreeResponse {
  repeated RoleNode rows = 1;  // 分组
  int32 total = 2;
}

// 权限节点
message RoleNode {
  uint64 id = 1; // 唯一ID
  string label = 2;  // 名称
  string key = 3;  // 唯一标志
  string value = 4;
  string icon = 5;
  string domain = 6;
  bool isFullResource = 7; // 是否可访问全部资源
  repeated Resource resources = 8;  // 资源范围
  RoleExtra extra = 9; // 角色拓展信息
  repeated Action actions = 10; // 权限列表
  repeated RoleNode children = 11;
}

// 复制用户权限
message CopyPolicyRequest {
  uint64 partnerId = 1;  // 租户ID, 不传通过token获取, 传了则以传的为准
  uint64 operator = 2; // 操作人
  repeated Subject from_subjects = 3; // 被复制权限的用户
  repeated Subject to_subjects = 4; // 给某些用户复制权限
  GrantCategory category = 5; // 复制权限类型
}

// 检查复制用户权限
message CheckCopyPolicyRequest {
  uint64 partnerId = 1;  // 租户ID, 不传通过token获取, 传了则以传的为准
  uint64 operator = 2; // 操作人
  repeated Subject subjects = 3; // 复制角色的Subject
}

// 可以操作的Subject
message CheckCopyPolicyResponse {
  uint64 partnerId = 1;  // 租户ID, 不传通过token获取, 传了则以传的为准
  repeated Subject subjects = 2; // 复制角色的Subject
}

// 复制权限类型
enum GrantCategory {
  PolicyINIT = 0;  // 初始值, 无意义
  ReplacePolicy = 1;  // 替换原有权限
  InsertPolicy = 2;  // 在原有权限上新增
}