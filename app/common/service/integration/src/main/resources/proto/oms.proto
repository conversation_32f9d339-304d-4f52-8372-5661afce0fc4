syntax = "proto3";

package oms;

option java_multiple_files = true;
option java_package = "cn.hexcloud.pbis.common.service.integration.oms";

import "google/protobuf/struct.proto";
import "google/api/annotations.proto";

service OmsApiService {

  // 同步kos订单状态
  rpc AcceptKosOrderStatus(AcceptKosOrderStatusRequest) returns (AcceptKosOrderStatusResponse) {

  }
}

message AcceptKosOrderStatusRequest {
  string store_id = 1;
  string order_no = 2;
  int32 status = 3;
  string pick_up_num = 4;
  int64 wait_time = 5;
  int64 make_time = 6;
  int64 wait_order_count = 7;
  int64 wait_product_count = 8;
  string tpName = 9;
}

message AcceptKosOrderStatusResponse {
  string message = 1;
}