syntax = "proto3";

package eticket_proto;
option java_multiple_files = true;

import "ticket.proto";

option java_package = "cn.hexcloud.pbis.common.service.integration.eticket";


service Eticket {

    // 电子小票订单上传
    rpc UploadTicket(UploadTicketRequest) returns (UploadTicketResponse) {

    }

}


message UploadTicketRequest {
    // （必传）完整的ticket信息
    Ticket ticket = 1;
}


message UploadTicketResponse {
    // （必传）是否上传成功
    bool success = 1;
    string ticket_id = 2;
}


