syntax = "proto3";
package tji_coupon;

option java_multiple_files = true;
option java_package = "cn.hexcloud.pbis.common.service.integration.tji";
option java_outer_classname = "TjiCouponProto";

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

// 谭仔卡券支付（对应核销、反核销、查询操作）
service TjiCouponService {

    // 卡券查询
    rpc Query (QueryRequest) returns (CommonResponse) {
        option (google.api.http) = {
          post: "/api/v1/gs-tji-coupon/coupon/query"
        };
    };

    // 卡券核销
    rpc Consume (ConsumeRequest) returns (CommonResponse) {
        option (google.api.http) = {
          post: "/api/v1/gs-tji-coupon/coupon/consume"
        };
    };

    // 卡券反核销
    rpc Cancel (CancelRequest) returns (CommonResponse) {
        option (google.api.http) = {
          post: "/api/v1/gs-tji-coupon/coupon/cancel"
        };
    };
}

message TjiCoupon {
    //（必填）券号
    string coupon_no = 1;
    //（必填）面额（单位：分）
    uint32 amount = 2;
    //（必填）券状态，0-可用，1-不可用
    uint32 status = 3;
    //（选填）门店编码
    string shop_no = 4;
    //（选填）操作日期（格式：yyyyMMdd）
    string opt_date = 5;
    //（选填）操作时间（格式：HH:mm）
    string opt_time = 6;
    //（选填）餐号
    string bill_no = 7;
    //（选填）商户允差额（单位：分）
    int32 merchant_allowance = 8;
    // SN类型, 在合阔用作名称
    string sn_type = 9;
    // 过期时间
    string expire_time = 10;
    // 模板号
    string assign_no = 11;
}

message CommonResponse {
    //（必填）业务编码
    string code = 1;
    //（必填）业务消息
    string message = 2;
    //（选填）券信息
    TjiCoupon data = 3;
}

message QueryRequest {
    //（必填）券号
    string coupon_no = 1;
    // 语言
    string lang = 2;
}

message ConsumeRequest {
    //（必填）券号
    string coupon_no = 1;
    //（必填）核销金额（单位：分）
    uint32 amount = 2;
    //（必填）门店编码
    string shop_no = 3;
    //（必填）操作日期（格式：yyyyMMdd）
    string opt_date = 4;
    //（必填）操作时间（格式：HH:mm）
    string opt_time = 5;
    //（必填）点餐号
    string bill_no = 6;
    //（必填）卡券面额（分）
    int32 par_value = 7;
    //（必填）用户购买卡券金额（分）
    int32 cost = 8;
    // 语言
    string lang = 9;
}

message CancelRequest {
    //（必填）券号
    string coupon_no = 1;
    // 语言
    string lang = 2;
}