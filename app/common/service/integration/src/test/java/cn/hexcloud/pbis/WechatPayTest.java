package cn.hexcloud.pbis;

import cn.hexcloud.commons.utils.UUIDUtil;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import lombok.SneakyThrows;

/**
 * @ClassName WechatPayTest.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/02 14:10:24
 */
public class WechatPayTest {

  private static final String MERCHANT_ID = "1613787228";
  private static final String APP_KEY = "hijsg94h20u0sgh394ht93hge9uh2nis";
  private static final String CERT_TEXT = "MIID8zCCAtugAwIBAgIUOJpQnH0+PoBUEXmIsMxuOABBDz8wDQYJKoZIhvcNAQEL"
      + "BQAwXjELMAkGA1UEBhMCQ04xEzARBgNVBAoTClRlbnBheS5jb20xHTAbBgNVBAsT"
      + "FFRlbnBheS5jb20gQ0EgQ2VudGVyMRswGQYDVQQDExJUZW5wYXkuY29tIFJvb3Qg"
      + "Q0EwHhcNMjExMjE1MTEyMzEzWhcNMjYxMjE0MTEyMzEzWjCBhDETMBEGA1UEAwwK"
      + "MTYxMzc4NzIyODEbMBkGA1UECgwS5b6u5L+h5ZWG5oi357O757ufMTAwLgYDVQQL"
      + "DCfkuIrmtbfomY7lpLTlsYDppJDppa7nrqHnkIbmnInpmZDlhazlj7gxCzAJBgNV"
      + "BAYMAkNOMREwDwYDVQQHDAhTaGVuWmhlbjCCASIwDQYJKoZIhvcNAQEBBQADggEP"
      + "ADCCAQoCggEBAK4zECKYnhVrN+ZOi32EMpbCobjWSjy7aKrqDW/CEfFlvW7dWZw9"
      + "TXXsLFSUa8v8+cgw1EB3SlDl+/sgLUO9/+yoW7tvRLbZMoPpTXKV5XrwCHtCQd0m"
      + "ejN5oMouUXd4+ZwaZDezOSEvKo7Vj3Q59EJqWQCSJxNyGX7DwHQnwqdbHs/qa9Ev"
      + "tujtXM+CZVjbcq1zID4BwN15FD/bVExqEB2xRElRHB05OzxoZ5GrAVKPACKhyiXa"
      + "Xg1WrR34N2DTA89IL3yyvQVSYdxZiqOHcVnwCS72thjVo9rFA7Q1fQzCKk1IjdRC"
      + "RN5gWLKpxjeqY61WVbJw0oouMEsI6REdN08CAwEAAaOBgTB/MAkGA1UdEwQCMAAw"
      + "CwYDVR0PBAQDAgTwMGUGA1UdHwReMFwwWqBYoFaGVGh0dHA6Ly9ldmNhLml0cnVz"
      + "LmNvbS5jbi9wdWJsaWMvaXRydXNjcmw/Q0E9MUJENDIyMEU1MERCQzA0QjA2QUQz"
      + "OTc1NDk4NDZDMDFDM0U4RUJEMjANBgkqhkiG9w0BAQsFAAOCAQEAqiNDZWtE/Ib1"
      + "JyIUyEb8DU5UGAq7/tPx7CMnuYImOF6JENFEVt57/fiYEgJoCaQ+KjkArBGQ8x12"
      + "k2DXA/RDAeWu6EfvOrsoegEi/ENL61OQa34/7qY3x5uQCIG6ilDkT/B307+kJ0+r"
      + "5yXuoa9GpcKY/GkVZKFfEv6EtyIx7CR1Q2VObMfgH7/aM0/WftQ1EyR0JiG1ftX/"
      + "rh58LQ8V0X+uQOOYGqOI83YSHa/6Z38zapSSrDAoPfBZHKJLRU9HK+ZHMg8qdeAv"
      + "WC8DNNWV6ahB6vSdEcNb0Zf1teNA4gVGsVrUG4zi0mD0PyWCBD5XFrd7UTLm8R9j"
      + "MvH83F4o0A==";
  private static final String PRIVATE_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCuMxAimJ4Vazfm"
      + "Tot9hDKWwqG41ko8u2iq6g1vwhHxZb1u3VmcPU117CxUlGvL/PnIMNRAd0pQ5fv7"
      + "IC1Dvf/sqFu7b0S22TKD6U1yleV68Ah7QkHdJnozeaDKLlF3ePmcGmQ3szkhLyqO"
      + "1Y90OfRCalkAkicTchl+w8B0J8KnWx7P6mvRL7bo7VzPgmVY23KtcyA+AcDdeRQ/"
      + "21RMahAdsURJURwdOTs8aGeRqwFSjwAiocol2l4NVq0d+Ddg0wPPSC98sr0FUmHc"
      + "WYqjh3FZ8Aku9rYY1aPaxQO0NX0MwipNSI3UQkTeYFiyqcY3qmOtVlWycNKKLjBL"
      + "COkRHTdPAgMBAAECggEAZelfxi2tRnCcmnNXLg5aHlMtk9piQheOC3e0dq5+GtDf"
      + "u21qEHrYx2VtJepTClssso7GjhR7+Moj2e4gLSKKCCUSzZwyAhsdb4jdE21YfMzS"
      + "2XGQ4NWy5kyA4eepJ5Exzr6t5NxITv6uEjQkoeWJ3hUjjq7yh3DYM45IGGxud8Ni"
      + "gGw8T2avv3zXYQTeWb5kicP1b+dnXNh+mtanv3AD79n6KerlXRQPGjVDicOstFL1"
      + "rtPAGts8Sn4BDPeK7ZBuA9f9KEr+0Y8LxV2RqMPqPoex9DLFAJkEAgFtA63funMx"
      + "PZxATTCAx3FqnIQL9zPJmV5mLT5Bazk6R6dGqPc2EQKBgQDjpzWBL4bJlWx39+f/"
      + "UtL+adY+2awPDYMM5OPgml9dN+R8/bIZ+U6v1JgOlnYebOaFQEs2dixLk2p9K6ZL"
      + "l6J7iQNKTJUPUUNjiiif96361SUk0rBnnUuuZkT5CJqFsDm4S/bf6y1fmZjdzSYE"
      + "yvcRQXO1c5VEx5VS2FIX+9jKeQKBgQDD4++mB0vKJPD8rgR/xAUdIsdwFCjG8XJg"
      + "dOlR1FlzXhr3Fut1EE9WlSQcfqyAFCAGDX60pyGYaRhDxUKZI2EMt1YOEaJ+xH3L"
      + "eTjcsATKGqt+cBmrhDLhh+8NVySaB3ubkEujJqhUODEg8glDn3ygO7fKMvTa2UY6"
      + "YNs3nMGeBwKBgGzypMSCfjsS66ouVjT26ksfU40YlyNNEB6nm/btah4ulYbL0uRj"
      + "T6kOUQcKMtU0wM2ci2IxA9nXXuswbJFYrEX6uDxikiOWfG2yvOZxni2gu/n3arzr"
      + "pkB8aR3VZD3+rGDq2ecnobtSwrnDaCJ7D+qOLFX2TsVvzqKrK/6WZJXhAoGAKD/7"
      + "i+fP4Nc6VREEh6QlUNlGlXbu8gHGeJ9NGa1CO/xIGiExfDo/sxCt/NTiaOa5tcSs"
      + "Fj6JHqHphHNRvdmRJWW+XuGsu9Nv7ZEY5++a7FQSjMZH4AAn3ENsEReow6Vp9ort"
      + "n7Zfu/5XyecxlB1/JAVhPEDAIv+l+HTSVlj1R50CgYEAqyUgiuHA1vLh3cWD0v3b"
      + "2319Mw/Kn0vWjFNdiUBTB72A82mXS0HU1Yp8tRVGpU+sIoILAYKdhZ9tTdjbdZWW"
      + "T841V5TLAzubIOt5tQCGccLdXpwarLn3l1o4Xy0jCbKAGtX1reBq0BwVo9IcoDpJ"
      + "cbBQFrBCVtuZD+RokqjqC/g=";
  private static final String WECHAT_CERT_TEXT = "MIID3DCCAsSgAwIBAgIUe7D1/TshAN1+DQ8k1gyf6ErUXjIwDQYJKoZIhvcNAQEL"
      + "BQAwXjELMAkGA1UEBhMCQ04xEzARBgNVBAoTClRlbnBheS5jb20xHTAbBgNVBAsT"
      + "FFRlbnBheS5jb20gQ0EgQ2VudGVyMRswGQYDVQQDExJUZW5wYXkuY29tIFJvb3Qg"
      + "Q0EwHhcNMjExMjE1MTEyMzEzWhcNMjYxMjE0MTEyMzEzWjBuMRgwFgYDVQQDDA9U"
      + "ZW5wYXkuY29tIHNpZ24xEzARBgNVBAoMClRlbnBheS5jb20xHTAbBgNVBAsMFFRl"
      + "bnBheS5jb20gQ0EgQ2VudGVyMQswCQYDVQQGDAJDTjERMA8GA1UEBwwIU2hlblpo"
      + "ZW4wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDlzuHQTB43/wfvdeHY"
      + "80UbikP3eLKP+GLcx1DpP7OvTW8scVlpkvDZAlUjaKGfinI6bJ84FVZ8OXyEmv7/"
      + "UI2BzsZFunhdiq/OJYBJ68PL7Nu/QZoUBZrCT+EvxTio+M2L1haHeefjeWlQy+O/"
      + "qMPGSQJahtW1WXv6mga2cnyZ+P3chXK/kuJ399YIY1RGKHIg2rNqE/Qr6aWhlaQT"
      + "0yp27RSnRYkwUjsUUinPKnvkIXi+zMWjvG/Mnno2gStajM08p1VxUd56vK4BYcWH"
      + "J+IXDnkxyU3NHgCM9Ndo/POsjMaDfkaQhsCvK5rTgAe+O7Lh8nztjB62nPCMkpGg"
      + "nLQBAgMBAAGjgYEwfzAJBgNVHRMEAjAAMAsGA1UdDwQEAwIE8DBlBgNVHR8EXjBc"
      + "MFqgWKBWhlRodHRwOi8vZXZjYS5pdHJ1cy5jb20uY24vcHVibGljL2l0cnVzY3Js"
      + "P0NBPTFCRDQyMjBFNTBEQkMwNEIwNkFEMzk3NTQ5ODQ2QzAxQzNFOEVCRDIwDQYJ"
      + "KoZIhvcNAQELBQADggEBACI/QN1eQd4ws8X1jioqHvaJ3k87gFk5+c9qwsFLBXtL"
      + "g19zxjlVcYNBFQJC+4hMTfQNnDkFXGtZh9xNjh5BIMJ+ldEynPLj7NOFKAxudqvO"
      + "JZimV//A/U3KsTSdWeSocYjItlaAmQ274bijdtBNxCCCFYYzYl5VwgQT4kS7RLBw"
      + "3fm3LQ4JT6iX6n01Z+BjbmhBOEpTQqpRFNs0tJqum6bOg+sixeGWUsLrzW/MOwtb"
      + "AcLcxhbGdE4N1WeDQmh5xnoGonw/FDnknfcTglJL0s8eqgpWGwaSDHbbF9UICfKj"
      + "jVYkztUXcGtukdDF1xG8HsoChu4Lx7s2Wx0ngS6fX84=";

  public static void main(String[] args) {
    String nonce = "cb47a8a75077";
    String associatedData = "certificate";
    String cipherText = "1lcgSUYPaUKlUWuOhJ4JijYF1s3fgQuubGMvwCc0liN03g9lXGs5qiNEgLJmidrP1F/sRkkfFRfdrXNrESRvP6ivlX1dMp5iZl9foRK+3kf0zpH8HBe+9o04ZjWhVtOt9Gp2UmDH5ElUxp/4qFgA/Y+Q+kBkvYh2LLhLLwZXqUNV/QMMFo8BpfIr0yZsz+W0JPGOg7/Mug1U3cyB00qbkTCgyZbMuTfjrUrqSW4ramSGvS55a9GxxTiScvGsbkP5SG6an93csz2pEvWuuljY8JWDN2nCB3fzOkoIv9jre0S90O4SCd/EALQbv3ws2kK3IvVB+KGLyBEF+WXDM1KCTaWSB8QRHItl6UOoR4sXuAPkZ4cZgQWiM6R9fys4P6YWmz9DZFZhh+VtRpcPkALuasLeiBwED9fLFRiqKJ2Yx9D/Oyq6nZR6yeOXNu9JT5dhkIoa2QSLd0fFU+LoeE0lhtrvAMwliC6tc8KDP5/xo1gZmc7666LoSVbIO6++xfRVxYzHhiNuaZX4kczefhz+2sdAg0fWDdtpnRv2dH3emH/Pd9fv7pRTt84ihK5kM1p4LGvdM/esSsyuPxZt/YW01VOfzxcq6wfqbqD+F+afXAIqnVHRn1YnF93j7GbYNYUVSBVlpfat1zKPWC1KgChvHf/L+qY77Ft4Xhg4yHwzd8e43k3qL2EIeCTF/HkexWWgqdoHYkuxBfDIQGU3JakI7HaW61hSScDsFXdrxYHhPMU+r2UupkfCDs4VcdQ3iVMOHiX/2QdDTRuGPO4kxOseUOsSi16pKPLgKMfMDkBnMQm0Ky0QkYWFYLWffLFdBnfvQUuQlo/rpJI/xH7wQOxqheHEsQffcG9zEVX1StCrT9F9/cGbNGfsaJ/k+mRalQf6qEHOQ4ZRf+pIubLTZoz0AsJ36UctyEg57dPsu0EFNFK5AuEd9qAyY6MZqg6K7VJAGVnHVfj+O92kGHbfK2yelmLZkIgFK/+IaUPYVx/aucNQ30S4e71m8yo9eW7g2NsTN6vN2IrA7At/QMXQzdfllcsIHZRxD0E+ssyZdlJvRE8yKAjL/QS09+bjeF7XnIdn9v2kLtVL7kJjn2PpXMheZDL7lb/+rZQTGtXirUWcEr5ZxXQr0O/Lk9psPPKI1pL74Q5WhUEy/SGHsl9JSXyGQrdd6DM546Dmph45ev9IRaKbO9xX+dbsrQGnlG79FjqSJjcvf9UgpneQWZGLFJc806oKV8KCzdhAfXzERlu/gMOLZsRQB+W2Y1l0b3ugPRrBEt5f598qzA2F6wwiy00UPIkJnRrjzh3S8SRqJy+lU6aKz3sPvIYEv6ycAwE2ijYqlzWWBmp1VFMjiIzVzIBG0P19ijZ2sY6hwz+sV6glnOkky9HlhI8u76kUTvlvF2asOKvImcr1+86slQdMGF3gGzDO25+Lrt3iPCupXE30b9xkX4Fe2eQEr6p87XHqdXcRnhm0gbIZoqGuAdYF9ZdCFj/1fft1TfX1VMfRw5WsQ/6FxZxvIWIz1NSJRMcC2LUyecfCDRzmWmKsTWUH1qb37J5soKDjUU4c0qBptoja1370cHUAg2bx5nKBfSilVoWg4OH/4KaN77vQug4Wp6Ya38YhYWiFDnp6zRDgLKDYt8M5ShQ68O9qXCD/2M90PXl1ti60Y4kPhYbwS1dZNbfyqYhC3Ziq9AiyHuUJmRaPGAYylJ2jOjsZEsxaOTbVRND0KiM/ox0Mu8P1szKwzC8OLLvMGde7o4TImakmQtqx1CJGblcuuYwjPmH1DV6zm7o8Q7R2gNvm3lpttIBKRHGInoEFj5UxksjD9gvy7H3dvOZgmWQGjbICAmGYC3Z7nZovhLEgCQZZr+4Qx5lZtaI+JfewOBMSsQ==";
//    System.out.println("cipherText:" + cipherText);
//    String decryptText = decrypt(associatedData, cipherText, nonce);
//    System.out.println("decryptText:" + decryptText);
    System.out.println(getPublicKeyFromCert(WECHAT_CERT_TEXT));
//    System.out.println(generateSignature());
  }

  /**
   * @Description: 生成微信支付签名
   * @Params: []
   * @return: java.lang.String
   * @Author: stark.hu
   * @Date: 2021/11/2
   */
  @SneakyThrows
  private static String generateSignature() {
    long timestamp = System.currentTimeMillis() / 1000;
    String nonce = UUIDUtil.getUUID().toUpperCase();
    String data = "GET\n"
        + "/v3/certificates\n"
        + timestamp + "\n"
        + nonce + "\n"
        + "\n";
    CertificateFactory cf = CertificateFactory.getInstance("X509");
    X509Certificate cert = (X509Certificate) cf.generateCertificate(
        new ByteArrayInputStream(Base64.getDecoder().decode(CERT_TEXT)));
    String sn = cert.getSerialNumber().toString(16).toUpperCase();
    Signature sign = Signature.getInstance("SHA256withRSA");
    PrivateKey priKey = KeyFactory.getInstance("RSA")
        .generatePrivate(new PKCS8EncodedKeySpec(Base64.getDecoder().decode(PRIVATE_KEY)));
    sign.initSign(priKey);
    sign.update(data.getBytes(StandardCharsets.UTF_8));
    String signature = Base64.getEncoder().encodeToString(sign.sign());
    String content = "WECHATPAY2-SHA256-RSA2048 " + "mchid=\"" + MERCHANT_ID + "\""
        + ",timestamp=\"" + timestamp + "\",nonce_str=\"" + nonce + "\",serial_no=\"" + sn + "\",signature=\"" + signature + "\"";
    return content;
  }

  /** 
  * @Description: 从证书提取公钥 
  * @Params: [certText] 
  * @return: java.lang.String 
  * @Author: stark.hu
  * @Date: 2021/12/20 
  */ 
  @SneakyThrows
  private static String getPublicKeyFromCert(String certText) {
    CertificateFactory cf = CertificateFactory.getInstance("X509");
    X509Certificate cert = (X509Certificate) cf.generateCertificate(
        new ByteArrayInputStream(Base64.getDecoder().decode(certText)));
    return Base64.getEncoder().encodeToString(cert.getPublicKey().getEncoded());
  }

  /**
   * @Description: 解密微信回调报文
   * @Params: [associatedData, cipherText, nonce]
   * @return: java.lang.String
   * @Author: stark.hu
   * @Date: 2021/11/2
   */
  @SneakyThrows
  private static String decrypt(String associatedData, String cipherText, String nonce) {
    Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
    SecretKeySpec key = new SecretKeySpec(APP_KEY.getBytes(StandardCharsets.UTF_8), "AES");
    GCMParameterSpec spec = new GCMParameterSpec(128, nonce.getBytes(StandardCharsets.UTF_8));
    cipher.init(Cipher.DECRYPT_MODE, key, spec);
    cipher.updateAAD(associatedData.getBytes(StandardCharsets.UTF_8));
    return new String(cipher.doFinal(Base64.getDecoder().decode(cipherText)), StandardCharsets.UTF_8);
  }

}
