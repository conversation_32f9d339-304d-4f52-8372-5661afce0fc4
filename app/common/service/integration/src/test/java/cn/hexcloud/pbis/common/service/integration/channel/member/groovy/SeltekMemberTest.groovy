package cn.hexcloud.pbis.common.service.integration.channel.member.groovy

import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.commons.utils.UUIDUtil
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.request.ChannelMemberRequest
import cn.hexcloud.pbis.common.service.integration.channel.member.dto.response.ChannelMemberResponse
import cn.hexcloud.pbis.common.service.integration.channel.member.provider.DefaultMember
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Commodity
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.springframework.context.support.GenericApplicationContext

class SeltekMemberTest {

    static void main(String[] args) {
        getMember()
    }

    static SeltekMemberModule init() {
        SpringContextUtil u = new SpringContextUtil()
        GenericApplicationContext context = new GenericApplicationContext()
        context.refresh()
        u.setApplicationContext(context)
        ServiceContext.set(ContextKeyConstant.PARTNER_ID, "1372")
        ServiceContext.set(ContextKeyConstant.STORE_ID, "*******************")
        TransactionLoggerContext.createAndShift("getMember")

        DefaultMember member = new DefaultMember()
        ChannelAccessConfig c = new ChannelAccessConfig();
        c.setChannelCode("SeltekOperation")
        String properties = "{\"order_cancel_url\":\"\",\"member_info_by_mobile_url\":\"\",\"member_info_by_member_code_url\":\"\",\"enabled\":true,\"member_info_by_card_no_url\":\"\",\"member_info_url\":\"http://jpgopen.seltek.cn\"}"
        c.setProperties(JSONObject.parseObject(properties))
        c.setMerchantId("105")
        c.setAppKey("test")
        c.setGatewayUrl("https://open-test.hexcloud.net.cn")
        member.init(c)
        return new SeltekMemberModule(member)
    }

    static void getMember() {
        ChannelMemberRequest request = new ChannelMemberRequest()
        request.setChannel("SeltekOperation")
        request.setStoreCode("CS1002")
        request.setCouponNo("2923179000010")
        ChannelMemberResponse response = init().getMember(request)
        println(JSON.toJSONString(response))
    }
}
