package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.commons.utils.UUIDUtil
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Commodity
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.DefaultPay
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alipay.api.internal.util.file.IOUtils
import com.sun.net.httpserver.HttpExchange
import com.sun.net.httpserver.HttpHandler
import com.sun.net.httpserver.HttpServer
import org.springframework.context.support.GenericApplicationContext

import java.security.KeyFactory
import java.security.PrivateKey
import java.security.spec.PKCS8EncodedKeySpec

class BbmslMPayTest {

//    private static final String ORG_PRIVATE_KEY = "MIIEpAIBAAKCAQEAvaXTA5LpQT/rOzW6H9W/lSGTTLIDwtlnG7g1txZgcdI5XDGHiN3QNNTNItxnFVs7VNKCJfyBRaMeBpD/DzoEGGhEl4VeH8otmVP/JKi6rXBkPMwoq3r+S2ak9afWJdgen3WFNIrYOsiDE+hwZlC2jHZTq4DYK4Bt/UySHfWZsmtN6y/BMvG/dU7hrw/DKo/zVvR+nzwRuTGq3AgaAisYQFz3ki8TAiWwnZ7yt3cC7XRBbQ/8ReFuexODkw3avQMAtR4HkTvJzvuzqQ13DUHFTE1I01dwcpvQ3P1OCGmKLdjdlK9ju1Bgw53lzarEFehFYGBYbVoXfuooKp+46KqaiQIDAQABAoIBAQCxENMsog0Aa/Jv7ODjekmOrrN9xzM1yI+5VMtqLw1LGDGP/3qA8kDEIu+InentCaoirwzu9Iup/fs5vEU37T4Hn8pzAgV4fucGImO00YjCCI6+KQvL12D3+1UHO3X5DYjB7xK4lO4ALFgdzUascK2QBaZnGTw7mYSYG9bM/BNNfcDgisLX2sveaUrPg6FKDE7P3f4DM4RrKjzmEQ/wwsSX1fOpN5oGvkwgHIru/XZE8qMY88z45A3qX60gwidLMu/0WPU2VDYqlxI9Fd/fRfQoMkrwIzcUCWAiFqhfczc9UlLaKMi7HIUhdVSwvd7XyqV5vBJE/f4xvsiqIkp0gs1lAoGBAN6dJFV2HK9ObIQ9z6FSBLk0LT3+G7woBzKOQNi6NnsgQiYvw315rqOEcXjkVTL068Pv6lLNqvW2RqWtQafWDsfXuyV3gpToEdGULWFUP9vtdEHgoRqIKAM6eOHKfiDl6S42EMigKg9/n4rktkmsou962LxHQJ1xR8SgN9ik6hZ3AoGBANoXAj+Zv7B+WnivA3S7TiUR5LxkfScLhGU3W5Tuj/fBceeqnCK3fCXErOvdB3l7gqMNZ04zwg0V26zZgSzq/HWVHcthqECCMm4i6mGaV1vgClP3ESBe/CBKmcQ0d0vopQZvj6UtQpcb/rKTfsUVl/fDvlvNZxn4XCmooA+68Bb/AoGAEXyOihgJEMw8QfcJBEcJD/NFSoVXcGREjHgYJ+4YsXtLNuyxkn2odjfFIhXti18p1e5WncKCF2MGEaWWbrSl3JTZoovMuh2wqKHXHrobeg8g/rvCa+8eFYLWVAYv+i2VL4gXsWrDYwxHYAWvPAJPmjgwJjoCnVt0o8FiIaZyrOkCgYEA2cD3gcFl4DeI5c75SGhskh7XoDNFsuKRAu7PBllcbig8Lu/ujcterec+MRK0D9v+iAcuCqsrKh2VBXT2wX6vLEgDQ7ryX9afu9Ao7eNJLnb4WPXrp8KvQx65gWpRYv2bKF3jsy0JvA87s9oJTYdOARj0zHy+YPPWMdIoXwxsBMkCgYBnmnothtRTklLUtmL9P2VgRPzGpcOPRogJx8+lBE1QOXn+MAVdZpLU6dBha1OzPzrZoMYWwzUgPSy+J+gVgJb7pTfX5CsIBkK3iRJmHi7n7mI7Pss0OoW2cRDoACmHAVXxT4PCypC+F5tMIjg5HE1/JVei4hLMd0+uylvre4TOYw=="
    private static final String THIRD_PARTY_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkDecXu4GFMxCqp4pjfwtN1nSQiV9kmdcBMnKq5IeLB6BYWOENqeY+JFftnNaxHOgnhOrbrl71D6G57G7rhNLClgBNerB7mINDBwvENkEVq6zNbJsjOJekJtTVkxs7KoBip44odCBmElCFrUsr0qOr10kzUzYHXXEUpTqQon3jDGm+EkFoNv3RLwn0ZWuwid5kuk6tZ0Xj3OxiKTrzXK2STjzJ8Q25e9CKbO03fpaMSpBRrkuA1NHRQoSO0ew6lGE4swQ+dseVbh+z7YFVUWqDyjJ6pB+F3p4vDniw4r9/rE+ikP0eLMg99vWDjuQbPtUHYaQtMYNSzrmcTkBCGkt6QIDAQAB"
    private static final String PRIVATE_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC9pdMDkulBP+s7Nbof1b+VIZNMsgPC2WcbuDW3FmBx0jlcMYeI3dA01M0i3GcVWztU0oIl/IFFox4GkP8POgQYaESXhV4fyi2ZU/8kqLqtcGQ8zCirev5LZqT1p9Yl2B6fdYU0itg6yIMT6HBmULaMdlOrgNgrgG39TJId9Zmya03rL8Ey8b91TuGvD8Mqj/NW9H6fPBG5MarcCBoCKxhAXPeSLxMCJbCdnvK3dwLtdEFtD/xF4W57E4OTDdq9AwC1HgeRO8nO+7OpDXcNQcVMTUjTV3Bym9Dc/U4IaYot2N2Ur2O7UGDDneXNqsQV6EVgYFhtWhd+6igqn7joqpqJAgMBAAECggEBALEQ0yyiDQBr8m/s4ON6SY6us33HMzXIj7lUy2ovDUsYMY//eoDyQMQi74id6e0JqiKvDO70i6n9+zm8RTftPgefynMCBXh+5wYiY7TRiMIIjr4pC8vXYPf7VQc7dfkNiMHvEriU7gAsWB3NRqxwrZAFpmcZPDuZhJgb1sz8E019wOCKwtfay95pSs+DoUoMTs/d/gMzhGsqPOYRD/DCxJfV86k3mga+TCAciu79dkTyoxjzzPjkDepfrSDCJ0sy7/RY9TZUNiqXEj0V399F9CgySvAjNxQJYCIWqF9zNz1SUtooyLschSF1VLC93tfKpXm8EkT9/jG+yKoiSnSCzWUCgYEA3p0kVXYcr05shD3PoVIEuTQtPf4bvCgHMo5A2Lo2eyBCJi/DfXmuo4RxeORVMvTrw+/qUs2q9bZGpa1Bp9YOx9e7JXeClOgR0ZQtYVQ/2+10QeChGogoAzp44cp+IOXpLjYQyKAqD3+fiuS2Sayi73rYvEdAnXFHxKA32KTqFncCgYEA2hcCP5m/sH5aeK8DdLtOJRHkvGR9JwuEZTdblO6P98Fx56qcIrd8JcSs690HeXuCow1nTjPCDRXbrNmBLOr8dZUdy2GoQIIybiLqYZpXW+AKU/cRIF78IEqZxDR3S+ilBm+PpS1Clxv+spN+xRWX98O+W81nGfhcKaigD7rwFv8CgYARfI6KGAkQzDxB9wkERwkP80VKhVdwZESMeBgn7hixe0s27LGSfah2N8UiFe2LXynV7ladwoIXYwYRpZZutKXclNmii8y6HbCoodceuht6DyD+u8Jr7x4VgtZUBi/6LZUviBexasNjDEdgBa88Ak+aODAmOgKdW3SjwWIhpnKs6QKBgQDZwPeBwWXgN4jlzvlIaGySHtegM0Wy4pEC7s8GWVxuKDwu7+6Ny16t5z4xErQP2/6IBy4KqysqHZUFdPbBfq8sSANDuvJf1p+70Cjt40kudvhY9eunwq9DHrmBalFi/ZsoXeOzLQm8Dzuz2glNh04BGPTMfL5g89Yx0ihfDGwEyQKBgGeaei2G1FOSUtS2Yv0/ZWBE/Malw49GiAnHz6UETVA5ef4wBV1mktTp0GFrU7M/OtmgxhbDNSA9LL4n6BWAlvulN9fkKwgGQreJEmYeLufuYjs+yzQ6hbZxEOgAKYcBVfFPg8LKkL4Xm0wiODkcTX8lV6LiEsx3T67KW+t7hM5j"

    static void main(String[] args) {
        String tpTransactionId = 522904
//        create()
//        query(tpTransactionId)
        refund(tpTransactionId)
//        cancel(tpTransactionId)
//        startNotifyServer()
    }

    static BbmslMPay init() {
        SpringContextUtil u = new SpringContextUtil()
        GenericApplicationContext context = new GenericApplicationContext()
        context.refresh()
        u.setApplicationContext(context)
        ServiceContext.set(ContextKeyConstant.PARTNER_ID, "1372")
        ServiceContext.set(ContextKeyConstant.STORE_ID, "4868825173857435648")
        TransactionLoggerContext.createAndShift("create")

        DefaultPay pay = new DefaultPay()
        ChannelAccessConfig c = new ChannelAccessConfig();
        c.setChannelCode("BbmslMPay")
        c.setThirdPartyPublicKey(THIRD_PARTY_PUBLIC_KEY)
        c.setPrivateKey(PRIVATE_KEY)
        c.setMerchantId("9000226")
        String properties = "{\"gateway_url\":\"http://m-app-qa.hexcloud.cn/\",\"notification_url\":\"https://hipos-saas-qa.hexcloud.cn/callback/pbis/payment\",\"create_url\":\"https://payapi.sit.bbmsl.com/hosted-checkout/create\",\"can_result_verify\":true,\"can_query\":true,\"query_url\":\"https://payapi.sit.bbmsl.com/hosted-checkout/query\",\"pos_cash_enabled\":true,\"deduct_time\":\"begin_verify\",\"cancel_url\":\"https://payapi.sit.bbmsl.com/hosted-checkout/void\",\"can_cancel\":true,\"description\":\"\",\"refund_url\":\"https://payapi.sit.bbmsl.com/hosted-checkout/refund\",\"can_refund\":true,\"pay_kind\":\"third_party_brands\"}"
        c.setProperties(JSONObject.parseObject(properties))
        pay.init(c)
        return new BbmslMPay(pay)
    }

    static void create() {
        ChannelCreateRequest request = new ChannelCreateRequest()
        request.setChannel("BbmslMPay")
        request.setAmount(BigDecimal.valueOf(500))
        request.setTransactionId(UUIDUtil.getUUID())
        request.setCurrency("HKD")
        List<Commodity> commodityList = new ArrayList<>()
        Commodity commodity = new Commodity()
        commodity.setQuantity(5)
        commodity.setPrice(new BigDecimal(100))
        commodity.setName("Books")
        commodityList.add(commodity)
        request.setCommodities(commodityList)
        ChannelCreateResponse response = init().create(request)
        println(JSON.toJSONString(response))
    }

    static void query(String tpTransactionId) {
        ChannelQueryRequest request = new ChannelQueryRequest()
        request.setChannel("PayMePay")
        request.setTransactionTime(new Date(System.currentTimeMillis() - 30 * 1000L))
        request.setTpTransactionId(tpTransactionId)
        println JSON.toJSONString(init().query(request))
    }

    static void refund(String tpTransactionId) {
        ChannelRefundRequest request = new ChannelRefundRequest()
        request.setChannel("PayMeMPay")
        request.setAmount(BigDecimal.valueOf(500))
        request.setRelatedTPTransactionId(tpTransactionId)
        request.setTransactionTime(new Date(System.currentTimeMillis() - 10 * 1000L))
        println JSON.toJSONString(init().refund(request))
    }

    static void cancel(String tpTransactionId) {
        ChannelCancelRequest request = new ChannelCancelRequest()
        request.setChannel("PayMeMPay")
        request.setAmount(BigDecimal.valueOf(120))
        request.setRelatedTPTransactionId(tpTransactionId)
        request.setTransactionTime(new Date(System.currentTimeMillis() - 10 * 1000L))
        println JSON.toJSONString(init().cancel(request))
    }

    static void startNotifyServer() {
//        PayMeMPay payMeMPay = init()
//        String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
//        String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
//        String path = payMeMPay.channel.getChannelCode() + "/" + partnerId + "/" + storeId
//        HttpServer server = HttpServer.create(new InetSocketAddress(8080), 0)
//        server.createContext("/" + path, new HttpHandler() {
//            @Override
//            void handle(HttpExchange exchange) throws IOException {
//                try {
//                    InputStream is = exchange.getRequestBody()
//                    String callbackJSONStr = IOUtils.toString(is, "UTF-8")
//                    ChannelNotificationResponse resp = payMeMPay.payNotify(callbackJSONStr)
//                    OutputStream os = exchange.getResponseBody()
//                    os.write(resp.getResponse().getBytes())
//                    os.flush()
//                    os.close()
//                } catch (Exception ex) {
//                    ex.printStackTrace()
//                }
//
//            }
//        })
//        server.setExecutor(null) // creates a default executor
//        server.start()
        println JSON.toJSONString(init().payNotify(null))
    }

}
