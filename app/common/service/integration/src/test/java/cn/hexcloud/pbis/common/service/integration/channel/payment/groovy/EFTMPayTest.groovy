package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.commons.utils.UUIDUtil
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.DefaultPay
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import com.alibaba.fastjson.JSON
import com.alipay.api.internal.util.file.IOUtils
import com.sun.net.httpserver.HttpExchange
import com.sun.net.httpserver.HttpHandler
import com.sun.net.httpserver.HttpServer
import org.springframework.context.support.GenericApplicationContext

class EFTMPayTest {

    static void main(String[] args) {
        create()
//        refund("236109690951548")
//        query("236109690951548")
//        startNotifyServer()
    }

    static EFTMPay initeft() {
        SpringContextUtil u = new SpringContextUtil();
        GenericApplicationContext context = new GenericApplicationContext()
        context.refresh();
        u.setApplicationContext(context)

        ServiceContext.set(ContextKeyConstant.PARTNER_ID, "1")
        ServiceContext.set(ContextKeyConstant.STORE_ID, "2")
        TransactionLoggerContext.createAndShift("test")

        DefaultPay pay = new DefaultPay();
        ChannelAccessConfig c = new ChannelAccessConfig();
        c.setChannelCode("EFTMPay")
        c.setAppKey("********")
        c.setAccessKey("685ed627f7225ec7427e01747dea8d1c")
        c.setProperties([
                "notification_url": "http://z6nxs5.natappfree.cc",
                "return_url": "https://www.baidu.com",
                "create_url": "https://vmp.eftpay.com.cn/VMP/Servlet/JSAPIService.do",
                "refund_url": "https://vmp.eftpay.com.cn/VMP/Servlet/JSAPIService.do",
                "query_url" : "https://vmp.eftpay.com.cn/VMP/Servlet/QRcodeTradeQuery.do",
        ])
        pay.init(c);
        return new EFTMPay(pay)
    }

    static void create() {
        ChannelCreateRequest request = new ChannelCreateRequest()
        request.setChannel("EFTMPay")
        request.setAmount(new BigDecimal("1"))
        request.setPayer("test")
        request.setDescription("test")
        String uuid = UUIDUtil.getUUID();
        request.setTransactionId(uuid)
        println JSON.toJSONString(initeft().create(request))
    }

    static void query(String transactionId) {
        ChannelQueryRequest request = new ChannelQueryRequest()
        request.setChannel("EFTMPay")
        request.setTransactionId(transactionId)

        println JSON.toJSONString(initeft().query(request))
    }

    static void refund(String transactionId) {
        ChannelRefundRequest request = new ChannelRefundRequest()
        request.setChannel("EFTMPay")
        request.setAmount(new BigDecimal("0.01"))
        String uuid = UUIDUtil.getUUID();
        request.setTransactionId(uuid)
        request.setRelatedTransactionId(transactionId)
        println JSON.toJSONString(initeft().refund(request))
    }

    static void startNotifyServer() {
        EFTMPay eft = initeft();
        String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
        String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
        String path = eft.channel.getChannelCode() + "/" + partnerId + "/" + storeId
        HttpServer server = HttpServer.create(new InetSocketAddress(8080), 0);
        server.createContext("/" + path, new HttpHandler() {
            @Override
            void handle(HttpExchange exchange) throws IOException {
                InputStream is = exchange.getRequestBody();
                String callbackJSONStr = IOUtils.toString(is, "UTF-8")
                ChannelNotificationResponse resp = eft.doPayNotify(callbackJSONStr)
                OutputStream os = exchange.getResponseBody();
                os.write(resp.getResponse().getBytes());
                os.flush();
                os.close();
            }
        });
        server.setExecutor(null); // creates a default executor
        server.start();
    }
}
