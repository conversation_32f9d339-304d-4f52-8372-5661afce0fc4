package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.DefaultPay
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.springframework.context.support.GenericApplicationContext

class MTechMPayTest {

    private static final String ACCESS_KEY = "E9AEEA8A5D49F1C4"
    // uat gateway url
    private static final String GATEWAY_URL = "https://demo.mobiletech.com.hk"
//  // prod gateway url
//  private static final String GATEWAY_URL = "https://mobiletech.com.hk"

    static void main(String[] args) {
        // 建单
//        create()

        // 查交易 :ok
         query("883049153376129024")

        // 关单 mpay不支持octopus渠道的关单
//         cancel("**********","190000154067")

        // 退款
//        refund("**********","190000154067",0.6,"HKD")

        // 主动回调
//        doNotify()

        // 第1组：建单->支付->void->作废link
        // -- 84661057141 WAITING
        // -- 84661057760 APPROVED
        // -- 84661047861 VOID

        // 第2组：建单->支付(3ds failed)->再试支付（3ds ok)->void->作废link
        // -- 84661088889 WAITING
        // -- 84661089087 FAILED
        // -- 84661089320 APPROVED
        // -- 中间3笔void流水：84661089865，84661089984，84661090040，没通过
        // -- 84661090049 VOID

        // 第3组：建单->支付(3ds ok)->整单退
        // -- 84661092365 WAITING
        // -- 84661092265 APPROVED
        // -- 84661093622 RETURN full

        // 第4组：建单->支付(3ds ok)->部分退，分2笔，第3笔之后退超报错
        // -- 84661093922 WAITING
        // -- 84661093843 APPROVED
        // -- 84661094080 RETURN 12.00
        // -- 84661094208 RETURN 0.34
        // -- 84661093843 RETURN 1.00
        // -- 84661094446 RETURN 0.01
    }

    static MTechMPay init() {
        SpringContextUtil u = new SpringContextUtil();
        GenericApplicationContext context = new GenericApplicationContext()
        context.refresh()
        u.setApplicationContext(context)
        ServiceContext.set(ContextKeyConstant.PARTNER_ID, "1372")
        ServiceContext.set(ContextKeyConstant.STORE_ID, "4868825173857435648")
        TransactionLoggerContext.createAndShift("create")

        DefaultPay pay = new DefaultPay();
        ChannelAccessConfig c = new ChannelAccessConfig();
        c.setChannelCode("MTechMPay")
        c.setAccessKey(ACCESS_KEY)
        c.setGatewayUrl(GATEWAY_URL)
        // TamJai: JOINTED-HEART LIMITED
        c.setMerchantId("1100880")
        // terminal id
        c.setTerminalId("001")
        String properties = "{\"merchant_name\":\"TamJai\",\"notification_url\":\"https://hipos-saas-qa.hexcloud.cn/callback/pbis/payment\",\"return_url\":\"https://baidu.com\"}"
        c.setProperties(JSONObject.parseObject(properties) as Map<String, String>)
        pay.init(c)
        return new MTechMPay(pay)
    }

    static void create() {
        ChannelCreateRequest request = new ChannelCreateRequest()
        request.setChannel("MTechMPay")
        // order_id,商户订单号
        request.setOrderNo("3424071900")
        request.setCurrency("HKD")
        request.setDescription("谭仔米线支付测试")
        request.setAmount(BigDecimal.valueOf(1234))
        ChannelCreateResponse response = init().create(request)
        println(JSON.toJSONString(response))
    }

    static void query(String orderNo) {
        ChannelQueryRequest request = new ChannelQueryRequest()
        request.setChannel("MTechMPay")
        request.setTransactionId(orderNo)
        println JSON.toJSONString(init().query(request))
    }

    static void cancel(String transactionId,String tpTransactionId) {
        ChannelCancelRequest request = new ChannelCancelRequest()
        request.setChannel("MTechMPay")
        request.setRelatedTransactionId(transactionId)
        request.setRelatedTPTransactionId(tpTransactionId)
        request.setPayCode("19")
        println JSON.toJSONString(init().cancel(request))
    }

    static void refund(String transactionId,String tpTransactionId,double amount,String currency) {
        ChannelRefundRequest request = new ChannelRefundRequest()
        request.setChannel("MTechMPay")
        request.setAmount(BigDecimal.valueOf(amount))
        request.setCurrency(currency)
        request.setRelatedTransactionId(transactionId)
        request.setRelatedTPTransactionId(tpTransactionId)
        request.setPayCode("19")
        println JSON.toJSONString(init().refund(request))
    }

    static void doNotify() {
        // 替换为指定的url-encode格式字符串
        String str = "ordernum=**********&sysdatetime=**************&ref=************&amt=1.23&currency=HKD&settledate=**************&rspcode=100&customizeddata=&authcode=2024071017122044393&fi_post_dt=**************&version=5.0&merchantid=1100880&storeid=&merchant_tid=001&paymethod=19&tokenid=&depositamt=&accounttype=&salt=1xfxhe6lvgw3gp5q&hash=CB1A67C6A7ED36EA14B11C2A90A2E58A2FAA26B7D09A07FF2CDD19F651010565";
        // 仅用于本地TEST调试
        ChannelNotificationResponse resp = init().payNotify(str)
    }
}
