package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.commons.utils.UUIDUtil
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Commodity
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.DefaultPay
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.springframework.context.support.GenericApplicationContext

class MpgsMPayTest {

//    // 测试
//    private static final String APP_ID = "HexcloudTest"
//    private static final String MERCHANT_ID = "TEST010826902"
//    private static final String ACCESS_KEY = "a4b4d9658044791f38b431658e2e8cc4"

    // 生产
    private static final String APP_ID = "HexcloudTest"
    private static final String MERCHANT_ID = "010831500"
    private static final String ACCESS_KEY = "bcda2cf7fbb814b0fd1996a43f8e90ff"

    static void main(String[] args) {
        String tpTransactionId = "SESSION0002934259006M45990138F5"
        String transactionId = "628b8811861f4736a6922dda0565a437"
        String checkout = "https://ap-gateway.mastercard.com/checkout/pay/SESSION0002503012282E0054211H59?checkoutVersion=1.0.0"

//      {"checkoutMode":"WEBSITE","merchant":"TEST010826902","result":"SUCCESS","session":{"id":"SESSION0002896840116M01375080H5","updateStatus":"SUCCESS","version":"a0d18db301"},"successIndicator":"87dd46305c4946f1"}

//        create()
        pay()
//        query(transactionId, tpTransactionId)
//        refund(1700, transactionId)
//        cancel(tpPaymentId)
//        startNotifyServer()
    }

    static MpgsMPay init() {
        SpringContextUtil u = new SpringContextUtil()
        GenericApplicationContext context = new GenericApplicationContext()
        context.refresh()
        u.setApplicationContext(context)
        ServiceContext.set(ContextKeyConstant.PARTNER_ID, "1372")
        ServiceContext.set(ContextKeyConstant.STORE_ID, "*******************")
        TransactionLoggerContext.createAndShift("create")

        DefaultPay pay = new DefaultPay()
        ChannelAccessConfig c = new ChannelAccessConfig();
        c.setChannelCode("MpgsMPay")
        c.setAppId(APP_ID)
        c.setAccessKey(ACCESS_KEY)
        c.setMerchantId(MERCHANT_ID)
        c.setGatewayUrl("https://hipos-saas-qa.hexcloud.cn/order")
        Map<String, Object> propertiesMap = new HashMap<>()
        propertiesMap.put("deduct_time", "begin_verify")
        propertiesMap.put("description", "")
        propertiesMap.put("pay_kind", "third_party_brands")
        propertiesMap.put("notification_url", "https://hipos-saas-qa.hexcloud.cn/callback/pbis/payment")
        propertiesMap.put("verify_url", "https://ap-gateway.mastercard.com/api/rest/version/%s/merchant/%s/session")
        propertiesMap.put("token_url", "https://ap-gateway.mastercard.com/api/rest/version/%s/merchant/%s/token")
        propertiesMap.put("authorize_url", "https://ap-gateway.mastercard.com/api/rest/version/%s/merchant/%s/order/%s/transaction/%s")
        propertiesMap.put("checkout_url", "https://ap-gateway.mastercard.com/checkout/pay/%s?checkoutVersion=1.0.0")
        propertiesMap.put("order_url", "https://ap-gateway.mastercard.com/api/rest/version/%s/merchant/%s/order/%s")
        propertiesMap.put("refund_url", "https://ap-gateway.mastercard.com/api/rest/version/%s/merchant/%s/order/%s/transaction/%s")
        String properties = JSONObject.toJSONString(propertiesMap)
        c.setProperties(JSONObject.parseObject(properties))
        pay.init(c)
        return new MpgsMPay(pay)
    }

    static void create() {
        ChannelCreateRequest request = new ChannelCreateRequest()
        request.setChannel("MpgsMPay")
        // UTC时间
        request.setAmount(BigDecimal.valueOf(1000))
        String transactionId = UUIDUtil.getUUID()
        println("transactionId: " + transactionId)
        request.setTransactionId(transactionId)
        request.setOrderNo("************")
        request.setCurrency("HKD")
        Map<String, Object> extendParams = new HashMap<>()
        extendParams.put("channel_label", "WEB")
        extendParams.put("pay_method", "VISA")
        extendParams.put("store_code", "S001")
        extendParams.put("ticket_uno", "2001")
        extendParams.put("os", "Android")
        request.setExtendedParams(JSONObject.toJSONString(extendParams))
        ChannelCreateResponse response = init().create(request)
        println(JSON.toJSONString(response))
    }

    static void pay() {
        ChannelPayRequest request = new ChannelPayRequest()
        request.setChannel("MpgsMPay")
        request.setAmount(BigDecimal.valueOf(1000))
        request.setTransactionId(UUIDUtil.getUUID())
        request.setOrderNo("***************")
        request.setPosId("10001")
        request.setPayCode("831234567890123457")
        String extendedParams = "{\"pay_method\":\"GooglePay\",\"paymentToken\":\"{\\\"apiVersion\\\":2,\\\"apiVersionMinor\\\":0,\\\"paymentMethodData\\\":{\\\"description\\\":\\\"萬事達卡 •••• 9345\\\",\\\"info\\\":{\\\"assuranceDetails\\\":{\\\"accountVerified\\\":true,\\\"cardHolderAuthenticated\\\":true},\\\"billingAddress\\\":{\\\"address1\\\":\\\"YIU HING ROAD 63\\\",\\\"address2\\\":\\\"Tung Lam Court 3112\\\",\\\"address3\\\":\\\"\\\",\\\"administrativeArea\\\":\\\"香港島\\\",\\\"countryCode\\\":\\\"HK\\\",\\\"locality\\\":\\\"西灣河\\\",\\\"name\\\":\\\"Hung Tsz Lam\\\",\\\"phoneNumber\\\":\\\"\\\",\\\"postalCode\\\":\\\"\\\",\\\"sortingCode\\\":\\\"\\\"},\\\"cardDetails\\\":\\\"9345\\\",\\\"cardNetwork\\\":\\\"MASTERCARD\\\"},\\\"tokenizationData\\\":{\\\"token\\\":\\\"{\\\\\\\"signature\\\\\\\":\\\\\\\"MEQCIE/FXeQ2yYlsLiJh+f2yDqyj8Mf3l0NK3yzEk4XXhMt/AiA+jnByqp7YVHCKVdOjfNBuP5X2ms/njc7g0zwCPGgMLg\\\\\\\\u003d\\\\\\\\u003d\\\\\\\",\\\\\\\"protocolVersion\\\\\\\":\\\\\\\"ECv1\\\\\\\",\\\\\\\"signedMessage\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"encryptedMessage\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"LPf63ob+Dew8rPUmfBI9mYfiPbgV+f0i3JSRpg5EmcwXHaeK48dzWixgmr3upauD37ZAUbUb/sgT3VfB70zMGvxaiInWYq673jYUEXtgHilYf2kfCodOQzEXwKTGlbmn5ND8Nxrj82BjFJexplibQxOodeFp48Em2S19+q3cFMKvBUXNk9LdUvq7yERF5jTgnQcotOTLbZgwRy+upF1xUH2od8mJ9nDhPiwG/3yt2azbHKqMBAueFMi8yitajnovmEBTHBGtcB1kHItFhZMGnlBAMjfPlvIu/HXtQf75YZ83I+wF+8Q6Tmqb2T2ImhSmGTn+inVQsuqiT2+UrDDIrqs6XKN7w6YpuW2nENW8/ME4M2XoDQ/UHoaPnTVHhxYni1URn5n4LPs34UJ/uvOyRUCCh0lnteAkeOTJBqT/eL8cDPZsLbLCzYIHMKivU23mgMLiPgGDuuH9G3tjiqf3Twni0c13TKfAxnAZIq8HskbiJbG+pJfy2OEyIK/YM1dtevGbgXKxKOP7XA8nSgnj+lQUpkPEflP99s7SpqXj9Fyzlxs\\\\\\\\\\\\\\\\u003d\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"ephemeralPublicKey\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"BDgUUHbwDVVRLwheyRSd1mUwV4Vlmw+0ky0TfzOOzSsafAcXBwgNNJXjE+hp4u7s+okzYB+ULDdh7WyMG03lR8c\\\\\\\\\\\\\\\\u003d\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"tag\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"+wofd9aKJK7oGDSGEcsqLzcSPk/HIYbRuaeeeVPVx4w\\\\\\\\\\\\\\\\u003d\\\\\\\\\\\\\\\"}\\\\\\\"}\\\",\\\"type\\\":\\\"PAYMENT_GATEWAY\\\"},\\\"type\\\":\\\"CARD\\\"}}\",\"store_code\":\"002\",\"store_name\":\"譚仔演示門店\"}"
//        Map<String, Object> tokenMap = JSONObject.parseObject(token, Map.class)
//        Map<String, Object> extendedParams = new HashMap<>()
//        extendedParams.put("store_code", "00001")
//        extendedParams.put("pay_method", "ApplePay")
//        extendedParams.put("paymentToken", tokenMap)
        request.setExtendedParams(extendedParams)
        ChannelPayResponse response = init().pay(request)
        println(JSON.toJSONString(response))
    }

    static void query(String transactionId, String tpTransactionId) {
        ChannelQueryRequest request = new ChannelQueryRequest()
        request.setChannel("MpgsMPay")
        request.setTransactionTime(new Date(System.currentTimeMillis() - 8 * 60 * 60 * 1000L))
        request.setTransactionId(transactionId)
        request.setTpTransactionId(tpTransactionId)
        println JSON.toJSONString(init().query(request))
    }

    static void refund(long amount, String transactionId) {
        ChannelRefundRequest request = new ChannelRefundRequest()
        request.setChannel("MpgsMPay")
        request.setAmount(BigDecimal.valueOf(amount))
        request.setTransactionId(UUIDUtil.getUUID())
        request.setRelatedTransactionId(transactionId)
        request.setTransactionTime(new Date(System.currentTimeMillis() - 8 * 60 * 60 * 1000L))
        println JSON.toJSONString(init().refund(request))
    }

    static void cancel(String tpTransactionId) {
        ChannelCancelRequest request = new ChannelCancelRequest()
        request.setChannel("MpgsMPay")
        request.setAmount(BigDecimal.valueOf(181))
        request.setRelatedTPTransactionId(tpTransactionId)
        request.setTransactionTime(new Date(System.currentTimeMillis() - 8 * 60 * 60 * 1000L))
        println JSON.toJSONString(init().cancel(request))
    }

    static void startNotifyServer() {
//        PayMeMPay payMeMPay = init()
//        String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
//        String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
//        String path = payMeMPay.channel.getChannelCode() + "/" + partnerId + "/" + storeId
//        HttpServer server = HttpServer.create(new InetSocketAddress(8080), 0)
//        server.createContext("/" + path, new HttpHandler() {
//            @Override
//            void handle(HttpExchange exchange) throws IOException {
//                try {
//                    InputStream is = exchange.getRequestBody()
//                    String callbackJSONStr = IOUtils.toString(is, "UTF-8")
//                    ChannelNotificationResponse resp = payMeMPay.payNotify(callbackJSONStr)
//                    OutputStream os = exchange.getResponseBody()
//                    os.write(resp.getResponse().getBytes())
//                    os.flush()
//                    os.close()
//                } catch (Exception ex) {
//                    ex.printStackTrace()
//                }
//
//            }
//        })
//        server.setExecutor(null) // creates a default executor
//        server.start()
        println JSON.toJSONString(init().payNotify(null))
    }
}
