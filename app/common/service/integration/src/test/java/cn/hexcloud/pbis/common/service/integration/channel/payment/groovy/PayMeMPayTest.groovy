package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.commons.utils.UUIDUtil
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.DefaultPay
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alipay.api.internal.util.file.IOUtils
import com.sun.net.httpserver.HttpExchange
import com.sun.net.httpserver.HttpHandler
import com.sun.net.httpserver.HttpServer
import org.springframework.context.support.GenericApplicationContext

class PayMeMPayTest {

    // 测试
//    private static final String APP_KEY = "61c529cb-fbb8-434c-b666-963bf284747e"
//    private static final String ACCESS_KEY = "****************************************"
//    private static final String PRIVATE_KEY = "b13e28da-f7fe-41f1-a55a-1bb685afc198"
//    private static final String TP_PUBLIC_KEY = "MzZ0TnZraDkyYTVnN3RDYUdKdjYwdTkzWWxuMFBrQ3FobWN4SEdqemtrdz0="

    // 生产(103 云南米线)
    private static final String APP_KEY = "40d0f591-47f3-4e25-8dc7-e1800fb26ab8"
    private static final String ACCESS_KEY = "****************************************"
    private static final String PRIVATE_KEY = "a14e5352-d47f-4c04-8d10-4c0fcecfefe7"
    private static final String TP_PUBLIC_KEY = "WUtrdDBuc0FQbXlQcUpzVTdJaTBOcVZFTTVOWU52eFNoclByRUN2TWYrQT0="


//    // 生产(102 三哥)
//    private static final String APP_KEY = "4cae9750-6a51-49c4-b88f-235e1634c3d7"
//    private static final String ACCESS_KEY = "****************************************"
//    private static final String PRIVATE_KEY = "63f812e1-6cda-42c2-babb-7d628d312918"
//    private static final String TP_PUBLIC_KEY = "bXhXTU54NXVaQUUvSmNzcmFXenUyZGIyL1RKUWhHVWM2NVhTUi93b0hHRT0="

    static void main(String[] args) {
//       String tpPaymentId = "41efaf89-8eee-4c47-9a25-d22cd6ecc84b"
//       String tpTransactionId = "0848524b-d76c-4bae-99cf-e8756b05abe2"
//      {"realAmount":300,"tpTransactionId":"332def1f-5bb8-4eae-aded-a84bceeb534d","transactionState":"REFUNDED"}

//        String tpPaymentId = "cd8f4551-5f91-48c7-a46e-9de0c0436076"
//        String tpTransactionId = "89446870-d473-441a-b9dc-c975b6497969"
//      {"realAmount":300,"tpTransactionId":"646931b6-b72e-4ef9-99ec-9192996a6a06","transactionState":"REFUNDED"}

//        String tpPaymentId = "98ed6872-41ef-48b2-90cf-c91098f82b86"
//        String tpTransactionId = "cd339b72-8c7a-4f5f-aa82-7c00c52766c7"
//        {"realAmount":600,"tpTransactionId":"1eabbd7b-d41e-4e80-9423-da710be70b1f","transactionState":"REFUNDED"}

        String tpPaymentId = "3c3dfd45-4fb4-4d50-8332-79f66033faef"
        String tpTransactionId = "74f3b75e-a637-4c4b-a2d2-b0813e593519"
//      {"realAmount":500,"tpTransactionId":"cf2be326-f418-4b18-bfc0-74ca8ba0c367","transactionState":"REFUNDED"}

//        create()
//        query(tpPaymentId)
        refund(500, tpTransactionId)
//        cancel(tpPaymentId)
//        startNotifyServer()
    }

    static PayMeMPay init() {
        SpringContextUtil u = new SpringContextUtil()
        GenericApplicationContext context = new GenericApplicationContext()
        context.refresh()
        u.setApplicationContext(context)
        ServiceContext.set(ContextKeyConstant.PARTNER_ID, "1372")
        ServiceContext.set(ContextKeyConstant.STORE_ID, "4868825173857435648")
        TransactionLoggerContext.createAndShift("create")

        DefaultPay pay = new DefaultPay()
        ChannelAccessConfig c = new ChannelAccessConfig();
        c.setChannelCode("PayMeMPay")
        c.setAppKey(APP_KEY)
        c.setAccessKey(ACCESS_KEY)
        c.setPrivateKey(PRIVATE_KEY)
        c.setThirdPartyPublicKey(TP_PUBLIC_KEY)
        c.setGatewayUrl("https://hipos-saas-qa.hexcloud.cn/order")
        String properties = "{\"get_access_token_url\":\"https://api.payme.hsbc.com.hk/oauth2/token\",\"pay_url\":\"https://api.payme.hsbc.com.hk/payments/payment\",\"notification_url\":\"https://hipos-saas-qa.hexcloud.cn/callback/pbis/payment\",\"create_url\":\"https://api.payme.hsbc.com.hk/payments/paymentrequests\",\"can_result_verify\":true,\"can_query\":true,\"query_url\":\"https://api.payme.hsbc.com.hk/payments/paymentrequests/:contextPath\",\"pos_cash_enabled\":true,\"deduct_time\":\"begin_verify\",\"cancel_url\":\"https://api.payme.hsbc.com.hk/payments/paymentrequests/:contextPath/cancel\",\"can_cancel\":true,\"description\":\"\",\"refund_url\":\"https://api.payme.hsbc.com.hk/payments/transactions/:contextPath/refunds\",\"can_refund\":true,\"pay_kind\":\"third_party_brands\"}"
        c.setProperties(JSONObject.parseObject(properties))
        pay.init(c)
        return new PayMeMPay(pay)
    }

    static void create() {
        ChannelCreateRequest request = new ChannelCreateRequest()
        request.setChannel("PayMeMPay")
        // UTC时间
        request.setTransactionTime(new Date(System.currentTimeMillis() - 8 * 60 * 60 * 1000L))
        request.setAmount(BigDecimal.valueOf(300))
        request.setTransactionId(UUIDUtil.getUUID())
        request.setOrderNo("20240923")
        request.setCurrency("HKD")
        Map<String, Object> extendParams = new HashMap<>()
        extendParams.put("channel_label", "WEB")
        request.setExtendedParams(JSONObject.toJSONString(extendParams))
        ChannelCreateResponse response = init().create(request)
        println(JSON.toJSONString(response))
    }

    static void query(String tpTransactionId) {
        ChannelQueryRequest request = new ChannelQueryRequest()
        request.setChannel("PayMeMPay")
        request.setTransactionTime(new Date(System.currentTimeMillis() - 8 * 60 * 60 * 1000L))
        request.setTpTransactionId(tpTransactionId)
        println JSON.toJSONString(init().query(request))
    }

    static void refund(long amount, String tpTransactionId) {
        ChannelRefundRequest request = new ChannelRefundRequest()
        request.setChannel("PayMeMPay")
        request.setAmount(BigDecimal.valueOf(amount))
        request.setRelatedTPTransactionId(tpTransactionId)
        Map<String, Object> extendParams = new HashMap<>()
        extendParams.put("transactionId", tpTransactionId)
        request.setExtendedParams(JSONObject.toJSONString(extendParams))
        request.setTransactionTime(new Date(System.currentTimeMillis() - 8 * 60 * 60 * 1000L))
        println JSON.toJSONString(init().refund(request))
    }

    static void cancel(String tpTransactionId) {
        ChannelCancelRequest request = new ChannelCancelRequest()
        request.setChannel("PayMeMPay")
        request.setAmount(BigDecimal.valueOf(181))
        request.setRelatedTPTransactionId(tpTransactionId)
        request.setTransactionTime(new Date(System.currentTimeMillis() - 8 * 60 * 60 * 1000L))
        println JSON.toJSONString(init().cancel(request))
    }

    static void startNotifyServer() {
//        PayMeMPay payMeMPay = init()
//        String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
//        String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
//        String path = payMeMPay.channel.getChannelCode() + "/" + partnerId + "/" + storeId
//        HttpServer server = HttpServer.create(new InetSocketAddress(8080), 0)
//        server.createContext("/" + path, new HttpHandler() {
//            @Override
//            void handle(HttpExchange exchange) throws IOException {
//                try {
//                    InputStream is = exchange.getRequestBody()
//                    String callbackJSONStr = IOUtils.toString(is, "UTF-8")
//                    ChannelNotificationResponse resp = payMeMPay.payNotify(callbackJSONStr)
//                    OutputStream os = exchange.getResponseBody()
//                    os.write(resp.getResponse().getBytes())
//                    os.flush()
//                    os.close()
//                } catch (Exception ex) {
//                    ex.printStackTrace()
//                }
//
//            }
//        })
//        server.setExecutor(null) // creates a default executor
//        server.start()
        println JSON.toJSONString(init().payNotify(null))
    }
}
