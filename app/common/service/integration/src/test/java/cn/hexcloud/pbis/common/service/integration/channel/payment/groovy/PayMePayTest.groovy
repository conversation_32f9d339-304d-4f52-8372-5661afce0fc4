package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.commons.utils.UUIDUtil
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Commodity
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.DefaultPay
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alipay.api.internal.util.file.IOUtils
import com.sun.net.httpserver.HttpExchange
import com.sun.net.httpserver.HttpHandler
import com.sun.net.httpserver.HttpServer
import org.springframework.context.support.GenericApplicationContext

import java.text.SimpleDateFormat

class PayMePayTest {

    private static final String APP_KEY = "58fc0c42-82c8-4cf4-ae2e-0202ddc172ab"
    private static final String ACCESS_KEY = "****************************************"
    private static final String PRIVATE_KEY = "b80cf606-b273-46a5-84a0-e73a00207121"
    private static final String TP_PUBLIC_KEY = "NGNmeEt6NFNkSEpETHEwcWd3MnJtWlJ6aTBiVjliT0hUcG1xRWdUbnZtcz0="
    private static final String MERCHANT_ID = "8a0bfe84-7ce0-4ad4-ad2d-1809a4267126"

//    private static final String APP_KEY = "0163d7c3-64b3-41bc-aeb2-60b98ba6963a"
//    private static final String ACCESS_KEY = "****************************************"
//    private static final String PRIVATE_KEY = "a388360e-6522-4cc6-bf19-4d2f68d710b6"
//    private static final String TP_PUBLIC_KEY = "cXBoVUIxZG9PSFA3VkMrNjZlTlVlN0t6TDJGVm1waXBFMWxSVXlrZ2J4MD0="
//    private static final String MERCHANT_ID = "50299b9a-1bc9-4321-aca0-e8641642112c"


    static void main(String[] args) {
        String tpPaymentId = "532b40a5-5595-4fb2-be70-af519303c4a0"
        String tpTransactionId = "d2be7f77-19bd-46f0-9b36-5ecdee94a5ad"
        pay()
//        query(tpPaymentId)
//        refund(tpTransactionId)
//        cancel(tpPaymentId)
//        startNotifyServer()
    }

    static PayMePay init() {
        SpringContextUtil u = new SpringContextUtil()
        GenericApplicationContext context = new GenericApplicationContext()
        context.refresh()
        u.setApplicationContext(context)
        ServiceContext.set(ContextKeyConstant.PARTNER_ID, "1372")
        ServiceContext.set(ContextKeyConstant.STORE_ID, "4868825173857435648")
        TransactionLoggerContext.createAndShift("pay")

        DefaultPay pay = new DefaultPay()
        ChannelAccessConfig c = new ChannelAccessConfig()
        c.setChannelCode("PayMePay")
        c.setAppKey(APP_KEY)
        c.setAccessKey(ACCESS_KEY)
        c.setPrivateKey(PRIVATE_KEY)
        c.setThirdPartyPublicKey(TP_PUBLIC_KEY)
        c.setMerchantId(MERCHANT_ID)
//        String properties = "{\"get_access_token_url\":\"https://api.payme.hsbc.com.hk/oauth2/token\",\"pay_url\":\"https://api.payme.hsbc.com.hk/payments/payment\",\"notification_url\":\"https://hipos-saas-qa.hexcloud.cn/callback/pbis/payment\",\"create_url\":\"https://api.payme.hsbc.com.hk/payments/paymentrequests\",\"can_result_verify\":true,\"can_query\":true,\"query_url\":\"https://api.payme.hsbc.com.hk/payments/paymentrequests/:contextPath\",\"pos_cash_enabled\":true,\"deduct_time\":\"begin_verify\",\"cancel_url\":\"https://api.payme.hsbc.com.hk/payments/paymentrequests/:contextPath/cancel\",\"can_cancel\":true,\"description\":\"\",\"refund_url\":\"https://api.payme.hsbc.com.hk/payments/transactions/:contextPath/refunds\",\"can_refund\":true,\"pay_kind\":\"third_party_brands\"}"
        String properties = "{\"get_access_token_url\":\"https://sandbox.api.payme.hsbc.com.hk/oauth2/token\",\"pay_url\":\"https://sandbox.api.payme.hsbc.com.hk/payments/payment\",\"notification_url\":\"https://hipos-saas-qa.hexcloud.cn/callback/pbis/payment\",\"create_url\":\"https://sandbox.api.payme.hsbc.com.hk/payments/paymentrequests\",\"can_result_verify\":true,\"can_query\":true,\"query_url\":\"https://sandbox.api.payme.hsbc.com.hk/payments/paymentrequests/:contextPath\",\"pos_cash_enabled\":true,\"deduct_time\":\"begin_verify\",\"cancel_url\":\"https://sandbox.api.payme.hsbc.com.hk/payments/paymentrequests/:contextPath/cancel\",\"can_cancel\":true,\"description\":\"\",\"refund_url\":\"https://sandbox.api.payme.hsbc.com.hk/payments/transactions/:contextPath/refunds\",\"can_refund\":true,\"pay_kind\":\"third_party_brands\"}"
        c.setProperties(JSONObject.parseObject(properties))
        pay.init(c)
        return new PayMePay(pay)
    }

    static void pay() {
        ChannelPayRequest request = new ChannelPayRequest()
        request.setChannel("PayMePay")
        request.setAmount(BigDecimal.valueOf(181))
        request.setTransactionId(UUIDUtil.getUUID())
        request.setOrderNo("************")
        request.setPosId("10001")
        request.setPayCode("831234567890123457")
        List<Commodity> commodityList = new ArrayList<>()
        Commodity commodity = new Commodity()
        commodity.setQuantity(1)
        commodity.setCode("FA1013011")
        commodity.setName("過橋米線")
        commodity.setId("4910412607107661824")
        commodity.setPrice(new BigDecimal("1102"))
        commodityList.add(commodity)
        request.setCommodities(commodityList)
        ChannelPayResponse response = init().pay(request)
        println(JSON.toJSONString(response))
    }

    static void query(String tpTransactionId) {
        ChannelQueryRequest request = new ChannelQueryRequest()
        request.setChannel("PayMePay")
        request.setTransactionTime(new Date(System.currentTimeMillis() - 8 * 60 * 60 * 1000L))
        request.setTpTransactionId(tpTransactionId)
        println JSON.toJSONString(init().query(request))
    }

    static void refund(String tpTransactionId) {
        ChannelRefundRequest request = new ChannelRefundRequest()
        request.setChannel("PayMeMPay")
        request.setAmount(BigDecimal.valueOf(100))
        request.setRelatedTPTransactionId(tpTransactionId)
        Map<String, Object> extendParams = new HashMap<>()
        extendParams.put("transactionId", tpTransactionId)
        request.setExtendedParams(JSONObject.toJSONString(extendParams))
        request.setTransactionTime(new Date(System.currentTimeMillis() - 8 * 60 * 60 * 1000L))
        println JSON.toJSONString(init().refund(request))
    }

    static void cancel(String tpTransactionId) {
        ChannelCancelRequest request = new ChannelCancelRequest()
        request.setChannel("PayMeMPay")
        request.setAmount(BigDecimal.valueOf(120))
        request.setRelatedTPTransactionId(tpTransactionId)
        request.setTransactionTime(new Date(System.currentTimeMillis() - 8 * 60 * 60 * 1000L))
        println JSON.toJSONString(init().cancel(request))
    }

    static void startNotifyServer() {
        PayMeMPay payMeMPay = init()
        String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
        String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
        String path = payMeMPay.channel.getChannelCode() + "/" + partnerId + "/" + storeId
        HttpServer server = HttpServer.create(new InetSocketAddress(8080), 0)
        server.createContext("/" + path, new HttpHandler() {
            @Override
            void handle(HttpExchange exchange) throws IOException {
                try {
                    InputStream is = exchange.getRequestBody()
                    String callbackJSONStr = IOUtils.toString(is, "UTF-8")
                    ChannelNotificationResponse resp = payMeMPay.payNotify(callbackJSONStr)
                    OutputStream os = exchange.getResponseBody()
                    os.write(resp.getResponse().getBytes())
                    os.flush()
                    os.close()
                } catch (Exception ex) {
                    ex.printStackTrace()
                }

            }
        })
        server.setExecutor(null) // creates a default executor
        server.start()
    }
}
