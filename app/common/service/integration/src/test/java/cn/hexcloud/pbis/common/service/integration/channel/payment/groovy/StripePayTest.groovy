package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.commons.utils.UUIDUtil
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.DefaultPay
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import com.alibaba.fastjson.JSON
import com.alipay.api.internal.util.file.IOUtils
import com.sun.net.httpserver.HttpExchange
import com.sun.net.httpserver.HttpHandler
import com.sun.net.httpserver.HttpServer
import org.springframework.context.support.GenericApplicationContext

class StripePayTest {

    private static final String PUBLIC_KEY = "pk_live_51PGH5B2LmLYPoz59JO5l4hNHa0fosvsm7qLC6mGfoXveVyBrsQsV0McOJ1TigrcCy8uGTcQmIffmrkikfmwxya7I00FE6oDL68"
    private static final String PRIVATE_KEY = "***********************************************************************************************************"
//  private static final String MERCHANT_ID = "acct_1PWTH403GrXqbcTp"
    private static final String MERCHANT_ID = "acct_1PGH5B2LmLYPoz59"

//    private static final String PUBLIC_KEY = "pk_test_51PGH5B2LmLYPoz59zrG7KHa5LSq0FYjTpj6VTnxEUBNgQwaDtUE1fiT4zHC645Xix4LMsUZMD5yDUjAjKvTRFjKP00D97mDpUD"
//    private static final String PRIVATE_KEY = "sk_test_51PGH5B2LmLYPoz59AeRE46qFf6CZnkMQaMYo6ZIhV7mDm3qHnT2amNpwUAB177gnbGGLwXU8YaXGggrovG2OxuOp00azx7lb4i"
//    private static final String MERCHANT_ID = "acct_1PGH5B2LmLYPoz59"

    static void main(String[] args) {
//        create()
//    query("pi_3QAr8u03GrXqbcTp0bl0LIsS")
//    refund("3bd9638b-8077-4bf9-ba9e-60abdcb27a1d","pi_3QIRJw2LmLYPoz591i1livAB")
        startPayNotifyServer()
    }

    static StripeMPay init() {
        SpringContextUtil u = new SpringContextUtil();
        GenericApplicationContext context = new GenericApplicationContext()
        context.refresh()
        u.setApplicationContext(context)
        ServiceContext.set(ContextKeyConstant.PARTNER_ID, "101")
        ServiceContext.set(ContextKeyConstant.STORE_ID, "4905145432960696320")
        TransactionLoggerContext.createAndShift("create")

        DefaultPay pay = new DefaultPay();
        ChannelAccessConfig c = new ChannelAccessConfig();
        c.setChannelCode("StripeMPay")
        c.setThirdPartyPublicKey(PUBLIC_KEY)
        c.setPrivateKey(PRIVATE_KEY)
        c.setMerchantId(MERCHANT_ID)
        pay.init(c)
        return new StripeMPay(pay)
    }

    static void create() {
        ChannelCreateRequest request = new ChannelCreateRequest()
        request.setChannel("StripeMPay")
        request.setCurrency("aud")
        request.setAmount(BigDecimal.valueOf(1400))
        request.setTransactionId(UUIDUtil.getUUID())
        ChannelCreateResponse response = init().create(request)
        println(JSON.toJSONString(response))
    }

    //
    static void query(String tpTransactionId) {
        ChannelQueryRequest request = new ChannelQueryRequest()
        request.setChannel("StripeMPay")
        request.setTpTransactionId(tpTransactionId)
        println JSON.toJSONString(init().query(request))
    }

    static void refund(String transactionId, String tpTransactionId) {
        ChannelRefundRequest request = new ChannelRefundRequest()
        request.setChannel("StripeMPay")
        request.setAmount(BigDecimal.valueOf(1210))
        request.setTransactionId(transactionId)
        request.setRelatedTPTransactionId(tpTransactionId)
        println JSON.toJSONString(init().refund(request))
    }

    static void startPayNotifyServer() {
        println JSON.toJSONString(init().payNotify(null))
    }

    static void startNotifyServer() {
        StripeMPay stripeMPay = init()
        String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
        String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
        String path = stripeMPay.channel.getChannelCode() + "/" + partnerId + "/" + storeId
        HttpServer server = HttpServer.create(new InetSocketAddress(8080), 0)
        server.createContext("/" + path, new HttpHandler() {
            @Override
            void handle(HttpExchange exchange) throws IOException {
                InputStream is = exchange.getRequestBody();
                String callbackJSONStr = IOUtils.toString(is, "UTF-8")
                ChannelNotificationResponse resp = stripeMPay.payNotify(callbackJSONStr)
                OutputStream os = exchange.getResponseBody()
                os.write(resp.getResponse().getBytes())
                os.flush()
                os.close()
            }
        });
        server.setExecutor(null) // creates a default executor
        server.start()
    }
}
