package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.commons.utils.UUIDUtil
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Commodity
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.DefaultPay
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alipay.api.internal.util.file.IOUtils
import com.sun.net.httpserver.HttpExchange
import com.sun.net.httpserver.HttpHandler
import com.sun.net.httpserver.HttpServer
import org.springframework.context.support.GenericApplicationContext

class TjiCouponPayTest {

    static void main(String[] args) {
        String tpPaymentId = "5020a1e8-0066-4a0e-ac46-73bc9fb4de6e"
        String tpTransactionId = "7f3d3b53-a839-4f2e-95d3-1fdb906a65b4"
        pay()
//        query(tpPaymentId)
//        refund(tpTransactionId)
//        cancel(tpPaymentId)
//        startNotifyServer()
    }

    static TjiCouponPay init() {
        SpringContextUtil u = new SpringContextUtil()
        GenericApplicationContext context = new GenericApplicationContext()
        context.refresh()
        u.setApplicationContext(context)
        ServiceContext.set(ContextKeyConstant.PARTNER_ID, "1372")
        ServiceContext.set(ContextKeyConstant.STORE_ID, "4868825173857435648")
        TransactionLoggerContext.createAndShift("pay")

        DefaultPay pay = new DefaultPay()
        ChannelAccessConfig c = new ChannelAccessConfig()
        c.setChannelCode("TjiCouponPay")
        String properties = "{\"can_query\":true,\"is_mock\":\"false\",\"pos_cash_enabled\":true,\"deduct_time\":\"begin_verify\",\"can_cancel\":true,\"description\":\"\",\"pay_kind\":\"third_party_brands\"}"
        c.setProperties(JSONObject.parseObject(properties))
        pay.init(c)
        return new TjiCouponPay(pay)
    }

    static void pay() {
        ChannelPayRequest request = new ChannelPayRequest()
        request.setChannel("TjiCouponPay")
        request.setAmount(BigDecimal.valueOf(1102))
        request.setTransactionId(UUIDUtil.getUUID())
        request.setOrderNo("************")
        request.setPosId("10001")
        request.setTransactionTime(new Date())
        request.setExtendedParams("{\"store_code\":\"csmd001\",\"store_name\":\"ssx的门店\",\"ticket_no\":\"00002\"}")
        request.setPayCode("2000002")
        ChannelPayResponse response = init().pay(request)
        println(JSON.toJSONString(response))
    }

    static void query(String tpTransactionId) {
        ChannelQueryRequest request = new ChannelQueryRequest()
        request.setChannel("TjiCouponPay")
        request.setTransactionTime(new Date(System.currentTimeMillis() - 8 * 60 * 60 * 1000L))
        request.setTpTransactionId(tpTransactionId)
        println JSON.toJSONString(init().query(request))
    }

    static void cancel(String tpTransactionId) {
        ChannelCancelRequest request = new ChannelCancelRequest()
        request.setChannel("TjiCouponPay")
        request.setAmount(BigDecimal.valueOf(120))
        request.setRelatedTPTransactionId(tpTransactionId)
        request.setTransactionTime(new Date(System.currentTimeMillis() - 8 * 60 * 60 * 1000L))
        println JSON.toJSONString(init().cancel(request))
    }

}
