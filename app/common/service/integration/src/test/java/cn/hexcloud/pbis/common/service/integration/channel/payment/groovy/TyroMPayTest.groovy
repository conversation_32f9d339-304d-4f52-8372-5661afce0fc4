package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCreateRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelCreateResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.DefaultPay
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import org.springframework.context.support.GenericApplicationContext

class TyroMPayTest {

    private static final String CLIENT_ID = "y79fgoezj9qeyUBsQsJritGKC2Z7up8O"
    private static final String CLIENT_SECRET = "hfWSkBJwWo1jN4Fk5ADCG_lfrOx4NTTgV3lZ0SgfodIz1en66UsQ4duo6RZlFjOU"
    private static final String LOCATION_ID = "tc-hexcloud-2000"

    static void main(String[] args) {
        testGetToken()

        // 建单(内含IPG回调BOH的回调地址：transactionNotificationURL），返回支付url :ok
//        create()

        // 查订单(仅debug辅助查单)
//        queryOrder("ssx000000004")
//        queryOrder("**********")

        // 查交易 :ok
//         query("")

//        queryOrder("882996677002620928")

        // 关单 :ok
//         cancel("84661089320")

        // 退款
//        refund("84662426445",1500,"HKD")

        // 主动回调
//        doNotify()

        // 第1组：建单->支付->void->作废link
        // -- 84661057141 WAITING
        // -- 84661057760 APPROVED
        // -- 84661047861 VOID

        // 第2组：建单->支付(3ds failed)->再试支付（3ds ok)->void->作废link
        // -- 84661088889 WAITING
        // -- 84661089087 FAILED
        // -- 84661089320 APPROVED
        // -- 中间3笔void流水：84661089865，84661089984，84661090040，没通过
        // -- 84661090049 VOID

        // 第3组：建单->支付(3ds ok)->整单退
        // -- 84661092365 WAITING
        // -- 84661092265 APPROVED
        // -- 84661093622 RETURN full

        // 第4组：建单->支付(3ds ok)->部分退，分2笔，第3笔之后退超报错
        // -- 84661093922 WAITING
        // -- 84661093843 APPROVED
        // -- 84661094080 RETURN 12.00
        // -- 84661094208 RETURN 0.34
        // -- 84661093843 RETURN 1.00
        // -- 84661094446 RETURN 0.01
    }

    static TyroMPay init() {
        SpringContextUtil u = new SpringContextUtil();
        GenericApplicationContext context = new GenericApplicationContext()
        context.refresh()
        u.setApplicationContext(context)
        ServiceContext.set(ContextKeyConstant.PARTNER_ID, "1372")
        ServiceContext.set(ContextKeyConstant.STORE_ID, "4868825173857435648")
        TransactionLoggerContext.createAndShift("create")

        DefaultPay pay = new DefaultPay();
        ChannelAccessConfig c = new ChannelAccessConfig();
        c.setChannelCode("TyroMPay")
        c.setAppId(CLIENT_ID)
        c.setAppKey(CLIENT_SECRET)
        c.setMerchantId(LOCATION_ID)
        String properties = "{\"deduct_time\":\"begin_verify\",\"can_cancel\":true,\"can_refund\":true,\"description\":\"\",\"pay_kind\":\"third_party_brands\",\"can_result_verify\":true,\"can_query\":true,\"pos_cash_enabled\":true,\"timezone\":\"Australia/Sydney\",\"language\":\"en-AU\",\"locale\":\"en-AU\",\"notification_url\":\"https://hipos-saas-qa.hexcloud.cn/callback/pbis/payment/TyroMPay/webhook\",\"return_url\":\"https://hipos-saas-qa.hexcloud.cn/callback/pbis/payment-return\",\"redirect_url\":\"https://hipos-saas-qa.hexcloud.cn/order\"}";
        c.setProperties(JSONObject.parseObject(properties) as Map<String, String>)
        pay.init(c)
        return new TyroMPay(pay)
    }

    static void create() {
        ChannelCreateRequest request = new ChannelCreateRequest()
        request.setChannel("TyroMPay")
        // tyro订单id
        request.setTransactionId("0f448ac1-862a-4c7b-bdb4-a3b7cdbf445")
        request.setCurrency("AUD")
        request.setDescription("tamjai aus tyro pay test")
        request.setAmount(BigDecimal.valueOf(1234))//1234分 = 12.34元
        request.setExtendedParams("{\"platform\":\"POS_APP\"}")
        ChannelCreateResponse response = init().create(request)
        println(JSON.toJSONString(response))
    }

    static void query(String tpTransactionId) {
        ChannelQueryRequest request = new ChannelQueryRequest()
        request.setChannel("TyroMPay")
//        request.setOrderNo("e3c95afe-d9aa-4371-8800-0da0b8786362")
        request.setTransactionId("fe881ca8-8ef8-4cfe-8d1e-8d2da4d1fe46")
        request.setTpTransactionId(tpTransactionId)
        println JSON.toJSONString(init().query(request))
    }

    static void cancel(String tpTransactionId) {
        ChannelCancelRequest request = new ChannelCancelRequest()
        request.setChannel("TyroMPay")
        request.setRelatedTPTransactionId(tpTransactionId)
        println JSON.toJSONString(init().cancel(request))
    }

    static void refund(String tpTransactionId, double amount, String currency) {
        ChannelRefundRequest request = new ChannelRefundRequest()
        request.setChannel("TyroMPay")
        request.setAmount(BigDecimal.valueOf(amount))
        request.setCurrency(currency)
        request.setRelatedTPTransactionId(tpTransactionId)
        println JSON.toJSONString(init().refund(request))
    }

    static void doNotify() {
        TyroMPay TyroMPay = init();
        // 替换为指定的url-encode格式字符串
        String str = "txndate_processed=2024%2F10%2F16+%E4%B8%8A%E5%8D%88+09%3A58%3A06&ccbin=554433&timezone=Asia%2FShanghai&oid=1309366e-f96a-423b-a586-e25db4bce27c&cccountry=TWN&expmonth=10&hash_algorithm=HMACSHA256&endpointTransactionId=101607319978&currency=344&processor_response_code=00&chargetotal=103.50&terminal_id=00002373&associationResponseCode=0+&approval_code=Y%3A416600%3A4670299081%3APPX+%3A101607319978&expyear=2024&response_code_3dsecure=1&schemeResponseCode=00+&notification_hash=3OsStoeg8FgTe4tMWuQuGj6j1gTCIZI2ldPvIocDYXQ%3D&transactionNotificationURL=https%3A%2F%2Fhipos-saas-qa.hexcloud.cn%2Fcallback%2Fpbis%2Fpayment%2FTyroMPay%2F1372%2F4877537183948374016&schemeTransactionId=MCG1DXREL1016&tdate=1729065486&installments_interest=false&bname=FISERV+UAT%2FTest+Card+01&ccbrand=MASTERCARD&refnumber=101607319978&txntype=sale&paymentMethod=M&txndatetime=2024%3A10%3A16-15%3A55%3A53&cardnumber=%28MASTERCARD%29+...+0235&ipgTransactionId=84670299081&status=%E4%BA%A4%E6%98%93%E6%89%B9%E5%87%86";
        // 仅用于本地TEST调试
        ChannelNotificationResponse resp = TyroMPay.payNotify(str)
    }


    //
    static void testGetToken() {
        def payRequest = [
                locationId: "tc-hexcloud-2000",
                origin: [
                        orderId: "0f448ac1-862a-4c7b-bdb4-a3b7cdbf445"
                ],
                provider: [
                        name: "TYRO",
                        method: "CARD"
                ],
                total: [
                        amount: 10000,
                        currency: "AUD"
                ]
        ]
        def response = TyroMPay.createPayRequest(payRequest)
        println "Pay Request Response: ${response}"
    }
}
