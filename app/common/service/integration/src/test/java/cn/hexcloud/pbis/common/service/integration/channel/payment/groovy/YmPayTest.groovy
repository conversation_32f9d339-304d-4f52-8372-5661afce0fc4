package cn.hexcloud.pbis.common.service.integration.channel.payment.groovy

import cn.hexcloud.commons.utils.SpringContextUtil
import cn.hexcloud.commons.utils.UUIDUtil
import cn.hexcloud.pbis.common.service.integration.channel.config.ChannelAccessConfig
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.Commodity
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelCancelRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelPayRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelQueryRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.request.ChannelRefundRequest
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelNotificationResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.dto.response.ChannelPayResponse
import cn.hexcloud.pbis.common.service.integration.channel.payment.provider.DefaultPay
import cn.hexcloud.pbis.common.util.context.ContextKeyConstant
import cn.hexcloud.pbis.common.util.context.ServiceContext
import cn.hexcloud.pbis.common.util.context.TransactionLoggerContext
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.alipay.api.internal.util.file.IOUtils
import com.sun.net.httpserver.HttpExchange
import com.sun.net.httpserver.HttpHandler
import com.sun.net.httpserver.HttpServer
import org.springframework.context.support.GenericApplicationContext

class YmPayTest {

    private static final String ACCESS_KEY = "test"
    private static final String MERCHANT_ID = "8888"
    private static final String SUB_MERCHANT_ID = "00061100"

    static void main(String[] args) {
        String transactionId = "Q5a4yX8mKKj3knBsYFVW4b"
        String tpTransactionId = "7f3d3b53-a839-4f2e-95d3-1fdb906a65b4"
        bindPos()
//        pay()
//        query(transactionId)
//        refund(transactionId)
//        cancel(tpPaymentId)
//        startNotifyServer()
    }

    static YmPay init() {
        SpringContextUtil u = new SpringContextUtil()
        GenericApplicationContext context = new GenericApplicationContext()
        context.refresh()
        u.setApplicationContext(context)
        ServiceContext.set(ContextKeyConstant.PARTNER_ID, "1026")
        ServiceContext.set(ContextKeyConstant.STORE_ID, "4561522948388749312")
        TransactionLoggerContext.createAndShift("pay")

        DefaultPay pay = new DefaultPay()
        ChannelAccessConfig c = new ChannelAccessConfig()
        c.setChannelCode("YmPay")
        c.setAccessKey(ACCESS_KEY)
        c.setMerchantId(MERCHANT_ID)
        c.setSubMerchantId(SUB_MERCHANT_ID)
        String properties = "{\"bind_pos_url\":\"https://ipos-sa.imageco.cn/\",\"pay_url\":\"https://ipos-sa.imageco.cn/\",\"notification_url\":\"https://hipos-saas-qa.hexcloud.cn/callback/pbis/payment\",\"can_result_verify\":true,\"can_query\":true,\"query_url\":\"https://ipos-sa.imageco.cn/\",\"pos_cash_enabled\":true,\"deduct_time\":\"begin_verify\",\"cancel_url\":\"https://ipos-sa.imageco.cn/\",\"can_cancel\":true,\"description\":\"\",\"refund_url\":\"https://ipos-sa.imageco.cn/\",\"can_refund\":true,\"pay_kind\":\"third_party_brands\"}"
        c.setProperties(JSONObject.parseObject(properties))
        pay.init(c)
        return new YmPay(pay)
    }

    static void bindPos() {
        Map<String, String> param = new HashMap<>()
        param.put("storeId", "4561522948388749312")
        param.put("storeName", "久久丫翼码支付测试门店")
        param.put("storeAddress", "江苏省-南京市-雨花台区-花神大道9号阅城国际花园幽然园")
        JSONObject response = init().bindPosStore(param)
        println(response.toString())
    }

    static void pay() {
        ChannelPayRequest request = new ChannelPayRequest()
        request.setChannel("YmPay")
        request.setAmount(BigDecimal.valueOf(10))
        request.setTransactionId(UUIDUtil.getUUID())
        request.setOrderNo("************")
        request.setPosId("4723931092338409472")
        request.setPayCode("133651862689462594")
        List<Commodity> commodityList = new ArrayList<>()
        Commodity commodity = new Commodity()
        commodity.setQuantity(1)
        commodity.setCode("FA1013011")
        commodity.setName("過橋米線")
        commodity.setId("4910412607107661824")
        commodity.setPrice(new BigDecimal("10"))
        commodityList.add(commodity)
        request.setCommodities(commodityList)
        ChannelPayResponse response = init().pay(request)
        println(JSON.toJSONString(response))
    }

    static void query(String transactionId) {
        ChannelQueryRequest request = new ChannelQueryRequest()
        request.setChannel("YmPay")
        request.setTransactionId(transactionId)
        println JSON.toJSONString(init().query(request))
    }

    static void refund(String relateTransactionId) {
        ChannelRefundRequest request = new ChannelRefundRequest()
        request.setChannel("YmPay")
        request.setAmount(BigDecimal.valueOf(100))
        request.setTransactionId(UUIDUtil.UUID)
        request.setRelatedTransactionId(relateTransactionId)
        println JSON.toJSONString(init().refund(request))
    }

    static void cancel(String tpTransactionId) {
        ChannelCancelRequest request = new ChannelCancelRequest()
        request.setChannel("PayMeMPay")
        request.setAmount(BigDecimal.valueOf(120))
        request.setRelatedTPTransactionId(tpTransactionId)
        request.setTransactionTime(new Date(System.currentTimeMillis() - 8 * 60 * 60 * 1000L))
        println JSON.toJSONString(init().cancel(request))
    }

    static void startNotifyServer() {
        PayMeMPay payMeMPay = init()
        String partnerId = ServiceContext.getString(ContextKeyConstant.PARTNER_ID)
        String storeId = ServiceContext.getString(ContextKeyConstant.STORE_ID)
        String path = payMeMPay.channel.getChannelCode() + "/" + partnerId + "/" + storeId
        HttpServer server = HttpServer.create(new InetSocketAddress(8080), 0)
        server.createContext("/" + path, new HttpHandler() {
            @Override
            void handle(HttpExchange exchange) throws IOException {
                try {
                    InputStream is = exchange.getRequestBody()
                    String callbackJSONStr = IOUtils.toString(is, "UTF-8")
                    ChannelNotificationResponse resp = payMeMPay.payNotify(callbackJSONStr)
                    OutputStream os = exchange.getResponseBody()
                    os.write(resp.getResponse().getBytes())
                    os.flush()
                    os.close()
                } catch (Exception ex) {
                    ex.printStackTrace()
                }

            }
        })
        server.setExecutor(null) // creates a default executor
        server.start()
    }
}
