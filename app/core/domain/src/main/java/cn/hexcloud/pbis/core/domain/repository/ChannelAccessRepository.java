package cn.hexcloud.pbis.core.domain.repository;

import cn.hexcloud.commons.ddd.stereotype.Repository;
import cn.hexcloud.pbis.core.domain.object.channel.ChannelAccess;
import java.util.List;

/**
 * @ClassName ChannelAccessRepository.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/19 13:55:20
 */
public interface ChannelAccessRepository extends Repository<ChannelAccess> {

  List<ChannelAccess> findAny(String partnerId, String companyId, String storeId, String channelCode,
      String businessCode, Integer level);

  List<ChannelAccess> findAnyBySpecificPartnerId(String partnerId, String companyId, String storeId, String channelCode,
      String businessCode, Integer level);

  void remove(String aid, Integer level, String channelCode, List<String> companyIds, List<String> storeId);

}
