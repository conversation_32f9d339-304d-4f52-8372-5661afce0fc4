package cn.hexcloud.pbis.core.domain.repository;

import cn.hexcloud.commons.ddd.stereotype.Repository;
import cn.hexcloud.pbis.core.domain.object.channel.ChannelAuthorization;
import cn.hexcloud.pbis.core.domain.object.channel.SignupMaterials;
import java.util.List;

/**
 * @ClassName ChannelAuthorizationRepository.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/19 14:22:21
 */
public interface ChannelAuthorizationRepository extends Repository<ChannelAuthorization> {

  List<ChannelAuthorization> findByIdList(List<Integer> idList);

  ChannelAuthorization findFirst(String channelCode, String thirdPartyMerchantId, List<String> signupStates, String grantState);

  ChannelAuthorization findFirstNotByPartnerId(String channelCode, String thirdPartyMerchantId);

  SignupMaterials findSignupMaterials(Integer authId);

}
