package cn.hexcloud.pbis.core.domain.repository;

import cn.hexcloud.commons.ddd.stereotype.Repository;
import cn.hexcloud.pbis.core.domain.object.channel.ChannelBinding;
import cn.hexcloud.pbis.core.story.channel.BindingStory;
import cn.hexcloud.pbis.core.story.channel.ListBindingCriteria;
import java.util.List;

/**
 * @ClassName ChannelBindingRepository.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/19 13:54:52
 */
public interface ChannelBindingRepository extends Repository<ChannelBinding> {

  ChannelBinding findFirst(String channelCode);

  List<BindingStory> findAnyToBindingStory(ListBindingCriteria listBindingCriteria);

}
