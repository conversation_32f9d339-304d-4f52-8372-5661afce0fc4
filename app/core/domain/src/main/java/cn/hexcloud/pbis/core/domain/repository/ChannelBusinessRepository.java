package cn.hexcloud.pbis.core.domain.repository;

import cn.hexcloud.commons.ddd.stereotype.Repository;
import cn.hexcloud.pbis.core.domain.object.channel.ChannelBusiness;
import java.util.List;

/**
 * @program: pbis
 * @author: miao
 * @create: 2021-11-25 20:58
 **/
public interface ChannelBusinessRepository extends Repository<ChannelBusiness> {

  List<ChannelBusiness> findByCode(String channelCode);

  List<ChannelBusiness> findByCodeList(List<String> codeList);

  void removeByCode(String channelCode,String code);

}
