package cn.hexcloud.pbis.core.domain.repository;

import cn.hexcloud.commons.ddd.stereotype.Repository;
import cn.hexcloud.pbis.core.domain.object.channel.ChannelRelation;
import cn.hexcloud.pbis.core.domain.object.enums.ChannelRelationType;
import java.util.List;

/**
 * @ClassName ChannelRelationRepository.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2022/04/20 13:58:05
 */
public interface ChannelRelationRepository extends Repository<ChannelRelation> {

  ChannelRelation findByTpEntityId(String channelCode, ChannelRelationType relationType, String tpEntityId);

  ChannelRelation findByHexEntityId(String channelCode, ChannelRelationType relationType, String hexEntityId);

}
