package cn.hexcloud.pbis.core.domain.repository;

import cn.hexcloud.commons.ddd.stereotype.Repository;
import cn.hexcloud.pbis.core.domain.object.channel.Channel;
import java.util.List;

/**
 * @ClassName ChannelRepository.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/11/17 19:13:19
 */
public interface ChannelRepository extends Repository<Channel> {

  Channel findByCode(String channelCode);

  // 条件直接是 OR 非AND
  List<Channel> findByCodeOrName(String code, String name);

  List<Channel> findAny(String code, String category,String subCategory, String type, String searchName);

  List<Channel> findByCodeList(List<String> channelCodeList ,String subCategory);

}
