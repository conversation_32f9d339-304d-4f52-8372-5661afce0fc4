package cn.hexcloud.pbis.core.domain.repository;

import cn.hexcloud.commons.ddd.stereotype.Repository;
import cn.hexcloud.pbis.core.domain.object.transaction.TransactionLog;
import java.util.List;

/**
 * @ClassName TransactionLogRepository.java
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 * @CreateTime 2021/10/14 17:45:18
 */
public interface TransactionLogRepository extends Repository<TransactionLog> {

  List<TransactionLog> findByTransactionId(String transactionId);

}
