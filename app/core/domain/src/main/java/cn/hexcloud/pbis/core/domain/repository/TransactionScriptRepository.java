package cn.hexcloud.pbis.core.domain.repository;

import cn.hexcloud.commons.ddd.stereotype.Repository;
import cn.hexcloud.pbis.core.domain.object.channel.TransactionScript;

import java.util.List;

/**
 * <AUTHOR>
 * @Version 1.0
 * @Description TODO
 */
public interface TransactionScriptRepository extends Repository<TransactionScript> {

  TransactionScript findByCodeAndKey(String channelCode, String scriptKey);

  List<TransactionScript> findAny(String channelCode, String scriptKey);

}
