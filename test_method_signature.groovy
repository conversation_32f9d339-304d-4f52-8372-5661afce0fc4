#!/usr/bin/env groovy

// 测试方法签名是否修复

// 模拟测试数据 - 这应该是 LinkedHashMap 类型
def payRequest = [
    locationId: "tc-hexcloud-2000",
    origin: [
        orderId: "0f448ac1-862a-4c7b-bdb4-a3b7cdbf444"
    ],
    provider: [
        name: "TYRO",
        method: "CARD"
    ],
    total: [
        amount: 10000,
        currency: "AUD"
    ]
]

println "测试数据类型: ${payRequest.getClass()}"
println "是否为 Map: ${payRequest instanceof Map}"
println "是否为 LinkedHashMap: ${payRequest instanceof LinkedHashMap}"

// 测试方法签名
try {
    // 这个调用应该不再报 MissingMethodException
    // 但可能会因为网络/认证问题失败，这是预期的
    println "尝试调用 TyroMPay.createPayRequest()..."
    
    // 注意：这里我们不能直接调用，因为需要导入类
    // 但至少我们可以验证参数类型兼容性
    
    // 模拟方法签名检查
    def testMethod = { Map requestBody ->
        println "方法接收到参数类型: ${requestBody.getClass()}"
        println "参数内容: ${requestBody}"
        return "测试成功"
    }
    
    def result = testMethod(payRequest)
    println "方法调用结果: ${result}"
    
} catch (Exception e) {
    println "错误: ${e.message}"
    e.printStackTrace()
}

println "测试完成"
