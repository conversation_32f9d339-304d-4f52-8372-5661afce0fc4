#!/usr/bin/env groovy

// 测试修复后的 Tyro API 调用

@Grab('com.alibaba:fastjson:1.2.83')
@Grab('com.google.code.gson:gson:2.8.9')

import com.alibaba.fastjson.JSON
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

// 测试 Token 获取 - 使用修复后的逻辑
def testTokenRequest() {
    println "=== 测试修复后的 Token 获取 ==="
    
    def TOKEN_URL = "https://auth.connect.tyro.com/oauth/token"
    def CLIENT_ID = "y79fgoezj9qeyUBsQsJritGKC2Z7up8O"
    def CLIENT_SECRET = "hfWSkBJwWo1jN4Fk5ADCG_lfrOx4NTTgV3lZ0SgfodIz1en66UsQ4duo6RZlFjOU"
    def GRANT_TYPE = "client_credentials"
    def AUDIENCE = "https://app.connect.tyro"
    
    try {
        def connection = new URL(TOKEN_URL).openConnection() as HttpURLConnection
        connection.requestMethod = "POST"
        connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded")
        connection.doOutput = true

        // 构建请求体 - 包含 audience 参数
        def postData = "client_id=${CLIENT_ID}&client_secret=${CLIENT_SECRET}&grant_type=${GRANT_TYPE}&audience=${AUDIENCE}"
        println "请求数据: client_id=${CLIENT_ID}&client_secret=***&grant_type=${GRANT_TYPE}&audience=${AUDIENCE}"
        
        def outputStream = connection.outputStream
        outputStream.write(postData.getBytes("UTF-8"))
        outputStream.flush()
        outputStream.close()

        def responseCode = connection.responseCode
        println "响应码: ${responseCode}"
        
        if (responseCode == HttpURLConnection.HTTP_OK) {
            def gson = new Gson()
            def responseText = connection.inputStream.text
            def response = gson.fromJson(responseText, new TypeToken<Map<String, Object>>(){}.getType())
            
            println "✓ Token 获取成功!"
            println "Token 类型: ${response.token_type}"
            println "过期时间: ${response.expires_in} 秒"
            println "Access Token (前50字符): ${response.access_token.toString().substring(0, Math.min(50, response.access_token.toString().length()))}..."
            
            return response.access_token
        } else {
            def errorText = connection.errorStream?.text ?: "无法读取错误信息"
            println "✗ Token 获取失败: ${errorText}"
            return null
        }
    } catch (Exception e) {
        println "✗ Token 获取异常: ${e.message}"
        e.printStackTrace()
        return null
    }
}

// 测试方法签名兼容性
def testMethodSignature() {
    println "\n=== 测试方法签名兼容性 ==="
    
    // 模拟测试数据 - 这应该是 LinkedHashMap 类型
    def payRequest = [
        locationId: "tc-hexcloud-2000",
        origin: [
            orderId: "0f448ac1-862a-4c7b-bdb4-a3b7cdbf444"
        ],
        provider: [
            name: "TYRO",
            method: "CARD"
        ],
        total: [
            amount: 10000,
            currency: "AUD"
        ]
    ]

    println "测试数据类型: ${payRequest.getClass()}"
    println "是否为 Map: ${payRequest instanceof Map}"
    println "是否为 LinkedHashMap: ${payRequest instanceof LinkedHashMap}"

    // 模拟方法签名检查
    def testMethod = { Map requestBody ->
        println "✓ 方法接收到参数类型: ${requestBody.getClass()}"
        println "✓ 参数内容: ${JSON.toJSONString(requestBody)}"
        return "方法签名测试成功"
    }
    
    try {
        def result = testMethod(payRequest)
        println "✓ ${result}"
        return true
    } catch (Exception e) {
        println "✗ 方法签名测试失败: ${e.message}"
        return false
    }
}

// 主测试流程
println "开始测试修复后的 Tyro 集成..."

def tokenSuccess = testTokenRequest()
def signatureSuccess = testMethodSignature()

println "\n=== 测试结果总结 ==="
println "Token 获取: ${tokenSuccess ? '✓ 成功' : '✗ 失败'}"
println "方法签名: ${signatureSuccess ? '✓ 成功' : '✗ 失败'}"

if (tokenSuccess && signatureSuccess) {
    println "\n🎉 所有测试通过！原始的 MissingMethodException 问题已解决，Token 获取也正常工作。"
} else {
    println "\n⚠️  部分测试失败，需要进一步检查。"
}

println "\n测试完成"
